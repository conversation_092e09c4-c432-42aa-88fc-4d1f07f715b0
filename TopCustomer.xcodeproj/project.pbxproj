// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		3B467919C5B739C7362C95C1 /* Pods_TopCustomer.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 2D14DDD621EC1361F80D6245 /* Pods_TopCustomer.framework */; };
		7304D5032A4371F0008FE96E /* PaymentSDK.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7304D5022A4371F0008FE96E /* PaymentSDK.xcframework */; };
		7305CA742BF5066B00F5C4FE /* Int+.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7305CA732BF5066B00F5C4FE /* Int+.swift */; };
		730930482B58072700264FA3 /* best_offers.json in Resources */ = {isa = PBXBuildFile; fileRef = 730930472B58072700264FA3 /* best_offers.json */; };
		73190A5B2A8C786D000C31D7 /* ShadowViewOnly.swift in Sources */ = {isa = PBXBuildFile; fileRef = 73190A5A2A8C786D000C31D7 /* ShadowViewOnly.swift */; };
		732215612C47E7F200A6C763 /* InviteFriendsGiftVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 732215602C47E7F200A6C763 /* InviteFriendsGiftVC.swift */; };
		732320172C3B2F6500DE37B6 /* InviteFriendsVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 732320162C3B2F6500DE37B6 /* InviteFriendsVC.swift */; };
		73248D9A2C10A24100C2E101 /* UITableViewCell+.swift in Sources */ = {isa = PBXBuildFile; fileRef = 73248D992C10A24100C2E101 /* UITableViewCell+.swift */; };
		7325619F2C3D7157004AB08D /* UserQRCodeModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7325619E2C3D7157004AB08D /* UserQRCodeModel.swift */; };
		732561A12C3D774C004AB08D /* InviteFriendsPresenter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 732561A02C3D774C004AB08D /* InviteFriendsPresenter.swift */; };
		732561A32C3D78AE004AB08D /* InviteFriendsInteractor.swift in Sources */ = {isa = PBXBuildFile; fileRef = 732561A22C3D78AE004AB08D /* InviteFriendsInteractor.swift */; };
		7325FBB32AFE620E00690253 /* coins_shape.json in Resources */ = {isa = PBXBuildFile; fileRef = 7325FBB22AFE620E00690253 /* coins_shape.json */; };
		7325FBB72B00444000690253 /* LastOfferTableViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = 7325FBB62B00444000690253 /* LastOfferTableViewCell.xib */; };
		732D67372AB20C8400366120 /* CategoriesTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 732D67362AB20C8400366120 /* CategoriesTableViewCell.swift */; };
		732E03352C2C4A6E008B6088 /* ChooseDeliveryDateVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 732E03342C2C4A6E008B6088 /* ChooseDeliveryDateVC.swift */; };
		732E03392C2C4E86008B6088 /* ChooseDeliveryDateCVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 732E03372C2C4E86008B6088 /* ChooseDeliveryDateCVC.swift */; };
		732E033A2C2C4E86008B6088 /* ChooseDeliveryDateCVC.xib in Resources */ = {isa = PBXBuildFile; fileRef = 732E03382C2C4E86008B6088 /* ChooseDeliveryDateCVC.xib */; };
		7330075A2D5F7727002474AD /* TikTokEvents.swift in Sources */ = {isa = PBXBuildFile; fileRef = 733007592D5F7727002474AD /* TikTokEvents.swift */; };
		73342DBE2BF3C0A3005FC06E /* Notification+.swift in Sources */ = {isa = PBXBuildFile; fileRef = 73342DBD2BF3C0A3005FC06E /* Notification+.swift */; };
		7335B9032AFB099800C877FF /* GetPointsDescriptionModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7335B9022AFB099800C877FF /* GetPointsDescriptionModel.swift */; };
		7335B9062AFB0E4B00C877FF /* RewardDetailsCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7335B9042AFB0E4B00C877FF /* RewardDetailsCell.swift */; };
		7335B9072AFB0E4B00C877FF /* RewardDetailsCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = 7335B9052AFB0E4B00C877FF /* RewardDetailsCell.xib */; };
		73381ADE2D7CEC340059A27C /* ImageSliderView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 73381ADD2D7CEC340059A27C /* ImageSliderView.swift */; };
		73381AE02D7CEFF90059A27C /* ProductDetailsViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 73381ADF2D7CEFF90059A27C /* ProductDetailsViewModel.swift */; };
		73381AE52D7D00410059A27C /* SearchViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 73381AE42D7D00410059A27C /* SearchViewModel.swift */; };
		733E02012C04DF40000D972D /* SuperCollectionViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 733E01FF2C04DF40000D972D /* SuperCollectionViewCell.swift */; };
		733E02022C04DF40000D972D /* SuperCollectionViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = 733E02002C04DF40000D972D /* SuperCollectionViewCell.xib */; };
		73465F4B2AD7682300321243 /* CongratsRewardsVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 73465F4A2AD7682300321243 /* CongratsRewardsVC.swift */; };
		7346C1BE2A40C40200A9E5DF /* BaseResponseModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7346C1BD2A40C40200A9E5DF /* BaseResponseModel.swift */; };
		734B9FF22C4015EA00C992C2 /* UIImageView+.swift in Sources */ = {isa = PBXBuildFile; fileRef = 734B9FF12C4015EA00C992C2 /* UIImageView+.swift */; };
		73520B272D75D41200BD7086 /* ProfileView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 73520B252D75D41200BD7086 /* ProfileView.swift */; };
		73520B282D75D41200BD7086 /* OrdersView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 73520B232D75D41200BD7086 /* OrdersView.swift */; };
		73520B292D75D41200BD7086 /* CartView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 73520B1B2D75D41200BD7086 /* CartView.swift */; };
		73520B2A2D75D41200BD7086 /* AccountView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 73520B1A2D75D41200BD7086 /* AccountView.swift */; };
		73520B2B2D75D41200BD7086 /* MainTabBarView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 73520B222D75D41200BD7086 /* MainTabBarView.swift */; };
		73520B2C2D75D41200BD7086 /* SearchView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 73520B262D75D41200BD7086 /* SearchView.swift */; };
		73520B2D2D75D41200BD7086 /* FoodHomeView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 73520B1F2D75D41200BD7086 /* FoodHomeView.swift */; };
		73520B2E2D75D41200BD7086 /* CategoriesView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 73520B1C2D75D41200BD7086 /* CategoriesView.swift */; };
		73520B2F2D75D41200BD7086 /* CheckoutView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 73520B1E2D75D41200BD7086 /* CheckoutView.swift */; };
		73520B302D75D41200BD7086 /* CheckoutNewView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 73520B1D2D75D41200BD7086 /* CheckoutNewView.swift */; };
		73520B312D75D41200BD7086 /* MainCategoriesView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 73520B202D75D41200BD7086 /* MainCategoriesView.swift */; };
		73520B322D75D41200BD7086 /* MainHomeView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 73520B212D75D41200BD7086 /* MainHomeView.swift */; };
		73520B332D75D41200BD7086 /* ProductDetailView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 73520B242D75D41200BD7086 /* ProductDetailView.swift */; };
		7352DBB12CFFC23200320EDD /* ContentImageExtentionPush.appex in Embed Foundation Extensions */ = {isa = PBXBuildFile; fileRef = 7352DBAA2CFFC23200320EDD /* ContentImageExtentionPush.appex */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
		735499192ADAB2180007E26E /* RewardVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 735499182ADAB2180007E26E /* RewardVC.swift */; };
		7354991F2ADAB3A80007E26E /* PointsHostoryTVCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7354991D2ADAB3A80007E26E /* PointsHostoryTVCell.swift */; };
		735499202ADAB3A80007E26E /* PointsHostoryTVCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = 7354991E2ADAB3A80007E26E /* PointsHostoryTVCell.xib */; };
		735499222ADAC53B0007E26E /* PointsHostoryModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 735499212ADAC53B0007E26E /* PointsHostoryModel.swift */; };
		735499252ADACD440007E26E /* RewardsVCPresenter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 735499242ADACD440007E26E /* RewardsVCPresenter.swift */; };
		735499272ADACD9D0007E26E /* RewardsVCInteractor.swift in Sources */ = {isa = PBXBuildFile; fileRef = 735499262ADACD9D0007E26E /* RewardsVCInteractor.swift */; };
		73566ACF2C43FC0000D0AA8E /* loading_shape.json in Resources */ = {isa = PBXBuildFile; fileRef = 73566ACE2C43FBFF00D0AA8E /* loading_shape.json */; };
		73566AD12C4404A300D0AA8E /* add_to_cart_animation.json in Resources */ = {isa = PBXBuildFile; fileRef = 73566AD02C4404A300D0AA8E /* add_to_cart_animation.json */; };
		7358CEE12B0F9F05001F3611 /* CoinsLevelsShape.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7358CEE02B0F9F05001F3611 /* CoinsLevelsShape.swift */; };
		735B63072B5D201900650BEF /* CheckCartResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = 735B63062B5D201900650BEF /* CheckCartResponse.swift */; };
		73676ABC2CE13FDE00BFF236 /* FacebookAEM in Frameworks */ = {isa = PBXBuildFile; productRef = 73676ABB2CE13FDE00BFF236 /* FacebookAEM */; };
		73676ABE2CE13FDE00BFF236 /* FacebookBasics in Frameworks */ = {isa = PBXBuildFile; productRef = 73676ABD2CE13FDE00BFF236 /* FacebookBasics */; };
		73676AC02CE13FDE00BFF236 /* FacebookCore in Frameworks */ = {isa = PBXBuildFile; productRef = 73676ABF2CE13FDE00BFF236 /* FacebookCore */; };
		73676AC22CE13FDE00BFF236 /* FacebookLogin in Frameworks */ = {isa = PBXBuildFile; productRef = 73676AC12CE13FDE00BFF236 /* FacebookLogin */; };
		73676AC42CE13FDE00BFF236 /* FacebookShare in Frameworks */ = {isa = PBXBuildFile; productRef = 73676AC32CE13FDE00BFF236 /* FacebookShare */; };
		7367EE212AD764B30042BACF /* YourRewardsVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7367EE202AD764B30042BACF /* YourRewardsVC.swift */; };
		736CE1AC2C519111006ECEBC /* add_to_cart_animation_ar.json in Resources */ = {isa = PBXBuildFile; fileRef = 736CE1AB2C519111006ECEBC /* add_to_cart_animation_ar.json */; };
		7370954F2AAD1F4C0089D3E2 /* UserSearchAPI.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7370954E2AAD1F4C0089D3E2 /* UserSearchAPI.swift */; };
		737095512AAD204E0089D3E2 /* UserSearchModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 737095502AAD204E0089D3E2 /* UserSearchModel.swift */; };
		7372D9742B0E064600A9506D /* main3.json in Resources */ = {isa = PBXBuildFile; fileRef = 7372D9692B0E064600A9506D /* main3.json */; };
		7372D9752B0E064600A9506D /* middle1.json in Resources */ = {isa = PBXBuildFile; fileRef = 7372D96A2B0E064600A9506D /* middle1.json */; };
		7372D9762B0E064600A9506D /* main2.json in Resources */ = {isa = PBXBuildFile; fileRef = 7372D96B2B0E064600A9506D /* main2.json */; };
		7372D9772B0E064600A9506D /* main5.json in Resources */ = {isa = PBXBuildFile; fileRef = 7372D96C2B0E064600A9506D /* main5.json */; };
		7372D9782B0E064600A9506D /* main4.json in Resources */ = {isa = PBXBuildFile; fileRef = 7372D96D2B0E064600A9506D /* main4.json */; };
		7372D9792B0E064600A9506D /* middle4.json in Resources */ = {isa = PBXBuildFile; fileRef = 7372D96E2B0E064600A9506D /* middle4.json */; };
		7372D97A2B0E064600A9506D /* main6.json in Resources */ = {isa = PBXBuildFile; fileRef = 7372D96F2B0E064600A9506D /* main6.json */; };
		7372D97B2B0E064600A9506D /* middle5.json in Resources */ = {isa = PBXBuildFile; fileRef = 7372D9702B0E064600A9506D /* middle5.json */; };
		7372D97C2B0E064600A9506D /* middle2.json in Resources */ = {isa = PBXBuildFile; fileRef = 7372D9712B0E064600A9506D /* middle2.json */; };
		7372D97D2B0E064600A9506D /* main1.json in Resources */ = {isa = PBXBuildFile; fileRef = 7372D9722B0E064600A9506D /* main1.json */; };
		7372D97E2B0E064600A9506D /* middle3.json in Resources */ = {isa = PBXBuildFile; fileRef = 7372D9732B0E064600A9506D /* middle3.json */; };
		7372ECC22D82D37300818CD3 /* AlertType.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7372ECBF2D82D37300818CD3 /* AlertType.swift */; };
		7372ECC32D82D37300818CD3 /* ALertView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7372ECC02D82D37300818CD3 /* ALertView.swift */; };
		73757E632A8AFBB20052D068 /* RecommendationProductCVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 73757E612A8AFBB20052D068 /* RecommendationProductCVC.swift */; };
		73757E642A8AFBB20052D068 /* RecommendationProductCVC.xib in Resources */ = {isa = PBXBuildFile; fileRef = 73757E622A8AFBB20052D068 /* RecommendationProductCVC.xib */; };
		7376F23B2AFB9D1700FAB85A /* LastOfferTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7376F2392AFB9D1700FAB85A /* LastOfferTableViewCell.swift */; };
		7376F2402AFBAF3F00FAB85A /* LastOfferCollectionViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7376F23E2AFBAF3F00FAB85A /* LastOfferCollectionViewCell.swift */; };
		73778AC12CF8859D00D99032 /* PaymentsByUrlVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 73778ABF2CF8859D00D99032 /* PaymentsByUrlVC.swift */; };
		73778AC22CF8859D00D99032 /* PaymentsByUrlVC.xib in Resources */ = {isa = PBXBuildFile; fileRef = 73778AC02CF8859D00D99032 /* PaymentsByUrlVC.xib */; };
		737E27EC2D7EE26D00723266 /* OrdersListView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 737E27EB2D7EE26D00723266 /* OrdersListView.swift */; };
		737E27EE2D7EE68D00723266 /* OrdersListViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 737E27ED2D7EE68D00723266 /* OrdersListViewModel.swift */; };
		737E27F02D7EEAEA00723266 /* OrderDetailView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 737E27EF2D7EEAEA00723266 /* OrderDetailView.swift */; };
		737E27F22D7EEAFA00723266 /* EmptyOrdersView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 737E27F12D7EEAFA00723266 /* EmptyOrdersView.swift */; };
		7382DE5B2CB7F04C0060C4B5 /* MainSliderCVCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = 7382DE5A2CB7F04C0060C4B5 /* MainSliderCVCell.xib */; };
		7382DE5C2CB7F04C0060C4B5 /* MainSliderCVCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7382DE592CB7F04C0060C4B5 /* MainSliderCVCell.swift */; };
		738716D82BFFC9A200B1B8F6 /* ProductsOfBundleTVCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 738716D62BFFC9A200B1B8F6 /* ProductsOfBundleTVCell.swift */; };
		738716D92BFFC9A200B1B8F6 /* ProductsOfBundleTVCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = 738716D72BFFC9A200B1B8F6 /* ProductsOfBundleTVCell.xib */; };
		738AD26D2D6E50F700419CCA /* ViewInvoiceVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 738AD26C2D6E50F700419CCA /* ViewInvoiceVC.swift */; };
		738B81B32C1196850069DC6E /* BundleProductsView.xib in Resources */ = {isa = PBXBuildFile; fileRef = 738B81B22C1196850069DC6E /* BundleProductsView.xib */; };
		738B81B52C1196CE0069DC6E /* BundleProductsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 738B81B42C1196CE0069DC6E /* BundleProductsView.swift */; };
		738B81B92C11A4A90069DC6E /* CartProductsOfBundleTVCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 738B81B72C11A4A90069DC6E /* CartProductsOfBundleTVCell.swift */; };
		738B81BA2C11A4A90069DC6E /* CartProductsOfBundleTVCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = 738B81B82C11A4A90069DC6E /* CartProductsOfBundleTVCell.xib */; };
		738B91FE2CA53BCC00B406CB /* AppDelegate+AppsFlyer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 738B91FD2CA53BCC00B406CB /* AppDelegate+AppsFlyer.swift */; };
		738B92002CA58F5500B406CB /* AppDelegate+AppsFlyerDeepLinkDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 738B91FF2CA58F5500B406CB /* AppDelegate+AppsFlyerDeepLinkDelegate.swift */; };
		738BAC1A2AE50E1F00D42040 /* PointsLevelsModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 738BAC192AE50E1F00D42040 /* PointsLevelsModel.swift */; };
		738BAC1C2AE5380D00D42040 /* CalculateUserPointsToSARModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 738BAC1B2AE5380D00D42040 /* CalculateUserPointsToSARModel.swift */; };
		7390FF812D75048600C813EC /* SelectDeliveryTypeView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7390FF802D75048600C813EC /* SelectDeliveryTypeView.swift */; };
		739771102C7C78BF00FA8476 /* Tabby in Frameworks */ = {isa = PBXBuildFile; productRef = 7397710F2C7C78BF00FA8476 /* Tabby */; };
		739A394E2D7900CC00C4847A /* LoadingImageWithUrlView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 739A394D2D7900CC00C4847A /* LoadingImageWithUrlView.swift */; };
		739A39512D79086D00C4847A /* CartManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 739A39502D79086D00C4847A /* CartManager.swift */; };
		739A398B2D79123200C4847A /* CartViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 739A398A2D79123200C4847A /* CartViewModel.swift */; };
		739EBB0E2B065D4000E89E8B /* data5.json in Resources */ = {isa = PBXBuildFile; fileRef = 739EBB092B065D4000E89E8B /* data5.json */; };
		739EBB0F2B065D4000E89E8B /* data2.json in Resources */ = {isa = PBXBuildFile; fileRef = 739EBB0A2B065D4000E89E8B /* data2.json */; };
		739EBB102B065D4000E89E8B /* data1.json in Resources */ = {isa = PBXBuildFile; fileRef = 739EBB0B2B065D4000E89E8B /* data1.json */; };
		739EBB112B065D4000E89E8B /* data4.json in Resources */ = {isa = PBXBuildFile; fileRef = 739EBB0C2B065D4000E89E8B /* data4.json */; };
		739EBB122B065D4000E89E8B /* data3.json in Resources */ = {isa = PBXBuildFile; fileRef = 739EBB0D2B065D4000E89E8B /* data3.json */; };
		73A565C52D89709F00BDB509 /* StepperCartView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 73A565C42D89709F00BDB509 /* StepperCartView.swift */; };
		73A565C72D8970B800BDB509 /* SaudiRiyalAmount.swift in Sources */ = {isa = PBXBuildFile; fileRef = 73A565C62D8970B800BDB509 /* SaudiRiyalAmount.swift */; };
		73A565C92D89731F00BDB509 /* OrderDetailViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 73A565C82D89731F00BDB509 /* OrderDetailViewModel.swift */; };
		73A6CF392A988E14007F3657 /* CartYouMayAlsoLikeProductsResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = 73A6CF382A988E14007F3657 /* CartYouMayAlsoLikeProductsResponse.swift */; };
		73A6F6D22B860808001DC9EA /* BundelsListResponseFields.swift in Sources */ = {isa = PBXBuildFile; fileRef = 73A6F6D12B860808001DC9EA /* BundelsListResponseFields.swift */; };
		73A6F6D72B861D82001DC9EA /* BundelsListTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 73A6F6D52B861D82001DC9EA /* BundelsListTableViewCell.swift */; };
		73A6F6D82B861D82001DC9EA /* BundelsListTableViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = 73A6F6D62B861D82001DC9EA /* BundelsListTableViewCell.xib */; };
		73A6F6DB2B861DAF001DC9EA /* BundelsListCollectionViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 73A6F6D92B861DAF001DC9EA /* BundelsListCollectionViewCell.swift */; };
		73A6F6DC2B861DAF001DC9EA /* BundelsListCollectionViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = 73A6F6DA2B861DAF001DC9EA /* BundelsListCollectionViewCell.xib */; };
		73A941AE2B331C2100F1C8A3 /* UICollectionViewCell+.swift in Sources */ = {isa = PBXBuildFile; fileRef = 73A941AD2B331C2100F1C8A3 /* UICollectionViewCell+.swift */; };
		73A9D3052B441ADD00A5CC72 /* AdvertisingCollectionViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 73A9D3032B441ADD00A5CC72 /* AdvertisingCollectionViewCell.swift */; };
		73A9D3062B441ADD00A5CC72 /* AdvertisingCollectionViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = 73A9D3042B441ADD00A5CC72 /* AdvertisingCollectionViewCell.xib */; };
		73ABBA5A2CE291F700166D7E /* CityModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 73ABBA592CE291F700166D7E /* CityModel.swift */; };
		73B47A902D787551007B8FB9 /* OffersView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 73B47A8F2D787551007B8FB9 /* OffersView.swift */; };
		73B47A922D787564007B8FB9 /* OffersViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 73B47A912D787564007B8FB9 /* OffersViewModel.swift */; };
		73B47A962D787C4D007B8FB9 /* OfferCardView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 73B47A952D787C4D007B8FB9 /* OfferCardView.swift */; };
		73B47AD12D788058007B8FB9 /* ProductDetailsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 73B47AD02D788058007B8FB9 /* ProductDetailsView.swift */; };
		73B47AD32D788298007B8FB9 /* OfferCardDetailsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 73B47AD22D788298007B8FB9 /* OfferCardDetailsView.swift */; };
		73B6240C2D75B4BB0064D21A /* SelectDeliveryTypeViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 73B6240B2D75B4BB0064D21A /* SelectDeliveryTypeViewModel.swift */; };
		73B6240F2D75B6540064D21A /* WarehouseModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 73B6240E2D75B6540064D21A /* WarehouseModel.swift */; };
		73B624462D75CFA20064D21A /* LoadingIndicator.swift in Sources */ = {isa = PBXBuildFile; fileRef = 73B624452D75CFA20064D21A /* LoadingIndicator.swift */; };
		73B961BE2A8C595900B92293 /* ShadowView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 73B961BD2A8C595900B92293 /* ShadowView.swift */; };
		73C765DD2C510ABE0080282D /* invite_friends_gift_en.json in Resources */ = {isa = PBXBuildFile; fileRef = 73C765DC2C510ABE0080282D /* invite_friends_gift_en.json */; };
		73C765DF2C510AD30080282D /* invite_friends_gift.json in Resources */ = {isa = PBXBuildFile; fileRef = 73C765DE2C510AD30080282D /* invite_friends_gift.json */; };
		73D0AA052B98A7A0006B3BCC /* MosquesResponseFields.swift in Sources */ = {isa = PBXBuildFile; fileRef = 73D0AA042B98A7A0006B3BCC /* MosquesResponseFields.swift */; };
		73D172332D82533400E7EF01 /* CategoriesModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 73D172322D82533400E7EF01 /* CategoriesModel.swift */; };
		73D172782D8256AF00E7EF01 /* TabCategoriesSelectionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 73D172772D8256AF00E7EF01 /* TabCategoriesSelectionView.swift */; };
		73D363E92CB6B12700F1D163 /* AppsFlyerEvents.swift in Sources */ = {isa = PBXBuildFile; fileRef = 73D363E82CB6B12700F1D163 /* AppsFlyerEvents.swift */; };
		73DC7FE22C7F171F00403CDE /* TabyPaymentManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 73DC7FE12C7F171F00403CDE /* TabyPaymentManager.swift */; };
		73DCE10C2B004A0400AA4AAC /* LastOfferCollectionViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = 73DCE10B2B004A0400AA4AAC /* LastOfferCollectionViewCell.xib */; };
		73DEBD202B1626130026EB10 /* Font+.swift in Sources */ = {isa = PBXBuildFile; fileRef = 73DEBD1F2B1626130026EB10 /* Font+.swift */; };
		73E160942D60E90F0086A16B /* FirebaseEvents.swift in Sources */ = {isa = PBXBuildFile; fileRef = 73E160932D60E90F0086A16B /* FirebaseEvents.swift */; };
		73E185F62A8AC629008FC178 /* segoeUI.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 73E185F52A8AC629008FC178 /* segoeUI.ttf */; };
		73E190052A9D32D80010AE40 /* MayAlsoLikeTVCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 73E190032A9D32D80010AE40 /* MayAlsoLikeTVCell.swift */; };
		73E190062A9D32D80010AE40 /* MayAlsoLikeTVCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = 73E190042A9D32D80010AE40 /* MayAlsoLikeTVCell.xib */; };
		73E190082A9D4CFA0010AE40 /* UICollectionView+.swift in Sources */ = {isa = PBXBuildFile; fileRef = 73E190072A9D4CFA0010AE40 /* UICollectionView+.swift */; };
		73E595AE2D747C520043EE66 /* FloatingButtonView.xib in Resources */ = {isa = PBXBuildFile; fileRef = 73E595AD2D747C520043EE66 /* FloatingButtonView.xib */; };
		73E595AF2D747C520043EE66 /* FloatingButtonView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 73E595AC2D747C520043EE66 /* FloatingButtonView.swift */; };
		73EBCF002CD010FD00EB14C4 /* AdvertisementCVCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 73EBCEFE2CD010FD00EB14C4 /* AdvertisementCVCell.swift */; };
		73EBCF012CD010FD00EB14C4 /* AdvertisementCVCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = 73EBCEFF2CD010FD00EB14C4 /* AdvertisementCVCell.xib */; };
		73ED99D42BCD0EDD007FBD73 /* MainButtonDesignable.swift in Sources */ = {isa = PBXBuildFile; fileRef = 73ED99D32BCD0EDD007FBD73 /* MainButtonDesignable.swift */; };
		73F2D1DF2C8ECDE9004A9738 /* QuantitiesDiscountCVCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = 73F2D1DE2C8ECDE9004A9738 /* QuantitiesDiscountCVCell.xib */; };
		73F2D1E02C8ECDE9004A9738 /* QuantitiesDiscountCVCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 73F2D1DD2C8ECDE9004A9738 /* QuantitiesDiscountCVCell.swift */; };
		73F5A8D62D7B23F900C260DA /* YouMayAlsoLikeView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 73F5A8D52D7B23F900C260DA /* YouMayAlsoLikeView.swift */; };
		73F5A8D92D7B242200C260DA /* ProductItemView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 73F5A8D82D7B242200C260DA /* ProductItemView.swift */; };
		73F6BBDC2A9783FE0009A7E0 /* FreeDeliveryValueResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = 73F6BBDB2A9783FD0009A7E0 /* FreeDeliveryValueResponse.swift */; };
		73F7EFFC2D7C66DE00E35734 /* Date+.swift in Sources */ = {isa = PBXBuildFile; fileRef = 73F7EFFB2D7C66DE00E35734 /* Date+.swift */; };
		73F7F03F2D7C91E800E35734 /* CategoriesViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 73F7F03E2D7C91E800E35734 /* CategoriesViewModel.swift */; };
		73F9CE492C553EAA00635DDC /* ShiftsBySelectDateCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 73F9CE472C553EAA00635DDC /* ShiftsBySelectDateCell.swift */; };
		73F9CE4A2C553EAA00635DDC /* ShiftsBySelectDateCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = 73F9CE482C553EAA00635DDC /* ShiftsBySelectDateCell.xib */; };
		73FCE8862ACD6A8A0014DD95 /* UILabel+.swift in Sources */ = {isa = PBXBuildFile; fileRef = 73FCE8852ACD6A8A0014DD95 /* UILabel+.swift */; };
		73FEA8302D7700F4001C4360 /* MarketingBannersView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 73FEA82F2D7700F4001C4360 /* MarketingBannersView.swift */; };
		73FEA8322D771039001C4360 /* MainSectionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 73FEA8312D771039001C4360 /* MainSectionView.swift */; };
		73FED5312D7BAC9300C9F1F0 /* CheckoutViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 73FED5302D7BAC9300C9F1F0 /* CheckoutViewModel.swift */; };
		73FF5AEA2D7659270067415B /* MainHomeViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 73FF5AE92D7659270067415B /* MainHomeViewModel.swift */; };
		8117DF6C27E998A700004F69 /* HistoryOrdersPresenter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8117DF6927E998A700004F69 /* HistoryOrdersPresenter.swift */; };
		8117DF6D27E998A700004F69 /* HistoryOrdersViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8117DF6A27E998A700004F69 /* HistoryOrdersViewController.swift */; };
		8117DF6E27E998A700004F69 /* HistoryOrdersInteractor.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8117DF6B27E998A700004F69 /* HistoryOrdersInteractor.swift */; };
		8117DF7127E9990B00004F69 /* HistoryOrdersTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8117DF6F27E9990B00004F69 /* HistoryOrdersTableViewCell.swift */; };
		8117DF7227E9990B00004F69 /* HistoryOrdersTableViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = 8117DF7027E9990B00004F69 /* HistoryOrdersTableViewCell.xib */; };
		8117DF7727E9AA5C00004F69 /* HistoryOrderDetailsPresenter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8117DF7427E9AA5C00004F69 /* HistoryOrderDetailsPresenter.swift */; };
		8117DF7827E9AA5C00004F69 /* HistoryOrderDetailsViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8117DF7527E9AA5C00004F69 /* HistoryOrderDetailsViewController.swift */; };
		8117DF7927E9AA5C00004F69 /* HistoryOrderDetailsInteractor.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8117DF7627E9AA5C00004F69 /* HistoryOrderDetailsInteractor.swift */; };
		8117DF7C27E9B07100004F69 /* HistoryOrderDetailsTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8117DF7A27E9B07100004F69 /* HistoryOrderDetailsTableViewCell.swift */; };
		8117DF7D27E9B07100004F69 /* HistoryOrderDetailsTableViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = 8117DF7B27E9B07100004F69 /* HistoryOrderDetailsTableViewCell.xib */; };
		81A484AC27DF678F00528766 /* FSPagerView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 81A484A227DF678F00528766 /* FSPagerView.swift */; };
		81A484AD27DF678F00528766 /* FSPageViewTransformer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 81A484A327DF678F00528766 /* FSPageViewTransformer.swift */; };
		81A484AE27DF678F00528766 /* FSPagerViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = 81A484A427DF678F00528766 /* FSPagerViewCell.xib */; };
		81A484AF27DF678F00528766 /* FSPagerViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 81A484A627DF678F00528766 /* FSPagerViewCell.swift */; };
		81A484B027DF678F00528766 /* FSPagerViewLayoutAttributes.swift in Sources */ = {isa = PBXBuildFile; fileRef = 81A484A727DF678F00528766 /* FSPagerViewLayoutAttributes.swift */; };
		81A484B127DF678F00528766 /* FSPageViewLayout.swift in Sources */ = {isa = PBXBuildFile; fileRef = 81A484A827DF678F00528766 /* FSPageViewLayout.swift */; };
		81A484B227DF678F00528766 /* FSPageControl.swift in Sources */ = {isa = PBXBuildFile; fileRef = 81A484A927DF678F00528766 /* FSPageControl.swift */; };
		81A484B327DF678F00528766 /* FSPagerViewObjcCompat.m in Sources */ = {isa = PBXBuildFile; fileRef = 81A484AA27DF678F00528766 /* FSPagerViewObjcCompat.m */; };
		81A484B427DF678F00528766 /* FSPagerCollectionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 81A484AB27DF678F00528766 /* FSPagerCollectionView.swift */; };
		81A484B727DF69AE00528766 /* CategoriesCollectionViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 81A484B527DF69AE00528766 /* CategoriesCollectionViewCell.swift */; };
		81A484B827DF69AE00528766 /* CategoriesCollectionViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = 81A484B627DF69AE00528766 /* CategoriesCollectionViewCell.xib */; };
		81A484BB27DF6BF500528766 /* ProductsCollectionViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 81A484B927DF6BF500528766 /* ProductsCollectionViewCell.swift */; };
		81A484BC27DF6BF600528766 /* ProductsCollectionViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = 81A484BA27DF6BF500528766 /* ProductsCollectionViewCell.xib */; };
		81A484BF27DF6F4A00528766 /* UITableViewExtensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 81A484BD27DF6F4A00528766 /* UITableViewExtensions.swift */; };
		81A484C027DF6F4A00528766 /* UICollectionViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 81A484BE27DF6F4A00528766 /* UICollectionViewCell.swift */; };
		8BD0310F2D3F91100040A590 /* NotificationService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8BD0310D2D3F91100040A590 /* NotificationService.swift */; };
		BE04A56E27F5FF8700EE26EA /* HelpPresenter.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE04A56B27F5FF8700EE26EA /* HelpPresenter.swift */; };
		BE04A57027F5FF8700EE26EA /* HelpInteractor.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE04A56D27F5FF8700EE26EA /* HelpInteractor.swift */; };
		BE0D069D283E40E100F934F9 /* WebKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = BE0D069C283E40E100F934F9 /* WebKit.framework */; };
		BE0EED3B29DB020B00CAE929 /* CustomPopupAlertViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE0EED3A29DB020B00CAE929 /* CustomPopupAlertViewController.swift */; };
		************************ /* MaterialLocalizeButton.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE10DF1D28BBFBFE000983A2 /* MaterialLocalizeButton.swift */; };
		BE10DF2328BBFCCB000983A2 /* MaterialLocalizeLable.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE10DF1E28BBFBFE000983A2 /* MaterialLocalizeLable.swift */; };
		BE10DF2428BBFCCE000983A2 /* MaterialLocalizeImageView.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE10DF1F28BBFBFE000983A2 /* MaterialLocalizeImageView.swift */; };
		BE13CB8027D6302400607C2D /* LoewNextArabic-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = BE8D82F127D604E100A11D15 /* LoewNextArabic-Bold.ttf */; };
		BE13CB8227D6357E00607C2D /* LoewNextArabic-Medium.ttf in Resources */ = {isa = PBXBuildFile; fileRef = BE13CB8127D6357800607C2D /* LoewNextArabic-Medium.ttf */; };
		************************ /* CustomTextfieldWithFontStyle.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE13CB8327D6362600607C2D /* CustomTextfieldWithFontStyle.swift */; };
		BE1B8B5527D9D1D10045FA0E /* MyOrdersPresenter.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE1B8B5227D9D1D10045FA0E /* MyOrdersPresenter.swift */; };
		BE1B8B5627D9D1D10045FA0E /* MyOrdersViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE1B8B5327D9D1D10045FA0E /* MyOrdersViewController.swift */; };
		BE1B8B5727D9D1D10045FA0E /* MyOrdersInteractor.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE1B8B5427D9D1D10045FA0E /* MyOrdersInteractor.swift */; };
		BE1B8B5C27D9D2080045FA0E /* OffersPresenter.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE1B8B5927D9D2080045FA0E /* OffersPresenter.swift */; };
		BE1B8B5D27D9D2080045FA0E /* OffersViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE1B8B5A27D9D2080045FA0E /* OffersViewController.swift */; };
		BE1B8B5E27D9D2080045FA0E /* OffersInteractor.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE1B8B5B27D9D2080045FA0E /* OffersInteractor.swift */; };
		BE1BC0B627FD71DD00C9E42E /* MyCartCheckoutTableViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = BE1BC0B427FD71DD00C9E42E /* MyCartCheckoutTableViewCell.xib */; };
		BE1BC0B727FD71DD00C9E42E /* MyCartCheckoutTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE1BC0B527FD71DD00C9E42E /* MyCartCheckoutTableViewCell.swift */; };
		BE1FF69027D886CB008291AF /* Home.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = BE1FF68F27D886CB008291AF /* Home.storyboard */; };
		BE1FF69327D8C19E008291AF /* MainTabbarViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE1FF69227D8C19E008291AF /* MainTabbarViewController.swift */; };
		BE29143327DB490700802E36 /* OTPVerificationPresenter.swift in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* OTPVerificationPresenter.swift */; };
		************************ /* OTPVerificationViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* OTPVerificationViewController.swift */; };
		************************ /* OTPVerificationInteractor.swift in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* OTPVerificationInteractor.swift */; };
		************************ /* AccountInteractor.swift in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* AccountInteractor.swift */; };
		************************ /* AccountPresenter.swift in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* AccountPresenter.swift */; };
		************************ /* AccountViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* AccountViewController.swift */; };
		************************ /* ContactUsViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* ContactUsViewController.swift */; };
		************************ /* ContactUsPresenter.swift in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* ContactUsPresenter.swift */; };
		************************ /* ContactUsInteractor.swift in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* ContactUsInteractor.swift */; };
		************************ /* OTPFieldView.swift in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* OTPFieldView.swift */; };
		************************ /* OTPTextField.swift in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* OTPTextField.swift */; };
		************************ /* OffersTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* OffersTableViewCell.swift */; };
		BE647A8428E833B50051F5FE /* TopCustomer.entitlements in Resources */ = {isa = PBXBuildFile; fileRef = BE6621A62840B412004D0D0D /* TopCustomer.entitlements */; };
		BE6C5EAA27DA123D00F6C188 /* LoewNextArabic-ExtraBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = BE6C5EA927DA123800F6C188 /* LoewNextArabic-ExtraBold.ttf */; };
		BE7A839B291132F80078FD2F /* BannerInfoPresenter.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE7A8398291132F80078FD2F /* BannerInfoPresenter.swift */; };
		BE7A839C291132F80078FD2F /* BannerInfoViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE7A8399291132F80078FD2F /* BannerInfoViewController.swift */; };
		BE7A839D291132F80078FD2F /* BannerInfoInteractor.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE7A839A291132F80078FD2F /* BannerInfoInteractor.swift */; };
		BE822D4D291904BB00E91318 /* HomeTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE822D4C291904BB00E91318 /* HomeTableViewCell.swift */; };
		BE8CBF6628EC39FF00A3BB7A /* SplashViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE8CBF6528EC39FF00A3BB7A /* SplashViewController.swift */; };
		BE8CBF6728EC3C3400A3BB7A /* Material_video.mp4 in Resources */ = {isa = PBXBuildFile; fileRef = BE8CBF6428EC393800A3BB7A /* Material_video.mp4 */; };
		BE8D82E827D5E54B00A11D15 /* LoginPresenter.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE8D82E527D5E54B00A11D15 /* LoginPresenter.swift */; };
		BE8D82E927D5E54B00A11D15 /* LoginViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE8D82E627D5E54B00A11D15 /* LoginViewController.swift */; };
		BE8D82EA27D5E54B00A11D15 /* LoginInteractor.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE8D82E727D5E54B00A11D15 /* LoginInteractor.swift */; };
		BE8D82ED27D5E90F00A11D15 /* CustomRoundedButtton.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE8D82EC27D5E90F00A11D15 /* CustomRoundedButtton.swift */; };
		BE8D82EF27D5EA7C00A11D15 /* UIColor+Extension.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE8D82EE27D5EA7C00A11D15 /* UIColor+Extension.swift */; };
		BE8D82F727D6069000A11D15 /* Localizable.strings in Resources */ = {isa = PBXBuildFile; fileRef = BE8D82F927D6069000A11D15 /* Localizable.strings */; };
		BE90222D27E344CA00F9ACE4 /* SelectLanguagePresenter.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE90222A27E344CA00F9ACE4 /* SelectLanguagePresenter.swift */; };
		BE90222E27E344CA00F9ACE4 /* SelectLanguageInteractor.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE90222B27E344CA00F9ACE4 /* SelectLanguageInteractor.swift */; };
		BE90222F27E344CA00F9ACE4 /* SelectLanguageViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE90222C27E344CA00F9ACE4 /* SelectLanguageViewController.swift */; };
		BE90223427E344D900F9ACE4 /* SelectLocationViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE90223127E344D900F9ACE4 /* SelectLocationViewController.swift */; };
		BE90223527E344D900F9ACE4 /* SelectLocationInteractor.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE90223227E344D900F9ACE4 /* SelectLocationInteractor.swift */; };
		BE90223627E344D900F9ACE4 /* SelectLocationPresenter.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE90223327E344D900F9ACE4 /* SelectLocationPresenter.swift */; };
		BE90224027E3454800F9ACE4 /* BottomPopupUtils.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE90223827E3454800F9ACE4 /* BottomPopupUtils.swift */; };
		BE90224127E3454800F9ACE4 /* BottomPopupViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE90223927E3454800F9ACE4 /* BottomPopupViewController.swift */; };
		BE90224227E3454800F9ACE4 /* BottomPopupDismissInteractionController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE90223A27E3454800F9ACE4 /* BottomPopupDismissInteractionController.swift */; };
		************************ /* BottomPopupNavigationController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE90223B27E3454800F9ACE4 /* BottomPopupNavigationController.swift */; };
		BE90224427E3454800F9ACE4 /* BottomPopupDismissAnimator.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE90223C27E3454800F9ACE4 /* BottomPopupDismissAnimator.swift */; };
		BE90224527E3454800F9ACE4 /* BottomPopupPresentAnimator.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE90223D27E3454800F9ACE4 /* BottomPopupPresentAnimator.swift */; };
		BE90224627E3454800F9ACE4 /* BottomPopupTransitionHandler.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE90223E27E3454800F9ACE4 /* BottomPopupTransitionHandler.swift */; };
		BE90224727E3454800F9ACE4 /* BottomPopupPresentationController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE90223F27E3454800F9ACE4 /* BottomPopupPresentationController.swift */; };
		BE933FA127D77247005B27A4 /* Settings.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = ************************ /* Settings.storyboard */; };
		************************ /* QTYButton.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE93F86527EDE30E0019120C /* QTYButton.swift */; };
		BE93F86927EDE30E0019120C /* QTYButton.xib in Resources */ = {isa = PBXBuildFile; fileRef = BE93F86727EDE30E0019120C /* QTYButton.xib */; };
		************************ /* MyCartInteractor.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE93F87B27EDE3B20019120C /* MyCartInteractor.swift */; };
		BE93F88527EDE3B20019120C /* MyCartViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE93F87C27EDE3B20019120C /* MyCartViewController.swift */; };
		BE93F88627EDE3B20019120C /* MyCartPresenter.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE93F87D27EDE3B20019120C /* MyCartPresenter.swift */; };
		************************ /* CheckoutInteractor.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE93F87F27EDE3B20019120C /* CheckoutInteractor.swift */; };
		BE93F88827EDE3B20019120C /* CheckoutViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE93F88027EDE3B20019120C /* CheckoutViewController.swift */; };
		BE93F88927EDE3B20019120C /* CheckoutPresenter.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE93F88127EDE3B20019120C /* CheckoutPresenter.swift */; };
		BE93F88A27EDE3B20019120C /* MyCartTableViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = BE93F88227EDE3B20019120C /* MyCartTableViewCell.xib */; };
		BE93F88B27EDE3B20019120C /* MyCartTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE93F88327EDE3B20019120C /* MyCartTableViewCell.swift */; };
		BE93F88D27EDE3E50019120C /* MyCart.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = ************************ /* MyCart.storyboard */; };
		BE94FADE27FC573E00B131DD /* InvoicePresenter.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE94FADB27FC573E00B131DD /* InvoicePresenter.swift */; };
		BE94FADF27FC573E00B131DD /* InvoiceViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE94FADC27FC573E00B131DD /* InvoiceViewController.swift */; };
		BE94FAE027FC573E00B131DD /* InvoiceInteractor.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE94FADD27FC573E00B131DD /* InvoiceInteractor.swift */; };
		BE94FAE527FC5D7F00B131DD /* ScheduledOrderDetailPresenter.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE94FAE227FC5D7F00B131DD /* ScheduledOrderDetailPresenter.swift */; };
		************************ /* ScheduledOrderDetailViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE94FAE327FC5D7F00B131DD /* ScheduledOrderDetailViewController.swift */; };
		BE94FAE727FC5D7F00B131DD /* ScheduledOrderDetailInteractor.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE94FAE427FC5D7F00B131DD /* ScheduledOrderDetailInteractor.swift */; };
		BE9568EF27F6FBFB002E96C3 /* FilterSideMenuPresenter.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE9568EC27F6FBFB002E96C3 /* FilterSideMenuPresenter.swift */; };
		BE9568F027F6FBFB002E96C3 /* FilterSideMenuViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE9568ED27F6FBFB002E96C3 /* FilterSideMenuViewController.swift */; };
		BE9568F127F6FBFB002E96C3 /* FilterSideMenuInteractor.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE9568EE27F6FBFB002E96C3 /* FilterSideMenuInteractor.swift */; };
		************************ /* User.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE9655FF280D389A00DB6E83 /* User.swift */; };
		BE973F0327E85B0B00C09458 /* ActivityIndicator.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE973F0227E85B0B00C09458 /* ActivityIndicator.swift */; };
		BE973F0527E88CC300C09458 /* LoewNextArabic-Heavy.ttf in Resources */ = {isa = PBXBuildFile; fileRef = BE973F0427E88CC300C09458 /* LoewNextArabic-Heavy.ttf */; };
		BE97639B27E2081400F29744 /* CurrentOrdersPresenter.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE97639827E2081400F29744 /* CurrentOrdersPresenter.swift */; };
		BE97639C27E2081400F29744 /* CurrentOrdersViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE97639927E2081400F29744 /* CurrentOrdersViewController.swift */; };
		BE97639D27E2081400F29744 /* CurrentOrdersInteractor.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE97639A27E2081400F29744 /* CurrentOrdersInteractor.swift */; };
		BE9763A227E2085E00F29744 /* ScheduledOrdersPresenter.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE97639F27E2085E00F29744 /* ScheduledOrdersPresenter.swift */; };
		BE9763A327E2085E00F29744 /* ScheduledOrdersViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE9763A027E2085E00F29744 /* ScheduledOrdersViewController.swift */; };
		BE9763A427E2085E00F29744 /* ScheduledOrdersInteractor.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE9763A127E2085E00F29744 /* ScheduledOrdersInteractor.swift */; };
		BE9763A627E2105F00F29744 /* ViewEmbedder.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE9763A527E2105F00F29744 /* ViewEmbedder.swift */; };
		BE99782E27E0B8BC005CC308 /* MyOrdersTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE99782D27E0B8BC005CC308 /* MyOrdersTableViewCell.swift */; };
		BE9A1D1A27D641CA0072BCAC /* CountryCode.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE9A1D1627D641CA0072BCAC /* CountryCode.swift */; };
		BE9A1D1B27D641CA0072BCAC /* CountrySelectView.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE9A1D1727D641CA0072BCAC /* CountrySelectView.swift */; };
		BE9A1D1C27D641CA0072BCAC /* CountryTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE9A1D1827D641CA0072BCAC /* CountryTableViewCell.swift */; };
		BE9A1D1D27D641CA0072BCAC /* CountryPicker.bundle in Resources */ = {isa = PBXBuildFile; fileRef = BE9A1D1927D641CA0072BCAC /* CountryPicker.bundle */; };
		BE9A1D2627D64D630072BCAC /* BaseViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE9A1D2527D64D630072BCAC /* BaseViewController.swift */; };
		BE9A1D2827D64EB70072BCAC /* AppSuportedLanguages.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE9A1D2727D64EB70072BCAC /* AppSuportedLanguages.swift */; };
		BE9A1D2A27D64F9A0072BCAC /* AppSingleton.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE9A1D2927D64F9A0072BCAC /* AppSingleton.swift */; };
		BE9B3A4527FEF83D00EF3615 /* PaymentsPresenter.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE9B3A4227FEF83D00EF3615 /* PaymentsPresenter.swift */; };
		BE9B3A4627FEF83D00EF3615 /* PaymentsViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE9B3A4327FEF83D00EF3615 /* PaymentsViewController.swift */; };
		BE9B3A4727FEF83D00EF3615 /* PaymentsInteractor.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE9B3A4427FEF83D00EF3615 /* PaymentsInteractor.swift */; };
		BE9B3A4927FEF88400EF3615 /* Payments.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = BE9B3A4827FEF88400EF3615 /* Payments.storyboard */; };
		BE9B3A4B27FEFB1A00EF3615 /* PaymentCardListTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE9B3A4A27FEFB1A00EF3615 /* PaymentCardListTableViewCell.swift */; };
		BE9B3A5027FF21B800EF3615 /* ChoosePaymentPresenter.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE9B3A4D27FF21B800EF3615 /* ChoosePaymentPresenter.swift */; };
		BE9B3A5127FF21B800EF3615 /* ChoosePaymentViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE9B3A4E27FF21B800EF3615 /* ChoosePaymentViewController.swift */; };
		BE9B3A5227FF21B800EF3615 /* ChoosePaymentInteractor.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE9B3A4F27FF21B800EF3615 /* ChoosePaymentInteractor.swift */; };
		BE9B3A5727FF49D000EF3615 /* AddCardPresenter.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE9B3A5427FF49D000EF3615 /* AddCardPresenter.swift */; };
		BE9B3A5827FF49D000EF3615 /* AddCardViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE9B3A5527FF49D000EF3615 /* AddCardViewController.swift */; };
		BE9B3A5927FF49D000EF3615 /* AddCardInteractor.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE9B3A5627FF49D000EF3615 /* AddCardInteractor.swift */; };
		BE9FEF1127EC9300002CAB93 /* CurrentOrdersTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE9FEF1027EC9300002CAB93 /* CurrentOrdersTableViewCell.swift */; };
		BE9FEF1327EC9CE2002CAB93 /* ScheduledOrdersTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE9FEF1227EC9CE2002CAB93 /* ScheduledOrdersTableViewCell.swift */; };
		BEA7620E2886ACF200A890E4 /* TrackOrderPresenter.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEA7620B2886ACF200A890E4 /* TrackOrderPresenter.swift */; };
		BEA7620F2886ACF200A890E4 /* TrackOrderViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEA7620C2886ACF200A890E4 /* TrackOrderViewController.swift */; };
		BEA762102886ACF200A890E4 /* TrackOrderInteractor.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEA7620D2886ACF200A890E4 /* TrackOrderInteractor.swift */; };
		BEA7B9642836A44F00F9F235 /* LocationManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEA7B9632836A44F00F9F235 /* LocationManager.swift */; };
		BEA85B292876E26700F4754E /* CategoriesCollectionViewCellBig.xib in Resources */ = {isa = PBXBuildFile; fileRef = BEA85B272876E26700F4754E /* CategoriesCollectionViewCellBig.xib */; };
		BEA85B2A2876E26700F4754E /* CategoriesCollectionViewCellBig.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEA85B282876E26700F4754E /* CategoriesCollectionViewCellBig.swift */; };
		BEA9574428DDDA8A00DCFFF9 /* LoaderAnimation.json in Resources */ = {isa = PBXBuildFile; fileRef = BEA9574228DDDA8900DCFFF9 /* LoaderAnimation.json */; };
		BEA9574528DDDA8A00DCFFF9 /* LoadingView.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEA9574328DDDA8A00DCFFF9 /* LoadingView.swift */; };
		BEAAEC7D2850825F005A1D49 /* SocketIOManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEAAEC7C2850825F005A1D49 /* SocketIOManager.swift */; };
		BEAB6E7F28BCD3D100D47F96 /* MaterialLocalizeTextfield.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEAB6E7E28BCD3D000D47F96 /* MaterialLocalizeTextfield.swift */; };
		BEABE2742865F2AD0033AB1D /* Date+Extension.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEABE2732865F2AD0033AB1D /* Date+Extension.swift */; };
		BEADE08D28C9D2870095913E /* HelpViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEADE08C28C9D2870095913E /* HelpViewController.swift */; };
		BEADE940286D73B000556D1B /* ProductsCollectionViewCellSmall.xib in Resources */ = {isa = PBXBuildFile; fileRef = BEADE93E286D73B000556D1B /* ProductsCollectionViewCellSmall.xib */; };
		BEADE941286D73B000556D1B /* ProductsCollectionViewCellSmall.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEADE93F286D73B000556D1B /* ProductsCollectionViewCellSmall.swift */; };
		BEAEE5CD28BE2E210098946C /* ReferenceVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEAEE5CC28BE2E210098946C /* ReferenceVC.swift */; };
		BEAF3DC4288BD8A0006DE167 /* GoogleAPIHelper.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEAF3DC3288BD8A0006DE167 /* GoogleAPIHelper.swift */; };
		BEAF3DDC288BDA09006DE167 /* GoogleDistanceMetrixElement.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEAF3DC6288BDA08006DE167 /* GoogleDistanceMetrixElement.swift */; };
		BEAF3DDD288BDA09006DE167 /* GoogleDistanceMetrixDuration.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEAF3DC7288BDA08006DE167 /* GoogleDistanceMetrixDuration.swift */; };
		BEAF3DDE288BDA09006DE167 /* GoogleDistanceMetrixRow.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEAF3DC8288BDA08006DE167 /* GoogleDistanceMetrixRow.swift */; };
		BEAF3DDF288BDA09006DE167 /* GoogleDistanceMetrixDistance.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEAF3DC9288BDA08006DE167 /* GoogleDistanceMetrixDistance.swift */; };
		BEAF3DE0288BDA09006DE167 /* GoogleDistanceMetrixRootClass.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEAF3DCA288BDA08006DE167 /* GoogleDistanceMetrixRootClass.swift */; };
		BEAF3DE1288BDA09006DE167 /* GoogleDirectionDetailsLeg.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEAF3DCC288BDA09006DE167 /* GoogleDirectionDetailsLeg.swift */; };
		BEAF3DE2288BDA09006DE167 /* GoogleDirectionDetailsEndLocation.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEAF3DCD288BDA09006DE167 /* GoogleDirectionDetailsEndLocation.swift */; };
		BEAF3DE3288BDA09006DE167 /* GoogleDirectionDetailsOverviewPolyline.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEAF3DCE288BDA09006DE167 /* GoogleDirectionDetailsOverviewPolyline.swift */; };
		BEAF3DE4288BDA09006DE167 /* GoogleDirectionDetailsStep.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEAF3DCF288BDA09006DE167 /* GoogleDirectionDetailsStep.swift */; };
		BEAF3DE5288BDA09006DE167 /* GoogleDirectionDetailsBound.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEAF3DD0288BDA09006DE167 /* GoogleDirectionDetailsBound.swift */; };
		BEAF3DE6288BDA09006DE167 /* GoogleDirectionDetailsSouthwest.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEAF3DD1288BDA09006DE167 /* GoogleDirectionDetailsSouthwest.swift */; };
		BEAF3DE7288BDA09006DE167 /* GoogleDirectionDetailsLocation.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEAF3DD2288BDA09006DE167 /* GoogleDirectionDetailsLocation.swift */; };
		BEAF3DE8288BDA09006DE167 /* GoogleDirectionDetailsDistance.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEAF3DD3288BDA09006DE167 /* GoogleDirectionDetailsDistance.swift */; };
		BEAF3DE9288BDA09006DE167 /* GoogleDirectionDetailsStartLocation.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEAF3DD4288BDA09006DE167 /* GoogleDirectionDetailsStartLocation.swift */; };
		BEAF3DEA288BDA09006DE167 /* GoogleDirectionDetailsDuration.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEAF3DD5288BDA09006DE167 /* GoogleDirectionDetailsDuration.swift */; };
		BEAF3DEB288BDA09006DE167 /* GoogleDirectionDetailsNortheast.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEAF3DD6288BDA09006DE167 /* GoogleDirectionDetailsNortheast.swift */; };
		BEAF3DEC288BDA09006DE167 /* GoogleDirectionDetailsViaWaypoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEAF3DD7288BDA09006DE167 /* GoogleDirectionDetailsViaWaypoint.swift */; };
		BEAF3DED288BDA09006DE167 /* GoogleDirectionDetailsPolyline.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEAF3DD8288BDA09006DE167 /* GoogleDirectionDetailsPolyline.swift */; };
		BEAF3DEE288BDA09006DE167 /* GoogleDirectionDetailsGeocodedWaypoint.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEAF3DD9288BDA09006DE167 /* GoogleDirectionDetailsGeocodedWaypoint.swift */; };
		BEAF3DEF288BDA09006DE167 /* GoogleDirectionDetailsRoute.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEAF3DDA288BDA09006DE167 /* GoogleDirectionDetailsRoute.swift */; };
		BEAF3DF0288BDA09006DE167 /* GoogleDirectionDetailsRootClass.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEAF3DDB288BDA09006DE167 /* GoogleDirectionDetailsRootClass.swift */; };
		BEAFCB9E2995265900276C1D /* FloatRatingView.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEAFCB9D2995265900276C1D /* FloatRatingView.swift */; };
		BEB2C57027CCC57E00C5FBCD /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = BEB2C56F27CCC57E00C5FBCD /* GoogleService-Info.plist */; };
		BEB3CADF2918DC1B001C400E /* NewHomePresenter.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEB3CADC2918DC1B001C400E /* NewHomePresenter.swift */; };
		BEB3CAE02918DC1B001C400E /* NewHomeViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEB3CADD2918DC1B001C400E /* NewHomeViewController.swift */; };
		BEB3CAE12918DC1B001C400E /* NewHomeInteractor.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEB3CADE2918DC1B001C400E /* NewHomeInteractor.swift */; };
		BEB4FE1627F5A7D0006B5BCC /* ThankYouPresenter.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEB4FE1327F5A7D0006B5BCC /* ThankYouPresenter.swift */; };
		BEB4FE1727F5A7D0006B5BCC /* ThankYouViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEB4FE1427F5A7D0006B5BCC /* ThankYouViewController.swift */; };
		BEB4FE1827F5A7D0006B5BCC /* ThankYouInteractor.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEB4FE1527F5A7D0006B5BCC /* ThankYouInteractor.swift */; };
		BEB4FE1D27F5AFCD006B5BCC /* OfferDetailsPresenter.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEB4FE1A27F5AFCD006B5BCC /* OfferDetailsPresenter.swift */; };
		BEB4FE1E27F5AFCD006B5BCC /* OfferDetailsViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEB4FE1B27F5AFCD006B5BCC /* OfferDetailsViewController.swift */; };
		BEB4FE1F27F5AFCD006B5BCC /* OfferDetailsInteractor.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEB4FE1C27F5AFCD006B5BCC /* OfferDetailsInteractor.swift */; };
		BEB4FE2427F5CC13006B5BCC /* OrderDetailPresenter.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEB4FE2127F5CC13006B5BCC /* OrderDetailPresenter.swift */; };
		BEB4FE2527F5CC13006B5BCC /* OrderDetailViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEB4FE2227F5CC13006B5BCC /* OrderDetailViewController.swift */; };
		BEB4FE2627F5CC13006B5BCC /* OrderDetailInteractor.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEB4FE2327F5CC13006B5BCC /* OrderDetailInteractor.swift */; };
		BEB5BE2B28B773690075CF42 /* QTYButtonOLD.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEB5BE2828B773690075CF42 /* QTYButtonOLD.swift */; };
		BEB5BE2C28B773690075CF42 /* QTYButtonOLD.xib in Resources */ = {isa = PBXBuildFile; fileRef = BEB5BE2A28B773690075CF42 /* QTYButtonOLD.xib */; };
		BEBA109927E9F32C0063E617 /* ProductPopup.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = BEBA109827E9F32C0063E617 /* ProductPopup.storyboard */; };
		BEBA109E27E9F3610063E617 /* ProductPopupInteractor.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEBA109B27E9F3610063E617 /* ProductPopupInteractor.swift */; };
		BEBA109F27E9F3610063E617 /* ProductPopupViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEBA109C27E9F3610063E617 /* ProductPopupViewController.swift */; };
		BEBA10A027E9F3610063E617 /* ProductPopupPresenter.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEBA109D27E9F3610063E617 /* ProductPopupPresenter.swift */; };
		BEBF066927D7769D00C3AF62 /* SettingsPresenter.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEBF066627D7769D00C3AF62 /* SettingsPresenter.swift */; };
		BEBF066A27D7769D00C3AF62 /* SettingsViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEBF066727D7769D00C3AF62 /* SettingsViewController.swift */; };
		BEBF066B27D7769D00C3AF62 /* SettingsInteractor.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEBF066827D7769D00C3AF62 /* SettingsInteractor.swift */; };
		BEBF067127D7844600C3AF62 /* MirroringLabel.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEBF066D27D7844600C3AF62 /* MirroringLabel.swift */; };
		BEBF067227D7844600C3AF62 /* MirroringViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEBF066E27D7844600C3AF62 /* MirroringViewController.swift */; };
		BEBF067327D7844600C3AF62 /* L012Localizer.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEBF066F27D7844600C3AF62 /* L012Localizer.swift */; };
		BEBF067427D7844600C3AF62 /* L102Language.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEBF067027D7844600C3AF62 /* L102Language.swift */; };
		BEBF067627D78A9200C3AF62 /* SettingsTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEBF067527D78A9200C3AF62 /* SettingsTableViewCell.swift */; };
		BEBF067B27D79F0B00C3AF62 /* StaticPagePresenter.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEBF067827D79F0B00C3AF62 /* StaticPagePresenter.swift */; };
		BEBF067C27D79F0B00C3AF62 /* StaticPageViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEBF067927D79F0B00C3AF62 /* StaticPageViewController.swift */; };
		BEBF067D27D79F0B00C3AF62 /* StaticPageInteractor.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEBF067A27D79F0B00C3AF62 /* StaticPageInteractor.swift */; };
		BEC0146627D61E9D00509E26 /* UIStoryboard+Helper.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEC0146527D61E9D00509E26 /* UIStoryboard+Helper.swift */; };
		BEC0146827D6232900509E26 /* CustomViewForFields.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEC0146727D6232900509E26 /* CustomViewForFields.swift */; };
		BEC2851627FAD25800A998A8 /* FilterSearchPresenter.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEC2851327FAD25800A998A8 /* FilterSearchPresenter.swift */; };
		BEC2851727FAD25800A998A8 /* FilterSearchViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEC2851427FAD25800A998A8 /* FilterSearchViewController.swift */; };
		BEC2851827FAD25800A998A8 /* FilterSearchInteractor.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEC2851527FAD25800A998A8 /* FilterSearchInteractor.swift */; };
		BEC2851D27FAF6A200A998A8 /* AddAddressPresenter.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEC2851A27FAF6A200A998A8 /* AddAddressPresenter.swift */; };
		BEC2851E27FAF6A200A998A8 /* AddAddressViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEC2851B27FAF6A200A998A8 /* AddAddressViewController.swift */; };
		BEC2851F27FAF6A200A998A8 /* AddAddressInteractor.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEC2851C27FAF6A200A998A8 /* AddAddressInteractor.swift */; };
		BEC2E9D929CA27AB00B837F8 /* CheckoutShiftsCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEC2E9D729CA27AB00B837F8 /* CheckoutShiftsCell.swift */; };
		BEC2E9DA29CA27AB00B837F8 /* CheckoutShiftsCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = BEC2E9D829CA27AB00B837F8 /* CheckoutShiftsCell.xib */; };
		BEC3587A29642F910005040C /* AdvertisementPopupPresenter.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEC3587729642F910005040C /* AdvertisementPopupPresenter.swift */; };
		BEC3587B29642F910005040C /* AdvertisementPopupViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEC3587829642F910005040C /* AdvertisementPopupViewController.swift */; };
		BEC3587C29642F910005040C /* AdvertisementPopupInteractor.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEC3587929642F910005040C /* AdvertisementPopupInteractor.swift */; };
		BEC898B728FFFF22008AE165 /* SearchSuggestionCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEC898B628FFFF22008AE165 /* SearchSuggestionCell.swift */; };
		BEC97A0B29E030740049E7C6 /* done_icon.json in Resources */ = {isa = PBXBuildFile; fileRef = BEC97A0A29E030740049E7C6 /* done_icon.json */; };
		BECA3B7D28217E8C00675D20 /* AddressListTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = BECA3B7C28217E8C00675D20 /* AddressListTableViewCell.swift */; };
		BECDBAA429D56A60008E8EAC /* VersionUpdatePresenter.swift in Sources */ = {isa = PBXBuildFile; fileRef = BECDBAA129D56A60008E8EAC /* VersionUpdatePresenter.swift */; };
		BECDBAA529D56A60008E8EAC /* VersionUpdateViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BECDBAA229D56A60008E8EAC /* VersionUpdateViewController.swift */; };
		BECDBAA629D56A60008E8EAC /* VersionUpdateInteractor.swift in Sources */ = {isa = PBXBuildFile; fileRef = BECDBAA329D56A60008E8EAC /* VersionUpdateInteractor.swift */; };
		BED0E5B229B73EDC00CF14FA /* HomeBannerCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = BED0E5B129B73EDC00CF14FA /* HomeBannerCell.swift */; };
		BED25AE528F814670048062D /* MixpanelEvents.swift in Sources */ = {isa = PBXBuildFile; fileRef = BED25AE428F814670048062D /* MixpanelEvents.swift */; };
		BEDD1D342A0243E0007D4E4B /* APIHelper.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1C8A2A0243E0007D4E4B /* APIHelper.swift */; };
		BEDD1D352A0243E0007D4E4B /* AlamofireImplementations.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1C8B2A0243E0007D4E4B /* AlamofireImplementations.swift */; };
		BEDD1D362A0243E0007D4E4B /* JSONValue.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1C8C2A0243E0007D4E4B /* JSONValue.swift */; };
		BEDD1D372A0243E0007D4E4B /* UserAPI.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1C8E2A0243E0007D4E4B /* UserAPI.swift */; };
		BEDD1D382A0243E0007D4E4B /* APPVersionCheckAPI.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1C8F2A0243E0007D4E4B /* APPVersionCheckAPI.swift */; };
		BEDD1D392A0243E0007D4E4B /* ContactUsAPI.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1C902A0243E0007D4E4B /* ContactUsAPI.swift */; };
		BEDD1D3A2A0243E0007D4E4B /* OrderAPI.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1C912A0243E0007D4E4B /* OrderAPI.swift */; };
		BEDD1D3B2A0243E0007D4E4B /* NotificationAPI.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1C922A0243E0007D4E4B /* NotificationAPI.swift */; };
		BEDD1D3C2A0243E0007D4E4B /* UserCardsAPI.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1C932A0243E0007D4E4B /* UserCardsAPI.swift */; };
		BEDD1D3D2A0243E0007D4E4B /* UserWalletAPI.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1C942A0243E0007D4E4B /* UserWalletAPI.swift */; };
		BEDD1D3E2A0243E0007D4E4B /* CartAPI.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1C952A0243E0007D4E4B /* CartAPI.swift */; };
		BEDD1D3F2A0243E0007D4E4B /* DriverAPI.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1C962A0243E0007D4E4B /* DriverAPI.swift */; };
		BEDD1D402A0243E0007D4E4B /* RatingAPI.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1C972A0243E0007D4E4B /* RatingAPI.swift */; };
		BEDD1D412A0243E0007D4E4B /* FavouriteAPI.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1C982A0243E0007D4E4B /* FavouriteAPI.swift */; };
		BEDD1D422A0243E0007D4E4B /* AuthenticationAPI.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1C992A0243E0007D4E4B /* AuthenticationAPI.swift */; };
		BEDD1D432A0243E0007D4E4B /* ProductAPI.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1C9A2A0243E0007D4E4B /* ProductAPI.swift */; };
		BEDD1D442A0243E0007D4E4B /* NotifyMeAPI.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1C9B2A0243E0007D4E4B /* NotifyMeAPI.swift */; };
		BEDD1D452A0243E0007D4E4B /* AdvertisementAPI.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1C9C2A0243E0007D4E4B /* AdvertisementAPI.swift */; };
		BEDD1D462A0243E0007D4E4B /* CategoryAPI.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1C9D2A0243E0007D4E4B /* CategoryAPI.swift */; };
		BEDD1D472A0243E0007D4E4B /* OfferAPI.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1C9E2A0243E0007D4E4B /* OfferAPI.swift */; };
		BEDD1D482A0243E0007D4E4B /* CountryAPI.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1C9F2A0243E0007D4E4B /* CountryAPI.swift */; };
		BEDD1D492A0243E0007D4E4B /* PromocodeAPI.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CA02A0243E0007D4E4B /* PromocodeAPI.swift */; };
		BEDD1D4A2A0243E0007D4E4B /* BannerAPI.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CA12A0243E0007D4E4B /* BannerAPI.swift */; };
		BEDD1D4B2A0243E0007D4E4B /* CommonAPI.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CA22A0243E0007D4E4B /* CommonAPI.swift */; };
		BEDD1D4C2A0243E0007D4E4B /* CodableHelper.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CA32A0243E0007D4E4B /* CodableHelper.swift */; };
		BEDD1D4D2A0243E0007D4E4B /* JSONEncodableEncoding.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CA42A0243E0007D4E4B /* JSONEncodableEncoding.swift */; };
		BEDD1D4E2A0243E0007D4E4B /* APIs.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CA52A0243E0007D4E4B /* APIs.swift */; };
		BEDD1D4F2A0243E0007D4E4B /* OauthGuestloginBody.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CA72A0243E0007D4E4B /* OauthGuestloginBody.swift */; };
		BEDD1D502A0243E0007D4E4B /* SkipRatingResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CA82A0243E0007D4E4B /* SkipRatingResponse.swift */; };
		BEDD1D512A0243E0007D4E4B /* LatestHomeWebListingResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CA92A0243E0007D4E4B /* LatestHomeWebListingResponse.swift */; };
		BEDD1D522A0243E0007D4E4B /* GetCartResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CAA2A0243E0007D4E4B /* GetCartResponse.swift */; };
		BEDD1D532A0243E0007D4E4B /* ProductSuggestionResponseFields.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CAB2A0243E0007D4E4B /* ProductSuggestionResponseFields.swift */; };
		BEDD1D542A0243E0007D4E4B /* DriverProductCountResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CAC2A0243E0007D4E4B /* DriverProductCountResponse.swift */; };
		BEDD1D552A0243E0007D4E4B /* WebCategoryListResponseFields.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CAD2A0243E0007D4E4B /* WebCategoryListResponseFields.swift */; };
		BEDD1D562A0243E0007D4E4B /* OauthSocialsigninBody.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CAE2A0243E0007D4E4B /* OauthSocialsigninBody.swift */; };
		BEDD1D572A0243E0007D4E4B /* ProductLatestproductlistingBody.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CAF2A0243E0007D4E4B /* ProductLatestproductlistingBody.swift */; };
		BEDD1D582A0243E0007D4E4B /* AppVersionResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CB02A0243E0007D4E4B /* AppVersionResponse.swift */; };
		BEDD1D592A0243E0007D4E4B /* PromocodeCheckResponseFields.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CB12A0243E0007D4E4B /* PromocodeCheckResponseFields.swift */; };
		BEDD1D5A2A0243E0007D4E4B /* OrderCreateorderBody.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CB22A0243E0007D4E4B /* OrderCreateorderBody.swift */; };
		BEDD1D5B2A0243E0007D4E4B /* ProductInfoResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CB32A0243E0007D4E4B /* ProductInfoResponse.swift */; };
		BEDD1D5C2A0243E0007D4E4B /* WalletDetailsResponseFields.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CB42A0243E0007D4E4B /* WalletDetailsResponseFields.swift */; };
		BEDD1D5D2A0243E0007D4E4B /* AppVersionResponseFields.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CB52A0243E0007D4E4B /* AppVersionResponseFields.swift */; };
		BEDD1D5E2A0243E0007D4E4B /* DriverWeeklyScheduleResponseFields.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CB62A0243E0007D4E4B /* DriverWeeklyScheduleResponseFields.swift */; };
		BEDD1D5F2A0243E0007D4E4B /* AdvertisementResponseFields.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CB72A0243E0007D4E4B /* AdvertisementResponseFields.swift */; };
		BEDD1D602A0243E0007D4E4B /* UserChangepasswordBody.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CB82A0243E0007D4E4B /* UserChangepasswordBody.swift */; };
		BEDD1D612A0243E0007D4E4B /* DriverOrdercancelBody.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CB92A0243E0007D4E4B /* DriverOrdercancelBody.swift */; };
		BEDD1D622A0243E0007D4E4B /* UserAddusercardsBody.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CBA2A0243E0007D4E4B /* UserAddusercardsBody.swift */; };
		BEDD1D632A0243E0007D4E4B /* BannerResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CBB2A0243E0007D4E4B /* BannerResponse.swift */; };
		BEDD1D642A0243E0007D4E4B /* UpdateLanguageResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CBC2A0243E0007D4E4B /* UpdateLanguageResponse.swift */; };
		BEDD1D652A0243E0007D4E4B /* ProductResponseFields.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CBD2A0243E0007D4E4B /* ProductResponseFields.swift */; };
		BEDD1D662A0243E0007D4E4B /* LatestHomeWebListingResponseFields.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CBE2A0243E0007D4E4B /* LatestHomeWebListingResponseFields.swift */; };
		BEDD1D672A0243E0007D4E4B /* ShiftResponseFields.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CBF2A0243E0007D4E4B /* ShiftResponseFields.swift */; };
		BEDD1D682A0243E0007D4E4B /* AdvertisementResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CC02A0243E0007D4E4B /* AdvertisementResponse.swift */; };
		BEDD1D692A0243E0007D4E4B /* DetailFields.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CC12A0243E0007D4E4B /* DetailFields.swift */; };
		BEDD1D6A2A0243E0007D4E4B /* ProductProductsuggestionBody.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CC22A0243E0007D4E4B /* ProductProductsuggestionBody.swift */; };
		BEDD1D6B2A0243E0007D4E4B /* OfferResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CC32A0243E0007D4E4B /* OfferResponse.swift */; };
		BEDD1D6C2A0243E0007D4E4B /* DriverProductCountResponseFields.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CC42A0243E0007D4E4B /* DriverProductCountResponseFields.swift */; };
		BEDD1D6D2A0243E0007D4E4B /* CheckappversionBody.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CC52A0243E0007D4E4B /* CheckappversionBody.swift */; };
		BEDD1D6E2A0243E0007D4E4B /* LatestProductListResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CC62A0243E0007D4E4B /* LatestProductListResponse.swift */; };
		BEDD1D6F2A0243E0007D4E4B /* PromocodePromocodecheckBody.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CC72A0243E0007D4E4B /* PromocodePromocodecheckBody.swift */; };
		BEDD1D702A0243E0007D4E4B /* OauthSignupBody.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CC82A0243E0007D4E4B /* OauthSignupBody.swift */; };
		BEDD1D712A0243E0007D4E4B /* TransactionResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CC92A0243E0007D4E4B /* TransactionResponse.swift */; };
		BEDD1D722A0243E0007D4E4B /* WebProductResponseFields.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CCA2A0243E0007D4E4B /* WebProductResponseFields.swift */; };
		BEDD1D732A0243E0007D4E4B /* DriverCompletedOrderDeatailsResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CCB2A0243E0007D4E4B /* DriverCompletedOrderDeatailsResponse.swift */; };
		BEDD1D742A0243E0007D4E4B /* DriverOrderDeatailsResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CCC2A0243E0007D4E4B /* DriverOrderDeatailsResponse.swift */; };
		BEDD1D752A0243E0007D4E4B /* ProductInfoResponseFields.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CCD2A0243E0007D4E4B /* ProductInfoResponseFields.swift */; };
		BEDD1D762A0243E0007D4E4B /* CategoryListResponseFields.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CCE2A0243E0007D4E4B /* CategoryListResponseFields.swift */; };
		BEDD1D772A0243E0007D4E4B /* OrderedProductDetailResponseFields.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CCF2A0243E0007D4E4B /* OrderedProductDetailResponseFields.swift */; };
		BEDD1D782A0243E0007D4E4B /* OrderListResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CD02A0243E0007D4E4B /* OrderListResponse.swift */; };
		BEDD1D792A0243E0007D4E4B /* ContentPageResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CD12A0243E0007D4E4B /* ContentPageResponse.swift */; };
		BEDD1D7A2A0243E0007D4E4B /* OrderGetorderlistBody.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CD22A0243E0007D4E4B /* OrderGetorderlistBody.swift */; };
		BEDD1D7B2A0243E0007D4E4B /* DriverRescheduleorderBody.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CD32A0243E0007D4E4B /* DriverRescheduleorderBody.swift */; };
		BEDD1D7C2A0243E0007D4E4B /* CreateOrderResponseFields.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CD42A0243E0007D4E4B /* CreateOrderResponseFields.swift */; };
		BEDD1D7D2A0243E0007D4E4B /* ContactContacthelpcreateBody.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CD52A0243E0007D4E4B /* ContactContacthelpcreateBody.swift */; };
		BEDD1D7E2A0243E0007D4E4B /* LatestHomeListingResponseFields.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CD62A0243E0007D4E4B /* LatestHomeListingResponseFields.swift */; };
		BEDD1D7F2A0243E0007D4E4B /* LatestHomeListingResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CD72A0243E0007D4E4B /* LatestHomeListingResponse.swift */; };
		BEDD1D802A0243E0007D4E4B /* UserDetailListingResponseFields.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CD82A0243E0007D4E4B /* UserDetailListingResponseFields.swift */; };
		BEDD1D812A0243E0007D4E4B /* FavouriteMarkfavouriteBody.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CD92A0243E0007D4E4B /* FavouriteMarkfavouriteBody.swift */; };
		BEDD1D822A0243E0007D4E4B /* UserUpdatedevicetokenBody.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CDA2A0243E0007D4E4B /* UserUpdatedevicetokenBody.swift */; };
		BEDD1D832A0243E0007D4E4B /* GetCartResponseFields.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CDB2A0243E0007D4E4B /* GetCartResponseFields.swift */; };
		BEDD1D842A0243E0007D4E4B /* WalletDetailsResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CDC2A0243E0007D4E4B /* WalletDetailsResponse.swift */; };
		BEDD1D852A0243E0007D4E4B /* NotificationListResponseFields.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CDD2A0243E0007D4E4B /* NotificationListResponseFields.swift */; };
		BEDD1D862A0243E0007D4E4B /* CardResponseFields.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CDE2A0243E0007D4E4B /* CardResponseFields.swift */; };
		BEDD1D872A0243E0007D4E4B /* DriverOrderResponseFields.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CDF2A0243E0007D4E4B /* DriverOrderResponseFields.swift */; };
		BEDD1D882A0243E0007D4E4B /* OfferListResponseFields.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CE02A0243E0007D4E4B /* OfferListResponseFields.swift */; };
		BEDD1D892A0243E0007D4E4B /* OrderOrdertransactionupdateBody.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CE12A0243E0007D4E4B /* OrderOrdertransactionupdateBody.swift */; };
		BEDD1D8A2A0243E0007D4E4B /* ListResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CE22A0243E0007D4E4B /* ListResponse.swift */; };
		BEDD1D8B2A0243E0007D4E4B /* GetCartShiftResponseFields.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CE32A0243E0007D4E4B /* GetCartShiftResponseFields.swift */; };
		BEDD1D8C2A0243E0007D4E4B /* DriverHomeListingResponseFields.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CE42A0243E0007D4E4B /* DriverHomeListingResponseFields.swift */; };
		BEDD1D8D2A0243E0007D4E4B /* MarketingBannerResponseFields.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CE52A0243E0007D4E4B /* MarketingBannerResponseFields.swift */; };
		BEDD1D8E2A0243E0007D4E4B /* DriverOrderDeatailsResponseFields.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CE62A0243E0007D4E4B /* DriverOrderDeatailsResponseFields.swift */; };
		BEDD1D8F2A0243E0007D4E4B /* CacheResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CE72A0243E0007D4E4B /* CacheResponse.swift */; };
		BEDD1D902A0243E0007D4E4B /* PromocodeCheckResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CE82A0243E0007D4E4B /* PromocodeCheckResponse.swift */; };
		BEDD1D912A0243E0007D4E4B /* ProductProductlistingBody.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CE92A0243E0007D4E4B /* ProductProductlistingBody.swift */; };
		BEDD1D922A0243E0007D4E4B /* OrderResponseFields.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CEA2A0243E0007D4E4B /* OrderResponseFields.swift */; };
		BEDD1D932A0243E0007D4E4B /* DayWiseResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CEB2A0243E0007D4E4B /* DayWiseResponse.swift */; };
		BEDD1D942A0243E0007D4E4B /* ContentPageResponseFields.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CEC2A0243E0007D4E4B /* ContentPageResponseFields.swift */; };
		BEDD1D952A0243E0007D4E4B /* ListResponseFields.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CED2A0243E0007D4E4B /* ListResponseFields.swift */; };
		BEDD1D962A0243E0007D4E4B /* UpdateLanguageResponseFields.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CEE2A0243E0007D4E4B /* UpdateLanguageResponseFields.swift */; };
		BEDD1D972A0243E0007D4E4B /* CancelOrderReasonResponseFields.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CEF2A0243E0007D4E4B /* CancelOrderReasonResponseFields.swift */; };
		BEDD1D982A0243E0007D4E4B /* OrderDetailResponseFields.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CF02A0243E0007D4E4B /* OrderDetailResponseFields.swift */; };
		BEDD1D992A0243E0007D4E4B /* OfferListResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CF12A0243E0007D4E4B /* OfferListResponse.swift */; };
		BEDD1D9A2A0243E0007D4E4B /* ProductProductweblistingBody.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CF22A0243E0007D4E4B /* ProductProductweblistingBody.swift */; };
		BEDD1D9B2A0243E0007D4E4B /* NotificationResponseFields.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CF32A0243E0007D4E4B /* NotificationResponseFields.swift */; };
		BEDD1D9C2A0243E0007D4E4B /* AddToCartResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CF42A0243E0007D4E4B /* AddToCartResponse.swift */; };
		BEDD1D9D2A0243E0007D4E4B /* AddressResponseFields.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CF52A0243E0007D4E4B /* AddressResponseFields.swift */; };
		BEDD1D9E2A0243E0007D4E4B /* RatingAddratingBody.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CF62A0243E0007D4E4B /* RatingAddratingBody.swift */; };
		BEDD1D9F2A0243E0007D4E4B /* ProductDetailResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CF72A0243E0007D4E4B /* ProductDetailResponse.swift */; };
		BEDD1DA02A0243E0007D4E4B /* DriverHomeListingResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CF82A0243E0007D4E4B /* DriverHomeListingResponse.swift */; };
		BEDD1DA12A0243E0007D4E4B /* DriverOrderStatusResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CF92A0243E0007D4E4B /* DriverOrderStatusResponse.swift */; };
		BEDD1DA22A0243E0007D4E4B /* OrderListResponseFields.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CFA2A0243E0007D4E4B /* OrderListResponseFields.swift */; };
		BEDD1DA32A0243E0007D4E4B /* AddRatingResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CFB2A0243E0007D4E4B /* AddRatingResponse.swift */; };
		BEDD1DA42A0243E0007D4E4B /* NotificationResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CFC2A0243E0007D4E4B /* NotificationResponse.swift */; };
		BEDD1DA52A0243E0007D4E4B /* ShiftResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CFD2A0243E0007D4E4B /* ShiftResponse.swift */; };
		BEDD1DA62A0243E0007D4E4B /* UserResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CFE2A0243E0007D4E4B /* UserResponse.swift */; };
		BEDD1DA72A0243E0007D4E4B /* DriverCurrentOrderListingResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1CFF2A0243E0007D4E4B /* DriverCurrentOrderListingResponse.swift */; };
		BEDD1DA82A0243E0007D4E4B /* OrderUpdatecontactnumberBody.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1D002A0243E0007D4E4B /* OrderUpdatecontactnumberBody.swift */; };
		BEDD1DA92A0243E0007D4E4B /* UserDetailListingResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1D012A0243E0007D4E4B /* UserDetailListingResponse.swift */; };
		BEDD1DAA2A0243E0007D4E4B /* DynamicSectionResponseFields.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1D022A0243E0007D4E4B /* DynamicSectionResponseFields.swift */; };
		BEDD1DAB2A0243E0007D4E4B /* HomeListingResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1D032A0243E0007D4E4B /* HomeListingResponse.swift */; };
		BEDD1DAC2A0243E0007D4E4B /* AddressResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1D042A0243E0007D4E4B /* AddressResponse.swift */; };
		BEDD1DAD2A0243E0007D4E4B /* OauthVerifyotpBody.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1D052A0243E0007D4E4B /* OauthVerifyotpBody.swift */; };
		BEDD1DAE2A0243E0007D4E4B /* ProductSuggestionResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1D062A0243E0007D4E4B /* ProductSuggestionResponse.swift */; };
		BEDD1DAF2A0243E0007D4E4B /* UserProfileBody.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1D072A0243E0007D4E4B /* UserProfileBody.swift */; };
		BEDD1DB02A0243E0007D4E4B /* FavouriteResponseFields.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1D082A0243E0007D4E4B /* FavouriteResponseFields.swift */; };
		BEDD1DB12A0243E0007D4E4B /* CountryListResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1D092A0243E0007D4E4B /* CountryListResponse.swift */; };
		BEDD1DB22A0243E0007D4E4B /* GetUserCardDetailResponseFields.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1D0A2A0243E0007D4E4B /* GetUserCardDetailResponseFields.swift */; };
		BEDD1DB32A0243E0007D4E4B /* CreateOrderResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1D0B2A0243E0007D4E4B /* CreateOrderResponse.swift */; };
		BEDD1DB42A0243E0007D4E4B /* UserDrivertipBody.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1D0C2A0243E0007D4E4B /* UserDrivertipBody.swift */; };
		BEDD1DB52A0243E0007D4E4B /* CardResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1D0D2A0243E0007D4E4B /* CardResponse.swift */; };
		BEDD1DB62A0243E0007D4E4B /* NotifymeBody.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1D0E2A0243E0007D4E4B /* NotifymeBody.swift */; };
		BEDD1DB72A0243E0007D4E4B /* UserAddaddressBody.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1D0F2A0243E0007D4E4B /* UserAddaddressBody.swift */; };
		BEDD1DB82A0243E0007D4E4B /* ProductProductinfoBody.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1D102A0243E0007D4E4B /* ProductProductinfoBody.swift */; };
		BEDD1DB92A0243E0007D4E4B /* UserUserlanguageBody.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1D112A0243E0007D4E4B /* UserUserlanguageBody.swift */; };
		BEDD1DBA2A0243E0007D4E4B /* DriverCurrentOrderListingResponseFields.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1D122A0243E0007D4E4B /* DriverCurrentOrderListingResponseFields.swift */; };
		BEDD1DBB2A0243E0007D4E4B /* DriverCompletedOrderDeatailsResponseFields.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1D132A0243E0007D4E4B /* DriverCompletedOrderDeatailsResponseFields.swift */; };
		BEDD1DBC2A0243E0007D4E4B /* CountryListResponseFields.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1D142A0243E0007D4E4B /* CountryListResponseFields.swift */; };
		BEDD1DBD2A0243E0007D4E4B /* AddToCartResponseFields.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1D152A0243E0007D4E4B /* AddToCartResponseFields.swift */; };
		BEDD1DBE2A0243E0007D4E4B /* UserAddmoneytowalletBody.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1D162A0243E0007D4E4B /* UserAddmoneytowalletBody.swift */; };
		BEDD1DBF2A0243E0007D4E4B /* DriverWeeklyScheduleResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1D172A0243E0007D4E4B /* DriverWeeklyScheduleResponse.swift */; };
		BEDD1DC02A0243E0007D4E4B /* AddRatingResponseFields.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1D182A0243E0007D4E4B /* AddRatingResponseFields.swift */; };
		BEDD1DC12A0243E0007D4E4B /* ProductDetailsResponseFields.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1D192A0243E0007D4E4B /* ProductDetailsResponseFields.swift */; };
		BEDD1DC22A0243E0007D4E4B /* HomeListingResponseFields.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1D1A2A0243E0007D4E4B /* HomeListingResponseFields.swift */; };
		BEDD1DC32A0243E0007D4E4B /* UserResponseFields.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1D1B2A0243E0007D4E4B /* UserResponseFields.swift */; };
		BEDD1DC42A0243E0007D4E4B /* FavouriteResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1D1C2A0243E0007D4E4B /* FavouriteResponse.swift */; };
		BEDD1DC52A0243E0007D4E4B /* DriverRescheduleOrderResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1D1D2A0243E0007D4E4B /* DriverRescheduleOrderResponse.swift */; };
		BEDD1DC62A0243E0007D4E4B /* ProductListResponseFields.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1D1E2A0243E0007D4E4B /* ProductListResponseFields.swift */; };
		BEDD1DC72A0243E0007D4E4B /* FavouriteListResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1D1F2A0243E0007D4E4B /* FavouriteListResponse.swift */; };
		BEDD1DC82A0243E0007D4E4B /* OauthRequestforotpBody.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1D202A0243E0007D4E4B /* OauthRequestforotpBody.swift */; };
		BEDD1DC92A0243E0007D4E4B /* OrderDetailResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1D212A0243E0007D4E4B /* OrderDetailResponse.swift */; };
		BEDD1DCA2A0243E0007D4E4B /* TransactionResponseFields.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1D222A0243E0007D4E4B /* TransactionResponseFields.swift */; };
		BEDD1DCB2A0243E0007D4E4B /* CartAddtocartBody.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1D232A0243E0007D4E4B /* CartAddtocartBody.swift */; };
		BEDD1DCC2A0243E0007D4E4B /* DriverCountResponseFields.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1D242A0243E0007D4E4B /* DriverCountResponseFields.swift */; };
		BEDD1DCD2A0243E0007D4E4B /* GetCartListResponseFields.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1D252A0243E0007D4E4B /* GetCartListResponseFields.swift */; };
		BEDD1DCE2A0243E0007D4E4B /* OfferResponseFields.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1D262A0243E0007D4E4B /* OfferResponseFields.swift */; };
		BEDD1DCF2A0243E0007D4E4B /* DriverOrderstatusupdateBody.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1D272A0243E0007D4E4B /* DriverOrderstatusupdateBody.swift */; };
		BEDD1DD02A0243E0007D4E4B /* CommonFields.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1D282A0243E0007D4E4B /* CommonFields.swift */; };
		BEDD1DD12A0243E0007D4E4B /* CancelOrderReasonResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1D292A0243E0007D4E4B /* CancelOrderReasonResponse.swift */; };
		BEDD1DD22A0243E0007D4E4B /* NotificationBadgeResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1D2A2A0243E0007D4E4B /* NotificationBadgeResponse.swift */; };
		BEDD1DD32A0243E0007D4E4B /* MarketingBannerImageResponseFields.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1D2B2A0243E0007D4E4B /* MarketingBannerImageResponseFields.swift */; };
		BEDD1DD42A0243E0007D4E4B /* DriverRescheduleOrderResponseFields.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1D2C2A0243E0007D4E4B /* DriverRescheduleOrderResponseFields.swift */; };
		BEDD1DD52A0243E0007D4E4B /* OauthLoginBody.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1D2D2A0243E0007D4E4B /* OauthLoginBody.swift */; };
		BEDD1DD62A0243E0007D4E4B /* ProductListResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1D2E2A0243E0007D4E4B /* ProductListResponse.swift */; };
		BEDD1DD72A0243E0007D4E4B /* AddressListResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1D2F2A0243E0007D4E4B /* AddressListResponse.swift */; };
		BEDD1DD82A0243E0007D4E4B /* Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1D302A0243E0007D4E4B /* Extensions.swift */; };
		BEDD1DD92A0243E0007D4E4B /* JSONEncodingHelper.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1D312A0243E0007D4E4B /* JSONEncodingHelper.swift */; };
		BEDD1DDA2A0243E0007D4E4B /* Models.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1D322A0243E0007D4E4B /* Models.swift */; };
		BEDD1DDB2A0243E0007D4E4B /* Configuration.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEDD1D332A0243E0007D4E4B /* Configuration.swift */; };
		BEE21A66299503890090C9BA /* RatingPresenter.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEE21A63299503890090C9BA /* RatingPresenter.swift */; };
		BEE21A67299503890090C9BA /* RatingViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEE21A64299503890090C9BA /* RatingViewController.swift */; };
		BEE21A68299503890090C9BA /* RatingInteractor.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEE21A65299503890090C9BA /* RatingInteractor.swift */; };
		BEE4FF6229B0BF3A0010A24E /* FavoritePresenter.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEE4FF5F29B0BF3A0010A24E /* FavoritePresenter.swift */; };
		BEE4FF6329B0BF3A0010A24E /* FavoriteViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEE4FF6029B0BF3A0010A24E /* FavoriteViewController.swift */; };
		BEE4FF6429B0BF3A0010A24E /* FavoriteInteractor.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEE4FF6129B0BF3A0010A24E /* FavoriteInteractor.swift */; };
		BEE6424B297581E400211571 /* ImageScrollView.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEE6424A297581E300211571 /* ImageScrollView.swift */; };
		BEE642512975825000211571 /* FullScreenImagePresenter.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEE6424E2975825000211571 /* FullScreenImagePresenter.swift */; };
		BEE642522975825000211571 /* FullScreenImageViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEE6424F2975825000211571 /* FullScreenImageViewController.swift */; };
		BEE642532975825000211571 /* FullScreenImageInteractor.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEE642502975825000211571 /* FullScreenImageInteractor.swift */; };
		BEE94349297ACC4700CF3EE6 /* FirebaseDeeplinkingHelper.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEE94348297ACC4700CF3EE6 /* FirebaseDeeplinkingHelper.swift */; };
		BEEAD04D29CD84E9008455E5 /* AnimationTest.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEEAD04C29CD84E9008455E5 /* AnimationTest.swift */; };
		BEF4D1DB2942109D00EA53FF /* CancelReasonPresenter.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEF4D1D82942109D00EA53FF /* CancelReasonPresenter.swift */; };
		BEF4D1DC2942109D00EA53FF /* CancelReasonViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEF4D1D92942109D00EA53FF /* CancelReasonViewController.swift */; };
		BEF4D1DD2942109D00EA53FF /* CancelReasonInteractor.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEF4D1DA2942109D00EA53FF /* CancelReasonInteractor.swift */; };
		BEF4D1DF2942249F00EA53FF /* CancelReasonTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEF4D1DE2942249F00EA53FF /* CancelReasonTableViewCell.swift */; };
		E20B05702E32622300E635FB /* NewMainHomeView.swift in Sources */ = {isa = PBXBuildFile; fileRef = E20B056F2E32622300E635FB /* NewMainHomeView.swift */; };
		E20B05732E32635B00E635FB /* NewMainHomeModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = E20B05722E32635B00E635FB /* NewMainHomeModel.swift */; };
		E20B05752E32643700E635FB /* NewMainHomeViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = E20B05742E32643700E635FB /* NewMainHomeViewModel.swift */; };
		E20B057A2E32921800E635FB /* NewProductsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = E20B05792E32921800E635FB /* NewProductsView.swift */; };
		E20B057C2E32925200E635FB /* NewProductsViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = E20B057B2E32925200E635FB /* NewProductsViewModel.swift */; };
		E21F53EE2E09CF260096BC2A /* WalletView.swift in Sources */ = {isa = PBXBuildFile; fileRef = E21F53ED2E09CF260096BC2A /* WalletView.swift */; };
		E21F53F12E0B71EA0096BC2A /* WalletViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = E21F53F02E0B71EA0096BC2A /* WalletViewModel.swift */; };
		E21F53F32E0B72A90096BC2A /* TransactionsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = E21F53F22E0B72A90096BC2A /* TransactionsView.swift */; };
		E21F53F82E0B7D110096BC2A /* MyFavouritesView.swift in Sources */ = {isa = PBXBuildFile; fileRef = E21F53F72E0B7D110096BC2A /* MyFavouritesView.swift */; };
		E21F53FA2E0B7E170096BC2A /* MyFavViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = E21F53F92E0B7E170096BC2A /* MyFavViewModel.swift */; };
		E21F53FE2E0B8F2E0096BC2A /* MyAddressesView.swift in Sources */ = {isa = PBXBuildFile; fileRef = E21F53FD2E0B8F2E0096BC2A /* MyAddressesView.swift */; };
		E21F54002E0B8F630096BC2A /* MyAddressesViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = E21F53FF2E0B8F630096BC2A /* MyAddressesViewModel.swift */; };
		E21F54022E0B97510096BC2A /* AddressItem.swift in Sources */ = {isa = PBXBuildFile; fileRef = E21F54012E0B97510096BC2A /* AddressItem.swift */; };
		E21F54062E0BB64C0096BC2A /* ReferView.swift in Sources */ = {isa = PBXBuildFile; fileRef = E21F54052E0BB64C0096BC2A /* ReferView.swift */; };
		E23FC0A12E23D28C006AB6B4 /* OrderDeliveredView.swift in Sources */ = {isa = PBXBuildFile; fileRef = E23FC0A02E23D28C006AB6B4 /* OrderDeliveredView.swift */; };
		E25C5B4C2E3B6E2300DA87DF /* NewSubCategoriesView.swift in Sources */ = {isa = PBXBuildFile; fileRef = E25C5B4B2E3B6E2300DA87DF /* NewSubCategoriesView.swift */; };
		E25C5B4E2E3B6E4C00DA87DF /* NewSubCategoriesTapSelectionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = E25C5B4D2E3B6E4C00DA87DF /* NewSubCategoriesTapSelectionView.swift */; };
		E25C5B502E3B6E8D00DA87DF /* NewSubCategoriesViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = E25C5B4F2E3B6E8D00DA87DF /* NewSubCategoriesViewModel.swift */; };
		E25C5B522E3B6FDA00DA87DF /* NewSubCategoryModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = E25C5B512E3B6FDA00DA87DF /* NewSubCategoryModel.swift */; };
		E2914FCF2E1BC9C50084CEC1 /* TabRouter.swift in Sources */ = {isa = PBXBuildFile; fileRef = E2914FCE2E1BC9C50084CEC1 /* TabRouter.swift */; };
		E29542432E48BFA90002622C /* LoadingSquareImageWithUrlView.swift in Sources */ = {isa = PBXBuildFile; fileRef = E29542422E48BFA90002622C /* LoadingSquareImageWithUrlView.swift */; };
		E29542452E48BFEA0002622C /* ProductSquareItemView.swift in Sources */ = {isa = PBXBuildFile; fileRef = E29542442E48BFEA0002622C /* ProductSquareItemView.swift */; };
		E2C33C932E00DF8500EF66A2 /* CustomSwiftUIAlertView.swift in Sources */ = {isa = PBXBuildFile; fileRef = E2C33C922E00DF8500EF66A2 /* CustomSwiftUIAlertView.swift */; };
		E2E5B0752E252CD000CA6144 /* OrderDetailsPopupView.swift in Sources */ = {isa = PBXBuildFile; fileRef = E2E5B0742E252CCF00CA6144 /* OrderDetailsPopupView.swift */; };
		E2E5B07A2E27EDE600CA6144 /* SubCategoriesViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = E2E5B0792E27EDE600CA6144 /* SubCategoriesViewModel.swift */; };
		E2E5B07C2E27EE2900CA6144 /* SubCategoriesView.swift in Sources */ = {isa = PBXBuildFile; fileRef = E2E5B07B2E27EE2900CA6144 /* SubCategoriesView.swift */; };
		E2E5B0822E28E71000CA6144 /* SubCategoriesTapSelectionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = E2E5B0812E28E71000CA6144 /* SubCategoriesTapSelectionView.swift */; };
		F90B4C9E20329C45006ADAF6 /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = F90B4C9C20329C45006ADAF6 /* Main.storyboard */; };
		F90B4CA020329C45006ADAF6 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = F90B4C9F20329C45006ADAF6 /* Assets.xcassets */; };
		F90B4CA320329C45006ADAF6 /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = F90B4CA120329C45006ADAF6 /* LaunchScreen.storyboard */; };
		F90B4CD92032A87B006ADAF6 /* Structure_Guide.rtf in Resources */ = {isa = PBXBuildFile; fileRef = F90B4CD82032A87B006ADAF6 /* Structure_Guide.rtf */; };
		F90B4CDE2032AAB8006ADAF6 /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = F90B4CDC2032AAB8006ADAF6 /* AppDelegate.swift */; };
		F90B4CE22032F4A6006ADAF6 /* SOAPI.swift in Sources */ = {isa = PBXBuildFile; fileRef = F90B4CE02032F4A6006ADAF6 /* SOAPI.swift */; };
		F90B4CE52032F4A6006ADAF6 /* SOService.swift in Sources */ = {isa = PBXBuildFile; fileRef = F90B4CE12032F4A6006ADAF6 /* SOService.swift */; };
		F948AECA203E865500F5A852 /* AppdelegateNotification.swift in Sources */ = {isa = PBXBuildFile; fileRef = F948AEC9203E865500F5A852 /* AppdelegateNotification.swift */; };
		F952382B2047BA3500BC0D49 /* RequestParameter.swift in Sources */ = {isa = PBXBuildFile; fileRef = F952382A2047BA3400BC0D49 /* RequestParameter.swift */; };
		F96C2D2D20380B1A00F8CBC7 /* Validator.swift in Sources */ = {isa = PBXBuildFile; fileRef = F96C2D2C20380B1A00F8CBC7 /* Validator.swift */; };
		F97A9A2821103F9B007C45F5 /* UIApplicationExtensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = F97A9A1521102E18007C45F5 /* UIApplicationExtensions.swift */; };
		F97A9A2921103F9F007C45F5 /* UIViewExtensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = F97A9A1C211031A6007C45F5 /* UIViewExtensions.swift */; };
		F97A9A2A21103FA2007C45F5 /* UIViewControllerExtensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = F97A9A1B211031A6007C45F5 /* UIViewControllerExtensions.swift */; };
		F97A9A2B21103FA4007C45F5 /* StringExtensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = F97A9A1321102CEF007C45F5 /* StringExtensions.swift */; };
		F97A9A2C21103FA7007C45F5 /* CGRectExtensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = F97A9A0D21102600007C45F5 /* CGRectExtensions.swift */; };
		F97A9A2D21103FAA007C45F5 /* EZSwiftFunctions.swift in Sources */ = {isa = PBXBuildFile; fileRef = F97A9A112110274A007C45F5 /* EZSwiftFunctions.swift */; };
		F97A9A2E21103FAD007C45F5 /* CGFloatExtensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = F97A9A1F211031C0007C45F5 /* CGFloatExtensions.swift */; };
		F97A9A2F21103FAF007C45F5 /* UserDefaultsExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = F97A99C62110226F007C45F5 /* UserDefaultsExtension.swift */; };
		F995BDC1204D1277005E5279 /* KeyMessages.swift in Sources */ = {isa = PBXBuildFile; fileRef = F995BDC0204D1277005E5279 /* KeyMessages.swift */; };
		F9A4539F203AA7200065C9FD /* Constant.swift in Sources */ = {isa = PBXBuildFile; fileRef = F9A4539E203AA7200065C9FD /* Constant.swift */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		7352DBAF2CFFC23200320EDD /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = F90B4C8D20329C45006ADAF6 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 7352DBA92CFFC23200320EDD;
			remoteInfo = ContentImageExtentionPushNotification;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		73C1A8EE2CFFA05B00CCF584 /* Embed Foundation Extensions */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 12;
			dstPath = "";
			dstSubfolderSpec = 13;
			files = (
				7352DBB12CFFC23200320EDD /* ContentImageExtentionPush.appex in Embed Foundation Extensions */,
			);
			name = "Embed Foundation Extensions";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		2D14DDD621EC1361F80D6245 /* Pods_TopCustomer.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_TopCustomer.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		36CCE020292AFB9CF22A3724 /* Pods-TopCustomer.releasestaging.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-TopCustomer.releasestaging.xcconfig"; path = "Target Support Files/Pods-TopCustomer/Pods-TopCustomer.releasestaging.xcconfig"; sourceTree = "<group>"; };
		45F95AE511E3881314FA4AA5 /* Pods-TopCustomer.devrelease.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-TopCustomer.devrelease.xcconfig"; path = "Target Support Files/Pods-TopCustomer/Pods-TopCustomer.devrelease.xcconfig"; sourceTree = "<group>"; };
		6395572ACF2F73DAD7E168CF /* Pods-TopCustomer.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-TopCustomer.debug.xcconfig"; path = "Target Support Files/Pods-TopCustomer/Pods-TopCustomer.debug.xcconfig"; sourceTree = "<group>"; };
		7304D5022A4371F0008FE96E /* PaymentSDK.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; name = PaymentSDK.xcframework; path = Pods/PayTabsSDK/sources/PaymentSDK.xcframework; sourceTree = "<group>"; };
		7305CA732BF5066B00F5C4FE /* Int+.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Int+.swift"; sourceTree = "<group>"; };
		730930472B58072700264FA3 /* best_offers.json */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.json; path = best_offers.json; sourceTree = "<group>"; };
		73190A5A2A8C786D000C31D7 /* ShadowViewOnly.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ShadowViewOnly.swift; sourceTree = "<group>"; };
		731A7BBA2D02263000B5DC75 /* GoogleMaps.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; name = GoogleMaps.xcframework; path = Pods/GoogleMaps/Maps/Frameworks/GoogleMaps.xcframework; sourceTree = "<group>"; };
		732215602C47E7F200A6C763 /* InviteFriendsGiftVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = InviteFriendsGiftVC.swift; sourceTree = "<group>"; };
		732320162C3B2F6500DE37B6 /* InviteFriendsVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = InviteFriendsVC.swift; sourceTree = "<group>"; };
		73248D992C10A24100C2E101 /* UITableViewCell+.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "UITableViewCell+.swift"; sourceTree = "<group>"; };
		7325619E2C3D7157004AB08D /* UserQRCodeModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserQRCodeModel.swift; sourceTree = "<group>"; };
		732561A02C3D774C004AB08D /* InviteFriendsPresenter.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = InviteFriendsPresenter.swift; sourceTree = "<group>"; };
		732561A22C3D78AE004AB08D /* InviteFriendsInteractor.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = InviteFriendsInteractor.swift; sourceTree = "<group>"; };
		7325FBB22AFE620E00690253 /* coins_shape.json */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.json; path = coins_shape.json; sourceTree = "<group>"; };
		7325FBB62B00444000690253 /* LastOfferTableViewCell.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = LastOfferTableViewCell.xib; sourceTree = "<group>"; };
		732D67362AB20C8400366120 /* CategoriesTableViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CategoriesTableViewCell.swift; sourceTree = "<group>"; };
		732E03342C2C4A6E008B6088 /* ChooseDeliveryDateVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ChooseDeliveryDateVC.swift; sourceTree = "<group>"; };
		732E03372C2C4E86008B6088 /* ChooseDeliveryDateCVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ChooseDeliveryDateCVC.swift; sourceTree = "<group>"; };
		732E03382C2C4E86008B6088 /* ChooseDeliveryDateCVC.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = ChooseDeliveryDateCVC.xib; sourceTree = "<group>"; };
		733007592D5F7727002474AD /* TikTokEvents.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TikTokEvents.swift; sourceTree = "<group>"; };
		73342DBD2BF3C0A3005FC06E /* Notification+.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Notification+.swift"; sourceTree = "<group>"; };
		7335B9022AFB099800C877FF /* GetPointsDescriptionModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GetPointsDescriptionModel.swift; sourceTree = "<group>"; };
		7335B9042AFB0E4B00C877FF /* RewardDetailsCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RewardDetailsCell.swift; sourceTree = "<group>"; };
		7335B9052AFB0E4B00C877FF /* RewardDetailsCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = RewardDetailsCell.xib; sourceTree = "<group>"; };
		73381ADD2D7CEC340059A27C /* ImageSliderView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ImageSliderView.swift; sourceTree = "<group>"; };
		73381ADF2D7CEFF90059A27C /* ProductDetailsViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ProductDetailsViewModel.swift; sourceTree = "<group>"; };
		73381AE42D7D00410059A27C /* SearchViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SearchViewModel.swift; sourceTree = "<group>"; };
		733E01FF2C04DF40000D972D /* SuperCollectionViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SuperCollectionViewCell.swift; sourceTree = "<group>"; };
		733E02002C04DF40000D972D /* SuperCollectionViewCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = SuperCollectionViewCell.xib; sourceTree = "<group>"; };
		73465F4A2AD7682300321243 /* CongratsRewardsVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CongratsRewardsVC.swift; sourceTree = "<group>"; };
		7346C1BD2A40C40200A9E5DF /* BaseResponseModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BaseResponseModel.swift; sourceTree = "<group>"; };
		734B9FF12C4015EA00C992C2 /* UIImageView+.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "UIImageView+.swift"; sourceTree = "<group>"; };
		73520B1A2D75D41200BD7086 /* AccountView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AccountView.swift; sourceTree = "<group>"; };
		73520B1B2D75D41200BD7086 /* CartView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CartView.swift; sourceTree = "<group>"; };
		73520B1C2D75D41200BD7086 /* CategoriesView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CategoriesView.swift; sourceTree = "<group>"; };
		73520B1D2D75D41200BD7086 /* CheckoutNewView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CheckoutNewView.swift; sourceTree = "<group>"; };
		73520B1E2D75D41200BD7086 /* CheckoutView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CheckoutView.swift; sourceTree = "<group>"; };
		73520B1F2D75D41200BD7086 /* FoodHomeView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FoodHomeView.swift; sourceTree = "<group>"; };
		73520B202D75D41200BD7086 /* MainCategoriesView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MainCategoriesView.swift; sourceTree = "<group>"; };
		73520B212D75D41200BD7086 /* MainHomeView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MainHomeView.swift; sourceTree = "<group>"; };
		73520B222D75D41200BD7086 /* MainTabBarView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MainTabBarView.swift; sourceTree = "<group>"; };
		73520B232D75D41200BD7086 /* OrdersView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OrdersView.swift; sourceTree = "<group>"; };
		73520B242D75D41200BD7086 /* ProductDetailView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ProductDetailView.swift; sourceTree = "<group>"; };
		73520B252D75D41200BD7086 /* ProfileView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ProfileView.swift; sourceTree = "<group>"; };
		73520B262D75D41200BD7086 /* SearchView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SearchView.swift; sourceTree = "<group>"; };
		7352DB912CFFB77100320EDD /* UserNotifications.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UserNotifications.framework; path = System/Library/Frameworks/UserNotifications.framework; sourceTree = SDKROOT; };
		7352DB932CFFB77100320EDD /* UserNotificationsUI.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UserNotificationsUI.framework; path = System/Library/Frameworks/UserNotificationsUI.framework; sourceTree = SDKROOT; };
		7352DBAA2CFFC23200320EDD /* ContentImageExtentionPush.appex */ = {isa = PBXFileReference; explicitFileType = "wrapper.app-extension"; includeInIndex = 0; path = ContentImageExtentionPush.appex; sourceTree = BUILT_PRODUCTS_DIR; };
		735499182ADAB2180007E26E /* RewardVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RewardVC.swift; sourceTree = "<group>"; };
		7354991D2ADAB3A80007E26E /* PointsHostoryTVCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PointsHostoryTVCell.swift; sourceTree = "<group>"; };
		7354991E2ADAB3A80007E26E /* PointsHostoryTVCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = PointsHostoryTVCell.xib; sourceTree = "<group>"; };
		735499212ADAC53B0007E26E /* PointsHostoryModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PointsHostoryModel.swift; sourceTree = "<group>"; };
		735499242ADACD440007E26E /* RewardsVCPresenter.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RewardsVCPresenter.swift; sourceTree = "<group>"; };
		735499262ADACD9D0007E26E /* RewardsVCInteractor.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RewardsVCInteractor.swift; sourceTree = "<group>"; };
		73566ACE2C43FBFF00D0AA8E /* loading_shape.json */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.json; path = loading_shape.json; sourceTree = "<group>"; };
		73566AD02C4404A300D0AA8E /* add_to_cart_animation.json */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.json; path = add_to_cart_animation.json; sourceTree = "<group>"; };
		7358CEE02B0F9F05001F3611 /* CoinsLevelsShape.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CoinsLevelsShape.swift; sourceTree = "<group>"; };
		735B63062B5D201900650BEF /* CheckCartResponse.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CheckCartResponse.swift; sourceTree = "<group>"; };
		7367EE202AD764B30042BACF /* YourRewardsVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = YourRewardsVC.swift; sourceTree = "<group>"; };
		736CE1AB2C519111006ECEBC /* add_to_cart_animation_ar.json */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.json; path = add_to_cart_animation_ar.json; sourceTree = "<group>"; };
		7370954E2AAD1F4C0089D3E2 /* UserSearchAPI.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserSearchAPI.swift; sourceTree = "<group>"; };
		737095502AAD204E0089D3E2 /* UserSearchModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserSearchModel.swift; sourceTree = "<group>"; };
		7372D9692B0E064600A9506D /* main3.json */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.json; path = main3.json; sourceTree = "<group>"; };
		7372D96A2B0E064600A9506D /* middle1.json */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.json; path = middle1.json; sourceTree = "<group>"; };
		7372D96B2B0E064600A9506D /* main2.json */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.json; path = main2.json; sourceTree = "<group>"; };
		7372D96C2B0E064600A9506D /* main5.json */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.json; path = main5.json; sourceTree = "<group>"; };
		7372D96D2B0E064600A9506D /* main4.json */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.json; path = main4.json; sourceTree = "<group>"; };
		7372D96E2B0E064600A9506D /* middle4.json */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.json; path = middle4.json; sourceTree = "<group>"; };
		7372D96F2B0E064600A9506D /* main6.json */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.json; path = main6.json; sourceTree = "<group>"; };
		7372D9702B0E064600A9506D /* middle5.json */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.json; path = middle5.json; sourceTree = "<group>"; };
		7372D9712B0E064600A9506D /* middle2.json */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.json; path = middle2.json; sourceTree = "<group>"; };
		7372D9722B0E064600A9506D /* main1.json */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.json; path = main1.json; sourceTree = "<group>"; };
		7372D9732B0E064600A9506D /* middle3.json */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.json; path = middle3.json; sourceTree = "<group>"; };
		7372ECBF2D82D37300818CD3 /* AlertType.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AlertType.swift; sourceTree = "<group>"; };
		7372ECC02D82D37300818CD3 /* ALertView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ALertView.swift; sourceTree = "<group>"; };
		73757E612A8AFBB20052D068 /* RecommendationProductCVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RecommendationProductCVC.swift; sourceTree = "<group>"; };
		73757E622A8AFBB20052D068 /* RecommendationProductCVC.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = RecommendationProductCVC.xib; sourceTree = "<group>"; };
		7376F2392AFB9D1700FAB85A /* LastOfferTableViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LastOfferTableViewCell.swift; sourceTree = "<group>"; };
		7376F23E2AFBAF3F00FAB85A /* LastOfferCollectionViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LastOfferCollectionViewCell.swift; sourceTree = "<group>"; };
		73778ABF2CF8859D00D99032 /* PaymentsByUrlVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PaymentsByUrlVC.swift; sourceTree = "<group>"; };
		73778AC02CF8859D00D99032 /* PaymentsByUrlVC.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = PaymentsByUrlVC.xib; sourceTree = "<group>"; };
		737E27EB2D7EE26D00723266 /* OrdersListView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OrdersListView.swift; sourceTree = "<group>"; };
		737E27ED2D7EE68D00723266 /* OrdersListViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OrdersListViewModel.swift; sourceTree = "<group>"; };
		737E27EF2D7EEAEA00723266 /* OrderDetailView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OrderDetailView.swift; sourceTree = "<group>"; };
		737E27F12D7EEAFA00723266 /* EmptyOrdersView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EmptyOrdersView.swift; sourceTree = "<group>"; };
		7382DE592CB7F04C0060C4B5 /* MainSliderCVCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MainSliderCVCell.swift; sourceTree = "<group>"; };
		7382DE5A2CB7F04C0060C4B5 /* MainSliderCVCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = MainSliderCVCell.xib; sourceTree = "<group>"; };
		738716D62BFFC9A200B1B8F6 /* ProductsOfBundleTVCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ProductsOfBundleTVCell.swift; sourceTree = "<group>"; };
		738716D72BFFC9A200B1B8F6 /* ProductsOfBundleTVCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = ProductsOfBundleTVCell.xib; sourceTree = "<group>"; };
		738AD26C2D6E50F700419CCA /* ViewInvoiceVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ViewInvoiceVC.swift; sourceTree = "<group>"; };
		738B81B22C1196850069DC6E /* BundleProductsView.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = BundleProductsView.xib; sourceTree = "<group>"; };
		738B81B42C1196CE0069DC6E /* BundleProductsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BundleProductsView.swift; sourceTree = "<group>"; };
		738B81B72C11A4A90069DC6E /* CartProductsOfBundleTVCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CartProductsOfBundleTVCell.swift; sourceTree = "<group>"; };
		738B81B82C11A4A90069DC6E /* CartProductsOfBundleTVCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = CartProductsOfBundleTVCell.xib; sourceTree = "<group>"; };
		738B91FD2CA53BCC00B406CB /* AppDelegate+AppsFlyer.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "AppDelegate+AppsFlyer.swift"; sourceTree = "<group>"; };
		738B91FF2CA58F5500B406CB /* AppDelegate+AppsFlyerDeepLinkDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "AppDelegate+AppsFlyerDeepLinkDelegate.swift"; sourceTree = "<group>"; };
		738BAC192AE50E1F00D42040 /* PointsLevelsModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PointsLevelsModel.swift; sourceTree = "<group>"; };
		738BAC1B2AE5380D00D42040 /* CalculateUserPointsToSARModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CalculateUserPointsToSARModel.swift; sourceTree = "<group>"; };
		7390FF802D75048600C813EC /* SelectDeliveryTypeView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SelectDeliveryTypeView.swift; sourceTree = "<group>"; };
		739A394D2D7900CC00C4847A /* LoadingImageWithUrlView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LoadingImageWithUrlView.swift; sourceTree = "<group>"; };
		739A39502D79086D00C4847A /* CartManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CartManager.swift; sourceTree = "<group>"; };
		739A398A2D79123200C4847A /* CartViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CartViewModel.swift; sourceTree = "<group>"; };
		739EBB092B065D4000E89E8B /* data5.json */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.json; path = data5.json; sourceTree = "<group>"; };
		739EBB0A2B065D4000E89E8B /* data2.json */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.json; path = data2.json; sourceTree = "<group>"; };
		739EBB0B2B065D4000E89E8B /* data1.json */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.json; path = data1.json; sourceTree = "<group>"; };
		739EBB0C2B065D4000E89E8B /* data4.json */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.json; path = data4.json; sourceTree = "<group>"; };
		739EBB0D2B065D4000E89E8B /* data3.json */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.json; path = data3.json; sourceTree = "<group>"; };
		73A565C42D89709F00BDB509 /* StepperCartView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StepperCartView.swift; sourceTree = "<group>"; };
		73A565C62D8970B800BDB509 /* SaudiRiyalAmount.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SaudiRiyalAmount.swift; sourceTree = "<group>"; };
		73A565C82D89731F00BDB509 /* OrderDetailViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OrderDetailViewModel.swift; sourceTree = "<group>"; };
		73A6CF382A988E14007F3657 /* CartYouMayAlsoLikeProductsResponse.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CartYouMayAlsoLikeProductsResponse.swift; sourceTree = "<group>"; };
		73A6F6D12B860808001DC9EA /* BundelsListResponseFields.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BundelsListResponseFields.swift; sourceTree = "<group>"; };
		73A6F6D52B861D82001DC9EA /* BundelsListTableViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BundelsListTableViewCell.swift; sourceTree = "<group>"; };
		73A6F6D62B861D82001DC9EA /* BundelsListTableViewCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = BundelsListTableViewCell.xib; sourceTree = "<group>"; };
		73A6F6D92B861DAF001DC9EA /* BundelsListCollectionViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BundelsListCollectionViewCell.swift; sourceTree = "<group>"; };
		73A6F6DA2B861DAF001DC9EA /* BundelsListCollectionViewCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = BundelsListCollectionViewCell.xib; sourceTree = "<group>"; };
		73A798872C6BBCCF0077ECE0 /* libsqlite3.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libsqlite3.tbd; path = usr/lib/libsqlite3.tbd; sourceTree = SDKROOT; };
		73A941AD2B331C2100F1C8A3 /* UICollectionViewCell+.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "UICollectionViewCell+.swift"; sourceTree = "<group>"; };
		73A9D3032B441ADD00A5CC72 /* AdvertisingCollectionViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AdvertisingCollectionViewCell.swift; sourceTree = "<group>"; };
		73A9D3042B441ADD00A5CC72 /* AdvertisingCollectionViewCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = AdvertisingCollectionViewCell.xib; sourceTree = "<group>"; };
		73ABBA592CE291F700166D7E /* CityModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CityModel.swift; sourceTree = "<group>"; };
		73B47A8F2D787551007B8FB9 /* OffersView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OffersView.swift; sourceTree = "<group>"; };
		73B47A912D787564007B8FB9 /* OffersViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OffersViewModel.swift; sourceTree = "<group>"; };
		73B47A952D787C4D007B8FB9 /* OfferCardView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OfferCardView.swift; sourceTree = "<group>"; };
		73B47AD02D788058007B8FB9 /* ProductDetailsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ProductDetailsView.swift; sourceTree = "<group>"; };
		73B47AD22D788298007B8FB9 /* OfferCardDetailsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OfferCardDetailsView.swift; sourceTree = "<group>"; };
		73B6240B2D75B4BB0064D21A /* SelectDeliveryTypeViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SelectDeliveryTypeViewModel.swift; sourceTree = "<group>"; };
		73B6240E2D75B6540064D21A /* WarehouseModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WarehouseModel.swift; sourceTree = "<group>"; };
		73B624452D75CFA20064D21A /* LoadingIndicator.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LoadingIndicator.swift; sourceTree = "<group>"; };
		73B961BD2A8C595900B92293 /* ShadowView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ShadowView.swift; sourceTree = "<group>"; };
		73C765DC2C510ABE0080282D /* invite_friends_gift_en.json */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.json; path = invite_friends_gift_en.json; sourceTree = "<group>"; };
		73C765DE2C510AD30080282D /* invite_friends_gift.json */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.json; path = invite_friends_gift.json; sourceTree = "<group>"; };
		73D0AA042B98A7A0006B3BCC /* MosquesResponseFields.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MosquesResponseFields.swift; sourceTree = "<group>"; };
		73D172322D82533400E7EF01 /* CategoriesModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CategoriesModel.swift; sourceTree = "<group>"; };
		73D172772D8256AF00E7EF01 /* TabCategoriesSelectionView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TabCategoriesSelectionView.swift; sourceTree = "<group>"; };
		73D363E82CB6B12700F1D163 /* AppsFlyerEvents.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppsFlyerEvents.swift; sourceTree = "<group>"; };
		73DC7FE12C7F171F00403CDE /* TabyPaymentManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TabyPaymentManager.swift; sourceTree = "<group>"; };
		73DCE10B2B004A0400AA4AAC /* LastOfferCollectionViewCell.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = LastOfferCollectionViewCell.xib; sourceTree = "<group>"; };
		73DEBD1F2B1626130026EB10 /* Font+.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Font+.swift"; sourceTree = "<group>"; };
		73E160932D60E90F0086A16B /* FirebaseEvents.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FirebaseEvents.swift; sourceTree = "<group>"; };
		73E185F52A8AC629008FC178 /* segoeUI.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = segoeUI.ttf; sourceTree = "<group>"; };
		73E190032A9D32D80010AE40 /* MayAlsoLikeTVCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MayAlsoLikeTVCell.swift; sourceTree = "<group>"; };
		73E190042A9D32D80010AE40 /* MayAlsoLikeTVCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = MayAlsoLikeTVCell.xib; sourceTree = "<group>"; };
		73E190072A9D4CFA0010AE40 /* UICollectionView+.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = "UICollectionView+.swift"; sourceTree = "<group>"; };
		73E595AC2D747C520043EE66 /* FloatingButtonView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FloatingButtonView.swift; sourceTree = "<group>"; };
		73E595AD2D747C520043EE66 /* FloatingButtonView.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = FloatingButtonView.xib; sourceTree = "<group>"; };
		73EBCEFE2CD010FD00EB14C4 /* AdvertisementCVCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AdvertisementCVCell.swift; sourceTree = "<group>"; };
		73EBCEFF2CD010FD00EB14C4 /* AdvertisementCVCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = AdvertisementCVCell.xib; sourceTree = "<group>"; };
		73ED99D32BCD0EDD007FBD73 /* MainButtonDesignable.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MainButtonDesignable.swift; sourceTree = "<group>"; };
		73F2D1DD2C8ECDE9004A9738 /* QuantitiesDiscountCVCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = QuantitiesDiscountCVCell.swift; sourceTree = "<group>"; };
		73F2D1DE2C8ECDE9004A9738 /* QuantitiesDiscountCVCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = QuantitiesDiscountCVCell.xib; sourceTree = "<group>"; };
		73F5A8D52D7B23F900C260DA /* YouMayAlsoLikeView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = YouMayAlsoLikeView.swift; sourceTree = "<group>"; };
		73F5A8D82D7B242200C260DA /* ProductItemView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ProductItemView.swift; sourceTree = "<group>"; };
		73F6BBDB2A9783FD0009A7E0 /* FreeDeliveryValueResponse.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FreeDeliveryValueResponse.swift; sourceTree = "<group>"; };
		73F7EFFB2D7C66DE00E35734 /* Date+.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Date+.swift"; sourceTree = "<group>"; };
		73F7F03E2D7C91E800E35734 /* CategoriesViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CategoriesViewModel.swift; sourceTree = "<group>"; };
		73F9CE472C553EAA00635DDC /* ShiftsBySelectDateCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ShiftsBySelectDateCell.swift; sourceTree = "<group>"; };
		73F9CE482C553EAA00635DDC /* ShiftsBySelectDateCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = ShiftsBySelectDateCell.xib; sourceTree = "<group>"; };
		73FCE8852ACD6A8A0014DD95 /* UILabel+.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "UILabel+.swift"; sourceTree = "<group>"; };
		73FEA82F2D7700F4001C4360 /* MarketingBannersView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MarketingBannersView.swift; sourceTree = "<group>"; };
		73FEA8312D771039001C4360 /* MainSectionView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MainSectionView.swift; sourceTree = "<group>"; };
		73FED5302D7BAC9300C9F1F0 /* CheckoutViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CheckoutViewModel.swift; sourceTree = "<group>"; };
		73FF5AE92D7659270067415B /* MainHomeViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MainHomeViewModel.swift; sourceTree = "<group>"; };
		8117DF6927E998A700004F69 /* HistoryOrdersPresenter.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HistoryOrdersPresenter.swift; sourceTree = "<group>"; };
		8117DF6A27E998A700004F69 /* HistoryOrdersViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HistoryOrdersViewController.swift; sourceTree = "<group>"; };
		8117DF6B27E998A700004F69 /* HistoryOrdersInteractor.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HistoryOrdersInteractor.swift; sourceTree = "<group>"; };
		8117DF6F27E9990B00004F69 /* HistoryOrdersTableViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HistoryOrdersTableViewCell.swift; sourceTree = "<group>"; };
		8117DF7027E9990B00004F69 /* HistoryOrdersTableViewCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = HistoryOrdersTableViewCell.xib; sourceTree = "<group>"; };
		8117DF7427E9AA5C00004F69 /* HistoryOrderDetailsPresenter.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HistoryOrderDetailsPresenter.swift; sourceTree = "<group>"; };
		8117DF7527E9AA5C00004F69 /* HistoryOrderDetailsViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HistoryOrderDetailsViewController.swift; sourceTree = "<group>"; };
		8117DF7627E9AA5C00004F69 /* HistoryOrderDetailsInteractor.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HistoryOrderDetailsInteractor.swift; sourceTree = "<group>"; };
		8117DF7A27E9B07100004F69 /* HistoryOrderDetailsTableViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HistoryOrderDetailsTableViewCell.swift; sourceTree = "<group>"; };
		8117DF7B27E9B07100004F69 /* HistoryOrderDetailsTableViewCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = HistoryOrderDetailsTableViewCell.xib; sourceTree = "<group>"; };
		81A484A227DF678F00528766 /* FSPagerView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = FSPagerView.swift; sourceTree = "<group>"; };
		81A484A327DF678F00528766 /* FSPageViewTransformer.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = FSPageViewTransformer.swift; sourceTree = "<group>"; };
		81A484A427DF678F00528766 /* FSPagerViewCell.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = FSPagerViewCell.xib; sourceTree = "<group>"; };
		81A484A527DF678F00528766 /* FSPagerViewObjcCompat.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FSPagerViewObjcCompat.h; sourceTree = "<group>"; };
		81A484A627DF678F00528766 /* FSPagerViewCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = FSPagerViewCell.swift; sourceTree = "<group>"; };
		81A484A727DF678F00528766 /* FSPagerViewLayoutAttributes.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = FSPagerViewLayoutAttributes.swift; sourceTree = "<group>"; };
		81A484A827DF678F00528766 /* FSPageViewLayout.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = FSPageViewLayout.swift; sourceTree = "<group>"; };
		81A484A927DF678F00528766 /* FSPageControl.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = FSPageControl.swift; sourceTree = "<group>"; };
		81A484AA27DF678F00528766 /* FSPagerViewObjcCompat.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FSPagerViewObjcCompat.m; sourceTree = "<group>"; };
		81A484AB27DF678F00528766 /* FSPagerCollectionView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = FSPagerCollectionView.swift; sourceTree = "<group>"; };
		81A484B527DF69AE00528766 /* CategoriesCollectionViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CategoriesCollectionViewCell.swift; sourceTree = "<group>"; };
		81A484B627DF69AE00528766 /* CategoriesCollectionViewCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = CategoriesCollectionViewCell.xib; sourceTree = "<group>"; };
		81A484B927DF6BF500528766 /* ProductsCollectionViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ProductsCollectionViewCell.swift; sourceTree = "<group>"; };
		81A484BA27DF6BF500528766 /* ProductsCollectionViewCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = ProductsCollectionViewCell.xib; sourceTree = "<group>"; };
		81A484BD27DF6F4A00528766 /* UITableViewExtensions.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = UITableViewExtensions.swift; sourceTree = "<group>"; };
		81A484BE27DF6F4A00528766 /* UICollectionViewCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = UICollectionViewCell.swift; sourceTree = "<group>"; };
		8BD0310C2D3F91100040A590 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		8BD0310D2D3F91100040A590 /* NotificationService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NotificationService.swift; sourceTree = "<group>"; };
		8C2B88409C3FEA362CAC03EA /* Pods-TopCustomer.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-TopCustomer.release.xcconfig"; path = "Target Support Files/Pods-TopCustomer/Pods-TopCustomer.release.xcconfig"; sourceTree = "<group>"; };
		9929B054DF35A09609A5E64A /* Pods-TopCustomer.staging.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-TopCustomer.staging.xcconfig"; path = "Target Support Files/Pods-TopCustomer/Pods-TopCustomer.staging.xcconfig"; sourceTree = "<group>"; };
		BE04A56B27F5FF8700EE26EA /* HelpPresenter.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HelpPresenter.swift; sourceTree = "<group>"; };
		BE04A56D27F5FF8700EE26EA /* HelpInteractor.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HelpInteractor.swift; sourceTree = "<group>"; };
		BE0D069C283E40E100F934F9 /* WebKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = WebKit.framework; path = System/Library/Frameworks/WebKit.framework; sourceTree = SDKROOT; };
		BE0EED3A29DB020B00CAE929 /* CustomPopupAlertViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CustomPopupAlertViewController.swift; sourceTree = "<group>"; };
		BE10DF1D28BBFBFE000983A2 /* MaterialLocalizeButton.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MaterialLocalizeButton.swift; sourceTree = "<group>"; };
		BE10DF1E28BBFBFE000983A2 /* MaterialLocalizeLable.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MaterialLocalizeLable.swift; sourceTree = "<group>"; };
		BE10DF1F28BBFBFE000983A2 /* MaterialLocalizeImageView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MaterialLocalizeImageView.swift; sourceTree = "<group>"; };
		BE13CB8127D6357800607C2D /* LoewNextArabic-Medium.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "LoewNextArabic-Medium.ttf"; sourceTree = "<group>"; };
		BE13CB8327D6362600607C2D /* CustomTextfieldWithFontStyle.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CustomTextfieldWithFontStyle.swift; sourceTree = "<group>"; };
		BE1B8B5227D9D1D10045FA0E /* MyOrdersPresenter.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MyOrdersPresenter.swift; sourceTree = "<group>"; };
		BE1B8B5327D9D1D10045FA0E /* MyOrdersViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MyOrdersViewController.swift; sourceTree = "<group>"; };
		BE1B8B5427D9D1D10045FA0E /* MyOrdersInteractor.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MyOrdersInteractor.swift; sourceTree = "<group>"; };
		BE1B8B5927D9D2080045FA0E /* OffersPresenter.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OffersPresenter.swift; sourceTree = "<group>"; };
		BE1B8B5A27D9D2080045FA0E /* OffersViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OffersViewController.swift; sourceTree = "<group>"; };
		BE1B8B5B27D9D2080045FA0E /* OffersInteractor.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OffersInteractor.swift; sourceTree = "<group>"; };
		BE1BC0B427FD71DD00C9E42E /* MyCartCheckoutTableViewCell.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = MyCartCheckoutTableViewCell.xib; sourceTree = "<group>"; };
		BE1BC0B527FD71DD00C9E42E /* MyCartCheckoutTableViewCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MyCartCheckoutTableViewCell.swift; sourceTree = "<group>"; };
		BE1FF68F27D886CB008291AF /* Home.storyboard */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; path = Home.storyboard; sourceTree = "<group>"; };
		BE1FF69227D8C19E008291AF /* MainTabbarViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MainTabbarViewController.swift; sourceTree = "<group>"; };
		************************ /* OTPVerificationPresenter.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = OTPVerificationPresenter.swift; sourceTree = "<group>"; };
		************************ /* OTPVerificationViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = OTPVerificationViewController.swift; sourceTree = "<group>"; };
		************************ /* OTPVerificationInteractor.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = OTPVerificationInteractor.swift; sourceTree = "<group>"; };
		************************ /* AccountInteractor.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AccountInteractor.swift; sourceTree = "<group>"; };
		************************ /* AccountPresenter.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AccountPresenter.swift; sourceTree = "<group>"; };
		************************ /* AccountViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AccountViewController.swift; sourceTree = "<group>"; };
		************************ /* ContactUsViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ContactUsViewController.swift; sourceTree = "<group>"; };
		************************ /* ContactUsPresenter.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ContactUsPresenter.swift; sourceTree = "<group>"; };
		************************ /* ContactUsInteractor.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ContactUsInteractor.swift; sourceTree = "<group>"; };
		************************ /* OTPFieldView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = OTPFieldView.swift; sourceTree = "<group>"; };
		************************ /* OTPTextField.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = OTPTextField.swift; sourceTree = "<group>"; };
		************************ /* OffersTableViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OffersTableViewCell.swift; sourceTree = "<group>"; };
		BE647A8528E836870051F5FE /* Branch.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = Branch.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		BE6621A62840B412004D0D0D /* TopCustomer.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = TopCustomer.entitlements; sourceTree = "<group>"; };
		BE6C5EA927DA123800F6C188 /* LoewNextArabic-ExtraBold.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "LoewNextArabic-ExtraBold.ttf"; sourceTree = "<group>"; };
		BE7A8398291132F80078FD2F /* BannerInfoPresenter.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BannerInfoPresenter.swift; sourceTree = "<group>"; };
		BE7A8399291132F80078FD2F /* BannerInfoViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BannerInfoViewController.swift; sourceTree = "<group>"; };
		BE7A839A291132F80078FD2F /* BannerInfoInteractor.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BannerInfoInteractor.swift; sourceTree = "<group>"; };
		BE822D4C291904BB00E91318 /* HomeTableViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HomeTableViewCell.swift; sourceTree = "<group>"; };
		BE8CBF6428EC393800A3BB7A /* Material_video.mp4 */ = {isa = PBXFileReference; lastKnownFileType = file; path = Material_video.mp4; sourceTree = "<group>"; };
		BE8CBF6528EC39FF00A3BB7A /* SplashViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SplashViewController.swift; sourceTree = "<group>"; };
		BE8D82E527D5E54B00A11D15 /* LoginPresenter.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LoginPresenter.swift; sourceTree = "<group>"; };
		BE8D82E627D5E54B00A11D15 /* LoginViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LoginViewController.swift; sourceTree = "<group>"; };
		BE8D82E727D5E54B00A11D15 /* LoginInteractor.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LoginInteractor.swift; sourceTree = "<group>"; };
		BE8D82EC27D5E90F00A11D15 /* CustomRoundedButtton.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CustomRoundedButtton.swift; sourceTree = "<group>"; };
		BE8D82EE27D5EA7C00A11D15 /* UIColor+Extension.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = "UIColor+Extension.swift"; sourceTree = "<group>"; };
		BE8D82F127D604E100A11D15 /* LoewNextArabic-Bold.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "LoewNextArabic-Bold.ttf"; sourceTree = "<group>"; };
		BE8D82F227D605FB00A11D15 /* ar */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = ar; path = ar.lproj/Main.strings; sourceTree = "<group>"; };
		BE8D82F327D605FB00A11D15 /* ar */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = ar; path = ar.lproj/LaunchScreen.strings; sourceTree = "<group>"; };
		BE8D82F827D6069000A11D15 /* en */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = en; path = en.lproj/Localizable.strings; sourceTree = "<group>"; };
		BE8D82FA27D6069700A11D15 /* ar */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = ar; path = ar.lproj/Localizable.strings; sourceTree = "<group>"; };
		BE90222A27E344CA00F9ACE4 /* SelectLanguagePresenter.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SelectLanguagePresenter.swift; sourceTree = "<group>"; };
		BE90222B27E344CA00F9ACE4 /* SelectLanguageInteractor.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SelectLanguageInteractor.swift; sourceTree = "<group>"; };
		BE90222C27E344CA00F9ACE4 /* SelectLanguageViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SelectLanguageViewController.swift; sourceTree = "<group>"; };
		BE90223127E344D900F9ACE4 /* SelectLocationViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SelectLocationViewController.swift; sourceTree = "<group>"; };
		BE90223227E344D900F9ACE4 /* SelectLocationInteractor.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SelectLocationInteractor.swift; sourceTree = "<group>"; };
		BE90223327E344D900F9ACE4 /* SelectLocationPresenter.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SelectLocationPresenter.swift; sourceTree = "<group>"; };
		BE90223827E3454800F9ACE4 /* BottomPopupUtils.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = BottomPopupUtils.swift; sourceTree = "<group>"; };
		BE90223927E3454800F9ACE4 /* BottomPopupViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = BottomPopupViewController.swift; sourceTree = "<group>"; };
		BE90223A27E3454800F9ACE4 /* BottomPopupDismissInteractionController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = BottomPopupDismissInteractionController.swift; sourceTree = "<group>"; };
		BE90223B27E3454800F9ACE4 /* BottomPopupNavigationController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = BottomPopupNavigationController.swift; sourceTree = "<group>"; };
		BE90223C27E3454800F9ACE4 /* BottomPopupDismissAnimator.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = BottomPopupDismissAnimator.swift; sourceTree = "<group>"; };
		BE90223D27E3454800F9ACE4 /* BottomPopupPresentAnimator.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = BottomPopupPresentAnimator.swift; sourceTree = "<group>"; };
		BE90223E27E3454800F9ACE4 /* BottomPopupTransitionHandler.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = BottomPopupTransitionHandler.swift; sourceTree = "<group>"; };
		BE90223F27E3454800F9ACE4 /* BottomPopupPresentationController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = BottomPopupPresentationController.swift; sourceTree = "<group>"; };
		************************ /* Settings.storyboard */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; path = Settings.storyboard; sourceTree = "<group>"; };
		BE93F86527EDE30E0019120C /* QTYButton.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = QTYButton.swift; sourceTree = "<group>"; };
		BE93F86627EDE30E0019120C /* QTYButtons.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = QTYButtons.h; sourceTree = "<group>"; };
		BE93F86727EDE30E0019120C /* QTYButton.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = QTYButton.xib; sourceTree = "<group>"; };
		BE93F87B27EDE3B20019120C /* MyCartInteractor.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MyCartInteractor.swift; sourceTree = "<group>"; };
		BE93F87C27EDE3B20019120C /* MyCartViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MyCartViewController.swift; sourceTree = "<group>"; };
		BE93F87D27EDE3B20019120C /* MyCartPresenter.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MyCartPresenter.swift; sourceTree = "<group>"; };
		BE93F87F27EDE3B20019120C /* CheckoutInteractor.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CheckoutInteractor.swift; sourceTree = "<group>"; };
		BE93F88027EDE3B20019120C /* CheckoutViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CheckoutViewController.swift; sourceTree = "<group>"; };
		BE93F88127EDE3B20019120C /* CheckoutPresenter.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CheckoutPresenter.swift; sourceTree = "<group>"; };
		BE93F88227EDE3B20019120C /* MyCartTableViewCell.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = MyCartTableViewCell.xib; sourceTree = "<group>"; };
		BE93F88327EDE3B20019120C /* MyCartTableViewCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MyCartTableViewCell.swift; sourceTree = "<group>"; };
		************************ /* MyCart.storyboard */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; path = MyCart.storyboard; sourceTree = "<group>"; };
		BE94FADB27FC573E00B131DD /* InvoicePresenter.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = InvoicePresenter.swift; sourceTree = "<group>"; };
		BE94FADC27FC573E00B131DD /* InvoiceViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = InvoiceViewController.swift; sourceTree = "<group>"; };
		BE94FADD27FC573E00B131DD /* InvoiceInteractor.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = InvoiceInteractor.swift; sourceTree = "<group>"; };
		BE94FAE227FC5D7F00B131DD /* ScheduledOrderDetailPresenter.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ScheduledOrderDetailPresenter.swift; sourceTree = "<group>"; };
		BE94FAE327FC5D7F00B131DD /* ScheduledOrderDetailViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ScheduledOrderDetailViewController.swift; sourceTree = "<group>"; };
		BE94FAE427FC5D7F00B131DD /* ScheduledOrderDetailInteractor.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ScheduledOrderDetailInteractor.swift; sourceTree = "<group>"; };
		BE9568EC27F6FBFB002E96C3 /* FilterSideMenuPresenter.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FilterSideMenuPresenter.swift; sourceTree = "<group>"; };
		BE9568ED27F6FBFB002E96C3 /* FilterSideMenuViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FilterSideMenuViewController.swift; sourceTree = "<group>"; };
		BE9568EE27F6FBFB002E96C3 /* FilterSideMenuInteractor.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FilterSideMenuInteractor.swift; sourceTree = "<group>"; };
		BE9655FF280D389A00DB6E83 /* User.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = User.swift; sourceTree = "<group>"; };
		BE973F0227E85B0B00C09458 /* ActivityIndicator.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ActivityIndicator.swift; sourceTree = "<group>"; };
		BE973F0427E88CC300C09458 /* LoewNextArabic-Heavy.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "LoewNextArabic-Heavy.ttf"; sourceTree = "<group>"; };
		BE97639827E2081400F29744 /* CurrentOrdersPresenter.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CurrentOrdersPresenter.swift; sourceTree = "<group>"; };
		BE97639927E2081400F29744 /* CurrentOrdersViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CurrentOrdersViewController.swift; sourceTree = "<group>"; };
		BE97639A27E2081400F29744 /* CurrentOrdersInteractor.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CurrentOrdersInteractor.swift; sourceTree = "<group>"; };
		BE97639F27E2085E00F29744 /* ScheduledOrdersPresenter.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ScheduledOrdersPresenter.swift; sourceTree = "<group>"; };
		BE9763A027E2085E00F29744 /* ScheduledOrdersViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ScheduledOrdersViewController.swift; sourceTree = "<group>"; };
		BE9763A127E2085E00F29744 /* ScheduledOrdersInteractor.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ScheduledOrdersInteractor.swift; sourceTree = "<group>"; };
		BE9763A527E2105F00F29744 /* ViewEmbedder.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ViewEmbedder.swift; sourceTree = "<group>"; };
		BE99782D27E0B8BC005CC308 /* MyOrdersTableViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MyOrdersTableViewCell.swift; sourceTree = "<group>"; };
		BE9A1D1627D641CA0072BCAC /* CountryCode.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CountryCode.swift; sourceTree = "<group>"; };
		BE9A1D1727D641CA0072BCAC /* CountrySelectView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CountrySelectView.swift; sourceTree = "<group>"; };
		BE9A1D1827D641CA0072BCAC /* CountryTableViewCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CountryTableViewCell.swift; sourceTree = "<group>"; };
		BE9A1D1927D641CA0072BCAC /* CountryPicker.bundle */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.plug-in"; path = CountryPicker.bundle; sourceTree = "<group>"; };
		BE9A1D2527D64D630072BCAC /* BaseViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = BaseViewController.swift; sourceTree = "<group>"; };
		BE9A1D2727D64EB70072BCAC /* AppSuportedLanguages.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AppSuportedLanguages.swift; sourceTree = "<group>"; };
		BE9A1D2927D64F9A0072BCAC /* AppSingleton.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AppSingleton.swift; sourceTree = "<group>"; };
		BE9B3A4227FEF83D00EF3615 /* PaymentsPresenter.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PaymentsPresenter.swift; sourceTree = "<group>"; };
		BE9B3A4327FEF83D00EF3615 /* PaymentsViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PaymentsViewController.swift; sourceTree = "<group>"; };
		BE9B3A4427FEF83D00EF3615 /* PaymentsInteractor.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PaymentsInteractor.swift; sourceTree = "<group>"; };
		BE9B3A4827FEF88400EF3615 /* Payments.storyboard */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; path = Payments.storyboard; sourceTree = "<group>"; };
		BE9B3A4A27FEFB1A00EF3615 /* PaymentCardListTableViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PaymentCardListTableViewCell.swift; sourceTree = "<group>"; };
		BE9B3A4D27FF21B800EF3615 /* ChoosePaymentPresenter.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ChoosePaymentPresenter.swift; sourceTree = "<group>"; };
		BE9B3A4E27FF21B800EF3615 /* ChoosePaymentViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ChoosePaymentViewController.swift; sourceTree = "<group>"; };
		BE9B3A4F27FF21B800EF3615 /* ChoosePaymentInteractor.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ChoosePaymentInteractor.swift; sourceTree = "<group>"; };
		BE9B3A5427FF49D000EF3615 /* AddCardPresenter.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AddCardPresenter.swift; sourceTree = "<group>"; };
		BE9B3A5527FF49D000EF3615 /* AddCardViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AddCardViewController.swift; sourceTree = "<group>"; };
		BE9B3A5627FF49D000EF3615 /* AddCardInteractor.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AddCardInteractor.swift; sourceTree = "<group>"; };
		BE9FEF1027EC9300002CAB93 /* CurrentOrdersTableViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CurrentOrdersTableViewCell.swift; sourceTree = "<group>"; };
		BE9FEF1227EC9CE2002CAB93 /* ScheduledOrdersTableViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ScheduledOrdersTableViewCell.swift; sourceTree = "<group>"; };
		BEA7620B2886ACF200A890E4 /* TrackOrderPresenter.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TrackOrderPresenter.swift; sourceTree = "<group>"; };
		BEA7620C2886ACF200A890E4 /* TrackOrderViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TrackOrderViewController.swift; sourceTree = "<group>"; };
		BEA7620D2886ACF200A890E4 /* TrackOrderInteractor.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TrackOrderInteractor.swift; sourceTree = "<group>"; };
		BEA7B9632836A44F00F9F235 /* LocationManager.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = LocationManager.swift; sourceTree = "<group>"; };
		BEA85B272876E26700F4754E /* CategoriesCollectionViewCellBig.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = CategoriesCollectionViewCellBig.xib; sourceTree = "<group>"; };
		BEA85B282876E26700F4754E /* CategoriesCollectionViewCellBig.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CategoriesCollectionViewCellBig.swift; sourceTree = "<group>"; };
		BEA9574228DDDA8900DCFFF9 /* LoaderAnimation.json */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.json; path = LoaderAnimation.json; sourceTree = "<group>"; };
		BEA9574328DDDA8A00DCFFF9 /* LoadingView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = LoadingView.swift; sourceTree = "<group>"; };
		BEAAEC7C2850825F005A1D49 /* SocketIOManager.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SocketIOManager.swift; sourceTree = "<group>"; };
		BEAB6E7E28BCD3D000D47F96 /* MaterialLocalizeTextfield.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MaterialLocalizeTextfield.swift; sourceTree = "<group>"; };
		BEABE2732865F2AD0033AB1D /* Date+Extension.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = "Date+Extension.swift"; sourceTree = "<group>"; };
		BEADE08C28C9D2870095913E /* HelpViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = HelpViewController.swift; sourceTree = "<group>"; };
		BEADE93E286D73B000556D1B /* ProductsCollectionViewCellSmall.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = ProductsCollectionViewCellSmall.xib; sourceTree = "<group>"; };
		BEADE93F286D73B000556D1B /* ProductsCollectionViewCellSmall.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ProductsCollectionViewCellSmall.swift; sourceTree = "<group>"; };
		BEAEE5CC28BE2E210098946C /* ReferenceVC.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ReferenceVC.swift; sourceTree = "<group>"; };
		BEAF3DC3288BD8A0006DE167 /* GoogleAPIHelper.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = GoogleAPIHelper.swift; sourceTree = "<group>"; };
		BEAF3DC6288BDA08006DE167 /* GoogleDistanceMetrixElement.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = GoogleDistanceMetrixElement.swift; sourceTree = "<group>"; };
		BEAF3DC7288BDA08006DE167 /* GoogleDistanceMetrixDuration.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = GoogleDistanceMetrixDuration.swift; sourceTree = "<group>"; };
		BEAF3DC8288BDA08006DE167 /* GoogleDistanceMetrixRow.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = GoogleDistanceMetrixRow.swift; sourceTree = "<group>"; };
		BEAF3DC9288BDA08006DE167 /* GoogleDistanceMetrixDistance.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = GoogleDistanceMetrixDistance.swift; sourceTree = "<group>"; };
		BEAF3DCA288BDA08006DE167 /* GoogleDistanceMetrixRootClass.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = GoogleDistanceMetrixRootClass.swift; sourceTree = "<group>"; };
		BEAF3DCC288BDA09006DE167 /* GoogleDirectionDetailsLeg.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = GoogleDirectionDetailsLeg.swift; sourceTree = "<group>"; };
		BEAF3DCD288BDA09006DE167 /* GoogleDirectionDetailsEndLocation.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = GoogleDirectionDetailsEndLocation.swift; sourceTree = "<group>"; };
		BEAF3DCE288BDA09006DE167 /* GoogleDirectionDetailsOverviewPolyline.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = GoogleDirectionDetailsOverviewPolyline.swift; sourceTree = "<group>"; };
		BEAF3DCF288BDA09006DE167 /* GoogleDirectionDetailsStep.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = GoogleDirectionDetailsStep.swift; sourceTree = "<group>"; };
		BEAF3DD0288BDA09006DE167 /* GoogleDirectionDetailsBound.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = GoogleDirectionDetailsBound.swift; sourceTree = "<group>"; };
		BEAF3DD1288BDA09006DE167 /* GoogleDirectionDetailsSouthwest.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = GoogleDirectionDetailsSouthwest.swift; sourceTree = "<group>"; };
		BEAF3DD2288BDA09006DE167 /* GoogleDirectionDetailsLocation.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = GoogleDirectionDetailsLocation.swift; sourceTree = "<group>"; };
		BEAF3DD3288BDA09006DE167 /* GoogleDirectionDetailsDistance.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = GoogleDirectionDetailsDistance.swift; sourceTree = "<group>"; };
		BEAF3DD4288BDA09006DE167 /* GoogleDirectionDetailsStartLocation.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = GoogleDirectionDetailsStartLocation.swift; sourceTree = "<group>"; };
		BEAF3DD5288BDA09006DE167 /* GoogleDirectionDetailsDuration.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = GoogleDirectionDetailsDuration.swift; sourceTree = "<group>"; };
		BEAF3DD6288BDA09006DE167 /* GoogleDirectionDetailsNortheast.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = GoogleDirectionDetailsNortheast.swift; sourceTree = "<group>"; };
		BEAF3DD7288BDA09006DE167 /* GoogleDirectionDetailsViaWaypoint.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = GoogleDirectionDetailsViaWaypoint.swift; sourceTree = "<group>"; };
		BEAF3DD8288BDA09006DE167 /* GoogleDirectionDetailsPolyline.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = GoogleDirectionDetailsPolyline.swift; sourceTree = "<group>"; };
		BEAF3DD9288BDA09006DE167 /* GoogleDirectionDetailsGeocodedWaypoint.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = GoogleDirectionDetailsGeocodedWaypoint.swift; sourceTree = "<group>"; };
		BEAF3DDA288BDA09006DE167 /* GoogleDirectionDetailsRoute.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = GoogleDirectionDetailsRoute.swift; sourceTree = "<group>"; };
		BEAF3DDB288BDA09006DE167 /* GoogleDirectionDetailsRootClass.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = GoogleDirectionDetailsRootClass.swift; sourceTree = "<group>"; };
		BEAFCB9D2995265900276C1D /* FloatRatingView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = FloatRatingView.swift; sourceTree = "<group>"; };
		BEB2C56F27CCC57E00C5FBCD /* GoogleService-Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = "GoogleService-Info.plist"; sourceTree = "<group>"; };
		BEB3CADC2918DC1B001C400E /* NewHomePresenter.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = NewHomePresenter.swift; sourceTree = "<group>"; };
		BEB3CADD2918DC1B001C400E /* NewHomeViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = NewHomeViewController.swift; sourceTree = "<group>"; };
		BEB3CADE2918DC1B001C400E /* NewHomeInteractor.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = NewHomeInteractor.swift; sourceTree = "<group>"; };
		BEB4FE1327F5A7D0006B5BCC /* ThankYouPresenter.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ThankYouPresenter.swift; sourceTree = "<group>"; };
		BEB4FE1427F5A7D0006B5BCC /* ThankYouViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ThankYouViewController.swift; sourceTree = "<group>"; };
		BEB4FE1527F5A7D0006B5BCC /* ThankYouInteractor.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ThankYouInteractor.swift; sourceTree = "<group>"; };
		BEB4FE1A27F5AFCD006B5BCC /* OfferDetailsPresenter.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OfferDetailsPresenter.swift; sourceTree = "<group>"; };
		BEB4FE1B27F5AFCD006B5BCC /* OfferDetailsViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OfferDetailsViewController.swift; sourceTree = "<group>"; };
		BEB4FE1C27F5AFCD006B5BCC /* OfferDetailsInteractor.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OfferDetailsInteractor.swift; sourceTree = "<group>"; };
		BEB4FE2127F5CC13006B5BCC /* OrderDetailPresenter.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OrderDetailPresenter.swift; sourceTree = "<group>"; };
		BEB4FE2227F5CC13006B5BCC /* OrderDetailViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OrderDetailViewController.swift; sourceTree = "<group>"; };
		BEB4FE2327F5CC13006B5BCC /* OrderDetailInteractor.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OrderDetailInteractor.swift; sourceTree = "<group>"; };
		BEB5BE2828B773690075CF42 /* QTYButtonOLD.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = QTYButtonOLD.swift; sourceTree = "<group>"; };
		BEB5BE2928B773690075CF42 /* QTYButtonOLD.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = QTYButtonOLD.h; sourceTree = "<group>"; };
		BEB5BE2A28B773690075CF42 /* QTYButtonOLD.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = QTYButtonOLD.xib; sourceTree = "<group>"; };
		BEBA109827E9F32C0063E617 /* ProductPopup.storyboard */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; path = ProductPopup.storyboard; sourceTree = "<group>"; };
		BEBA109B27E9F3610063E617 /* ProductPopupInteractor.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ProductPopupInteractor.swift; sourceTree = "<group>"; };
		BEBA109C27E9F3610063E617 /* ProductPopupViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ProductPopupViewController.swift; sourceTree = "<group>"; };
		BEBA109D27E9F3610063E617 /* ProductPopupPresenter.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ProductPopupPresenter.swift; sourceTree = "<group>"; };
		BEBF066627D7769D00C3AF62 /* SettingsPresenter.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SettingsPresenter.swift; sourceTree = "<group>"; };
		BEBF066727D7769D00C3AF62 /* SettingsViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SettingsViewController.swift; sourceTree = "<group>"; };
		BEBF066827D7769D00C3AF62 /* SettingsInteractor.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SettingsInteractor.swift; sourceTree = "<group>"; };
		BEBF066D27D7844600C3AF62 /* MirroringLabel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MirroringLabel.swift; sourceTree = "<group>"; };
		BEBF066E27D7844600C3AF62 /* MirroringViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MirroringViewController.swift; sourceTree = "<group>"; };
		BEBF066F27D7844600C3AF62 /* L012Localizer.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = L012Localizer.swift; sourceTree = "<group>"; };
		BEBF067027D7844600C3AF62 /* L102Language.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = L102Language.swift; sourceTree = "<group>"; };
		BEBF067527D78A9200C3AF62 /* SettingsTableViewCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SettingsTableViewCell.swift; sourceTree = "<group>"; };
		BEBF067827D79F0B00C3AF62 /* StaticPagePresenter.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StaticPagePresenter.swift; sourceTree = "<group>"; };
		BEBF067927D79F0B00C3AF62 /* StaticPageViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StaticPageViewController.swift; sourceTree = "<group>"; };
		BEBF067A27D79F0B00C3AF62 /* StaticPageInteractor.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StaticPageInteractor.swift; sourceTree = "<group>"; };
		BEC0146527D61E9D00509E26 /* UIStoryboard+Helper.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = "UIStoryboard+Helper.swift"; sourceTree = "<group>"; };
		BEC0146727D6232900509E26 /* CustomViewForFields.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CustomViewForFields.swift; sourceTree = "<group>"; };
		BEC2851327FAD25800A998A8 /* FilterSearchPresenter.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FilterSearchPresenter.swift; sourceTree = "<group>"; };
		BEC2851427FAD25800A998A8 /* FilterSearchViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FilterSearchViewController.swift; sourceTree = "<group>"; };
		BEC2851527FAD25800A998A8 /* FilterSearchInteractor.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FilterSearchInteractor.swift; sourceTree = "<group>"; };
		BEC2851A27FAF6A200A998A8 /* AddAddressPresenter.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AddAddressPresenter.swift; sourceTree = "<group>"; };
		BEC2851B27FAF6A200A998A8 /* AddAddressViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AddAddressViewController.swift; sourceTree = "<group>"; };
		BEC2851C27FAF6A200A998A8 /* AddAddressInteractor.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AddAddressInteractor.swift; sourceTree = "<group>"; };
		BEC2E9D729CA27AB00B837F8 /* CheckoutShiftsCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CheckoutShiftsCell.swift; sourceTree = "<group>"; };
		BEC2E9D829CA27AB00B837F8 /* CheckoutShiftsCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = CheckoutShiftsCell.xib; sourceTree = "<group>"; };
		BEC3587729642F910005040C /* AdvertisementPopupPresenter.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AdvertisementPopupPresenter.swift; sourceTree = "<group>"; };
		BEC3587829642F910005040C /* AdvertisementPopupViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AdvertisementPopupViewController.swift; sourceTree = "<group>"; };
		BEC3587929642F910005040C /* AdvertisementPopupInteractor.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AdvertisementPopupInteractor.swift; sourceTree = "<group>"; };
		BEC898B628FFFF22008AE165 /* SearchSuggestionCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SearchSuggestionCell.swift; sourceTree = "<group>"; };
		BEC97A0A29E030740049E7C6 /* done_icon.json */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.json; path = done_icon.json; sourceTree = "<group>"; };
		BECA3B7C28217E8C00675D20 /* AddressListTableViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AddressListTableViewCell.swift; sourceTree = "<group>"; };
		BECDBAA129D56A60008E8EAC /* VersionUpdatePresenter.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VersionUpdatePresenter.swift; sourceTree = "<group>"; };
		BECDBAA229D56A60008E8EAC /* VersionUpdateViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VersionUpdateViewController.swift; sourceTree = "<group>"; };
		BECDBAA329D56A60008E8EAC /* VersionUpdateInteractor.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VersionUpdateInteractor.swift; sourceTree = "<group>"; };
		BED0E5B129B73EDC00CF14FA /* HomeBannerCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HomeBannerCell.swift; sourceTree = "<group>"; };
		BED25AE428F814670048062D /* MixpanelEvents.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MixpanelEvents.swift; sourceTree = "<group>"; };
		BEDD1C8A2A0243E0007D4E4B /* APIHelper.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = APIHelper.swift; sourceTree = "<group>"; };
		BEDD1C8B2A0243E0007D4E4B /* AlamofireImplementations.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AlamofireImplementations.swift; sourceTree = "<group>"; };
		BEDD1C8C2A0243E0007D4E4B /* JSONValue.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = JSONValue.swift; sourceTree = "<group>"; };
		BEDD1C8E2A0243E0007D4E4B /* UserAPI.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = UserAPI.swift; sourceTree = "<group>"; };
		BEDD1C8F2A0243E0007D4E4B /* APPVersionCheckAPI.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = APPVersionCheckAPI.swift; sourceTree = "<group>"; };
		BEDD1C902A0243E0007D4E4B /* ContactUsAPI.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ContactUsAPI.swift; sourceTree = "<group>"; };
		BEDD1C912A0243E0007D4E4B /* OrderAPI.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = OrderAPI.swift; sourceTree = "<group>"; };
		BEDD1C922A0243E0007D4E4B /* NotificationAPI.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = NotificationAPI.swift; sourceTree = "<group>"; };
		BEDD1C932A0243E0007D4E4B /* UserCardsAPI.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = UserCardsAPI.swift; sourceTree = "<group>"; };
		BEDD1C942A0243E0007D4E4B /* UserWalletAPI.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = UserWalletAPI.swift; sourceTree = "<group>"; };
		BEDD1C952A0243E0007D4E4B /* CartAPI.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CartAPI.swift; sourceTree = "<group>"; };
		BEDD1C962A0243E0007D4E4B /* DriverAPI.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = DriverAPI.swift; sourceTree = "<group>"; };
		BEDD1C972A0243E0007D4E4B /* RatingAPI.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = RatingAPI.swift; sourceTree = "<group>"; };
		BEDD1C982A0243E0007D4E4B /* FavouriteAPI.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = FavouriteAPI.swift; sourceTree = "<group>"; };
		BEDD1C992A0243E0007D4E4B /* AuthenticationAPI.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AuthenticationAPI.swift; sourceTree = "<group>"; };
		BEDD1C9A2A0243E0007D4E4B /* ProductAPI.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ProductAPI.swift; sourceTree = "<group>"; };
		BEDD1C9B2A0243E0007D4E4B /* NotifyMeAPI.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = NotifyMeAPI.swift; sourceTree = "<group>"; };
		BEDD1C9C2A0243E0007D4E4B /* AdvertisementAPI.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AdvertisementAPI.swift; sourceTree = "<group>"; };
		BEDD1C9D2A0243E0007D4E4B /* CategoryAPI.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CategoryAPI.swift; sourceTree = "<group>"; };
		BEDD1C9E2A0243E0007D4E4B /* OfferAPI.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = OfferAPI.swift; sourceTree = "<group>"; };
		BEDD1C9F2A0243E0007D4E4B /* CountryAPI.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CountryAPI.swift; sourceTree = "<group>"; };
		BEDD1CA02A0243E0007D4E4B /* PromocodeAPI.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = PromocodeAPI.swift; sourceTree = "<group>"; };
		BEDD1CA12A0243E0007D4E4B /* BannerAPI.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = BannerAPI.swift; sourceTree = "<group>"; };
		BEDD1CA22A0243E0007D4E4B /* CommonAPI.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CommonAPI.swift; sourceTree = "<group>"; };
		BEDD1CA32A0243E0007D4E4B /* CodableHelper.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CodableHelper.swift; sourceTree = "<group>"; };
		BEDD1CA42A0243E0007D4E4B /* JSONEncodableEncoding.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = JSONEncodableEncoding.swift; sourceTree = "<group>"; };
		BEDD1CA52A0243E0007D4E4B /* APIs.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = APIs.swift; sourceTree = "<group>"; };
		BEDD1CA72A0243E0007D4E4B /* OauthGuestloginBody.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = OauthGuestloginBody.swift; sourceTree = "<group>"; };
		BEDD1CA82A0243E0007D4E4B /* SkipRatingResponse.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SkipRatingResponse.swift; sourceTree = "<group>"; };
		BEDD1CA92A0243E0007D4E4B /* LatestHomeWebListingResponse.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = LatestHomeWebListingResponse.swift; sourceTree = "<group>"; };
		BEDD1CAA2A0243E0007D4E4B /* GetCartResponse.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = GetCartResponse.swift; sourceTree = "<group>"; };
		BEDD1CAB2A0243E0007D4E4B /* ProductSuggestionResponseFields.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ProductSuggestionResponseFields.swift; sourceTree = "<group>"; };
		BEDD1CAC2A0243E0007D4E4B /* DriverProductCountResponse.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = DriverProductCountResponse.swift; sourceTree = "<group>"; };
		BEDD1CAD2A0243E0007D4E4B /* WebCategoryListResponseFields.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = WebCategoryListResponseFields.swift; sourceTree = "<group>"; };
		BEDD1CAE2A0243E0007D4E4B /* OauthSocialsigninBody.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = OauthSocialsigninBody.swift; sourceTree = "<group>"; };
		BEDD1CAF2A0243E0007D4E4B /* ProductLatestproductlistingBody.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ProductLatestproductlistingBody.swift; sourceTree = "<group>"; };
		BEDD1CB02A0243E0007D4E4B /* AppVersionResponse.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AppVersionResponse.swift; sourceTree = "<group>"; };
		BEDD1CB12A0243E0007D4E4B /* PromocodeCheckResponseFields.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = PromocodeCheckResponseFields.swift; sourceTree = "<group>"; };
		BEDD1CB22A0243E0007D4E4B /* OrderCreateorderBody.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = OrderCreateorderBody.swift; sourceTree = "<group>"; };
		BEDD1CB32A0243E0007D4E4B /* ProductInfoResponse.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ProductInfoResponse.swift; sourceTree = "<group>"; };
		BEDD1CB42A0243E0007D4E4B /* WalletDetailsResponseFields.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = WalletDetailsResponseFields.swift; sourceTree = "<group>"; };
		BEDD1CB52A0243E0007D4E4B /* AppVersionResponseFields.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AppVersionResponseFields.swift; sourceTree = "<group>"; };
		BEDD1CB62A0243E0007D4E4B /* DriverWeeklyScheduleResponseFields.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = DriverWeeklyScheduleResponseFields.swift; sourceTree = "<group>"; };
		BEDD1CB72A0243E0007D4E4B /* AdvertisementResponseFields.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AdvertisementResponseFields.swift; sourceTree = "<group>"; };
		BEDD1CB82A0243E0007D4E4B /* UserChangepasswordBody.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = UserChangepasswordBody.swift; sourceTree = "<group>"; };
		BEDD1CB92A0243E0007D4E4B /* DriverOrdercancelBody.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = DriverOrdercancelBody.swift; sourceTree = "<group>"; };
		BEDD1CBA2A0243E0007D4E4B /* UserAddusercardsBody.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = UserAddusercardsBody.swift; sourceTree = "<group>"; };
		BEDD1CBB2A0243E0007D4E4B /* BannerResponse.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = BannerResponse.swift; sourceTree = "<group>"; };
		BEDD1CBC2A0243E0007D4E4B /* UpdateLanguageResponse.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = UpdateLanguageResponse.swift; sourceTree = "<group>"; };
		BEDD1CBD2A0243E0007D4E4B /* ProductResponseFields.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ProductResponseFields.swift; sourceTree = "<group>"; };
		BEDD1CBE2A0243E0007D4E4B /* LatestHomeWebListingResponseFields.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = LatestHomeWebListingResponseFields.swift; sourceTree = "<group>"; };
		BEDD1CBF2A0243E0007D4E4B /* ShiftResponseFields.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ShiftResponseFields.swift; sourceTree = "<group>"; };
		BEDD1CC02A0243E0007D4E4B /* AdvertisementResponse.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AdvertisementResponse.swift; sourceTree = "<group>"; };
		BEDD1CC12A0243E0007D4E4B /* DetailFields.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = DetailFields.swift; sourceTree = "<group>"; };
		BEDD1CC22A0243E0007D4E4B /* ProductProductsuggestionBody.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ProductProductsuggestionBody.swift; sourceTree = "<group>"; };
		BEDD1CC32A0243E0007D4E4B /* OfferResponse.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = OfferResponse.swift; sourceTree = "<group>"; };
		BEDD1CC42A0243E0007D4E4B /* DriverProductCountResponseFields.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = DriverProductCountResponseFields.swift; sourceTree = "<group>"; };
		BEDD1CC52A0243E0007D4E4B /* CheckappversionBody.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CheckappversionBody.swift; sourceTree = "<group>"; };
		BEDD1CC62A0243E0007D4E4B /* LatestProductListResponse.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = LatestProductListResponse.swift; sourceTree = "<group>"; };
		BEDD1CC72A0243E0007D4E4B /* PromocodePromocodecheckBody.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = PromocodePromocodecheckBody.swift; sourceTree = "<group>"; };
		BEDD1CC82A0243E0007D4E4B /* OauthSignupBody.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = OauthSignupBody.swift; sourceTree = "<group>"; };
		BEDD1CC92A0243E0007D4E4B /* TransactionResponse.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = TransactionResponse.swift; sourceTree = "<group>"; };
		BEDD1CCA2A0243E0007D4E4B /* WebProductResponseFields.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = WebProductResponseFields.swift; sourceTree = "<group>"; };
		BEDD1CCB2A0243E0007D4E4B /* DriverCompletedOrderDeatailsResponse.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = DriverCompletedOrderDeatailsResponse.swift; sourceTree = "<group>"; };
		BEDD1CCC2A0243E0007D4E4B /* DriverOrderDeatailsResponse.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = DriverOrderDeatailsResponse.swift; sourceTree = "<group>"; };
		BEDD1CCD2A0243E0007D4E4B /* ProductInfoResponseFields.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ProductInfoResponseFields.swift; sourceTree = "<group>"; };
		BEDD1CCE2A0243E0007D4E4B /* CategoryListResponseFields.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CategoryListResponseFields.swift; sourceTree = "<group>"; };
		BEDD1CCF2A0243E0007D4E4B /* OrderedProductDetailResponseFields.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = OrderedProductDetailResponseFields.swift; sourceTree = "<group>"; };
		BEDD1CD02A0243E0007D4E4B /* OrderListResponse.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = OrderListResponse.swift; sourceTree = "<group>"; };
		BEDD1CD12A0243E0007D4E4B /* ContentPageResponse.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ContentPageResponse.swift; sourceTree = "<group>"; };
		BEDD1CD22A0243E0007D4E4B /* OrderGetorderlistBody.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = OrderGetorderlistBody.swift; sourceTree = "<group>"; };
		BEDD1CD32A0243E0007D4E4B /* DriverRescheduleorderBody.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = DriverRescheduleorderBody.swift; sourceTree = "<group>"; };
		BEDD1CD42A0243E0007D4E4B /* CreateOrderResponseFields.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CreateOrderResponseFields.swift; sourceTree = "<group>"; };
		BEDD1CD52A0243E0007D4E4B /* ContactContacthelpcreateBody.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ContactContacthelpcreateBody.swift; sourceTree = "<group>"; };
		BEDD1CD62A0243E0007D4E4B /* LatestHomeListingResponseFields.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = LatestHomeListingResponseFields.swift; sourceTree = "<group>"; };
		BEDD1CD72A0243E0007D4E4B /* LatestHomeListingResponse.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = LatestHomeListingResponse.swift; sourceTree = "<group>"; };
		BEDD1CD82A0243E0007D4E4B /* UserDetailListingResponseFields.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = UserDetailListingResponseFields.swift; sourceTree = "<group>"; };
		BEDD1CD92A0243E0007D4E4B /* FavouriteMarkfavouriteBody.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = FavouriteMarkfavouriteBody.swift; sourceTree = "<group>"; };
		BEDD1CDA2A0243E0007D4E4B /* UserUpdatedevicetokenBody.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = UserUpdatedevicetokenBody.swift; sourceTree = "<group>"; };
		BEDD1CDB2A0243E0007D4E4B /* GetCartResponseFields.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = GetCartResponseFields.swift; sourceTree = "<group>"; };
		BEDD1CDC2A0243E0007D4E4B /* WalletDetailsResponse.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = WalletDetailsResponse.swift; sourceTree = "<group>"; };
		BEDD1CDD2A0243E0007D4E4B /* NotificationListResponseFields.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = NotificationListResponseFields.swift; sourceTree = "<group>"; };
		BEDD1CDE2A0243E0007D4E4B /* CardResponseFields.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CardResponseFields.swift; sourceTree = "<group>"; };
		BEDD1CDF2A0243E0007D4E4B /* DriverOrderResponseFields.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = DriverOrderResponseFields.swift; sourceTree = "<group>"; };
		BEDD1CE02A0243E0007D4E4B /* OfferListResponseFields.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = OfferListResponseFields.swift; sourceTree = "<group>"; };
		BEDD1CE12A0243E0007D4E4B /* OrderOrdertransactionupdateBody.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = OrderOrdertransactionupdateBody.swift; sourceTree = "<group>"; };
		BEDD1CE22A0243E0007D4E4B /* ListResponse.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ListResponse.swift; sourceTree = "<group>"; };
		BEDD1CE32A0243E0007D4E4B /* GetCartShiftResponseFields.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = GetCartShiftResponseFields.swift; sourceTree = "<group>"; };
		BEDD1CE42A0243E0007D4E4B /* DriverHomeListingResponseFields.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = DriverHomeListingResponseFields.swift; sourceTree = "<group>"; };
		BEDD1CE52A0243E0007D4E4B /* MarketingBannerResponseFields.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MarketingBannerResponseFields.swift; sourceTree = "<group>"; };
		BEDD1CE62A0243E0007D4E4B /* DriverOrderDeatailsResponseFields.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = DriverOrderDeatailsResponseFields.swift; sourceTree = "<group>"; };
		BEDD1CE72A0243E0007D4E4B /* CacheResponse.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CacheResponse.swift; sourceTree = "<group>"; };
		BEDD1CE82A0243E0007D4E4B /* PromocodeCheckResponse.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = PromocodeCheckResponse.swift; sourceTree = "<group>"; };
		BEDD1CE92A0243E0007D4E4B /* ProductProductlistingBody.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ProductProductlistingBody.swift; sourceTree = "<group>"; };
		BEDD1CEA2A0243E0007D4E4B /* OrderResponseFields.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = OrderResponseFields.swift; sourceTree = "<group>"; };
		BEDD1CEB2A0243E0007D4E4B /* DayWiseResponse.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = DayWiseResponse.swift; sourceTree = "<group>"; };
		BEDD1CEC2A0243E0007D4E4B /* ContentPageResponseFields.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ContentPageResponseFields.swift; sourceTree = "<group>"; };
		BEDD1CED2A0243E0007D4E4B /* ListResponseFields.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ListResponseFields.swift; sourceTree = "<group>"; };
		BEDD1CEE2A0243E0007D4E4B /* UpdateLanguageResponseFields.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = UpdateLanguageResponseFields.swift; sourceTree = "<group>"; };
		BEDD1CEF2A0243E0007D4E4B /* CancelOrderReasonResponseFields.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CancelOrderReasonResponseFields.swift; sourceTree = "<group>"; };
		BEDD1CF02A0243E0007D4E4B /* OrderDetailResponseFields.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = OrderDetailResponseFields.swift; sourceTree = "<group>"; };
		BEDD1CF12A0243E0007D4E4B /* OfferListResponse.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = OfferListResponse.swift; sourceTree = "<group>"; };
		BEDD1CF22A0243E0007D4E4B /* ProductProductweblistingBody.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ProductProductweblistingBody.swift; sourceTree = "<group>"; };
		BEDD1CF32A0243E0007D4E4B /* NotificationResponseFields.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = NotificationResponseFields.swift; sourceTree = "<group>"; };
		BEDD1CF42A0243E0007D4E4B /* AddToCartResponse.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AddToCartResponse.swift; sourceTree = "<group>"; };
		BEDD1CF52A0243E0007D4E4B /* AddressResponseFields.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AddressResponseFields.swift; sourceTree = "<group>"; };
		BEDD1CF62A0243E0007D4E4B /* RatingAddratingBody.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = RatingAddratingBody.swift; sourceTree = "<group>"; };
		BEDD1CF72A0243E0007D4E4B /* ProductDetailResponse.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ProductDetailResponse.swift; sourceTree = "<group>"; };
		BEDD1CF82A0243E0007D4E4B /* DriverHomeListingResponse.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = DriverHomeListingResponse.swift; sourceTree = "<group>"; };
		BEDD1CF92A0243E0007D4E4B /* DriverOrderStatusResponse.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = DriverOrderStatusResponse.swift; sourceTree = "<group>"; };
		BEDD1CFA2A0243E0007D4E4B /* OrderListResponseFields.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = OrderListResponseFields.swift; sourceTree = "<group>"; };
		BEDD1CFB2A0243E0007D4E4B /* AddRatingResponse.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AddRatingResponse.swift; sourceTree = "<group>"; };
		BEDD1CFC2A0243E0007D4E4B /* NotificationResponse.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = NotificationResponse.swift; sourceTree = "<group>"; };
		BEDD1CFD2A0243E0007D4E4B /* ShiftResponse.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ShiftResponse.swift; sourceTree = "<group>"; };
		BEDD1CFE2A0243E0007D4E4B /* UserResponse.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = UserResponse.swift; sourceTree = "<group>"; };
		BEDD1CFF2A0243E0007D4E4B /* DriverCurrentOrderListingResponse.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = DriverCurrentOrderListingResponse.swift; sourceTree = "<group>"; };
		BEDD1D002A0243E0007D4E4B /* OrderUpdatecontactnumberBody.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = OrderUpdatecontactnumberBody.swift; sourceTree = "<group>"; };
		BEDD1D012A0243E0007D4E4B /* UserDetailListingResponse.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = UserDetailListingResponse.swift; sourceTree = "<group>"; };
		BEDD1D022A0243E0007D4E4B /* DynamicSectionResponseFields.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = DynamicSectionResponseFields.swift; sourceTree = "<group>"; };
		BEDD1D032A0243E0007D4E4B /* HomeListingResponse.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = HomeListingResponse.swift; sourceTree = "<group>"; };
		BEDD1D042A0243E0007D4E4B /* AddressResponse.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AddressResponse.swift; sourceTree = "<group>"; };
		BEDD1D052A0243E0007D4E4B /* OauthVerifyotpBody.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = OauthVerifyotpBody.swift; sourceTree = "<group>"; };
		BEDD1D062A0243E0007D4E4B /* ProductSuggestionResponse.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ProductSuggestionResponse.swift; sourceTree = "<group>"; };
		BEDD1D072A0243E0007D4E4B /* UserProfileBody.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = UserProfileBody.swift; sourceTree = "<group>"; };
		BEDD1D082A0243E0007D4E4B /* FavouriteResponseFields.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = FavouriteResponseFields.swift; sourceTree = "<group>"; };
		BEDD1D092A0243E0007D4E4B /* CountryListResponse.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CountryListResponse.swift; sourceTree = "<group>"; };
		BEDD1D0A2A0243E0007D4E4B /* GetUserCardDetailResponseFields.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = GetUserCardDetailResponseFields.swift; sourceTree = "<group>"; };
		BEDD1D0B2A0243E0007D4E4B /* CreateOrderResponse.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CreateOrderResponse.swift; sourceTree = "<group>"; };
		BEDD1D0C2A0243E0007D4E4B /* UserDrivertipBody.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = UserDrivertipBody.swift; sourceTree = "<group>"; };
		BEDD1D0D2A0243E0007D4E4B /* CardResponse.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CardResponse.swift; sourceTree = "<group>"; };
		BEDD1D0E2A0243E0007D4E4B /* NotifymeBody.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = NotifymeBody.swift; sourceTree = "<group>"; };
		BEDD1D0F2A0243E0007D4E4B /* UserAddaddressBody.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = UserAddaddressBody.swift; sourceTree = "<group>"; };
		BEDD1D102A0243E0007D4E4B /* ProductProductinfoBody.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ProductProductinfoBody.swift; sourceTree = "<group>"; };
		BEDD1D112A0243E0007D4E4B /* UserUserlanguageBody.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = UserUserlanguageBody.swift; sourceTree = "<group>"; };
		BEDD1D122A0243E0007D4E4B /* DriverCurrentOrderListingResponseFields.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = DriverCurrentOrderListingResponseFields.swift; sourceTree = "<group>"; };
		BEDD1D132A0243E0007D4E4B /* DriverCompletedOrderDeatailsResponseFields.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = DriverCompletedOrderDeatailsResponseFields.swift; sourceTree = "<group>"; };
		BEDD1D142A0243E0007D4E4B /* CountryListResponseFields.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CountryListResponseFields.swift; sourceTree = "<group>"; };
		BEDD1D152A0243E0007D4E4B /* AddToCartResponseFields.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AddToCartResponseFields.swift; sourceTree = "<group>"; };
		BEDD1D162A0243E0007D4E4B /* UserAddmoneytowalletBody.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = UserAddmoneytowalletBody.swift; sourceTree = "<group>"; };
		BEDD1D172A0243E0007D4E4B /* DriverWeeklyScheduleResponse.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = DriverWeeklyScheduleResponse.swift; sourceTree = "<group>"; };
		BEDD1D182A0243E0007D4E4B /* AddRatingResponseFields.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AddRatingResponseFields.swift; sourceTree = "<group>"; };
		BEDD1D192A0243E0007D4E4B /* ProductDetailsResponseFields.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ProductDetailsResponseFields.swift; sourceTree = "<group>"; };
		BEDD1D1A2A0243E0007D4E4B /* HomeListingResponseFields.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = HomeListingResponseFields.swift; sourceTree = "<group>"; };
		BEDD1D1B2A0243E0007D4E4B /* UserResponseFields.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = UserResponseFields.swift; sourceTree = "<group>"; };
		BEDD1D1C2A0243E0007D4E4B /* FavouriteResponse.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = FavouriteResponse.swift; sourceTree = "<group>"; };
		BEDD1D1D2A0243E0007D4E4B /* DriverRescheduleOrderResponse.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = DriverRescheduleOrderResponse.swift; sourceTree = "<group>"; };
		BEDD1D1E2A0243E0007D4E4B /* ProductListResponseFields.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ProductListResponseFields.swift; sourceTree = "<group>"; };
		BEDD1D1F2A0243E0007D4E4B /* FavouriteListResponse.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = FavouriteListResponse.swift; sourceTree = "<group>"; };
		BEDD1D202A0243E0007D4E4B /* OauthRequestforotpBody.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = OauthRequestforotpBody.swift; sourceTree = "<group>"; };
		BEDD1D212A0243E0007D4E4B /* OrderDetailResponse.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = OrderDetailResponse.swift; sourceTree = "<group>"; };
		BEDD1D222A0243E0007D4E4B /* TransactionResponseFields.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = TransactionResponseFields.swift; sourceTree = "<group>"; };
		BEDD1D232A0243E0007D4E4B /* CartAddtocartBody.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CartAddtocartBody.swift; sourceTree = "<group>"; };
		BEDD1D242A0243E0007D4E4B /* DriverCountResponseFields.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = DriverCountResponseFields.swift; sourceTree = "<group>"; };
		BEDD1D252A0243E0007D4E4B /* GetCartListResponseFields.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = GetCartListResponseFields.swift; sourceTree = "<group>"; };
		BEDD1D262A0243E0007D4E4B /* OfferResponseFields.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = OfferResponseFields.swift; sourceTree = "<group>"; };
		BEDD1D272A0243E0007D4E4B /* DriverOrderstatusupdateBody.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = DriverOrderstatusupdateBody.swift; sourceTree = "<group>"; };
		BEDD1D282A0243E0007D4E4B /* CommonFields.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CommonFields.swift; sourceTree = "<group>"; };
		BEDD1D292A0243E0007D4E4B /* CancelOrderReasonResponse.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CancelOrderReasonResponse.swift; sourceTree = "<group>"; };
		BEDD1D2A2A0243E0007D4E4B /* NotificationBadgeResponse.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = NotificationBadgeResponse.swift; sourceTree = "<group>"; };
		BEDD1D2B2A0243E0007D4E4B /* MarketingBannerImageResponseFields.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MarketingBannerImageResponseFields.swift; sourceTree = "<group>"; };
		BEDD1D2C2A0243E0007D4E4B /* DriverRescheduleOrderResponseFields.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = DriverRescheduleOrderResponseFields.swift; sourceTree = "<group>"; };
		BEDD1D2D2A0243E0007D4E4B /* OauthLoginBody.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = OauthLoginBody.swift; sourceTree = "<group>"; };
		BEDD1D2E2A0243E0007D4E4B /* ProductListResponse.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ProductListResponse.swift; sourceTree = "<group>"; };
		BEDD1D2F2A0243E0007D4E4B /* AddressListResponse.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AddressListResponse.swift; sourceTree = "<group>"; };
		BEDD1D302A0243E0007D4E4B /* Extensions.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Extensions.swift; sourceTree = "<group>"; };
		BEDD1D312A0243E0007D4E4B /* JSONEncodingHelper.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = JSONEncodingHelper.swift; sourceTree = "<group>"; };
		BEDD1D322A0243E0007D4E4B /* Models.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Models.swift; sourceTree = "<group>"; };
		BEDD1D332A0243E0007D4E4B /* Configuration.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Configuration.swift; sourceTree = "<group>"; };
		BEE21A63299503890090C9BA /* RatingPresenter.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RatingPresenter.swift; sourceTree = "<group>"; };
		BEE21A64299503890090C9BA /* RatingViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RatingViewController.swift; sourceTree = "<group>"; };
		BEE21A65299503890090C9BA /* RatingInteractor.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RatingInteractor.swift; sourceTree = "<group>"; };
		BEE4FF5F29B0BF3A0010A24E /* FavoritePresenter.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FavoritePresenter.swift; sourceTree = "<group>"; };
		BEE4FF6029B0BF3A0010A24E /* FavoriteViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FavoriteViewController.swift; sourceTree = "<group>"; };
		BEE4FF6129B0BF3A0010A24E /* FavoriteInteractor.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FavoriteInteractor.swift; sourceTree = "<group>"; };
		BEE6424A297581E300211571 /* ImageScrollView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ImageScrollView.swift; sourceTree = "<group>"; };
		BEE6424E2975825000211571 /* FullScreenImagePresenter.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FullScreenImagePresenter.swift; sourceTree = "<group>"; };
		BEE6424F2975825000211571 /* FullScreenImageViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FullScreenImageViewController.swift; sourceTree = "<group>"; };
		BEE642502975825000211571 /* FullScreenImageInteractor.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FullScreenImageInteractor.swift; sourceTree = "<group>"; };
		BEE94348297ACC4700CF3EE6 /* FirebaseDeeplinkingHelper.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FirebaseDeeplinkingHelper.swift; sourceTree = "<group>"; };
		BEEAD04C29CD84E9008455E5 /* AnimationTest.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AnimationTest.swift; sourceTree = "<group>"; };
		BEF4D1D82942109D00EA53FF /* CancelReasonPresenter.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CancelReasonPresenter.swift; sourceTree = "<group>"; };
		BEF4D1D92942109D00EA53FF /* CancelReasonViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CancelReasonViewController.swift; sourceTree = "<group>"; };
		BEF4D1DA2942109D00EA53FF /* CancelReasonInteractor.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CancelReasonInteractor.swift; sourceTree = "<group>"; };
		BEF4D1DE2942249F00EA53FF /* CancelReasonTableViewCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CancelReasonTableViewCell.swift; sourceTree = "<group>"; };
		E20B056F2E32622300E635FB /* NewMainHomeView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NewMainHomeView.swift; sourceTree = "<group>"; };
		E20B05722E32635B00E635FB /* NewMainHomeModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NewMainHomeModel.swift; sourceTree = "<group>"; };
		E20B05742E32643700E635FB /* NewMainHomeViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NewMainHomeViewModel.swift; sourceTree = "<group>"; };
		E20B05792E32921800E635FB /* NewProductsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NewProductsView.swift; sourceTree = "<group>"; };
		E20B057B2E32925200E635FB /* NewProductsViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NewProductsViewModel.swift; sourceTree = "<group>"; };
		E21F53ED2E09CF260096BC2A /* WalletView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WalletView.swift; sourceTree = "<group>"; };
		E21F53F02E0B71EA0096BC2A /* WalletViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WalletViewModel.swift; sourceTree = "<group>"; };
		E21F53F22E0B72A90096BC2A /* TransactionsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TransactionsView.swift; sourceTree = "<group>"; };
		E21F53F72E0B7D110096BC2A /* MyFavouritesView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MyFavouritesView.swift; sourceTree = "<group>"; };
		E21F53F92E0B7E170096BC2A /* MyFavViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MyFavViewModel.swift; sourceTree = "<group>"; };
		E21F53FD2E0B8F2E0096BC2A /* MyAddressesView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MyAddressesView.swift; sourceTree = "<group>"; };
		E21F53FF2E0B8F630096BC2A /* MyAddressesViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MyAddressesViewModel.swift; sourceTree = "<group>"; };
		E21F54012E0B97510096BC2A /* AddressItem.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AddressItem.swift; sourceTree = "<group>"; };
		E21F54052E0BB64C0096BC2A /* ReferView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ReferView.swift; sourceTree = "<group>"; };
		E23FC0A02E23D28C006AB6B4 /* OrderDeliveredView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OrderDeliveredView.swift; sourceTree = "<group>"; };
		E25C5B4B2E3B6E2300DA87DF /* NewSubCategoriesView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NewSubCategoriesView.swift; sourceTree = "<group>"; };
		E25C5B4D2E3B6E4C00DA87DF /* NewSubCategoriesTapSelectionView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NewSubCategoriesTapSelectionView.swift; sourceTree = "<group>"; };
		E25C5B4F2E3B6E8D00DA87DF /* NewSubCategoriesViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NewSubCategoriesViewModel.swift; sourceTree = "<group>"; };
		E25C5B512E3B6FDA00DA87DF /* NewSubCategoryModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NewSubCategoryModel.swift; sourceTree = "<group>"; };
		E2914FCE2E1BC9C50084CEC1 /* TabRouter.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TabRouter.swift; sourceTree = "<group>"; };
		E29542422E48BFA90002622C /* LoadingSquareImageWithUrlView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LoadingSquareImageWithUrlView.swift; sourceTree = "<group>"; };
		E29542442E48BFEA0002622C /* ProductSquareItemView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ProductSquareItemView.swift; sourceTree = "<group>"; };
		E2C33C922E00DF8500EF66A2 /* CustomSwiftUIAlertView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CustomSwiftUIAlertView.swift; sourceTree = "<group>"; };
		E2E5B0742E252CCF00CA6144 /* OrderDetailsPopupView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OrderDetailsPopupView.swift; sourceTree = "<group>"; };
		E2E5B0792E27EDE600CA6144 /* SubCategoriesViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SubCategoriesViewModel.swift; sourceTree = "<group>"; };
		E2E5B07B2E27EE2900CA6144 /* SubCategoriesView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SubCategoriesView.swift; sourceTree = "<group>"; };
		E2E5B0812E28E71000CA6144 /* SubCategoriesTapSelectionView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SubCategoriesTapSelectionView.swift; sourceTree = "<group>"; };
		F90B4C9520329C45006ADAF6 /* TopCustomer.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = TopCustomer.app; sourceTree = BUILT_PRODUCTS_DIR; };
		F90B4C9D20329C45006ADAF6 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		F90B4C9F20329C45006ADAF6 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		F90B4CA220329C45006ADAF6 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		F90B4CA420329C45006ADAF6 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		F90B4CCC2032A7EA006ADAF6 /* Basecode-Bridging-Header.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "Basecode-Bridging-Header.h"; sourceTree = "<group>"; };
		F90B4CD82032A87B006ADAF6 /* Structure_Guide.rtf */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.rtf; path = Structure_Guide.rtf; sourceTree = "<group>"; };
		F90B4CDC2032AAB8006ADAF6 /* AppDelegate.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		F90B4CE02032F4A6006ADAF6 /* SOAPI.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SOAPI.swift; sourceTree = "<group>"; };
		F90B4CE12032F4A6006ADAF6 /* SOService.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SOService.swift; sourceTree = "<group>"; };
		F948AEC9203E865500F5A852 /* AppdelegateNotification.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppdelegateNotification.swift; sourceTree = "<group>"; };
		F952382A2047BA3400BC0D49 /* RequestParameter.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = RequestParameter.swift; sourceTree = "<group>"; };
		F96C2D2C20380B1A00F8CBC7 /* Validator.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Validator.swift; sourceTree = "<group>"; };
		F97A99C62110226F007C45F5 /* UserDefaultsExtension.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = UserDefaultsExtension.swift; sourceTree = "<group>"; };
		F97A9A0D21102600007C45F5 /* CGRectExtensions.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CGRectExtensions.swift; sourceTree = "<group>"; };
		F97A9A112110274A007C45F5 /* EZSwiftFunctions.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = EZSwiftFunctions.swift; sourceTree = "<group>"; };
		F97A9A1321102CEF007C45F5 /* StringExtensions.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = StringExtensions.swift; sourceTree = "<group>"; };
		F97A9A1521102E18007C45F5 /* UIApplicationExtensions.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = UIApplicationExtensions.swift; sourceTree = "<group>"; };
		F97A9A1B211031A6007C45F5 /* UIViewControllerExtensions.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = UIViewControllerExtensions.swift; sourceTree = "<group>"; };
		F97A9A1C211031A6007C45F5 /* UIViewExtensions.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = UIViewExtensions.swift; sourceTree = "<group>"; };
		F97A9A1F211031C0007C45F5 /* CGFloatExtensions.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CGFloatExtensions.swift; sourceTree = "<group>"; };
		F995BDC0204D1277005E5279 /* KeyMessages.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = KeyMessages.swift; sourceTree = "<group>"; };
		F9A4539E203AA7200065C9FD /* Constant.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Constant.swift; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		7352DBA72CFFC23200320EDD /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F90B4C9220329C45006ADAF6 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				73676AC22CE13FDE00BFF236 /* FacebookLogin in Frameworks */,
				7304D5032A4371F0008FE96E /* PaymentSDK.xcframework in Frameworks */,
				73676AC42CE13FDE00BFF236 /* FacebookShare in Frameworks */,
				73676ABC2CE13FDE00BFF236 /* FacebookAEM in Frameworks */,
				73676AC02CE13FDE00BFF236 /* FacebookCore in Frameworks */,
				BE0D069D283E40E100F934F9 /* WebKit.framework in Frameworks */,
				73676ABE2CE13FDE00BFF236 /* FacebookBasics in Frameworks */,
				739771102C7C78BF00FA8476 /* Tabby in Frameworks */,
				3B467919C5B739C7362C95C1 /* Pods_TopCustomer.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		03850A438F42A45BE95361E0 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				731A7BBA2D02263000B5DC75 /* GoogleMaps.xcframework */,
				73A798872C6BBCCF0077ECE0 /* libsqlite3.tbd */,
				7304D5022A4371F0008FE96E /* PaymentSDK.xcframework */,
				BE647A8528E836870051F5FE /* Branch.framework */,
				BE0D069C283E40E100F934F9 /* WebKit.framework */,
				7352DB912CFFB77100320EDD /* UserNotifications.framework */,
				7352DB932CFFB77100320EDD /* UserNotificationsUI.framework */,
				2D14DDD621EC1361F80D6245 /* Pods_TopCustomer.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		730930462B5806F700264FA3 /* LottieAnimationFiles */ = {
			isa = PBXGroup;
			children = (
				73C765DC2C510ABE0080282D /* invite_friends_gift_en.json */,
				73C765DE2C510AD30080282D /* invite_friends_gift.json */,
				73566AD02C4404A300D0AA8E /* add_to_cart_animation.json */,
				736CE1AB2C519111006ECEBC /* add_to_cart_animation_ar.json */,
				73566ACE2C43FBFF00D0AA8E /* loading_shape.json */,
				730930472B58072700264FA3 /* best_offers.json */,
			);
			path = LottieAnimationFiles;
			sourceTree = "<group>";
		};
		7322155F2C47E7DA00A6C763 /* InviteFriendsGiftVC */ = {
			isa = PBXGroup;
			children = (
				732215602C47E7F200A6C763 /* InviteFriendsGiftVC.swift */,
			);
			path = InviteFriendsGiftVC;
			sourceTree = "<group>";
		};
		732320142C3B2A1900DE37B6 /* InviteFriends */ = {
			isa = PBXGroup;
			children = (
				7322155F2C47E7DA00A6C763 /* InviteFriendsGiftVC */,
				732320152C3B2A2D00DE37B6 /* InviteFriendsVC */,
			);
			path = InviteFriends;
			sourceTree = "<group>";
		};
		732320152C3B2A2D00DE37B6 /* InviteFriendsVC */ = {
			isa = PBXGroup;
			children = (
				732320162C3B2F6500DE37B6 /* InviteFriendsVC.swift */,
				732561A02C3D774C004AB08D /* InviteFriendsPresenter.swift */,
				732561A22C3D78AE004AB08D /* InviteFriendsInteractor.swift */,
			);
			path = InviteFriendsVC;
			sourceTree = "<group>";
		};
		732E03332C2C4A07008B6088 /* ChooseDeliveryDate */ = {
			isa = PBXGroup;
			children = (
				732E03362C2C4E46008B6088 /* CollectionViewCell */,
				732E03342C2C4A6E008B6088 /* ChooseDeliveryDateVC.swift */,
			);
			path = ChooseDeliveryDate;
			sourceTree = "<group>";
		};
		732E03362C2C4E46008B6088 /* CollectionViewCell */ = {
			isa = PBXGroup;
			children = (
				732E03372C2C4E86008B6088 /* ChooseDeliveryDateCVC.swift */,
				732E03382C2C4E86008B6088 /* ChooseDeliveryDateCVC.xib */,
			);
			path = CollectionViewCell;
			sourceTree = "<group>";
		};
		73381AE12D7D00120059A27C /* Search */ = {
			isa = PBXGroup;
			children = (
				73D172312D8252F600E7EF01 /* Model */,
				73381AE32D7D00200059A27C /* ViewModel */,
				73381AE22D7D00190059A27C /* Views */,
			);
			path = Search;
			sourceTree = "<group>";
		};
		73381AE22D7D00190059A27C /* Views */ = {
			isa = PBXGroup;
			children = (
				73520B262D75D41200BD7086 /* SearchView.swift */,
				73D172772D8256AF00E7EF01 /* TabCategoriesSelectionView.swift */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		73381AE32D7D00200059A27C /* ViewModel */ = {
			isa = PBXGroup;
			children = (
				73381AE42D7D00410059A27C /* SearchViewModel.swift */,
			);
			path = ViewModel;
			sourceTree = "<group>";
		};
		734031272C7DC054009F6BF5 /* TabbyPayment */ = {
			isa = PBXGroup;
			children = (
				73DC7FE12C7F171F00403CDE /* TabyPaymentManager.swift */,
			);
			path = TabbyPayment;
			sourceTree = "<group>";
		};
		734560522D7E51DF000A4119 /* Orders */ = {
			isa = PBXGroup;
			children = (
				734560542D7E51EF000A4119 /* ViewModel */,
				734560532D7E51E8000A4119 /* Views */,
			);
			path = Orders;
			sourceTree = "<group>";
		};
		734560532D7E51E8000A4119 /* Views */ = {
			isa = PBXGroup;
			children = (
				737E27F12D7EEAFA00723266 /* EmptyOrdersView.swift */,
				737E27EF2D7EEAEA00723266 /* OrderDetailView.swift */,
				737E27EB2D7EE26D00723266 /* OrdersListView.swift */,
				73520B232D75D41200BD7086 /* OrdersView.swift */,
				E23FC0A02E23D28C006AB6B4 /* OrderDeliveredView.swift */,
				E2E5B0742E252CCF00CA6144 /* OrderDetailsPopupView.swift */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		734560542D7E51EF000A4119 /* ViewModel */ = {
			isa = PBXGroup;
			children = (
				737E27ED2D7EE68D00723266 /* OrdersListViewModel.swift */,
				73A565C82D89731F00BDB509 /* OrderDetailViewModel.swift */,
			);
			path = ViewModel;
			sourceTree = "<group>";
		};
		73520B182D75D25C00BD7086 /* HomeScreen */ = {
			isa = PBXGroup;
			children = (
			);
			path = HomeScreen;
			sourceTree = "<group>";
		};
		73520B192D75D40B00BD7086 /* MainTabBar */ = {
			isa = PBXGroup;
			children = (
				73520B1A2D75D41200BD7086 /* AccountView.swift */,
				73520B1E2D75D41200BD7086 /* CheckoutView.swift */,
				73520B1F2D75D41200BD7086 /* FoodHomeView.swift */,
				73520B202D75D41200BD7086 /* MainCategoriesView.swift */,
				73520B222D75D41200BD7086 /* MainTabBarView.swift */,
				73520B242D75D41200BD7086 /* ProductDetailView.swift */,
				73520B252D75D41200BD7086 /* ProfileView.swift */,
			);
			path = MainTabBar;
			sourceTree = "<group>";
		};
		7354991C2ADAB3730007E26E /* UITableViewCell */ = {
			isa = PBXGroup;
			children = (
				7354991D2ADAB3A80007E26E /* PointsHostoryTVCell.swift */,
				7354991E2ADAB3A80007E26E /* PointsHostoryTVCell.xib */,
				7335B9042AFB0E4B00C877FF /* RewardDetailsCell.swift */,
				7335B9052AFB0E4B00C877FF /* RewardDetailsCell.xib */,
			);
			path = UITableViewCell;
			sourceTree = "<group>";
		};
		735499232ADACD050007E26E /* RewardsVC */ = {
			isa = PBXGroup;
			children = (
				735499182ADAB2180007E26E /* RewardVC.swift */,
				735499242ADACD440007E26E /* RewardsVCPresenter.swift */,
				735499262ADACD9D0007E26E /* RewardsVCInteractor.swift */,
			);
			path = RewardsVC;
			sourceTree = "<group>";
		};
		7367EE1A2AD74B700042BACF /* Shapes */ = {
			isa = PBXGroup;
			children = (
				7372D9682B0E064600A9506D /* LottieFiles */,
				739EBB0B2B065D4000E89E8B /* data1.json */,
				739EBB0A2B065D4000E89E8B /* data2.json */,
				739EBB0D2B065D4000E89E8B /* data3.json */,
				739EBB0C2B065D4000E89E8B /* data4.json */,
				739EBB092B065D4000E89E8B /* data5.json */,
				7325FBB22AFE620E00690253 /* coins_shape.json */,
				7358CEE02B0F9F05001F3611 /* CoinsLevelsShape.swift */,
			);
			path = Shapes;
			sourceTree = "<group>";
		};
		7367EE1F2AD7649E0042BACF /* Rewards */ = {
			isa = PBXGroup;
			children = (
				7354991C2ADAB3730007E26E /* UITableViewCell */,
				7367EE202AD764B30042BACF /* YourRewardsVC.swift */,
				73465F4A2AD7682300321243 /* CongratsRewardsVC.swift */,
				735499232ADACD050007E26E /* RewardsVC */,
			);
			path = Rewards;
			sourceTree = "<group>";
		};
		7372D9682B0E064600A9506D /* LottieFiles */ = {
			isa = PBXGroup;
			children = (
				7372D9692B0E064600A9506D /* main3.json */,
				7372D96A2B0E064600A9506D /* middle1.json */,
				7372D96B2B0E064600A9506D /* main2.json */,
				7372D96C2B0E064600A9506D /* main5.json */,
				7372D96D2B0E064600A9506D /* main4.json */,
				7372D96E2B0E064600A9506D /* middle4.json */,
				7372D96F2B0E064600A9506D /* main6.json */,
				7372D9702B0E064600A9506D /* middle5.json */,
				7372D9712B0E064600A9506D /* middle2.json */,
				7372D9722B0E064600A9506D /* main1.json */,
				7372D9732B0E064600A9506D /* middle3.json */,
			);
			path = LottieFiles;
			sourceTree = "<group>";
		};
		7372ECC12D82D37300818CD3 /* Alert */ = {
			isa = PBXGroup;
			children = (
				7372ECBF2D82D37300818CD3 /* AlertType.swift */,
				7372ECC02D82D37300818CD3 /* ALertView.swift */,
			);
			path = Alert;
			sourceTree = "<group>";
		};
		73757E5F2A8AFB6E0052D068 /* RecommendationProducts */ = {
			isa = PBXGroup;
			children = (
				73757E602A8AFB860052D068 /* UICollectionViewCell */,
			);
			path = RecommendationProducts;
			sourceTree = "<group>";
		};
		73757E602A8AFB860052D068 /* UICollectionViewCell */ = {
			isa = PBXGroup;
			children = (
				73757E612A8AFBB20052D068 /* RecommendationProductCVC.swift */,
				73757E622A8AFBB20052D068 /* RecommendationProductCVC.xib */,
			);
			path = UICollectionViewCell;
			sourceTree = "<group>";
		};
		7376F23D2AFB9D9E00FAB85A /* LastOffer */ = {
			isa = PBXGroup;
			children = (
				7376F2392AFB9D1700FAB85A /* LastOfferTableViewCell.swift */,
				7325FBB62B00444000690253 /* LastOfferTableViewCell.xib */,
				7376F23E2AFBAF3F00FAB85A /* LastOfferCollectionViewCell.swift */,
				73DCE10B2B004A0400AA4AAC /* LastOfferCollectionViewCell.xib */,
			);
			path = LastOffer;
			sourceTree = "<group>";
		};
		73778ABE2CF8854400D99032 /* PaymentsWithURL */ = {
			isa = PBXGroup;
			children = (
				73778ABF2CF8859D00D99032 /* PaymentsByUrlVC.swift */,
				73778AC02CF8859D00D99032 /* PaymentsByUrlVC.xib */,
			);
			path = PaymentsWithURL;
			sourceTree = "<group>";
		};
		7382DE582CB7F0240060C4B5 /* MainSliderCells */ = {
			isa = PBXGroup;
			children = (
				7382DE592CB7F04C0060C4B5 /* MainSliderCVCell.swift */,
				7382DE5A2CB7F04C0060C4B5 /* MainSliderCVCell.xib */,
			);
			path = MainSliderCells;
			sourceTree = "<group>";
		};
		738AD26B2D6E50CC00419CCA /* ViewInvoice */ = {
			isa = PBXGroup;
			children = (
				738AD26C2D6E50F700419CCA /* ViewInvoiceVC.swift */,
			);
			path = ViewInvoice;
			sourceTree = "<group>";
		};
		738B81AF2C1195EC0069DC6E /* UIView */ = {
			isa = PBXGroup;
			children = (
				738B81B22C1196850069DC6E /* BundleProductsView.xib */,
				738B81B42C1196CE0069DC6E /* BundleProductsView.swift */,
			);
			path = UIView;
			sourceTree = "<group>";
		};
		738B81B62C11A4750069DC6E /* CartProductsOfBundleTVCell */ = {
			isa = PBXGroup;
			children = (
				738B81B72C11A4A90069DC6E /* CartProductsOfBundleTVCell.swift */,
				738B81B82C11A4A90069DC6E /* CartProductsOfBundleTVCell.xib */,
			);
			path = CartProductsOfBundleTVCell;
			sourceTree = "<group>";
		};
		7390FF7D2D75045800C813EC /* BeqalaApp */ = {
			isa = PBXGroup;
			children = (
				E25C5B482E3B6DF700DA87DF /* NewSubCategories */,
				E20B05762E3291F200E635FB /* NewProducts */,
				E2E5B0762E27EDB700CA6144 /* SubCategories */,
				E21F54032E0BB6360096BC2A /* Refer */,
				E21F53FB2E0B8F0D0096BC2A /* MyAddresses */,
				E21F53F42E0B7CE00096BC2A /* MyFavourite */,
				E21F53EB2E09CDF40096BC2A /* Wallet */,
				7372ECC12D82D37300818CD3 /* Alert */,
				734560522D7E51DF000A4119 /* Orders */,
				73381AE12D7D00120059A27C /* Search */,
				73F7F03B2D7C91B500E35734 /* Categories */,
				73FED52D2D7BAC4000C9F1F0 /* Checkout */,
				73F5A8D82D7B242200C260DA /* ProductItemView.swift */,
				73F5A8D72D7B240500C260DA /* YouMayAlsoLike */,
				73F5A8D52D7B23F900C260DA /* YouMayAlsoLikeView.swift */,
				739A394F2D79085700C4847A /* Cart */,
				73B47ACD2D788027007B8FB9 /* ProductDetails */,
				73B47A8C2D78751E007B8FB9 /* Offers */,
				E20B056C2E3261E400E635FB /* NewMainHome */,
				73FF5AE62D7658E30067415B /* MainHome */,
				73520B192D75D40B00BD7086 /* MainTabBar */,
				73B624422D75CF740064D21A /* Utils */,
				73520B182D75D25C00BD7086 /* HomeScreen */,
				7390FF7E2D75046400C813EC /* SelectDeliveryType */,
			);
			path = BeqalaApp;
			sourceTree = "<group>";
		};
		7390FF7E2D75046400C813EC /* SelectDeliveryType */ = {
			isa = PBXGroup;
			children = (
				73B6240D2D75B6390064D21A /* Model */,
				7390FF822D75054A00C813EC /* ViewModel */,
				7390FF7F2D75047100C813EC /* Views */,
			);
			path = SelectDeliveryType;
			sourceTree = "<group>";
		};
		7390FF7F2D75047100C813EC /* Views */ = {
			isa = PBXGroup;
			children = (
				7390FF802D75048600C813EC /* SelectDeliveryTypeView.swift */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		7390FF822D75054A00C813EC /* ViewModel */ = {
			isa = PBXGroup;
			children = (
				73B6240B2D75B4BB0064D21A /* SelectDeliveryTypeViewModel.swift */,
			);
			path = ViewModel;
			sourceTree = "<group>";
		};
		739A394F2D79085700C4847A /* Cart */ = {
			isa = PBXGroup;
			children = (
				739A39892D790F8900C4847A /* ViewModel */,
				739A39882D790F8200C4847A /* Views */,
				739A39502D79086D00C4847A /* CartManager.swift */,
			);
			path = Cart;
			sourceTree = "<group>";
		};
		739A39882D790F8200C4847A /* Views */ = {
			isa = PBXGroup;
			children = (
				73520B1B2D75D41200BD7086 /* CartView.swift */,
				73A565C62D8970B800BDB509 /* SaudiRiyalAmount.swift */,
				73A565C42D89709F00BDB509 /* StepperCartView.swift */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		739A39892D790F8900C4847A /* ViewModel */ = {
			isa = PBXGroup;
			children = (
				739A398A2D79123200C4847A /* CartViewModel.swift */,
			);
			path = ViewModel;
			sourceTree = "<group>";
		};
		73A6F6D42B860B2D001DC9EA /* BunddelsList */ = {
			isa = PBXGroup;
			children = (
				73A6F6D52B861D82001DC9EA /* BundelsListTableViewCell.swift */,
				73A6F6D62B861D82001DC9EA /* BundelsListTableViewCell.xib */,
				73A6F6D92B861DAF001DC9EA /* BundelsListCollectionViewCell.swift */,
				73A6F6DA2B861DAF001DC9EA /* BundelsListCollectionViewCell.xib */,
				738716D62BFFC9A200B1B8F6 /* ProductsOfBundleTVCell.swift */,
				738716D72BFFC9A200B1B8F6 /* ProductsOfBundleTVCell.xib */,
			);
			path = BunddelsList;
			sourceTree = "<group>";
		};
		73B47A8C2D78751E007B8FB9 /* Offers */ = {
			isa = PBXGroup;
			children = (
				73B47A8E2D787533007B8FB9 /* View */,
				73B47A8D2D787526007B8FB9 /* ViewModel */,
			);
			path = Offers;
			sourceTree = "<group>";
		};
		73B47A8D2D787526007B8FB9 /* ViewModel */ = {
			isa = PBXGroup;
			children = (
				73B47A912D787564007B8FB9 /* OffersViewModel.swift */,
			);
			path = ViewModel;
			sourceTree = "<group>";
		};
		73B47A8E2D787533007B8FB9 /* View */ = {
			isa = PBXGroup;
			children = (
				73B47AD22D788298007B8FB9 /* OfferCardDetailsView.swift */,
				73B47A8F2D787551007B8FB9 /* OffersView.swift */,
				73B47A952D787C4D007B8FB9 /* OfferCardView.swift */,
			);
			path = View;
			sourceTree = "<group>";
		};
		73B47ACD2D788027007B8FB9 /* ProductDetails */ = {
			isa = PBXGroup;
			children = (
				73B47ACF2D78803B007B8FB9 /* ViewModel */,
				73B47ACE2D788033007B8FB9 /* Views */,
			);
			path = ProductDetails;
			sourceTree = "<group>";
		};
		73B47ACE2D788033007B8FB9 /* Views */ = {
			isa = PBXGroup;
			children = (
				73381ADD2D7CEC340059A27C /* ImageSliderView.swift */,
				73B47AD02D788058007B8FB9 /* ProductDetailsView.swift */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		73B47ACF2D78803B007B8FB9 /* ViewModel */ = {
			isa = PBXGroup;
			children = (
				73381ADF2D7CEFF90059A27C /* ProductDetailsViewModel.swift */,
			);
			path = ViewModel;
			sourceTree = "<group>";
		};
		73B6240D2D75B6390064D21A /* Model */ = {
			isa = PBXGroup;
			children = (
				73B6240E2D75B6540064D21A /* WarehouseModel.swift */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		73B624422D75CF740064D21A /* Utils */ = {
			isa = PBXGroup;
			children = (
				73B624452D75CFA20064D21A /* LoadingIndicator.swift */,
				739A394D2D7900CC00C4847A /* LoadingImageWithUrlView.swift */,
				73F7EFFB2D7C66DE00E35734 /* Date+.swift */,
				E2914FCE2E1BC9C50084CEC1 /* TabRouter.swift */,
				E29542422E48BFA90002622C /* LoadingSquareImageWithUrlView.swift */,
			);
			path = Utils;
			sourceTree = "<group>";
		};
		73D172312D8252F600E7EF01 /* Model */ = {
			isa = PBXGroup;
			children = (
				73D172322D82533400E7EF01 /* CategoriesModel.swift */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		73E190022A9D32AE0010AE40 /* MayAlsoLikeTVCell */ = {
			isa = PBXGroup;
			children = (
				73E190032A9D32D80010AE40 /* MayAlsoLikeTVCell.swift */,
				73E190042A9D32D80010AE40 /* MayAlsoLikeTVCell.xib */,
			);
			path = MayAlsoLikeTVCell;
			sourceTree = "<group>";
		};
		73E595AB2D747C4C0043EE66 /* FloatingButton */ = {
			isa = PBXGroup;
			children = (
				73E595AC2D747C520043EE66 /* FloatingButtonView.swift */,
				73E595AD2D747C520043EE66 /* FloatingButtonView.xib */,
			);
			path = FloatingButton;
			sourceTree = "<group>";
		};
		73EBCEFD2CD010AD00EB14C4 /* Cells */ = {
			isa = PBXGroup;
			children = (
				73EBCEFE2CD010FD00EB14C4 /* AdvertisementCVCell.swift */,
				73EBCEFF2CD010FD00EB14C4 /* AdvertisementCVCell.xib */,
			);
			path = Cells;
			sourceTree = "<group>";
		};
		73ED99D12BCD0EBD007FBD73 /* Designable */ = {
			isa = PBXGroup;
			children = (
				73ED99D22BCD0EC9007FBD73 /* UIButtons */,
			);
			path = Designable;
			sourceTree = "<group>";
		};
		73ED99D22BCD0EC9007FBD73 /* UIButtons */ = {
			isa = PBXGroup;
			children = (
				73ED99D32BCD0EDD007FBD73 /* MainButtonDesignable.swift */,
			);
			path = UIButtons;
			sourceTree = "<group>";
		};
		73F2D1DB2C8ECD51004A9738 /* Cells */ = {
			isa = PBXGroup;
			children = (
				73F2D1DC2C8ECD8D004A9738 /* UICollectionViewCell */,
			);
			path = Cells;
			sourceTree = "<group>";
		};
		73F2D1DC2C8ECD8D004A9738 /* UICollectionViewCell */ = {
			isa = PBXGroup;
			children = (
				73F2D1DD2C8ECDE9004A9738 /* QuantitiesDiscountCVCell.swift */,
				73F2D1DE2C8ECDE9004A9738 /* QuantitiesDiscountCVCell.xib */,
			);
			path = UICollectionViewCell;
			sourceTree = "<group>";
		};
		73F5A8D72D7B240500C260DA /* YouMayAlsoLike */ = {
			isa = PBXGroup;
			children = (
			);
			path = YouMayAlsoLike;
			sourceTree = "<group>";
		};
		73F7F03B2D7C91B500E35734 /* Categories */ = {
			isa = PBXGroup;
			children = (
				73F7F03D2D7C91D300E35734 /* ViewModel */,
				73F7F03C2D7C91CB00E35734 /* Views */,
			);
			path = Categories;
			sourceTree = "<group>";
		};
		73F7F03C2D7C91CB00E35734 /* Views */ = {
			isa = PBXGroup;
			children = (
				73520B1C2D75D41200BD7086 /* CategoriesView.swift */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		73F7F03D2D7C91D300E35734 /* ViewModel */ = {
			isa = PBXGroup;
			children = (
				73F7F03E2D7C91E800E35734 /* CategoriesViewModel.swift */,
			);
			path = ViewModel;
			sourceTree = "<group>";
		};
		73FCE8842ACD6A6B0014DD95 /* Extension */ = {
			isa = PBXGroup;
			children = (
				73FCE8852ACD6A8A0014DD95 /* UILabel+.swift */,
				73DEBD1F2B1626130026EB10 /* Font+.swift */,
				7305CA732BF5066B00F5C4FE /* Int+.swift */,
				734B9FF12C4015EA00C992C2 /* UIImageView+.swift */,
			);
			path = Extension;
			sourceTree = "<group>";
		};
		73FED52D2D7BAC4000C9F1F0 /* Checkout */ = {
			isa = PBXGroup;
			children = (
				73FED52F2D7BAC5200C9F1F0 /* ViewModel */,
				73FED52E2D7BAC4C00C9F1F0 /* Views */,
			);
			path = Checkout;
			sourceTree = "<group>";
		};
		73FED52E2D7BAC4C00C9F1F0 /* Views */ = {
			isa = PBXGroup;
			children = (
				73520B1D2D75D41200BD7086 /* CheckoutNewView.swift */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		73FED52F2D7BAC5200C9F1F0 /* ViewModel */ = {
			isa = PBXGroup;
			children = (
				73FED5302D7BAC9300C9F1F0 /* CheckoutViewModel.swift */,
			);
			path = ViewModel;
			sourceTree = "<group>";
		};
		73FF5AE62D7658E30067415B /* MainHome */ = {
			isa = PBXGroup;
			children = (
				73FF5AE82D7658F60067415B /* ViewModel */,
				73FF5AE72D7658F00067415B /* Views */,
			);
			path = MainHome;
			sourceTree = "<group>";
		};
		73FF5AE72D7658F00067415B /* Views */ = {
			isa = PBXGroup;
			children = (
				73520B212D75D41200BD7086 /* MainHomeView.swift */,
				73FEA8312D771039001C4360 /* MainSectionView.swift */,
				73FEA82F2D7700F4001C4360 /* MarketingBannersView.swift */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		73FF5AE82D7658F60067415B /* ViewModel */ = {
			isa = PBXGroup;
			children = (
				73FF5AE92D7659270067415B /* MainHomeViewModel.swift */,
			);
			path = ViewModel;
			sourceTree = "<group>";
		};
		8117DF6827E9989700004F69 /* History Orders */ = {
			isa = PBXGroup;
			children = (
				8117DF6F27E9990B00004F69 /* HistoryOrdersTableViewCell.swift */,
				8117DF7027E9990B00004F69 /* HistoryOrdersTableViewCell.xib */,
				8117DF6B27E998A700004F69 /* HistoryOrdersInteractor.swift */,
				8117DF6927E998A700004F69 /* HistoryOrdersPresenter.swift */,
				8117DF6A27E998A700004F69 /* HistoryOrdersViewController.swift */,
			);
			path = "History Orders";
			sourceTree = "<group>";
		};
		8117DF7327E9AA4600004F69 /* History Order Details */ = {
			isa = PBXGroup;
			children = (
				8117DF7A27E9B07100004F69 /* HistoryOrderDetailsTableViewCell.swift */,
				8117DF7B27E9B07100004F69 /* HistoryOrderDetailsTableViewCell.xib */,
				8117DF7627E9AA5C00004F69 /* HistoryOrderDetailsInteractor.swift */,
				8117DF7427E9AA5C00004F69 /* HistoryOrderDetailsPresenter.swift */,
				8117DF7527E9AA5C00004F69 /* HistoryOrderDetailsViewController.swift */,
			);
			path = "History Order Details";
			sourceTree = "<group>";
		};
		81A484A127DF678F00528766 /* FSPagerView */ = {
			isa = PBXGroup;
			children = (
				81A484A227DF678F00528766 /* FSPagerView.swift */,
				81A484A327DF678F00528766 /* FSPageViewTransformer.swift */,
				81A484A427DF678F00528766 /* FSPagerViewCell.xib */,
				81A484A527DF678F00528766 /* FSPagerViewObjcCompat.h */,
				81A484A627DF678F00528766 /* FSPagerViewCell.swift */,
				81A484A727DF678F00528766 /* FSPagerViewLayoutAttributes.swift */,
				81A484A827DF678F00528766 /* FSPageViewLayout.swift */,
				81A484A927DF678F00528766 /* FSPageControl.swift */,
				81A484AA27DF678F00528766 /* FSPagerViewObjcCompat.m */,
				81A484AB27DF678F00528766 /* FSPagerCollectionView.swift */,
			);
			path = FSPagerView;
			sourceTree = "<group>";
		};
		8BD0310E2D3F91100040A590 /* ContentImageExtentionPush */ = {
			isa = PBXGroup;
			children = (
				8BD0310C2D3F91100040A590 /* Info.plist */,
				8BD0310D2D3F91100040A590 /* NotificationService.swift */,
			);
			path = ContentImageExtentionPush;
			sourceTree = "<group>";
		};
		************************ /* Help */ = {
			isa = PBXGroup;
			children = (
				BEADE08C28C9D2870095913E /* HelpViewController.swift */,
				BE04A56D27F5FF8700EE26EA /* HelpInteractor.swift */,
				BE04A56B27F5FF8700EE26EA /* HelpPresenter.swift */,
			);
			path = Help;
			sourceTree = "<group>";
		};
		BE0EED3929DB01F700CAE929 /* CustomPopUp */ = {
			isa = PBXGroup;
			children = (
				BE0EED3A29DB020B00CAE929 /* CustomPopupAlertViewController.swift */,
			);
			path = CustomPopUp;
			sourceTree = "<group>";
		};
		BE10DF1C28BBFBFE000983A2 /* Localization */ = {
			isa = PBXGroup;
			children = (
				BEAB6E7E28BCD3D000D47F96 /* MaterialLocalizeTextfield.swift */,
				BE10DF1D28BBFBFE000983A2 /* MaterialLocalizeButton.swift */,
				BE10DF1E28BBFBFE000983A2 /* MaterialLocalizeLable.swift */,
				BE10DF1F28BBFBFE000983A2 /* MaterialLocalizeImageView.swift */,
			);
			path = Localization;
			sourceTree = "<group>";
		};
		BE1B8B4A27D9D14C0045FA0E /* Home */ = {
			isa = PBXGroup;
			children = (
				BEA85B282876E26700F4754E /* CategoriesCollectionViewCellBig.swift */,
				733E01FF2C04DF40000D972D /* SuperCollectionViewCell.swift */,
				733E02002C04DF40000D972D /* SuperCollectionViewCell.xib */,
				BEA85B272876E26700F4754E /* CategoriesCollectionViewCellBig.xib */,
				BEADE93F286D73B000556D1B /* ProductsCollectionViewCellSmall.swift */,
				BEADE93E286D73B000556D1B /* ProductsCollectionViewCellSmall.xib */,
				BEBA109A27E9F3610063E617 /* ProductPopup */,
				BE90223027E344D900F9ACE4 /* Select Location */,
				81A484B527DF69AE00528766 /* CategoriesCollectionViewCell.swift */,
				81A484B627DF69AE00528766 /* CategoriesCollectionViewCell.xib */,
				81A484B927DF6BF500528766 /* ProductsCollectionViewCell.swift */,
				81A484BA27DF6BF500528766 /* ProductsCollectionViewCell.xib */,
				73A9D3032B441ADD00A5CC72 /* AdvertisingCollectionViewCell.swift */,
				73A9D3042B441ADD00A5CC72 /* AdvertisingCollectionViewCell.xib */,
			);
			path = Home;
			sourceTree = "<group>";
		};
		BE1B8B5127D9D1C20045FA0E /* MyOrders */ = {
			isa = PBXGroup;
			children = (
				BE99782D27E0B8BC005CC308 /* MyOrdersTableViewCell.swift */,
				BE1B8B5427D9D1D10045FA0E /* MyOrdersInteractor.swift */,
				BE1B8B5227D9D1D10045FA0E /* MyOrdersPresenter.swift */,
				BE1B8B5327D9D1D10045FA0E /* MyOrdersViewController.swift */,
			);
			path = MyOrders;
			sourceTree = "<group>";
		};
		BE1B8B5827D9D1F90045FA0E /* Offers */ = {
			isa = PBXGroup;
			children = (
				************************ /* OffersTableViewCell.swift */,
				BE1B8B5B27D9D2080045FA0E /* OffersInteractor.swift */,
				BE1B8B5927D9D2080045FA0E /* OffersPresenter.swift */,
				BE1B8B5A27D9D2080045FA0E /* OffersViewController.swift */,
			);
			path = Offers;
			sourceTree = "<group>";
		};
		BE1BC0B327FD719900C9E42E /* Checkout */ = {
			isa = PBXGroup;
			children = (
				BE1BC0B527FD71DD00C9E42E /* MyCartCheckoutTableViewCell.swift */,
				BE1BC0B427FD71DD00C9E42E /* MyCartCheckoutTableViewCell.xib */,
			);
			path = Checkout;
			sourceTree = "<group>";
		};
		BE1FF69127D8C18D008291AF /* Tabbar */ = {
			isa = PBXGroup;
			children = (
				BE1FF69227D8C19E008291AF /* MainTabbarViewController.swift */,
			);
			path = Tabbar;
			sourceTree = "<group>";
		};
		************************ /* OTP Verification */ = {
			isa = PBXGroup;
			children = (
				************************ /* OTPVerificationInteractor.swift */,
				************************ /* OTPVerificationPresenter.swift */,
				************************ /* OTPVerificationViewController.swift */,
			);
			path = "OTP Verification";
			sourceTree = "<group>";
		};
		************************ /* Account */ = {
			isa = PBXGroup;
			children = (
				************************ /* AccountInteractor.swift */,
				************************ /* AccountPresenter.swift */,
				************************ /* AccountViewController.swift */,
			);
			path = Account;
			sourceTree = "<group>";
		};
		************************ /* Contact Us */ = {
			isa = PBXGroup;
			children = (
				************************ /* ContactUsInteractor.swift */,
				************************ /* ContactUsPresenter.swift */,
				************************ /* ContactUsViewController.swift */,
			);
			path = "Contact Us";
			sourceTree = "<group>";
		};
		************************ /* OTPFieldView */ = {
			isa = PBXGroup;
			children = (
				************************ /* OTPFieldView.swift */,
				************************ /* OTPTextField.swift */,
			);
			path = OTPFieldView;
			sourceTree = "<group>";
		};
		BE7A8397291132D40078FD2F /* BannerInfo */ = {
			isa = PBXGroup;
			children = (
				BE7A839A291132F80078FD2F /* BannerInfoInteractor.swift */,
				BE7A8398291132F80078FD2F /* BannerInfoPresenter.swift */,
				BE7A8399291132F80078FD2F /* BannerInfoViewController.swift */,
			);
			path = BannerInfo;
			sourceTree = "<group>";
		};
		BE8D82E427D5DC2E00A11D15 /* Login */ = {
			isa = PBXGroup;
			children = (
				BE8D82E727D5E54B00A11D15 /* LoginInteractor.swift */,
				BE8D82E527D5E54B00A11D15 /* LoginPresenter.swift */,
				BE8D82E627D5E54B00A11D15 /* LoginViewController.swift */,
			);
			path = Login;
			sourceTree = "<group>";
		};
		BE8D82EB27D5E8F200A11D15 /* Custom Classes */ = {
			isa = PBXGroup;
			children = (
				73ED99D12BCD0EBD007FBD73 /* Designable */,
				730930462B5806F700264FA3 /* LottieAnimationFiles */,
				7367EE1A2AD74B700042BACF /* Shapes */,
				73FCE8842ACD6A6B0014DD95 /* Extension */,
				BEC97A0A29E030740049E7C6 /* done_icon.json */,
				BE8CBF6528EC39FF00A3BB7A /* SplashViewController.swift */,
				BEA9574228DDDA8900DCFFF9 /* LoaderAnimation.json */,
				BEA9574328DDDA8A00DCFFF9 /* LoadingView.swift */,
				BE10DF1C28BBFBFE000983A2 /* Localization */,
				BEAF3DC3288BD8A0006DE167 /* GoogleAPIHelper.swift */,
				BEA7B9632836A44F00F9F235 /* LocationManager.swift */,
				BE973F0227E85B0B00C09458 /* ActivityIndicator.swift */,
				BE9763A527E2105F00F29744 /* ViewEmbedder.swift */,
				BE9A1D2527D64D630072BCAC /* BaseViewController.swift */,
				BEC0146727D6232900509E26 /* CustomViewForFields.swift */,
				BE8D82EC27D5E90F00A11D15 /* CustomRoundedButtton.swift */,
				BE13CB8327D6362600607C2D /* CustomTextfieldWithFontStyle.swift */,
				BED25AE428F814670048062D /* MixpanelEvents.swift */,
				73D363E82CB6B12700F1D163 /* AppsFlyerEvents.swift */,
				73E160932D60E90F0086A16B /* FirebaseEvents.swift */,
				733007592D5F7727002474AD /* TikTokEvents.swift */,
				E2C33C922E00DF8500EF66A2 /* CustomSwiftUIAlertView.swift */,
			);
			path = "Custom Classes";
			sourceTree = "<group>";
		};
		BE8D82F027D6045000A11D15 /* Fonts */ = {
			isa = PBXGroup;
			children = (
				73E185F52A8AC629008FC178 /* segoeUI.ttf */,
				BE973F0427E88CC300C09458 /* LoewNextArabic-Heavy.ttf */,
				BE6C5EA927DA123800F6C188 /* LoewNextArabic-ExtraBold.ttf */,
				BE13CB8127D6357800607C2D /* LoewNextArabic-Medium.ttf */,
				BE8D82F127D604E100A11D15 /* LoewNextArabic-Bold.ttf */,
			);
			path = Fonts;
			sourceTree = "<group>";
		};
		BE8D82F427D6063100A11D15 /* Localisations */ = {
			isa = PBXGroup;
			children = (
				BE8D82F927D6069000A11D15 /* Localizable.strings */,
			);
			path = Localisations;
			sourceTree = "<group>";
		};
		BE90222927E344CA00F9ACE4 /* Select Language */ = {
			isa = PBXGroup;
			children = (
				BE90222B27E344CA00F9ACE4 /* SelectLanguageInteractor.swift */,
				BE90222A27E344CA00F9ACE4 /* SelectLanguagePresenter.swift */,
				BE90222C27E344CA00F9ACE4 /* SelectLanguageViewController.swift */,
			);
			path = "Select Language";
			sourceTree = "<group>";
		};
		BE90223027E344D900F9ACE4 /* Select Location */ = {
			isa = PBXGroup;
			children = (
				BECA3B7C28217E8C00675D20 /* AddressListTableViewCell.swift */,
				BE90223227E344D900F9ACE4 /* SelectLocationInteractor.swift */,
				BE90223327E344D900F9ACE4 /* SelectLocationPresenter.swift */,
				BE90223127E344D900F9ACE4 /* SelectLocationViewController.swift */,
			);
			path = "Select Location";
			sourceTree = "<group>";
		};
		BE90223727E3454800F9ACE4 /* BottomPopupController */ = {
			isa = PBXGroup;
			children = (
				BE90223827E3454800F9ACE4 /* BottomPopupUtils.swift */,
				BE90223927E3454800F9ACE4 /* BottomPopupViewController.swift */,
				BE90223A27E3454800F9ACE4 /* BottomPopupDismissInteractionController.swift */,
				BE90223B27E3454800F9ACE4 /* BottomPopupNavigationController.swift */,
				BE90223C27E3454800F9ACE4 /* BottomPopupDismissAnimator.swift */,
				BE90223D27E3454800F9ACE4 /* BottomPopupPresentAnimator.swift */,
				BE90223E27E3454800F9ACE4 /* BottomPopupTransitionHandler.swift */,
				BE90223F27E3454800F9ACE4 /* BottomPopupPresentationController.swift */,
			);
			path = BottomPopupController;
			sourceTree = "<group>";
		};
		BE93F86427EDE30E0019120C /* QTYButtons */ = {
			isa = PBXGroup;
			children = (
				BE93F86527EDE30E0019120C /* QTYButton.swift */,
				BE93F86627EDE30E0019120C /* QTYButtons.h */,
				BE93F86727EDE30E0019120C /* QTYButton.xib */,
			);
			path = QTYButtons;
			sourceTree = "<group>";
		};
		************************ /* Cart */ = {
			isa = PBXGroup;
			children = (
				BE1BC0B327FD719900C9E42E /* Checkout */,
				BE93F87A27EDE3B20019120C /* MyCart */,
			);
			path = Cart;
			sourceTree = "<group>";
		};
		BE93F87A27EDE3B20019120C /* MyCart */ = {
			isa = PBXGroup;
			children = (
				732E03332C2C4A07008B6088 /* ChooseDeliveryDate */,
				738B81B62C11A4750069DC6E /* CartProductsOfBundleTVCell */,
				738B81AF2C1195EC0069DC6E /* UIView */,
				73E190022A9D32AE0010AE40 /* MayAlsoLikeTVCell */,
				BE93F87B27EDE3B20019120C /* MyCartInteractor.swift */,
				BE93F87D27EDE3B20019120C /* MyCartPresenter.swift */,
				BE93F87C27EDE3B20019120C /* MyCartViewController.swift */,
				BE93F87E27EDE3B20019120C /* Checkout */,
				BE93F88227EDE3B20019120C /* MyCartTableViewCell.xib */,
				BE93F88327EDE3B20019120C /* MyCartTableViewCell.swift */,
				BEC2E9D729CA27AB00B837F8 /* CheckoutShiftsCell.swift */,
				73F9CE472C553EAA00635DDC /* ShiftsBySelectDateCell.swift */,
				73F9CE482C553EAA00635DDC /* ShiftsBySelectDateCell.xib */,
				BEC2E9D829CA27AB00B837F8 /* CheckoutShiftsCell.xib */,
			);
			path = MyCart;
			sourceTree = "<group>";
		};
		BE93F87E27EDE3B20019120C /* Checkout */ = {
			isa = PBXGroup;
			children = (
				BE93F87F27EDE3B20019120C /* CheckoutInteractor.swift */,
				BE93F88127EDE3B20019120C /* CheckoutPresenter.swift */,
				BE93F88027EDE3B20019120C /* CheckoutViewController.swift */,
			);
			path = Checkout;
			sourceTree = "<group>";
		};
		BE94FADA27FC571B00B131DD /* Invoice */ = {
			isa = PBXGroup;
			children = (
				BE94FADD27FC573E00B131DD /* InvoiceInteractor.swift */,
				BE94FADB27FC573E00B131DD /* InvoicePresenter.swift */,
				BE94FADC27FC573E00B131DD /* InvoiceViewController.swift */,
			);
			path = Invoice;
			sourceTree = "<group>";
		};
		BE94FAE127FC5CFF00B131DD /* ScheduledOrderDetail */ = {
			isa = PBXGroup;
			children = (
				BE94FAE427FC5D7F00B131DD /* ScheduledOrderDetailInteractor.swift */,
				BE94FAE227FC5D7F00B131DD /* ScheduledOrderDetailPresenter.swift */,
				BE94FAE327FC5D7F00B131DD /* ScheduledOrderDetailViewController.swift */,
			);
			path = ScheduledOrderDetail;
			sourceTree = "<group>";
		};
		************************ /* FilterSideMenu */ = {
			isa = PBXGroup;
			children = (
				BE9568EE27F6FBFB002E96C3 /* FilterSideMenuInteractor.swift */,
				BE9568EC27F6FBFB002E96C3 /* FilterSideMenuPresenter.swift */,
				BE9568ED27F6FBFB002E96C3 /* FilterSideMenuViewController.swift */,
			);
			path = FilterSideMenu;
			sourceTree = "<group>";
		};
		************************ /* CurrentOrders */ = {
			isa = PBXGroup;
			children = (
				BE9FEF1027EC9300002CAB93 /* CurrentOrdersTableViewCell.swift */,
				BE97639A27E2081400F29744 /* CurrentOrdersInteractor.swift */,
				BE97639827E2081400F29744 /* CurrentOrdersPresenter.swift */,
				BE97639927E2081400F29744 /* CurrentOrdersViewController.swift */,
			);
			path = CurrentOrders;
			sourceTree = "<group>";
		};
		************************ /* ScheduledOrders */ = {
			isa = PBXGroup;
			children = (
				BE9FEF1227EC9CE2002CAB93 /* ScheduledOrdersTableViewCell.swift */,
				BE9763A127E2085E00F29744 /* ScheduledOrdersInteractor.swift */,
				BE97639F27E2085E00F29744 /* ScheduledOrdersPresenter.swift */,
				BE9763A027E2085E00F29744 /* ScheduledOrdersViewController.swift */,
			);
			path = ScheduledOrders;
			sourceTree = "<group>";
		};
		BE9A1D1427D641CA0072BCAC /* CountryPickerViewSwift */ = {
			isa = PBXGroup;
			children = (
				BE9A1D1527D641CA0072BCAC /* Resources */,
			);
			path = CountryPickerViewSwift;
			sourceTree = "<group>";
		};
		BE9A1D1527D641CA0072BCAC /* Resources */ = {
			isa = PBXGroup;
			children = (
				BE9A1D1627D641CA0072BCAC /* CountryCode.swift */,
				BE9A1D1727D641CA0072BCAC /* CountrySelectView.swift */,
				BE9A1D1827D641CA0072BCAC /* CountryTableViewCell.swift */,
				BE9A1D1927D641CA0072BCAC /* CountryPicker.bundle */,
			);
			path = Resources;
			sourceTree = "<group>";
		};
		BE9B3A4127FEF82E00EF3615 /* Payments */ = {
			isa = PBXGroup;
			children = (
				BE9B3A4427FEF83D00EF3615 /* PaymentsInteractor.swift */,
				BE9B3A4227FEF83D00EF3615 /* PaymentsPresenter.swift */,
				BE9B3A4327FEF83D00EF3615 /* PaymentsViewController.swift */,
				BE9B3A4A27FEFB1A00EF3615 /* PaymentCardListTableViewCell.swift */,
			);
			path = Payments;
			sourceTree = "<group>";
		};
		BE9B3A4C27FF21A500EF3615 /* ChoosePayment */ = {
			isa = PBXGroup;
			children = (
				BE9B3A4F27FF21B800EF3615 /* ChoosePaymentInteractor.swift */,
				BE9B3A4D27FF21B800EF3615 /* ChoosePaymentPresenter.swift */,
				BE9B3A4E27FF21B800EF3615 /* ChoosePaymentViewController.swift */,
			);
			path = ChoosePayment;
			sourceTree = "<group>";
		};
		BE9B3A5327FF49C200EF3615 /* AddCard */ = {
			isa = PBXGroup;
			children = (
				BE9B3A5627FF49D000EF3615 /* AddCardInteractor.swift */,
				BE9B3A5427FF49D000EF3615 /* AddCardPresenter.swift */,
				BE9B3A5527FF49D000EF3615 /* AddCardViewController.swift */,
			);
			path = AddCard;
			sourceTree = "<group>";
		};
		BEA7620A2886ACD900A890E4 /* TrackOrder */ = {
			isa = PBXGroup;
			children = (
				BEA7620D2886ACF200A890E4 /* TrackOrderInteractor.swift */,
				BEA7620B2886ACF200A890E4 /* TrackOrderPresenter.swift */,
				BEA7620C2886ACF200A890E4 /* TrackOrderViewController.swift */,
			);
			path = TrackOrder;
			sourceTree = "<group>";
		};
		BEAEE5CB28BE2E210098946C /* Reference */ = {
			isa = PBXGroup;
			children = (
				BEAEE5CC28BE2E210098946C /* ReferenceVC.swift */,
			);
			path = Reference;
			sourceTree = "<group>";
		};
		BEAF3DC5288BDA08006DE167 /* GoogleDistanceMetrix */ = {
			isa = PBXGroup;
			children = (
				BEAF3DC6288BDA08006DE167 /* GoogleDistanceMetrixElement.swift */,
				BEAF3DC7288BDA08006DE167 /* GoogleDistanceMetrixDuration.swift */,
				BEAF3DC8288BDA08006DE167 /* GoogleDistanceMetrixRow.swift */,
				BEAF3DC9288BDA08006DE167 /* GoogleDistanceMetrixDistance.swift */,
				BEAF3DCA288BDA08006DE167 /* GoogleDistanceMetrixRootClass.swift */,
			);
			path = GoogleDistanceMetrix;
			sourceTree = "<group>";
		};
		BEAF3DCB288BDA09006DE167 /* Google */ = {
			isa = PBXGroup;
			children = (
				BEAF3DCC288BDA09006DE167 /* GoogleDirectionDetailsLeg.swift */,
				BEAF3DCD288BDA09006DE167 /* GoogleDirectionDetailsEndLocation.swift */,
				BEAF3DCE288BDA09006DE167 /* GoogleDirectionDetailsOverviewPolyline.swift */,
				BEAF3DCF288BDA09006DE167 /* GoogleDirectionDetailsStep.swift */,
				BEAF3DD0288BDA09006DE167 /* GoogleDirectionDetailsBound.swift */,
				BEAF3DD1288BDA09006DE167 /* GoogleDirectionDetailsSouthwest.swift */,
				BEAF3DD2288BDA09006DE167 /* GoogleDirectionDetailsLocation.swift */,
				BEAF3DD3288BDA09006DE167 /* GoogleDirectionDetailsDistance.swift */,
				BEAF3DD4288BDA09006DE167 /* GoogleDirectionDetailsStartLocation.swift */,
				BEAF3DD5288BDA09006DE167 /* GoogleDirectionDetailsDuration.swift */,
				BEAF3DD6288BDA09006DE167 /* GoogleDirectionDetailsNortheast.swift */,
				BEAF3DD7288BDA09006DE167 /* GoogleDirectionDetailsViaWaypoint.swift */,
				BEAF3DD8288BDA09006DE167 /* GoogleDirectionDetailsPolyline.swift */,
				BEAF3DD9288BDA09006DE167 /* GoogleDirectionDetailsGeocodedWaypoint.swift */,
				BEAF3DDA288BDA09006DE167 /* GoogleDirectionDetailsRoute.swift */,
				BEAF3DDB288BDA09006DE167 /* GoogleDirectionDetailsRootClass.swift */,
			);
			path = Google;
			sourceTree = "<group>";
		};
		BEAFCB9C2995265900276C1D /* FloatRatingView */ = {
			isa = PBXGroup;
			children = (
				BEAFCB9D2995265900276C1D /* FloatRatingView.swift */,
			);
			path = FloatRatingView;
			sourceTree = "<group>";
		};
		BEB3CADB2918DBF2001C400E /* NewHome */ = {
			isa = PBXGroup;
			children = (
				7382DE582CB7F0240060C4B5 /* MainSliderCells */,
				73A6F6D42B860B2D001DC9EA /* BunddelsList */,
				BEB3CADE2918DC1B001C400E /* NewHomeInteractor.swift */,
				BEB3CADC2918DC1B001C400E /* NewHomePresenter.swift */,
				BEB3CADD2918DC1B001C400E /* NewHomeViewController.swift */,
				BE822D4C291904BB00E91318 /* HomeTableViewCell.swift */,
				BED0E5B129B73EDC00CF14FA /* HomeBannerCell.swift */,
				732D67362AB20C8400366120 /* CategoriesTableViewCell.swift */,
				7376F23D2AFB9D9E00FAB85A /* LastOffer */,
			);
			path = NewHome;
			sourceTree = "<group>";
		};
		BEB4FE1227F5A7B8006B5BCC /* ThankYou */ = {
			isa = PBXGroup;
			children = (
				BEB4FE1527F5A7D0006B5BCC /* ThankYouInteractor.swift */,
				BEB4FE1327F5A7D0006B5BCC /* ThankYouPresenter.swift */,
				BEB4FE1427F5A7D0006B5BCC /* ThankYouViewController.swift */,
			);
			path = ThankYou;
			sourceTree = "<group>";
		};
		BEB4FE1927F5AFB5006B5BCC /* OfferDetails */ = {
			isa = PBXGroup;
			children = (
				BEB4FE1C27F5AFCD006B5BCC /* OfferDetailsInteractor.swift */,
				BEB4FE1A27F5AFCD006B5BCC /* OfferDetailsPresenter.swift */,
				BEB4FE1B27F5AFCD006B5BCC /* OfferDetailsViewController.swift */,
			);
			path = OfferDetails;
			sourceTree = "<group>";
		};
		BEB4FE2027F5CC00006B5BCC /* OrderDetail */ = {
			isa = PBXGroup;
			children = (
				BEB4FE2327F5CC13006B5BCC /* OrderDetailInteractor.swift */,
				BEB4FE2127F5CC13006B5BCC /* OrderDetailPresenter.swift */,
				BEB4FE2227F5CC13006B5BCC /* OrderDetailViewController.swift */,
			);
			path = OrderDetail;
			sourceTree = "<group>";
		};
		BEB5BE2728B773690075CF42 /* QTYButtons_old */ = {
			isa = PBXGroup;
			children = (
				BEB5BE2828B773690075CF42 /* QTYButtonOLD.swift */,
				BEB5BE2928B773690075CF42 /* QTYButtonOLD.h */,
				BEB5BE2A28B773690075CF42 /* QTYButtonOLD.xib */,
			);
			path = QTYButtons_old;
			sourceTree = "<group>";
		};
		BEBA109A27E9F3610063E617 /* ProductPopup */ = {
			isa = PBXGroup;
			children = (
				73F2D1DB2C8ECD51004A9738 /* Cells */,
				BEBA109B27E9F3610063E617 /* ProductPopupInteractor.swift */,
				BEBA109D27E9F3610063E617 /* ProductPopupPresenter.swift */,
				BEBA109C27E9F3610063E617 /* ProductPopupViewController.swift */,
			);
			path = ProductPopup;
			sourceTree = "<group>";
		};
		BEBF066527D7753E00C3AF62 /* Settings */ = {
			isa = PBXGroup;
			children = (
				8117DF7327E9AA4600004F69 /* History Order Details */,
				8117DF6827E9989700004F69 /* History Orders */,
				BE90222927E344CA00F9ACE4 /* Select Language */,
				BEBF067527D78A9200C3AF62 /* SettingsTableViewCell.swift */,
				BEBF066827D7769D00C3AF62 /* SettingsInteractor.swift */,
				BEBF066627D7769D00C3AF62 /* SettingsPresenter.swift */,
				BEBF066727D7769D00C3AF62 /* SettingsViewController.swift */,
			);
			path = Settings;
			sourceTree = "<group>";
		};
		BEBF066C27D7844600C3AF62 /* Language */ = {
			isa = PBXGroup;
			children = (
				BEBF066D27D7844600C3AF62 /* MirroringLabel.swift */,
				BEBF066E27D7844600C3AF62 /* MirroringViewController.swift */,
				BEBF066F27D7844600C3AF62 /* L012Localizer.swift */,
				BEBF067027D7844600C3AF62 /* L102Language.swift */,
			);
			path = Language;
			sourceTree = "<group>";
		};
		BEBF067727D79EF400C3AF62 /* StaticPage */ = {
			isa = PBXGroup;
			children = (
				BEBF067A27D79F0B00C3AF62 /* StaticPageInteractor.swift */,
				BEBF067827D79F0B00C3AF62 /* StaticPagePresenter.swift */,
				BEBF067927D79F0B00C3AF62 /* StaticPageViewController.swift */,
			);
			path = StaticPage;
			sourceTree = "<group>";
		};
		BEC2851127FAD23200A998A8 /* FilterSearch */ = {
			isa = PBXGroup;
			children = (
				BEC898B628FFFF22008AE165 /* SearchSuggestionCell.swift */,
				BEC2851527FAD25800A998A8 /* FilterSearchInteractor.swift */,
				BEC2851327FAD25800A998A8 /* FilterSearchPresenter.swift */,
				BEC2851427FAD25800A998A8 /* FilterSearchViewController.swift */,
			);
			path = FilterSearch;
			sourceTree = "<group>";
		};
		BEC2851927FAF69100A998A8 /* AddAddress */ = {
			isa = PBXGroup;
			children = (
				BEC2851C27FAF6A200A998A8 /* AddAddressInteractor.swift */,
				BEC2851A27FAF6A200A998A8 /* AddAddressPresenter.swift */,
				BEC2851B27FAF6A200A998A8 /* AddAddressViewController.swift */,
			);
			path = AddAddress;
			sourceTree = "<group>";
		};
		BEC3587629642F6D0005040C /* AdvertisementPopup */ = {
			isa = PBXGroup;
			children = (
				73EBCEFD2CD010AD00EB14C4 /* Cells */,
				BEC3587929642F910005040C /* AdvertisementPopupInteractor.swift */,
				BEC3587729642F910005040C /* AdvertisementPopupPresenter.swift */,
				BEC3587829642F910005040C /* AdvertisementPopupViewController.swift */,
			);
			path = AdvertisementPopup;
			sourceTree = "<group>";
		};
		BECDBAA029D56A37008E8EAC /* VersionUpdatePopup */ = {
			isa = PBXGroup;
			children = (
				BECDBAA329D56A60008E8EAC /* VersionUpdateInteractor.swift */,
				BECDBAA129D56A60008E8EAC /* VersionUpdatePresenter.swift */,
				BECDBAA229D56A60008E8EAC /* VersionUpdateViewController.swift */,
			);
			path = VersionUpdatePopup;
			sourceTree = "<group>";
		};
		BEDD1C892A0243E0007D4E4B /* Swaggers */ = {
			isa = PBXGroup;
			children = (
				BEDD1C8A2A0243E0007D4E4B /* APIHelper.swift */,
				BEDD1C8B2A0243E0007D4E4B /* AlamofireImplementations.swift */,
				BEDD1C8C2A0243E0007D4E4B /* JSONValue.swift */,
				BEDD1C8D2A0243E0007D4E4B /* APIs */,
				BEDD1CA32A0243E0007D4E4B /* CodableHelper.swift */,
				BEDD1CA42A0243E0007D4E4B /* JSONEncodableEncoding.swift */,
				BEDD1CA52A0243E0007D4E4B /* APIs.swift */,
				BEDD1CA62A0243E0007D4E4B /* Models */,
				BEDD1D302A0243E0007D4E4B /* Extensions.swift */,
				BEDD1D312A0243E0007D4E4B /* JSONEncodingHelper.swift */,
				BEDD1D322A0243E0007D4E4B /* Models.swift */,
				BEDD1D332A0243E0007D4E4B /* Configuration.swift */,
			);
			path = Swaggers;
			sourceTree = "<group>";
		};
		BEDD1C8D2A0243E0007D4E4B /* APIs */ = {
			isa = PBXGroup;
			children = (
				BEDD1C8E2A0243E0007D4E4B /* UserAPI.swift */,
				BEDD1C8F2A0243E0007D4E4B /* APPVersionCheckAPI.swift */,
				BEDD1C902A0243E0007D4E4B /* ContactUsAPI.swift */,
				BEDD1C912A0243E0007D4E4B /* OrderAPI.swift */,
				BEDD1C922A0243E0007D4E4B /* NotificationAPI.swift */,
				BEDD1C932A0243E0007D4E4B /* UserCardsAPI.swift */,
				BEDD1C942A0243E0007D4E4B /* UserWalletAPI.swift */,
				BEDD1C952A0243E0007D4E4B /* CartAPI.swift */,
				BEDD1C962A0243E0007D4E4B /* DriverAPI.swift */,
				BEDD1C972A0243E0007D4E4B /* RatingAPI.swift */,
				BEDD1C982A0243E0007D4E4B /* FavouriteAPI.swift */,
				BEDD1C992A0243E0007D4E4B /* AuthenticationAPI.swift */,
				BEDD1C9A2A0243E0007D4E4B /* ProductAPI.swift */,
				BEDD1C9B2A0243E0007D4E4B /* NotifyMeAPI.swift */,
				BEDD1C9C2A0243E0007D4E4B /* AdvertisementAPI.swift */,
				BEDD1C9D2A0243E0007D4E4B /* CategoryAPI.swift */,
				BEDD1C9E2A0243E0007D4E4B /* OfferAPI.swift */,
				BEDD1C9F2A0243E0007D4E4B /* CountryAPI.swift */,
				BEDD1CA02A0243E0007D4E4B /* PromocodeAPI.swift */,
				BEDD1CA12A0243E0007D4E4B /* BannerAPI.swift */,
				BEDD1CA22A0243E0007D4E4B /* CommonAPI.swift */,
				7370954E2AAD1F4C0089D3E2 /* UserSearchAPI.swift */,
			);
			path = APIs;
			sourceTree = "<group>";
		};
		BEDD1CA62A0243E0007D4E4B /* Models */ = {
			isa = PBXGroup;
			children = (
				BEDD1CA72A0243E0007D4E4B /* OauthGuestloginBody.swift */,
				BEDD1CA82A0243E0007D4E4B /* SkipRatingResponse.swift */,
				BEDD1CA92A0243E0007D4E4B /* LatestHomeWebListingResponse.swift */,
				BEDD1CAA2A0243E0007D4E4B /* GetCartResponse.swift */,
				BEDD1CAB2A0243E0007D4E4B /* ProductSuggestionResponseFields.swift */,
				BEDD1CAC2A0243E0007D4E4B /* DriverProductCountResponse.swift */,
				73A6CF382A988E14007F3657 /* CartYouMayAlsoLikeProductsResponse.swift */,
				BEDD1CAD2A0243E0007D4E4B /* WebCategoryListResponseFields.swift */,
				BEDD1CAE2A0243E0007D4E4B /* OauthSocialsigninBody.swift */,
				BEDD1CAF2A0243E0007D4E4B /* ProductLatestproductlistingBody.swift */,
				BEDD1CB02A0243E0007D4E4B /* AppVersionResponse.swift */,
				BEDD1CB12A0243E0007D4E4B /* PromocodeCheckResponseFields.swift */,
				BEDD1CB22A0243E0007D4E4B /* OrderCreateorderBody.swift */,
				BEDD1CB32A0243E0007D4E4B /* ProductInfoResponse.swift */,
				BEDD1CB42A0243E0007D4E4B /* WalletDetailsResponseFields.swift */,
				BEDD1CB52A0243E0007D4E4B /* AppVersionResponseFields.swift */,
				BEDD1CB62A0243E0007D4E4B /* DriverWeeklyScheduleResponseFields.swift */,
				BEDD1CB72A0243E0007D4E4B /* AdvertisementResponseFields.swift */,
				BEDD1CB82A0243E0007D4E4B /* UserChangepasswordBody.swift */,
				BEDD1CB92A0243E0007D4E4B /* DriverOrdercancelBody.swift */,
				BEDD1CBA2A0243E0007D4E4B /* UserAddusercardsBody.swift */,
				BEDD1CBB2A0243E0007D4E4B /* BannerResponse.swift */,
				BEDD1CBC2A0243E0007D4E4B /* UpdateLanguageResponse.swift */,
				BEDD1CBD2A0243E0007D4E4B /* ProductResponseFields.swift */,
				BEDD1CBE2A0243E0007D4E4B /* LatestHomeWebListingResponseFields.swift */,
				BEDD1CBF2A0243E0007D4E4B /* ShiftResponseFields.swift */,
				BEDD1CC02A0243E0007D4E4B /* AdvertisementResponse.swift */,
				BEDD1CC12A0243E0007D4E4B /* DetailFields.swift */,
				BEDD1CC22A0243E0007D4E4B /* ProductProductsuggestionBody.swift */,
				BEDD1CC32A0243E0007D4E4B /* OfferResponse.swift */,
				BEDD1CC42A0243E0007D4E4B /* DriverProductCountResponseFields.swift */,
				BEDD1CC52A0243E0007D4E4B /* CheckappversionBody.swift */,
				BEDD1CC62A0243E0007D4E4B /* LatestProductListResponse.swift */,
				BEDD1CC72A0243E0007D4E4B /* PromocodePromocodecheckBody.swift */,
				BEDD1CC82A0243E0007D4E4B /* OauthSignupBody.swift */,
				BEDD1CC92A0243E0007D4E4B /* TransactionResponse.swift */,
				BEDD1CCA2A0243E0007D4E4B /* WebProductResponseFields.swift */,
				BEDD1CCB2A0243E0007D4E4B /* DriverCompletedOrderDeatailsResponse.swift */,
				BEDD1CCC2A0243E0007D4E4B /* DriverOrderDeatailsResponse.swift */,
				BEDD1CCD2A0243E0007D4E4B /* ProductInfoResponseFields.swift */,
				BEDD1CCE2A0243E0007D4E4B /* CategoryListResponseFields.swift */,
				BEDD1CCF2A0243E0007D4E4B /* OrderedProductDetailResponseFields.swift */,
				BEDD1CD02A0243E0007D4E4B /* OrderListResponse.swift */,
				BEDD1CD12A0243E0007D4E4B /* ContentPageResponse.swift */,
				BEDD1CD22A0243E0007D4E4B /* OrderGetorderlistBody.swift */,
				BEDD1CD32A0243E0007D4E4B /* DriverRescheduleorderBody.swift */,
				BEDD1CD42A0243E0007D4E4B /* CreateOrderResponseFields.swift */,
				BEDD1CD52A0243E0007D4E4B /* ContactContacthelpcreateBody.swift */,
				BEDD1CD62A0243E0007D4E4B /* LatestHomeListingResponseFields.swift */,
				BEDD1CD72A0243E0007D4E4B /* LatestHomeListingResponse.swift */,
				BEDD1CD82A0243E0007D4E4B /* UserDetailListingResponseFields.swift */,
				BEDD1CD92A0243E0007D4E4B /* FavouriteMarkfavouriteBody.swift */,
				BEDD1CDA2A0243E0007D4E4B /* UserUpdatedevicetokenBody.swift */,
				BEDD1CDB2A0243E0007D4E4B /* GetCartResponseFields.swift */,
				BEDD1CDC2A0243E0007D4E4B /* WalletDetailsResponse.swift */,
				BEDD1CDD2A0243E0007D4E4B /* NotificationListResponseFields.swift */,
				BEDD1CDE2A0243E0007D4E4B /* CardResponseFields.swift */,
				BEDD1CDF2A0243E0007D4E4B /* DriverOrderResponseFields.swift */,
				BEDD1CE02A0243E0007D4E4B /* OfferListResponseFields.swift */,
				BEDD1CE12A0243E0007D4E4B /* OrderOrdertransactionupdateBody.swift */,
				BEDD1CE22A0243E0007D4E4B /* ListResponse.swift */,
				BEDD1CE32A0243E0007D4E4B /* GetCartShiftResponseFields.swift */,
				BEDD1CE42A0243E0007D4E4B /* DriverHomeListingResponseFields.swift */,
				BEDD1CE52A0243E0007D4E4B /* MarketingBannerResponseFields.swift */,
				BEDD1CE62A0243E0007D4E4B /* DriverOrderDeatailsResponseFields.swift */,
				BEDD1CE72A0243E0007D4E4B /* CacheResponse.swift */,
				BEDD1CE82A0243E0007D4E4B /* PromocodeCheckResponse.swift */,
				BEDD1CE92A0243E0007D4E4B /* ProductProductlistingBody.swift */,
				BEDD1CEA2A0243E0007D4E4B /* OrderResponseFields.swift */,
				BEDD1CEB2A0243E0007D4E4B /* DayWiseResponse.swift */,
				BEDD1CEC2A0243E0007D4E4B /* ContentPageResponseFields.swift */,
				BEDD1CED2A0243E0007D4E4B /* ListResponseFields.swift */,
				BEDD1CEE2A0243E0007D4E4B /* UpdateLanguageResponseFields.swift */,
				BEDD1CEF2A0243E0007D4E4B /* CancelOrderReasonResponseFields.swift */,
				BEDD1CF02A0243E0007D4E4B /* OrderDetailResponseFields.swift */,
				BEDD1CF12A0243E0007D4E4B /* OfferListResponse.swift */,
				BEDD1CF22A0243E0007D4E4B /* ProductProductweblistingBody.swift */,
				BEDD1CF32A0243E0007D4E4B /* NotificationResponseFields.swift */,
				BEDD1CF42A0243E0007D4E4B /* AddToCartResponse.swift */,
				BEDD1CF52A0243E0007D4E4B /* AddressResponseFields.swift */,
				BEDD1CF62A0243E0007D4E4B /* RatingAddratingBody.swift */,
				BEDD1CF72A0243E0007D4E4B /* ProductDetailResponse.swift */,
				BEDD1CF82A0243E0007D4E4B /* DriverHomeListingResponse.swift */,
				BEDD1CF92A0243E0007D4E4B /* DriverOrderStatusResponse.swift */,
				BEDD1CFA2A0243E0007D4E4B /* OrderListResponseFields.swift */,
				BEDD1CFB2A0243E0007D4E4B /* AddRatingResponse.swift */,
				BEDD1CFC2A0243E0007D4E4B /* NotificationResponse.swift */,
				BEDD1CFD2A0243E0007D4E4B /* ShiftResponse.swift */,
				BEDD1CFE2A0243E0007D4E4B /* UserResponse.swift */,
				BEDD1CFF2A0243E0007D4E4B /* DriverCurrentOrderListingResponse.swift */,
				BEDD1D002A0243E0007D4E4B /* OrderUpdatecontactnumberBody.swift */,
				BEDD1D012A0243E0007D4E4B /* UserDetailListingResponse.swift */,
				BEDD1D022A0243E0007D4E4B /* DynamicSectionResponseFields.swift */,
				BEDD1D032A0243E0007D4E4B /* HomeListingResponse.swift */,
				BEDD1D042A0243E0007D4E4B /* AddressResponse.swift */,
				BEDD1D052A0243E0007D4E4B /* OauthVerifyotpBody.swift */,
				BEDD1D062A0243E0007D4E4B /* ProductSuggestionResponse.swift */,
				BEDD1D072A0243E0007D4E4B /* UserProfileBody.swift */,
				BEDD1D082A0243E0007D4E4B /* FavouriteResponseFields.swift */,
				BEDD1D092A0243E0007D4E4B /* CountryListResponse.swift */,
				BEDD1D0A2A0243E0007D4E4B /* GetUserCardDetailResponseFields.swift */,
				BEDD1D0B2A0243E0007D4E4B /* CreateOrderResponse.swift */,
				BEDD1D0C2A0243E0007D4E4B /* UserDrivertipBody.swift */,
				BEDD1D0D2A0243E0007D4E4B /* CardResponse.swift */,
				BEDD1D0E2A0243E0007D4E4B /* NotifymeBody.swift */,
				BEDD1D0F2A0243E0007D4E4B /* UserAddaddressBody.swift */,
				BEDD1D102A0243E0007D4E4B /* ProductProductinfoBody.swift */,
				BEDD1D112A0243E0007D4E4B /* UserUserlanguageBody.swift */,
				BEDD1D122A0243E0007D4E4B /* DriverCurrentOrderListingResponseFields.swift */,
				BEDD1D132A0243E0007D4E4B /* DriverCompletedOrderDeatailsResponseFields.swift */,
				BEDD1D142A0243E0007D4E4B /* CountryListResponseFields.swift */,
				BEDD1D152A0243E0007D4E4B /* AddToCartResponseFields.swift */,
				BEDD1D162A0243E0007D4E4B /* UserAddmoneytowalletBody.swift */,
				BEDD1D172A0243E0007D4E4B /* DriverWeeklyScheduleResponse.swift */,
				BEDD1D182A0243E0007D4E4B /* AddRatingResponseFields.swift */,
				BEDD1D192A0243E0007D4E4B /* ProductDetailsResponseFields.swift */,
				BEDD1D1A2A0243E0007D4E4B /* HomeListingResponseFields.swift */,
				BEDD1D1B2A0243E0007D4E4B /* UserResponseFields.swift */,
				BEDD1D1C2A0243E0007D4E4B /* FavouriteResponse.swift */,
				BEDD1D1D2A0243E0007D4E4B /* DriverRescheduleOrderResponse.swift */,
				BEDD1D1E2A0243E0007D4E4B /* ProductListResponseFields.swift */,
				BEDD1D1F2A0243E0007D4E4B /* FavouriteListResponse.swift */,
				BEDD1D202A0243E0007D4E4B /* OauthRequestforotpBody.swift */,
				BEDD1D212A0243E0007D4E4B /* OrderDetailResponse.swift */,
				BEDD1D222A0243E0007D4E4B /* TransactionResponseFields.swift */,
				BEDD1D232A0243E0007D4E4B /* CartAddtocartBody.swift */,
				BEDD1D242A0243E0007D4E4B /* DriverCountResponseFields.swift */,
				BEDD1D252A0243E0007D4E4B /* GetCartListResponseFields.swift */,
				BEDD1D262A0243E0007D4E4B /* OfferResponseFields.swift */,
				BEDD1D272A0243E0007D4E4B /* DriverOrderstatusupdateBody.swift */,
				BEDD1D282A0243E0007D4E4B /* CommonFields.swift */,
				BEDD1D292A0243E0007D4E4B /* CancelOrderReasonResponse.swift */,
				BEDD1D2A2A0243E0007D4E4B /* NotificationBadgeResponse.swift */,
				BEDD1D2B2A0243E0007D4E4B /* MarketingBannerImageResponseFields.swift */,
				BEDD1D2C2A0243E0007D4E4B /* DriverRescheduleOrderResponseFields.swift */,
				BEDD1D2D2A0243E0007D4E4B /* OauthLoginBody.swift */,
				BEDD1D2E2A0243E0007D4E4B /* ProductListResponse.swift */,
				BEDD1D2F2A0243E0007D4E4B /* AddressListResponse.swift */,
				7346C1BD2A40C40200A9E5DF /* BaseResponseModel.swift */,
				73F6BBDB2A9783FD0009A7E0 /* FreeDeliveryValueResponse.swift */,
				737095502AAD204E0089D3E2 /* UserSearchModel.swift */,
				735499212ADAC53B0007E26E /* PointsHostoryModel.swift */,
				738BAC192AE50E1F00D42040 /* PointsLevelsModel.swift */,
				738BAC1B2AE5380D00D42040 /* CalculateUserPointsToSARModel.swift */,
				7335B9022AFB099800C877FF /* GetPointsDescriptionModel.swift */,
				735B63062B5D201900650BEF /* CheckCartResponse.swift */,
				73A6F6D12B860808001DC9EA /* BundelsListResponseFields.swift */,
				73D0AA042B98A7A0006B3BCC /* MosquesResponseFields.swift */,
				7325619E2C3D7157004AB08D /* UserQRCodeModel.swift */,
				73ABBA592CE291F700166D7E /* CityModel.swift */,
				E25C5B512E3B6FDA00DA87DF /* NewSubCategoryModel.swift */,
			);
			path = Models;
			sourceTree = "<group>";
		};
		BEE21A62299503770090C9BA /* Rating */ = {
			isa = PBXGroup;
			children = (
				BEE21A65299503890090C9BA /* RatingInteractor.swift */,
				BEE21A63299503890090C9BA /* RatingPresenter.swift */,
				BEE21A64299503890090C9BA /* RatingViewController.swift */,
			);
			path = Rating;
			sourceTree = "<group>";
		};
		BEE4FF5E29B0BF210010A24E /* Favorite */ = {
			isa = PBXGroup;
			children = (
				BEE4FF6129B0BF3A0010A24E /* FavoriteInteractor.swift */,
				BEE4FF5F29B0BF3A0010A24E /* FavoritePresenter.swift */,
				BEE4FF6029B0BF3A0010A24E /* FavoriteViewController.swift */,
			);
			path = Favorite;
			sourceTree = "<group>";
		};
		BEE64249297581CB00211571 /* ImageScrollView */ = {
			isa = PBXGroup;
			children = (
				BEE6424A297581E300211571 /* ImageScrollView.swift */,
			);
			path = ImageScrollView;
			sourceTree = "<group>";
		};
		BEE6424C2975823000211571 /* FullScreenImage */ = {
			isa = PBXGroup;
			children = (
				BEE642502975825000211571 /* FullScreenImageInteractor.swift */,
				BEE6424E2975825000211571 /* FullScreenImagePresenter.swift */,
				BEE6424F2975825000211571 /* FullScreenImageViewController.swift */,
			);
			path = FullScreenImage;
			sourceTree = "<group>";
		};
		BEF4D1D72942108900EA53FF /* CancelReason */ = {
			isa = PBXGroup;
			children = (
				BEF4D1DE2942249F00EA53FF /* CancelReasonTableViewCell.swift */,
				BEF4D1DA2942109D00EA53FF /* CancelReasonInteractor.swift */,
				BEF4D1D82942109D00EA53FF /* CancelReasonPresenter.swift */,
				BEF4D1D92942109D00EA53FF /* CancelReasonViewController.swift */,
			);
			path = CancelReason;
			sourceTree = "<group>";
		};
		E20B056C2E3261E400E635FB /* NewMainHome */ = {
			isa = PBXGroup;
			children = (
				E20B05712E32634B00E635FB /* Model */,
				E20B056E2E3261FA00E635FB /* Views */,
				E20B056D2E3261F300E635FB /* ViewModel */,
			);
			path = NewMainHome;
			sourceTree = "<group>";
		};
		E20B056D2E3261F300E635FB /* ViewModel */ = {
			isa = PBXGroup;
			children = (
				E20B05742E32643700E635FB /* NewMainHomeViewModel.swift */,
			);
			path = ViewModel;
			sourceTree = "<group>";
		};
		E20B056E2E3261FA00E635FB /* Views */ = {
			isa = PBXGroup;
			children = (
				E20B056F2E32622300E635FB /* NewMainHomeView.swift */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		E20B05712E32634B00E635FB /* Model */ = {
			isa = PBXGroup;
			children = (
				E20B05722E32635B00E635FB /* NewMainHomeModel.swift */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		E20B05762E3291F200E635FB /* NewProducts */ = {
			isa = PBXGroup;
			children = (
				E20B05782E32920400E635FB /* ViewModel */,
				E20B05772E3291FC00E635FB /* Views */,
			);
			path = NewProducts;
			sourceTree = "<group>";
		};
		E20B05772E3291FC00E635FB /* Views */ = {
			isa = PBXGroup;
			children = (
				E20B05792E32921800E635FB /* NewProductsView.swift */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		E20B05782E32920400E635FB /* ViewModel */ = {
			isa = PBXGroup;
			children = (
				E20B057B2E32925200E635FB /* NewProductsViewModel.swift */,
			);
			path = ViewModel;
			sourceTree = "<group>";
		};
		E21F53EB2E09CDF40096BC2A /* Wallet */ = {
			isa = PBXGroup;
			children = (
				E21F53EF2E0B71D90096BC2A /* ViewModel */,
				E21F53EC2E09CF090096BC2A /* Views */,
			);
			path = Wallet;
			sourceTree = "<group>";
		};
		E21F53EC2E09CF090096BC2A /* Views */ = {
			isa = PBXGroup;
			children = (
				E21F53ED2E09CF260096BC2A /* WalletView.swift */,
				E21F53F22E0B72A90096BC2A /* TransactionsView.swift */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		E21F53EF2E0B71D90096BC2A /* ViewModel */ = {
			isa = PBXGroup;
			children = (
				E21F53F02E0B71EA0096BC2A /* WalletViewModel.swift */,
			);
			path = ViewModel;
			sourceTree = "<group>";
		};
		E21F53F42E0B7CE00096BC2A /* MyFavourite */ = {
			isa = PBXGroup;
			children = (
				E21F53F62E0B7CFE0096BC2A /* ViewModel */,
				E21F53F52E0B7CF80096BC2A /* Views */,
			);
			path = MyFavourite;
			sourceTree = "<group>";
		};
		E21F53F52E0B7CF80096BC2A /* Views */ = {
			isa = PBXGroup;
			children = (
				E21F53F72E0B7D110096BC2A /* MyFavouritesView.swift */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		E21F53F62E0B7CFE0096BC2A /* ViewModel */ = {
			isa = PBXGroup;
			children = (
				E21F53F92E0B7E170096BC2A /* MyFavViewModel.swift */,
			);
			path = ViewModel;
			sourceTree = "<group>";
		};
		E21F53FB2E0B8F0D0096BC2A /* MyAddresses */ = {
			isa = PBXGroup;
			children = (
				E21F53FC2E0B8F190096BC2A /* Views */,
				E21F53FF2E0B8F630096BC2A /* MyAddressesViewModel.swift */,
			);
			path = MyAddresses;
			sourceTree = "<group>";
		};
		E21F53FC2E0B8F190096BC2A /* Views */ = {
			isa = PBXGroup;
			children = (
				E21F53FD2E0B8F2E0096BC2A /* MyAddressesView.swift */,
				E21F54012E0B97510096BC2A /* AddressItem.swift */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		E21F54032E0BB6360096BC2A /* Refer */ = {
			isa = PBXGroup;
			children = (
				E21F54042E0BB63D0096BC2A /* Views */,
			);
			path = Refer;
			sourceTree = "<group>";
		};
		E21F54042E0BB63D0096BC2A /* Views */ = {
			isa = PBXGroup;
			children = (
				E21F54052E0BB64C0096BC2A /* ReferView.swift */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		E25C5B482E3B6DF700DA87DF /* NewSubCategories */ = {
			isa = PBXGroup;
			children = (
				E25C5B4A2E3B6E0C00DA87DF /* ViewModel */,
				E25C5B492E3B6E0600DA87DF /* Views */,
			);
			path = NewSubCategories;
			sourceTree = "<group>";
		};
		E25C5B492E3B6E0600DA87DF /* Views */ = {
			isa = PBXGroup;
			children = (
				E25C5B4B2E3B6E2300DA87DF /* NewSubCategoriesView.swift */,
				E25C5B4D2E3B6E4C00DA87DF /* NewSubCategoriesTapSelectionView.swift */,
				E29542442E48BFEA0002622C /* ProductSquareItemView.swift */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		E25C5B4A2E3B6E0C00DA87DF /* ViewModel */ = {
			isa = PBXGroup;
			children = (
				E25C5B4F2E3B6E8D00DA87DF /* NewSubCategoriesViewModel.swift */,
			);
			path = ViewModel;
			sourceTree = "<group>";
		};
		E2E5B0762E27EDB700CA6144 /* SubCategories */ = {
			isa = PBXGroup;
			children = (
				E2E5B0782E27EDC600CA6144 /* ViewModel */,
				E2E5B0772E27EDC100CA6144 /* Views */,
			);
			path = SubCategories;
			sourceTree = "<group>";
		};
		E2E5B0772E27EDC100CA6144 /* Views */ = {
			isa = PBXGroup;
			children = (
				E2E5B07B2E27EE2900CA6144 /* SubCategoriesView.swift */,
				E2E5B0812E28E71000CA6144 /* SubCategoriesTapSelectionView.swift */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		E2E5B0782E27EDC600CA6144 /* ViewModel */ = {
			isa = PBXGroup;
			children = (
				E2E5B0792E27EDE600CA6144 /* SubCategoriesViewModel.swift */,
			);
			path = ViewModel;
			sourceTree = "<group>";
		};
		E4E25859B4CD0A1D369B7165 /* Pods */ = {
			isa = PBXGroup;
			children = (
				6395572ACF2F73DAD7E168CF /* Pods-TopCustomer.debug.xcconfig */,
				45F95AE511E3881314FA4AA5 /* Pods-TopCustomer.devrelease.xcconfig */,
				9929B054DF35A09609A5E64A /* Pods-TopCustomer.staging.xcconfig */,
				8C2B88409C3FEA362CAC03EA /* Pods-TopCustomer.release.xcconfig */,
				36CCE020292AFB9CF22A3724 /* Pods-TopCustomer.releasestaging.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
		F90B4C8C20329C45006ADAF6 = {
			isa = PBXGroup;
			children = (
				F90B4C9720329C45006ADAF6 /* TopCustomer */,
				8BD0310E2D3F91100040A590 /* ContentImageExtentionPush */,
				F90B4C9620329C45006ADAF6 /* Products */,
				E4E25859B4CD0A1D369B7165 /* Pods */,
				03850A438F42A45BE95361E0 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		F90B4C9620329C45006ADAF6 /* Products */ = {
			isa = PBXGroup;
			children = (
				F90B4C9520329C45006ADAF6 /* TopCustomer.app */,
				7352DBAA2CFFC23200320EDD /* ContentImageExtentionPush.appex */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		F90B4C9720329C45006ADAF6 /* TopCustomer */ = {
			isa = PBXGroup;
			children = (
				734031272C7DC054009F6BF5 /* TabbyPayment */,
				BE6621A62840B412004D0D0D /* TopCustomer.entitlements */,
				BEDD1C892A0243E0007D4E4B /* Swaggers */,
				BE8D82F427D6063100A11D15 /* Localisations */,
				BE8D82EB27D5E8F200A11D15 /* Custom Classes */,
				BEB2C56F27CCC57E00C5FBCD /* GoogleService-Info.plist */,
				F9EDD441203C2F2D008FA83F /* Category */,
				F9A453A2203AA74A0065C9FD /* VIP */,
				F9A4539D203AA7110065C9FD /* Constant */,
				F90B4CC62032A7C1006ADAF6 /* Application */,
				F90B4CC92032A7C1006ADAF6 /* Library */,
				F90B4CC82032A7C1006ADAF6 /* Models */,
				F90B4CCA2032A7C1006ADAF6 /* Vendors */,
				F9A453DB203AA8220065C9FD /* StoryBoard */,
				F90B4CCB2032A7C1006ADAF6 /* Resources */,
				F90B4CA420329C45006ADAF6 /* Info.plist */,
			);
			path = TopCustomer;
			sourceTree = "<group>";
		};
		F90B4CC62032A7C1006ADAF6 /* Application */ = {
			isa = PBXGroup;
			children = (
				F90B4CDB2032AAB8006ADAF6 /* AppDelegate */,
				F90B4CCC2032A7EA006ADAF6 /* Basecode-Bridging-Header.h */,
			);
			path = Application;
			sourceTree = "<group>";
		};
		F90B4CC82032A7C1006ADAF6 /* Models */ = {
			isa = PBXGroup;
			children = (
				BEAF3DCB288BDA09006DE167 /* Google */,
				BEAF3DC5288BDA08006DE167 /* GoogleDistanceMetrix */,
				F952382A2047BA3400BC0D49 /* RequestParameter.swift */,
				BE9655FF280D389A00DB6E83 /* User.swift */,
			);
			path = Models;
			sourceTree = "<group>";
		};
		F90B4CC92032A7C1006ADAF6 /* Library */ = {
			isa = PBXGroup;
			children = (
				BEAFCB9C2995265900276C1D /* FloatRatingView */,
				BEE64249297581CB00211571 /* ImageScrollView */,
				BEB5BE2728B773690075CF42 /* QTYButtons_old */,
				BE93F86427EDE30E0019120C /* QTYButtons */,
				BE90223727E3454800F9ACE4 /* BottomPopupController */,
				81A484A127DF678F00528766 /* FSPagerView */,
				************************ /* OTPFieldView */,
				BE9A1D1427D641CA0072BCAC /* CountryPickerViewSwift */,
				F90B4CDF2032E116006ADAF6 /* APIManager */,
				BEE94348297ACC4700CF3EE6 /* FirebaseDeeplinkingHelper.swift */,
			);
			path = Library;
			sourceTree = "<group>";
		};
		F90B4CCA2032A7C1006ADAF6 /* Vendors */ = {
			isa = PBXGroup;
			children = (
				BEBF066C27D7844600C3AF62 /* Language */,
				F96C2D2C20380B1A00F8CBC7 /* Validator.swift */,
			);
			path = Vendors;
			sourceTree = "<group>";
		};
		F90B4CCB2032A7C1006ADAF6 /* Resources */ = {
			isa = PBXGroup;
			children = (
				BE8CBF6428EC393800A3BB7A /* Material_video.mp4 */,
				BE8D82F027D6045000A11D15 /* Fonts */,
				F90B4CD72032A87B006ADAF6 /* Documents */,
				F90B4C9F20329C45006ADAF6 /* Assets.xcassets */,
			);
			path = Resources;
			sourceTree = "<group>";
		};
		F90B4CD72032A87B006ADAF6 /* Documents */ = {
			isa = PBXGroup;
			children = (
				F90B4CD82032A87B006ADAF6 /* Structure_Guide.rtf */,
			);
			path = Documents;
			sourceTree = "<group>";
		};
		F90B4CDB2032AAB8006ADAF6 /* AppDelegate */ = {
			isa = PBXGroup;
			children = (
				F90B4CDC2032AAB8006ADAF6 /* AppDelegate.swift */,
				F948AEC9203E865500F5A852 /* AppdelegateNotification.swift */,
				738B91FD2CA53BCC00B406CB /* AppDelegate+AppsFlyer.swift */,
				738B91FF2CA58F5500B406CB /* AppDelegate+AppsFlyerDeepLinkDelegate.swift */,
			);
			path = AppDelegate;
			sourceTree = "<group>";
		};
		F90B4CDF2032E116006ADAF6 /* APIManager */ = {
			isa = PBXGroup;
			children = (
				F90B4CE02032F4A6006ADAF6 /* SOAPI.swift */,
				F90B4CE12032F4A6006ADAF6 /* SOService.swift */,
			);
			path = APIManager;
			sourceTree = "<group>";
		};
		F97A999F2110226E007C45F5 /* More */ = {
			isa = PBXGroup;
			children = (
				F97A9A1F211031C0007C45F5 /* CGFloatExtensions.swift */,
				F97A9A112110274A007C45F5 /* EZSwiftFunctions.swift */,
				F97A9A0D21102600007C45F5 /* CGRectExtensions.swift */,
			);
			path = More;
			sourceTree = "<group>";
		};
		F97A99B82110226F007C45F5 /* UIKit */ = {
			isa = PBXGroup;
			children = (
				73E190072A9D4CFA0010AE40 /* UICollectionView+.swift */,
				BEABE2732865F2AD0033AB1D /* Date+Extension.swift */,
				81A484BE27DF6F4A00528766 /* UICollectionViewCell.swift */,
				81A484BD27DF6F4A00528766 /* UITableViewExtensions.swift */,
				F97A9A1B211031A6007C45F5 /* UIViewControllerExtensions.swift */,
				F97A9A1C211031A6007C45F5 /* UIViewExtensions.swift */,
				F97A9A1521102E18007C45F5 /* UIApplicationExtensions.swift */,
				BE8D82EE27D5EA7C00A11D15 /* UIColor+Extension.swift */,
				73B961BD2A8C595900B92293 /* ShadowView.swift */,
				73190A5A2A8C786D000C31D7 /* ShadowViewOnly.swift */,
				73A941AD2B331C2100F1C8A3 /* UICollectionViewCell+.swift */,
				73248D992C10A24100C2E101 /* UITableViewCell+.swift */,
			);
			path = UIKit;
			sourceTree = "<group>";
		};
		F97A99C22110226F007C45F5 /* Foundation */ = {
			isa = PBXGroup;
			children = (
				F97A99C62110226F007C45F5 /* UserDefaultsExtension.swift */,
			);
			path = Foundation;
			sourceTree = "<group>";
		};
		F97A99C72110226F007C45F5 /* StandardLib */ = {
			isa = PBXGroup;
			children = (
				F97A9A1321102CEF007C45F5 /* StringExtensions.swift */,
			);
			path = StandardLib;
			sourceTree = "<group>";
		};
		F9A4539D203AA7110065C9FD /* Constant */ = {
			isa = PBXGroup;
			children = (
				BEAAEC7C2850825F005A1D49 /* SocketIOManager.swift */,
				BE9A1D2927D64F9A0072BCAC /* AppSingleton.swift */,
				BE9A1D2727D64EB70072BCAC /* AppSuportedLanguages.swift */,
				F995BDC0204D1277005E5279 /* KeyMessages.swift */,
				F9A4539E203AA7200065C9FD /* Constant.swift */,
				73342DBD2BF3C0A3005FC06E /* Notification+.swift */,
			);
			path = Constant;
			sourceTree = "<group>";
		};
		F9A453A2203AA74A0065C9FD /* VIP */ = {
			isa = PBXGroup;
			children = (
				7390FF7D2D75045800C813EC /* BeqalaApp */,
				73E595AB2D747C4C0043EE66 /* FloatingButton */,
				738AD26B2D6E50CC00419CCA /* ViewInvoice */,
				73778ABE2CF8854400D99032 /* PaymentsWithURL */,
				732320142C3B2A1900DE37B6 /* InviteFriends */,
				7367EE1F2AD7649E0042BACF /* Rewards */,
				73757E5F2A8AFB6E0052D068 /* RecommendationProducts */,
				BE0EED3929DB01F700CAE929 /* CustomPopUp */,
				BECDBAA029D56A37008E8EAC /* VersionUpdatePopup */,
				BEE4FF5E29B0BF210010A24E /* Favorite */,
				BEE21A62299503770090C9BA /* Rating */,
				BEE6424C2975823000211571 /* FullScreenImage */,
				BEC3587629642F6D0005040C /* AdvertisementPopup */,
				BEF4D1D72942108900EA53FF /* CancelReason */,
				BEB3CADB2918DBF2001C400E /* NewHome */,
				BE7A8397291132D40078FD2F /* BannerInfo */,
				BEAEE5CB28BE2E210098946C /* Reference */,
				BEA7620A2886ACD900A890E4 /* TrackOrder */,
				BE9B3A5327FF49C200EF3615 /* AddCard */,
				BE9B3A4C27FF21A500EF3615 /* ChoosePayment */,
				BE9B3A4127FEF82E00EF3615 /* Payments */,
				BE94FAE127FC5CFF00B131DD /* ScheduledOrderDetail */,
				BE94FADA27FC571B00B131DD /* Invoice */,
				BEC2851927FAF69100A998A8 /* AddAddress */,
				BEC2851127FAD23200A998A8 /* FilterSearch */,
				************************ /* FilterSideMenu */,
				************************ /* Help */,
				BEB4FE2027F5CC00006B5BCC /* OrderDetail */,
				BEB4FE1927F5AFB5006B5BCC /* OfferDetails */,
				BEB4FE1227F5A7B8006B5BCC /* ThankYou */,
				************************ /* Cart */,
				************************ /* ScheduledOrders */,
				************************ /* CurrentOrders */,
				************************ /* Contact Us */,
				************************ /* Account */,
				************************ /* OTP Verification */,
				BEBF066527D7753E00C3AF62 /* Settings */,
				BE1B8B5827D9D1F90045FA0E /* Offers */,
				BE1B8B5127D9D1C20045FA0E /* MyOrders */,
				BE1B8B4A27D9D14C0045FA0E /* Home */,
				BE1FF69127D8C18D008291AF /* Tabbar */,
				BEBF067727D79EF400C3AF62 /* StaticPage */,
				BE8D82E427D5DC2E00A11D15 /* Login */,
				BEEAD04C29CD84E9008455E5 /* AnimationTest.swift */,
			);
			path = VIP;
			sourceTree = "<group>";
		};
		F9A453DB203AA8220065C9FD /* StoryBoard */ = {
			isa = PBXGroup;
			children = (
				************************ /* MyCart.storyboard */,
				BEBA109827E9F32C0063E617 /* ProductPopup.storyboard */,
				F90B4C9C20329C45006ADAF6 /* Main.storyboard */,
				F90B4CA120329C45006ADAF6 /* LaunchScreen.storyboard */,
				************************ /* Settings.storyboard */,
				BE1FF68F27D886CB008291AF /* Home.storyboard */,
				BE9B3A4827FEF88400EF3615 /* Payments.storyboard */,
			);
			path = StoryBoard;
			sourceTree = "<group>";
		};
		F9EDD441203C2F2D008FA83F /* Category */ = {
			isa = PBXGroup;
			children = (
				BEC0146527D61E9D00509E26 /* UIStoryboard+Helper.swift */,
				F97A99C22110226F007C45F5 /* Foundation */,
				F97A999F2110226E007C45F5 /* More */,
				F97A99C72110226F007C45F5 /* StandardLib */,
				F97A99B82110226F007C45F5 /* UIKit */,
			);
			path = Category;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		7352DBA92CFFC23200320EDD /* ContentImageExtentionPush */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 7352DBB32CFFC23200320EDD /* Build configuration list for PBXNativeTarget "ContentImageExtentionPush" */;
			buildPhases = (
				7352DBA62CFFC23200320EDD /* Sources */,
				7352DBA72CFFC23200320EDD /* Frameworks */,
				7352DBA82CFFC23200320EDD /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = ContentImageExtentionPush;
			productName = ContentImageExtentionPushNotification;
			productReference = 7352DBAA2CFFC23200320EDD /* ContentImageExtentionPush.appex */;
			productType = "com.apple.product-type.app-extension";
		};
		F90B4C9420329C45006ADAF6 /* TopCustomer */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = F90B4CBD20329C45006ADAF6 /* Build configuration list for PBXNativeTarget "TopCustomer" */;
			buildPhases = (
				E0B307EC701231360D376009 /* [CP] Check Pods Manifest.lock */,
				73C1A8EE2CFFA05B00CCF584 /* Embed Foundation Extensions */,
				F90B4C9120329C45006ADAF6 /* Sources */,
				F90B4C9220329C45006ADAF6 /* Frameworks */,
				F90B4C9320329C45006ADAF6 /* Resources */,
				BEB2C57127CCC77900C5FBCD /* ShellScript */,
				AA95F8C0572B26B6FB53909F /* [CP] Embed Pods Frameworks */,
				CA97A15BA520EDD53ED445EC /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
				7352DBB02CFFC23200320EDD /* PBXTargetDependency */,
			);
			name = TopCustomer;
			packageProductDependencies = (
				7397710F2C7C78BF00FA8476 /* Tabby */,
				73676ABB2CE13FDE00BFF236 /* FacebookAEM */,
				73676ABD2CE13FDE00BFF236 /* FacebookBasics */,
				73676ABF2CE13FDE00BFF236 /* FacebookCore */,
				73676AC12CE13FDE00BFF236 /* FacebookLogin */,
				73676AC32CE13FDE00BFF236 /* FacebookShare */,
			);
			productName = Basecode;
			productReference = F90B4C9520329C45006ADAF6 /* TopCustomer.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		F90B4C8D20329C45006ADAF6 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 1610;
				LastUpgradeCheck = 1320;
				ORGANIZATIONNAME = SOTSYS203;
				TargetAttributes = {
					7352DBA92CFFC23200320EDD = {
						CreatedOnToolsVersion = 16.1;
					};
					F90B4C9420329C45006ADAF6 = {
						CreatedOnToolsVersion = 9.1;
						LastSwiftMigration = 1020;
					};
				};
			};
			buildConfigurationList = F90B4C9020329C45006ADAF6 /* Build configuration list for PBXProject "TopCustomer" */;
			compatibilityVersion = "Xcode 8.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
				ar,
			);
			mainGroup = F90B4C8C20329C45006ADAF6;
			packageReferences = (
				7397710E2C7C78BF00FA8476 /* XCRemoteSwiftPackageReference "tabby-ios-sdk" */,
				73676ABA2CE13FDE00BFF236 /* XCRemoteSwiftPackageReference "facebook-ios-sdk" */,
			);
			productRefGroup = F90B4C9620329C45006ADAF6 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				F90B4C9420329C45006ADAF6 /* TopCustomer */,
				7352DBA92CFFC23200320EDD /* ContentImageExtentionPush */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		7352DBA82CFFC23200320EDD /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F90B4C9320329C45006ADAF6 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				739EBB112B065D4000E89E8B /* data4.json in Resources */,
				7372D97C2B0E064600A9506D /* middle2.json in Resources */,
				73C765DD2C510ABE0080282D /* invite_friends_gift_en.json in Resources */,
				73A6F6D82B861D82001DC9EA /* BundelsListTableViewCell.xib in Resources */,
				738716D92BFFC9A200B1B8F6 /* ProductsOfBundleTVCell.xib in Resources */,
				BE647A8428E833B50051F5FE /* TopCustomer.entitlements in Resources */,
				BE93F88D27EDE3E50019120C /* MyCart.storyboard in Resources */,
				73A9D3062B441ADD00A5CC72 /* AdvertisingCollectionViewCell.xib in Resources */,
				736CE1AC2C519111006ECEBC /* add_to_cart_animation_ar.json in Resources */,
				7372D9772B0E064600A9506D /* main5.json in Resources */,
				73E190062A9D32D80010AE40 /* MayAlsoLikeTVCell.xib in Resources */,
				F90B4CA320329C45006ADAF6 /* LaunchScreen.storyboard in Resources */,
				7325FBB32AFE620E00690253 /* coins_shape.json in Resources */,
				BE1FF69027D886CB008291AF /* Home.storyboard in Resources */,
				73566ACF2C43FC0000D0AA8E /* loading_shape.json in Resources */,
				730930482B58072700264FA3 /* best_offers.json in Resources */,
				BEC97A0B29E030740049E7C6 /* done_icon.json in Resources */,
				BEA9574428DDDA8A00DCFFF9 /* LoaderAnimation.json in Resources */,
				BE9B3A4927FEF88400EF3615 /* Payments.storyboard in Resources */,
				BE8D82F727D6069000A11D15 /* Localizable.strings in Resources */,
				BEC2E9DA29CA27AB00B837F8 /* CheckoutShiftsCell.xib in Resources */,
				BE13CB8027D6302400607C2D /* LoewNextArabic-Bold.ttf in Resources */,
				73778AC22CF8859D00D99032 /* PaymentsByUrlVC.xib in Resources */,
				7372D9752B0E064600A9506D /* middle1.json in Resources */,
				81A484B827DF69AE00528766 /* CategoriesCollectionViewCell.xib in Resources */,
				73DCE10C2B004A0400AA4AAC /* LastOfferCollectionViewCell.xib in Resources */,
				73E185F62A8AC629008FC178 /* segoeUI.ttf in Resources */,
				BEBA109927E9F32C0063E617 /* ProductPopup.storyboard in Resources */,
				BE13CB8227D6357E00607C2D /* LoewNextArabic-Medium.ttf in Resources */,
				BE1BC0B627FD71DD00C9E42E /* MyCartCheckoutTableViewCell.xib in Resources */,
				BE973F0527E88CC300C09458 /* LoewNextArabic-Heavy.ttf in Resources */,
				BE93F86927EDE30E0019120C /* QTYButton.xib in Resources */,
				BE93F88A27EDE3B20019120C /* MyCartTableViewCell.xib in Resources */,
				7325FBB72B00444000690253 /* LastOfferTableViewCell.xib in Resources */,
				BE9A1D1D27D641CA0072BCAC /* CountryPicker.bundle in Resources */,
				7372D9762B0E064600A9506D /* main2.json in Resources */,
				7335B9072AFB0E4B00C877FF /* RewardDetailsCell.xib in Resources */,
				BE6C5EAA27DA123D00F6C188 /* LoewNextArabic-ExtraBold.ttf in Resources */,
				7372D97E2B0E064600A9506D /* middle3.json in Resources */,
				73C765DF2C510AD30080282D /* invite_friends_gift.json in Resources */,
				7382DE5B2CB7F04C0060C4B5 /* MainSliderCVCell.xib in Resources */,
				7372D97A2B0E064600A9506D /* main6.json in Resources */,
				7372D97D2B0E064600A9506D /* main1.json in Resources */,
				732E033A2C2C4E86008B6088 /* ChooseDeliveryDateCVC.xib in Resources */,
				73F9CE4A2C553EAA00635DDC /* ShiftsBySelectDateCell.xib in Resources */,
				7372D9742B0E064600A9506D /* main3.json in Resources */,
				738B81BA2C11A4A90069DC6E /* CartProductsOfBundleTVCell.xib in Resources */,
				8117DF7227E9990B00004F69 /* HistoryOrdersTableViewCell.xib in Resources */,
				73757E642A8AFBB20052D068 /* RecommendationProductCVC.xib in Resources */,
				8117DF7D27E9B07100004F69 /* HistoryOrderDetailsTableViewCell.xib in Resources */,
				BEB5BE2C28B773690075CF42 /* QTYButtonOLD.xib in Resources */,
				F90B4CA020329C45006ADAF6 /* Assets.xcassets in Resources */,
				81A484AE27DF678F00528766 /* FSPagerViewCell.xib in Resources */,
				F90B4C9E20329C45006ADAF6 /* Main.storyboard in Resources */,
				73E595AE2D747C520043EE66 /* FloatingButtonView.xib in Resources */,
				BEADE940286D73B000556D1B /* ProductsCollectionViewCellSmall.xib in Resources */,
				735499202ADAB3A80007E26E /* PointsHostoryTVCell.xib in Resources */,
				F90B4CD92032A87B006ADAF6 /* Structure_Guide.rtf in Resources */,
				733E02022C04DF40000D972D /* SuperCollectionViewCell.xib in Resources */,
				73A6F6DC2B861DAF001DC9EA /* BundelsListCollectionViewCell.xib in Resources */,
				81A484BC27DF6BF600528766 /* ProductsCollectionViewCell.xib in Resources */,
				7372D97B2B0E064600A9506D /* middle5.json in Resources */,
				7372D9782B0E064600A9506D /* main4.json in Resources */,
				739EBB0F2B065D4000E89E8B /* data2.json in Resources */,
				73EBCF012CD010FD00EB14C4 /* AdvertisementCVCell.xib in Resources */,
				BE8CBF6728EC3C3400A3BB7A /* Material_video.mp4 in Resources */,
				73F2D1DF2C8ECDE9004A9738 /* QuantitiesDiscountCVCell.xib in Resources */,
				BEA85B292876E26700F4754E /* CategoriesCollectionViewCellBig.xib in Resources */,
				739EBB102B065D4000E89E8B /* data1.json in Resources */,
				BEB2C57027CCC57E00C5FBCD /* GoogleService-Info.plist in Resources */,
				73566AD12C4404A300D0AA8E /* add_to_cart_animation.json in Resources */,
				BE933FA127D77247005B27A4 /* Settings.storyboard in Resources */,
				739EBB122B065D4000E89E8B /* data3.json in Resources */,
				7372D9792B0E064600A9506D /* middle4.json in Resources */,
				739EBB0E2B065D4000E89E8B /* data5.json in Resources */,
				738B81B32C1196850069DC6E /* BundleProductsView.xib in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		AA95F8C0572B26B6FB53909F /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-TopCustomer/Pods-TopCustomer-frameworks.sh",
				"${BUILT_PRODUCTS_DIR}/AACarousel/AACarousel.framework",
				"${BUILT_PRODUCTS_DIR}/ActionSheetPicker-3.0/ActionSheetPicker_3_0.framework",
				"${BUILT_PRODUCTS_DIR}/Alamofire/Alamofire.framework",
				"${BUILT_PRODUCTS_DIR}/AlamofireImage/AlamofireImage.framework",
				"${BUILT_PRODUCTS_DIR}/Branch/Branch.framework",
				"${BUILT_PRODUCTS_DIR}/CocoaLumberjack/CocoaLumberjack.framework",
				"${BUILT_PRODUCTS_DIR}/FirebaseCore/FirebaseCore.framework",
				"${BUILT_PRODUCTS_DIR}/FirebaseCoreExtension/FirebaseCoreExtension.framework",
				"${BUILT_PRODUCTS_DIR}/FirebaseCoreInternal/FirebaseCoreInternal.framework",
				"${BUILT_PRODUCTS_DIR}/FirebaseCrashlytics/FirebaseCrashlytics.framework",
				"${BUILT_PRODUCTS_DIR}/FirebaseDynamicLinks/FirebaseDynamicLinks.framework",
				"${BUILT_PRODUCTS_DIR}/FirebaseInstallations/FirebaseInstallations.framework",
				"${BUILT_PRODUCTS_DIR}/FirebaseMessaging/FirebaseMessaging.framework",
				"${BUILT_PRODUCTS_DIR}/FirebaseRemoteConfigInterop/FirebaseRemoteConfigInterop.framework",
				"${BUILT_PRODUCTS_DIR}/FirebaseSessions/FirebaseSessions.framework",
				"${BUILT_PRODUCTS_DIR}/GoogleDataTransport/GoogleDataTransport.framework",
				"${BUILT_PRODUCTS_DIR}/GoogleUtilities/GoogleUtilities.framework",
				"${BUILT_PRODUCTS_DIR}/IQKeyboardManagerSwift/IQKeyboardManagerSwift.framework",
				"${BUILT_PRODUCTS_DIR}/ImageSlideshow/ImageSlideshow.framework",
				"${BUILT_PRODUCTS_DIR}/Kingfisher/Kingfisher.framework",
				"${BUILT_PRODUCTS_DIR}/Mixpanel-swift/Mixpanel.framework",
				"${BUILT_PRODUCTS_DIR}/PromisesObjC/FBLPromises.framework",
				"${BUILT_PRODUCTS_DIR}/PromisesSwift/Promises.framework",
				"${BUILT_PRODUCTS_DIR}/ReachabilitySwift/Reachability.framework",
				"${BUILT_PRODUCTS_DIR}/STTabbar/STTabbar.framework",
				"${BUILT_PRODUCTS_DIR}/SVGKit/SVGKit.framework",
				"${BUILT_PRODUCTS_DIR}/Socket.IO-Client-Swift/SocketIO.framework",
				"${BUILT_PRODUCTS_DIR}/Starscream/Starscream.framework",
				"${BUILT_PRODUCTS_DIR}/SwiftyJSON/SwiftyJSON.framework",
				"${BUILT_PRODUCTS_DIR}/TikTokBusinessSDK/TikTokBusinessSDK.framework",
				"${BUILT_PRODUCTS_DIR}/Toast-Swift/Toast_Swift.framework",
				"${BUILT_PRODUCTS_DIR}/lottie-ios/Lottie.framework",
				"${BUILT_PRODUCTS_DIR}/nanopb/nanopb.framework",
				"${PODS_XCFRAMEWORKS_BUILD_DIR}/PayTabsSDK/PaymentSDK.framework/PaymentSDK",
			);
			name = "[CP] Embed Pods Frameworks";
			outputPaths = (
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/AACarousel.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/ActionSheetPicker_3_0.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/Alamofire.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/AlamofireImage.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/Branch.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/CocoaLumberjack.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FirebaseCore.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FirebaseCoreExtension.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FirebaseCoreInternal.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FirebaseCrashlytics.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FirebaseDynamicLinks.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FirebaseInstallations.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FirebaseMessaging.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FirebaseRemoteConfigInterop.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FirebaseSessions.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/GoogleDataTransport.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/GoogleUtilities.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/IQKeyboardManagerSwift.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/ImageSlideshow.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/Kingfisher.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/Mixpanel.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FBLPromises.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/Promises.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/Reachability.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/STTabbar.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/SVGKit.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/SocketIO.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/Starscream.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/SwiftyJSON.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/TikTokBusinessSDK.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/Toast_Swift.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/Lottie.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/nanopb.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/PaymentSDK.framework",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-TopCustomer/Pods-TopCustomer-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		BEB2C57127CCC77900C5FBCD /* ShellScript */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 12;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${DWARF_DSYM_FOLDER_PATH}/${DWARF_DSYM_FILE_NAME}/Contents/Resources/DWARF/${TARGET_NAME}",
				"$(SRCROOT)/$(BUILT_PRODUCTS_DIR)/$(INFOPLIST_PATH)",
			);
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/FirebaseCrashlytics/run\"\n";
		};
		CA97A15BA520EDD53ED445EC /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-TopCustomer/Pods-TopCustomer-resources.sh",
				"${PODS_CONFIGURATION_BUILD_DIR}/AppsFlyerFramework/AppsFlyerLib_Privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/GoogleMaps/GoogleMapsResources.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/GooglePlaces/GooglePlacesResources.bundle",
			);
			name = "[CP] Copy Pods Resources";
			outputPaths = (
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/AppsFlyerLib_Privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/GoogleMapsResources.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/GooglePlacesResources.bundle",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-TopCustomer/Pods-TopCustomer-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		E0B307EC701231360D376009 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-TopCustomer-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		7352DBA62CFFC23200320EDD /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				8BD0310F2D3F91100040A590 /* NotificationService.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F90B4C9120329C45006ADAF6 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				BEAF3DEE288BDA09006DE167 /* GoogleDirectionDetailsGeocodedWaypoint.swift in Sources */,
				732E03352C2C4A6E008B6088 /* ChooseDeliveryDateVC.swift in Sources */,
				73DEBD202B1626130026EB10 /* Font+.swift in Sources */,
				E2E5B0752E252CD000CA6144 /* OrderDetailsPopupView.swift in Sources */,
				BE9763A427E2085E00F29744 /* ScheduledOrdersInteractor.swift in Sources */,
				81A484B127DF678F00528766 /* FSPageViewLayout.swift in Sources */,
				BEC2851F27FAF6A200A998A8 /* AddAddressInteractor.swift in Sources */,
				BE8D82EF27D5EA7C00A11D15 /* UIColor+Extension.swift in Sources */,
				BE9B3A4527FEF83D00EF3615 /* PaymentsPresenter.swift in Sources */,
				E20B057A2E32921800E635FB /* NewProductsView.swift in Sources */,
				BEDD1DC32A0243E0007D4E4B /* UserResponseFields.swift in Sources */,
				BEDD1DB22A0243E0007D4E4B /* GetUserCardDetailResponseFields.swift in Sources */,
				BE9B3A4727FEF83D00EF3615 /* PaymentsInteractor.swift in Sources */,
				E21F53EE2E09CF260096BC2A /* WalletView.swift in Sources */,
				BEF4D1DB2942109D00EA53FF /* CancelReasonPresenter.swift in Sources */,
				BEDD1D772A0243E0007D4E4B /* OrderedProductDetailResponseFields.swift in Sources */,
				BEDD1D632A0243E0007D4E4B /* BannerResponse.swift in Sources */,
				BEDD1DCE2A0243E0007D4E4B /* OfferResponseFields.swift in Sources */,
				BE93F88827EDE3B20019120C /* CheckoutViewController.swift in Sources */,
				E2914FCF2E1BC9C50084CEC1 /* TabRouter.swift in Sources */,
				BEDD1D3F2A0243E0007D4E4B /* DriverAPI.swift in Sources */,
				BEDD1D8A2A0243E0007D4E4B /* ListResponse.swift in Sources */,
				739A398B2D79123200C4847A /* CartViewModel.swift in Sources */,
				73A6F6D72B861D82001DC9EA /* BundelsListTableViewCell.swift in Sources */,
				BEDD1D752A0243E0007D4E4B /* ProductInfoResponseFields.swift in Sources */,
				73ABBA5A2CE291F700166D7E /* CityModel.swift in Sources */,
				BEDD1D852A0243E0007D4E4B /* NotificationListResponseFields.swift in Sources */,
				BEDD1D372A0243E0007D4E4B /* UserAPI.swift in Sources */,
				BEBF067D27D79F0B00C3AF62 /* StaticPageInteractor.swift in Sources */,
				E20B05732E32635B00E635FB /* NewMainHomeModel.swift in Sources */,
				BEDD1DC12A0243E0007D4E4B /* ProductDetailsResponseFields.swift in Sources */,
				BE9A1D2A27D64F9A0072BCAC /* AppSingleton.swift in Sources */,
				73381AE02D7CEFF90059A27C /* ProductDetailsViewModel.swift in Sources */,
				738B92002CA58F5500B406CB /* AppDelegate+AppsFlyerDeepLinkDelegate.swift in Sources */,
				BE9B3A5727FF49D000EF3615 /* AddCardPresenter.swift in Sources */,
				BE90223627E344D900F9ACE4 /* SelectLocationPresenter.swift in Sources */,
				BEDD1D532A0243E0007D4E4B /* ProductSuggestionResponseFields.swift in Sources */,
				7330075A2D5F7727002474AD /* TikTokEvents.swift in Sources */,
				7372ECC22D82D37300818CD3 /* AlertType.swift in Sources */,
				7372ECC32D82D37300818CD3 /* ALertView.swift in Sources */,
				735499222ADAC53B0007E26E /* PointsHostoryModel.swift in Sources */,
				BEDD1DD02A0243E0007D4E4B /* CommonFields.swift in Sources */,
				73D172782D8256AF00E7EF01 /* TabCategoriesSelectionView.swift in Sources */,
				BEDD1DAB2A0243E0007D4E4B /* HomeListingResponse.swift in Sources */,
				BEAF3DDD288BDA09006DE167 /* GoogleDistanceMetrixDuration.swift in Sources */,
				BEDD1DAC2A0243E0007D4E4B /* AddressResponse.swift in Sources */,
				BEAF3DE9288BDA09006DE167 /* GoogleDirectionDetailsStartLocation.swift in Sources */,
				BEAEE5CD28BE2E210098946C /* ReferenceVC.swift in Sources */,
				BEAF3DE4288BDA09006DE167 /* GoogleDirectionDetailsStep.swift in Sources */,
				BEDD1D882A0243E0007D4E4B /* OfferListResponseFields.swift in Sources */,
				BEDD1DA12A0243E0007D4E4B /* DriverOrderStatusResponse.swift in Sources */,
				BEAF3DE2288BDA09006DE167 /* GoogleDirectionDetailsEndLocation.swift in Sources */,
				BED0E5B229B73EDC00CF14FA /* HomeBannerCell.swift in Sources */,
				73D0AA052B98A7A0006B3BCC /* MosquesResponseFields.swift in Sources */,
				BE9B3A4627FEF83D00EF3615 /* PaymentsViewController.swift in Sources */,
				8117DF6C27E998A700004F69 /* HistoryOrdersPresenter.swift in Sources */,
				BEDD1D732A0243E0007D4E4B /* DriverCompletedOrderDeatailsResponse.swift in Sources */,
				BEB3CAE12918DC1B001C400E /* NewHomeInteractor.swift in Sources */,
				BE9A1D1B27D641CA0072BCAC /* CountrySelectView.swift in Sources */,
				732D67372AB20C8400366120 /* CategoriesTableViewCell.swift in Sources */,
				BE04A57027F5FF8700EE26EA /* HelpInteractor.swift in Sources */,
				73B6240C2D75B4BB0064D21A /* SelectDeliveryTypeViewModel.swift in Sources */,
				E20B05702E32622300E635FB /* NewMainHomeView.swift in Sources */,
				BE90222F27E344CA00F9ACE4 /* SelectLanguageViewController.swift in Sources */,
				E21F54022E0B97510096BC2A /* AddressItem.swift in Sources */,
				BEDD1D702A0243E0007D4E4B /* OauthSignupBody.swift in Sources */,
				E29542432E48BFA90002622C /* LoadingSquareImageWithUrlView.swift in Sources */,
				BEBF066927D7769D00C3AF62 /* SettingsPresenter.swift in Sources */,
				BEDD1D802A0243E0007D4E4B /* UserDetailListingResponseFields.swift in Sources */,
				BE93F88627EDE3B20019120C /* MyCartPresenter.swift in Sources */,
				BE90222E27E344CA00F9ACE4 /* SelectLanguageInteractor.swift in Sources */,
				BE8D82E927D5E54B00A11D15 /* LoginViewController.swift in Sources */,
				BEE94349297ACC4700CF3EE6 /* FirebaseDeeplinkingHelper.swift in Sources */,
				BE0EED3B29DB020B00CAE929 /* CustomPopupAlertViewController.swift in Sources */,
				BE94FAE727FC5D7F00B131DD /* ScheduledOrderDetailInteractor.swift in Sources */,
				BEDD1D452A0243E0007D4E4B /* AdvertisementAPI.swift in Sources */,
				BE9A1D2827D64EB70072BCAC /* AppSuportedLanguages.swift in Sources */,
				BEDD1D472A0243E0007D4E4B /* OfferAPI.swift in Sources */,
				BEDD1DC92A0243E0007D4E4B /* OrderDetailResponse.swift in Sources */,
				BE8D82EA27D5E54B00A11D15 /* LoginInteractor.swift in Sources */,
				BE1B8B5D27D9D2080045FA0E /* OffersViewController.swift in Sources */,
				BEDD1D5F2A0243E0007D4E4B /* AdvertisementResponseFields.swift in Sources */,
				73381AE52D7D00410059A27C /* SearchViewModel.swift in Sources */,
				BE9A1D2627D64D630072BCAC /* BaseViewController.swift in Sources */,
				BEDD1DCA2A0243E0007D4E4B /* TransactionResponseFields.swift in Sources */,
				BE90222D27E344CA00F9ACE4 /* SelectLanguagePresenter.swift in Sources */,
				7325619F2C3D7157004AB08D /* UserQRCodeModel.swift in Sources */,
				73F7F03F2D7C91E800E35734 /* CategoriesViewModel.swift in Sources */,
				73B47A922D787564007B8FB9 /* OffersViewModel.swift in Sources */,
				BEA7620F2886ACF200A890E4 /* TrackOrderViewController.swift in Sources */,
				8117DF7827E9AA5C00004F69 /* HistoryOrderDetailsViewController.swift in Sources */,
				BE1FF69327D8C19E008291AF /* MainTabbarViewController.swift in Sources */,
				7367EE212AD764B30042BACF /* YourRewardsVC.swift in Sources */,
				8117DF6E27E998A700004F69 /* HistoryOrdersInteractor.swift in Sources */,
				BE90224527E3454800F9ACE4 /* BottomPopupPresentAnimator.swift in Sources */,
				BE1B8B5727D9D1D10045FA0E /* MyOrdersInteractor.swift in Sources */,
				BEDD1D712A0243E0007D4E4B /* TransactionResponse.swift in Sources */,
				BEDD1D762A0243E0007D4E4B /* CategoryListResponseFields.swift in Sources */,
				BEDD1D562A0243E0007D4E4B /* OauthSocialsigninBody.swift in Sources */,
				E23FC0A12E23D28C006AB6B4 /* OrderDeliveredView.swift in Sources */,
				BEDD1D3E2A0243E0007D4E4B /* CartAPI.swift in Sources */,
				BEAB6E7F28BCD3D100D47F96 /* MaterialLocalizeTextfield.swift in Sources */,
				BEBF067427D7844600C3AF62 /* L102Language.swift in Sources */,
				BEDD1DB32A0243E0007D4E4B /* CreateOrderResponse.swift in Sources */,
				E2E5B0822E28E71000CA6144 /* SubCategoriesTapSelectionView.swift in Sources */,
				BE10DF2328BBFCCB000983A2 /* MaterialLocalizeLable.swift in Sources */,
				BEDD1DA92A0243E0007D4E4B /* UserDetailListingResponse.swift in Sources */,
				BEB3CADF2918DC1B001C400E /* NewHomePresenter.swift in Sources */,
				BEAF3DE8288BDA09006DE167 /* GoogleDirectionDetailsDistance.swift in Sources */,
				F948AECA203E865500F5A852 /* AppdelegateNotification.swift in Sources */,
				BEE21A67299503890090C9BA /* RatingViewController.swift in Sources */,
				************************ /* OTPVerificationInteractor.swift in Sources */,
				BE93F88B27EDE3B20019120C /* MyCartTableViewCell.swift in Sources */,
				F97A9A2C21103FA7007C45F5 /* CGRectExtensions.swift in Sources */,
				BEDD1DD32A0243E0007D4E4B /* MarketingBannerImageResponseFields.swift in Sources */,
				7358CEE12B0F9F05001F3611 /* CoinsLevelsShape.swift in Sources */,
				BEDD1D492A0243E0007D4E4B /* PromocodeAPI.swift in Sources */,
				F97A9A2E21103FAD007C45F5 /* CGFloatExtensions.swift in Sources */,
				BEDD1D3C2A0243E0007D4E4B /* UserCardsAPI.swift in Sources */,
				E20B05752E32643700E635FB /* NewMainHomeViewModel.swift in Sources */,
				BE94FAE527FC5D7F00B131DD /* ScheduledOrderDetailPresenter.swift in Sources */,
				E25C5B502E3B6E8D00DA87DF /* NewSubCategoriesViewModel.swift in Sources */,
				BEDD1D692A0243E0007D4E4B /* DetailFields.swift in Sources */,
				BEDD1D502A0243E0007D4E4B /* SkipRatingResponse.swift in Sources */,
				73D363E92CB6B12700F1D163 /* AppsFlyerEvents.swift in Sources */,
				BEDD1D432A0243E0007D4E4B /* ProductAPI.swift in Sources */,
				BE90223427E344D900F9ACE4 /* SelectLocationViewController.swift in Sources */,
				73778AC12CF8859D00D99032 /* PaymentsByUrlVC.swift in Sources */,
				BEC0146627D61E9D00509E26 /* UIStoryboard+Helper.swift in Sources */,
				73ED99D42BCD0EDD007FBD73 /* MainButtonDesignable.swift in Sources */,
				73E190082A9D4CFA0010AE40 /* UICollectionView+.swift in Sources */,
				BE9763A227E2085E00F29744 /* ScheduledOrdersPresenter.swift in Sources */,
				BEBA109F27E9F3610063E617 /* ProductPopupViewController.swift in Sources */,
				BE90224227E3454800F9ACE4 /* BottomPopupDismissInteractionController.swift in Sources */,
				81A484BB27DF6BF500528766 /* ProductsCollectionViewCell.swift in Sources */,
				BEDD1DD92A0243E0007D4E4B /* JSONEncodingHelper.swift in Sources */,
				BEDD1D3B2A0243E0007D4E4B /* NotificationAPI.swift in Sources */,
				BEDD1DC52A0243E0007D4E4B /* DriverRescheduleOrderResponse.swift in Sources */,
				F97A9A2A21103FA2007C45F5 /* UIViewControllerExtensions.swift in Sources */,
				BEBF067327D7844600C3AF62 /* L012Localizer.swift in Sources */,
				BEDD1DDB2A0243E0007D4E4B /* Configuration.swift in Sources */,
				BE94FADE27FC573E00B131DD /* InvoicePresenter.swift in Sources */,
				73FCE8862ACD6A8A0014DD95 /* UILabel+.swift in Sources */,
				BEE642522975825000211571 /* FullScreenImageViewController.swift in Sources */,
				BE9568EF27F6FBFB002E96C3 /* FilterSideMenuPresenter.swift in Sources */,
				BE9FEF1127EC9300002CAB93 /* CurrentOrdersTableViewCell.swift in Sources */,
				BEDD1D872A0243E0007D4E4B /* DriverOrderResponseFields.swift in Sources */,
				BEAF3DEA288BDA09006DE167 /* GoogleDirectionDetailsDuration.swift in Sources */,
				73A9D3052B441ADD00A5CC72 /* AdvertisingCollectionViewCell.swift in Sources */,
				E21F54062E0BB64C0096BC2A /* ReferView.swift in Sources */,
				BEDD1DD22A0243E0007D4E4B /* NotificationBadgeResponse.swift in Sources */,
				BE90224427E3454800F9ACE4 /* BottomPopupDismissAnimator.swift in Sources */,
				BEDD1DB12A0243E0007D4E4B /* CountryListResponse.swift in Sources */,
				73F2D1E02C8ECDE9004A9738 /* QuantitiesDiscountCVCell.swift in Sources */,
				BEAF3DE0288BDA09006DE167 /* GoogleDistanceMetrixRootClass.swift in Sources */,
				BEAF3DDC288BDA09006DE167 /* GoogleDistanceMetrixElement.swift in Sources */,
				BE90224127E3454800F9ACE4 /* BottomPopupViewController.swift in Sources */,
				************************ /* ScheduledOrderDetailViewController.swift in Sources */,
				BEC2851627FAD25800A998A8 /* FilterSearchPresenter.swift in Sources */,
				73A6F6DB2B861DAF001DC9EA /* BundelsListCollectionViewCell.swift in Sources */,
				7376F2402AFBAF3F00FAB85A /* LastOfferCollectionViewCell.swift in Sources */,
				BEDD1D742A0243E0007D4E4B /* DriverOrderDeatailsResponse.swift in Sources */,
				BEDD1D792A0243E0007D4E4B /* ContentPageResponse.swift in Sources */,
				************************ /* AccountViewController.swift in Sources */,
				BEDD1DBC2A0243E0007D4E4B /* CountryListResponseFields.swift in Sources */,
				BEA7B9642836A44F00F9F235 /* LocationManager.swift in Sources */,
				BEDD1DBA2A0243E0007D4E4B /* DriverCurrentOrderListingResponseFields.swift in Sources */,
				************************ /* MyCartInteractor.swift in Sources */,
				BEDD1DA22A0243E0007D4E4B /* OrderListResponseFields.swift in Sources */,
				7305CA742BF5066B00F5C4FE /* Int+.swift in Sources */,
				BECDBAA629D56A60008E8EAC /* VersionUpdateInteractor.swift in Sources */,
				BEDD1D6A2A0243E0007D4E4B /* ProductProductsuggestionBody.swift in Sources */,
				BE9A1D1A27D641CA0072BCAC /* CountryCode.swift in Sources */,
				BEDD1D832A0243E0007D4E4B /* GetCartResponseFields.swift in Sources */,
				BEDD1DAF2A0243E0007D4E4B /* UserProfileBody.swift in Sources */,
				73FED5312D7BAC9300C9F1F0 /* CheckoutViewModel.swift in Sources */,
				BE7A839B291132F80078FD2F /* BannerInfoPresenter.swift in Sources */,
				BEDD1D922A0243E0007D4E4B /* OrderResponseFields.swift in Sources */,
				BEB4FE1827F5A7D0006B5BCC /* ThankYouInteractor.swift in Sources */,
				BEDD1D4C2A0243E0007D4E4B /* CodableHelper.swift in Sources */,
				BEDD1D362A0243E0007D4E4B /* JSONValue.swift in Sources */,
				BEBF067627D78A9200C3AF62 /* SettingsTableViewCell.swift in Sources */,
				BEDD1DD12A0243E0007D4E4B /* CancelOrderReasonResponse.swift in Sources */,
				BE1B8B5C27D9D2080045FA0E /* OffersPresenter.swift in Sources */,
				BEDD1D512A0243E0007D4E4B /* LatestHomeWebListingResponse.swift in Sources */,
				73A6F6D22B860808001DC9EA /* BundelsListResponseFields.swift in Sources */,
				BEDD1D5B2A0243E0007D4E4B /* ProductInfoResponse.swift in Sources */,
				BE29143327DB490700802E36 /* OTPVerificationPresenter.swift in Sources */,
				BEDD1D612A0243E0007D4E4B /* DriverOrdercancelBody.swift in Sources */,
				735499272ADACD9D0007E26E /* RewardsVCInteractor.swift in Sources */,
				BEDD1D8E2A0243E0007D4E4B /* DriverOrderDeatailsResponseFields.swift in Sources */,
				732320172C3B2F6500DE37B6 /* InviteFriendsVC.swift in Sources */,
				BEDD1D662A0243E0007D4E4B /* LatestHomeWebListingResponseFields.swift in Sources */,
				BEDD1DB82A0243E0007D4E4B /* ProductProductinfoBody.swift in Sources */,
				732561A12C3D774C004AB08D /* InviteFriendsPresenter.swift in Sources */,
				BEDD1D9F2A0243E0007D4E4B /* ProductDetailResponse.swift in Sources */,
				F995BDC1204D1277005E5279 /* KeyMessages.swift in Sources */,
				BE8CBF6628EC39FF00A3BB7A /* SplashViewController.swift in Sources */,
				BEAF3DDE288BDA09006DE167 /* GoogleDistanceMetrixRow.swift in Sources */,
				8117DF7C27E9B07100004F69 /* HistoryOrderDetailsTableViewCell.swift in Sources */,
				E25C5B4C2E3B6E2300DA87DF /* NewSubCategoriesView.swift in Sources */,
				F96C2D2D20380B1A00F8CBC7 /* Validator.swift in Sources */,
				BEBA10A027E9F3610063E617 /* ProductPopupPresenter.swift in Sources */,
				BEDD1DA52A0243E0007D4E4B /* ShiftResponse.swift in Sources */,
				BEC2E9D929CA27AB00B837F8 /* CheckoutShiftsCell.swift in Sources */,
				81A484AD27DF678F00528766 /* FSPageViewTransformer.swift in Sources */,
				BEDD1D842A0243E0007D4E4B /* WalletDetailsResponse.swift in Sources */,
				E21F53FE2E0B8F2E0096BC2A /* MyAddressesView.swift in Sources */,
				BECDBAA429D56A60008E8EAC /* VersionUpdatePresenter.swift in Sources */,
				BE9568F127F6FBFB002E96C3 /* FilterSideMenuInteractor.swift in Sources */,
				BEDD1DBF2A0243E0007D4E4B /* DriverWeeklyScheduleResponse.swift in Sources */,
				BEC3587A29642F910005040C /* AdvertisementPopupPresenter.swift in Sources */,
				BEDD1DB52A0243E0007D4E4B /* CardResponse.swift in Sources */,
				BEAF3DE3288BDA09006DE167 /* GoogleDirectionDetailsOverviewPolyline.swift in Sources */,
				BEDD1D522A0243E0007D4E4B /* GetCartResponse.swift in Sources */,
				BE97639C27E2081400F29744 /* CurrentOrdersViewController.swift in Sources */,
				BE90224027E3454800F9ACE4 /* BottomPopupUtils.swift in Sources */,
				BEDD1D672A0243E0007D4E4B /* ShiftResponseFields.swift in Sources */,
				BE90223527E344D900F9ACE4 /* SelectLocationInteractor.swift in Sources */,
				BEAF3DC4288BD8A0006DE167 /* GoogleAPIHelper.swift in Sources */,
				BE973F0327E85B0B00C09458 /* ActivityIndicator.swift in Sources */,
				BEDD1DCC2A0243E0007D4E4B /* DriverCountResponseFields.swift in Sources */,
				BEC3587B29642F910005040C /* AdvertisementPopupViewController.swift in Sources */,
				BEEAD04D29CD84E9008455E5 /* AnimationTest.swift in Sources */,
				81A484C027DF6F4A00528766 /* UICollectionViewCell.swift in Sources */,
				************************ /* OTPTextField.swift in Sources */,
				BEAF3DED288BDA09006DE167 /* GoogleDirectionDetailsPolyline.swift in Sources */,
				BEC2851727FAD25800A998A8 /* FilterSearchViewController.swift in Sources */,
				BEF4D1DC2942109D00EA53FF /* CancelReasonViewController.swift in Sources */,
				BE94FAE027FC573E00B131DD /* InvoiceInteractor.swift in Sources */,
				BEDD1D7F2A0243E0007D4E4B /* LatestHomeListingResponse.swift in Sources */,
				BEDD1DA62A0243E0007D4E4B /* UserResponse.swift in Sources */,
				BECA3B7D28217E8C00675D20 /* AddressListTableViewCell.swift in Sources */,
				E25C5B522E3B6FDA00DA87DF /* NewSubCategoryModel.swift in Sources */,
				BEDD1D862A0243E0007D4E4B /* CardResponseFields.swift in Sources */,
				8117DF7727E9AA5C00004F69 /* HistoryOrderDetailsPresenter.swift in Sources */,
				BE93F88927EDE3B20019120C /* CheckoutPresenter.swift in Sources */,
				8117DF7127E9990B00004F69 /* HistoryOrdersTableViewCell.swift in Sources */,
				73F5A8D92D7B242200C260DA /* ProductItemView.swift in Sources */,
				733E02012C04DF40000D972D /* SuperCollectionViewCell.swift in Sources */,
				BEDD1DB42A0243E0007D4E4B /* UserDrivertipBody.swift in Sources */,
				73EBCF002CD010FD00EB14C4 /* AdvertisementCVCell.swift in Sources */,
				73DC7FE22C7F171F00403CDE /* TabyPaymentManager.swift in Sources */,
				BEAF3DDF288BDA09006DE167 /* GoogleDistanceMetrixDistance.swift in Sources */,
				BEDD1D782A0243E0007D4E4B /* OrderListResponse.swift in Sources */,
				73FEA8302D7700F4001C4360 /* MarketingBannersView.swift in Sources */,
				************************ /* OffersTableViewCell.swift in Sources */,
				BEAF3DEB288BDA09006DE167 /* GoogleDirectionDetailsNortheast.swift in Sources */,
				BEDD1D462A0243E0007D4E4B /* CategoryAPI.swift in Sources */,
				BE7A839C291132F80078FD2F /* BannerInfoViewController.swift in Sources */,
				739A39512D79086D00C4847A /* CartManager.swift in Sources */,
				BE90224627E3454800F9ACE4 /* BottomPopupTransitionHandler.swift in Sources */,
				BEDD1D382A0243E0007D4E4B /* APPVersionCheckAPI.swift in Sources */,
				BEDD1DA82A0243E0007D4E4B /* OrderUpdatecontactnumberBody.swift in Sources */,
				7335B9032AFB099800C877FF /* GetPointsDescriptionModel.swift in Sources */,
				BEDD1D4A2A0243E0007D4E4B /* BannerAPI.swift in Sources */,
				735499192ADAB2180007E26E /* RewardVC.swift in Sources */,
				BEDD1D9E2A0243E0007D4E4B /* RatingAddratingBody.swift in Sources */,
				BE04A56E27F5FF8700EE26EA /* HelpPresenter.swift in Sources */,
				738BAC1C2AE5380D00D42040 /* CalculateUserPointsToSARModel.swift in Sources */,
				BEBF067C27D79F0B00C3AF62 /* StaticPageViewController.swift in Sources */,
				BEC2851827FAD25800A998A8 /* FilterSearchInteractor.swift in Sources */,
				732561A32C3D78AE004AB08D /* InviteFriendsInteractor.swift in Sources */,
				7382DE5C2CB7F04C0060C4B5 /* MainSliderCVCell.swift in Sources */,
				738B91FE2CA53BCC00B406CB /* AppDelegate+AppsFlyer.swift in Sources */,
				BEDD1DBD2A0243E0007D4E4B /* AddToCartResponseFields.swift in Sources */,
				************************ /* CheckoutInteractor.swift in Sources */,
				BEDD1D8C2A0243E0007D4E4B /* DriverHomeListingResponseFields.swift in Sources */,
				************************ /* QTYButton.swift in Sources */,
				73465F4B2AD7682300321243 /* CongratsRewardsVC.swift in Sources */,
				73520B272D75D41200BD7086 /* ProfileView.swift in Sources */,
				73520B282D75D41200BD7086 /* OrdersView.swift in Sources */,
				73520B292D75D41200BD7086 /* CartView.swift in Sources */,
				73520B2A2D75D41200BD7086 /* AccountView.swift in Sources */,
				73520B2B2D75D41200BD7086 /* MainTabBarView.swift in Sources */,
				73520B2C2D75D41200BD7086 /* SearchView.swift in Sources */,
				E21F53F32E0B72A90096BC2A /* TransactionsView.swift in Sources */,
				73520B2D2D75D41200BD7086 /* FoodHomeView.swift in Sources */,
				73520B2E2D75D41200BD7086 /* CategoriesView.swift in Sources */,
				73520B2F2D75D41200BD7086 /* CheckoutView.swift in Sources */,
				73520B302D75D41200BD7086 /* CheckoutNewView.swift in Sources */,
				73A565C92D89731F00BDB509 /* OrderDetailViewModel.swift in Sources */,
				73520B312D75D41200BD7086 /* MainCategoriesView.swift in Sources */,
				73A565C72D8970B800BDB509 /* SaudiRiyalAmount.swift in Sources */,
				73520B322D75D41200BD7086 /* MainHomeView.swift in Sources */,
				73520B332D75D41200BD7086 /* ProductDetailView.swift in Sources */,
				BEAF3DE7288BDA09006DE167 /* GoogleDirectionDetailsLocation.swift in Sources */,
				81A484BF27DF6F4A00528766 /* UITableViewExtensions.swift in Sources */,
				BEDD1D422A0243E0007D4E4B /* AuthenticationAPI.swift in Sources */,
				E21F53FA2E0B7E170096BC2A /* MyFavViewModel.swift in Sources */,
				BE10DF2428BBFCCE000983A2 /* MaterialLocalizeImageView.swift in Sources */,
				F97A9A2821103F9B007C45F5 /* UIApplicationExtensions.swift in Sources */,
				BEDD1D8D2A0243E0007D4E4B /* MarketingBannerResponseFields.swift in Sources */,
				BEDD1DA42A0243E0007D4E4B /* NotificationResponse.swift in Sources */,
				************************ /* OTPFieldView.swift in Sources */,
				BEE4FF6229B0BF3A0010A24E /* FavoritePresenter.swift in Sources */,
				734B9FF22C4015EA00C992C2 /* UIImageView+.swift in Sources */,
				7370954F2AAD1F4C0089D3E2 /* UserSearchAPI.swift in Sources */,
				BEDD1DAD2A0243E0007D4E4B /* OauthVerifyotpBody.swift in Sources */,
				BE1B8B5627D9D1D10045FA0E /* MyOrdersViewController.swift in Sources */,
				E2E5B07C2E27EE2900CA6144 /* SubCategoriesView.swift in Sources */,
				737095512AAD204E0089D3E2 /* UserSearchModel.swift in Sources */,
				735B63072B5D201900650BEF /* CheckCartResponse.swift in Sources */,
				738B81B52C1196CE0069DC6E /* BundleProductsView.swift in Sources */,
				BEAF3DE6288BDA09006DE167 /* GoogleDirectionDetailsSouthwest.swift in Sources */,
				BEDD1D982A0243E0007D4E4B /* OrderDetailResponseFields.swift in Sources */,
				BED25AE528F814670048062D /* MixpanelEvents.swift in Sources */,
				7390FF812D75048600C813EC /* SelectDeliveryTypeView.swift in Sources */,
				E25C5B4E2E3B6E4C00DA87DF /* NewSubCategoriesTapSelectionView.swift in Sources */,
				BEDD1D3A2A0243E0007D4E4B /* OrderAPI.swift in Sources */,
				************************ /* ContactUsInteractor.swift in Sources */,
				BE9763A327E2085E00F29744 /* ScheduledOrdersViewController.swift in Sources */,
				BEB3CAE02918DC1B001C400E /* NewHomeViewController.swift in Sources */,
				BEDD1DD52A0243E0007D4E4B /* OauthLoginBody.swift in Sources */,
				BEC898B728FFFF22008AE165 /* SearchSuggestionCell.swift in Sources */,
				BEDD1D7D2A0243E0007D4E4B /* ContactContacthelpcreateBody.swift in Sources */,
				73F9CE492C553EAA00635DDC /* ShiftsBySelectDateCell.swift in Sources */,
				81A484AC27DF678F00528766 /* FSPagerView.swift in Sources */,
				BEE6424B297581E400211571 /* ImageScrollView.swift in Sources */,
				BEE21A68299503890090C9BA /* RatingInteractor.swift in Sources */,
				BE94FADF27FC573E00B131DD /* InvoiceViewController.swift in Sources */,
				E21F54002E0B8F630096BC2A /* MyAddressesViewModel.swift in Sources */,
				BEDD1D932A0243E0007D4E4B /* DayWiseResponse.swift in Sources */,
				BEB5BE2B28B773690075CF42 /* QTYButtonOLD.swift in Sources */,
				BEDD1DDA2A0243E0007D4E4B /* Models.swift in Sources */,
				BEDD1D5E2A0243E0007D4E4B /* DriverWeeklyScheduleResponseFields.swift in Sources */,
				BEDD1D352A0243E0007D4E4B /* AlamofireImplementations.swift in Sources */,
				73FEA8322D771039001C4360 /* MainSectionView.swift in Sources */,
				BEB4FE1D27F5AFCD006B5BCC /* OfferDetailsPresenter.swift in Sources */,
				BEDD1D6C2A0243E0007D4E4B /* DriverProductCountResponseFields.swift in Sources */,
				F90B4CDE2032AAB8006ADAF6 /* AppDelegate.swift in Sources */,
				BEDD1D952A0243E0007D4E4B /* ListResponseFields.swift in Sources */,
				BEBA109E27E9F3610063E617 /* ProductPopupInteractor.swift in Sources */,
				F952382B2047BA3500BC0D49 /* RequestParameter.swift in Sources */,
				************************ /* MaterialLocalizeButton.swift in Sources */,
				E2C33C932E00DF8500EF66A2 /* CustomSwiftUIAlertView.swift in Sources */,
				BEA762102886ACF200A890E4 /* TrackOrderInteractor.swift in Sources */,
				BEB4FE1F27F5AFCD006B5BCC /* OfferDetailsInteractor.swift in Sources */,
				************************ /* BottomPopupNavigationController.swift in Sources */,
				BE8D82E827D5E54B00A11D15 /* LoginPresenter.swift in Sources */,
				************************ /* AccountInteractor.swift in Sources */,
				BEDD1DB92A0243E0007D4E4B /* UserUserlanguageBody.swift in Sources */,
				************************ /* ContactUsPresenter.swift in Sources */,
				BEDD1D7B2A0243E0007D4E4B /* DriverRescheduleorderBody.swift in Sources */,
				BEDD1D962A0243E0007D4E4B /* UpdateLanguageResponseFields.swift in Sources */,
				E29542452E48BFEA0002622C /* ProductSquareItemView.swift in Sources */,
				BEDD1D642A0243E0007D4E4B /* UpdateLanguageResponse.swift in Sources */,
				************************ /* User.swift in Sources */,
				BEDD1DD72A0243E0007D4E4B /* AddressListResponse.swift in Sources */,
				E21F53F12E0B71EA0096BC2A /* WalletViewModel.swift in Sources */,
				BEDD1DC42A0243E0007D4E4B /* FavouriteResponse.swift in Sources */,
				7346C1BE2A40C40200A9E5DF /* BaseResponseModel.swift in Sources */,
				E21F53F82E0B7D110096BC2A /* MyFavouritesView.swift in Sources */,
				BEBF066A27D7769D00C3AF62 /* SettingsViewController.swift in Sources */,
				BEA9574528DDDA8A00DCFFF9 /* LoadingView.swift in Sources */,
				BEDD1D542A0243E0007D4E4B /* DriverProductCountResponse.swift in Sources */,
				73381ADE2D7CEC340059A27C /* ImageSliderView.swift in Sources */,
				BEDD1D592A0243E0007D4E4B /* PromocodeCheckResponseFields.swift in Sources */,
				BEDD1D5A2A0243E0007D4E4B /* OrderCreateorderBody.swift in Sources */,
				BEC2851D27FAF6A200A998A8 /* AddAddressPresenter.swift in Sources */,
				F97A9A2921103F9F007C45F5 /* UIViewExtensions.swift in Sources */,
				BE8D82ED27D5E90F00A11D15 /* CustomRoundedButtton.swift in Sources */,
				73FF5AEA2D7659270067415B /* MainHomeViewModel.swift in Sources */,
				73B624462D75CFA20064D21A /* LoadingIndicator.swift in Sources */,
				BE1B8B5E27D9D2080045FA0E /* OffersInteractor.swift in Sources */,
				BEC3587C29642F910005040C /* AdvertisementPopupInteractor.swift in Sources */,
				BECDBAA529D56A60008E8EAC /* VersionUpdateViewController.swift in Sources */,
				BEDD1D4E2A0243E0007D4E4B /* APIs.swift in Sources */,
				BE9A1D1C27D641CA0072BCAC /* CountryTableViewCell.swift in Sources */,
				738AD26D2D6E50F700419CCA /* ViewInvoiceVC.swift in Sources */,
				BEDD1D892A0243E0007D4E4B /* OrderOrdertransactionupdateBody.swift in Sources */,
				BEAF3DEC288BDA09006DE167 /* GoogleDirectionDetailsViaWaypoint.swift in Sources */,
				73E190052A9D32D80010AE40 /* MayAlsoLikeTVCell.swift in Sources */,
				737E27F22D7EEAFA00723266 /* EmptyOrdersView.swift in Sources */,
				BEDD1D572A0243E0007D4E4B /* ProductLatestproductlistingBody.swift in Sources */,
				BEDD1DCD2A0243E0007D4E4B /* GetCartListResponseFields.swift in Sources */,
				BEAF3DF0288BDA09006DE167 /* GoogleDirectionDetailsRootClass.swift in Sources */,
				73F6BBDC2A9783FE0009A7E0 /* FreeDeliveryValueResponse.swift in Sources */,
				73D172332D82533400E7EF01 /* CategoriesModel.swift in Sources */,
				BEE4FF6329B0BF3A0010A24E /* FavoriteViewController.swift in Sources */,
				F97A9A2D21103FAA007C45F5 /* EZSwiftFunctions.swift in Sources */,
				BEDD1D942A0243E0007D4E4B /* ContentPageResponseFields.swift in Sources */,
				BEA7620E2886ACF200A890E4 /* TrackOrderPresenter.swift in Sources */,
				73E160942D60E90F0086A16B /* FirebaseEvents.swift in Sources */,
				BEDD1D6D2A0243E0007D4E4B /* CheckappversionBody.swift in Sources */,
				BE99782E27E0B8BC005CC308 /* MyOrdersTableViewCell.swift in Sources */,
				8117DF7927E9AA5C00004F69 /* HistoryOrderDetailsInteractor.swift in Sources */,
				739A394E2D7900CC00C4847A /* LoadingImageWithUrlView.swift in Sources */,
				BEDD1D5C2A0243E0007D4E4B /* WalletDetailsResponseFields.swift in Sources */,
				BEE21A66299503890090C9BA /* RatingPresenter.swift in Sources */,
				BEAF3DEF288BDA09006DE167 /* GoogleDirectionDetailsRoute.swift in Sources */,
				73248D9A2C10A24100C2E101 /* UITableViewCell+.swift in Sources */,
				BEDD1D552A0243E0007D4E4B /* WebCategoryListResponseFields.swift in Sources */,
				BEDD1DC22A0243E0007D4E4B /* HomeListingResponseFields.swift in Sources */,
				BEDD1D4B2A0243E0007D4E4B /* CommonAPI.swift in Sources */,
				81A484B727DF69AE00528766 /* CategoriesCollectionViewCell.swift in Sources */,
				BEA85B2A2876E26700F4754E /* CategoriesCollectionViewCellBig.swift in Sources */,
				73A565C52D89709F00BDB509 /* StepperCartView.swift in Sources */,
				BE822D4D291904BB00E91318 /* HomeTableViewCell.swift in Sources */,
				BEDD1DA72A0243E0007D4E4B /* DriverCurrentOrderListingResponse.swift in Sources */,
				************************ /* ContactUsViewController.swift in Sources */,
				BEDD1DC62A0243E0007D4E4B /* ProductListResponseFields.swift in Sources */,
				BEE4FF6429B0BF3A0010A24E /* FavoriteInteractor.swift in Sources */,
				BE97639D27E2081400F29744 /* CurrentOrdersInteractor.swift in Sources */,
				BEDD1D4D2A0243E0007D4E4B /* JSONEncodableEncoding.swift in Sources */,
				81A484B027DF678F00528766 /* FSPagerViewLayoutAttributes.swift in Sources */,
				73F7EFFC2D7C66DE00E35734 /* Date+.swift in Sources */,
				737E27F02D7EEAEA00723266 /* OrderDetailView.swift in Sources */,
				BEB4FE1627F5A7D0006B5BCC /* ThankYouPresenter.swift in Sources */,
				732E03392C2C4E86008B6088 /* ChooseDeliveryDateCVC.swift in Sources */,
				BEDD1D482A0243E0007D4E4B /* CountryAPI.swift in Sources */,
				BEAAEC7D2850825F005A1D49 /* SocketIOManager.swift in Sources */,
				BEB4FE2527F5CC13006B5BCC /* OrderDetailViewController.swift in Sources */,
				BEDD1D412A0243E0007D4E4B /* FavouriteAPI.swift in Sources */,
				BEDD1D582A0243E0007D4E4B /* AppVersionResponse.swift in Sources */,
				BEF4D1DD2942109D00EA53FF /* CancelReasonInteractor.swift in Sources */,
				BEDD1D8B2A0243E0007D4E4B /* GetCartShiftResponseFields.swift in Sources */,
				BEDD1DC02A0243E0007D4E4B /* AddRatingResponseFields.swift in Sources */,
				************************ /* AccountPresenter.swift in Sources */,
				BEDD1D4F2A0243E0007D4E4B /* OauthGuestloginBody.swift in Sources */,
				732215612C47E7F200A6C763 /* InviteFriendsGiftVC.swift in Sources */,
				BEDD1DBB2A0243E0007D4E4B /* DriverCompletedOrderDeatailsResponseFields.swift in Sources */,
				BEDD1DAE2A0243E0007D4E4B /* ProductSuggestionResponse.swift in Sources */,
				73F5A8D62D7B23F900C260DA /* YouMayAlsoLikeView.swift in Sources */,
				BEE642532975825000211571 /* FullScreenImageInteractor.swift in Sources */,
				737E27EC2D7EE26D00723266 /* OrdersListView.swift in Sources */,
				BEABE2742865F2AD0033AB1D /* Date+Extension.swift in Sources */,
				BEDD1DAA2A0243E0007D4E4B /* DynamicSectionResponseFields.swift in Sources */,
				BEB4FE1E27F5AFCD006B5BCC /* OfferDetailsViewController.swift in Sources */,
				81A484B227DF678F00528766 /* FSPageControl.swift in Sources */,
				************************ /* CustomTextfieldWithFontStyle.swift in Sources */,
				BEDD1D7E2A0243E0007D4E4B /* LatestHomeListingResponseFields.swift in Sources */,
				73E595AF2D747C520043EE66 /* FloatingButtonView.swift in Sources */,
				BEDD1D7A2A0243E0007D4E4B /* OrderGetorderlistBody.swift in Sources */,
				BEDD1D342A0243E0007D4E4B /* APIHelper.swift in Sources */,
				BEDD1D442A0243E0007D4E4B /* NotifyMeAPI.swift in Sources */,
				BEBF067127D7844600C3AF62 /* MirroringLabel.swift in Sources */,
				BEDD1D402A0243E0007D4E4B /* RatingAPI.swift in Sources */,
				BE97639B27E2081400F29744 /* CurrentOrdersPresenter.swift in Sources */,
				BEBF067B27D79F0B00C3AF62 /* StaticPagePresenter.swift in Sources */,
				BE9B3A5227FF21B800EF3615 /* ChoosePaymentInteractor.swift in Sources */,
				BE9B3A5827FF49D000EF3615 /* AddCardViewController.swift in Sources */,
				BEDD1DC82A0243E0007D4E4B /* OauthRequestforotpBody.swift in Sources */,
				BE93F88527EDE3B20019120C /* MyCartViewController.swift in Sources */,
				BEDD1DCB2A0243E0007D4E4B /* CartAddtocartBody.swift in Sources */,
				BEADE941286D73B000556D1B /* ProductsCollectionViewCellSmall.swift in Sources */,
				BE9B3A5927FF49D000EF3615 /* AddCardInteractor.swift in Sources */,
				F97A9A2F21103FAF007C45F5 /* UserDefaultsExtension.swift in Sources */,
				BEDD1DD42A0243E0007D4E4B /* DriverRescheduleOrderResponseFields.swift in Sources */,
				BEDD1DD62A0243E0007D4E4B /* ProductListResponse.swift in Sources */,
				BE1BC0B727FD71DD00C9E42E /* MyCartCheckoutTableViewCell.swift in Sources */,
				BEDD1D6B2A0243E0007D4E4B /* OfferResponse.swift in Sources */,
				7335B9062AFB0E4B00C877FF /* RewardDetailsCell.swift in Sources */,
				BEDD1DB72A0243E0007D4E4B /* UserAddaddressBody.swift in Sources */,
				BEDD1D682A0243E0007D4E4B /* AdvertisementResponse.swift in Sources */,
				F90B4CE52032F4A6006ADAF6 /* SOService.swift in Sources */,
				735499252ADACD440007E26E /* RewardsVCPresenter.swift in Sources */,
				BEAFCB9E2995265900276C1D /* FloatRatingView.swift in Sources */,
				BEDD1D3D2A0243E0007D4E4B /* UserWalletAPI.swift in Sources */,
				F90B4CE22032F4A6006ADAF6 /* SOAPI.swift in Sources */,
				BEC2851E27FAF6A200A998A8 /* AddAddressViewController.swift in Sources */,
				BEB4FE1727F5A7D0006B5BCC /* ThankYouViewController.swift in Sources */,
				BEDD1DB62A0243E0007D4E4B /* NotifymeBody.swift in Sources */,
				BEDD1D8F2A0243E0007D4E4B /* CacheResponse.swift in Sources */,
				BEDD1D722A0243E0007D4E4B /* WebProductResponseFields.swift in Sources */,
				73B47AD12D788058007B8FB9 /* ProductDetailsView.swift in Sources */,
				73757E632A8AFBB20052D068 /* RecommendationProductCVC.swift in Sources */,
				BEDD1DA32A0243E0007D4E4B /* AddRatingResponse.swift in Sources */,
				BEE642512975825000211571 /* FullScreenImagePresenter.swift in Sources */,
				BEDD1D902A0243E0007D4E4B /* PromocodeCheckResponse.swift in Sources */,
				BEDD1DBE2A0243E0007D4E4B /* UserAddmoneytowalletBody.swift in Sources */,
				BEDD1D6F2A0243E0007D4E4B /* PromocodePromocodecheckBody.swift in Sources */,
				8117DF6D27E998A700004F69 /* HistoryOrdersViewController.swift in Sources */,
				E2E5B07A2E27EDE600CA6144 /* SubCategoriesViewModel.swift in Sources */,
				BEDD1D822A0243E0007D4E4B /* UserUpdatedevicetokenBody.swift in Sources */,
				73A6CF392A988E14007F3657 /* CartYouMayAlsoLikeProductsResponse.swift in Sources */,
				BE9B3A4B27FEFB1A00EF3615 /* PaymentCardListTableViewCell.swift in Sources */,
				738B81B92C11A4A90069DC6E /* CartProductsOfBundleTVCell.swift in Sources */,
				BEDD1D7C2A0243E0007D4E4B /* CreateOrderResponseFields.swift in Sources */,
				BEDD1D652A0243E0007D4E4B /* ProductResponseFields.swift in Sources */,
				BE9568F027F6FBFB002E96C3 /* FilterSideMenuViewController.swift in Sources */,
				BEDD1D602A0243E0007D4E4B /* UserChangepasswordBody.swift in Sources */,
				73B47AD32D788298007B8FB9 /* OfferCardDetailsView.swift in Sources */,
				BEDD1D9D2A0243E0007D4E4B /* AddressResponseFields.swift in Sources */,
				BEAF3DE1288BDA09006DE167 /* GoogleDirectionDetailsLeg.swift in Sources */,
				738716D82BFFC9A200B1B8F6 /* ProductsOfBundleTVCell.swift in Sources */,
				73B47A962D787C4D007B8FB9 /* OfferCardView.swift in Sources */,
				F9A4539F203AA7200065C9FD /* Constant.swift in Sources */,
				BEB4FE2627F5CC13006B5BCC /* OrderDetailInteractor.swift in Sources */,
				73190A5B2A8C786D000C31D7 /* ShadowViewOnly.swift in Sources */,
				73B961BE2A8C595900B92293 /* ShadowView.swift in Sources */,
				************************ /* OTPVerificationViewController.swift in Sources */,
				81A484B427DF678F00528766 /* FSPagerCollectionView.swift in Sources */,
				BE9B3A5027FF21B800EF3615 /* ChoosePaymentPresenter.swift in Sources */,
				7376F23B2AFB9D1700FAB85A /* LastOfferTableViewCell.swift in Sources */,
				BE90224727E3454800F9ACE4 /* BottomPopupPresentationController.swift in Sources */,
				BEAF3DE5288BDA09006DE167 /* GoogleDirectionDetailsBound.swift in Sources */,
				BEDD1D392A0243E0007D4E4B /* ContactUsAPI.swift in Sources */,
				BE1B8B5527D9D1D10045FA0E /* MyOrdersPresenter.swift in Sources */,
				BEDD1DA02A0243E0007D4E4B /* DriverHomeListingResponse.swift in Sources */,
				BEDD1D912A0243E0007D4E4B /* ProductProductlistingBody.swift in Sources */,
				BEBF067227D7844600C3AF62 /* MirroringViewController.swift in Sources */,
				BE7A839D291132F80078FD2F /* BannerInfoInteractor.swift in Sources */,
				BEF4D1DF2942249F00EA53FF /* CancelReasonTableViewCell.swift in Sources */,
				BEDD1D6E2A0243E0007D4E4B /* LatestProductListResponse.swift in Sources */,
				7354991F2ADAB3A80007E26E /* PointsHostoryTVCell.swift in Sources */,
				BEDD1D5D2A0243E0007D4E4B /* AppVersionResponseFields.swift in Sources */,
				BEDD1DD82A0243E0007D4E4B /* Extensions.swift in Sources */,
				E20B057C2E32925200E635FB /* NewProductsViewModel.swift in Sources */,
				73B47A902D787551007B8FB9 /* OffersView.swift in Sources */,
				BE9763A627E2105F00F29744 /* ViewEmbedder.swift in Sources */,
				BEDD1DCF2A0243E0007D4E4B /* DriverOrderstatusupdateBody.swift in Sources */,
				737E27EE2D7EE68D00723266 /* OrdersListViewModel.swift in Sources */,
				BEC0146827D6232900509E26 /* CustomViewForFields.swift in Sources */,
				BEDD1D812A0243E0007D4E4B /* FavouriteMarkfavouriteBody.swift in Sources */,
				81A484AF27DF678F00528766 /* FSPagerViewCell.swift in Sources */,
				BEDD1D9A2A0243E0007D4E4B /* ProductProductweblistingBody.swift in Sources */,
				BE9B3A5127FF21B800EF3615 /* ChoosePaymentViewController.swift in Sources */,
				73342DBE2BF3C0A3005FC06E /* Notification+.swift in Sources */,
				81A484B327DF678F00528766 /* FSPagerViewObjcCompat.m in Sources */,
				BEDD1D9B2A0243E0007D4E4B /* NotificationResponseFields.swift in Sources */,
				738BAC1A2AE50E1F00D42040 /* PointsLevelsModel.swift in Sources */,
				73B6240F2D75B6540064D21A /* WarehouseModel.swift in Sources */,
				BEDD1D992A0243E0007D4E4B /* OfferListResponse.swift in Sources */,
				BEDD1DC72A0243E0007D4E4B /* FavouriteListResponse.swift in Sources */,
				73A941AE2B331C2100F1C8A3 /* UICollectionViewCell+.swift in Sources */,
				F97A9A2B21103FA4007C45F5 /* StringExtensions.swift in Sources */,
				BEDD1D622A0243E0007D4E4B /* UserAddusercardsBody.swift in Sources */,
				BEB4FE2427F5CC13006B5BCC /* OrderDetailPresenter.swift in Sources */,
				BE9FEF1327EC9CE2002CAB93 /* ScheduledOrdersTableViewCell.swift in Sources */,
				BEBF066B27D7769D00C3AF62 /* SettingsInteractor.swift in Sources */,
				BEDD1D9C2A0243E0007D4E4B /* AddToCartResponse.swift in Sources */,
				BEDD1DB02A0243E0007D4E4B /* FavouriteResponseFields.swift in Sources */,
				BEADE08D28C9D2870095913E /* HelpViewController.swift in Sources */,
				BEDD1D972A0243E0007D4E4B /* CancelOrderReasonResponseFields.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		7352DBB02CFFC23200320EDD /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 7352DBA92CFFC23200320EDD /* ContentImageExtentionPush */;
			targetProxy = 7352DBAF2CFFC23200320EDD /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		BE8D82F927D6069000A11D15 /* Localizable.strings */ = {
			isa = PBXVariantGroup;
			children = (
				BE8D82F827D6069000A11D15 /* en */,
				BE8D82FA27D6069700A11D15 /* ar */,
			);
			name = Localizable.strings;
			sourceTree = "<group>";
		};
		F90B4C9C20329C45006ADAF6 /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				F90B4C9D20329C45006ADAF6 /* Base */,
				BE8D82F227D605FB00A11D15 /* ar */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
		F90B4CA120329C45006ADAF6 /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				F90B4CA220329C45006ADAF6 /* Base */,
				BE8D82F327D605FB00A11D15 /* ar */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		7352DBB42CFFC23200320EDD /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = J7FTCACFB3;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = ContentImageExtentionPush/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = ContentImageExtentionPush;
				INFOPLIST_KEY_NSHumanReadableCopyright = "Copyright © 2024 SOTSYS203. All rights reserved.";
				IPHONEOS_DEPLOYMENT_TARGET = 18.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MARKETING_VERSION = 1.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.material.customer.ContentImageExtentionPush;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE = "";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		7352DBB52CFFC23200320EDD /* DevRelease */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = J7FTCACFB3;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = ContentImageExtentionPush/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = ContentImageExtentionPush;
				INFOPLIST_KEY_NSHumanReadableCopyright = "Copyright © 2024 SOTSYS203. All rights reserved.";
				IPHONEOS_DEPLOYMENT_TARGET = 18.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MARKETING_VERSION = 1.0;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.material.customer.ContentImageExtentionPush;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE = "";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = DevRelease;
		};
		7352DBB62CFFC23200320EDD /* Staging */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = J7FTCACFB3;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = ContentImageExtentionPush/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = ContentImageExtentionPush;
				INFOPLIST_KEY_NSHumanReadableCopyright = "Copyright © 2024 SOTSYS203. All rights reserved.";
				IPHONEOS_DEPLOYMENT_TARGET = 18.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MARKETING_VERSION = 1.0;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.material.customer.ContentImageExtentionPush;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE = "";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Staging;
		};
		7352DBB72CFFC23200320EDD /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = J7FTCACFB3;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = ContentImageExtentionPush/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = ContentImageExtentionPush;
				INFOPLIST_KEY_NSHumanReadableCopyright = "Copyright © 2024 SOTSYS203. All rights reserved.";
				IPHONEOS_DEPLOYMENT_TARGET = 18.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MARKETING_VERSION = 1.0;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.material.customer.ContentImageExtentionPush;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE = "";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		7352DBB82CFFC23200320EDD /* ReleaseStaging */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = J7FTCACFB3;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = ContentImageExtentionPush/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = ContentImageExtentionPush;
				INFOPLIST_KEY_NSHumanReadableCopyright = "Copyright © 2024 SOTSYS203. All rights reserved.";
				IPHONEOS_DEPLOYMENT_TARGET = 18.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MARKETING_VERSION = 1.0;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.material.customer.ContentImageExtentionPush;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE = "";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = ReleaseStaging;
		};
		BECDA1D52947051300EB10E1 /* DevRelease */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				PROVISIONING_PROFILE = "3b6534d4-7404-42bd-8dd1-ebd618ba6163";
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = DevRelease;
		};
		BECDA1D62947051300EB10E1 /* DevRelease */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 45F95AE511E3881314FA4AA5 /* Pods-TopCustomer.devrelease.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = TopCustomer/TopCustomerDebug.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = J7FTCACFB3;
				ENABLE_BITCODE = NO;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "";
				INFOPLIST_FILE = "$(SRCROOT)/TopCustomer/Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Basecode/Library",
				);
				MARKETING_VERSION = 6.1.39;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_LDFLAGS = (
					"$(OTHER_LDFLAGS)",
					"$(inherited)",
					"-ObjC",
					"-l\"c++\"",
					"-l\"sqlite3\"",
					"-l\"swiftCoreGraphics\"",
					"-l\"xml2\"",
					"-l\"z\"",
					"-framework",
					"\"AACarousel\"",
					"-framework",
					"\"Accelerate\"",
					"-framework",
					"\"ActionSheetPicker_3_0\"",
					"-framework",
					"\"Alamofire\"",
					"-framework",
					"\"Branch\"",
					"-framework",
					"\"CFNetwork\"",
					"-framework",
					"\"CocoaLumberjack\"",
					"-framework",
					"\"CoreData\"",
					"-framework",
					"\"CoreGraphics\"",
					"-framework",
					"\"CoreImage\"",
					"-framework",
					"\"CoreLocation\"",
					"-framework",
					"\"CoreServices\"",
					"-framework",
					"\"CoreTelephony\"",
					"-framework",
					"\"CoreText\"",
					"-framework",
					"\"FBLPromises\"",
					"-framework",
					"\"FirebaseAnalytics\"",
					"-framework",
					"\"FirebaseCore\"",
					"-framework",
					"\"FirebaseCoreInternal\"",
					"-framework",
					"\"FirebaseDynamicLinks\"",
					"-framework",
					"\"FirebaseInstallations\"",
					"-framework",
					"\"FirebaseMessaging\"",
					"-framework",
					"\"Foundation\"",
					"-framework",
					"\"GLKit\"",
					"-framework",
					"\"GoogleAppMeasurement\"",
					"-framework",
					"\"GoogleAppMeasurementIdentitySupport\"",
					"-framework",
					"\"GoogleDataTransport\"",
					"-framework",
					"\"GoogleMaps\"",
					"-framework",
					"\"GoogleMapsBase\"",
					"-framework",
					"\"GoogleMapsCore\"",
					"-framework",
					"\"GooglePlaces\"",
					"-framework",
					"\"GoogleUtilities\"",
					"-framework",
					"\"IQKeyboardManagerSwift\"",
					"-framework",
					"\"ImageIO\"",
					"-framework",
					"\"Kingfisher\"",
					"-framework",
					"\"Lottie\"",
					"-framework",
					"\"Metal\"",
					"-framework",
					"\"Mixpanel\"",
					"-framework",
					"\"OpenGLES\"",
					"-framework",
					"\"PassKit\"",
					"-framework",
					"\"PaymentSDK\"",
					"-framework",
					"\"QuartzCore\"",
					"-framework",
					"\"Reachability\"",
					"-framework",
					"\"STTabbar\"",
					"-framework",
					"\"SVGKit\"",
					"-framework",
					"\"Security\"",
					"-framework",
					"\"SocketIO\"",
					"-framework",
					"\"Starscream\"",
					"-framework",
					"\"StoreKit\"",
					"-framework",
					"\"SwiftyJSON\"",
					"-framework",
					"\"SystemConfiguration\"",
					"-framework",
					"\"Toast_Swift\"",
					"-framework",
					"\"UIKit\"",
					"-framework",
					"\"WebKit\"",
					"-framework",
					"\"iAd\"",
					"-framework",
					"\"nanopb\"",
					"-weak_framework",
					"\"Combine\"",
					"-weak_framework",
					"\"LinkPresentation\"",
					"-weak_framework",
					"\"SwiftUI\"",
					"-weak_framework",
					"\"UserNotifications\"",
					"-weak_framework",
					"\"WebKit\"",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.material.customer;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE = "";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEVRELEASE;
				SWIFT_OBJC_BRIDGING_HEADER = "$(PRODUCT_NAME)/Application/Basecode-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = DevRelease;
		};
		BECDA1D729470AE000EB10E1 /* ReleaseStaging */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				PROVISIONING_PROFILE = "3b6534d4-7404-42bd-8dd1-ebd618ba6163";
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				VALIDATE_PRODUCT = YES;
			};
			name = ReleaseStaging;
		};
		BECDA1D829470AE000EB10E1 /* ReleaseStaging */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 36CCE020292AFB9CF22A3724 /* Pods-TopCustomer.releasestaging.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = TopCustomer/TopCustomerRelease.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = J7FTCACFB3;
				ENABLE_BITCODE = NO;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "";
				INFOPLIST_FILE = "$(SRCROOT)/TopCustomer/Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Basecode/Library",
				);
				MARKETING_VERSION = 6.1.39;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_LDFLAGS = (
					"$(OTHER_LDFLAGS)",
					"$(inherited)",
					"-ObjC",
					"-l\"c++\"",
					"-l\"sqlite3\"",
					"-l\"swiftCoreGraphics\"",
					"-l\"xml2\"",
					"-l\"z\"",
					"-framework",
					"\"AACarousel\"",
					"-framework",
					"\"Accelerate\"",
					"-framework",
					"\"ActionSheetPicker_3_0\"",
					"-framework",
					"\"Alamofire\"",
					"-framework",
					"\"Branch\"",
					"-framework",
					"\"CFNetwork\"",
					"-framework",
					"\"CocoaLumberjack\"",
					"-framework",
					"\"CoreData\"",
					"-framework",
					"\"CoreGraphics\"",
					"-framework",
					"\"CoreImage\"",
					"-framework",
					"\"CoreLocation\"",
					"-framework",
					"\"CoreServices\"",
					"-framework",
					"\"CoreTelephony\"",
					"-framework",
					"\"CoreText\"",
					"-framework",
					"\"FBLPromises\"",
					"-framework",
					"\"FirebaseAnalytics\"",
					"-framework",
					"\"FirebaseCore\"",
					"-framework",
					"\"FirebaseCoreInternal\"",
					"-framework",
					"\"FirebaseDynamicLinks\"",
					"-framework",
					"\"FirebaseInstallations\"",
					"-framework",
					"\"FirebaseMessaging\"",
					"-framework",
					"\"Foundation\"",
					"-framework",
					"\"GLKit\"",
					"-framework",
					"\"GoogleAppMeasurement\"",
					"-framework",
					"\"GoogleAppMeasurementIdentitySupport\"",
					"-framework",
					"\"GoogleDataTransport\"",
					"-framework",
					"\"GoogleMaps\"",
					"-framework",
					"\"GoogleMapsBase\"",
					"-framework",
					"\"GoogleMapsCore\"",
					"-framework",
					"\"GooglePlaces\"",
					"-framework",
					"\"GoogleUtilities\"",
					"-framework",
					"\"IQKeyboardManagerSwift\"",
					"-framework",
					"\"ImageIO\"",
					"-framework",
					"\"Kingfisher\"",
					"-framework",
					"\"Lottie\"",
					"-framework",
					"\"Metal\"",
					"-framework",
					"\"Mixpanel\"",
					"-framework",
					"\"OpenGLES\"",
					"-framework",
					"\"PassKit\"",
					"-framework",
					"\"PaymentSDK\"",
					"-framework",
					"\"QuartzCore\"",
					"-framework",
					"\"Reachability\"",
					"-framework",
					"\"STTabbar\"",
					"-framework",
					"\"SVGKit\"",
					"-framework",
					"\"Security\"",
					"-framework",
					"\"SocketIO\"",
					"-framework",
					"\"Starscream\"",
					"-framework",
					"\"StoreKit\"",
					"-framework",
					"\"SwiftyJSON\"",
					"-framework",
					"\"SystemConfiguration\"",
					"-framework",
					"\"Toast_Swift\"",
					"-framework",
					"\"UIKit\"",
					"-framework",
					"\"WebKit\"",
					"-framework",
					"\"iAd\"",
					"-framework",
					"\"nanopb\"",
					"-weak_framework",
					"\"Combine\"",
					"-weak_framework",
					"\"LinkPresentation\"",
					"-weak_framework",
					"\"SwiftUI\"",
					"-weak_framework",
					"\"UserNotifications\"",
					"-weak_framework",
					"\"WebKit\"",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.material.customer;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE = "";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = RELEASESTAGING;
				SWIFT_OBJC_BRIDGING_HEADER = "$(PRODUCT_NAME)/Application/Basecode-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = ReleaseStaging;
		};
		F9031F152035A469002EBE92 /* Staging */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				PROVISIONING_PROFILE = "3b6534d4-7404-42bd-8dd1-ebd618ba6163";
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Staging;
		};
		F9031F162035A469002EBE92 /* Staging */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9929B054DF35A09609A5E64A /* Pods-TopCustomer.staging.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = TopCustomer/TopCustomerStaging.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = J7FTCACFB3;
				ENABLE_BITCODE = NO;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "";
				INFOPLIST_FILE = "$(SRCROOT)/TopCustomer/Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Basecode/Library",
				);
				MARKETING_VERSION = 6.1.39;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_LDFLAGS = (
					"$(OTHER_LDFLAGS)",
					"$(inherited)",
					"-ObjC",
					"-l\"c++\"",
					"-l\"sqlite3\"",
					"-l\"swiftCoreGraphics\"",
					"-l\"xml2\"",
					"-l\"z\"",
					"-framework",
					"\"AACarousel\"",
					"-framework",
					"\"Accelerate\"",
					"-framework",
					"\"ActionSheetPicker_3_0\"",
					"-framework",
					"\"Alamofire\"",
					"-framework",
					"\"Branch\"",
					"-framework",
					"\"CFNetwork\"",
					"-framework",
					"\"CocoaLumberjack\"",
					"-framework",
					"\"CoreData\"",
					"-framework",
					"\"CoreGraphics\"",
					"-framework",
					"\"CoreImage\"",
					"-framework",
					"\"CoreLocation\"",
					"-framework",
					"\"CoreServices\"",
					"-framework",
					"\"CoreTelephony\"",
					"-framework",
					"\"CoreText\"",
					"-framework",
					"\"FBLPromises\"",
					"-framework",
					"\"FirebaseAnalytics\"",
					"-framework",
					"\"FirebaseCore\"",
					"-framework",
					"\"FirebaseCoreInternal\"",
					"-framework",
					"\"FirebaseDynamicLinks\"",
					"-framework",
					"\"FirebaseInstallations\"",
					"-framework",
					"\"FirebaseMessaging\"",
					"-framework",
					"\"Foundation\"",
					"-framework",
					"\"GLKit\"",
					"-framework",
					"\"GoogleAppMeasurement\"",
					"-framework",
					"\"GoogleAppMeasurementIdentitySupport\"",
					"-framework",
					"\"GoogleDataTransport\"",
					"-framework",
					"\"GoogleMaps\"",
					"-framework",
					"\"GoogleMapsBase\"",
					"-framework",
					"\"GoogleMapsCore\"",
					"-framework",
					"\"GooglePlaces\"",
					"-framework",
					"\"GoogleUtilities\"",
					"-framework",
					"\"IQKeyboardManagerSwift\"",
					"-framework",
					"\"ImageIO\"",
					"-framework",
					"\"Kingfisher\"",
					"-framework",
					"\"Lottie\"",
					"-framework",
					"\"Metal\"",
					"-framework",
					"\"Mixpanel\"",
					"-framework",
					"\"OpenGLES\"",
					"-framework",
					"\"PassKit\"",
					"-framework",
					"\"PaymentSDK\"",
					"-framework",
					"\"QuartzCore\"",
					"-framework",
					"\"Reachability\"",
					"-framework",
					"\"STTabbar\"",
					"-framework",
					"\"SVGKit\"",
					"-framework",
					"\"Security\"",
					"-framework",
					"\"SocketIO\"",
					"-framework",
					"\"Starscream\"",
					"-framework",
					"\"StoreKit\"",
					"-framework",
					"\"SwiftyJSON\"",
					"-framework",
					"\"SystemConfiguration\"",
					"-framework",
					"\"Toast_Swift\"",
					"-framework",
					"\"UIKit\"",
					"-framework",
					"\"WebKit\"",
					"-framework",
					"\"iAd\"",
					"-framework",
					"\"nanopb\"",
					"-weak_framework",
					"\"Combine\"",
					"-weak_framework",
					"\"LinkPresentation\"",
					"-weak_framework",
					"\"SwiftUI\"",
					"-weak_framework",
					"\"UserNotifications\"",
					"-weak_framework",
					"\"WebKit\"",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.material.customer;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE = "";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = STAGING;
				SWIFT_OBJC_BRIDGING_HEADER = "$(PRODUCT_NAME)/Application/Basecode-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Staging;
		};
		F90B4CBB20329C45006ADAF6 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				PROVISIONING_PROFILE = "3b6534d4-7404-42bd-8dd1-ebd618ba6163";
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		F90B4CBC20329C45006ADAF6 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				PROVISIONING_PROFILE = "3b6534d4-7404-42bd-8dd1-ebd618ba6163";
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		F90B4CBE20329C45006ADAF6 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 6395572ACF2F73DAD7E168CF /* Pods-TopCustomer.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = TopCustomer/TopCustomerDebug.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = J7FTCACFB3;
				ENABLE_BITCODE = NO;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "";
				INFOPLIST_FILE = "$(SRCROOT)/TopCustomer/Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Basecode/Library",
				);
				MARKETING_VERSION = 6.1.39;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_LDFLAGS = (
					"$(OTHER_LDFLAGS)",
					"$(inherited)",
					"-ObjC",
					"-l\"c++\"",
					"-l\"sqlite3\"",
					"-l\"swiftCoreGraphics\"",
					"-l\"xml2\"",
					"-l\"z\"",
					"-framework",
					"\"AACarousel\"",
					"-framework",
					"\"Accelerate\"",
					"-framework",
					"\"ActionSheetPicker_3_0\"",
					"-framework",
					"\"Alamofire\"",
					"-framework",
					"\"Branch\"",
					"-framework",
					"\"CFNetwork\"",
					"-framework",
					"\"CocoaLumberjack\"",
					"-framework",
					"\"CoreData\"",
					"-framework",
					"\"CoreGraphics\"",
					"-framework",
					"\"CoreImage\"",
					"-framework",
					"\"CoreLocation\"",
					"-framework",
					"\"CoreServices\"",
					"-framework",
					"\"CoreTelephony\"",
					"-framework",
					"\"CoreText\"",
					"-framework",
					"\"FBLPromises\"",
					"-framework",
					"\"FirebaseAnalytics\"",
					"-framework",
					"\"FirebaseCore\"",
					"-framework",
					"\"FirebaseCoreInternal\"",
					"-framework",
					"\"FirebaseDynamicLinks\"",
					"-framework",
					"\"FirebaseInstallations\"",
					"-framework",
					"\"FirebaseMessaging\"",
					"-framework",
					"\"Foundation\"",
					"-framework",
					"\"GLKit\"",
					"-framework",
					"\"GoogleAppMeasurement\"",
					"-framework",
					"\"GoogleAppMeasurementIdentitySupport\"",
					"-framework",
					"\"GoogleDataTransport\"",
					"-framework",
					"\"GoogleMaps\"",
					"-framework",
					"\"GoogleMapsBase\"",
					"-framework",
					"\"GoogleMapsCore\"",
					"-framework",
					"\"GooglePlaces\"",
					"-framework",
					"\"GoogleUtilities\"",
					"-framework",
					"\"IQKeyboardManagerSwift\"",
					"-framework",
					"\"ImageIO\"",
					"-framework",
					"\"Kingfisher\"",
					"-framework",
					"\"Lottie\"",
					"-framework",
					"\"Metal\"",
					"-framework",
					"\"Mixpanel\"",
					"-framework",
					"\"OpenGLES\"",
					"-framework",
					"\"PassKit\"",
					"-framework",
					"\"PaymentSDK\"",
					"-framework",
					"\"QuartzCore\"",
					"-framework",
					"\"Reachability\"",
					"-framework",
					"\"STTabbar\"",
					"-framework",
					"\"SVGKit\"",
					"-framework",
					"\"Security\"",
					"-framework",
					"\"SocketIO\"",
					"-framework",
					"\"Starscream\"",
					"-framework",
					"\"StoreKit\"",
					"-framework",
					"\"SwiftyJSON\"",
					"-framework",
					"\"SystemConfiguration\"",
					"-framework",
					"\"Toast_Swift\"",
					"-framework",
					"\"UIKit\"",
					"-framework",
					"\"WebKit\"",
					"-framework",
					"\"iAd\"",
					"-framework",
					"\"nanopb\"",
					"-weak_framework",
					"\"Combine\"",
					"-weak_framework",
					"\"LinkPresentation\"",
					"-weak_framework",
					"\"SwiftUI\"",
					"-weak_framework",
					"\"UserNotifications\"",
					"-weak_framework",
					"\"WebKit\"",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.material.customer;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE = "";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OBJC_BRIDGING_HEADER = "$(PRODUCT_NAME)/Application/Basecode-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		F90B4CBF20329C45006ADAF6 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 8C2B88409C3FEA362CAC03EA /* Pods-TopCustomer.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = TopCustomer/TopCustomerRelease.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = J7FTCACFB3;
				ENABLE_BITCODE = NO;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "";
				INFOPLIST_FILE = "$(SRCROOT)/TopCustomer/Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Basecode/Library",
				);
				MARKETING_VERSION = 6.1.39;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_LDFLAGS = (
					"$(OTHER_LDFLAGS)",
					"$(inherited)",
					"-ObjC",
					"-l\"c++\"",
					"-l\"sqlite3\"",
					"-l\"swiftCoreGraphics\"",
					"-l\"xml2\"",
					"-l\"z\"",
					"-framework",
					"\"AACarousel\"",
					"-framework",
					"\"Accelerate\"",
					"-framework",
					"\"ActionSheetPicker_3_0\"",
					"-framework",
					"\"Alamofire\"",
					"-framework",
					"\"Branch\"",
					"-framework",
					"\"CFNetwork\"",
					"-framework",
					"\"CocoaLumberjack\"",
					"-framework",
					"\"CoreData\"",
					"-framework",
					"\"CoreGraphics\"",
					"-framework",
					"\"CoreImage\"",
					"-framework",
					"\"CoreLocation\"",
					"-framework",
					"\"CoreServices\"",
					"-framework",
					"\"CoreTelephony\"",
					"-framework",
					"\"CoreText\"",
					"-framework",
					"\"FBLPromises\"",
					"-framework",
					"\"FirebaseAnalytics\"",
					"-framework",
					"\"FirebaseCore\"",
					"-framework",
					"\"FirebaseCoreInternal\"",
					"-framework",
					"\"FirebaseDynamicLinks\"",
					"-framework",
					"\"FirebaseInstallations\"",
					"-framework",
					"\"FirebaseMessaging\"",
					"-framework",
					"\"Foundation\"",
					"-framework",
					"\"GLKit\"",
					"-framework",
					"\"GoogleAppMeasurement\"",
					"-framework",
					"\"GoogleAppMeasurementIdentitySupport\"",
					"-framework",
					"\"GoogleDataTransport\"",
					"-framework",
					"\"GoogleMaps\"",
					"-framework",
					"\"GoogleMapsBase\"",
					"-framework",
					"\"GoogleMapsCore\"",
					"-framework",
					"\"GooglePlaces\"",
					"-framework",
					"\"GoogleUtilities\"",
					"-framework",
					"\"IQKeyboardManagerSwift\"",
					"-framework",
					"\"ImageIO\"",
					"-framework",
					"\"Kingfisher\"",
					"-framework",
					"\"Lottie\"",
					"-framework",
					"\"Metal\"",
					"-framework",
					"\"Mixpanel\"",
					"-framework",
					"\"OpenGLES\"",
					"-framework",
					"\"PassKit\"",
					"-framework",
					"\"PaymentSDK\"",
					"-framework",
					"\"QuartzCore\"",
					"-framework",
					"\"Reachability\"",
					"-framework",
					"\"STTabbar\"",
					"-framework",
					"\"SVGKit\"",
					"-framework",
					"\"Security\"",
					"-framework",
					"\"SocketIO\"",
					"-framework",
					"\"Starscream\"",
					"-framework",
					"\"StoreKit\"",
					"-framework",
					"\"SwiftyJSON\"",
					"-framework",
					"\"SystemConfiguration\"",
					"-framework",
					"\"Toast_Swift\"",
					"-framework",
					"\"UIKit\"",
					"-framework",
					"\"WebKit\"",
					"-framework",
					"\"iAd\"",
					"-framework",
					"\"nanopb\"",
					"-weak_framework",
					"\"Combine\"",
					"-weak_framework",
					"\"LinkPresentation\"",
					"-weak_framework",
					"\"SwiftUI\"",
					"-weak_framework",
					"\"UserNotifications\"",
					"-weak_framework",
					"\"WebKit\"",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.material.customer;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE = "";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = RELEASE;
				SWIFT_OBJC_BRIDGING_HEADER = "$(PRODUCT_NAME)/Application/Basecode-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		7352DBB32CFFC23200320EDD /* Build configuration list for PBXNativeTarget "ContentImageExtentionPush" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7352DBB42CFFC23200320EDD /* Debug */,
				7352DBB52CFFC23200320EDD /* DevRelease */,
				7352DBB62CFFC23200320EDD /* Staging */,
				7352DBB72CFFC23200320EDD /* Release */,
				7352DBB82CFFC23200320EDD /* ReleaseStaging */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		F90B4C9020329C45006ADAF6 /* Build configuration list for PBXProject "TopCustomer" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F90B4CBB20329C45006ADAF6 /* Debug */,
				BECDA1D52947051300EB10E1 /* DevRelease */,
				F9031F152035A469002EBE92 /* Staging */,
				F90B4CBC20329C45006ADAF6 /* Release */,
				BECDA1D729470AE000EB10E1 /* ReleaseStaging */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		F90B4CBD20329C45006ADAF6 /* Build configuration list for PBXNativeTarget "TopCustomer" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F90B4CBE20329C45006ADAF6 /* Debug */,
				BECDA1D62947051300EB10E1 /* DevRelease */,
				F9031F162035A469002EBE92 /* Staging */,
				F90B4CBF20329C45006ADAF6 /* Release */,
				BECDA1D829470AE000EB10E1 /* ReleaseStaging */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		73676ABA2CE13FDE00BFF236 /* XCRemoteSwiftPackageReference "facebook-ios-sdk" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/facebook/facebook-ios-sdk";
			requirement = {
				kind = exactVersion;
				version = 17.1.0;
			};
		};
		7397710E2C7C78BF00FA8476 /* XCRemoteSwiftPackageReference "tabby-ios-sdk" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/tabby-ai/tabby-ios-sdk.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 1.7.6;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		73676ABB2CE13FDE00BFF236 /* FacebookAEM */ = {
			isa = XCSwiftPackageProductDependency;
			package = 73676ABA2CE13FDE00BFF236 /* XCRemoteSwiftPackageReference "facebook-ios-sdk" */;
			productName = FacebookAEM;
		};
		73676ABD2CE13FDE00BFF236 /* FacebookBasics */ = {
			isa = XCSwiftPackageProductDependency;
			package = 73676ABA2CE13FDE00BFF236 /* XCRemoteSwiftPackageReference "facebook-ios-sdk" */;
			productName = FacebookBasics;
		};
		73676ABF2CE13FDE00BFF236 /* FacebookCore */ = {
			isa = XCSwiftPackageProductDependency;
			package = 73676ABA2CE13FDE00BFF236 /* XCRemoteSwiftPackageReference "facebook-ios-sdk" */;
			productName = FacebookCore;
		};
		73676AC12CE13FDE00BFF236 /* FacebookLogin */ = {
			isa = XCSwiftPackageProductDependency;
			package = 73676ABA2CE13FDE00BFF236 /* XCRemoteSwiftPackageReference "facebook-ios-sdk" */;
			productName = FacebookLogin;
		};
		73676AC32CE13FDE00BFF236 /* FacebookShare */ = {
			isa = XCSwiftPackageProductDependency;
			package = 73676ABA2CE13FDE00BFF236 /* XCRemoteSwiftPackageReference "facebook-ios-sdk" */;
			productName = FacebookShare;
		};
		7397710F2C7C78BF00FA8476 /* Tabby */ = {
			isa = XCSwiftPackageProductDependency;
			package = 7397710E2C7C78BF00FA8476 /* XCRemoteSwiftPackageReference "tabby-ios-sdk" */;
			productName = Tabby;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = F90B4C8D20329C45006ADAF6 /* Project object */;
}
