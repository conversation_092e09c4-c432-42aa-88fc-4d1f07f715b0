GEM
  remote: https://rubygems.org/
  specs:
    CFPropertyList (3.0.0)
    addressable (2.5.2)
      public_suffix (>= 2.0.2, < 4.0)
    atomos (0.1.2)
    babosa (1.0.2)
    claide (1.0.2)
    colored (1.2)
    colored2 (3.1.2)
    commander-fastlane (4.4.6)
      highline (~> 1.7.2)
    declarative (0.0.10)
    declarative-option (0.1.0)
    domain_name (0.5.20180417)
      unf (>= 0.0.5, < 1.0.0)
    dotenv (2.5.0)
    emoji_regex (0.1.1)
    excon (0.62.0)
    faraday (0.15.2)
      multipart-post (>= 1.2, < 3)
    faraday-cookie_jar (0.0.6)
      faraday (>= 0.7.4)
      http-cookie (~> 1.0.0)
    faraday_middleware (0.12.2)
      faraday (>= 0.7.4, < 1.0)
    fastimage (2.1.3)
    fastlane (2.99.0)
      CFPropertyList (>= 2.3, < 4.0.0)
      addressable (>= 2.3, < 3.0.0)
      babosa (>= 1.0.2, < 2.0.0)
      bundler (>= 1.12.0, < 2.0.0)
      colored
      commander-fastlane (>= 4.4.6, < 5.0.0)
      dotenv (>= 2.1.1, < 3.0.0)
      emoji_regex (~> 0.1)
      excon (>= 0.45.0, < 1.0.0)
      faraday (~> 0.9)
      faraday-cookie_jar (~> 0.0.6)
      faraday_middleware (~> 0.9)
      fastimage (>= 2.1.0, < 3.0.0)
      gh_inspector (>= 1.1.2, < 2.0.0)
      google-api-client (>= 0.21.2, < 0.22.0)
      highline (>= 1.7.2, < 2.0.0)
      json (< 3.0.0)
      mini_magick (~> 4.5.1)
      multi_json
      multi_xml (~> 0.5)
      multipart-post (~> 2.0.0)
      plist (>= 3.1.0, < 4.0.0)
      public_suffix (~> 2.0.0)
      rubyzip (>= 1.2.1, < 2.0.0)
      security (= 0.1.3)
      simctl (~> 1.6.3)
      slack-notifier (>= 2.0.0, < 3.0.0)
      terminal-notifier (>= 1.6.2, < 2.0.0)
      terminal-table (>= 1.4.5, < 2.0.0)
      tty-screen (>= 0.6.3, < 1.0.0)
      tty-spinner (>= 0.8.0, < 1.0.0)
      word_wrap (~> 1.0.0)
      xcodeproj (>= 1.5.7, < 2.0.0)
      xcpretty (~> 0.2.8)
      xcpretty-travis-formatter (>= 0.0.3)
    gh_inspector (1.1.3)
    google-api-client (0.21.2)
      addressable (~> 2.5, >= 2.5.1)
      googleauth (>= 0.5, < 0.7.0)
      httpclient (>= 2.8.1, < 3.0)
      mime-types (~> 3.0)
      representable (~> 3.0)
      retriable (>= 2.0, < 4.0)
    googleauth (0.6.2)
      faraday (~> 0.12)
      jwt (>= 1.4, < 3.0)
      logging (~> 2.0)
      memoist (~> 0.12)
      multi_json (~> 1.11)
      os (~> 0.9)
      signet (~> 0.7)
    highline (1.7.10)
    http-cookie (1.0.3)
      domain_name (~> 0.5)
    httpclient (2.8.3)
    json (2.1.0)
    jwt (2.1.0)
    little-plugger (1.1.4)
    logging (2.2.2)
      little-plugger (~> 1.1)
      multi_json (~> 1.10)
    memoist (0.16.0)
    mime-types (3.1)
      mime-types-data (~> 3.2015)
    mime-types-data (3.2016.0521)
    mini_magick (4.5.1)
    multi_json (1.13.1)
    multi_xml (0.6.0)
    multipart-post (2.0.0)
    nanaimo (0.2.6)
    naturally (2.2.0)
    os (0.9.6)
    plist (3.4.0)
    public_suffix (2.0.5)
    representable (3.0.4)
      declarative (< 0.1.0)
      declarative-option (< 0.2.0)
      uber (< 0.2.0)
    retriable (3.1.2)
    rouge (2.0.7)
    rubyzip (1.2.1)
    security (0.1.3)
    signet (0.8.1)
      addressable (~> 2.3)
      faraday (~> 0.9)
      jwt (>= 1.5, < 3.0)
      multi_json (~> 1.10)
    simctl (1.6.5)
      CFPropertyList
      naturally
    slack-notifier (2.3.2)
    terminal-notifier (1.8.0)
    terminal-table (1.8.0)
      unicode-display_width (~> 1.1, >= 1.1.1)
    tty-cursor (0.5.0)
    tty-screen (0.6.4)
    tty-spinner (0.8.0)
      tty-cursor (>= 0.5.0)
    uber (0.1.0)
    unf (0.1.4)
      unf_ext
    unf_ext (*******)
    unicode-display_width (1.4.0)
    word_wrap (1.0.0)
    xcodeproj (1.5.9)
      CFPropertyList (>= 2.3.3, < 4.0)
      atomos (~> 0.1.2)
      claide (>= 1.0.2, < 2.0)
      colored2 (~> 3.1)
      nanaimo (~> 0.2.5)
    xcpretty (0.2.8)
      rouge (~> 2.0.7)
    xcpretty-travis-formatter (1.0.0)
      xcpretty (~> 0.2, >= 0.0.7)

PLATFORMS
  ruby

DEPENDENCIES
  fastlane

BUNDLED WITH
   1.16.1
