// APIs.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation

open class SwaggerClientAPI {
    
    // Local Swati "http://172.16.17.41/top-materials-web/code/public/api/v3"
    // Local Pavan "http://172.16.17.72/project/top_materials_web/top-materials-web/code/public/api/v2"
    // Our Staging "https://dev2.spaceo.in/project/top_materials_web/code/public/api/v2"

    public static var baseUrl: String {
        return "https://app.materiel.sa/api/v3" //Constant.shared.SELECTED_WAREHOUSE_ID == 1 ? "https://app.materiel.sa/api-market" : "https://app.materiel.sa/api-market" //"https://app.materiel.sa/api/v3"
    }
    
    public static var baseMarketUrl: String {
        return "https://app.materiel.sa/api-market" //Constant.shared.SELECTED_WAREHOUSE_ID == 1 ? "https://app.materiel.sa/api-market" : "https://app.materiel.sa/api-market" //"https://app.materiel.sa/api/v3"
    }
    
    
    
#if DEBUG
    public static var basePath: String { baseUrl }
    public static var baseMarketPath: String { baseMarketUrl }

    #elseif STAGING
    public static var basePath: String { baseUrl }
    public static var baseMarketPath: String { baseMarketUrl }

    #elseif DEVRELEASE
    public static var basePath: String { baseUrl }
    public static var baseMarketPath: String { baseMarketUrl }

    #elseif RELEASESTAGING
    public static var basePath: String { baseUrl }
    public static var baseMarketPath: String { baseMarketUrl }

    #else
    public static var basePath: String { baseUrl }
    public static var baseMarketPath: String { baseMarketUrl }
    #endif

    
    public static var credential: URLCredential?
    public static var customHeaders: [String:String] = [:]
    public static var requestBuilderFactory: RequestBuilderFactory = AlamofireRequestBuilderFactory()
}

open class RequestBuilder<T> {
    var credential: URLCredential?
    var headers: [String:String]
    public let parameters: [String:Any]?
    public let isBody: Bool
    public let method: String
    public let URLString: String

    /// Optional block to obtain a reference to the request's progress instance when available.
    public var onProgressReady: ((Progress) -> ())?

    required public init(method: String, URLString: String, parameters: [String:Any]?, isBody: Bool, headers: [String:String] = [:]) {
        self.method = method
        self.URLString = URLString
        self.parameters = parameters
        self.isBody = isBody
        self.headers = headers

        addHeaders(SwaggerClientAPI.customHeaders)
    }

    open func addHeaders(_ aHeaders:[String:String]) {
        for (header, value) in aHeaders {
            headers[header] = value
        }
        headers["city"] = "\(UserDefaults.standard.selectedCityID)"
    }

    open func execute(_ completion: @escaping (_ response: Response<T>?, _ error: Error?) -> Void) { }

    public func addHeader(name: String, value: String) -> Self {
        if !value.isEmpty {
            headers[name] = value
        }
        return self
    }

    open func addCredential() -> Self {
        self.credential = SwaggerClientAPI.credential
        return self
    }
}

public protocol RequestBuilderFactory {
    func getNonDecodableBuilder<T>() -> RequestBuilder<T>.Type
    func getBuilder<T:Decodable>() -> RequestBuilder<T>.Type
}
