//
// LatestHomeListingResponseFields.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation

public struct LatestHomeListingResponseFields: Codable {
    
    public var banners: [ListResponseFields]?
    public var categories: [CategoryListResponseFields]?
    public var sections: [DynamicSectionResponseFields]?
    public var dVatNumber: String?
    public var superCategoryList: [SuperCategoryListResponseFields]?
    public var lastOffers: [OfferResponseFields]?
    public var gifts: GiftsModel?
    
    public init(banners: [ListResponseFields]? = nil,
                categories: [CategoryListResponseFields]? = nil,
                sections: [DynamicSectionResponseFields]? = nil,
                dVatNumber: String? = nil,
                superCategoryList: [SuperCategoryListResponseFields]? = nil,
                lastOffers :  [OfferResponseFields]? = nil ,
                gifts: GiftsModel? = nil) {
        self.banners = banners
        self.categories = categories
        self.sections = sections
        self.dVatNumber = dVatNumber
        self.superCategoryList = superCategoryList
        self.gifts = gifts
        self.lastOffers = lastOffers
    }
    
    public enum CodingKeys: String, CodingKey {
        case banners
        case categories
        case sections
        case dVatNumber
        case superCategoryList = "category_list"
        case gifts
    }
    
}
public struct GiftsModel: Codable {
    
    public var giftValue: String?
    public var giftUrl: String?
    public var inviteFreind: Bool?
    
    public init(giftValue: String? = nil, giftUrl: String? = nil, inviteFreind: Bool? = nil) {
        self.giftValue = giftValue
        self.giftUrl = giftUrl
        self.inviteFreind = inviteFreind
    }
    
}
