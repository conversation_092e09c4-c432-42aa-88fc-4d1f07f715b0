//
// OrderDetailResponseFields.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct OrderDetailResponseFields: Codable {
    public var iOrderId: Int?
    public var vOrderNumber: String?
    public var vDriverMobile: String?
    public var tsOrderedAt: String?
    public var productDetails: [OrderedProductDetailResponseFields]?
    public var dDeliveryCharge: String?
    public var tsOrderDeliveredAt: String?
    public var dTotalAmount: String?
    public var dOrderSubTotal: String?
    public var dVatCharge: String?
    public var dVatPercentage: String?
    public var tiOrderStatus: Int?
    public var tiIsReccuring: Int?
    public var iReccuringType: Int?
    public var vPaymentId: String?
    public var tiIsPaybtnEnabled: Int?
    public var dOrderLatitude: String?
    public var dOrderLongitude: String?
    public var dDriverLatitude: String?
    public var dDriverLongitude: String?
    public var tsExpectedStartTime: String?
    public var tsExpectedEndTime: String?
    public var tiPaymentStatus: Int?
    public var dOrderDiscount: String?
    public var iOrderDiscountType: Int?
    public var tiTransactionType: Int?
    public var vInvoicePdfName: String?
    public var additionalSecondsEstForCar: String?
    public var additionalSecondsEstForTruck: String?
    public var tiIsWalletUsed: Int?
    public var vAlternativeISDCode: String?
    public var vAlternativeMobileNumber: String?
    public var vType: String?
    public var tAdditionalNote: String?
    public var txAddress: String?
    public var vPromocode: String?
    public var tiShiftType: Int?
    public var dMinOrderFreeDelivery: Int?
    public var dDistanceCostSetInAdmin: String?
    public var dHasFreeOrder: Int?
    public var vShiftDisplayName: String?
    public var RewardAmount: Double?
    public var isMosques: Int?
    public var newdVatCost: String?

    public init(iOrderId: Int? = nil, vOrderNumber: String? = nil, vDriverMobile: String? = nil, tsOrderedAt: String? = nil, productDetails: [OrderedProductDetailResponseFields]? = nil, dDeliveryCharge: String? = nil, tsOrderDeliveredAt: String? = nil, dTotalAmount: String? = nil, dOrderSubTotal: String? = nil, dVatCharge: String? = nil, dVatPercentage: String? = nil, tiOrderStatus: Int? = nil, tiIsReccuring: Int? = nil, iReccuringType: Int? = nil, vPaymentId: String? = nil, tiIsPaybtnEnabled: Int? = nil, dOrderLatitude: String? = nil, dOrderLongitude: String? = nil, dDriverLatitude: String? = nil, dDriverLongitude: String? = nil, tsExpectedStartTime: String? = nil, tsExpectedEndTime: String? = nil, tiPaymentStatus: Int? = nil, dOrderDiscount: String? = nil, iOrderDiscountType: Int? = nil, tiTransactionType: Int? = nil, vInvoicePdfName: String? = nil, additionalSecondsEstForCar: String? = nil, additionalSecondsEstForTruck: String? = nil, tiIsWalletUsed: Int? = nil, vAlternativeISDCode: String? = nil, vAlternativeMobileNumber: String? = nil, vType: String? = nil, tAdditionalNote: String? = nil, txAddress: String? = nil, vPromocode: String? = nil, tiShiftType: Int? = nil, dMinOrderFreeDelivery: Int? = nil, dDistanceCostSetInAdmin: String? = nil, dHasFreeOrder: Int? = nil, vShiftDisplayName: String? = nil, RewardAmount: Double? = nil, isMosques: Int? = nil, newdVatCost: String? = nil) {
        self.iOrderId = iOrderId
        self.vOrderNumber = vOrderNumber
        self.vDriverMobile = vDriverMobile
        self.tsOrderedAt = tsOrderedAt
        self.productDetails = productDetails
        self.dDeliveryCharge = dDeliveryCharge
        self.tsOrderDeliveredAt = tsOrderDeliveredAt
        self.dTotalAmount = dTotalAmount
        self.dOrderSubTotal = dOrderSubTotal
        self.dVatCharge = dVatCharge
        self.dVatPercentage = dVatPercentage
        self.tiOrderStatus = tiOrderStatus
        self.tiIsReccuring = tiIsReccuring
        self.iReccuringType = iReccuringType
        self.vPaymentId = vPaymentId
        self.tiIsPaybtnEnabled = tiIsPaybtnEnabled
        self.dOrderLatitude = dOrderLatitude
        self.dOrderLongitude = dOrderLongitude
        self.dDriverLatitude = dDriverLatitude
        self.dDriverLongitude = dDriverLongitude
        self.tsExpectedStartTime = tsExpectedStartTime
        self.tsExpectedEndTime = tsExpectedEndTime
        self.tiPaymentStatus = tiPaymentStatus
        self.dOrderDiscount = dOrderDiscount
        self.iOrderDiscountType = iOrderDiscountType
        self.tiTransactionType = tiTransactionType
        self.vInvoicePdfName = vInvoicePdfName
        self.additionalSecondsEstForCar = additionalSecondsEstForCar
        self.additionalSecondsEstForTruck = additionalSecondsEstForTruck
        self.tiIsWalletUsed = tiIsWalletUsed
        self.vAlternativeISDCode = vAlternativeISDCode
        self.vAlternativeMobileNumber = vAlternativeMobileNumber
        self.vType = vType
        self.tAdditionalNote = tAdditionalNote
        self.txAddress = txAddress
        self.vPromocode = vPromocode
        self.tiShiftType = tiShiftType
        self.dMinOrderFreeDelivery = dMinOrderFreeDelivery
        self.dDistanceCostSetInAdmin = dDistanceCostSetInAdmin
        self.dHasFreeOrder = dHasFreeOrder
        self.vShiftDisplayName = vShiftDisplayName
        self.RewardAmount = RewardAmount
        self.isMosques = isMosques
        self.newdVatCost = newdVatCost
    }
}
