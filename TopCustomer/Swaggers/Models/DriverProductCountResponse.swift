//
// DriverProductCountResponse.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct DriverProductCountResponse: Codable {

    public var responseCode: Int?
    public var responseMessage: String?
    public var responseData: DriverProductCountResponseFields?

    public init(responseCode: Int? = nil, responseMessage: String? = nil, responseData: DriverProductCountResponseFields? = nil) {
        self.responseCode = responseCode
        self.responseMessage = responseMessage
        self.responseData = responseData
    }


}
