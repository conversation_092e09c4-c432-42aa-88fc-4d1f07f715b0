//
// DriverCurrentOrderListingResponseFields.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct DriverCurrentOrderListingResponseFields: Codable {

    public var vName: String?
    public var vCarName: String?
    public var vCarPlate: String?
    public var vShiftTime: String?
    public var orders: [DriverOrderResponseFields]?

    public init(vName: String? = nil, vCarName: String? = nil, vCarPlate: String? = nil, vShiftTime: String? = nil, orders: [DriverOrderResponseFields]? = nil) {
        self.vName = vName
        self.vCarName = vCarName
        self.vCarPlate = vCarPlate
        self.vShiftTime = vShiftTime
        self.orders = orders
    }


}
