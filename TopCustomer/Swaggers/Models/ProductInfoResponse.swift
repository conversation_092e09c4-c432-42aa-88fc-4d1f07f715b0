//
// ProductInfoResponse.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct ProductInfoResponse: Codable {

    public var responseCode: Int?
    public var responseMessage: String?
    public var responseData: ProductInfoResponseFields?

    public init(responseCode: Int? = nil, responseMessage: String? = nil, responseData: ProductInfoResponseFields? = nil) {
        self.responseCode = responseCode
        self.responseMessage = responseMessage
        self.responseData = responseData
    }


}
