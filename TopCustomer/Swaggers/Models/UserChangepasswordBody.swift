//
// UserChangepasswordBody.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct UserChangepasswordBody: Codable {

    public var vCurrentPassword: String
    public var vNewPassword: String
    public var vConfirmPassword: String

    public init(vCurrentPassword: String, vNewPassword: String, vConfirmPassword: String) {
        self.vCurrentPassword = vCurrentPassword
        self.vNewPassword = vNewPassword
        self.vConfirmPassword = vConfirmPassword
    }


}
