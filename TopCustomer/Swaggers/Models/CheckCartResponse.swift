//
//  CheckCartResponse.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 21/01/2024.
//  Copyright © 2024 SOTSYS203. All rights reserved.
//

import Foundation

public struct CheckCartResponse: Codable {
    
    public var responseCode: Int?
    public var responseMessage: String?
    public var responseData: CheckCartResponseFields?
    
    public init(responseCode: Int? = nil, responseMessage: String? = nil, responseData: CheckCartResponseFields? = nil) {
        self.responseCode = responseCode
        self.responseMessage = responseMessage
        self.responseData = responseData
    }
    
}

public struct CheckCartResponseFields: Codable {
    
    public var iCartId: Int?
    public var vCartUuid: String?
    public var iUserId: Int?
    public var tiIsActive: Int?
    public var tsCreatedAt: String?
    public var tsDeletedAt: String?
    
    public init(iCartId: Int? = nil, vCartUuid: String? = nil, iUserId: Int? = nil, tiIsActive: Int? = nil, tsCreatedAt: String? = nil, tsDeletedAt: String? = nil) {
        self.iCartId = iCartId
        self.vCartUuid = vCartUuid
        self.iUserId = iUserId
        self.tiIsActive = tiIsActive
        self.tsCreatedAt = tsCreatedAt
        self.tsDeletedAt = tsDeletedAt
    }
    
}
