//
// ProductDetailsResponseFields.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct ProductDetailsResponseFields: Codable {

    public var productDetails: ProductResponseFields?
    public var products: [ProductResponseFields]?
    public var bundle: BundelsListResponseFields?
    
    public init(productDetails: ProductResponseFields? = nil,
                products: [ProductResponseFields]? = nil,
                bundle: BundelsListResponseFields? = nil) {
        self.productDetails = productDetails
        self.products = products
        self.bundle = bundle
    }

    public enum CodingKeys: String, CodingKey {
        case productDetails
        case products
        case bundle = "bunddel"
    }
}
