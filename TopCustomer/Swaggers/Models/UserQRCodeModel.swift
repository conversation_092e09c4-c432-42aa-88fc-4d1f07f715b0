//
//  UserQRCodeModel.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 09/07/2024.
//  Copyright © 2024 SOTSYS203. All rights reserved.
//

import Foundation

public struct UserQRCodeResponse : Codable {
    // MARK: - Properties
    public var responseCode: Int?
    public var responseMessage: String?
    public var qrCode : String?
    public var url : String?
    public var referralCode: Int?
    public var inviteCode: String?
    
    enum CodingKeys: String, CodingKey {
        case responseCode
        case responseMessage
        case qrCode = "QR"
        case url = "Url"
        case referralCode = "Code"
        case inviteCode = "InviteCode"
    }
    
    // MARK: - Init
    public init(from decoder: Decoder) throws {
        let values = try decoder.container(keyedBy: CodingKeys.self)
        responseCode = try values.decodeIfPresent(Int.self, forKey: .responseCode)
        responseMessage = try values.decodeIfPresent(String.self, forKey: .responseMessage)
        referralCode = try values.decodeIfPresent(Int.self, forKey: .referralCode)
        qrCode = try values.decodeIfPresent(String.self, forKey: .qrCode)
        url = try values.decodeIfPresent(String.self, forKey: .url)
        inviteCode = try values.decodeIfPresent(String.self, forKey: .inviteCode)
    }
    
}

struct UserQRCodeResponseFields {
    var referralCode = Int()
    var qrCode = String()
    var url = String()
    var inviteCode = String()
}
