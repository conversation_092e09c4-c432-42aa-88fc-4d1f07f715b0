//
// DriverCompletedOrderDeatailsResponseFields.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct DriverCompletedOrderDeatailsResponseFields: Codable {

    public var vName: String?
    public var vISDCode: String?
    public var vMobileNumber: String?
    public var vOrderNumber: String?
    public var tsOrderDeliveredAt: String?
    public var tiOrderStatus: Int?
    public var vOrderAssignedAt: String?
    public var vOrderAcceptedAt: String?
    public var vOrderInProcessAt: String?
    public var vOrderDeliveredAt: String?
    public var vOrderCompletedAt: String?
    public var vAlternativeISDCode: String?
    public var vAlternativeMobileNumber: String?

    public init(vName: String? = nil, vISDCode: String? = nil, vMobileNumber: String? = nil, vOrderNumber: String? = nil, tsOrderDeliveredAt: String? = nil, tiOrderStatus: Int? = nil, vOrderAssignedAt: String? = nil, vOrderAcceptedAt: String? = nil, vOrderInProcessAt: String? = nil, vOrderDeliveredAt: String? = nil, vOrderCompletedAt: String? = nil, vAlternativeISDCode: String? = nil, vAlternativeMobileNumber: String? = nil) {
        self.vName = vName
        self.vISDCode = vISDCode
        self.vMobileNumber = vMobileNumber
        self.vOrderNumber = vOrderNumber
        self.tsOrderDeliveredAt = tsOrderDeliveredAt
        self.tiOrderStatus = tiOrderStatus
        self.vOrderAssignedAt = vOrderAssignedAt
        self.vOrderAcceptedAt = vOrderAcceptedAt
        self.vOrderInProcessAt = vOrderInProcessAt
        self.vOrderDeliveredAt = vOrderDeliveredAt
        self.vOrderCompletedAt = vOrderCompletedAt
        self.vAlternativeISDCode = vAlternativeISDCode
        self.vAlternativeMobileNumber = vAlternativeMobileNumber
    }


}
