//
// DriverOrderStatusResponse.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct DriverOrderStatusResponse: Codable {

    public var responseCode: Int?
    public var responseMessage: String?
    public var responseData: DriverOrderResponseFields?

    public init(responseCode: Int? = nil, responseMessage: String? = nil, responseData: DriverOrderResponseFields? = nil) {
        self.responseCode = responseCode
        self.responseMessage = responseMessage
        self.responseData = responseData
    }


}
