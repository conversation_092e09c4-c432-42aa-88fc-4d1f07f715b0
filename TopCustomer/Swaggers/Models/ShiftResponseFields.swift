//
// ShiftResponseFields.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct ShiftResponseFields: Codable {

    public var iShiftId: Int?
    public var vShiftDisplayName: String?
    public var vStartAt: String?
    public var vCloseAt: String?

    public init(iShiftId: Int? = nil, vShiftDisplayName: String? = nil, vStartAt: String? = nil, vCloseAt: String? = nil) {
        self.iShiftId = iShiftId
        self.vShiftDisplayName = vShiftDisplayName
        self.vStartAt = vStartAt
        self.vCloseAt = vCloseAt
    }


}
