//
// UserDetailListingResponseFields.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct UserDetailListingResponseFields: Codable {

    public var cartProductCount: Int?
    public var isCurrentOrderAvailable: Int?
    public var latestWalletBalance: String?
    public var iOrderId: String?
    public var tiIsRated: Int?
    public var tiIsRatingSkip: Int?

    public init(cartProductCount: Int? = nil, isCurrentOrderAvailable: Int? = nil, latestWalletBalance: String? = nil, iOrderId: String? = nil, tiIsRated: Int? = nil, tiIsRatingSkip: Int? = nil) {
        self.cartProductCount = cartProductCount
        self.isCurrentOrderAvailable = isCurrentOrderAvailable
        self.latestWalletBalance = latestWalletBalance
        self.iOrderId = iOrderId
        self.tiIsRated = tiIsRated
        self.tiIsRatingSkip = tiIsRatingSkip
    }


}
