//
//  CityModel.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 11/11/2024.
//  Copyright © 2024 SOTSYS203. All rights reserved.
//

import UIKit

struct CityModel {
    
    let cityId: Int
    let cityName: String
    
    init(cityId: Int = 1, cityName: String = "") {
        self.cityId = cityId
        self.cityName = cityName
    }
    
   static var getCurrentCityName: String {
        get {
            let cityName = CityModel.getData().filter { city in
                city.cityId == UserDefaults.standard.selectedCityID
            }.first?.cityName.localized ?? ""
            return cityName
        }
    }
    
    static func getData() -> [CityModel] {
        var cites = [CityModel]()
        cites.append(CityModel(cityId: 1, cityName: "riyadh".localized))
        cites.append(CityModel(cityId: 2, cityName: "alkhabar".localized))
        return cites
    }
    
}
