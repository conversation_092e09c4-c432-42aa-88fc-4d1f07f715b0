//
// WebProductResponseFields.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct WebProductResponseFields: Codable {

    public var iProductCategoryId: String?
    public var biProductId: Int?
    public var vProductName: String?
    public var vProductUnit: String?
    public var vProductImage: String?
    public var txProductDescription: String?
    public var vProductSKU: String?
    public var dbOriginalProductPrice: String?
    public var tiDiscountType: Int?
    public var dDiscountAmount: String?
    public var dDiscountedProductPrice: String?
    public var isLowQuantity: Int?
    public var iHowMuchLeftInStock: Int?
    public var offertitle: String?
    
    public init(iProductCategoryId: String? = nil, biProductId: Int? = nil, vProductName: String? = nil, vProductUnit: String? = nil, vProductImage: String? = nil, txProductDescription: String? = nil, vProductSKU: String? = nil, dbOriginalProductPrice: String? = nil, tiDiscountType: Int? = nil, dDiscountAmount: String? = nil, dDiscountedProductPrice: String? = nil, isLowQuantity: Int? = nil, iHowMuchLeftInStock: Int? = nil, offertitle: String? = nil) {
        self.iProductCategoryId = iProductCategoryId
        self.biProductId = biProductId
        self.vProductName = vProductName
        self.vProductUnit = vProductUnit
        self.vProductImage = vProductImage
        self.txProductDescription = txProductDescription
        self.vProductSKU = vProductSKU
        self.dbOriginalProductPrice = dbOriginalProductPrice
        self.tiDiscountType = tiDiscountType
        self.dDiscountAmount = dDiscountAmount
        self.dDiscountedProductPrice = dDiscountedProductPrice
        self.isLowQuantity = isLowQuantity
        self.iHowMuchLeftInStock = iHowMuchLeftInStock
        self.offertitle = offertitle
    }


}
