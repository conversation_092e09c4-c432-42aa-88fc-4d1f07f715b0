//
// AddressResponseFields.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct AddressResponseFields: Codable {
//    public var id = UUID()
    public var iAddressId: Int?
    public var iUserId: Int?
    public var vType: String?
    public var txAddress: String?
    public var vZipCode: String?
    public var txAptSuite: String?
    public var dLatitude: String?
    public var dLongitude: String?
    public var vCity: String?
    public var vState: String?
    public var vCountryCode: String?
    public var isMosques: Int?

    public init(iAddressId: Int? = nil, iUserId: Int? = nil, vType: String? = nil, txAddress: String? = nil, vZipCode: String? = nil, txAptSuite: String? = nil, dLatitude: String? = nil, dLongitude: String? = nil, vCity: String? = nil, vState: String? = nil, vCountryCode: String? = nil, isMosques: Int? = nil) {
        self.iAddressId = iAddressId
        self.iUserId = iUserId
        self.vType = vType
        self.txAddress = txAddress
        self.vZipCode = vZipCode
        self.txAptSuite = txAptSuite
        self.dLatitude = dLatitude
        self.dLongitude = dLongitude
        self.vCity = vCity
        self.vState = vState
        self.vCountryCode = vCountryCode
        self.isMosques = isMosques
    }


}
