//
//  PointsHostoryModel.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 14/10/2023.
//  Copyright © 2023 SOTSYS203. All rights reserved.
//

import Foundation

public struct PointsHostoryResponse: Codable {
    let responseCode : Int?
    let responseMessage : String?
    let data : [PointsHistoryData]?

    public enum CodingKeys: String, CodingKey {

        case responseCode = "responseCode"
        case responseMessage = "responseMessage"
        case data = "data"
    }

    public init(from decoder: Decoder) throws {
        let values = try decoder.container(keyedBy: CodingKeys.self)
        responseCode = try values.decodeIfPresent(Int.self, forKey: .responseCode)
        responseMessage = try values.decodeIfPresent(String.self, forKey: .responseMessage)
        data = try values.decodeIfPresent([PointsHistoryData].self, forKey: .data)
    }

}

public struct PointsHistoryData: Codable {
    let id : Int?
    let iUserId : Int?
    let points : Int?
    let ponitsStatus : String?
    let iOrderId : Int?
    let type : String?
    let vOrderNumber : String?
    let amount : Double?
    let createdAt : String?

    public enum CodingKeys: String, CodingKey {

        case id = "id"
        case iUserId = "iUserId"
        case points = "points"
        case ponitsStatus = "ponits_Status"
        case iOrderId = "iOrderId"
        case type = "type"
        case vOrderNumber = "vOrderNumber"
        case amount = "amount"
        case createdAt = "created_at"
    }

    public init(from decoder: Decoder) throws {
        let values = try decoder.container(keyedBy: CodingKeys.self)
        id = try values.decodeIfPresent(Int.self, forKey: .id)
        iUserId = try values.decodeIfPresent(Int.self, forKey: .iUserId)
        points = try values.decodeIfPresent(Int.self, forKey: .points)
        ponitsStatus = try values.decodeIfPresent(String.self, forKey: .ponitsStatus)
        iOrderId = try values.decodeIfPresent(Int.self, forKey: .iOrderId)
        type = try values.decodeIfPresent(String.self, forKey: .type)
        vOrderNumber = try values.decodeIfPresent(String.self, forKey: .vOrderNumber)
        amount = try values.decodeIfPresent(Double.self, forKey: .amount)
        createdAt = try values.decodeIfPresent(String.self, forKey: .createdAt)
    }

}
