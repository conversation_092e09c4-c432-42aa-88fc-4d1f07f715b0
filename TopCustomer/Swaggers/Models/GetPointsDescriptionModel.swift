//
//  GetPointsDescriptionModel.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 08/11/2023.
//  Copyright © 2023 SOTSYS203. All rights reserved.
//

import Foundation

public struct GetPointsDescriptionResponse: Codable {
    let responseCode: Int?
    let data: [String]?
    
    public enum CodingKeys: String, CodingKey {
        case responseCode = "responseCode"
        case data = "data"
    
    }
    
    public init(from decoder: Decoder) throws {
        let values = try decoder.container(keyedBy: CodingKeys.self)
        responseCode = try values.decodeIfPresent(Int.self, forKey: .responseCode)
        data = try values.decodeIfPresent([String].self, forKey: .data)
    }
    
}
