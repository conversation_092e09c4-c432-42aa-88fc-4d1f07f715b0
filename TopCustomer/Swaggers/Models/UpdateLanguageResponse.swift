//
// UpdateLanguageResponse.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct UpdateLanguageResponse: Codable {

    public var responseCode: Int?
    public var responseMessage: String?
    public var responseData: UpdateLanguageResponseFields?

    public init(responseCode: Int? = nil, responseMessage: String? = nil, responseData: UpdateLanguageResponseFields? = nil) {
        self.responseCode = responseCode
        self.responseMessage = responseMessage
        self.responseData = responseData
    }


}
