//
// CreateOrderResponseFields.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct CreateOrderResponseFields: Codable {

    public var iOrderId: Int?
    public var vOrderNumber: String?
    public var tsOrderedAt: String?
    public var tiOrderStatus: Int?
    public var dOrderTotal: String?

    public init(iOrderId: Int? = nil, vOrderNumber: String? = nil, tsOrderedAt: String? = nil, tiOrderStatus: Int? = nil, dOrderTotal: String? = nil) {
        self.iOrderId = iOrderId
        self.vOrderNumber = vOrderNumber
        self.tsOrderedAt = tsOrderedAt
        self.tiOrderStatus = tiOrderStatus
        self.dOrderTotal = dOrderTotal
    }


}
