//
// PromocodeCheckResponse.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct PromocodeCheckResponse: Codable {

    public var responseCode: Int?
    public var responseMessage: String?
    public var responseData: PromocodeCheckResponseFields?

    public init(responseCode: Int? = nil, responseMessage: String? = nil, responseData: PromocodeCheckResponseFields? = nil) {
        self.responseCode = responseCode
        self.responseMessage = responseMessage
        self.responseData = responseData
    }


}
