//
// UserResponse.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct UserResponse: Codable {

    public var responseCode: Int?
    public var responseMessage: String?
    public var responseData: UserResponseFields?

    public init(responseCode: Int? = nil, responseMessage: String? = nil, responseData: UserResponseFields? = nil) {
        self.responseCode = responseCode
        self.responseMessage = responseMessage
        self.responseData = responseData
    }


}
