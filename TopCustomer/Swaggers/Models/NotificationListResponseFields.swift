//
// NotificationListResponseFields.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct NotificationListResponseFields: Codable {

    public var limit: Int?
    public var totalRecord: Int?
    public var totalPage: Int?
    public var notifications: [NotificationResponseFields]?

    public init(limit: Int? = nil, totalRecord: Int? = nil, totalPage: Int? = nil, notifications: [NotificationResponseFields]? = nil) {
        self.limit = limit
        self.totalRecord = totalRecord
        self.totalPage = totalPage
        self.notifications = notifications
    }


}
