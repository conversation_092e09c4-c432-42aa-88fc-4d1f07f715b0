//
// DriverHomeListingResponseFields.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct DriverHomeListingResponseFields: Codable {

    public var limit: Int?
    public var totalRecord: Int?
    public var totalPage: Int?
    public var vName: String?
    public var vCarName: String?
    public var vCarPlate: String?
    public var vShiftTime: String?
    public var orders: [DriverOrderResponseFields]?

    public init(limit: Int? = nil, totalRecord: Int? = nil, totalPage: Int? = nil, vName: String? = nil, vCarName: String? = nil, vCarPlate: String? = nil, vShiftTime: String? = nil, orders: [DriverOrderResponseFields]? = nil) {
        self.limit = limit
        self.totalRecord = totalRecord
        self.totalPage = totalPage
        self.vName = vName
        self.vCarName = vCarName
        self.vCarPlate = vCarPlate
        self.vShiftTime = vShiftTime
        self.orders = orders
    }


}
