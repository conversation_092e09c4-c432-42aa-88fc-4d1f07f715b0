//
// BannerResponse.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct BannerResponse: Codable {

    public var responseCode: Int?
    public var responseMessage: String?
    public var responseData: ListResponseFields?

    public init(responseCode: Int? = nil, responseMessage: String? = nil, responseData: ListResponseFields? = nil) {
        self.responseCode = responseCode
        self.responseMessage = responseMessage
        self.responseData = responseData
    }


}
