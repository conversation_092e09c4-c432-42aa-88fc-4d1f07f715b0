//
// OrderCreateorderBody.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct OrderCreateorderBody: Codable {

    public enum IReccuringType: Int, Codable { 
        case _1 = 1
        case _2 = 2
        case _3 = 3
        case _4 = 4
    }
    public var iCartId: Int
    public var iAddressId: Int
    /** 1 &#x3D; Only Once, 2 &#x3D; Weekly, 3 &#x3D; Bi-weekly, 4 &#x3D; Monthly */
    public var iReccuringType: IReccuringType
    public var tAdditionalNote: String?
    public var iPromocodeUseId: Int?
    public var dOrderTotal: String
    public var dVatCost: String
    /** 1 - Morning, 2 - Evening */
    public var tiShiftType: Int
    public var vAlternativeISDCode: String?
    public var vAlternativeMobileNumber: String?
    public var iShiftId: Int
    public var vShiftDisplayNameEn: String
    public var vShiftDisplayNameAr: String
    public var vStartAt: String
    public var vCloseAt: String
    public var tiIsFriday: Int
    public var tiIsRamadan: Int
    public var tiIsRegularShift: Int

    public init(iCartId: Int, iAddressId: Int, iReccuringType: IReccuringType, tAdditionalNote: String? = nil, iPromocodeUseId: Int? = nil, dOrderTotal: String, dVatCost: String, tiShiftType: Int, vAlternativeISDCode: String? = nil, vAlternativeMobileNumber: String? = nil, iShiftId: Int, vShiftDisplayNameEn: String, vShiftDisplayNameAr: String, vStartAt: String, vCloseAt: String, tiIsFriday: Int, tiIsRamadan: Int, tiIsRegularShift: Int) {
        self.iCartId = iCartId
        self.iAddressId = iAddressId
        self.iReccuringType = iReccuringType
        self.tAdditionalNote = tAdditionalNote
        self.iPromocodeUseId = iPromocodeUseId
        self.dOrderTotal = dOrderTotal
        self.dVatCost = dVatCost
        self.tiShiftType = tiShiftType
        self.vAlternativeISDCode = vAlternativeISDCode
        self.vAlternativeMobileNumber = vAlternativeMobileNumber
        self.iShiftId = iShiftId
        self.vShiftDisplayNameEn = vShiftDisplayNameEn
        self.vShiftDisplayNameAr = vShiftDisplayNameAr
        self.vStartAt = vStartAt
        self.vCloseAt = vCloseAt
        self.tiIsFriday = tiIsFriday
        self.tiIsRamadan = tiIsRamadan
        self.tiIsRegularShift = tiIsRegularShift
    }


}
