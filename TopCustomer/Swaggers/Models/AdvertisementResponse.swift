//
// AdvertisementResponse.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct AdvertisementResponse: Codable {

    public var responseCode: Int?
    public var responseMessage: String?
    public var responseData: [AdvertisementResponseFields]?

    public init(responseCode: Int? = nil, responseMessage: String? = nil, responseData: [AdvertisementResponseFields]? = nil) {
        self.responseCode = responseCode
        self.responseMessage = responseMessage
        self.responseData = responseData
    }


}
