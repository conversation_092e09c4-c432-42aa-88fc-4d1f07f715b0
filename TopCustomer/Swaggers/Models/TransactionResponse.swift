//
// TransactionResponse.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct TransactionResponse: Codable {

    public var responseCode: Int?
    public var responseMessage: String?
    public var responseData: TransactionResponseFields?

    public init(responseCode: Int? = nil, responseMessage: String? = nil, responseData: TransactionResponseFields? = nil) {
        self.responseCode = responseCode
        self.responseMessage = responseMessage
        self.responseData = responseData
    }


}
