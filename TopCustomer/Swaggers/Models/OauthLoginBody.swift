//
// OauthLoginBody.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct OauthLoginBody: Codable {

    public enum TiDeviceType: Int, Codable { 
        case _0 = 0
        case _1 = 1
        case _2 = 2
    }
    /** user-&gt;2, driver-&gt;3 */
    public var iRoleId: Int
    public var vName: String
    /** en or ar */
    public var vLanguage: String
    public var vISDCode: String
    public var vMobileNumber: String
    public var vDeviceToken: String
    /** 0 &#x3D; Web, 1 &#x3D; android, 2 &#x3D; ios */
    public var tiDeviceType: TiDeviceType
    public var vDeviceName: String?
    public var vDeviceKey: String?
    public var vReferalPlatform: String?

    public init(iRoleId: Int, vName: String, vLanguage: String, vISDCode: String, vMobileNumber: String, vDeviceToken: String, tiDeviceType: TiDeviceType, vDeviceName: String? = nil, vDeviceKey: String? = nil, vReferalPlatform: String? = nil) {
        self.iRoleId = iRoleId
        self.vName = vName
        self.vLanguage = vLanguage
        self.vISDCode = vISDCode
        self.vMobileNumber = vMobileNumber
        self.vDeviceToken = vDeviceToken
        self.tiDeviceType = tiDeviceType
        self.vDeviceName = vDeviceName
        self.vDeviceKey = vDeviceKey
        self.vReferalPlatform = vReferalPlatform
    }


}
