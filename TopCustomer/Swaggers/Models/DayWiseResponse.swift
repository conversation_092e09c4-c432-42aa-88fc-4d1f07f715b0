//
// DayWiseResponse.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct DayWiseResponse: Codable {

    public var day: String?
    public var iVehicleTypeId: String?
    public var vShiftStartsAt: String?
    public var vRole: String?
    public var tiIsOffDay: Int?

    public init(day: String? = nil, iVehicleTypeId: String? = nil, vShiftStartsAt: String? = nil, vRole: String? = nil, tiIsOffDay: Int? = nil) {
        self.day = day
        self.iVehicleTypeId = iVehicleTypeId
        self.vShiftStartsAt = vShiftStartsAt
        self.vRole = vRole
        self.tiIsOffDay = tiIsOffDay
    }


}
