//
// AddToCartResponse.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct AddToCartResponse: Codable {

    public var responseCode: Int?
    public var responseMessage: String?
    public var responseData: AddToCartResponseFields?

    public init(responseCode: Int? = nil, responseMessage: String? = nil, responseData: AddToCartResponseFields? = nil) {
        self.responseCode = responseCode
        self.responseMessage = responseMessage
        self.responseData = responseData
    }


}
