//
// WalletDetailsResponse.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct WalletDetailsResponse: Codable {

    public var responseCode: Int?
    public var responseMessage: String?
    public var responseData: WalletDetailsResponseFields?

    public init(responseCode: Int? = nil, responseMessage: String? = nil, responseData: WalletDetailsResponseFields? = nil) {
        self.responseCode = responseCode
        self.responseMessage = responseMessage
        self.responseData = responseData
    }


}
