//
// DynamicSectionResponseFields.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation

public struct DynamicSectionResponseFields: Codable, Identifiable {
    public var id = UUID()
    public var type: Int?
    public var typeName: String?
    public var offerType: String?
    public var banners: [MarketingBannerResponseFields]?
    public var products: [CategoryProductsResponseFields]?
    public var lastOffers: [OfferResponseFields]?
    public var bundelsList: [BundelsListResponseFields]?
    
    public init(type: Int? = nil, typeName: String? = nil, offerType: String?, banners: [MarketingBannerResponseFields]? = nil, products: [CategoryProductsResponseFields]?, lastOffers: [OfferResponseFields]? = nil, bundelsList: [BundelsListResponseFields]? = nil) {
        self.type = type
        self.typeName = typeName
        self.offerType = offerType
        self.banners = banners
        self.products = products
        self.lastOffers = lastOffers
        self.bundelsList = bundelsList
    }
    
    public enum CodingKeys: String, CodingKey {
        case type
        case typeName = "type_name"
        case offerType = "offertype"
        case banners = "banners"
        case products = "data"
        case lastOffers = "last_offers"
        case bundelsList = "bunddels_list"
    }
    
}

public struct SuperSectionResponseFields: Codable {
    
    public var type: Int?
    public var typeName: String?
    public var offerType: String?
    public var lastOffers: [OfferResponseFields]?
    public var data: [CategoryProductsResponseFields]?
    
    public init(type: Int? = nil, typeName: String? = nil, offerType: String?, lastOffers: [OfferResponseFields]? = nil, data: [CategoryProductsResponseFields]? = nil) {
        self.type = type
        self.typeName = typeName
        self.offerType = offerType
        self.lastOffers = lastOffers
        self.data = data
    }
    
    public enum CodingKeys: String, CodingKey {
        case type
        case typeName = "type_name"
        case offerType = "offertype"
        case lastOffers = "last_offers"
        case data = "data"
    }
    
}

public struct SuperCategoryListResponseFields: Codable, Identifiable, Hashable {
    public var id = UUID()
    public var _id: Int?
    public var name: String?
    public var image: String?
    public var isAppAdvertisement: Int?
    var isSelectedItem: Bool? = false
    
    public init(_id: Int? = nil, name: String? = nil, image: String?, isAppAdvertisement: Int? = nil) {
        self._id = _id
        self.name = name
        self.image = image
        self.isAppAdvertisement = isAppAdvertisement
    }
    
    public enum CodingKeys: String, CodingKey {
        case _id = "id"
        case name = "vName"
        case image = "vImage"
        case isAppAdvertisement = "isAppAdvertisement"
    }
    
}


public struct SuperCategoryListResponseData: Codable {
    
    public var responseCode: Int?
    public var responseMessage: String?
    public var responseData: SuperCategoryListResponse?
    
    public init(responseCode: Int? = nil, responseMessage: String? = nil, responseData: SuperCategoryListResponse? = nil) {
        self.responseCode = responseCode
        self.responseMessage = responseMessage
        self.responseData = responseData
    }
    
    public enum CodingKeys: String, CodingKey {
        case responseCode = "status_code"
        case responseMessage = "response_message"
        case responseData = "data"
    }
    
}

public struct SuperCategoryListResponse: Codable {
    
    public var limit: Int?
    public var totalRecord: Int?
    public var totalPage: Int?
    public var categories: [CategoryListResponseFields]?
    public var sections: [DynamicSectionResponseFields]?
    
    
    public init(limit: Int? = nil, totalRecord: Int? = nil, totalPage: Int? = nil, categories: [CategoryListResponseFields]? = nil, sections: [DynamicSectionResponseFields]? = nil) {
        self.limit = limit
        self.totalRecord = totalRecord
        self.totalPage = totalPage
        self.categories = categories
        self.sections = sections
    }
    
}
