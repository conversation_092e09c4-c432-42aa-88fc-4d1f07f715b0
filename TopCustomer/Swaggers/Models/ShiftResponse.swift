//
// ShiftResponse.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct ShiftResponse: Codable {

    public var responseCode: Int?
    public var responseMessage: String?
    public var responseData: [ShiftResponseFields]?

    public init(responseCode: Int? = nil, responseMessage: String? = nil, responseData: [ShiftResponseFields]? = nil) {
        self.responseCode = responseCode
        self.responseMessage = responseMessage
        self.responseData = responseData
    }


}
