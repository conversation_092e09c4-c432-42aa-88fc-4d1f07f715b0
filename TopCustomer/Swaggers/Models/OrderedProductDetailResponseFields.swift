//
// OrderedProductDetailResponseFields.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct OrderedProductDetailResponseFields: Codable {

    public var biProductId: Int?
    public var vProductName: String?
    public var dbPrice: String?
    public var vProductUnit: String?
    public var vProductImage: String?
    public var txProductDescription: String?
    public var vProductSKU: String?
    public var iProductQuantity: Int?
    public var vCategoryName: String?

    public init(biProductId: Int? = nil, vProductName: String? = nil, dbPrice: String? = nil, vProductUnit: String? = nil, vProductImage: String? = nil, txProductDescription: String? = nil, vProductSKU: String? = nil, iProductQuantity: Int? = nil, vCategoryName: String? = nil) {
        self.biProductId = biProductId
        self.vProductName = vProductName
        self.dbPrice = dbPrice
        self.vProductUnit = vProductUnit
        self.vProductImage = vProductImage
        self.txProductDescription = txProductDescription
        self.vProductSKU = vProductSKU
        self.iProductQuantity = iProductQuantity
        self.vCategoryName = vCategoryName
    }


}
