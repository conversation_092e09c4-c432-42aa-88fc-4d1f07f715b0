//
// OauthVerifyotpBody.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct OauthVerifyotpBody: Codable {

    public enum TiDeviceType: Int, Codable { 
        case _0 = 0
        case _1 = 1
        case _2 = 2
    }
    public var iRoleId: Int
    public var vISDCode: String
    public var vMobileNumber: String
    public var iOTP: Int
    public var vDeviceToken: String
    /** 0 &#x3D; Web, 1 &#x3D; android, 2 &#x3D; ios */
    public var tiDeviceType: TiDeviceType
    public var vDeviceName: String?

    public init(iRoleId: Int, vISDCode: String, vMobileNumber: String, iOTP: Int, vDeviceToken: String, tiDeviceType: TiDeviceType, vDeviceName: String? = nil) {
        self.iRoleId = iRoleId
        self.vISDCode = vISDCode
        self.vMobileNumber = vMobileNumber
        self.iOTP = iOTP
        self.vDeviceToken = vDeviceToken
        self.tiDeviceType = tiDeviceType
        self.vDeviceName = vDeviceName
    }


}
