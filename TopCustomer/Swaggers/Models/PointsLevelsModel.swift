//
//  PointsLevelsModel.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 22/10/2023.
//  Copyright © 2023 SOTSYS203. All rights reserved.
//

import Foundation

public struct PointsLevelsResponse: Codable {
    let responseCode : Int?
    let responseMessage : String?
    let responseData : PointsLevelsDataResponse?
    
    public enum CodingKeys: String, CodingKey {
        
        case responseCode = "responseCode"
        case responseMessage = "responseMessage"
        case responseData = "responseData"
    }
    
    public init(from decoder: Decoder) throws {
        let values = try decoder.container(keyedBy: CodingKeys.self)
        responseCode = try values.decodeIfPresent(Int.self, forKey: .responseCode)
        responseMessage = try values.decodeIfPresent(String.self, forKey: .responseMessage)
        responseData = try values.decodeIfPresent(PointsLevelsDataResponse.self, forKey: .responseData)
    }
}

public struct PointsLevelsDataResponse: Codable {
    let levels : PointsLevelsData?
    
    public enum CodingKeys: String, CodingKey {
        
        case levels = "levels"
    }
    
    public init(from decoder: Decoder) throws {
        let values = try decoder.container(keyedBy: CodingKeys.self)
        levels = try values.decodeIfPresent(PointsLevelsData.self, forKey: .levels)
    }
    
}

public struct PointsLevelsData : Codable {
    let levelPoint : Int?
    let maxPoint : Int?
    let rewardPoint : Int?
    let userPoint : Int?
    let remainingPoint : Int?
    let currentLevel : Int?
    let maxLevel : Int?
    
    public enum CodingKeys: String, CodingKey {
        
        case levelPoint = "LevelPoint"
        case maxPoint = "MaxPoint"
        case rewardPoint = "RewardPoint"
        case userPoint = "UserPoint"
        case remainingPoint = "RemainingPoint"
        case currentLevel = "currentLevel"
        case maxLevel = "MaxLevel"
    }
    
    public init(from decoder: Decoder) throws {
        let values = try decoder.container(keyedBy: CodingKeys.self)
        levelPoint = try values.decodeIfPresent(Int.self, forKey: .levelPoint)
        maxPoint = try values.decodeIfPresent(Int.self, forKey: .maxPoint)
        rewardPoint = try values.decodeIfPresent(Int.self, forKey: .rewardPoint)
        userPoint = try values.decodeIfPresent(Int.self, forKey: .userPoint)
        remainingPoint = try values.decodeIfPresent(Int.self, forKey: .remainingPoint)
        currentLevel = try values.decodeIfPresent(Int.self, forKey: .currentLevel)
        maxLevel = try values.decodeIfPresent(Int.self, forKey: .maxLevel)
    }
    
}
