//
//  CalculateUserPointsToSARModel.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 22/10/2023.
//  Copyright © 2023 SOTSYS203. All rights reserved.
//

import Foundation

public struct CalculateUserPointsToSARResponse : Codable {
    let responseCode : Int?
    let pointsValueBySAR : Double?
    let userPoints : Int?
    let userPointsBySAR : Double?
    let totalUserPoints: Int?
    let totalUserPointsValueBySAR: Double?
    
    public enum CodingKeys: String, CodingKey {
        
        case responseCode = "responseCode"
        case pointsValueBySAR = "pointsValueBySAR"
        case userPoints = "userpoints"
        case userPointsBySAR = "userpointsbySAR"
        case totalUserPoints = "totalUserPoints"
        case totalUserPointsValueBySAR = "totalUserPointsValueBySAR"
    }
    
    public init(from decoder: Decoder) throws {
        let values = try decoder.container(keyedBy: CodingKeys.self)
        responseCode = try values.decodeIfPresent(Int.self, forKey: .responseCode)
        pointsValueBySAR = try values.decodeIfPresent(Double.self, forKey: .pointsValueBySAR)
        userPoints = try values.decodeIfPresent(Int.self, forKey: .userPoints)
        userPointsBySAR = try values.decodeIfPresent(Double.self, forKey: .userPointsBySAR)
        totalUserPoints = try values.decodeIfPresent(Int.self, forKey: .totalUserPoints)
        totalUserPointsValueBySAR = try values.decodeIfPresent(Double.self, forKey: .totalUserPointsValueBySAR)
    }
    
}


public struct CalculateUserPointsToSARResponseFields: Codable {
    
    public var pointsValueBySAR: Double?
    public var userPoints: Int?
    public var userPointsBySAR: Double?
    public var totalUserPoints: Int?
    public var totalUserPointsValueBySAR: Double?
    
    public init(pointsValueBySAR: Double? = nil, userPoints: Int? = nil, userPointsBySAR: Double? = nil, totalUserPoints: Int? = nil, totalUserPointsValueBySAR: Double? = nil) {
        self.pointsValueBySAR = pointsValueBySAR
        self.userPoints = userPoints
        self.userPointsBySAR = userPointsBySAR
        self.totalUserPoints = totalUserPoints
        self.totalUserPointsValueBySAR = totalUserPointsValueBySAR
    }
    
}


public struct convertSARToPointsResponse: Codable {
    let responseCode : Int?
    let get : Int?
    let pay : Int?
    
    public enum CodingKeys: String, CodingKey {
        case responseCode = "responseCode"
        case get = "get"
        case pay = "pay"
    }
    
    public init(from decoder: Decoder) throws {
        let values = try decoder.container(keyedBy: CodingKeys.self)
        responseCode = try values.decodeIfPresent(Int.self, forKey: .responseCode)
        get = try values.decodeIfPresent(Int.self, forKey: .get)
        pay = try values.decodeIfPresent(Int.self, forKey: .pay)
    }
    
}


public struct convertSARToPointsResponseFields: Codable {
    
    public var get : Int?
    public var pay : Int?
    
    public init(get: Int? = nil, pay: Int? = nil) {
        self.get = get
        self.pay = pay
    }
    
}
