//
// AppVersionResponseFields.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct AppVersionResponseFields: Codable {

    public var vVersionNumber: String?
    public var tiOsType: Int?
    public var tiUpdateType: Int?

    public init(vVersionNumber: String? = nil, tiOsType: Int? = nil, tiUpdateType: Int? = nil) {
        self.vVersionNumber = vVersionNumber
        self.tiOsType = tiOsType
        self.tiUpdateType = tiUpdateType
    }


}
