//
// CountryListResponse.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct CountryListResponse: Codable {

    public var responseCode: Int?
    public var responseMessage: String?
    public var responseData: [CountryListResponseFields]?

    public init(responseCode: Int? = nil, responseMessage: String? = nil, responseData: [CountryListResponseFields]? = nil) {
        self.responseCode = responseCode
        self.responseMessage = responseMessage
        self.responseData = responseData
    }


}
