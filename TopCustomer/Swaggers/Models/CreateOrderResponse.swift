//
// CreateOrderResponse.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct CreateOrderResponse: Codable {

    public var responseCode: Int?
    public var responseMessage: String?
    public var responseData: CreateOrderResponseFields?

    public init(responseCode: Int? = nil, responseMessage: String? = nil, responseData: CreateOrderResponseFields? = nil) {
        self.responseCode = responseCode
        self.responseMessage = responseMessage
        self.responseData = responseData
    }


}
