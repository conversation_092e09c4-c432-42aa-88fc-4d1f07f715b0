//
// AddToCartResponseFields.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct AddToCartResponseFields: Codable {

    public var iCartId: Int?
    public var iUserId: Int?
    public var biProductId: Int?
    public var iProductQuantity: Int?
    public var dbPrice: String?
    public var dbFinalPrice: String?
    public var getCartProducts: Int?

    public init(iCartId: Int? = nil, iUserId: Int? = nil, biProductId: Int? = nil, iProductQuantity: Int? = nil, dbPrice: String? = nil, dbFinalPrice: String? = nil, getCartProducts: Int? = nil) {
        self.iCartId = iCartId
        self.iUserId = iUserId
        self.biProductId = biProductId
        self.iProductQuantity = iProductQuantity
        self.dbPrice = dbPrice
        self.dbFinalPrice = dbFinalPrice
        self.getCartProducts = getCartProducts
    }


}
