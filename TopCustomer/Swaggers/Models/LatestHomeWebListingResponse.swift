//
// LatestHomeWebListingResponse.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct LatestHomeWebListingResponse: Codable {

    public var responseCode: Int?
    public var responseMessage: String?
    public var responseData: LatestHomeWebListingResponseFields?

    public init(responseCode: Int? = nil, responseMessage: String? = nil, responseData: LatestHomeWebListingResponseFields? = nil) {
        self.responseCode = responseCode
        self.responseMessage = responseMessage
        self.responseData = responseData
    }


}
