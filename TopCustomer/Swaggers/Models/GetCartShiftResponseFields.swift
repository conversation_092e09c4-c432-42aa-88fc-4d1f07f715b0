//
// GetCartShiftResponseFields.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct GetCartShiftResponseFields: Codable, Identifiable {
    public var id = UUID()
    public var iShiftId: Int?
    public var vShiftDisplayNameEn: String?
    public var vShiftDisplayNameAr: String?
    public var vStartAt: String?
    public var vCloseAt: String?
    public var tiShiftType: Int?
    public var tiIsFriday: Int?
    public var tiIsRamadan: Int?
    public var tiIsRegularShift: Int?
    public var isDisabled: Int?
    public var order_cut_off_time: Int?
    public var isShiftAvailable: Bool?

    public init(iShiftId: Int? = nil, vShiftDisplayNameEn: String? = nil, vShiftDisplayNameAr: String? = nil, vStartAt: String? = nil, vCloseAt: String? = nil, tiShiftType: Int? = nil, tiIsFriday: Int? = nil, tiIsRamadan: Int? = nil, tiIsRegularShift: Int? = nil, isDisabled: Int? = nil, order_cut_off_time: Int? = nil, isShiftAvailable: Bool? = true) {
        self.iShiftId = iShiftId
        self.vShiftDisplayNameEn = vShiftDisplayNameEn
        self.vShiftDisplayNameAr = vShiftDisplayNameAr
        self.vStartAt = vStartAt
        self.vCloseAt = vCloseAt
        self.tiShiftType = tiShiftType
        self.tiIsFriday = tiIsFriday
        self.tiIsRamadan = tiIsRamadan
        self.tiIsRegularShift = tiIsRegularShift
        self.isDisabled = isDisabled
        self.order_cut_off_time = order_cut_off_time
        self.isShiftAvailable = isShiftAvailable
    }

    mutating func setIsShiftAvailable(value: Bool) {
        self.isShiftAvailable = value
    }

    enum CodingKeys: String, CodingKey {
        case iShiftId
        case vShiftDisplayNameEn
        case vShiftDisplayNameAr
        case vStartAt
        case vCloseAt
        case tiShiftType
        case tiIsFriday
        case tiIsRamadan
        case tiIsRegularShift
        case isDisabled
        case order_cut_off_time
        case isShiftAvailable
    }
}
