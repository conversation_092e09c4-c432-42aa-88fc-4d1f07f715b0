//
// LatestHomeListingResponse.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct LatestHomeListingResponse: Codable {

    public var responseCode: Int?
    public var responseMessage: String?
    public var responseData: LatestHomeListingResponseFields?

    public init(responseCode: Int? = nil, responseMessage: String? = nil, responseData: LatestHomeListingResponseFields? = nil) {
        self.responseCode = responseCode
        self.responseMessage = responseMessage
        self.responseData = responseData
    }

    public enum CodingKeys: String, CodingKey {
        case responseCode = "status_code"
        case responseMessage
        case responseData = "data"
    }
    
}
