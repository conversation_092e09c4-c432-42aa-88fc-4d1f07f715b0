//
// FavouriteMarkfavouriteBody.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct FavouriteMarkfavouriteBody: Codable {

    public var biProductId: Int
    /** 1 &#x3D; favourite, 0 &#x3D; Not favourite, 2 &#x3D; To show flag in favourite product detail */
    public var tiIsFavourite: Int

    public init(biProductId: Int, tiIsFavourite: Int) {
        self.biProductId = biProductId
        self.tiIsFavourite = tiIsFavourite
    }


}
