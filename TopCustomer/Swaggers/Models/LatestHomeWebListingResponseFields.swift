//
// LatestHomeWebListingResponseFields.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct LatestHomeWebListingResponseFields: Codable {

    public var categories: [WebCategoryListResponseFields]?
    public var newlyArrivedProducts: [WebProductResponseFields]?
    public var bestSellerProducts: [WebProductResponseFields]?

    public init(categories: [WebCategoryListResponseFields]? = nil, newlyArrivedProducts: [WebProductResponseFields]? = nil, bestSellerProducts: [WebProductResponseFields]? = nil) {
        self.categories = categories
        self.newlyArrivedProducts = newlyArrivedProducts
        self.bestSellerProducts = bestSellerProducts
    }


}
