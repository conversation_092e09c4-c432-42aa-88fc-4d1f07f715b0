//
// DriverOrderstatusupdateBody.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct DriverOrderstatusupdateBody: Codable {

    public var iOrderId: Int
    /** 3-In Progress,4-Ready,5-On the way,6-Delivered */
    public var tiOrderStatus: Int
    /** In Seconds */
    public var vEstimationTimeInSeconds: Int?

    public init(iOrderId: Int, tiOrderStatus: Int, vEstimationTimeInSeconds: Int? = nil) {
        self.iOrderId = iOrderId
        self.tiOrderStatus = tiOrderStatus
        self.vEstimationTimeInSeconds = vEstimationTimeInSeconds
    }


}
