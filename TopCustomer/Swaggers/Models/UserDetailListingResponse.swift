//
// UserDetailListingResponse.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct UserDetailListingResponse: Codable {

    public var responseCode: Int?
    public var responseMessage: String?
    public var responseData: UserDetailListingResponseFields?

    public init(responseCode: Int? = nil, responseMessage: String? = nil, responseData: UserDetailListingResponseFields? = nil) {
        self.responseCode = responseCode
        self.responseMessage = responseMessage
        self.responseData = responseData
    }


}
