//
//  CartYouMayAlsoLikeProductsResponse.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 25/08/2023.
//  Copyright © 2023 SOTSYS203. All rights reserved.
//

import Foundation

public struct CartYouMayAlsoLikeProductsResponse: Codable {
    // MARK: - Variables
    public var statusCode: Int?
    public var message: String?
    public var data: [CartYouMayAlsoLikeProductsResponseFields]?
    
    // MARK: - Init
    public init(statusCode: Int? = nil, message: String? = nil, data: [CartYouMayAlsoLikeProductsResponseFields]? = nil) {
        self.statusCode = statusCode
        self.message = message
        self.data = data
    }
    
    public enum CodingKeys: String, CodingKey {
        case statusCode = "status_code"
        case message = "message"
        case data = "data"
    }
}

public struct CartYouMayAlsoLikeProductsResponseFields: Codable {
    // MARK: - Variables
    public var id: Int?
    public var uuid: String?
    public var name: String?
    public var price: Double?
    public var image: String?
    public var categoryID: Int?
    public var categoryName: String?
    public var unit: String?
    public var discountPrice: Double?
    
    // MARK: - Init
    public init(id: Int? = nil, uuid: String? = nil, name: String? = nil, price: Double? = nil, image: String? = nil, categoryID: Int? = nil, categoryName: String? = nil, unit: String? = nil, discountPrice: Double? = nil) {
        self.id = id
        self.uuid = uuid
        self.name = name
        self.price = price
        self.image = image
        self.categoryID = categoryID
        self.categoryName = categoryName
        self.unit = unit
        self.discountPrice = discountPrice
    }
    
    public enum CodingKeys: String, CodingKey {
        case id = "id"
        case uuid = "uuid"
        case name = "name"
        case price = "price"
        case image = "image"
        case categoryID = "category_id"
        case categoryName = "category_name"
        case unit = "unit"
        case discountPrice = "discount_price"
    }
    
}
