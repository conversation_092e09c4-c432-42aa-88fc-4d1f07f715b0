//
// FavouriteResponseFields.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct FavouriteResponseFields: Codable {

    public var iFavouriteId: Int?
    public var biProductId: Int?
    public var iUserId: Int?
    public var tiIsFavourite: Int?

    public init(iFavouriteId: Int? = nil, biProductId: Int? = nil, iUserId: Int? = nil, tiIsFavourite: Int? = nil) {
        self.iFavouriteId = iFavouriteId
        self.biProductId = biProductId
        self.iUserId = iUserId
        self.tiIsFavourite = tiIsFavourite
    }


}
