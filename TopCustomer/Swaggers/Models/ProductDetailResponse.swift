//
// ProductDetailResponse.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct ProductDetailResponse: Codable {

    public var responseCode: Int?
    public var responseMessage: String?
    public var responseData: ProductDetailsResponseFields?

    public init(responseCode: Int? = nil, responseMessage: String? = nil, responseData: ProductDetailsResponseFields? = nil) {
        self.responseCode = responseCode
        self.responseMessage = responseMessage
        self.responseData = responseData
    }


}
