//
// GetCartListResponseFields.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct GetCartListResponseFields: Codable, Identifiable {
    public var id = UUID()
    public var limit: Int?
    public var totalRecord: Int?
    public var totalPage: Int?
    public var dDistanceCost: String?
    public var dMinOrderFreeDelivery: Int?
    public var dHasFreeOrder: Int?
    public var dDistanceCostSetInAdmin: String?
    public var products: [GetCartResponseFields]?
    public var shift: [GetCartShiftResponseFields]?
    public var dSubTotal: String?
    public var dSubTotalWithoutVAT: String?
    public var dVatPercentage: String?
    public var dVatCost: String?
    public var dIsCustomShift: Int?
    public var bunddels: [BundelsListResponseFields]?
    public var deliveryCharageVat: Double?
    public var isTodayAvailable: Int?
    
    enum CodingKeys: String, CodingKey {
        case limit
        case totalRecord
        case totalPage
        case dDistanceCost
        case dMinOrderFreeDelivery
        case dHasFreeOrder
        case dDistanceCostSetInAdmin
        case products
        case shift
        case dSubTotal
        case dSubTotalWithoutVAT
        case dVatPercentage
        case dVatCost = "newdVatCost"
        case dIsCustomShift
        case bunddels
        case deliveryCharageVat
        case isTodayAvailable = "is_today_available"
    }

    public init(limit: Int? = nil, totalRecord: Int? = nil, totalPage: Int? = nil, dDistanceCost: String? = nil, dMinOrderFreeDelivery: Int? = nil, dHasFreeOrder: Int? = nil, dDistanceCostSetInAdmin: String? = nil, products: [GetCartResponseFields]? = nil, shift: [GetCartShiftResponseFields]? = nil, dSubTotal: String? = nil, dSubTotalWithoutVAT: String? = nil, dVatPercentage: String? = nil, dVatCost: String? = nil, dIsCustomShift: Int? = nil, deliveryCharageVat: Double? = nil, bunddels: [BundelsListResponseFields]? = nil, isTodayAvailable: Int? = nil) {
        self.limit = limit
        self.totalRecord = totalRecord
        self.totalPage = totalPage
        self.dDistanceCost = dDistanceCost
        self.dMinOrderFreeDelivery = dMinOrderFreeDelivery
        self.dHasFreeOrder = dHasFreeOrder
        self.dDistanceCostSetInAdmin = dDistanceCostSetInAdmin
        self.products = products
        self.shift = shift
        self.dSubTotal = dSubTotal
        self.dSubTotalWithoutVAT = dSubTotalWithoutVAT
        self.dVatPercentage = dVatPercentage
        self.dVatCost = dVatCost
        self.dIsCustomShift = dIsCustomShift
        self.bunddels = bunddels
        self.deliveryCharageVat = deliveryCharageVat
        self.isTodayAvailable = isTodayAvailable
    }

}
