//
// OauthSocialsigninBody.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct OauthSocialsigninBody: Codable {

    public enum TiSocialType: Int, Codable { 
        case _1 = 1
        case _2 = 2
    }
    public enum TiDeviceType: Int, Codable { 
        case _0 = 0
        case _1 = 1
        case _2 = 2
    }
    public var vSocialId: String
    /** 1 &#x3D; Facebook, 2 &#x3D; Google */
    public var tiSocialType: TiSocialType
    public var vDeviceToken: String
    /** 0 &#x3D; Web, 1 &#x3D; android, 2 &#x3D; ios */
    public var tiDeviceType: TiDeviceType
    public var vDeviceName: String?
    public var vTimezone: String?

    public init(vSocialId: String, tiSocialType: TiSocialType, vDeviceToken: String, tiDeviceType: TiDeviceType, vDeviceName: String? = nil, vTimezone: String? = nil) {
        self.vSocialId = vSocialId
        self.tiSocialType = tiSocialType
        self.vDeviceToken = vDeviceToken
        self.tiDeviceType = tiDeviceType
        self.vDeviceName = vDeviceName
        self.vTimezone = vTimezone
    }


}
