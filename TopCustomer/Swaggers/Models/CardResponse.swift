//
// CardResponse.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct CardResponse: Codable {

    public var responseCode: Int?
    public var responseMessage: String?
    public var responseData: CardResponseFields?

    public init(responseCode: Int? = nil, responseMessage: String? = nil, responseData: CardResponseFields? = nil) {
        self.responseCode = responseCode
        self.responseMessage = responseMessage
        self.responseData = responseData
    }


}
