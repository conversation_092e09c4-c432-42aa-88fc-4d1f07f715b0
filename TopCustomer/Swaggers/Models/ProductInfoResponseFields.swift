//
// ProductInfoResponseFields.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct ProductInfoResponseFields: Codable {
    // MARK: - Properties
    public var offerDescription: String?
    public var maxQty: String?
    public var dateRange: String?
    public var offerTitle: String?
    public var offerQuantity: Int?
    public var bundle: BundelsListResponseFields?

    // MARK: - Init
    public init(offerDescription: String? = nil,
                maxQty: String? = nil,
                dateRange: String? = nil,
                offerTitle: String? = nil,
                offerQuantity: Int? = nil,
                bundle: BundelsListResponseFields? = nil) {
        self.offerDescription = offerDescription
        self.maxQty = maxQty
        self.dateRange = dateRange
        self.offerTitle = offerTitle
        self.offerQuantity = offerQuantity
        self.bundle = bundle
    }

    public enum CodingKeys: String, CodingKey { 
        case offerDescription = "offer-description"
        case maxQty = "max-qty"
        case dateRange = "date-range"
        case offerTitle = "offertitle"
        case offerQuantity = "offerQuantity"
        case bundle = "bunddel"
    }

}
