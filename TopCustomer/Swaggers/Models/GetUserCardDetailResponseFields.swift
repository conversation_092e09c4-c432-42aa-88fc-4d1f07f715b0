//
// GetUserCardDetailResponseFields.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct GetUserCardDetailResponseFields: Codable {

    public var iCardId: Int?
    public var vToken: String?
    public var vTransactionId: String?
    public var vPaymentDescription: String?
    public var vCardType: String?
    public var vCardScheme: String?

    public init(iCardId: Int? = nil, vToken: String? = nil, vTransactionId: String? = nil, vPaymentDescription: String? = nil, vCardType: String? = nil, vCardScheme: String? = nil) {
        self.iCardId = iCardId
        self.vToken = vToken
        self.vTransactionId = vTransactionId
        self.vPaymentDescription = vPaymentDescription
        self.vCardType = vCardType
        self.vCardScheme = vCardScheme
    }


}
