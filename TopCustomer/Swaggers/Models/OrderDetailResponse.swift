//
// OrderDetailResponse.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct OrderDetailResponse: Codable {
    public var responseCode: Int?
    public var responseMessage: String?
    public var responseData: OrderDetailResponseFields?
    public init(responseCode: Int? = nil, responseMessage: String? = nil, responseData: OrderDetailResponseFields? = nil) {
        self.responseCode = responseCode
        self.responseMessage = responseMessage
        self.responseData = responseData
    }
}
