//
// CountryListResponseFields.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct CountryListResponseFields: Codable {

    public var iCountryId: Int?
    public var vCountryName: String?
    public var vDialingCode: Int?
    public var vImage: String?

    public init(iCountryId: Int? = nil, vCountryName: String? = nil, vDialingCode: Int? = nil, vImage: String? = nil) {
        self.iCountryId = iCountryId
        self.vCountryName = vCountryName
        self.vDialingCode = vDialingCode
        self.vImage = vImage
    }


}
