//
// DriverWeeklyScheduleResponse.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct DriverWeeklyScheduleResponse: Codable {

    public var responseCode: Int?
    public var responseMessage: String?
    public var responseData: DriverWeeklyScheduleResponseFields?

    public init(responseCode: Int? = nil, responseMessage: String? = nil, responseData: DriverWeeklyScheduleResponseFields? = nil) {
        self.responseCode = responseCode
        self.responseMessage = responseMessage
        self.responseData = responseData
    }


}
