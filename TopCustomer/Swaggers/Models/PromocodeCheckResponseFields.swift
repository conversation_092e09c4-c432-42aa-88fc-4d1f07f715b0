//
// PromocodeCheckResponseFields.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct PromocodeCheckResponseFields: Codable {

    public var iPromocodeUseId: Int?
    public var dDiscountAmount: String?
    public var dOrderAmount: String?
    public var dDiscountedOrderAmount: String?
    public var dDistanceCost: String?
    public var tiDiscountType: Int?
    public var dVatCost: String?

    enum CodingKeys: String, CodingKey {
        case iPromocodeUseId
        case dDiscountAmount
        case dOrderAmount
        case dDiscountedOrderAmount
        case dDistanceCost
        case tiDiscountType
        case dVatCost = "newdVatCost"
    }
    
    public init(iPromocodeUseId: Int? = nil, dDiscountAmount: String? = nil, dOrderAmount: String? = nil, dDiscountedOrderAmount: String? = nil, dDistanceCost: String? = nil, tiDiscountType: Int? = nil, dVatCost: String? = nil) {
        self.iPromocodeUseId = iPromocodeUseId
        self.dDiscountAmount = dDiscountAmount
        self.dOrderAmount = dOrderAmount
        self.dDiscountedOrderAmount = dDiscountedOrderAmount
        self.dDistanceCost = dDistanceCost
        self.tiDiscountType = tiDiscountType
        self.dVatCost = dVatCost
    }


}
