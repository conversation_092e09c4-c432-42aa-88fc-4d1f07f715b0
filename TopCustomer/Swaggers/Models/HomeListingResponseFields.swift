//
// HomeListingResponseFields.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct HomeListingResponseFields: Codable {

    public var banners: [ListResponseFields]?
    public var categories: [ListResponseFields]?
    public var newlyArrivedProducts: [ProductResponseFields]?
    public var bestSellerProducts: [ProductResponseFields]?
    public var cartProductCount: Int?
    public var isCurrentOrderAvailable: Int?

    public init(banners: [ListResponseFields]? = nil, categories: [ListResponseFields]? = nil, newlyArrivedProducts: [ProductResponseFields]? = nil, bestSellerProducts: [ProductResponseFields]? = nil, cartProductCount: Int? = nil, isCurrentOrderAvailable: Int? = nil) {
        self.banners = banners
        self.categories = categories
        self.newlyArrivedProducts = newlyArrivedProducts
        self.bestSellerProducts = bestSellerProducts
        self.cartProductCount = cartProductCount
        self.isCurrentOrderAvailable = isCurrentOrderAvailable
    }


}
