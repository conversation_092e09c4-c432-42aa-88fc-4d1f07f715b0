//
// CardResponseFields.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct CardResponseFields: Codable {

    public var userCard: [GetUserCardDetailResponseFields]?
    public var latestWalletBalance: String?

    public init(userCard: [GetUserCardDetailResponseFields]? = nil, latestWalletBalance: String? = nil) {
        self.userCard = userCard
        self.latestWalletBalance = latestWalletBalance
    }

    public enum CodingKeys: String, CodingKey { 
        case userCard = "user_card"
        case latestWalletBalance
    }

}
