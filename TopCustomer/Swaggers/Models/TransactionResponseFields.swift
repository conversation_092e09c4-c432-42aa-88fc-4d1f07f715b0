//
// TransactionResponseFields.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct TransactionResponseFields: Codable {

    public var latestWalletBalance: String?
    public var paymentUrl: String?

    public init(latestWalletBalance: String? = nil, paymentUrl: String? = nil) {
        self.latestWalletBalance = latestWalletBalance
        self.paymentUrl = paymentUrl
    }
    
    enum CodingKeys: String, CodingKey {
        case latestWalletBalance
        case paymentUrl = "payment_url"
    }

}
