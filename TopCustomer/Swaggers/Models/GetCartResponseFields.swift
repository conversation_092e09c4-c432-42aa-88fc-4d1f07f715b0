//
// GetCartResponseFields.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct GetCartResponseFields: Codable, Identifiable {
    public var id = UUID()
    public var iCartId: Int?
    public var iUserId: Int?
    public var biProductId: Int?
    public var vProductImage: String?
    public var vProductName: String?
    public var vCategoryName: String?
    public var vProductSKU: String?
    public var vProductUnit: String?
    public var iProductQuantity: Int?
    public var dbPrice: String?
    public var totalProductPriceWithVAT: String?
    public var productPriceWithoutVAT: String?
    public var totalProductPriceWithoutVAT: String?
    public var tiDiscountType: Int?
    public var dDiscountAmount: String?
    public var dDiscountedTotalProductPrice: String?
    public var tiIsOfferedProduct: Int?
    public var productVatCharge: String?
    public var category_ids: [Int]?
    public var isMosques: Int?

    public init(iCartId: Int? = nil, iUserId: Int? = nil, biProductId: Int? = nil, vProductImage: String? = nil, vProductName: String? = nil, vCategoryName: String? = nil, vProductSKU: String? = nil, vProductUnit: String? = nil, iProductQuantity: Int? = nil, dbPrice: String? = nil, totalProductPriceWithVAT: String? = nil, productPriceWithoutVAT: String? = nil, totalProductPriceWithoutVAT: String? = nil, tiDiscountType: Int? = nil, dDiscountAmount: String? = nil, dDiscountedTotalProductPrice: String? = nil, tiIsOfferedProduct: Int? = nil, productVatCharge: String? = nil, category_ids: [Int]? = nil, isMosques: Int? = nil) {
        self.iCartId = iCartId
        self.iUserId = iUserId
        self.biProductId = biProductId
        self.vProductImage = vProductImage
        self.vProductName = vProductName
        self.vCategoryName = vCategoryName
        self.vProductSKU = vProductSKU
        self.vProductUnit = vProductUnit
        self.iProductQuantity = iProductQuantity
        self.dbPrice = dbPrice
        self.totalProductPriceWithVAT = totalProductPriceWithVAT
        self.productPriceWithoutVAT = productPriceWithoutVAT
        self.totalProductPriceWithoutVAT = totalProductPriceWithoutVAT
        self.tiDiscountType = tiDiscountType
        self.dDiscountAmount = dDiscountAmount
        self.dDiscountedTotalProductPrice = dDiscountedTotalProductPrice
        self.tiIsOfferedProduct = tiIsOfferedProduct
        self.productVatCharge = productVatCharge
        self.category_ids = category_ids
        self.isMosques = isMosques
    }

    enum CodingKeys: String, CodingKey {
        case iCartId
        case iUserId
        case biProductId
        case vProductImage
        case vProductName
        case vCategoryName
        case vProductSKU
        case vProductUnit
        case iProductQuantity
        case dbPrice
        case totalProductPriceWithVAT
        case productPriceWithoutVAT
        case totalProductPriceWithoutVAT
        case tiDiscountType
        case dDiscountAmount
        case dDiscountedTotalProductPrice
        case tiIsOfferedProduct
        case productVatCharge
        case category_ids
        case isMosques
    }

}
