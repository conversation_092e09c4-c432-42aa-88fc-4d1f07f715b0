//
// OauthSignupBody.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct OauthSignupBody: Codable {

    public enum TiDeviceType: Int, Codable { 
        case _0 = 0
        case _1 = 1
        case _2 = 2
    }
    public var vName: String
    public var vEmailId: String
    public var vPassword: String
    public var vDeviceToken: String
    /** 0 &#x3D; Web, 1 &#x3D; android, 2 &#x3D; ios */
    public var tiDeviceType: TiDeviceType
    public var vDeviceName: String?
    public var vDeviceKey: String?
    public var vTimezone: String?

    public init(vName: String, vEmailId: String, vPassword: String, vDeviceToken: String, tiDeviceType: TiDeviceType, vDeviceName: String? = nil, vDeviceKey: String? = nil, vTimezone: String? = nil) {
        self.vName = vName
        self.vEmailId = vEmailId
        self.vPassword = vPassword
        self.vDeviceToken = vDeviceToken
        self.tiDeviceType = tiDeviceType
        self.vDeviceName = vDeviceName
        self.vDeviceKey = vDeviceKey
        self.vTimezone = vTimezone
    }


}
