//
// WalletDetailsResponseFields.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct WalletDetailsResponseFields: Codable {

    public var limit: Int?
    public var totalRecord: Int?
    public var totalPage: Int?
    public var adminCommisionInPerc: String?
    public var totalAmount: String?
    public var details: [DetailFields]?

    public init(limit: Int? = nil, totalRecord: Int? = nil, totalPage: Int? = nil, adminCommisionInPerc: String? = nil, totalAmount: String? = nil, details: [DetailFields]? = nil) {
        self.limit = limit
        self.totalRecord = totalRecord
        self.totalPage = totalPage
        self.adminCommisionInPerc = adminCommisionInPerc
        self.totalAmount = totalAmount
        self.details = details
    }


}
