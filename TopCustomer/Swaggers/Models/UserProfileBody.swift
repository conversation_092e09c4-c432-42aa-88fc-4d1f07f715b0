//
// UserProfileBody.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct UserProfileBody: Codable {

    public enum TiGender: Int, Codable { 
        case _1 = 1
        case _2 = 2
        case _3 = 3
    }
    public var vName: String?
    public var vISDCode: String?
    public var vMobileNumber: String?
    public var vEmailId: String?
    public var iDob: String?
    /** 1 &#x3D; Male, 2 &#x3D; Female, 3 &#x3D; Other */
    public var tiGender: TiGender?

    public init(vName: String? = nil, vISDCode: String? = nil, vMobileNumber: String? = nil, vEmailId: String? = nil, iDob: String? = nil, tiGender: TiGender? = nil) {
        self.vName = vName
        self.vISDCode = vISDCode
        self.vMobileNumber = vMobileNumber
        self.vEmailId = vEmailId
        self.iDob = iDob
        self.tiGender = tiGender
    }


}
