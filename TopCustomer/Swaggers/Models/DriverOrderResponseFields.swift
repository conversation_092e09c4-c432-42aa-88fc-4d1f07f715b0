//
// DriverOrderResponseFields.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct DriverOrderResponseFields: Codable {

    public var iNextOrderIdForDriver: Int?
    public var iOrderId: Int?
    public var vName: String?
    public var vOrderNumber: String?
    public var tsOrderedAt: String?
    public var tiOrderStatus: Int?
    public var dTotalAmount: String?
    public var tiPaymentStatus: Int?
    public var dMoneyAfterDeductionFromWallet: Int?
    public var vAlternativeISDCode: String?
    public var vAlternativeMobileNumber: String?

    public init(iNextOrderIdForDriver: Int? = nil, iOrderId: Int? = nil, vName: String? = nil, vOrderNumber: String? = nil, tsOrderedAt: String? = nil, tiOrderStatus: Int? = nil, dTotalAmount: String? = nil, tiPaymentStatus: Int? = nil, dMoneyAfterDeductionFromWallet: Int? = nil, vAlternativeISDCode: String? = nil, vAlternativeMobileNumber: String? = nil) {
        self.iNextOrderIdForDriver = iNextOrderIdForDriver
        self.iOrderId = iOrderId
        self.vName = vName
        self.vOrderNumber = vOrderNumber
        self.tsOrderedAt = tsOrderedAt
        self.tiOrderStatus = tiOrderStatus
        self.dTotalAmount = dTotalAmount
        self.tiPaymentStatus = tiPaymentStatus
        self.dMoneyAfterDeductionFromWallet = dMoneyAfterDeductionFromWallet
        self.vAlternativeISDCode = vAlternativeISDCode
        self.vAlternativeMobileNumber = vAlternativeMobileNumber
    }


}
