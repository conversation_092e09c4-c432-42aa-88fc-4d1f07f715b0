//
//  FreeDeliveryValueResponse.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 24/08/2023.
//  Copyright © 2023 SOTSYS203. All rights reserved.
//

import Foundation

public struct FreeDeliveryValueResponse: Codable {
    // MARK: - Variables
    public var status_code: Int?
    public var value: String?

    // MARK: - Init
    public init(status_code: Int? = nil, value: String? = nil) {
        self.status_code = status_code
        self.value = value
    }

}
