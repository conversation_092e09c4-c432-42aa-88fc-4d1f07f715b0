//
// UserResponseFields.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct UserResponseFields: Codable {

    public var iUserId: Int?
    public var vName: String?
    public var vEmailId: String?
    public var vISDCode: String?
    public var vLanguage: String?
    public var vMobileNumber: String?
    public var vNewISDCode: String?
    public var vNewMobileNumber: String?
    public var tiIsMobileVerified: Int?
    public var tiGender: Int?
    public var iDob: String?
    public var tiIsSocialLogin: Int?
    public var vAccessToken: String?
    public var vReferalPlatform: String?
    public var inviteFriend: Bool?
    public var new: Bool?

    public init(iUserId: Int? = nil, vName: String? = nil, vEmailId: String? = nil, vISDCode: String? = nil, vLanguage: String? = nil, vMobileNumber: String? = nil, vNewISDCode: String? = nil, vNewMobileNumber: String? = nil, tiIsMobileVerified: Int? = nil, tiGender: Int? = nil, iDob: String? = nil, tiIsSocialLogin: Int? = nil, vAccessToken: String? = nil, vReferalPlatform: String? = nil, inviteFriend: Bool? = nil, new: Bool? = nil) {
        self.iUserId = iUserId
        self.vName = vName
        self.vEmailId = vEmailId
        self.vISDCode = vISDCode
        self.vLanguage = vLanguage
        self.vMobileNumber = vMobileNumber
        self.vNewISDCode = vNewISDCode
        self.vNewMobileNumber = vNewMobileNumber
        self.tiIsMobileVerified = tiIsMobileVerified
        self.tiGender = tiGender
        self.iDob = iDob
        self.tiIsSocialLogin = tiIsSocialLogin
        self.vAccessToken = vAccessToken
        self.vReferalPlatform = vReferalPlatform
        self.inviteFriend = inviteFriend
        self.new = new
    }


}
