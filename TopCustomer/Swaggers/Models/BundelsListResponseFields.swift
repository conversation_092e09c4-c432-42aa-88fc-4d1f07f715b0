//
//  BunddelsListResponseFields.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 21/02/2024.
//  Copyright © 2024 SOTSYS203. All rights reserved.
//

import Foundation

public struct BundelsListResponseFields: Codable, Identifiable {
    // MARK: - Properties
    public var id = UUID()
    public var iCartId : Int?
    public var bunddelId: Int?
    public var vBunddelName: String?
    public var price: Double?
    public var discountPrice: Double?
    public var vBunddelImage: String?
    public var tiDiscountType: Int?
    public var vBunddelSKU: String?
    public var txBunddelDescription: String?
    public var IsAppadvertisement: Int?
    public var iBunddelQuantity: Int?
    public var getCartBunddelProducts: [CartBunddelProductsFields]?
    public var iHowMuchLeftInStock: Int?
    
    
    // MARK: - Init
    public init(bunddelId: Int? = nil, vBunddelName: String? = nil, price: Double? = nil, discountPrice: Double? = nil, vBunddelImage: String? = nil, tiDiscountType: Int? = nil, vBunddelSKU: String? = nil, txBunddelDescription: String? = nil, IsAppadvertisement: Int? = nil, iBunddelQuantity: Int? = nil, iCartId: Int? = nil, getCartBunddelProducts: [CartBunddelProductsFields]? = nil, iHowMuchLeftInStock: Int? = nil) {
        self.bunddelId = bunddelId
        self.vBunddelName = vBunddelName
        self.price = price
        self.discountPrice = discountPrice
        self.vBunddelImage = vBunddelImage
        self.tiDiscountType = tiDiscountType
        self.vBunddelSKU = vBunddelSKU
        self.txBunddelDescription = txBunddelDescription
        self.IsAppadvertisement = IsAppadvertisement
        self.iBunddelQuantity = iBunddelQuantity
        self.iCartId = iCartId
        self.getCartBunddelProducts = getCartBunddelProducts
        self.iHowMuchLeftInStock = iHowMuchLeftInStock
    }
    
    public enum CodingKeys: String, CodingKey {
        case iCartId
        case bunddelId
        case vBunddelName
        case price
        case discountPrice = "discount_price"
        case vBunddelImage
        case tiDiscountType
        case vBunddelSKU
        case txBunddelDescription
        case IsAppadvertisement
        case iBunddelQuantity
        case getCartBunddelProducts
        case iHowMuchLeftInStock
    }
}

public struct CartBunddelProductsFields: Codable, Identifiable {
    // MARK: - Properties
    public var id = UUID()
    public var qty : Int?
    public var product: ProductResponseFields?
    public var youMayAlsoLikeProducts: [ProductResponseFields]?
}
