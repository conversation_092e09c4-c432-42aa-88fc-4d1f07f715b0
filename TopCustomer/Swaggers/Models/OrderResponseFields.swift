//
// OrderResponseFields.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct OrderResponseFields: Codable {

    public var iOrderId: Int?
    public var vOrderNumber: String?
    public var tsOrderedAt: String?
    public var tiOrderStatus: Int?
    public var tiPaymentStatus: Int?
    public var tiTransactionType: Int?
    public var tsExpectedStartTime: String?
    public var tsExpectedEndTime: String?
    public var dbRating: String?
    public var tComment: String?
    public var tiIsRated: Int?

    public init(iOrderId: Int? = nil, vOrderNumber: String? = nil, tsOrderedAt: String? = nil, tiOrderStatus: Int? = nil, tiPaymentStatus: Int? = nil, tiTransactionType: Int? = nil, tsExpectedStartTime: String? = nil, tsExpectedEndTime: String? = nil, dbRating: String? = nil, tComment: String? = nil, tiIsRated: Int? = nil) {
        self.iOrderId = iOrderId
        self.vOrderNumber = vOrderNumber
        self.tsOrderedAt = tsOrderedAt
        self.tiOrderStatus = tiOrderStatus
        self.tiPaymentStatus = tiPaymentStatus
        self.tiTransactionType = tiTransactionType
        self.tsExpectedStartTime = tsExpectedStartTime
        self.tsExpectedEndTime = tsExpectedEndTime
        self.dbRating = dbRating
        self.tComment = tComment
        self.tiIsRated = tiIsRated
    }


}
