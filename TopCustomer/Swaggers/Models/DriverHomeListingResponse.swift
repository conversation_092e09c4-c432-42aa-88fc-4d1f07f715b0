//
// DriverHomeListingResponse.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct DriverHomeListingResponse: Codable {

    public var responseCode: Int?
    public var responseMessage: String?
    public var responseData: DriverHomeListingResponseFields?

    public init(responseCode: Int? = nil, responseMessage: String? = nil, responseData: DriverHomeListingResponseFields? = nil) {
        self.responseCode = responseCode
        self.responseMessage = responseMessage
        self.responseData = responseData
    }


}
