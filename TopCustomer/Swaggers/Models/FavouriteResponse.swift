//
// FavouriteResponse.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct FavouriteResponse: Codable {

    public var responseCode: Int?
    public var responseMessage: String?
    public var responseData: FavouriteResponseFields?

    public init(responseCode: Int? = nil, responseMessage: String? = nil, responseData: FavouriteResponseFields? = nil) {
        self.responseCode = responseCode
        self.responseMessage = responseMessage
        self.responseData = responseData
    }


}
