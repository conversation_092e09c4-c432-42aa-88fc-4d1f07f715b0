//
//  NewSubCategoryModel.swift
//  TopCustomer
//
//  Created by macintosh on 31/07/2025.
//  Copyright © 2025 SOTSYS203. All rights reserved.
//

import Foundation


public struct NewSubCategoryModel: Codable {
    let status: Int?
    let responseMessage: String?
    let data: NewSubCategoryProductsListResponseFields?
    
    enum CodingKeys: String, CodingKey {
        case status
        case responseMessage = "message"
        case data
    }
}

public struct NewSubCategoryProductsListResponseFields: Codable {
    let category: NewCategory?
    let products: [NewProduct]?
    
    enum CodingKeys: String, CodingKey {
        case category
        case products
    }
}

public struct NewCategory: Codable {
    let id: Int?
    let name: String?
    let image: String?
    let subCategories: [SubCategory]?
    
    enum CodingKeys: String, CodingKey {
        case id
        case name = "vName"
        case image = "vImage"
        case subCategories = "sub_categories"
    }
}

public struct SubCategory: Codable , Equatable {
    let id: Int?
    let name: String?
    let image: String?
    
    enum CodingKeys: String, CodingKey {
        case id
        case name = "vName"
        case image = "vImage"
    }
}

public struct NewProduct: Codable {
    let id: Int?
    let name: String?
    let price: Double?
    let discountPrice: Double?
    let unit: String?
    let image: String?
    let isAppAdvertisement: Int?
    let offerTitle: String?
    let isLowQuantity: Int?
    let discountType: Int?
    let isMosques: Int?
    let stockLeft: Int?
    let sku: String?
    let quantityDiscount: Int?
    let prices: [Price]?
    let images: [String]?
    let description: String?
    
    enum CodingKeys: String, CodingKey {
        case id = "biProductId"
        case name = "vProductName"
        case price
        case discountPrice = "discount_price"
        case unit = "vProductUnit"
        case image = "vProductImage"
        case isAppAdvertisement
        case offerTitle = "offertitle"
        case isLowQuantity
        case discountType = "tiDiscountType"
        case isMosques = "IsMosques"
        case stockLeft = "iHowMuchLeftInStock"
        case sku = "vProductSKU"
        case quantityDiscount
        case prices
        case images = "imagesApp"
        case description = "txProductDescription"
    }
}

public struct Price: Codable {
    let id: Int?
    let productId: Int?
    let price: String?
    let warehouseId: Int?
    let quantityFrom: Int?
    let quantityTo: Int?
    let minQuantity: Int?
    let deletedAt: String?
    let createdAt: String?
    let updatedAt: String?
    
    enum CodingKeys: String, CodingKey {
        case id
        case productId = "biProductId"
        case price
        case warehouseId = "warehouse_id"
        case quantityFrom = "quantity_from"
        case quantityTo = "quantity_to"
        case minQuantity = "min_quantity"
        case deletedAt = "deleted_at"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
}
