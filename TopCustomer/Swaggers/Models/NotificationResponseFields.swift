//
// NotificationResponseFields.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct NotificationResponseFields: Codable {

    public var iNotificationId: Int?
    public var tiNotificationType: Int?
    public var vTitle: String?
    public var txBody: String?
    public var tiIsRead: Int?

    public init(iNotificationId: Int? = nil, tiNotificationType: Int? = nil, vTitle: String? = nil, txBody: String? = nil, tiIsRead: Int? = nil) {
        self.iNotificationId = iNotificationId
        self.tiNotificationType = tiNotificationType
        self.vTitle = vTitle
        self.txBody = txBody
        self.tiIsRead = tiIsRead
    }


}
