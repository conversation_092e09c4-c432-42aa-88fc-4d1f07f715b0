//
// OauthGuestloginBody.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct OauthGuestloginBody: Codable {

    public enum TiDeviceType: Int, Codable { 
        case _0 = 0
        case _1 = 1
        case _2 = 2
    }
    public var vDeviceToken: String
    /** 0 &#x3D; Web, 1 &#x3D; android, 2 &#x3D; ios */
    public var tiDeviceType: TiDeviceType
    public var vDeviceName: String

    public init(vDeviceToken: String, tiDeviceType: TiDeviceType, vDeviceName: String) {
        self.vDeviceToken = vDeviceToken
        self.tiDeviceType = tiDeviceType
        self.vDeviceName = vDeviceName
    }


}
