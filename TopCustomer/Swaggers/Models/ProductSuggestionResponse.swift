//
// ProductSuggestionResponse.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct ProductSuggestionResponse: Codable {

    public var responseCode: Int?
    public var responseMessage: String?
    public var responseData: [ProductSuggestionResponseFields]?

    public init(responseCode: Int? = nil, responseMessage: String? = nil, responseData: [ProductSuggestionResponseFields]? = nil) {
        self.responseCode = responseCode
        self.responseMessage = responseMessage
        self.responseData = responseData
    }


}
