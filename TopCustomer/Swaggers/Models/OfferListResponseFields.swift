//
// OfferListResponseFields.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct OfferListResponseFields: Codable, Identifiable {
    public var id = UUID()
    public var limit: Int?
    public var totalRecord: Int?
    public var totalPage: Int?
    public var offers: [OfferResponseFields]?

    public init(limit: Int? = nil, totalRecord: Int? = nil, totalPage: Int? = nil, offers: [OfferResponseFields]? = nil) {
        self.limit = limit
        self.totalRecord = totalRecord
        self.totalPage = totalPage
        self.offers = offers
    }
    
    enum CodingKeys: String, CodingKey {
          case limit
          case totalRecord, totalPage, offers
      }


}
