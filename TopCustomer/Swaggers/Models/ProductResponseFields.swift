//
// ProductResponseFields.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation

public struct CategoryProductsResponseFields: Codable, Identifiable {
    
    // MARK: - Properties
    public var id = UUID()
    public var categoryId: Int?
    public var categoryName: String?
    public var vImage: String?
    public var products: [ProductResponseFields]?
    public var isAppadvertisement: Int?

    // MARK: - Init
    public init(categoryId: Int? = nil,
                categoryName: String? = nil,
                vImage: String? = nil,
                products: [ProductResponseFields]? = nil,
                isAppadvertisement: Int? = nil) {
        self.categoryId = categoryId
        self.categoryName = categoryName
        self.vImage = vImage
        self.products = products
        self.isAppadvertisement = isAppadvertisement
    }
    
    public enum CodingKeys: String, CodingKey {
        case categoryId = "category_id"
        case categoryName = "category_name"
        case vImage = "vImage"
        case products = "products"
        case isAppadvertisement = "isAppAdvertisement"
    }
    

}

public struct ProductResponseFields: Codable, Identifiable {
    public var id = UUID()
    public var _id: Int?
    public var biProductId: Int?
    public var vProductName: String?
    public var vProductUnit: String?
    public var vProductImage: String?
    public var txProductDescription: String?
    public var vProductSKU: String?
    public var dbOriginalProductPrice: String?
    public var price: Double?
    public var tiDiscountType: Int?
    public var dDiscountAmount: String?
    public var dDiscountedProductPrice: String?
    public var discountPrice: Double?
    public var isLowQuantity: Int?
    public var iHowMuchLeftInStock: Int?
    public var tiIsFavourite: Int?
    public var maxQtyInfo: String?
    public var youMayAlsoLikeProducts: [ProductResponseFields]?
    public var offertitle: String?
    public var isAppAdvertisement: Int?
    public var name: String?
    public var bunddelId: Int?
    public var vBunddelName: String?
    public var vBunddelImage: String?
    public var vBunddelSKU: String?
    public var txBunddelDescription: String?
    public var isMosques: Int?
    public var qty: Int?
    public var quantityDiscount: Int?
    public var prices: [PricesResponseFields]?
    public var imagesApp: [String]?

    public init(biProductId: Int? = nil, vProductName: String? = nil, vProductUnit: String? = nil, vProductImage: String? = nil, txProductDescription: String? = nil, vProductSKU: String? = nil, price: Double? = nil, tiDiscountType: Int? = nil, dDiscountAmount: String? = nil, discountPrice: Double? = nil, isLowQuantity: Int? = nil, iHowMuchLeftInStock: Int? = nil, tiIsFavourite: Int? = nil, maxQtyInfo: String? = nil, youMayAlsoLikeProducts: [ProductResponseFields]? = nil, offertitle: String? = nil,
                isAppAdvertisement: Int? = nil, name: String? = nil, dbOriginalProductPrice: String? = nil, dDiscountedProductPrice: String? = nil, id: Int? = nil, isMosques: Int? = nil, bunddelId: Int? = nil, vBunddelName: String? = nil, vBunddelImage: String? = nil, vBunddelSKU: String? = nil, txBunddelDescription: String? = nil,
                qty: Int? = nil, quantityDiscount: Int? = nil, prices: [PricesResponseFields]? = nil, imagesApp: [String]? = nil) {
    
        self._id = id
        self.biProductId = biProductId
        self.vProductName = vProductName
        self.vProductUnit = vProductUnit
        self.vProductImage = vProductImage
        self.txProductDescription = txProductDescription
        self.vProductSKU = vProductSKU
        self.dDiscountedProductPrice = dDiscountedProductPrice
        self.price = price
        self.tiDiscountType = tiDiscountType
        self.dDiscountAmount = dDiscountAmount
        self.dbOriginalProductPrice = dbOriginalProductPrice
        self.discountPrice = discountPrice
        self.isLowQuantity = isLowQuantity
        self.iHowMuchLeftInStock = iHowMuchLeftInStock
        self.tiIsFavourite = tiIsFavourite
        self.maxQtyInfo = maxQtyInfo
        self.youMayAlsoLikeProducts = youMayAlsoLikeProducts
        self.offertitle = offertitle
        self.isAppAdvertisement = isAppAdvertisement
        self.name = name
        self.bunddelId = bunddelId
        self.vBunddelName = vBunddelName
        self.vBunddelImage = vBunddelImage
        self.vBunddelSKU = vBunddelSKU
        self.txBunddelDescription = txBunddelDescription
        self.isMosques = isMosques
        self.qty = qty
        self.quantityDiscount = quantityDiscount
        self.prices = prices
        self.imagesApp = imagesApp
    }

    public enum CodingKeys: String, CodingKey {
        case _id = "id"
        case biProductId
        case vProductName = "vProductName"
        case vProductUnit
        case vProductImage
        case txProductDescription
        case vProductSKU
        case price
        case tiDiscountType
        case dDiscountAmount
        case discountPrice = "discount_price"
        case isLowQuantity
        case iHowMuchLeftInStock
        case tiIsFavourite
        case maxQtyInfo
        case youMayAlsoLikeProducts
        case offertitle
        case isAppAdvertisement
        case name
        case dDiscountedProductPrice
        case dbOriginalProductPrice
        case bunddelId
        case vBunddelName
        case vBunddelImage
        case vBunddelSKU
        case txBunddelDescription
        case isMosques = "IsMosques"
        case qty
        case quantityDiscount
        case prices
        case imagesApp
    }
    
}


public struct CategoryResponseFields: Codable, Identifiable {
    
    // MARK: - Properties
    public var id = UUID()
    public var iProductCategoryId: Int?
    public var vProductCategoryName: String?
    public var vImage: String?
    public var products: [ProductResponseFields]?
    public var IsAppadvertisement: Int?

    // MARK: - Init
    public init(iProductCategoryId: Int? = nil,
                vProductCategoryName: String? = nil,
                vImage: String? = nil,
                products: [ProductResponseFields]? = nil,
                IsAppadvertisement: Int? = nil) {
        self.iProductCategoryId = iProductCategoryId
        self.vProductCategoryName = vProductCategoryName
        self.vImage = vImage
        self.products = products
        self.IsAppadvertisement = IsAppadvertisement
    }
    
    public enum CodingKeys: String, CodingKey {
        case iProductCategoryId
        case vProductCategoryName
        case vImage
        case products
        case IsAppadvertisement
    }

}


public struct AppadvertisementProduct: Codable, Identifiable {
    public var id = UUID()
    // MARK: - Properties
    public var iProductCategoryId: String?
    public var biProductId: Int?
    public var vProductImage: [String]?

    // MARK: - Init
    public init(iProductCategoryId: String? = nil,
                biProductId: Int? = nil,
                vProductImage: [String]? = nil) {
        self.iProductCategoryId = iProductCategoryId
        self.biProductId = biProductId
        self.vProductImage = vProductImage
    }
    
    public enum CodingKeys: String, CodingKey {
        case iProductCategoryId
        case biProductId
        case vProductImage
    }

}

public struct PricesResponseFields: Codable, Identifiable {
    
    // MARK: - Properties
    public var id = UUID()
    public var _id: Int?
    public var biProductId: Int?
    public var price: String?
    public var warehouse_id: Int?
    public var quantity_from: Int?
    public var quantity_to: Int?
    public var min_quantity: Int?

    // MARK: - Init
    public init(id: Int? = nil, biProductId: Int? = nil, price: String? = nil, warehouse_id: Int? = nil, quantity_from: Int? = nil, quantity_to: Int? = nil, min_quantity: Int? = nil) {
        self._id = id
        self.biProductId = biProductId
        self.price = price
        self.warehouse_id = warehouse_id
        self.quantity_from = quantity_from
        self.quantity_to = quantity_to
        self.min_quantity = min_quantity
    }
    
    public enum CodingKeys: String, CodingKey {
        case _id = "id"
        case biProductId
        case price
        case warehouse_id
        case quantity_from
        case quantity_to
        case min_quantity
    }
    
}
