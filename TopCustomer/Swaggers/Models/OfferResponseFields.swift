//
// OfferResponseFields.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct OfferResponseFields: Codable, Identifiable {
    public var id = UUID()
    public var iOfferId: Int?
    public var tiOfferType: Int?
    public var biProductId: Int?
    public var iBuyProductQuantity: Int?
    public var biOfferProductId: Int?
    public var iOfferProductQuantity: Int?
    public var vOfferName: String?
    public var dOfferAmount: String?
    public var vOfferImage: String?
    public var tiDiscountType: Int?
    public var txOfferDescription: String?
    public var tsStartDate: String?
    public var tsEndDate: String?
    public var vBuyProductName: String?
    public var vBuyProductSKU: String?
    public var vGetProductName: String?
    public var vGetProductSKU: String?
    public var productOriginalAmount: String?
    public var productOfferedAmount: String?
    public var productunit: String?
    public var IsAppadvertisement: Int?
    public var imagesApp: [String]?

    public init(iOfferId: Int? = nil, tiOfferType: Int? = nil, biProductId: Int? = nil, iBuyProductQuantity: Int? = nil, biOfferProductId: Int? = nil, iOfferProductQuantity: Int? = nil, vOfferName: String? = nil, dOfferAmount: String? = nil, vOfferImage: String? = nil, tiDiscountType: Int? = nil, txOfferDescription: String? = nil, tsStartDate: String? = nil, tsEndDate: String? = nil, vBuyProductName: String? = nil, vBuyProductSKU: String? = nil, vGetProductName: String? = nil, vGetProductSKU: String? = nil, productOriginalAmount: String? = nil, productOfferedAmount: String? = nil, productunit: String? = nil, IsAppadvertisement: Int? = nil, imagesApp: [String]? = nil) {
        self.iOfferId = iOfferId
        self.tiOfferType = tiOfferType
        self.biProductId = biProductId
        self.iBuyProductQuantity = iBuyProductQuantity
        self.biOfferProductId = biOfferProductId
        self.iOfferProductQuantity = iOfferProductQuantity
        self.vOfferName = vOfferName
        self.dOfferAmount = dOfferAmount
        self.vOfferImage = vOfferImage
        self.tiDiscountType = tiDiscountType
        self.txOfferDescription = txOfferDescription
        self.tsStartDate = tsStartDate
        self.tsEndDate = tsEndDate
        self.vBuyProductName = vBuyProductName
        self.vBuyProductSKU = vBuyProductSKU
        self.vGetProductName = vGetProductName
        self.vGetProductSKU = vGetProductSKU
        self.productOriginalAmount = productOriginalAmount
        self.productOfferedAmount = productOfferedAmount
        self.productunit = productunit
        self.IsAppadvertisement = IsAppadvertisement
        self.imagesApp = imagesApp
    }
    
    public enum CodingKeys: String, CodingKey {
        case iOfferId
        case tiOfferType
        case biProductId
        case iBuyProductQuantity
        case biOfferProductId
        case iOfferProductQuantity
        case vOfferName
        case dOfferAmount
        case vOfferImage
        case tiDiscountType
        case txOfferDescription
        case tsStartDate
        case tsEndDate
        case vBuyProductName
        case vBuyProductSKU
        case vGetProductName
        case vGetProductSKU
        case productOriginalAmount
        case productOfferedAmount
        case productunit
        case IsAppadvertisement
        case imagesApp
    }

}
