//
// CartAddtocartBody.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct CartAddtocartBody: Codable {

    public var biProductId: Int
    /** 0 - remove , 1 - Add */
    public var tiIsCheck: Int?
    public var iProductQuantity: Int
    public var dbPrice: String

    public init(biProductId: Int, tiIsCheck: Int? = nil, iProductQuantity: Int, dbPrice: String) {
        self.biProductId = biProductId
        self.tiIsCheck = tiIsCheck
        self.iProductQuantity = iProductQuantity
        self.dbPrice = dbPrice
    }


}
