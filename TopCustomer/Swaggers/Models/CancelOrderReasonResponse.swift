//
// CancelOrderReasonResponse.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct CancelOrderReasonResponse: Codable {

    public var responseCode: Int?
    public var responseMessage: String?
    public var responseData: [CancelOrderReasonResponseFields]?

    public init(responseCode: Int? = nil, responseMessage: String? = nil, responseData: [CancelOrderReasonResponseFields]? = nil) {
        self.responseCode = responseCode
        self.responseMessage = responseMessage
        self.responseData = responseData
    }


}
