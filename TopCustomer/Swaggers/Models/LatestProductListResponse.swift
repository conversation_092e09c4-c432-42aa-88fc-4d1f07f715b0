//
// LatestProductListResponse.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct LatestProductListResponse: Codable {

    public var responseCode: Int?
    public var responseMessage: String?
    public var productList: [ProductResponseFields]?

    public init(responseCode: Int? = nil, responseMessage: String? = nil, productList: [ProductResponseFields]? = nil) {
        self.responseCode = responseCode
        self.responseMessage = responseMessage
        self.productList = productList
    }


}
