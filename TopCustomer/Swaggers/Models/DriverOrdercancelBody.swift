//
// DriverOrdercancelBody.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct DriverOrdercancelBody: Codable {

    public var iOrderId: Int
    /** optional(shift id) */
    public var iShiftId: Int?
    /** the reason behind the order which is cancelled by driver. */
    public var tCancelledReason: String?

    public init(iOrderId: Int, iShiftId: Int? = nil, tCancelledReason: String? = nil) {
        self.iOrderId = iOrderId
        self.iShiftId = iShiftId
        self.tCancelledReason = tCancelledReason
    }


}
