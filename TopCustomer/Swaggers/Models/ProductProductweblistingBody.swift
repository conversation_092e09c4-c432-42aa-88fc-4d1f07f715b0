//
// ProductProductweblistingBody.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct ProductProductweblistingBody: Codable {

    /** 1 - new, 2 - Best Seller */
    public var iProductType: Int?
    public var iCategoryId: Int?
    public var vSerchString: String?

    public init(iProductType: Int? = nil, iCategoryId: Int? = nil, vSerchString: String? = nil) {
        self.iProductType = iProductType
        self.iCategoryId = iCategoryId
        self.vSerchString = vSerchString
    }


}
