//
// OrderListResponseFields.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct OrderListResponseFields: Codable {

    public var limit: Int?
    public var totalRecord: Int?
    public var totalPage: Int?
    public var orders: [OrderResponseFields]?

    public init(limit: Int? = nil, totalRecord: Int? = nil, totalPage: Int? = nil, orders: [OrderResponseFields]? = nil) {
        self.limit = limit
        self.totalRecord = totalRecord
        self.totalPage = totalPage
        self.orders = orders
    }


}
