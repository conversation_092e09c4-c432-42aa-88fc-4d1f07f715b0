//
// OrderGetorderlistBody.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct OrderGetorderlistBody: Codable {

    public enum IOrderType: Int, Codable { 
        case _1 = 1
        case _2 = 2
        case _3 = 3
    }
    /** 1 &#x3D; Current Order, 2 &#x3D; Scheduled Order, 3 &#x3D; History Order */
    public var iOrderType: IOrderType

    public init(iOrderType: IOrderType) {
        self.iOrderType = iOrderType
    }


}
