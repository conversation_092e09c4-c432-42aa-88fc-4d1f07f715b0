//
// DriverCountResponseFields.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct DriverCountResponseFields: Codable {

    public var biProductId: Int?
    public var iOrderId: Int?
    public var totalProduct: String?
    public var vProductName: String?
    public var vProductUnit: String?

    public init(biProductId: Int? = nil, iOrderId: Int? = nil, totalProduct: String? = nil, vProductName: String? = nil, vProductUnit: String? = nil) {
        self.biProductId = biProductId
        self.iOrderId = iOrderId
        self.totalProduct = totalProduct
        self.vProductName = vProductName
        self.vProductUnit = vProductUnit
    }


}
