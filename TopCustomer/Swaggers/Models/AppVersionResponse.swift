//
// AppVersionResponse.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct AppVersionResponse: Codable {

    public var responseCode: Int?
    public var responseMessage: String?
    public var responseData: AppVersionResponseFields?

    public init(responseCode: Int? = nil, responseMessage: String? = nil, responseData: AppVersionResponseFields? = nil) {
        self.responseCode = responseCode
        self.responseMessage = responseMessage
        self.responseData = responseData
    }


}
