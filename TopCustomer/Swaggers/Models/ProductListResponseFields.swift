//
// ProductListResponseFields.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation


public struct NewProductListResponseFields: Codable {
    let products: [NewProductResponse]?
}

public struct NewProductResponse: Codable {
    let biProductId: Int?
    let vProductName: String?
    let price: Double?
    let discount_price: Double?
    let vProductUnit: String?
    let vProductImage: String?
    let isAppAdvertisement: Int?
    let offertitle: String?
    let isLowQuantity: Int?
    let tiDiscountType: Int?
    let IsMosques: Int?
    let iHowMuchLeftInStock: Int?
}


public struct ProductListResponseFields: Codable {

    public var limit: Int?
    public var totalRecord: Int?
    public var totalPage: Int?
    public var productList: [ProductResponseFields]?
    public var AppadvertisementProduct: [AppadvertisementProduct]?
    public var bunddelsList: [ProductResponseFields]?

    public init(limit: Int? = nil, totalRecord: Int? = nil, totalPage: Int? = nil, productList: [ProductResponseFields]? = nil,
                AppadvertisementProduct: [AppadvertisementProduct]? = nil) {
        self.limit = limit
        self.totalRecord = totalRecord
        self.totalPage = totalPage
        self.productList = productList
        self.AppadvertisementProduct = AppadvertisementProduct
    }

}
