//
// CheckappversionBody.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct CheckappversionBody: Codable {

    public enum TiOsType: Int, Codable { 
        case _1 = 1
        case _2 = 2
    }
    /** 1 - Android, 2 - IOS */
    public var tiOsType: TiOsType

    public init(tiOsType: TiOsType) {
        self.tiOsType = tiOsType
    }


}
