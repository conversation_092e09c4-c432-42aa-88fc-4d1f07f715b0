//
// ListResponseFields.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct ListResponseFields: Codable, Identifiable, Hashable {
    public var id = UUID()
    public var _id: Int?
    public var vName: String?
    public var vImage: String?
    public var biProductId: Int?
    public var iProductCategoryId: Int?
    public var iOfferId: Int?
    public var vCategoryName: String?
    public var tBannerDescription: String?
    public var isAppAdvertisement: Int?

    public init(_id: Int? = nil, vName: String? = nil, vImage: String? = nil, biProductId: Int? = nil, iProductCategoryId: Int? = nil, iOfferId: Int? = nil, vCategoryName: String? = nil, tBannerDescription: String? = nil, isAppAdvertisement: Int? = nil) {
        self._id = _id
        self.vName = vName
        self.vImage = vImage
        self.biProductId = biProductId
        self.iProductCategoryId = iProductCategoryId
        self.iOfferId = iOfferId
        self.vCategoryName = vCategoryName
        self.tBannerDescription = tBannerDescription
        self.isAppAdvertisement = isAppAdvertisement
    }

    public enum CodingKeys: String, CodingKey { 
        case _id = "id"
        case vName
        case vImage
        case biProductId
        case iProductCategoryId
        case iOfferId
        case vCategoryName
        case tBannerDescription
        case isAppAdvertisement
    }

}
