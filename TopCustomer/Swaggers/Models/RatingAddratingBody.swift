//
// RatingAddratingBody.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct RatingAddratingBody: Codable {

    public var iOrderId: Int
    public var dbRating: String
    public var tComment: String?

    public init(iOrderId: Int, dbRating: String, tComment: String? = nil) {
        self.iOrderId = iOrderId
        self.dbRating = dbRating
        self.tComment = tComment
    }


}
