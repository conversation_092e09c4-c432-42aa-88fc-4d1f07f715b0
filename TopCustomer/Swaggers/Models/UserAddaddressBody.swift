//
// UserAddaddressBody.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct UserAddaddressBody: Codable {

    public var iTypeId: Int?
    public var txAddress: String
    public var vTypeTitle: String
    public var vZipCode: String
    public var txAptSuite: String?
    public var dLatitude: String
    public var dLongitude: String
    public var vCity: String?
    public var vState: String?
    public var vCountryCode: String?

    public init(iTypeId: Int? = nil, txAddress: String, vTypeTitle: String, vZipCode: String, txAptSuite: String? = nil, dLatitude: String, dLongitude: String, vCity: String? = nil, vState: String? = nil, vCountryCode: String? = nil) {
        self.iTypeId = iTypeId
        self.txAddress = txAddress
        self.vTypeTitle = vTypeTitle
        self.vZipCode = vZipCode
        self.txAptSuite = txAptSuite
        self.dLatitude = dLatitude
        self.dLongitude = dLongitude
        self.vCity = vCity
        self.vState = vState
        self.vCountryCode = vCountryCode
    }


}
