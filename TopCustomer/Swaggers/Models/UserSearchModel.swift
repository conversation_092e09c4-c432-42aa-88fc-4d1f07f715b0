//
//  UserSearchModel.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 10/09/2023.
//  Copyright © 2023 SOTSYS203. All rights reserved.
//

import Foundation

public struct UserSearchResponse: Codable {
    // MARK: - Variables
    let responseCode : Int?
    let responseMessage : String?
    let responseData : [UserSearchResponseData]?
    
    // MARK: - Init
    public init(from decoder: Decoder) throws {
        let values = try decoder.container(keyedBy: CodingKeys.self)
        responseCode = try values.decodeIfPresent(Int.self, forKey: .responseCode)
        responseMessage = try values.decodeIfPresent(String.self, forKey: .responseMessage)
        responseData = try values.decodeIfPresent([UserSearchResponseData].self, forKey: .responseData)
    }
    
    public enum CodingKeys: String, CodingKey {
        case responseCode = "responseCode"
        case responseMessage = "responseMessage"
        case responseData = "responseData"
    }
    
}


public struct UserSearchResponseData: Codable {
    // MARK: - Variables
    let id : Int?
    let searchtext : String?
    let deleted : Int?
    
    // MARK: - Init
    public init(from decoder: Decoder) throws {
        let values = try decoder.container(keyedBy: CodingKeys.self)
        id = try values.decodeIfPresent(Int.self, forKey: .id)
        searchtext = try values.decodeIfPresent(String.self, forKey: .searchtext)
        deleted = try values.decodeIfPresent(Int.self, forKey: .deleted)
    }
    
    public enum CodingKeys: String, CodingKey {
        case id = "id"
        case searchtext = "searchtext"
        case deleted = "Deleted"
    }
    
}
