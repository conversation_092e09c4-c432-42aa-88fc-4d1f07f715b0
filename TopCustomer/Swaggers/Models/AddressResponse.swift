//
// AddressResponse.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct AddressResponse: Codable {

    public var responseCode: Int?
    public var responseMessage: String?
    public var responseData: AddressResponseFields?

    public init(responseCode: Int? = nil, responseMessage: String? = nil, responseData: AddressResponseFields? = nil) {
        self.responseCode = responseCode
        self.responseMessage = responseMessage
        self.responseData = responseData
    }


}
