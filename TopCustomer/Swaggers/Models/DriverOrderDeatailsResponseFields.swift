//
// DriverOrderDeatailsResponseFields.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct DriverOrderDeatailsResponseFields: Codable {

    public var iOrderId: Int?
    public var vName: String?
    public var vISDCode: String?
    public var vMobileNumber: String?
    public var vOrderNumber: String?
    public var tsOrderedAt: String?
    public var tAdditionalNote: String?
    public var tiTransactionType: Int?
    public var tiPaymentStatus: Int?
    public var productDetails: [OrderedProductDetailResponseFields]?
    public var dDistanceCost: String?
    public var dVatCharge: String?
    public var dOrderTotal: String?
    public var dOrderLatitude: String?
    public var dOrderLongitude: String?
    public var dDriverLatitude: String?
    public var dDriverLongitude: String?
    public var vAlternativeISDCode: String?
    public var vAlternativeMobileNumber: String?

    public init(iOrderId: Int? = nil, vName: String? = nil, vISDCode: String? = nil, vMobileNumber: String? = nil, vOrderNumber: String? = nil, tsOrderedAt: String? = nil, tAdditionalNote: String? = nil, tiTransactionType: Int? = nil, tiPaymentStatus: Int? = nil, productDetails: [OrderedProductDetailResponseFields]? = nil, dDistanceCost: String? = nil, dVatCharge: String? = nil, dOrderTotal: String? = nil, dOrderLatitude: String? = nil, dOrderLongitude: String? = nil, dDriverLatitude: String? = nil, dDriverLongitude: String? = nil, vAlternativeISDCode: String? = nil, vAlternativeMobileNumber: String? = nil) {
        self.iOrderId = iOrderId
        self.vName = vName
        self.vISDCode = vISDCode
        self.vMobileNumber = vMobileNumber
        self.vOrderNumber = vOrderNumber
        self.tsOrderedAt = tsOrderedAt
        self.tAdditionalNote = tAdditionalNote
        self.tiTransactionType = tiTransactionType
        self.tiPaymentStatus = tiPaymentStatus
        self.productDetails = productDetails
        self.dDistanceCost = dDistanceCost
        self.dVatCharge = dVatCharge
        self.dOrderTotal = dOrderTotal
        self.dOrderLatitude = dOrderLatitude
        self.dOrderLongitude = dOrderLongitude
        self.dDriverLatitude = dDriverLatitude
        self.dDriverLongitude = dDriverLongitude
        self.vAlternativeISDCode = vAlternativeISDCode
        self.vAlternativeMobileNumber = vAlternativeMobileNumber
    }


}
