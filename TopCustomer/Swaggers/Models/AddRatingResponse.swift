//
// AddRatingResponse.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct AddRatingResponse: Codable {

    public var responseCode: Int?
    public var responseMessage: String?
    public var responseData: AddRatingResponseFields?

    public init(responseCode: Int? = nil, responseMessage: String? = nil, responseData: AddRatingResponseFields? = nil) {
        self.responseCode = responseCode
        self.responseMessage = responseMessage
        self.responseData = responseData
    }


}
