//
// OrderOrdertransactionupdateBody.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct OrderOrdertransactionupdateBody: Codable {

    public var iOrderId: Int
    /** 1 - Card, 2 - Apple Pay, 3 - COD , 4- Wallet */
    public var tiTransactionType: Int
    /** Transaction reference id (for COD &#x3D; COD, For Wallet &#x3D; Wallet) */
    public var vTransactionRef: String
    /** 0-Pending, 1-Success, 2-Fail */
    public var iPaymentStatus: Int
    /** 0-False, 1-True */
    public var iUseWallet: Int?
    /** mada,visa,master */
    public var vCardName: String?

    public init(iOrderId: Int, tiTransactionType: Int, vTransactionRef: String, iPaymentStatus: Int, iUseWallet: Int? = nil, vCardName: String? = nil) {
        self.iOrderId = iOrderId
        self.tiTransactionType = tiTransactionType
        self.vTransactionRef = vTransactionRef
        self.iPaymentStatus = iPaymentStatus
        self.iUseWallet = iUseWallet
        self.vCardName = vCardName
    }


}
