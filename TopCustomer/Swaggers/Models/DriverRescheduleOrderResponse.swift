//
// DriverRescheduleOrderResponse.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct DriverRescheduleOrderResponse: Codable {

    public var responseCode: Int?
    public var responseMessage: String?
    public var responseData: DriverRescheduleOrderResponseFields?

    public init(responseCode: Int? = nil, responseMessage: String? = nil, responseData: DriverRescheduleOrderResponseFields? = nil) {
        self.responseCode = responseCode
        self.responseMessage = responseMessage
        self.responseData = responseData
    }


}
