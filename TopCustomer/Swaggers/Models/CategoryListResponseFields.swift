//
// CategoryListResponseFields.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation

public struct CategoryListResponseFields: Codable, Identifiable {
    public var id = UUID()
    public var _id: Int?
    public var vName: String?
    public var vImage: String?
    public var biProductId: Int?
    public var iProductCategoryId: Int?
    public var iOfferId: Int?
    public var vCategoryName: String?
    public var products: [ProductResponseFields]?
    public var isAppAdvertisement: Int?

    public init(_id: Int? = nil, vName: String? = nil, vImage: String? = nil, biProductId: Int? = nil, iProductCategoryId: Int? = nil, iOfferId: Int? = nil, vCategoryName: String? = nil, products: [ProductResponseFields]? = nil, isAppAdvertisement: Int? = nil) {
        self._id = _id
        self.vName = vName
        self.vImage = vImage
        self.biProductId = biProductId
        self.iProductCategoryId = iProductCategoryId
        self.iOfferId = iOfferId
        self.vCategoryName = vCategoryName
        self.products = products
        self.isAppAdvertisement = isAppAdvertisement
    }

    public enum CodingKeys: String, CodingKey { 
        case _id = "id"
        case vName
        case vImage
        case biProductId
        case iProductCategoryId
        case iOfferId
        case vCategoryName
        case products
        case isAppAdvertisement
    }

}
