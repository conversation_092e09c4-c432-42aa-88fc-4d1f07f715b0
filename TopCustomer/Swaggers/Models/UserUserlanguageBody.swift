//
// UserUserlanguageBody.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct UserUserlanguageBody: Codable {

    public enum VLanguage: String, Codable { 
        case en = "en"
        case ar = "ar"
    }
    /** en &#x3D; English, ar &#x3D; Arabic */
    public var vLanguage: VLanguage
    public var vDeviceToken: String

    public init(vLanguage: VLanguage, vDeviceToken: String) {
        self.vLanguage = vLanguage
        self.vDeviceToken = vDeviceToken
    }


}
