//
// NotificationResponse.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct NotificationResponse: Codable {

    public var responseCode: Int?
    public var responseMessage: String?
    public var responseData: NotificationListResponseFields?

    public init(responseCode: Int? = nil, responseMessage: String? = nil, responseData: NotificationListResponseFields? = nil) {
        self.responseCode = responseCode
        self.responseMessage = responseMessage
        self.responseData = responseData
    }


}
