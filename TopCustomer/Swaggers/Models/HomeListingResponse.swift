//
// HomeListingResponse.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct HomeListingResponse: Codable {

    public var responseCode: Int?
    public var responseMessage: String?
    public var responseData: HomeListingResponseFields?

    public init(responseCode: Int? = nil, responseMessage: String? = nil, responseData: HomeListingResponseFields? = nil) {
        self.responseCode = responseCode
        self.responseMessage = responseMessage
        self.responseData = responseData
    }


}
