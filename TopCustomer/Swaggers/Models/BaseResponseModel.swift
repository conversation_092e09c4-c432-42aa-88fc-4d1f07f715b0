//
//  BaseResponseModel.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 19/06/2023.
//  Copyright © 2023 SOTSYS203. All rights reserved.
//

import Foundation


public struct BaseResponseModel: Codable {
    
    public var responseCode: Int?
    public var responseMessage: String?
    
    public init(responseCode: Int? = nil, responseMessage: String? = nil) {
        self.responseCode = responseCode
        self.responseMessage = responseMessage
    }
    
}
