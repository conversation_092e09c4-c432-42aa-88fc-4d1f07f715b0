//
// AdvertisementResponseFields.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct AdvertisementResponseFields: Codable {

    public var iAdvertisementId: Int?
    public var vAdTitle: String?
    public var vImage: String?
    public var biProductId: Int?
    public var iProductCategoryId: Int?
    public var iOfferId: Int?
    public var vSocialLink: String?
    public var tiIsActive: Int?
    public var vButtonText: String?
    public var vCategoryName: String?
    public var IsAppadvertisement: Int?

    public init(iAdvertisementId: Int? = nil, vAdTitle: String? = nil, vImage: String? = nil, biProductId: Int? = nil, iProductCategoryId: Int? = nil, iOfferId: Int? = nil, vSocialLink: String? = nil, tiIsActive: Int? = nil, vButtonText: String? = nil, vCategoryName: String? = nil, IsAppadvertisement: Int? = nil) {
        self.iAdvertisementId = iAdvertisementId
        self.vAdTitle = vAdTitle
        self.vImage = vImage
        self.biProductId = biProductId
        self.iProductCategoryId = iProductCategoryId
        self.iOfferId = iOfferId
        self.vSocialLink = vSocialLink
        self.tiIsActive = tiIsActive
        self.vButtonText = vButtonText
        self.vCategoryName = vCategoryName
        self.IsAppadvertisement = IsAppadvertisement
    }


}
