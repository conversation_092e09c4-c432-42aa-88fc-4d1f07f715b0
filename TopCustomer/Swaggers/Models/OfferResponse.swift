//
// OfferResponse.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct OfferResponse: Codable {

    public var responseCode: Int?
    public var responseMessage: String?
    public var responseData: OfferResponseFields?

    public init(responseCode: Int? = nil, responseMessage: String? = nil, responseData: OfferResponseFields? = nil) {
        self.responseCode = responseCode
        self.responseMessage = responseMessage
        self.responseData = responseData
    }


}
