//
// MarketingBannerResponseFields.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct MarketingBannerResponseFields: Codable, Identifiable {
    public var id = UUID()
    public var iMarketingBannerId: Int?
    public var vTitle: String?
    public var tiLinkType: Int?
    public var iOfferId: Int?
    public var biProductId: Int?
    public var iProductCategoryId: Int?
    public var vUrl: String?
    public var tiIsActive: Int?
    public var vCategoryName: String?
    public var marketingBannerImage: String?
    public var isAppadvertisement: Int?

    public init(iMarketingBannerId: Int? = nil, vTitle: String? = nil, tiLinkType: Int? = nil, iOfferId: Int? = nil, biProductId: Int? = nil, iProductCategoryId: Int? = nil, vUrl: String? = nil, tiIsActive: Int? = nil, vCategoryName: String? = nil, marketingBannerImage: String? = nil, isAppadvertisement: Int? = nil) {
        self.iMarketingBannerId = iMarketingBannerId
        self.vTitle = vTitle
        self.tiLinkType = tiLinkType
        self.iOfferId = iOfferId
        self.biProductId = biProductId
        self.iProductCategoryId = iProductCategoryId
        self.vUrl = vUrl
        self.tiIsActive = tiIsActive
        self.vCategoryName = vCategoryName
        self.marketingBannerImage = marketingBannerImage
        self.isAppadvertisement = isAppadvertisement
    }

    public enum CodingKeys: String, CodingKey { 
        case iMarketingBannerId
        case vTitle
        case tiLinkType
        case iOfferId
        case biProductId
        case iProductCategoryId
        case vUrl
        case tiIsActive
        case vCategoryName
        case marketingBannerImage = "marketing_banner_image"
        case isAppadvertisement = "isAppAdvertisement"
    }

}
