//
// DetailFields.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct DetailFields: Codable {

    public var iWalletId: Int?
    public var tiAction: String?
    public var tsAddedAt: String?
    public var tsExpiryAt: String?
    public var vAmount: String?

    public init(iWalletId: Int? = nil, tiAction: String? = nil, tsAddedAt: String? = nil, tsExpiryAt: String? = nil, vAmount: String? = nil) {
        self.iWalletId = iWalletId
        self.tiAction = tiAction
        self.tsAddedAt = tsAddedAt
        self.tsExpiryAt = tsExpiryAt
        self.vAmount = vAmount
    }


}
