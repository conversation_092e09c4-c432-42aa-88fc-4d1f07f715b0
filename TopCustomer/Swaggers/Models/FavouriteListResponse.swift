//
// FavouriteListResponse.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct FavouriteListResponse: Codable {

    public var responseCode: Int?
    public var responseMessage: String?
    public var responseData: ProductListResponseFields?

    public init(responseCode: Int? = nil, responseMessage: String? = nil, responseData: ProductListResponseFields? = nil) {
        self.responseCode = responseCode
        self.responseMessage = responseMessage
        self.responseData = responseData
    }


}
