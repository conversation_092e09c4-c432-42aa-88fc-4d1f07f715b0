//
// WebCategoryListResponseFields.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation



public struct WebCategoryListResponseFields: Codable {

    public var _id: Int?
    public var vName: String?
    public var vImage: String?

    public init(_id: Int? = nil, vName: String? = nil, vImage: String? = nil) {
        self._id = _id
        self.vName = vName
        self.vImage = vImage
    }

    public enum CodingKeys: String, CodingKey { 
        case _id = "id"
        case vName
        case vImage
    }

}
