//
// AuthenticationAPI.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation
import Alamofire


open class AuthenticationAPI {
    /**
     * enum for parameter tiDeviceType
     */
    public enum TiDeviceType_guestLogin: Int { 
        case _0 = 0
        case _1 = 1
        case _2 = 2
    }

    /**
     Customer Guest Login

     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter vDeviceToken: (form)  (optional)
     - parameter tiDeviceType: (form)  (optional)
     - parameter vDeviceName: (form)  (optional)
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func guestLogin(accept: String, lang: String, vDeviceToken: String? = nil, tiDeviceType: TiDeviceType_guestLogin? = nil, vDeviceName: String? = nil, completion: @escaping ((_ data: UserResponse?,_ error: Error?) -> Void)) {
        guestLoginWithRequestBuilder(accept: accept, lang: lang, vDeviceToken: vDeviceToken, tiDeviceType: tiDeviceType, vDeviceName: vDeviceName).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }


    /**
     Customer Guest Login
     - POST /oauth/guest-login
     - 

     - examples: [{contentType=application/json, example={
  "responseData" : {
    "vEmailId" : "vEmailId",
    "vName" : "vName",
    "vISDCode" : "vISDCode",
    "vNewISDCode" : "vNewISDCode",
    "vNewMobileNumber" : "vNewMobileNumber",
    "tiIsMobileVerified" : 1,
    "iUserId" : 6,
    "tiIsSocialLogin" : 5,
    "vLanguage" : "vLanguage",
    "tiGender" : 5,
    "vReferalPlatform" : "vReferalPlatform",
    "vAccessToken" : "vAccessToken",
    "iDob" : "iDob",
    "vMobileNumber" : "vMobileNumber"
  },
  "responseMessage" : "responseMessage",
  "responseCode" : 0
}}]
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter vDeviceToken: (form)  (optional)
     - parameter tiDeviceType: (form)  (optional)
     - parameter vDeviceName: (form)  (optional)

     - returns: RequestBuilder<UserResponse> 
     */
    open class func guestLoginWithRequestBuilder(accept: String, lang: String, vDeviceToken: String? = nil, tiDeviceType: TiDeviceType_guestLogin? = nil, vDeviceName: String? = nil) -> RequestBuilder<UserResponse> {
        let path = "/oauth/guest-login"
        let URLString = SwaggerClientAPI.basePath + path
        let formParams: [String:Any?] = [
                "vDeviceToken": vDeviceToken,
                "tiDeviceType": tiDeviceType?.rawValue,
                "vDeviceName": vDeviceName
        ]

        let nonNullParameters = APIHelper.rejectNil(formParams)
        let parameters = APIHelper.convertBoolToString(nonNullParameters)
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any?] = [
                        "Accept": accept,
                        "Lang": lang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)

        let requestBuilder: RequestBuilder<UserResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()

        return requestBuilder.init(method: "POST", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    /**
     * enum for parameter tiDeviceType
     */
    public enum TiDeviceType_login: Int { 
        case _0 = 0
        case _1 = 1
        case _2 = 2
    }

    /**
     Customer Login

     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter iRoleId: (form)  (optional)
     - parameter vName: (form)  (optional)
     - parameter vLanguage: (form)  (optional)
     - parameter vISDCode: (form)  (optional)
     - parameter vMobileNumber: (form)  (optional)
     - parameter vDeviceToken: (form)  (optional)
     - parameter tiDeviceType: (form)  (optional)
     - parameter vDeviceName: (form)  (optional)
     - parameter vDeviceKey: (form)  (optional)
     - parameter vReferalPlatform: (form)  (optional)
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func login(accept: String, lang: String, iRoleId: Int? = nil, vName: String? = nil, vLanguage: String? = nil, vISDCode: String? = nil, vMobileNumber: String? = nil, vDeviceToken: String? = nil, tiDeviceType: TiDeviceType_login? = nil, vDeviceName: String? = nil, vDeviceKey: String? = nil, vReferalPlatform: String? = nil, completion: @escaping ((_ data: UserResponse?,_ error: Error?) -> Void)) {
        loginWithRequestBuilder(accept: accept, lang: lang, iRoleId: iRoleId, vName: vName, vLanguage: vLanguage, vISDCode: vISDCode, vMobileNumber: vMobileNumber, vDeviceToken: vDeviceToken, tiDeviceType: tiDeviceType, vDeviceName: vDeviceName, vDeviceKey: vDeviceKey, vReferalPlatform: vReferalPlatform).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }


    /**
     Customer Login
     - POST /oauth/login
     - 

     - examples: [{contentType=application/json, example={
  "responseData" : {
    "vEmailId" : "vEmailId",
    "vName" : "vName",
    "vISDCode" : "vISDCode",
    "vNewISDCode" : "vNewISDCode",
    "vNewMobileNumber" : "vNewMobileNumber",
    "tiIsMobileVerified" : 1,
    "iUserId" : 6,
    "tiIsSocialLogin" : 5,
    "vLanguage" : "vLanguage",
    "tiGender" : 5,
    "vReferalPlatform" : "vReferalPlatform",
    "vAccessToken" : "vAccessToken",
    "iDob" : "iDob",
    "vMobileNumber" : "vMobileNumber"
  },
  "responseMessage" : "responseMessage",
  "responseCode" : 0
}}]
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter iRoleId: (form)  (optional)
     - parameter vName: (form)  (optional)
     - parameter vLanguage: (form)  (optional)
     - parameter vISDCode: (form)  (optional)
     - parameter vMobileNumber: (form)  (optional)
     - parameter vDeviceToken: (form)  (optional)
     - parameter tiDeviceType: (form)  (optional)
     - parameter vDeviceName: (form)  (optional)
     - parameter vDeviceKey: (form)  (optional)
     - parameter vReferalPlatform: (form)  (optional)

     - returns: RequestBuilder<UserResponse> 
     */
    open class func loginWithRequestBuilder(accept: String, lang: String, iRoleId: Int? = nil, vName: String? = nil, vLanguage: String? = nil, vISDCode: String? = nil, vMobileNumber: String? = nil, vDeviceToken: String? = nil, tiDeviceType: TiDeviceType_login? = nil, vDeviceName: String? = nil, vDeviceKey: String? = nil, vReferalPlatform: String? = nil) -> RequestBuilder<UserResponse> {
        let path = "/oauth/login"
        let URLString = SwaggerClientAPI.basePath + path
        let formParams: [String:Any?] = [
                "iRoleId": iRoleId?.encodeToJSON(),
                "vName": vName,
                "vLanguage": vLanguage,
                "vISDCode": vISDCode,
                "vMobileNumber": vMobileNumber,
                "vDeviceToken": vDeviceToken ?? AppDelegate.getDeviceToken(),
                "tiDeviceType": tiDeviceType?.rawValue,
                "vDeviceName": vDeviceName,
                "vDeviceKey": vDeviceKey,
//                "vReferalPlatform": vReferalPlatform,
                "sharedUser": UserDefaults.standard.inviteCode.encodeToJSON()
        ]

        let nonNullParameters = APIHelper.rejectNil(formParams)
        let parameters = APIHelper.convertBoolToString(nonNullParameters)
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any?] = [
                        "Accept": accept,
                        "Lang": lang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)

        let requestBuilder: RequestBuilder<UserResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()

        return requestBuilder.init(method: "POST", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    /**
     Request for OTP

     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter iRoleId: (form)  (optional)
     - parameter vISDCode: (form)  (optional)
     - parameter vMobileNumber: (form)  (optional)
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func requestForOtp(accept: String, lang: String, iRoleId: Int? = nil, vISDCode: String? = nil, vMobileNumber: String? = nil, completion: @escaping ((_ data: CommonFields?,_ error: Error?) -> Void)) {
        requestForOtpWithRequestBuilder(accept: accept, lang: lang, iRoleId: iRoleId, vISDCode: vISDCode, vMobileNumber: vMobileNumber).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }


    /**
     Request for OTP
     - POST /oauth/request-for-otp
     - 

     - examples: [{contentType=application/json, example={
  "responseMessage" : "responseMessage",
  "responseCode" : 0
}}]
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter iRoleId: (form)  (optional)
     - parameter vISDCode: (form)  (optional)
     - parameter vMobileNumber: (form)  (optional)

     - returns: RequestBuilder<CommonFields> 
     */
    open class func requestForOtpWithRequestBuilder(accept: String, lang: String, iRoleId: Int? = nil, vISDCode: String? = nil, vMobileNumber: String? = nil) -> RequestBuilder<CommonFields> {
        let path = "/oauth/request-for-otp"
        let URLString = SwaggerClientAPI.basePath + path
        let formParams: [String:Any?] = [
                "iRoleId": iRoleId?.encodeToJSON(),
                "vISDCode": vISDCode,
                "vMobileNumber": vMobileNumber
        ]

        let nonNullParameters = APIHelper.rejectNil(formParams)
        let parameters = APIHelper.convertBoolToString(nonNullParameters)
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any?] = [
                        "Accept": accept,
                        "Lang": lang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)

        let requestBuilder: RequestBuilder<CommonFields>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()

        return requestBuilder.init(method: "POST", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    /**
     * enum for parameter tiDeviceType
     */
    public enum TiDeviceType_signup: Int { 
        case _0 = 0
        case _1 = 1
        case _2 = 2
    }

    /**
     Signup user

     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter vName: (form)  (optional)
     - parameter vEmailId: (form)  (optional)
     - parameter vPassword: (form)  (optional)
     - parameter vDeviceToken: (form)  (optional)
     - parameter tiDeviceType: (form)  (optional)
     - parameter vDeviceName: (form)  (optional)
     - parameter vDeviceKey: (form)  (optional)
     - parameter vTimezone: (form)  (optional)
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func signup(accept: String, lang: String, vName: String? = nil, vEmailId: String? = nil, vPassword: String? = nil, vDeviceToken: String? = nil, tiDeviceType: TiDeviceType_signup? = nil, vDeviceName: String? = nil, vDeviceKey: String? = nil, vTimezone: String? = nil, completion: @escaping ((_ data: UserResponse?,_ error: Error?) -> Void)) {
        signupWithRequestBuilder(accept: accept, lang: lang, vName: vName, vEmailId: vEmailId, vPassword: vPassword, vDeviceToken: vDeviceToken, tiDeviceType: tiDeviceType, vDeviceName: vDeviceName, vDeviceKey: vDeviceKey, vTimezone: vTimezone).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }


    /**
     Signup user
     - POST /oauth/signup
     - 

     - examples: [{contentType=application/json, example={
  "responseData" : {
    "vEmailId" : "vEmailId",
    "vName" : "vName",
    "vISDCode" : "vISDCode",
    "vNewISDCode" : "vNewISDCode",
    "vNewMobileNumber" : "vNewMobileNumber",
    "tiIsMobileVerified" : 1,
    "iUserId" : 6,
    "tiIsSocialLogin" : 5,
    "vLanguage" : "vLanguage",
    "tiGender" : 5,
    "vReferalPlatform" : "vReferalPlatform",
    "vAccessToken" : "vAccessToken",
    "iDob" : "iDob",
    "vMobileNumber" : "vMobileNumber"
  },
  "responseMessage" : "responseMessage",
  "responseCode" : 0
}}]
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter vName: (form)  (optional)
     - parameter vEmailId: (form)  (optional)
     - parameter vPassword: (form)  (optional)
     - parameter vDeviceToken: (form)  (optional)
     - parameter tiDeviceType: (form)  (optional)
     - parameter vDeviceName: (form)  (optional)
     - parameter vDeviceKey: (form)  (optional)
     - parameter vTimezone: (form)  (optional)

     - returns: RequestBuilder<UserResponse> 
     */
    open class func signupWithRequestBuilder(accept: String, lang: String, vName: String? = nil, vEmailId: String? = nil, vPassword: String? = nil, vDeviceToken: String? = nil, tiDeviceType: TiDeviceType_signup? = nil, vDeviceName: String? = nil, vDeviceKey: String? = nil, vTimezone: String? = nil) -> RequestBuilder<UserResponse> {
        let path = "/oauth/signup"
        let URLString = SwaggerClientAPI.basePath + path
        let formParams: [String:Any?] = [
                "vName": vName,
                "vEmailId": vEmailId,
                "vPassword": vPassword,
                "vDeviceToken": vDeviceToken ?? AppDelegate.getDeviceToken(),
                "tiDeviceType": tiDeviceType?.rawValue,
                "vDeviceName": vDeviceName,
                "vDeviceKey": vDeviceKey,
                "vTimezone": vTimezone
        ]

        let nonNullParameters = APIHelper.rejectNil(formParams)
        let parameters = APIHelper.convertBoolToString(nonNullParameters)
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any?] = [
                        "Accept": accept,
                        "Lang": lang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)

        let requestBuilder: RequestBuilder<UserResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()

        return requestBuilder.init(method: "POST", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    /**
     * enum for parameter tiSocialType
     */
    public enum TiSocialType_socialSignin: Int { 
        case _1 = 1
        case _2 = 2
    }

    /**
     * enum for parameter tiDeviceType
     */
    public enum TiDeviceType_socialSignin: Int { 
        case _0 = 0
        case _1 = 1
        case _2 = 2
    }

    /**
     Social signin

     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter vSocialId: (form)  (optional)
     - parameter tiSocialType: (form)  (optional)
     - parameter vDeviceToken: (form)  (optional)
     - parameter tiDeviceType: (form)  (optional)
     - parameter vDeviceName: (form)  (optional)
     - parameter vTimezone: (form)  (optional)
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func socialSignin(accept: String, lang: String, vSocialId: String? = nil, tiSocialType: TiSocialType_socialSignin? = nil, vDeviceToken: String? = nil, tiDeviceType: TiDeviceType_socialSignin? = nil, vDeviceName: String? = nil, vTimezone: String? = nil, completion: @escaping ((_ data: UserResponse?,_ error: Error?) -> Void)) {
        socialSigninWithRequestBuilder(accept: accept, lang: lang, vSocialId: vSocialId, tiSocialType: tiSocialType, vDeviceToken: vDeviceToken, tiDeviceType: tiDeviceType, vDeviceName: vDeviceName, vTimezone: vTimezone).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }


    /**
     Social signin
     - POST /oauth/social-signin
     - 

     - examples: [{contentType=application/json, example={
  "responseData" : {
    "vEmailId" : "vEmailId",
    "vName" : "vName",
    "vISDCode" : "vISDCode",
    "vNewISDCode" : "vNewISDCode",
    "vNewMobileNumber" : "vNewMobileNumber",
    "tiIsMobileVerified" : 1,
    "iUserId" : 6,
    "tiIsSocialLogin" : 5,
    "vLanguage" : "vLanguage",
    "tiGender" : 5,
    "vReferalPlatform" : "vReferalPlatform",
    "vAccessToken" : "vAccessToken",
    "iDob" : "iDob",
    "vMobileNumber" : "vMobileNumber"
  },
  "responseMessage" : "responseMessage",
  "responseCode" : 0
}}]
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter vSocialId: (form)  (optional)
     - parameter tiSocialType: (form)  (optional)
     - parameter vDeviceToken: (form)  (optional)
     - parameter tiDeviceType: (form)  (optional)
     - parameter vDeviceName: (form)  (optional)
     - parameter vTimezone: (form)  (optional)

     - returns: RequestBuilder<UserResponse> 
     */
    open class func socialSigninWithRequestBuilder(accept: String, lang: String, vSocialId: String? = nil, tiSocialType: TiSocialType_socialSignin? = nil, vDeviceToken: String? = nil, tiDeviceType: TiDeviceType_socialSignin? = nil, vDeviceName: String? = nil, vTimezone: String? = nil) -> RequestBuilder<UserResponse> {
        let path = "/oauth/social-signin"
        let URLString = SwaggerClientAPI.basePath + path
        let formParams: [String:Any?] = [
                "vSocialId": vSocialId,
                "tiSocialType": tiSocialType?.rawValue,
                "vDeviceToken": vDeviceToken,
                "tiDeviceType": tiDeviceType?.rawValue,
                "vDeviceName": vDeviceName,
                "vTimezone": vTimezone
        ]

        let nonNullParameters = APIHelper.rejectNil(formParams)
        let parameters = APIHelper.convertBoolToString(nonNullParameters)
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any?] = [
                        "Accept": accept,
                        "Lang": lang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)

        let requestBuilder: RequestBuilder<UserResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()

        return requestBuilder.init(method: "POST", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    /**
     * enum for parameter tiDeviceType
     */
    public enum TiDeviceType_verifyOtp: Int { 
        case _0 = 0
        case _1 = 1
        case _2 = 2
    }

    /**
     Verify OTP

     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter iRoleId: (form)  (optional)
     - parameter vISDCode: (form)  (optional)
     - parameter vMobileNumber: (form)  (optional)
     - parameter iOTP: (form)  (optional)
     - parameter vDeviceToken: (form)  (optional)
     - parameter tiDeviceType: (form)  (optional)
     - parameter vDeviceName: (form)  (optional)
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func verifyOtp(accept: String, lang: String, iRoleId: Int? = nil, vISDCode: String? = nil, vMobileNumber: String? = nil, iOTP: Int? = nil, vDeviceToken: String? = nil, tiDeviceType: TiDeviceType_verifyOtp? = nil, vDeviceName: String? = nil, completion: @escaping ((_ data: UserResponse?,_ error: Error?) -> Void)) {
        verifyOtpWithRequestBuilder(accept: accept, lang: lang, iRoleId: iRoleId, vISDCode: vISDCode, vMobileNumber: vMobileNumber, iOTP: iOTP, vDeviceToken: vDeviceToken, tiDeviceType: tiDeviceType, vDeviceName: vDeviceName).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }


    /**
     Verify OTP
     - POST /oauth/verify-otp
     - 

     - examples: [{contentType=application/json, example={
  "responseData" : {
    "vEmailId" : "vEmailId",
    "vName" : "vName",
    "vISDCode" : "vISDCode",
    "vNewISDCode" : "vNewISDCode",
    "vNewMobileNumber" : "vNewMobileNumber",
    "tiIsMobileVerified" : 1,
    "iUserId" : 6,
    "tiIsSocialLogin" : 5,
    "vLanguage" : "vLanguage",
    "tiGender" : 5,
    "vReferalPlatform" : "vReferalPlatform",
    "vAccessToken" : "vAccessToken",
    "iDob" : "iDob",
    "vMobileNumber" : "vMobileNumber"
  },
  "responseMessage" : "responseMessage",
  "responseCode" : 0
}}]
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter iRoleId: (form)  (optional)
     - parameter vISDCode: (form)  (optional)
     - parameter vMobileNumber: (form)  (optional)
     - parameter iOTP: (form)  (optional)
     - parameter vDeviceToken: (form)  (optional)
     - parameter tiDeviceType: (form)  (optional)
     - parameter vDeviceName: (form)  (optional)

     - returns: RequestBuilder<UserResponse> 
     */
    open class func verifyOtpWithRequestBuilder(accept: String, lang: String, iRoleId: Int? = nil, vISDCode: String? = nil, vMobileNumber: String? = nil, iOTP: Int? = nil, vDeviceToken: String? = nil, tiDeviceType: TiDeviceType_verifyOtp? = nil, vDeviceName: String? = nil) -> RequestBuilder<UserResponse> {
        let path = "/oauth/verify-otp"
        let URLString = SwaggerClientAPI.basePath + path
        let formParams: [String:Any?] = [
                "iRoleId": iRoleId?.encodeToJSON(),
                "vISDCode": vISDCode,
                "vMobileNumber": vMobileNumber,
                "iOTP": iOTP?.encodeToJSON(),
                "vDeviceToken": vDeviceToken,
                "tiDeviceType": tiDeviceType?.rawValue,
                "vDeviceName": vDeviceName
        ]

        let nonNullParameters = APIHelper.rejectNil(formParams)
        let parameters = APIHelper.convertBoolToString(nonNullParameters)
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any?] = [
                        "Accept": accept,
                        "Lang": lang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)

        let requestBuilder: RequestBuilder<UserResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()

        return requestBuilder.init(method: "POST", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
}
