//
// CategoryAPI.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation
import Alamofire


open class CategoryAPI {
    /**
     Category Listing

     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func categoryListing(authorization: String, accept: String, lang: String, completion: @escaping ((_ data: ListResponse?,_ error: Error?) -> Void)) {
        categoryListingWithRequestBuilder(authorization: authorization, accept: accept, lang: lang).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }


    /**
     Category Listing
     - GET /product/category-listing
     - 

     - :
       - type: http
       - name: bearerAuth
     - examples: [{contentType=application/json, example={
  "responseData" : [ {
    "vName" : "vName",
    "vImage" : "vImage",
    "tBannerDescription" : "tBannerDescription",
    "iProductCategoryId" : 5,
    "vCategoryName" : "vCategoryName",
    "id" : 6,
    "iOfferId" : 5,
    "biProductId" : 1
  }, {
    "vName" : "vName",
    "vImage" : "vImage",
    "tBannerDescription" : "tBannerDescription",
    "iProductCategoryId" : 5,
    "vCategoryName" : "vCategoryName",
    "id" : 6,
    "iOfferId" : 5,
    "biProductId" : 1
  } ],
  "responseMessage" : "responseMessage",
  "responseCode" : 0
}}]
     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  

     - returns: RequestBuilder<ListResponse> 
     */
    open class func categoryListingWithRequestBuilder(authorization: String, accept: String, lang: String) -> RequestBuilder<ListResponse> {
        let path = "/product/category-listing"
        let URLString = SwaggerClientAPI.basePath + path
        let parameters: [String:Any]? = nil
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any?] = [
                        "Authorization": authorization,
                        "Accept": accept,
                        "Lang": lang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)

        let requestBuilder: RequestBuilder<ListResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()

        return requestBuilder.init(method: "GET", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
}
