//
// CommonAPI.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation
import Alamofire


open class CommonAPI {
    /**
     Get types of address

     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func addressTypes(authorization: String, accept: String, lang: String, completion: @escaping ((_ data: ListResponse?,_ error: Error?) -> Void)) {
        addressTypesWithRequestBuilder(authorization: authorization, accept: accept, lang: lang).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }


    /**
     Get types of address
     - GET /address-types
     - 

     - :
       - type: http
       - name: bearerAuth
     - examples: [{contentType=application/json, example={
  "responseData" : [ {
    "vName" : "vName",
    "vImage" : "vImage",
    "tBannerDescription" : "tBannerDescription",
    "iProductCategoryId" : 5,
    "vCategoryName" : "vCategoryName",
    "id" : 6,
    "iOfferId" : 5,
    "biProductId" : 1
  }, {
    "vName" : "vName",
    "vImage" : "vImage",
    "tBannerDescription" : "tBannerDescription",
    "iProductCategoryId" : 5,
    "vCategoryName" : "vCategoryName",
    "id" : 6,
    "iOfferId" : 5,
    "biProductId" : 1
  } ],
  "responseMessage" : "responseMessage",
  "responseCode" : 0
}}]
     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  

     - returns: RequestBuilder<ListResponse> 
     */
    open class func addressTypesWithRequestBuilder(authorization: String, accept: String, lang: String) -> RequestBuilder<ListResponse> {
        let path = "/address-types"
        let URLString = SwaggerClientAPI.basePath + path
        let parameters: [String:Any]? = nil
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any?] = [
                        "Authorization": authorization,
                        "Accept": accept,
                        "Lang": lang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)

        let requestBuilder: RequestBuilder<ListResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()

        return requestBuilder.init(method: "GET", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    /**
     Static content pages

     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter vSlug: (path) about-us, privacy-policy, terms-and-conditions 
     - parameter authorization: (header)  (optional, default to Bearer )
     - parameter isTerm: (header)  (optional)
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func contentPages(accept: String, lang: String, vSlug: String, authorization: String? = nil, isTerm: Int? = nil, completion: @escaping ((_ data: ContentPageResponse?,_ error: Error?) -> Void)) {
        contentPagesWithRequestBuilder(accept: accept, lang: lang, vSlug: vSlug, authorization: authorization, isTerm: isTerm).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }


    /**
     Static content pages
     - GET /content-pages/{vSlug}
     - 

     - examples: [{contentType=application/json, example={
  "responseData" : {
    "txContent" : "txContent",
    "vPageName" : "vPageName",
    "tsUpdatedAt" : "tsUpdatedAt"
  },
  "responseMessage" : "responseMessage",
  "responseCode" : 0
}}]
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter vSlug: (path) about-us, privacy-policy, terms-and-conditions 
     - parameter authorization: (header)  (optional, default to Bearer )
     - parameter isTerm: (header)  (optional)

     - returns: RequestBuilder<ContentPageResponse> 
     */
    open class func contentPagesWithRequestBuilder(accept: String, lang: String, vSlug: String, authorization: String? = nil, isTerm: Int? = nil) -> RequestBuilder<ContentPageResponse> {
        var path = "/content-pages/{vSlug}"
        let vSlugPreEscape = "\(vSlug)"
        let vSlugPostEscape = vSlugPreEscape.addingPercentEncoding(withAllowedCharacters: .urlPathAllowed) ?? ""
        path = path.replacingOccurrences(of: "{vSlug}", with: vSlugPostEscape, options: .literal, range: nil)
        let URLString = SwaggerClientAPI.basePath + path
        let parameters: [String:Any]? = nil
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any?] = [
                        "Authorization": authorization,
                        "Accept": accept,
                        "Lang": lang,
                        "isTerm": isTerm?.encodeToJSON()
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)

        let requestBuilder: RequestBuilder<ContentPageResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()

        return requestBuilder.init(method: "GET", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
}
