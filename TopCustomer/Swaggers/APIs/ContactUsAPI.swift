//
// ContactUsAPI.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation
import Alamofire


open class ContactUsAPI {
    /**
     ContactUs help create

     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter vName: (form)  (optional)
     - parameter vEmailId: (form)  (optional)
     - parameter txDescription: (form)  (optional)
     - parameter iOrderId: (form)  (optional)
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func contactHelpCreate(authorization: String, accept: String, lang: String, vName: String? = nil, vEmailId: String? = nil, txDescription: String? = nil, iOrderId: Int? = nil, completion: @escaping ((_ data: CommonFields?,_ error: Error?) -> Void)) {
        contactHelpCreateWithRequestBuilder(authorization: authorization, accept: accept, lang: lang, vName: vName, vEmailId: vEmailId, txDescription: txDescription, iOrderId: iOrderId).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }


    /**
     ContactUs help create
     - POST /contact/contact-help-create
     - 

     - :
       - type: http
       - name: bearerAuth
     - examples: [{contentType=application/json, example={
  "responseMessage" : "responseMessage",
  "responseCode" : 0
}}]
     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter vName: (form)  (optional)
     - parameter vEmailId: (form)  (optional)
     - parameter txDescription: (form)  (optional)
     - parameter iOrderId: (form)  (optional)

     - returns: RequestBuilder<CommonFields> 
     */
    open class func contactHelpCreateWithRequestBuilder(authorization: String, accept: String, lang: String, vName: String? = nil, vEmailId: String? = nil, txDescription: String? = nil, iOrderId: Int? = nil) -> RequestBuilder<CommonFields> {
        let path = "/contact/contact-help-create"
        let URLString = SwaggerClientAPI.basePath + path
        let formParams: [String:Any?] = [
                "vName": vName,
                "vEmailId": vEmailId,
                "txDescription": txDescription,
                "iOrderId": iOrderId?.encodeToJSON()
        ]

        let nonNullParameters = APIHelper.rejectNil(formParams)
        let parameters = APIHelper.convertBoolToString(nonNullParameters)
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any?] = [
                        "Authorization": authorization,
                        "Accept": accept,
                        "Lang": lang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)

        let requestBuilder: RequestBuilder<CommonFields>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()

        return requestBuilder.init(method: "POST", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    /**
     Contact Us Reason Listing

     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func contactListing(authorization: String, accept: String, lang: String, completion: @escaping ((_ data: ListResponse?,_ error: Error?) -> Void)) {
        contactListingWithRequestBuilder(authorization: authorization, accept: accept, lang: lang).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }


    /**
     Contact Us Reason Listing
     - GET /contact/contact-listing
     - 

     - :
       - type: http
       - name: bearerAuth
     - examples: [{contentType=application/json, example={
  "responseData" : [ {
    "vName" : "vName",
    "vImage" : "vImage",
    "tBannerDescription" : "tBannerDescription",
    "iProductCategoryId" : 5,
    "vCategoryName" : "vCategoryName",
    "id" : 6,
    "iOfferId" : 5,
    "biProductId" : 1
  }, {
    "vName" : "vName",
    "vImage" : "vImage",
    "tBannerDescription" : "tBannerDescription",
    "iProductCategoryId" : 5,
    "vCategoryName" : "vCategoryName",
    "id" : 6,
    "iOfferId" : 5,
    "biProductId" : 1
  } ],
  "responseMessage" : "responseMessage",
  "responseCode" : 0
}}]
     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  

     - returns: RequestBuilder<ListResponse> 
     */
    open class func contactListingWithRequestBuilder(authorization: String, accept: String, lang: String) -> RequestBuilder<ListResponse> {
        let path = "/contact/contact-listing"
        let URLString = SwaggerClientAPI.basePath + path
        let parameters: [String:Any]? = nil
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any?] = [
                        "Authorization": authorization,
                        "Accept": accept,
                        "Lang": lang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)

        let requestBuilder: RequestBuilder<ListResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()

        return requestBuilder.init(method: "GET", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
}
