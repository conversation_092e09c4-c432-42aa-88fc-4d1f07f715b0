//
// CartAPI.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation
import Alamofire


open class CartAPI {
    /**
     Procucts Add to Cart
     
     - parameter authorization: (header)
     - parameter accept: (header)
     - parameter lang: (header)
     - parameter biProductId: (form)  (optional)
     - parameter tiIsCheck: (form)  (optional)
     - parameter iProductQuantity: (form)  (optional)
     - parameter dbPrice: (form)  (optional)
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func addToCart(authorization: String, accept: String, lang: String, biProductId: Int? = nil, tiIsCheck: Int? = nil, iProductQuantity: Int? = nil, dbPrice: String? = nil, isMosques: String? = nil, dbBundleID: Int? = nil, completion: @escaping ((_ data: AddToCartResponse?,_ error: Error?) -> Void)) {
        addToCartWithRequestBuilder(authorization: authorization, accept: accept, lang: lang, biProductId: biProductId, tiIsCheck: tiIsCheck, iProductQuantity: iProductQuantity, dbPrice: dbPrice, isMosques: isMosques, dbBundleID: dbBundleID).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }
    
    open class func addToMarketCart(authorization: String, accept: String, lang: String, biProductId: Int? = nil, tiIsCheck: Int? = nil, iProductQuantity: Int? = nil, dbPrice: String? = nil, isMosques: String? = nil, dbBundleID: Int? = nil, completion: @escaping ((_ data: AddToCartResponse?,_ error: Error?) -> Void)) {
        addToMarketCartWithRequestBuilder(authorization: authorization, accept: accept, lang: lang, biProductId: biProductId, tiIsCheck: tiIsCheck, iProductQuantity: iProductQuantity, dbPrice: dbPrice, isMosques: isMosques, dbBundleID: dbBundleID).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }
    
    
    /**
     Procucts Add to Cart
     - POST /cart/add-to-cart
     -
     
     - :
     - type: http
     - name: bearerAuth
     - examples: [{contentType=application/json, example={
     "responseData" : {
     "getCartProducts" : 2,
     "iUserId" : 1,
     "iCartId" : 6,
     "dbFinalPrice" : "dbFinalPrice",
     "dbPrice" : "dbPrice",
     "biProductId" : 5,
     "iProductQuantity" : 5
     },
     "responseMessage" : "responseMessage",
     "responseCode" : 0
     }}]
     - parameter authorization: (header)
     - parameter accept: (header)
     - parameter lang: (header)
     - parameter biProductId: (form)  (optional)
     - parameter tiIsCheck: (form)  (optional)
     - parameter iProductQuantity: (form)  (optional)
     - parameter dbPrice: (form)  (optional)
     
     - returns: RequestBuilder<AddToCartResponse>
     */
    open class func addToCartWithRequestBuilder(authorization: String, accept: String, lang: String, biProductId: Int? = nil, tiIsCheck: Int? = nil, iProductQuantity: Int? = nil, dbPrice: String? = nil, isMosques: String? = nil, dbBundleID: Int? = nil) -> RequestBuilder<AddToCartResponse> {
        let path = "/cart/add-to-cart"
        let URLString = SwaggerClientAPI.basePath + path
        let formParams: [String:Any?] = [
            "biProductId": biProductId?.encodeToJSON(),
            "tiIsCheck": tiIsCheck?.encodeToJSON(),
            "iProductQuantity": iProductQuantity?.encodeToJSON(),
            "dbPrice": dbPrice,
            "bunddelId": dbBundleID?.encodeToJSON(),
            "isMosques": isMosques?.encodeToJSON(),
            "IsUpdated": "1".encodeToJSON()
        ]
        debugPrint(URLString)
        debugPrint(formParams)
        let nonNullParameters = APIHelper.rejectNil(formParams)
        let parameters = APIHelper.convertBoolToString(nonNullParameters)
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any?] = [
            "Authorization": authorization,
            "Accept": accept,
            "Lang": lang,
//            "warehouse" : Constant.shared.SELECTED_WAREHOUSE_ID == 0 ? "" : Constant.shared.SELECTED_WAREHOUSE_ID
        ]
        
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)
        debugPrint(headerParameters)
        let requestBuilder: RequestBuilder<AddToCartResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()
        
        return requestBuilder.init(method: "POST", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    
    open class func addToMarketCartWithRequestBuilder(authorization: String, accept: String, lang: String, biProductId: Int? = nil, tiIsCheck: Int? = nil, iProductQuantity: Int? = nil, dbPrice: String? = nil, isMosques: String? = nil, dbBundleID: Int? = nil) -> RequestBuilder<AddToCartResponse> {
        let path = "/cart/add-to-cart"
        let URLString = SwaggerClientAPI.baseMarketPath + path
        let formParams: [String:Any?] = [
            "biProductId": biProductId?.encodeToJSON(),
            "tiIsCheck": tiIsCheck?.encodeToJSON(),
            "iProductQuantity": iProductQuantity?.encodeToJSON(),
            "dbPrice": dbPrice,
            "bunddelId": dbBundleID?.encodeToJSON(),
            "isMosques": isMosques?.encodeToJSON(),
            "IsUpdated": "1".encodeToJSON()
        ]
        debugPrint(URLString)
        debugPrint(formParams)
        let nonNullParameters = APIHelper.rejectNil(formParams)
        let parameters = APIHelper.convertBoolToString(nonNullParameters)
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any?] = [
            "Authorization": authorization,
            "Accept": accept,
            "Lang": lang,
//            "warehouse" : Constant.shared.SELECTED_WAREHOUSE_ID == 0 ? "" : Constant.shared.SELECTED_WAREHOUSE_ID
        ]
        
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)
        debugPrint(headerParameters)
        let requestBuilder: RequestBuilder<AddToCartResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()
        
        return requestBuilder.init(method: "POST", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    /**
     Get Cart Details
     
     - parameter authorization: (header)
     - parameter accept: (header)
     - parameter lang: (header)
     - parameter offset: (query) offset (optional, default to 0)
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func getCart(authorization: String, accept: String, lang: String, offset: Int? = nil, shiftDate: String? = nil, completion: @escaping ((_ data: GetCartResponse?,_ error: Error?) -> Void)) {
        getCartWithRequestBuilder(authorization: authorization, accept: accept, lang: lang, offset: offset, shiftDate: shiftDate).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }
    
    open class func getMarketCart(authorization: String, accept: String, lang: String, offset: Int? = nil, shiftDate: String? = nil, completion: @escaping ((_ data: GetCartResponse?,_ error: Error?) -> Void)) {
        getMarketCartWithRequestBuilder(authorization: authorization, accept: accept, lang: lang, offset: offset, shiftDate: shiftDate).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }
    
    
    /**
     Get Cart Details
     - GET /cart/get-cart
     -
     
     - :
     - type: http
     - name: bearerAuth
     - examples: [{contentType=application/json, example={
     "responseData" : {
     "dMinOrderFreeDelivery" : 5,
     "dVatCost" : "dVatCost",
     "totalPage" : 5,
     "dDistanceCostSetInAdmin" : "dDistanceCostSetInAdmin",
     "dSubTotalWithoutVAT" : "dSubTotalWithoutVAT",
     "shift" : [ {
     "vStartAt" : "vStartAt",
     "tiIsRamadan" : 6,
     "iShiftId" : 1,
     "vShiftDisplayNameAr" : "vShiftDisplayNameAr",
     "tiShiftType" : 1,
     "tiIsFriday" : 1,
     "tiIsRegularShift" : 7,
     "vShiftDisplayNameEn" : "vShiftDisplayNameEn",
     "vCloseAt" : "vCloseAt"
     }, {
     "vStartAt" : "vStartAt",
     "tiIsRamadan" : 6,
     "iShiftId" : 1,
     "vShiftDisplayNameAr" : "vShiftDisplayNameAr",
     "tiShiftType" : 1,
     "tiIsFriday" : 1,
     "tiIsRegularShift" : 7,
     "vShiftDisplayNameEn" : "vShiftDisplayNameEn",
     "vCloseAt" : "vCloseAt"
     } ],
     "dHasFreeOrder" : 2,
     "dVatPercentage" : "dVatPercentage",
     "totalRecord" : 1,
     "dDistanceCost" : "dDistanceCost",
     "dSubTotal" : "dSubTotal",
     "products" : [ {
     "totalProductPriceWithoutVAT" : "totalProductPriceWithoutVAT",
     "vProductName" : "vProductName",
     "productVatCharge" : "productVatCharge",
     "vCategoryName" : "vCategoryName",
     "iCartId" : 7,
     "productPriceWithoutVAT" : "productPriceWithoutVAT",
     "vProductImage" : "vProductImage",
     "dbPrice" : "dbPrice",
     "biProductId" : 3,
     "dDiscountAmount" : "dDiscountAmount",
     "dDiscountedTotalProductPrice" : "dDiscountedTotalProductPrice",
     "iUserId" : 9,
     "tiIsOfferedProduct" : 7,
     "totalProductPriceWithVAT" : "totalProductPriceWithVAT",
     "tiDiscountType" : 4,
     "vProductSKU" : "vProductSKU",
     "vProductUnit" : "vProductUnit",
     "iProductQuantity" : 2
     }, {
     "totalProductPriceWithoutVAT" : "totalProductPriceWithoutVAT",
     "vProductName" : "vProductName",
     "productVatCharge" : "productVatCharge",
     "vCategoryName" : "vCategoryName",
     "iCartId" : 7,
     "productPriceWithoutVAT" : "productPriceWithoutVAT",
     "vProductImage" : "vProductImage",
     "dbPrice" : "dbPrice",
     "biProductId" : 3,
     "dDiscountAmount" : "dDiscountAmount",
     "dDiscountedTotalProductPrice" : "dDiscountedTotalProductPrice",
     "iUserId" : 9,
     "tiIsOfferedProduct" : 7,
     "totalProductPriceWithVAT" : "totalProductPriceWithVAT",
     "tiDiscountType" : 4,
     "vProductSKU" : "vProductSKU",
     "vProductUnit" : "vProductUnit",
     "iProductQuantity" : 2
     } ],
     "dIsCustomShift" : 1,
     "limit" : 6
     },
     "responseMessage" : "responseMessage",
     "responseCode" : 0
     }}]
     - parameter authorization: (header)
     - parameter accept: (header)
     - parameter lang: (header)
     - parameter offset: (query) offset (optional, default to 0)
     
     - returns: RequestBuilder<GetCartResponse>
     */
    open class func getCartWithRequestBuilder(authorization: String, accept: String, lang: String, offset: Int? = nil, shiftDate: String? = nil) -> RequestBuilder<GetCartResponse> {
        let path = "/cart/get-cart"
        let URLString = SwaggerClientAPI.basePath + path
        let parameters: [String:Any]? = nil
        var url = URLComponents(string: URLString)
        url?.queryItems = APIHelper.mapValuesToQueryItems([
            "offset": offset?.encodeToJSON(),
            "IsUpdated": "1".encodeToJSON(),
            "shift_date": shiftDate?.encodeToJSON() ?? ""
        ])
        let nillableHeaders: [String: Any?] = [
            "Authorization": authorization,
            "Accept": accept,
            "Lang": lang,
            "warehouse" : Constant.shared.SELECTED_WAREHOUSE_ID == 0 ? "" : Constant.shared.SELECTED_WAREHOUSE_ID
        ]
        debugPrint(URLString)
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)
        
        let requestBuilder: RequestBuilder<GetCartResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()
        
        return requestBuilder.init(method: "GET", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    
    open class func getMarketCartWithRequestBuilder(authorization: String, accept: String, lang: String, offset: Int? = nil, shiftDate: String? = nil) -> RequestBuilder<GetCartResponse> {
        let path = "/cart/get-cart"
        let URLString = SwaggerClientAPI.baseMarketPath + path
        let parameters: [String:Any]? = nil
        var url = URLComponents(string: URLString)
        url?.queryItems = APIHelper.mapValuesToQueryItems([
            "offset": offset?.encodeToJSON(),
            "IsUpdated": "1".encodeToJSON(),
            "shift_date": shiftDate?.encodeToJSON() ?? ""
        ])
        let nillableHeaders: [String: Any?] = [
            "Authorization": authorization,
            "Accept": accept,
            "Lang": lang,
            "warehouse" : Constant.shared.SELECTED_WAREHOUSE_ID == 0 ? "" : Constant.shared.SELECTED_WAREHOUSE_ID
        ]
        debugPrint(url)
        debugPrint(parameters)
        
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)
        debugPrint(headerParameters)
        let requestBuilder: RequestBuilder<GetCartResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()
        
        return requestBuilder.init(method: "GET", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    /**
     Remove Product from Cart
     
     - parameter authorization: (header)
     - parameter accept: (header)
     - parameter lang: (header)
     - parameter biProductId: (path)
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func removeFromCart(authorization: String, accept: String, lang: String, biProductId: Int, biBundleID: Int? = nil, completion: @escaping ((_ data: AddToCartResponse?,_ error: Error?) -> Void)) {
        removeFromCartWithRequestBuilder(authorization: authorization, accept: accept, lang: lang, biProductId: biProductId, biBundleID: biBundleID).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }
    
    open class func removeFromMarketCart(authorization: String, accept: String, lang: String, biProductId: Int, biBundleID: Int? = nil, completion: @escaping ((_ data: AddToCartResponse?,_ error: Error?) -> Void)) {
        removeFromMarketCartWithRequestBuilder(authorization: authorization, accept: accept, lang: lang, biProductId: biProductId, biBundleID: biBundleID).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }
    
    
    /**
     Remove Product from Cart
     - DELETE /cart/remove-from-cart/{biProductId}
     -
     
     - :
     - type: http
     - name: bearerAuth
     - examples: [{contentType=application/json, example={
     "responseData" : {
     "getCartProducts" : 2,
     "iUserId" : 1,
     "iCartId" : 6,
     "dbFinalPrice" : "dbFinalPrice",
     "dbPrice" : "dbPrice",
     "biProductId" : 5,
     "iProductQuantity" : 5
     },
     "responseMessage" : "responseMessage",
     "responseCode" : 0
     }}]
     - parameter authorization: (header)
     - parameter accept: (header)
     - parameter lang: (header)
     - parameter biProductId: (path)
     
     - returns: RequestBuilder<AddToCartResponse>
     */
    open class func removeFromCartWithRequestBuilder(authorization: String, accept: String, lang: String, biProductId: Int, biBundleID: Int? = nil) -> RequestBuilder<AddToCartResponse> {
        var path = "/cart/remove-from-cart/{biProductId}"
        let biProductIdPreEscape = "\(biProductId)"
        let biProductIdPostEscape = biProductIdPreEscape.addingPercentEncoding(withAllowedCharacters: .urlPathAllowed) ?? ""
        path = path.replacingOccurrences(of: "{biProductId}", with: biProductIdPostEscape, options: .literal, range: nil)
        let URLString = SwaggerClientAPI.basePath + path
        let url = URLComponents(string: URLString)
        let formParams: [String:Any?] = ["bunddelId": biBundleID?.encodeToJSON() ?? 0]
        debugPrint("formParams >>==",formParams)
        let nonNullParameters = APIHelper.rejectNil(formParams)
        let parameters = APIHelper.convertBoolToString(nonNullParameters)
        let nillableHeaders: [String: Any?] = [
            "Authorization": authorization,
            "Accept": accept,
            "Lang": lang,
            "warehouse" : Constant.shared.SELECTED_WAREHOUSE_ID == 0 ? "" : Constant.shared.SELECTED_WAREHOUSE_ID
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)
        debugPrint(headerParameters)
        let requestBuilder: RequestBuilder<AddToCartResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()
        return requestBuilder.init(method: "DELETE", URLString: (url?.string ?? URLString), parameters: biBundleID ?? 0 == 0 ? nil : parameters, isBody: false, headers: headerParameters)
    }
    
    open class func removeFromMarketCartWithRequestBuilder(authorization: String, accept: String, lang: String, biProductId: Int, biBundleID: Int? = nil) -> RequestBuilder<AddToCartResponse> {
        var path = "/cart/remove-from-cart/{biProductId}"
        let biProductIdPreEscape = "\(biProductId)"
        let biProductIdPostEscape = biProductIdPreEscape.addingPercentEncoding(withAllowedCharacters: .urlPathAllowed) ?? ""
        path = path.replacingOccurrences(of: "{biProductId}", with: biProductIdPostEscape, options: .literal, range: nil)
        let URLString = SwaggerClientAPI.baseMarketPath + path
        let url = URLComponents(string: URLString)
        let formParams: [String:Any?] = ["bunddelId": biBundleID?.encodeToJSON() ?? 0]
        debugPrint("formParams >>==",formParams)
        let nonNullParameters = APIHelper.rejectNil(formParams)
        let parameters = APIHelper.convertBoolToString(nonNullParameters)
        let nillableHeaders: [String: Any?] = [
            "Authorization": authorization,
            "Accept": accept,
            "Lang": lang,
            "warehouse" : Constant.shared.SELECTED_WAREHOUSE_ID == 0 ? "" : Constant.shared.SELECTED_WAREHOUSE_ID
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)
        debugPrint(headerParameters)
        let requestBuilder: RequestBuilder<AddToCartResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()
        return requestBuilder.init(method: "DELETE", URLString: (url?.string ?? URLString), parameters: biBundleID ?? 0 == 0 ? nil : parameters, isBody: false, headers: headerParameters)
    }
    
    open class func getFreeDeliveryValue(completion: @escaping ((_ data: FreeDeliveryValueResponse?,_ error: Error?) -> Void)) {
        getFreeDeliveryValueWithRequestBuilder().execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }
    
    open class func getFreeDeliveryValueWithRequestBuilder() -> RequestBuilder<FreeDeliveryValueResponse> {
        let path = "/cart/getFreeDeliveryValue"
        let URLString = SwaggerClientAPI.basePath + path
        let parameters: [String:Any]? = nil
        var url = URLComponents(string: URLString)
        
        let nillableHeaders: [String: Any?] = [
            "Accept": "application/json",
            "Content-Type": "application/json"
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)
        
        let requestBuilder: RequestBuilder<FreeDeliveryValueResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()
        
        return requestBuilder.init(method: "GET", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    
    open class func getCartYouMayAlsoLikeProducts(categoryIDS: String, productIDS: String, completion: @escaping ((_ data: CartYouMayAlsoLikeProductsResponse?,_ error: Error?) -> Void)) {
        getCartYouMayAlsoLikeProductsWithRequestBuilder(categoryIDS: categoryIDS, productIDS: productIDS).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }
    
    open class func getCartYouMayAlsoLikeProductsWithRequestBuilder(categoryIDS: String, productIDS: String) -> RequestBuilder<CartYouMayAlsoLikeProductsResponse> {
        let path = "/product/cartYouMayAlsoLikeProducts"
        let URLString = SwaggerClientAPI.basePath + path + "?category_ids=\(categoryIDS)&product_Ids=\(productIDS)"
        let parameters: [String:Any]? = nil
        var url = URLComponents(string: URLString)
        
        let nillableHeaders: [String: Any?] = [
            "Accept": "application/json",
            "Content-Type": "application/json",
            "Lang": CurrentAppLang,
            "warehouse" : Constant.shared.SELECTED_WAREHOUSE_ID == 0 ? "" : Constant.shared.SELECTED_WAREHOUSE_ID
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)
        debugPrint(headerParameters)
        let requestBuilder: RequestBuilder<CartYouMayAlsoLikeProductsResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()
        
        return requestBuilder.init(method: "GET", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    
    open class func checkCart(iCartId: Int? = nil, completion: @escaping ((_ data: CheckCartResponse?,_ error: Error?) -> Void)) {
        checkCartWithRequestBuilder(iCartId: iCartId).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }
    
    open class func checkCartWithRequestBuilder(iCartId: Int? = nil) -> RequestBuilder<CheckCartResponse> {
        let path = "/cart/check-cart"
        let URLString = SwaggerClientAPI.basePath + path
        let formParams: [String:Any?] = [
            "iCartId": iCartId?.encodeToJSON() ?? 0
        ]
        
        let nonNullParameters = APIHelper.rejectNil(formParams)
        let parameters = APIHelper.convertBoolToString(nonNullParameters)
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any?] = [
            "Authorization": getAuthorizationText(),
            "Accept": "application/json",
            "Lang": CurrentAppLang,
            "warehouse" : Constant.shared.SELECTED_WAREHOUSE_ID == 0 ? "" : Constant.shared.SELECTED_WAREHOUSE_ID
        ]
        debugPrint(URLString)
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)
        debugPrint(headerParameters)
        let requestBuilder: RequestBuilder<CheckCartResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()
        
        return requestBuilder.init(method: "POST", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    
}
