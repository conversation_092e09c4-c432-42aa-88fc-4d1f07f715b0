//
// CountryAPI.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation
import Alamofire


open class CountryAPI {
    /**
     Country Listing

     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func countryListing(accept: String, lang: String, completion: @escaping ((_ data: CountryListResponse?,_ error: Error?) -> Void)) {
        countryListingWithRequestBuilder(accept: accept, lang: lang).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }


    /**
     Country Listing
     - GET /country/country-listing
     - 

     - examples: [{contentType=application/json, example={
  "responseData" : [ {
    "vDialingCode" : 1,
    "vImage" : "vImage",
    "iCountryId" : 6,
    "vCountryName" : "vCountryName"
  }, {
    "vDialingCode" : 1,
    "vImage" : "vImage",
    "iCountryId" : 6,
    "vCountryName" : "vCountryName"
  } ],
  "responseMessage" : "responseMessage",
  "responseCode" : 0
}}]
     - parameter accept: (header)  
     - parameter lang: (header)  

     - returns: RequestBuilder<CountryListResponse> 
     */
    open class func countryListingWithRequestBuilder(accept: String, lang: String) -> RequestBuilder<CountryListResponse> {
        let path = "/country/country-listing"
        let URLString = SwaggerClientAPI.basePath + path
        let parameters: [String:Any]? = nil
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any?] = [
                        "Accept": accept,
                        "Lang": lang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)

        let requestBuilder: RequestBuilder<CountryListResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()

        return requestBuilder.init(method: "GET", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
}
