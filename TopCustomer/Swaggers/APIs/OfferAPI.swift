//
// OfferAPI.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation
import Alamofire


open class OfferAPI {
    /**
     Offer Details

     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter iOfferId: (path)  
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func offerDetails(authorization: String, accept: String, lang: String, iOfferId: Int, completion: @escaping ((_ data: OfferResponse?,_ error: Error?) -> Void)) {
        offerDetailsWithRequestBuilder(authorization: authorization, accept: accept, lang: lang, iOfferId: iOfferId).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }


    /**
     Offer Details
     - GET /offer/offer-details/{iOfferId}
     - 

     - :
       - type: http
       - name: bearerAuth
     - examples: [{contentType=application/json, example={
  "responseData" : {
    "vOfferName" : "vOfferName",
    "iOfferProductQuantity" : 2,
    "vBuyProductName" : "vBuyProductName",
    "tsEndDate" : "tsEndDate",
    "vBuyProductSKU" : "vBuyProductSKU",
    "biOfferProductId" : 3,
    "dOfferAmount" : "dOfferAmount",
    "iOfferId" : 5,
    "productOriginalAmount" : "productOriginalAmount",
    "tsStartDate" : "tsStartDate",
    "vGetProductName" : "vGetProductName",
    "biProductId" : 7,
    "iBuyProductQuantity" : 9,
    "vOfferImage" : "vOfferImage",
    "txOfferDescription" : "txOfferDescription",
    "vGetProductSKU" : "vGetProductSKU",
    "tiDiscountType" : 4,
    "tiOfferType" : 2,
    "productOfferedAmount" : "productOfferedAmount"
  },
  "responseMessage" : "responseMessage",
  "responseCode" : 0
}}]
     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter iOfferId: (path)  

     - returns: RequestBuilder<OfferResponse> 
     */
    open class func offerDetailsWithRequestBuilder(authorization: String, accept: String, lang: String, iOfferId: Int) -> RequestBuilder<OfferResponse> {
        var path = "/offer/offer-details/{iOfferId}"
        let iOfferIdPreEscape = "\(iOfferId)"
        let iOfferIdPostEscape = iOfferIdPreEscape.addingPercentEncoding(withAllowedCharacters: .urlPathAllowed) ?? ""
        path = path.replacingOccurrences(of: "{iOfferId}", with: iOfferIdPostEscape, options: .literal, range: nil)
        let URLString = SwaggerClientAPI.basePath + path
        let parameters: [String:Any]? = nil
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any?] = [
                        "Authorization": authorization,
                        "Accept": accept,
                        "Lang": lang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)

        let requestBuilder: RequestBuilder<OfferResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()

        return requestBuilder.init(method: "GET", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    /**
     Offer Listing

     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter offset: (query) offset (optional, default to 0)
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func offerListing(authorization: String, accept: String, lang: String, offset: Int? = nil, completion: @escaping ((_ data: OfferListResponse?,_ error: Error?) -> Void)) {
        offerListingWithRequestBuilder(authorization: authorization, accept: accept, lang: lang, offset: offset).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }
    
    open class func marketOfferListing(authorization: String, accept: String, lang: String, offset: Int? = nil, completion: @escaping ((_ data: OfferListResponse?,_ error: Error?) -> Void)) {
        marketOfferListingWithRequestBuilder(authorization: authorization, accept: accept, lang: lang, offset: offset).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }


    /**
     Offer Listing
     - GET /offer/offer-listing
     - 

     - :
       - type: http
       - name: bearerAuth
     - examples: [{contentType=application/json, example={
  "responseData" : {
    "offers" : [ {
      "vOfferName" : "vOfferName",
      "iOfferProductQuantity" : 2,
      "vBuyProductName" : "vBuyProductName",
      "tsEndDate" : "tsEndDate",
      "vBuyProductSKU" : "vBuyProductSKU",
      "biOfferProductId" : 3,
      "dOfferAmount" : "dOfferAmount",
      "iOfferId" : 5,
      "productOriginalAmount" : "productOriginalAmount",
      "tsStartDate" : "tsStartDate",
      "vGetProductName" : "vGetProductName",
      "biProductId" : 7,
      "iBuyProductQuantity" : 9,
      "vOfferImage" : "vOfferImage",
      "txOfferDescription" : "txOfferDescription",
      "vGetProductSKU" : "vGetProductSKU",
      "tiDiscountType" : 4,
      "tiOfferType" : 2,
      "productOfferedAmount" : "productOfferedAmount"
    }, {
      "vOfferName" : "vOfferName",
      "iOfferProductQuantity" : 2,
      "vBuyProductName" : "vBuyProductName",
      "tsEndDate" : "tsEndDate",
      "vBuyProductSKU" : "vBuyProductSKU",
      "biOfferProductId" : 3,
      "dOfferAmount" : "dOfferAmount",
      "iOfferId" : 5,
      "productOriginalAmount" : "productOriginalAmount",
      "tsStartDate" : "tsStartDate",
      "vGetProductName" : "vGetProductName",
      "biProductId" : 7,
      "iBuyProductQuantity" : 9,
      "vOfferImage" : "vOfferImage",
      "txOfferDescription" : "txOfferDescription",
      "vGetProductSKU" : "vGetProductSKU",
      "tiDiscountType" : 4,
      "tiOfferType" : 2,
      "productOfferedAmount" : "productOfferedAmount"
    } ],
    "totalPage" : 5,
    "limit" : 6,
    "totalRecord" : 1
  },
  "responseMessage" : "responseMessage",
  "responseCode" : 0
}}]
     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter offset: (query) offset (optional, default to 0)

     - returns: RequestBuilder<OfferListResponse> 
     */
    open class func offerListingWithRequestBuilder(authorization: String, accept: String, lang: String, offset: Int? = nil) -> RequestBuilder<OfferListResponse> {
        let path = "/offer/offer-listing"
        let URLString = SwaggerClientAPI.basePath + path
        let parameters: [String:Any]? = nil
        var url = URLComponents(string: URLString)
        url?.queryItems = APIHelper.mapValuesToQueryItems([
                        "offset": offset?.encodeToJSON()
        ])
        let nillableHeaders: [String: Any?] = [
                        "Authorization": authorization,
                        "Accept": accept,
                        "Lang": lang,
                        "warehouse" : Constant.shared.SELECTED_WAREHOUSE_ID == 0 ? "" : Constant.shared.SELECTED_WAREHOUSE_ID
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)
        debugPrint(headerParameters)
        let requestBuilder: RequestBuilder<OfferListResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()

        return requestBuilder.init(method: "GET", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    
    open class func marketOfferListingWithRequestBuilder(authorization: String, accept: String, lang: String, offset: Int? = nil) -> RequestBuilder<OfferListResponse> {
        let path = "/offer/offer-listing"
        let URLString = SwaggerClientAPI.baseMarketPath + path
        let parameters: [String:Any]? = nil
        var url = URLComponents(string: URLString)
        url?.queryItems = APIHelper.mapValuesToQueryItems([
                        "offset": offset?.encodeToJSON()
        ])
        let nillableHeaders: [String: Any?] = [
                        "Authorization": authorization,
                        "Accept": accept,
                        "Lang": lang,
//                        "warehouse" : Constant.shared.SELECTED_WAREHOUSE_ID == 0 ? "" : Constant.shared.SELECTED_WAREHOUSE_ID
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)
        debugPrint(headerParameters)
        debugPrint(url)
        debugPrint(parameters)
        
        let requestBuilder: RequestBuilder<OfferListResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()

        return requestBuilder.init(method: "GET", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
}
