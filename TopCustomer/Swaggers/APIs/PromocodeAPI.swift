//
// PromocodeAPI.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation
import Alamofire


open class PromocodeAPI {
    /**
     Promocode Details

     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter vPromocode: (form)  (optional)
     - parameter dSubTotal: (form)  (optional)
     - parameter dDistanceCost: (form)  (optional)
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func promocodeCheck(authorization: String, accept: String, lang: String, vPromocode: String? = nil, dSubTotal: String? = nil, dDistanceCost: String? = nil, completion: @escaping ((_ data: PromocodeCheckResponse?,_ error: Error?) -> Void)) {
        promocodeCheckWithRequestBuilder(authorization: authorization, accept: accept, lang: lang, vPromocode: vPromocode, dSubTotal: dSubTotal, dDistanceCost: dDistanceCost).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }
    
    open class func marketPromoCodeCheck(authorization: String, accept: String, lang: String, vPromocode: String? = nil, dSubTotal: String? = nil, dDistanceCost: String? = nil, completion: @escaping ((_ data: PromocodeCheckResponse?,_ error: Error?) -> Void)) {
        marketPromoCodeCheckWithRequestBuilder(authorization: authorization, accept: accept, lang: lang, vPromocode: vPromocode, dSubTotal: dSubTotal, dDistanceCost: dDistanceCost).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }


    /**
     Promocode Details
     - POST /promocode/promocode-check
     - 

     - :
       - type: http
       - name: bearerAuth
     - examples: [{contentType=application/json, example={
  "responseData" : {
    "dDiscountAmount" : "dDiscountAmount",
    "dOrderAmount" : "dOrderAmount",
    "dDiscountedOrderAmount" : "dDiscountedOrderAmount",
    "dVatCost" : "dVatCost",
    "iPromocodeUseId" : 6,
    "tiDiscountType" : 1,
    "dDistanceCost" : "dDistanceCost"
  },
  "responseMessage" : "responseMessage",
  "responseCode" : 0
}}]
     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter vPromocode: (form)  (optional)
     - parameter dSubTotal: (form)  (optional)
     - parameter dDistanceCost: (form)  (optional)

     - returns: RequestBuilder<PromocodeCheckResponse> 
     */
    open class func promocodeCheckWithRequestBuilder(authorization: String, accept: String, lang: String, vPromocode: String? = nil, dSubTotal: String? = nil, dDistanceCost: String? = nil) -> RequestBuilder<PromocodeCheckResponse> {
        let path = "/promocode/promocode-check"
        let URLString = SwaggerClientAPI.basePath + path
        let formParams: [String:Any?] = [
                "vPromocode": vPromocode,
                "dSubTotal": dSubTotal,
                "dDistanceCost": dDistanceCost
        ]

        let nonNullParameters = APIHelper.rejectNil(formParams)
        let parameters = APIHelper.convertBoolToString(nonNullParameters)
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any?] = [
                        "Authorization": authorization,
                        "Accept": accept,
                        "Lang": lang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)

        let requestBuilder: RequestBuilder<PromocodeCheckResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()

        return requestBuilder.init(method: "POST", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }

    open class func marketPromoCodeCheckWithRequestBuilder(authorization: String, accept: String, lang: String, vPromocode: String? = nil, dSubTotal: String? = nil, dDistanceCost: String? = nil) -> RequestBuilder<PromocodeCheckResponse> {
        let path = "/promocode/promocode-check"
        let URLString = SwaggerClientAPI.baseMarketPath + path
        let formParams: [String:Any?] = [
                "vPromocode": vPromocode,
                "dSubTotal": dSubTotal,
                "dDistanceCost": dDistanceCost
        ]

        let nonNullParameters = APIHelper.rejectNil(formParams)
        let parameters = APIHelper.convertBoolToString(nonNullParameters)
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any?] = [
                        "Authorization": authorization,
                        "Accept": accept,
                        "Lang": lang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)

        let requestBuilder: RequestBuilder<PromocodeCheckResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()

        return requestBuilder.init(method: "POST", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    /**
     Promocode Remove

     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter iPromocodeUseId: (path)  
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func removePromocode(authorization: String, accept: String, lang: String, iPromocodeUseId: Int, completion: @escaping ((_ data: CommonFields?,_ error: Error?) -> Void)) {
        removePromocodeWithRequestBuilder(authorization: authorization, accept: accept, lang: lang, iPromocodeUseId: iPromocodeUseId).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }


    /**
     Promocode Remove
     - DELETE /promocode/remove-promocode/{iPromocodeUseId}
     - 

     - :
       - type: http
       - name: bearerAuth
     - examples: [{contentType=application/json, example={
  "responseMessage" : "responseMessage",
  "responseCode" : 0
}}]
     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter iPromocodeUseId: (path)  

     - returns: RequestBuilder<CommonFields> 
     */
    open class func removePromocodeWithRequestBuilder(authorization: String, accept: String, lang: String, iPromocodeUseId: Int) -> RequestBuilder<CommonFields> {
        var path = "/promocode/remove-promocode/{iPromocodeUseId}"
        let iPromocodeUseIdPreEscape = "\(iPromocodeUseId)"
        let iPromocodeUseIdPostEscape = iPromocodeUseIdPreEscape.addingPercentEncoding(withAllowedCharacters: .urlPathAllowed) ?? ""
        path = path.replacingOccurrences(of: "{iPromocodeUseId}", with: iPromocodeUseIdPostEscape, options: .literal, range: nil)
        let URLString = SwaggerClientAPI.basePath + path
        let parameters: [String:Any]? = nil
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any?] = [
                        "Authorization": authorization,
                        "Accept": accept,
                        "Lang": lang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)

        let requestBuilder: RequestBuilder<CommonFields>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()

        return requestBuilder.init(method: "DELETE", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
}
