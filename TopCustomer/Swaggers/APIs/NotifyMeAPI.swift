//
// NotifyMeAPI.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation
import Alamofire


open class NotifyMeAPI {
    /**
     notify product

     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter biProductId: (form)  (optional)
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func notifyMe(authorization: String, accept: String, lang: String, biProductId: Int? = nil, biBundleId: Int? = nil, completion: @escaping ((_ data: CommonFields?,_ error: Error?) -> Void)) {
        notifyMeWithRequestBuilder(authorization: authorization, accept: accept, lang: lang, biProductId: biProductId, biBundleId: biBundleId).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }


    /**
     notify product
     - POST /notify-me
     - 

     - :
       - type: http
       - name: bearerAuth
     - examples: [{contentType=application/json, example={
  "responseMessage" : "responseMessage",
  "responseCode" : 0
}}]
     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter biProductId: (form)  (optional)

     - returns: RequestBuilder<CommonFields> 
     */
    open class func notifyMeWithRequestBuilder(authorization: String, accept: String, lang: String, biProductId: Int? = nil, biBundleId: Int? = nil) -> RequestBuilder<CommonFields> {
        let path = "/notify-me"
        let URLString = SwaggerClientAPI.basePath + path
        // "biBundleId": biBundleId?.encodeToJSON()
        let formParams: [String:Any?] = [
                "biProductId": biProductId?.encodeToJSON()
        ]

        let nonNullParameters = APIHelper.rejectNil(formParams)
        let parameters = APIHelper.convertBoolToString(nonNullParameters)
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any?] = [
                        "Authorization": authorization,
                        "Accept": accept,
                        "Lang": lang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)

        let requestBuilder: RequestBuilder<CommonFields>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()

        return requestBuilder.init(method: "POST", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
}
