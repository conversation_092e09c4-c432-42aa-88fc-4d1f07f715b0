//
// RatingAPI.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation
import Alamofire


open class RatingAPI {
    /**
     Add rating

     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter iOrderId: (form)  (optional)
     - parameter dbRating: (form)  (optional)
     - parameter tComment: (form)  (optional)
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func addRating(authorization: String, accept: String, lang: String, iOrderId: Int? = nil, dbRating: String? = nil, tComment: String? = nil, completion: @escaping ((_ data: AddRatingResponse?,_ error: Error?) -> Void)) {
        addRatingWithRequestBuilder(authorization: authorization, accept: accept, lang: lang, iOrderId: iOrderId, dbRating: dbRating, tComment: tComment).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }


    /**
     Add rating
     - POST /rating/add-rating
     - 

     - :
       - type: http
       - name: bearerAuth
     - examples: [{contentType=application/json, example={
  "responseData" : {
    "iOrderId" : "iOrderId",
    "dbRating" : 6
  },
  "responseMessage" : "responseMessage",
  "responseCode" : 0
}}]
     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter iOrderId: (form)  (optional)
     - parameter dbRating: (form)  (optional)
     - parameter tComment: (form)  (optional)

     - returns: RequestBuilder<AddRatingResponse> 
     */
    open class func addRatingWithRequestBuilder(authorization: String, accept: String, lang: String, iOrderId: Int? = nil, dbRating: String? = nil, tComment: String? = nil) -> RequestBuilder<AddRatingResponse> {
        let path = "/rating/add-rating"
        let URLString = SwaggerClientAPI.basePath + path
        let formParams: [String:Any?] = [
                "iOrderId": iOrderId?.encodeToJSON(),
                "dbRating": dbRating,
                "tComment": tComment
        ]

        let nonNullParameters = APIHelper.rejectNil(formParams)
        let parameters = APIHelper.convertBoolToString(nonNullParameters)
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any?] = [
                        "Authorization": authorization,
                        "Accept": accept,
                        "Lang": lang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)

        let requestBuilder: RequestBuilder<AddRatingResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()

        return requestBuilder.init(method: "POST", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    /**
     Skip Rating

     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter iOrderId: (path) iOrderId 
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func skipRating(authorization: String, accept: String, lang: String, iOrderId: Int, completion: @escaping ((_ data: SkipRatingResponse?,_ error: Error?) -> Void)) {
        skipRatingWithRequestBuilder(authorization: authorization, accept: accept, lang: lang, iOrderId: iOrderId).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }


    /**
     Skip Rating
     - GET /rating/skip-rating/{iOrderId}
     - 

     - :
       - type: http
       - name: bearerAuth
     - examples: [{contentType=application/json, example={
  "responseMessage" : "responseMessage",
  "responseCode" : 0
}}]
     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter iOrderId: (path) iOrderId 

     - returns: RequestBuilder<SkipRatingResponse> 
     */
    open class func skipRatingWithRequestBuilder(authorization: String, accept: String, lang: String, iOrderId: Int) -> RequestBuilder<SkipRatingResponse> {
        var path = "/rating/skip-rating/{iOrderId}"
        let iOrderIdPreEscape = "\(iOrderId)"
        let iOrderIdPostEscape = iOrderIdPreEscape.addingPercentEncoding(withAllowedCharacters: .urlPathAllowed) ?? ""
        path = path.replacingOccurrences(of: "{iOrderId}", with: iOrderIdPostEscape, options: .literal, range: nil)
        let URLString = SwaggerClientAPI.basePath + path
        let parameters: [String:Any]? = nil
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any?] = [
                        "Authorization": authorization,
                        "Accept": accept,
                        "Lang": lang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)

        let requestBuilder: RequestBuilder<SkipRatingResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()

        return requestBuilder.init(method: "GET", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
}
