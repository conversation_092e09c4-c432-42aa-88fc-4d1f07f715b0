//
// OrderAPI.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation
import Alamofire


open class OrderAPI {
    /**
     Cancel your Order

     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter iOrderId: (path) iOrderId 
     - parameter iOrderCancelReasonId: (query) iOrderCancelReasonId (optional)
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func cancelOrder(authorization: String, accept: String, lang: String, iOrderId: Int, iOrderCancelReasonId: Int? = nil, completion: @escaping ((_ data: CommonFields?,_ error: Error?) -> Void)) {
        cancelOrderWithRequestBuilder(authorization: authorization, accept: accept, lang: lang, iOrderId: iOrderId, iOrderCancelReasonId: iOrderCancelReasonId).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }


    /**
     Cancel your Order
     - GET /order/cancel-order/{iOrderId}
     - 

     - :
       - type: http
       - name: bearerAuth
     - examples: [{contentType=application/json, example={
  "responseMessage" : "responseMessage",
  "responseCode" : 0
}}]
     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter iOrderId: (path) iOrderId 
     - parameter iOrderCancelReasonId: (query) iOrderCancelReasonId (optional)

     - returns: RequestBuilder<CommonFields> 
     */
    open class func cancelOrderWithRequestBuilder(authorization: String, accept: String, lang: String, iOrderId: Int, iOrderCancelReasonId: Int? = nil) -> RequestBuilder<CommonFields> {
        var path = "/order/cancel-order/{iOrderId}"
        let iOrderIdPreEscape = "\(iOrderId)"
        let iOrderIdPostEscape = iOrderIdPreEscape.addingPercentEncoding(withAllowedCharacters: .urlPathAllowed) ?? ""
        path = path.replacingOccurrences(of: "{iOrderId}", with: iOrderIdPostEscape, options: .literal, range: nil)
        let URLString = SwaggerClientAPI.basePath + path
        let parameters: [String:Any]? = nil
        var url = URLComponents(string: URLString)
        url?.queryItems = APIHelper.mapValuesToQueryItems([
                        "iOrderCancelReasonId": iOrderCancelReasonId?.encodeToJSON()
        ])
        let nillableHeaders: [String: Any?] = [
                        "Authorization": authorization,
                        "Accept": accept,
                        "Lang": lang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)

        let requestBuilder: RequestBuilder<CommonFields>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()

        return requestBuilder.init(method: "GET", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    /**
     Cancel Schedule from the schedule order details page

     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter iOrderId: (path) iOrderId 
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func cancelSchedule(authorization: String, accept: String, lang: String, iOrderId: Int, completion: @escaping ((_ data: CommonFields?,_ error: Error?) -> Void)) {
        cancelScheduleWithRequestBuilder(authorization: authorization, accept: accept, lang: lang, iOrderId: iOrderId).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }


    /**
     Cancel Schedule from the schedule order details page
     - GET /order/cancel-schedule/{iOrderId}
     - 

     - :
       - type: http
       - name: bearerAuth
     - examples: [{contentType=application/json, example={
  "responseMessage" : "responseMessage",
  "responseCode" : 0
}}]
     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter iOrderId: (path) iOrderId 

     - returns: RequestBuilder<CommonFields> 
     */
    open class func cancelScheduleWithRequestBuilder(authorization: String, accept: String, lang: String, iOrderId: Int) -> RequestBuilder<CommonFields> {
        var path = "/order/cancel-schedule/{iOrderId}"
        let iOrderIdPreEscape = "\(iOrderId)"
        let iOrderIdPostEscape = iOrderIdPreEscape.addingPercentEncoding(withAllowedCharacters: .urlPathAllowed) ?? ""
        path = path.replacingOccurrences(of: "{iOrderId}", with: iOrderIdPostEscape, options: .literal, range: nil)
        let URLString = SwaggerClientAPI.basePath + path
        let parameters: [String:Any]? = nil
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any?] = [
                        "Authorization": authorization,
                        "Accept": accept,
                        "Lang": lang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)

        let requestBuilder: RequestBuilder<CommonFields>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()

        return requestBuilder.init(method: "GET", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    /**
     * enum for parameter iReccuringType
     */
    public enum IReccuringType_createOrder: Int { 
        case _1 = 1
        case _2 = 2
        case _3 = 3
        case _4 = 4
    }

    /**
     Create Order

     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter iCartId: (form)  (optional)
     - parameter iAddressId: (form)  (optional)
     - parameter iReccuringType: (form)  (optional)
     - parameter tAdditionalNote: (form)  (optional)
     - parameter iPromocodeUseId: (form)  (optional)
     - parameter dOrderTotal: (form)  (optional)
     - parameter dVatCost: (form)  (optional)
     - parameter tiShiftType: (form)  (optional)
     - parameter vAlternativeISDCode: (form)  (optional)
     - parameter vAlternativeMobileNumber: (form)  (optional)
     - parameter iShiftId: (form)  (optional)
     - parameter vShiftDisplayNameEn: (form)  (optional)
     - parameter vShiftDisplayNameAr: (form)  (optional)
     - parameter vStartAt: (form)  (optional)
     - parameter vCloseAt: (form)  (optional)
     - parameter tiIsFriday: (form)  (optional)
     - parameter tiIsRamadan: (form)  (optional)
     - parameter tiIsRegularShift: (form)  (optional)
     - parameter timezone: (header)  (optional, default to en)
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func createOrder(authorization: String, accept: String, lang: String, iCartId: Int? = nil, iAddressId: Int? = nil, iReccuringType: IReccuringType_createOrder? = nil, tAdditionalNote: String? = nil, iPromocodeUseId: Int? = nil, dOrderTotal: String? = nil, dVatCost: String? = nil, tiShiftType: Int? = nil, vAlternativeISDCode: String? = nil, vAlternativeMobileNumber: String? = nil, iShiftId: Int? = nil, vShiftDisplayNameEn: String? = nil, vShiftDisplayNameAr: String? = nil, vStartAt: String? = nil, vCloseAt: String? = nil, tiIsFriday: Int? = nil, tiIsRamadan: Int? = nil, tiIsRegularShift: Int? = nil, timezone: String? = nil, isreward: Bool? = false, rewardamount: Double? = 0.0, isMosques: String? = nil, isCartIncludeBundle: Int, expectedTime: Date? = nil, completion: @escaping ((_ data: CreateOrderResponse?,_ error: Error?) -> Void)) {
        createOrderWithRequestBuilder(authorization: authorization, accept: accept, lang: lang, iCartId: iCartId, iAddressId: iAddressId, iReccuringType: iReccuringType, tAdditionalNote: tAdditionalNote, iPromocodeUseId: iPromocodeUseId, dOrderTotal: dOrderTotal, dVatCost: dVatCost, tiShiftType: tiShiftType, vAlternativeISDCode: vAlternativeISDCode, vAlternativeMobileNumber: vAlternativeMobileNumber, iShiftId: iShiftId, vShiftDisplayNameEn: vShiftDisplayNameEn, vShiftDisplayNameAr: vShiftDisplayNameAr, vStartAt: vStartAt, vCloseAt: vCloseAt, tiIsFriday: tiIsFriday, tiIsRamadan: tiIsRamadan, tiIsRegularShift: tiIsRegularShift, timezone: timezone, isreward: isreward, rewardamount: rewardamount, isMosques: isMosques, isCartIncludeBundle: isCartIncludeBundle, expectedTime: expectedTime).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }
    
    
    open class func createMarketOrder(authorization: String, accept: String, lang: String, iCartId: Int? = nil, iAddressId: Int? = nil, iReccuringType: IReccuringType_createOrder? = nil, tAdditionalNote: String? = nil, iPromocodeUseId: Int? = nil, dOrderTotal: String? = nil, dVatCost: String? = nil, tiShiftType: Int? = nil, vAlternativeISDCode: String? = nil, vAlternativeMobileNumber: String? = nil, iShiftId: Int? = nil, vShiftDisplayNameEn: String? = nil, vShiftDisplayNameAr: String? = nil, vStartAt: String? = nil, vCloseAt: String? = nil, tiIsFriday: Int? = nil, tiIsRamadan: Int? = nil, tiIsRegularShift: Int? = nil, timezone: String? = nil, isreward: Bool? = false, rewardamount: Double? = 0.0, isMosques: String? = nil, isCartIncludeBundle: Int, expectedTime: Date? = nil, completion: @escaping ((_ data: CreateOrderResponse?,_ error: Error?) -> Void)) {
        createMarketOrderWithRequestBuilder(authorization: authorization, accept: accept, lang: lang, iCartId: iCartId, iAddressId: iAddressId, iReccuringType: iReccuringType, tAdditionalNote: tAdditionalNote, iPromocodeUseId: iPromocodeUseId, dOrderTotal: dOrderTotal, dVatCost: dVatCost, tiShiftType: tiShiftType, vAlternativeISDCode: vAlternativeISDCode, vAlternativeMobileNumber: vAlternativeMobileNumber, iShiftId: iShiftId, vShiftDisplayNameEn: vShiftDisplayNameEn, vShiftDisplayNameAr: vShiftDisplayNameAr, vStartAt: vStartAt, vCloseAt: vCloseAt, tiIsFriday: tiIsFriday, tiIsRamadan: tiIsRamadan, tiIsRegularShift: tiIsRegularShift, timezone: timezone, isreward: isreward, rewardamount: rewardamount, isMosques: isMosques, isCartIncludeBundle: isCartIncludeBundle, expectedTime: expectedTime).execute { (response, error) -> Void in
            print(response)
            completion(response?.body, error)
        }
    }


    /**
     Create Order
     - POST /order/create-order
     - 

     - :
       - type: http
       - name: bearerAuth
     - examples: [{contentType=application/json, example={
  "responseData" : {
    "iOrderId" : 6,
    "tsOrderedAt" : "tsOrderedAt",
    "tiOrderStatus" : 1,
    "dOrderTotal" : "dOrderTotal",
    "vOrderNumber" : "vOrderNumber"
  },
  "responseMessage" : "responseMessage",
  "responseCode" : 0
}}]
     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter iCartId: (form)  (optional)
     - parameter iAddressId: (form)  (optional)
     - parameter iReccuringType: (form)  (optional)
     - parameter tAdditionalNote: (form)  (optional)
     - parameter iPromocodeUseId: (form)  (optional)
     - parameter dOrderTotal: (form)  (optional)
     - parameter dVatCost: (form)  (optional)
     - parameter tiShiftType: (form)  (optional)
     - parameter vAlternativeISDCode: (form)  (optional)
     - parameter vAlternativeMobileNumber: (form)  (optional)
     - parameter iShiftId: (form)  (optional)
     - parameter vShiftDisplayNameEn: (form)  (optional)
     - parameter vShiftDisplayNameAr: (form)  (optional)
     - parameter vStartAt: (form)  (optional)
     - parameter vCloseAt: (form)  (optional)
     - parameter tiIsFriday: (form)  (optional)
     - parameter tiIsRamadan: (form)  (optional)
     - parameter tiIsRegularShift: (form)  (optional)
     - parameter timezone: (header)  (optional, default to en)

     - returns: RequestBuilder<CreateOrderResponse> 
     */
    open class func createOrderWithRequestBuilder(authorization: String, accept: String, lang: String, iCartId: Int? = nil, iAddressId: Int? = nil, iReccuringType: IReccuringType_createOrder? = nil, tAdditionalNote: String? = nil, iPromocodeUseId: Int? = nil, dOrderTotal: String? = nil, dVatCost: String? = nil, tiShiftType: Int? = nil, vAlternativeISDCode: String? = nil, vAlternativeMobileNumber: String? = nil, iShiftId: Int? = nil, vShiftDisplayNameEn: String? = nil, vShiftDisplayNameAr: String? = nil, vStartAt: String? = nil, vCloseAt: String? = nil, tiIsFriday: Int? = nil, tiIsRamadan: Int? = nil, tiIsRegularShift: Int? = nil, timezone: String? = nil, isreward: Bool? = false, rewardamount: Double? = 0.0, isMosques: String? = nil, isCartIncludeBundle: Int? = 0, expectedTime: Date? = nil) -> RequestBuilder<CreateOrderResponse> {
        let path = "/order/create-order"
        let URLString = SwaggerClientAPI.basePath + path
        let formParams: [String:Any?] = [
                "iCartId": iCartId?.encodeToJSON(),
                "iAddressId": iAddressId?.encodeToJSON(),
                "iReccuringType": iReccuringType?.rawValue,
                "tAdditionalNote": tAdditionalNote,
                "iPromocodeUseId": iPromocodeUseId?.encodeToJSON(),
                "dOrderTotal": dOrderTotal,
                "dVatCost": dVatCost,
                "tiShiftType": tiShiftType?.encodeToJSON(),
                "vAlternativeISDCode": vAlternativeISDCode,
                "vAlternativeMobileNumber": vAlternativeMobileNumber,
                "iShiftId": iShiftId?.encodeToJSON(),
                "vShiftDisplayNameEn": vShiftDisplayNameEn,
                "vShiftDisplayNameAr": vShiftDisplayNameAr,
                "vStartAt": vStartAt,
                "vCloseAt": vCloseAt,
                "tiIsFriday": tiIsFriday?.encodeToJSON(),
                "tiIsRamadan": tiIsRamadan?.encodeToJSON(),
                "tiIsRegularShift": tiIsRegularShift?.encodeToJSON(),
                "isreward": isreward == true ? 1.encodeToJSON() : 0.encodeToJSON(),
                "rewardamount": rewardamount?.encodeToJSON(),
                "bunddelId": isCartIncludeBundle?.encodeToJSON(),
                "isMosques": isMosques?.encodeToJSON(),
                "IsUpdated": "1".encodeToJSON(),
                "expectedTime": expectedTime?.convertDateToStringByLocale(dateFormat: "YYYY-MM-dd"),
        ]

        let nonNullParameters = APIHelper.rejectNil(formParams)
        let parameters = APIHelper.convertBoolToString(nonNullParameters)
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any?] = [
                        "Authorization": authorization,
                        "Accept": accept,
                        "Lang": lang,
                        "Timezone": timezone
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)

        let requestBuilder: RequestBuilder<CreateOrderResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()

        return requestBuilder.init(method: "POST", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    
    
    open class func createMarketOrderWithRequestBuilder(authorization: String, accept: String, lang: String, iCartId: Int? = nil, iAddressId: Int? = nil, iReccuringType: IReccuringType_createOrder? = nil, tAdditionalNote: String? = nil, iPromocodeUseId: Int? = nil, dOrderTotal: String? = nil, dVatCost: String? = nil, tiShiftType: Int? = nil, vAlternativeISDCode: String? = nil, vAlternativeMobileNumber: String? = nil, iShiftId: Int? = nil, vShiftDisplayNameEn: String? = nil, vShiftDisplayNameAr: String? = nil, vStartAt: String? = nil, vCloseAt: String? = nil, tiIsFriday: Int? = nil, tiIsRamadan: Int? = nil, tiIsRegularShift: Int? = nil, timezone: String? = nil, isreward: Bool? = false, rewardamount: Double? = 0.0, isMosques: String? = nil, isCartIncludeBundle: Int? = 0, expectedTime: Date? = nil) -> RequestBuilder<CreateOrderResponse> {
        let path = "/order/create-order"
        let URLString = SwaggerClientAPI.baseMarketPath + path
        let formParams: [String:Any?] = [
                "iCartId": iCartId?.encodeToJSON(),
                "iAddressId": iAddressId?.encodeToJSON(),
                "iReccuringType": iReccuringType?.rawValue,
                "tAdditionalNote": tAdditionalNote,
                "iPromocodeUseId": iPromocodeUseId?.encodeToJSON(),
                "dOrderTotal": dOrderTotal,
                "dVatCost": dVatCost,
                "tiShiftType": tiShiftType?.encodeToJSON(),
                "vAlternativeISDCode": vAlternativeISDCode,
                "vAlternativeMobileNumber": vAlternativeMobileNumber,
                "iShiftId": iShiftId?.encodeToJSON(),
                "vShiftDisplayNameEn": vShiftDisplayNameEn,
                "vShiftDisplayNameAr": vShiftDisplayNameAr,
                "vStartAt": vStartAt,
                "vCloseAt": vCloseAt,
                "tiIsFriday": tiIsFriday?.encodeToJSON(),
                "tiIsRamadan": tiIsRamadan?.encodeToJSON(),
                "tiIsRegularShift": tiIsRegularShift?.encodeToJSON(),
                "isreward": isreward == true ? 1.encodeToJSON() : 0.encodeToJSON(),
                "rewardamount": rewardamount?.encodeToJSON(),
                "bunddelId": isCartIncludeBundle?.encodeToJSON(),
                "isMosques": isMosques?.encodeToJSON(),
                "IsUpdated": "1".encodeToJSON(),
                "expectedTime": expectedTime?.convertDateToStringByLocale(dateFormat: "YYYY-MM-dd"),
        ]

        let nonNullParameters = APIHelper.rejectNil(formParams)
        let parameters = APIHelper.convertBoolToString(nonNullParameters)
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any?] = [
                        "Authorization": authorization,
                        "Accept": accept,
                        "Lang": lang,
                        "Timezone": timezone,
                        "warehouse" : Constant.shared.SELECTED_WAREHOUSE_ID == 0 ? "" : Constant.shared.SELECTED_WAREHOUSE_ID
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)

        let requestBuilder: RequestBuilder<CreateOrderResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()
        
        print(url)
        print(parameters)

        return requestBuilder.init(method: "POST", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    /**
     Delete Order To Add more cart items

     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter iOrderId: (path) iOrderId 
     - parameter iOrderCancelReasonId: (query) iOrderCancelReasonId (optional)
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func deleteOrder(authorization: String, accept: String, lang: String, iOrderId: Int, iOrderCancelReasonId: Int? = nil, completion: @escaping ((_ data: CommonFields?,_ error: Error?) -> Void)) {
        deleteOrderWithRequestBuilder(authorization: authorization, accept: accept, lang: lang, iOrderId: iOrderId, iOrderCancelReasonId: iOrderCancelReasonId).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }


    /**
     Delete Order To Add more cart items
     - GET /order/delete-order/{iOrderId}
     - 

     - :
       - type: http
       - name: bearerAuth
     - examples: [{contentType=application/json, example={
  "responseMessage" : "responseMessage",
  "responseCode" : 0
}}]
     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter iOrderId: (path) iOrderId 
     - parameter iOrderCancelReasonId: (query) iOrderCancelReasonId (optional)

     - returns: RequestBuilder<CommonFields> 
     */
    open class func deleteOrderWithRequestBuilder(authorization: String, accept: String, lang: String, iOrderId: Int, iOrderCancelReasonId: Int? = nil) -> RequestBuilder<CommonFields> {
        var path = "/order/delete-order/{iOrderId}"
        let iOrderIdPreEscape = "\(iOrderId)"
        let iOrderIdPostEscape = iOrderIdPreEscape.addingPercentEncoding(withAllowedCharacters: .urlPathAllowed) ?? ""
        path = path.replacingOccurrences(of: "{iOrderId}", with: iOrderIdPostEscape, options: .literal, range: nil)
        let URLString = SwaggerClientAPI.basePath + path
        let parameters: [String:Any]? = nil
        var url = URLComponents(string: URLString)
        url?.queryItems = APIHelper.mapValuesToQueryItems([
                        "iOrderCancelReasonId": iOrderCancelReasonId?.encodeToJSON()
        ])
        let nillableHeaders: [String: Any?] = [
                        "Authorization": authorization,
                        "Accept": accept,
                        "Lang": lang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)

        let requestBuilder: RequestBuilder<CommonFields>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()

        return requestBuilder.init(method: "GET", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    /**
     Get Order Details

     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter iOrderId: (path) iOrderId 
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func getOrder(authorization: String, accept: String, lang: String, iOrderId: Int, completion: @escaping ((_ data: OrderDetailResponse?,_ error: Error?) -> Void)) {
        getOrderWithRequestBuilder(authorization: authorization, accept: accept, lang: lang, iOrderId: iOrderId).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }
    
    open class func getMarketOrder(authorization: String, accept: String, lang: String, iOrderId: Int, completion: @escaping ((_ data: OrderDetailResponse?,_ error: Error?) -> Void)) {
        getMarketOrderWithRequestBuilder(authorization: authorization, accept: accept, lang: lang, iOrderId: iOrderId).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }


    /**
     Get Order Details
     - GET /order/get-order-details/{iOrderId}
     - 

     - :
       - type: http
       - name: bearerAuth
     - examples: [{contentType=application/json, example={
  "responseData" : {
    "iOrderId" : 6,
    "tsOrderedAt" : "tsOrderedAt",
    "dDriverLatitude" : "dDriverLatitude",
    "dMinOrderFreeDelivery" : 7,
    "iReccuringType" : 5,
    "vInvoicePdfName" : "vInvoicePdfName",
    "vPromocode" : "vPromocode",
    "dHasFreeOrder" : 1,
    "productDetails" : [ {
      "vProductName" : "vProductName",
      "txProductDescription" : "txProductDescription",
      "vCategoryName" : "vCategoryName",
      "vProductSKU" : "vProductSKU",
      "dbPrice" : "dbPrice",
      "vProductImage" : "vProductImage",
      "vProductUnit" : "vProductUnit",
      "biProductId" : 5,
      "iProductQuantity" : 2
    }, {
      "vProductName" : "vProductName",
      "txProductDescription" : "txProductDescription",
      "vCategoryName" : "vCategoryName",
      "vProductSKU" : "vProductSKU",
      "dbPrice" : "dbPrice",
      "vProductImage" : "vProductImage",
      "vProductUnit" : "vProductUnit",
      "biProductId" : 5,
      "iProductQuantity" : 2
    } ],
    "vPaymentId" : "vPaymentId",
    "txAddress" : "txAddress",
    "additionalSecondsEstForTruck" : "additionalSecondsEstForTruck",
    "dDriverLongitude" : "dDriverLongitude",
    "tiShiftType" : 4,
    "vShiftDisplayName" : "vShiftDisplayName",
    "vOrderNumber" : "vOrderNumber",
    "vAlternativeISDCode" : "vAlternativeISDCode",
    "tiIsReccuring" : 5,
    "additionalSecondsEstForCar" : "additionalSecondsEstForCar",
    "vDriverMobile" : "vDriverMobile",
    "dTotalAmount" : "dTotalAmount",
    "tiTransactionType" : 3,
    "tiOrderStatus" : 1,
    "dDistanceCostSetInAdmin" : "dDistanceCostSetInAdmin",
    "dVatPercentage" : "dVatPercentage",
    "vAlternativeMobileNumber" : "vAlternativeMobileNumber",
    "dOrderLatitude" : "dOrderLatitude",
    "tiPaymentStatus" : 7,
    "tiIsPaybtnEnabled" : 2,
    "tsExpectedEndTime" : "tsExpectedEndTime",
    "tAdditionalNote" : "tAdditionalNote",
    "dOrderLongitude" : "dOrderLongitude",
    "dDeliveryCharge" : "dDeliveryCharge",
    "dOrderSubTotal" : "dOrderSubTotal",
    "dOrderDiscount" : "dOrderDiscount",
    "iOrderDiscountType" : 9,
    "vType" : "vType",
    "dVatCharge" : "dVatCharge",
    "tsExpectedStartTime" : "tsExpectedStartTime",
    "tsOrderDeliveredAt" : "tsOrderDeliveredAt",
    "tiIsWalletUsed" : 2
  },
  "responseMessage" : "responseMessage",
  "responseCode" : 0
}}]
     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter iOrderId: (path) iOrderId 

     - returns: RequestBuilder<OrderDetailResponse> 
     */
    open class func getOrderWithRequestBuilder(authorization: String, accept: String, lang: String, iOrderId: Int) -> RequestBuilder<OrderDetailResponse> {
        var path = "/order/get-order-details/{iOrderId}"
        let iOrderIdPreEscape = "\(iOrderId)"
        let iOrderIdPostEscape = iOrderIdPreEscape.addingPercentEncoding(withAllowedCharacters: .urlPathAllowed) ?? ""
        path = path.replacingOccurrences(of: "{iOrderId}", with: iOrderIdPostEscape, options: .literal, range: nil)
        let URLString = SwaggerClientAPI.basePath + path
        let parameters: [String:Any]? = nil
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any?] = [
                        "Authorization": authorization,
                        "Accept": accept,
                        "Lang": lang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)

        let requestBuilder: RequestBuilder<OrderDetailResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()

        return requestBuilder.init(method: "GET", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    
    open class func getMarketOrderWithRequestBuilder(authorization: String, accept: String, lang: String, iOrderId: Int) -> RequestBuilder<OrderDetailResponse> {
        var path = "/order/get-order-details/{iOrderId}"
        let iOrderIdPreEscape = "\(iOrderId)"
        let iOrderIdPostEscape = iOrderIdPreEscape.addingPercentEncoding(withAllowedCharacters: .urlPathAllowed) ?? ""
        path = path.replacingOccurrences(of: "{iOrderId}", with: iOrderIdPostEscape, options: .literal, range: nil)
        let URLString = SwaggerClientAPI.baseMarketPath + path
        let parameters: [String:Any]? = nil
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any?] = [
                        "Authorization": authorization,
                        "Accept": accept,
                        "Lang": lang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)

        let requestBuilder: RequestBuilder<OrderDetailResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()

        return requestBuilder.init(method: "GET", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    /**
     * enum for parameter iOrderType
     */
    public enum IOrderType_getOrderList: Int { 
        case _1 = 1
        case _2 = 2
        case _3 = 3
    }

    /**
     Get Order Listing

     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter iOrderType: (form)  (optional)
     - parameter offset: (query) offset (optional, default to 1)
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func getOrderList(authorization: String, accept: String, lang: String, iOrderType: IOrderType_getOrderList? = nil, offset: Int? = nil, completion: @escaping ((_ data: OrderListResponse?,_ error: Error?) -> Void)) {
        getOrderListWithRequestBuilder(authorization: authorization, accept: accept, lang: lang, iOrderType: iOrderType, offset: offset).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }
    
    open class func getMarketOrderList(authorization: String, accept: String, lang: String, iOrderType: IOrderType_getOrderList? = nil, offset: Int? = nil, completion: @escaping ((_ data: OrderListResponse?,_ error: Error?) -> Void)) {
        getMarketOrderListWithRequestBuilder(authorization: authorization, accept: accept, lang: lang, iOrderType: iOrderType, offset: offset).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }


    /**
     Get Order Listing
     - POST /order/get-order-list
     - 

     - :
       - type: http
       - name: bearerAuth
     - examples: [{contentType=application/json, example={
  "responseData" : {
    "totalPage" : 5,
    "limit" : 6,
    "orders" : [ {
      "iOrderId" : 5,
      "tsOrderedAt" : "tsOrderedAt",
      "tiTransactionType" : 9,
      "tiOrderStatus" : 2,
      "tiIsRated" : 3,
      "tsExpectedStartTime" : "tsExpectedStartTime",
      "dbRating" : "dbRating",
      "tiPaymentStatus" : 7,
      "tsExpectedEndTime" : "tsExpectedEndTime",
      "tComment" : "tComment",
      "vOrderNumber" : "vOrderNumber"
    }, {
      "iOrderId" : 5,
      "tsOrderedAt" : "tsOrderedAt",
      "tiTransactionType" : 9,
      "tiOrderStatus" : 2,
      "tiIsRated" : 3,
      "tsExpectedStartTime" : "tsExpectedStartTime",
      "dbRating" : "dbRating",
      "tiPaymentStatus" : 7,
      "tsExpectedEndTime" : "tsExpectedEndTime",
      "tComment" : "tComment",
      "vOrderNumber" : "vOrderNumber"
    } ],
    "totalRecord" : 1
  },
  "responseMessage" : "responseMessage",
  "responseCode" : 0
}}]
     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter iOrderType: (form)  (optional)
     - parameter offset: (query) offset (optional, default to 1)

     - returns: RequestBuilder<OrderListResponse> 
     */
    open class func getOrderListWithRequestBuilder(authorization: String, accept: String, lang: String, iOrderType: IOrderType_getOrderList? = nil, offset: Int? = nil) -> RequestBuilder<OrderListResponse> {
        let path = "/order/get-order-list"
        let URLString = SwaggerClientAPI.basePath + path
        let formParams: [String:Any?] = [
                "iOrderType": iOrderType?.rawValue
        ]

        let nonNullParameters = APIHelper.rejectNil(formParams)
        let parameters = APIHelper.convertBoolToString(nonNullParameters)
        var url = URLComponents(string: URLString)
        url?.queryItems = APIHelper.mapValuesToQueryItems([
                        "offset": offset?.encodeToJSON()
        ])
        let nillableHeaders: [String: Any?] = [
                        "Authorization": authorization,
                        "Accept": accept,
                        "Lang": lang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)

        let requestBuilder: RequestBuilder<OrderListResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()

        return requestBuilder.init(method: "POST", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    
    open class func getMarketOrderListWithRequestBuilder(authorization: String, accept: String, lang: String, iOrderType: IOrderType_getOrderList? = nil, offset: Int? = nil) -> RequestBuilder<OrderListResponse> {
        let path = "/order/get-order-list"
        let URLString = SwaggerClientAPI.baseMarketPath + path
        let formParams: [String:Any?] = [
                "iOrderType": iOrderType?.rawValue
        ]

        let nonNullParameters = APIHelper.rejectNil(formParams)
        let parameters = APIHelper.convertBoolToString(nonNullParameters)
        var url = URLComponents(string: URLString)
        url?.queryItems = APIHelper.mapValuesToQueryItems([
                        "offset": offset?.encodeToJSON()
        ])
        let nillableHeaders: [String: Any?] = [
                        "Authorization": authorization,
                        "Accept": accept,
                        "Lang": lang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)

        let requestBuilder: RequestBuilder<OrderListResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()

        return requestBuilder.init(method: "POST", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    /**
     Order Cancel Reason

     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func orderCancelReason(authorization: String, accept: String, lang: String, completion: @escaping ((_ data: CancelOrderReasonResponse?,_ error: Error?) -> Void)) {
        orderCancelReasonWithRequestBuilder(authorization: authorization, accept: accept, lang: lang).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }


    /**
     Order Cancel Reason
     - GET /order/order-cancel-reason
     - 

     - :
       - type: http
       - name: bearerAuth
     - examples: [{contentType=application/json, example={
  "responseData" : [ {
    "iOrderCancelReasonId" : 6,
    "cancelReason" : "cancelReason"
  }, {
    "iOrderCancelReasonId" : 6,
    "cancelReason" : "cancelReason"
  } ],
  "responseMessage" : "responseMessage",
  "responseCode" : 0
}}]
     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  

     - returns: RequestBuilder<CancelOrderReasonResponse> 
     */
    open class func orderCancelReasonWithRequestBuilder(authorization: String, accept: String, lang: String) -> RequestBuilder<CancelOrderReasonResponse> {
        let path = "/order/order-cancel-reason"
        let URLString = SwaggerClientAPI.basePath + path
        let parameters: [String:Any]? = nil
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any?] = [
                        "Authorization": authorization,
                        "Accept": accept,
                        "Lang": lang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)

        let requestBuilder: RequestBuilder<CancelOrderReasonResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()

        return requestBuilder.init(method: "GET", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    /**
     Order transaction status & details update

     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter iOrderId: (form)  (optional)
     - parameter tiTransactionType: (form)  (optional)
     - parameter vTransactionRef: (form)  (optional)
     - parameter iPaymentStatus: (form)  (optional)
     - parameter iUseWallet: (form)  (optional)
     - parameter vCardName: (form)  (optional)
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func orderTransactionUpdate(authorization: String, accept: String, lang: String, iOrderId: Int? = nil, tiTransactionType: Int? = nil, vTransactionRef: String? = nil, iPaymentStatus: Int? = nil, iUseWallet: Int? = nil, vCardName: String? = nil, completion: @escaping ((_ data: TransactionResponse?,_ error: Error?) -> Void)) {
        orderTransactionUpdateWithRequestBuilder(authorization: authorization, accept: accept, lang: lang, iOrderId: iOrderId, tiTransactionType: tiTransactionType, vTransactionRef: vTransactionRef, iPaymentStatus: iPaymentStatus, iUseWallet: iUseWallet, vCardName: vCardName).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }


    /**
     Order transaction status & details update
     - POST /order/order-transaction-update
     - 

     - :
       - type: http
       - name: bearerAuth
     - examples: [{contentType=application/json, example={
  "responseData" : {
    "latestWalletBalance" : "latestWalletBalance"
  },
  "responseMessage" : "responseMessage",
  "responseCode" : 0
}}]
     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter iOrderId: (form)  (optional)
     - parameter tiTransactionType: (form)  (optional)
     - parameter vTransactionRef: (form)  (optional)
     - parameter iPaymentStatus: (form)  (optional)
     - parameter iUseWallet: (form)  (optional)
     - parameter vCardName: (form)  (optional)

     - returns: RequestBuilder<TransactionResponse> 
     */
    open class func orderTransactionUpdateWithRequestBuilder(authorization: String, accept: String, lang: String, iOrderId: Int? = nil, tiTransactionType: Int? = nil, vTransactionRef: String? = nil, iPaymentStatus: Int? = nil, iUseWallet: Int? = nil, vCardName: String? = nil) -> RequestBuilder<TransactionResponse> {
        let path = "/order/order-transaction-update"
        let URLString = SwaggerClientAPI.basePath + path
        let formParams: [String:Any?] = [
                "iOrderId": iOrderId?.encodeToJSON(),
                "tiTransactionType": tiTransactionType?.encodeToJSON(),
                "vTransactionRef": vTransactionRef,
                "iPaymentStatus": iPaymentStatus?.encodeToJSON(),
                "iUseWallet": iUseWallet?.encodeToJSON(),
                "vCardName": vCardName
        ]

        let nonNullParameters = APIHelper.rejectNil(formParams)
        let parameters = APIHelper.convertBoolToString(nonNullParameters)
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any?] = [
                        "Authorization": authorization,
                        "Accept": accept,
                        "Lang": lang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)

        let requestBuilder: RequestBuilder<TransactionResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()
        
        print(url)
        print(formParams)
        print(headerParameters)

        return requestBuilder.init(method: "POST", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    /**
     Re-order from your history order

     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter iOrderId: (path) iOrderId 
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func reOrder(authorization: String, accept: String, lang: String, iOrderId: Int, completion: @escaping ((_ data: CreateOrderResponse?,_ error: Error?) -> Void)) {
        reOrderWithRequestBuilder(authorization: authorization, accept: accept, lang: lang, iOrderId: iOrderId).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }


    /**
     Re-order from your history order
     - GET /order/re-order/{iOrderId}
     - 

     - :
       - type: http
       - name: bearerAuth
     - examples: [{contentType=application/json, example={
  "responseData" : {
    "iOrderId" : 6,
    "tsOrderedAt" : "tsOrderedAt",
    "tiOrderStatus" : 1,
    "dOrderTotal" : "dOrderTotal",
    "vOrderNumber" : "vOrderNumber"
  },
  "responseMessage" : "responseMessage",
  "responseCode" : 0
}}]
     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter iOrderId: (path) iOrderId 

     - returns: RequestBuilder<CreateOrderResponse> 
     */
    open class func reOrderWithRequestBuilder(authorization: String, accept: String, lang: String, iOrderId: Int) -> RequestBuilder<CreateOrderResponse> {
        var path = "/order/re-order/{iOrderId}"
        let iOrderIdPreEscape = "\(iOrderId)"
        let iOrderIdPostEscape = iOrderIdPreEscape.addingPercentEncoding(withAllowedCharacters: .urlPathAllowed) ?? ""
        path = path.replacingOccurrences(of: "{iOrderId}", with: iOrderIdPostEscape, options: .literal, range: nil)
        let URLString = SwaggerClientAPI.basePath + path
        let parameters: [String:Any]? = nil
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any?] = [
                        "Authorization": authorization,
                        "Accept": accept,
                        "Lang": lang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)

        let requestBuilder: RequestBuilder<CreateOrderResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()

        return requestBuilder.init(method: "GET", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    /**
     Update Alternate mobile number

     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter iOrderId: (form)  (optional)
     - parameter vAlternativeISDCode: (form)  (optional)
     - parameter vAlternativeMobileNumber: (form)  (optional)
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func updateAlternateNumber(authorization: String, accept: String, lang: String, iOrderId: Int? = nil, vAlternativeISDCode: String? = nil, vAlternativeMobileNumber: String? = nil, completion: @escaping ((_ data: CommonFields?,_ error: Error?) -> Void)) {
        updateAlternateNumberWithRequestBuilder(authorization: authorization, accept: accept, lang: lang, iOrderId: iOrderId, vAlternativeISDCode: vAlternativeISDCode, vAlternativeMobileNumber: vAlternativeMobileNumber).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }


    /**
     Update Alternate mobile number
     - POST /order/update-contact-number
     - 

     - :
       - type: http
       - name: bearerAuth
     - examples: [{contentType=application/json, example={
  "responseMessage" : "responseMessage",
  "responseCode" : 0
}}]
     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter iOrderId: (form)  (optional)
     - parameter vAlternativeISDCode: (form)  (optional)
     - parameter vAlternativeMobileNumber: (form)  (optional)

     - returns: RequestBuilder<CommonFields> 
     */
    open class func updateAlternateNumberWithRequestBuilder(authorization: String, accept: String, lang: String, iOrderId: Int? = nil, vAlternativeISDCode: String? = nil, vAlternativeMobileNumber: String? = nil) -> RequestBuilder<CommonFields> {
        let path = "/order/update-contact-number"
        let URLString = SwaggerClientAPI.basePath + path
        let formParams: [String:Any?] = [
                "iOrderId": iOrderId?.encodeToJSON(),
                "vAlternativeISDCode": vAlternativeISDCode,
                "vAlternativeMobileNumber": vAlternativeMobileNumber
        ]

        let nonNullParameters = APIHelper.rejectNil(formParams)
        let parameters = APIHelper.convertBoolToString(nonNullParameters)
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any?] = [
                        "Authorization": authorization,
                        "Accept": accept,
                        "Lang": lang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)

        let requestBuilder: RequestBuilder<CommonFields>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()

        return requestBuilder.init(method: "POST", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    
    open class func changePaymentAfterPlaceOrder(iOrderId: Int, tiTransactionType: Int, vTransactionRef: String, iPaymentStatus: Int, iUseWallet: Int?,
                                                 vCardName: String?, completion: @escaping ((_ data: TransactionResponse?,_ error: Error?) -> Void)) {
        let authorization = getAuthorizationText()
        changePaymentAfterPlaceOrderWithRequestBuilder(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, iOrderId: iOrderId, tiTransactionType: tiTransactionType, vTransactionRef: vTransactionRef, iPaymentStatus: iPaymentStatus, iUseWallet: iUseWallet, vCardName: vCardName).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }
    
    // Change Payment After PlaceOrder
    open class func changePaymentAfterPlaceOrderWithRequestBuilder(authorization: String, accept: String, lang: String, iOrderId: Int? = nil, tiTransactionType: Int? = nil, vTransactionRef: String? = nil, iPaymentStatus: Int? = nil, iUseWallet: Int? = nil, vCardName: String? = nil) -> RequestBuilder<TransactionResponse> {
        let path = "/order/change-payment-after-placeorder"
        let URLString = SwaggerClientAPI.basePath + path
        let formParams: [String:Any?] = [
                "iOrderId": iOrderId?.encodeToJSON(),
                "tiTransactionType": tiTransactionType?.encodeToJSON(),
                "vTransactionRef": vTransactionRef,
                "iPaymentStatus": iPaymentStatus?.encodeToJSON(),
                "iUseWallet": iUseWallet?.encodeToJSON(),
                "vCardName": vCardName
        ]

        let nonNullParameters = APIHelper.rejectNil(formParams)
        let parameters = APIHelper.convertBoolToString(nonNullParameters)
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any?] = [
                        "Authorization": authorization,
                        "Accept": accept,
                        "Lang": lang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)

        let requestBuilder: RequestBuilder<TransactionResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()

        return requestBuilder.init(method: "POST", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    
}
