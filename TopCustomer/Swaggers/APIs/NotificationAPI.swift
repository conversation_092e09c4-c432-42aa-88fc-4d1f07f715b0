//
// NotificationAPI.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation
import Alamofire


open class NotificationAPI {
    /**
     Badge notification count update

     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func badgeReset(authorization: String, accept: String, lang: String, completion: @escaping ((_ data: NotificationBadgeResponse?,_ error: Error?) -> Void)) {
        badgeResetWithRequestBuilder(authorization: authorization, accept: accept, lang: lang).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }


    /**
     Badge notification count update
     - GET /notification/badge-reset
     - 

     - :
       - type: http
       - name: bearerAuth
     - examples: [{contentType=application/json, example={
  "responseMessage" : "responseMessage",
  "responseCode" : 0
}}]
     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  

     - returns: RequestBuilder<NotificationBadgeResponse> 
     */
    open class func badgeResetWithRequestBuilder(authorization: String, accept: String, lang: String) -> RequestBuilder<NotificationBadgeResponse> {
        let path = "/notification/badge-reset"
        let URLString = SwaggerClientAPI.basePath + path
        let parameters: [String:Any]? = nil
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any?] = [
                        "Authorization": authorization,
                        "Accept": accept,
                        "Lang": lang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)

        let requestBuilder: RequestBuilder<NotificationBadgeResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()

        return requestBuilder.init(method: "GET", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    /**
     User Notification List

     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter offset: (query) offset (optional, default to 1)
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func notificationListing(authorization: String, accept: String, lang: String, offset: Int? = nil, completion: @escaping ((_ data: NotificationResponse?,_ error: Error?) -> Void)) {
        notificationListingWithRequestBuilder(authorization: authorization, accept: accept, lang: lang, offset: offset).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }


    /**
     User Notification List
     - GET /notification/notification-listing
     - 

     - :
       - type: http
       - name: bearerAuth
     - examples: [{contentType=application/json, example={
  "responseData" : {
    "totalPage" : 5,
    "limit" : 6,
    "totalRecord" : 1,
    "notifications" : [ {
      "tiNotificationType" : 2,
      "tiIsRead" : 7,
      "txBody" : "txBody",
      "iNotificationId" : 5,
      "vTitle" : "vTitle"
    }, {
      "tiNotificationType" : 2,
      "tiIsRead" : 7,
      "txBody" : "txBody",
      "iNotificationId" : 5,
      "vTitle" : "vTitle"
    } ]
  },
  "responseMessage" : "responseMessage",
  "responseCode" : 0
}}]
     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter offset: (query) offset (optional, default to 1)

     - returns: RequestBuilder<NotificationResponse> 
     */
    open class func notificationListingWithRequestBuilder(authorization: String, accept: String, lang: String, offset: Int? = nil) -> RequestBuilder<NotificationResponse> {
        let path = "/notification/notification-listing"
        let URLString = SwaggerClientAPI.basePath + path
        let parameters: [String:Any]? = nil
        var url = URLComponents(string: URLString)
        url?.queryItems = APIHelper.mapValuesToQueryItems([
                        "offset": offset?.encodeToJSON()
        ])
        let nillableHeaders: [String: Any?] = [
                        "Authorization": authorization,
                        "Accept": accept,
                        "Lang": lang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)

        let requestBuilder: RequestBuilder<NotificationResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()

        return requestBuilder.init(method: "GET", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
}
