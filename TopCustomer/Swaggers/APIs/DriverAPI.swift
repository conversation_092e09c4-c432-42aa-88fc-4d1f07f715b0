//
// DriverAPI.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation
import Alamofire


open class DriverAPI {
    /**
     Completed order details

     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter iOrderId: (path) iOrderId 
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func completedOrderDetails(authorization: String, accept: String, lang: String, iOrderId: Int, completion: @escaping ((_ data: DriverCompletedOrderDeatailsResponse?,_ error: Error?) -> Void)) {
        completedOrderDetailsWithRequestBuilder(authorization: authorization, accept: accept, lang: lang, iOrderId: iOrderId).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }


    /**
     Completed order details
     - GET /driver/completed-order-details/{iOrderId}
     - 

     - :
       - type: http
       - name: bearerAuth
     - examples: [{contentType=application/json, example={
  "responseData" : {
    "vAlternativeISDCode" : "vAlternativeISDCode",
    "vName" : "vName",
    "vISDCode" : "vISDCode",
    "tiOrderStatus" : 6,
    "vOrderDeliveredAt" : "vOrderDeliveredAt",
    "vOrderInProcessAt" : "vOrderInProcessAt",
    "vAlternativeMobileNumber" : "vAlternativeMobileNumber",
    "vOrderAssignedAt" : "vOrderAssignedAt",
    "tsOrderDeliveredAt" : "tsOrderDeliveredAt",
    "vOrderAcceptedAt" : "vOrderAcceptedAt",
    "vOrderCompletedAt" : "vOrderCompletedAt",
    "vOrderNumber" : "vOrderNumber",
    "vMobileNumber" : "vMobileNumber"
  },
  "responseMessage" : "responseMessage",
  "responseCode" : 0
}}]
     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter iOrderId: (path) iOrderId 

     - returns: RequestBuilder<DriverCompletedOrderDeatailsResponse> 
     */
    open class func completedOrderDetailsWithRequestBuilder(authorization: String, accept: String, lang: String, iOrderId: Int) -> RequestBuilder<DriverCompletedOrderDeatailsResponse> {
        var path = "/driver/completed-order-details/{iOrderId}"
        let iOrderIdPreEscape = "\(iOrderId)"
        let iOrderIdPostEscape = iOrderIdPreEscape.addingPercentEncoding(withAllowedCharacters: .urlPathAllowed) ?? ""
        path = path.replacingOccurrences(of: "{iOrderId}", with: iOrderIdPostEscape, options: .literal, range: nil)
        let URLString = SwaggerClientAPI.basePath + path
        let parameters: [String:Any]? = nil
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any?] = [
                        "Authorization": authorization,
                        "Accept": accept,
                        "Lang": lang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)

        let requestBuilder: RequestBuilder<DriverCompletedOrderDeatailsResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()

        return requestBuilder.init(method: "GET", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    /**
     Completed Order List

     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter offset: (query) offset (optional, default to 0)
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func completedOrders(authorization: String, accept: String, lang: String, offset: Int? = nil, completion: @escaping ((_ data: DriverHomeListingResponse?,_ error: Error?) -> Void)) {
        completedOrdersWithRequestBuilder(authorization: authorization, accept: accept, lang: lang, offset: offset).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }


    /**
     Completed Order List
     - GET /driver/completed-orders
     - 

     - :
       - type: http
       - name: bearerAuth
     - examples: [{contentType=application/json, example={
  "responseData" : {
    "vCarPlate" : "vCarPlate",
    "vName" : "vName",
    "vShiftTime" : "vShiftTime",
    "vCarName" : "vCarName",
    "totalPage" : 5,
    "limit" : 6,
    "orders" : [ {
      "iOrderId" : 1,
      "tsOrderedAt" : "tsOrderedAt",
      "vAlternativeISDCode" : "vAlternativeISDCode",
      "vName" : "vName",
      "dTotalAmount" : "dTotalAmount",
      "tiOrderStatus" : 5,
      "dMoneyAfterDeductionFromWallet" : 2,
      "vAlternativeMobileNumber" : "vAlternativeMobileNumber",
      "tiPaymentStatus" : 5,
      "iNextOrderIdForDriver" : 6,
      "vOrderNumber" : "vOrderNumber"
    }, {
      "iOrderId" : 1,
      "tsOrderedAt" : "tsOrderedAt",
      "vAlternativeISDCode" : "vAlternativeISDCode",
      "vName" : "vName",
      "dTotalAmount" : "dTotalAmount",
      "tiOrderStatus" : 5,
      "dMoneyAfterDeductionFromWallet" : 2,
      "vAlternativeMobileNumber" : "vAlternativeMobileNumber",
      "tiPaymentStatus" : 5,
      "iNextOrderIdForDriver" : 6,
      "vOrderNumber" : "vOrderNumber"
    } ],
    "totalRecord" : 1
  },
  "responseMessage" : "responseMessage",
  "responseCode" : 0
}}]
     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter offset: (query) offset (optional, default to 0)

     - returns: RequestBuilder<DriverHomeListingResponse> 
     */
    open class func completedOrdersWithRequestBuilder(authorization: String, accept: String, lang: String, offset: Int? = nil) -> RequestBuilder<DriverHomeListingResponse> {
        let path = "/driver/completed-orders"
        let URLString = SwaggerClientAPI.basePath + path
        let parameters: [String:Any]? = nil
        var url = URLComponents(string: URLString)
        url?.queryItems = APIHelper.mapValuesToQueryItems([
                        "offset": offset?.encodeToJSON()
        ])
        let nillableHeaders: [String: Any?] = [
                        "Authorization": authorization,
                        "Accept": accept,
                        "Lang": lang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)

        let requestBuilder: RequestBuilder<DriverHomeListingResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()

        return requestBuilder.init(method: "GET", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    /**
     Current order details

     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter iOrderId: (path) iOrderId 
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func currentOrderDetails(authorization: String, accept: String, lang: String, iOrderId: Int, completion: @escaping ((_ data: DriverOrderDeatailsResponse?,_ error: Error?) -> Void)) {
        currentOrderDetailsWithRequestBuilder(authorization: authorization, accept: accept, lang: lang, iOrderId: iOrderId).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }


    /**
     Current order details
     - GET /driver/current-order-details/{iOrderId}
     - 

     - :
       - type: http
       - name: bearerAuth
     - examples: [{contentType=application/json, example={
  "responseData" : {
    "iOrderId" : 6,
    "tsOrderedAt" : "tsOrderedAt",
    "dDriverLatitude" : "dDriverLatitude",
    "vAlternativeISDCode" : "vAlternativeISDCode",
    "vName" : "vName",
    "vISDCode" : "vISDCode",
    "tiTransactionType" : 1,
    "vAlternativeMobileNumber" : "vAlternativeMobileNumber",
    "tiPaymentStatus" : 5,
    "dOrderLatitude" : "dOrderLatitude",
    "productDetails" : [ {
      "vProductName" : "vProductName",
      "txProductDescription" : "txProductDescription",
      "vCategoryName" : "vCategoryName",
      "vProductSKU" : "vProductSKU",
      "dbPrice" : "dbPrice",
      "vProductImage" : "vProductImage",
      "vProductUnit" : "vProductUnit",
      "biProductId" : 5,
      "iProductQuantity" : 2
    }, {
      "vProductName" : "vProductName",
      "txProductDescription" : "txProductDescription",
      "vCategoryName" : "vCategoryName",
      "vProductSKU" : "vProductSKU",
      "dbPrice" : "dbPrice",
      "vProductImage" : "vProductImage",
      "vProductUnit" : "vProductUnit",
      "biProductId" : 5,
      "iProductQuantity" : 2
    } ],
    "dDistanceCost" : "dDistanceCost",
    "tAdditionalNote" : "tAdditionalNote",
    "dOrderLongitude" : "dOrderLongitude",
    "dDriverLongitude" : "dDriverLongitude",
    "dVatCharge" : "dVatCharge",
    "dOrderTotal" : "dOrderTotal",
    "vOrderNumber" : "vOrderNumber",
    "vMobileNumber" : "vMobileNumber"
  },
  "responseMessage" : "responseMessage",
  "responseCode" : 0
}}]
     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter iOrderId: (path) iOrderId 

     - returns: RequestBuilder<DriverOrderDeatailsResponse> 
     */
    open class func currentOrderDetailsWithRequestBuilder(authorization: String, accept: String, lang: String, iOrderId: Int) -> RequestBuilder<DriverOrderDeatailsResponse> {
        var path = "/driver/current-order-details/{iOrderId}"
        let iOrderIdPreEscape = "\(iOrderId)"
        let iOrderIdPostEscape = iOrderIdPreEscape.addingPercentEncoding(withAllowedCharacters: .urlPathAllowed) ?? ""
        path = path.replacingOccurrences(of: "{iOrderId}", with: iOrderIdPostEscape, options: .literal, range: nil)
        let URLString = SwaggerClientAPI.basePath + path
        let parameters: [String:Any]? = nil
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any?] = [
                        "Authorization": authorization,
                        "Accept": accept,
                        "Lang": lang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)

        let requestBuilder: RequestBuilder<DriverOrderDeatailsResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()

        return requestBuilder.init(method: "GET", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    /**
     Driver Home Page with Current Order List(order status like this 2-New,3-In Progress,4-Ready,5-Delivered)

     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func driverHomeListing(authorization: String, accept: String, lang: String, completion: @escaping ((_ data: DriverCurrentOrderListingResponse?,_ error: Error?) -> Void)) {
        driverHomeListingWithRequestBuilder(authorization: authorization, accept: accept, lang: lang).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }


    /**
     Driver Home Page with Current Order List(order status like this 2-New,3-In Progress,4-Ready,5-Delivered)
     - GET /driver/home-listing
     - 

     - :
       - type: http
       - name: bearerAuth
     - examples: [{contentType=application/json, example={
  "responseData" : {
    "vCarPlate" : "vCarPlate",
    "vName" : "vName",
    "vShiftTime" : "vShiftTime",
    "vCarName" : "vCarName",
    "orders" : [ {
      "iOrderId" : 1,
      "tsOrderedAt" : "tsOrderedAt",
      "vAlternativeISDCode" : "vAlternativeISDCode",
      "vName" : "vName",
      "dTotalAmount" : "dTotalAmount",
      "tiOrderStatus" : 5,
      "dMoneyAfterDeductionFromWallet" : 2,
      "vAlternativeMobileNumber" : "vAlternativeMobileNumber",
      "tiPaymentStatus" : 5,
      "iNextOrderIdForDriver" : 6,
      "vOrderNumber" : "vOrderNumber"
    }, {
      "iOrderId" : 1,
      "tsOrderedAt" : "tsOrderedAt",
      "vAlternativeISDCode" : "vAlternativeISDCode",
      "vName" : "vName",
      "dTotalAmount" : "dTotalAmount",
      "tiOrderStatus" : 5,
      "dMoneyAfterDeductionFromWallet" : 2,
      "vAlternativeMobileNumber" : "vAlternativeMobileNumber",
      "tiPaymentStatus" : 5,
      "iNextOrderIdForDriver" : 6,
      "vOrderNumber" : "vOrderNumber"
    } ]
  },
  "responseMessage" : "responseMessage",
  "responseCode" : 0
}}]
     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  

     - returns: RequestBuilder<DriverCurrentOrderListingResponse> 
     */
    open class func driverHomeListingWithRequestBuilder(authorization: String, accept: String, lang: String) -> RequestBuilder<DriverCurrentOrderListingResponse> {
        let path = "/driver/home-listing"
        let URLString = SwaggerClientAPI.basePath + path
        let parameters: [String:Any]? = nil
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any?] = [
                        "Authorization": authorization,
                        "Accept": accept,
                        "Lang": lang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)

        let requestBuilder: RequestBuilder<DriverCurrentOrderListingResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()

        return requestBuilder.init(method: "GET", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    /**
     get product counts which to be delivered

     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func driverProductCount(authorization: String, accept: String, lang: String, completion: @escaping ((_ data: DriverProductCountResponse?,_ error: Error?) -> Void)) {
        driverProductCountWithRequestBuilder(authorization: authorization, accept: accept, lang: lang).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }


    /**
     get product counts which to be delivered
     - GET /driver/product-count
     - 

     - :
       - type: http
       - name: bearerAuth
     - examples: [{contentType=application/json, example={
  "responseData" : {
    "productCount" : [ {
      "iOrderId" : 1,
      "vProductName" : "vProductName",
      "totalProduct" : "totalProduct",
      "vProductUnit" : "vProductUnit",
      "biProductId" : 6
    }, {
      "iOrderId" : 1,
      "vProductName" : "vProductName",
      "totalProduct" : "totalProduct",
      "vProductUnit" : "vProductUnit",
      "biProductId" : 6
    } ]
  },
  "responseMessage" : "responseMessage",
  "responseCode" : 0
}}]
     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  

     - returns: RequestBuilder<DriverProductCountResponse> 
     */
    open class func driverProductCountWithRequestBuilder(authorization: String, accept: String, lang: String) -> RequestBuilder<DriverProductCountResponse> {
        let path = "/driver/product-count"
        let URLString = SwaggerClientAPI.basePath + path
        let parameters: [String:Any]? = nil
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any?] = [
                        "Authorization": authorization,
                        "Accept": accept,
                        "Lang": lang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)

        let requestBuilder: RequestBuilder<DriverProductCountResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()

        return requestBuilder.init(method: "GET", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    /**
     Driver tip from the user for the specific order

     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter iOrderId: (form)  (optional)
     - parameter dTip: (form)  (optional)
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func driverTip(authorization: String, accept: String, lang: String, iOrderId: Int? = nil, dTip: String? = nil, completion: @escaping ((_ data: CommonFields?,_ error: Error?) -> Void)) {
        driverTipWithRequestBuilder(authorization: authorization, accept: accept, lang: lang, iOrderId: iOrderId, dTip: dTip).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }


    /**
     Driver tip from the user for the specific order
     - POST /user/driver-tip
     - 

     - :
       - type: http
       - name: bearerAuth
     - examples: [{contentType=application/json, example={
  "responseMessage" : "responseMessage",
  "responseCode" : 0
}}]
     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter iOrderId: (form)  (optional)
     - parameter dTip: (form)  (optional)

     - returns: RequestBuilder<CommonFields> 
     */
    open class func driverTipWithRequestBuilder(authorization: String, accept: String, lang: String, iOrderId: Int? = nil, dTip: String? = nil) -> RequestBuilder<CommonFields> {
        let path = "/user/driver-tip"
        let URLString = SwaggerClientAPI.basePath + path
        let formParams: [String:Any?] = [
                "iOrderId": iOrderId?.encodeToJSON(),
                "dTip": dTip
        ]

        let nonNullParameters = APIHelper.rejectNil(formParams)
        let parameters = APIHelper.convertBoolToString(nonNullParameters)
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any?] = [
                        "Authorization": authorization,
                        "Accept": accept,
                        "Lang": lang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)

        let requestBuilder: RequestBuilder<CommonFields>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()

        return requestBuilder.init(method: "POST", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    /**
     Order cancelled by driver

     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter iOrderId: (form)  (optional)
     - parameter iShiftId: (form)  (optional)
     - parameter tCancelledReason: (form)  (optional)
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func orderCancel(authorization: String, accept: String, lang: String, iOrderId: Int? = nil, iShiftId: Int? = nil, tCancelledReason: String? = nil, completion: @escaping ((_ data: DriverOrderStatusResponse?,_ error: Error?) -> Void)) {
        orderCancelWithRequestBuilder(authorization: authorization, accept: accept, lang: lang, iOrderId: iOrderId, iShiftId: iShiftId, tCancelledReason: tCancelledReason).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }


    /**
     Order cancelled by driver
     - POST /driver/order-cancel
     - 

     - :
       - type: http
       - name: bearerAuth
     - examples: [{contentType=application/json, example={
  "responseData" : {
    "iOrderId" : 1,
    "tsOrderedAt" : "tsOrderedAt",
    "vAlternativeISDCode" : "vAlternativeISDCode",
    "vName" : "vName",
    "dTotalAmount" : "dTotalAmount",
    "tiOrderStatus" : 5,
    "dMoneyAfterDeductionFromWallet" : 2,
    "vAlternativeMobileNumber" : "vAlternativeMobileNumber",
    "tiPaymentStatus" : 5,
    "iNextOrderIdForDriver" : 6,
    "vOrderNumber" : "vOrderNumber"
  },
  "responseMessage" : "responseMessage",
  "responseCode" : 0
}}]
     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter iOrderId: (form)  (optional)
     - parameter iShiftId: (form)  (optional)
     - parameter tCancelledReason: (form)  (optional)

     - returns: RequestBuilder<DriverOrderStatusResponse> 
     */
    open class func orderCancelWithRequestBuilder(authorization: String, accept: String, lang: String, iOrderId: Int? = nil, iShiftId: Int? = nil, tCancelledReason: String? = nil) -> RequestBuilder<DriverOrderStatusResponse> {
        let path = "/driver/order-cancel"
        let URLString = SwaggerClientAPI.basePath + path
        let formParams: [String:Any?] = [
                "iOrderId": iOrderId?.encodeToJSON(),
                "iShiftId": iShiftId?.encodeToJSON(),
                "tCancelledReason": tCancelledReason
        ]

        let nonNullParameters = APIHelper.rejectNil(formParams)
        let parameters = APIHelper.convertBoolToString(nonNullParameters)
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any?] = [
                        "Authorization": authorization,
                        "Accept": accept,
                        "Lang": lang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)

        let requestBuilder: RequestBuilder<DriverOrderStatusResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()

        return requestBuilder.init(method: "POST", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    /**
     Order status update by driver

     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter iOrderId: (form)  (optional)
     - parameter tiOrderStatus: (form)  (optional)
     - parameter vEstimationTimeInSeconds: (form)  (optional)
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func orderStatusUpdate(authorization: String, accept: String, lang: String, iOrderId: Int? = nil, tiOrderStatus: Int? = nil, vEstimationTimeInSeconds: Int? = nil, completion: @escaping ((_ data: DriverOrderStatusResponse?,_ error: Error?) -> Void)) {
        orderStatusUpdateWithRequestBuilder(authorization: authorization, accept: accept, lang: lang, iOrderId: iOrderId, tiOrderStatus: tiOrderStatus, vEstimationTimeInSeconds: vEstimationTimeInSeconds).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }


    /**
     Order status update by driver
     - POST /driver/order-status-update
     - 

     - :
       - type: http
       - name: bearerAuth
     - examples: [{contentType=application/json, example={
  "responseData" : {
    "iOrderId" : 1,
    "tsOrderedAt" : "tsOrderedAt",
    "vAlternativeISDCode" : "vAlternativeISDCode",
    "vName" : "vName",
    "dTotalAmount" : "dTotalAmount",
    "tiOrderStatus" : 5,
    "dMoneyAfterDeductionFromWallet" : 2,
    "vAlternativeMobileNumber" : "vAlternativeMobileNumber",
    "tiPaymentStatus" : 5,
    "iNextOrderIdForDriver" : 6,
    "vOrderNumber" : "vOrderNumber"
  },
  "responseMessage" : "responseMessage",
  "responseCode" : 0
}}]
     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter iOrderId: (form)  (optional)
     - parameter tiOrderStatus: (form)  (optional)
     - parameter vEstimationTimeInSeconds: (form)  (optional)

     - returns: RequestBuilder<DriverOrderStatusResponse> 
     */
    open class func orderStatusUpdateWithRequestBuilder(authorization: String, accept: String, lang: String, iOrderId: Int? = nil, tiOrderStatus: Int? = nil, vEstimationTimeInSeconds: Int? = nil) -> RequestBuilder<DriverOrderStatusResponse> {
        let path = "/driver/order-status-update"
        let URLString = SwaggerClientAPI.basePath + path
        let formParams: [String:Any?] = [
                "iOrderId": iOrderId?.encodeToJSON(),
                "tiOrderStatus": tiOrderStatus?.encodeToJSON(),
                "vEstimationTimeInSeconds": vEstimationTimeInSeconds?.encodeToJSON()
        ]

        let nonNullParameters = APIHelper.rejectNil(formParams)
        let parameters = APIHelper.convertBoolToString(nonNullParameters)
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any?] = [
                        "Authorization": authorization,
                        "Accept": accept,
                        "Lang": lang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)

        let requestBuilder: RequestBuilder<DriverOrderStatusResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()

        return requestBuilder.init(method: "POST", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    /**
     get product counts which to be delivered

     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter iOrderId: (form)  (optional)
     - parameter tRescheduleReason: (form)  (optional)
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func rescheduleOrder(authorization: String, accept: String, lang: String, iOrderId: Int? = nil, tRescheduleReason: String? = nil, completion: @escaping ((_ data: DriverRescheduleOrderResponse?,_ error: Error?) -> Void)) {
        rescheduleOrderWithRequestBuilder(authorization: authorization, accept: accept, lang: lang, iOrderId: iOrderId, tRescheduleReason: tRescheduleReason).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }


    /**
     get product counts which to be delivered
     - POST /driver/reschedule-order
     - 

     - :
       - type: http
       - name: bearerAuth
     - examples: [{contentType=application/json, example={
  "responseData" : {
    "nextOrderId" : 6,
    "tRescheduleReason" : "tRescheduleReason"
  },
  "responseMessage" : "responseMessage",
  "responseCode" : 0
}}]
     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter iOrderId: (form)  (optional)
     - parameter tRescheduleReason: (form)  (optional)

     - returns: RequestBuilder<DriverRescheduleOrderResponse> 
     */
    open class func rescheduleOrderWithRequestBuilder(authorization: String, accept: String, lang: String, iOrderId: Int? = nil, tRescheduleReason: String? = nil) -> RequestBuilder<DriverRescheduleOrderResponse> {
        let path = "/driver/reschedule-order"
        let URLString = SwaggerClientAPI.basePath + path
        let formParams: [String:Any?] = [
                "iOrderId": iOrderId?.encodeToJSON(),
                "tRescheduleReason": tRescheduleReason
        ]

        let nonNullParameters = APIHelper.rejectNil(formParams)
        let parameters = APIHelper.convertBoolToString(nonNullParameters)
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any?] = [
                        "Authorization": authorization,
                        "Accept": accept,
                        "Lang": lang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)

        let requestBuilder: RequestBuilder<DriverRescheduleOrderResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()

        return requestBuilder.init(method: "POST", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    /**
     Shift Listing

     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func shiftListing(authorization: String, accept: String, lang: String, completion: @escaping ((_ data: ShiftResponse?,_ error: Error?) -> Void)) {
        shiftListingWithRequestBuilder(authorization: authorization, accept: accept, lang: lang).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }


    /**
     Shift Listing
     - GET /driver/shift-listing
     - 

     - :
       - type: http
       - name: bearerAuth
     - examples: [{contentType=application/json, example={
  "responseData" : [ {
    "vStartAt" : "vStartAt",
    "iShiftId" : 6,
    "vShiftDisplayName" : "vShiftDisplayName",
    "vCloseAt" : "vCloseAt"
  }, {
    "vStartAt" : "vStartAt",
    "iShiftId" : 6,
    "vShiftDisplayName" : "vShiftDisplayName",
    "vCloseAt" : "vCloseAt"
  } ],
  "responseMessage" : "responseMessage",
  "responseCode" : 0
}}]
     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  

     - returns: RequestBuilder<ShiftResponse> 
     */
    open class func shiftListingWithRequestBuilder(authorization: String, accept: String, lang: String) -> RequestBuilder<ShiftResponse> {
        let path = "/driver/shift-listing"
        let URLString = SwaggerClientAPI.basePath + path
        let parameters: [String:Any]? = nil
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any?] = [
                        "Authorization": authorization,
                        "Accept": accept,
                        "Lang": lang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)

        let requestBuilder: RequestBuilder<ShiftResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()

        return requestBuilder.init(method: "GET", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    /**
     Driver Weekly Schedule

     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func weeklySchedule(authorization: String, accept: String, lang: String, completion: @escaping ((_ data: DriverWeeklyScheduleResponse?,_ error: Error?) -> Void)) {
        weeklyScheduleWithRequestBuilder(authorization: authorization, accept: accept, lang: lang).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }


    /**
     Driver Weekly Schedule
     - GET /driver/weekly-schedule
     - 

     - :
       - type: http
       - name: bearerAuth
     - examples: [{contentType=application/json, example={
  "responseData" : {
    "iDriverId" : 6,
    "jWeeklySchedule" : [ {
      "iVehicleTypeId" : "iVehicleTypeId",
      "vRole" : "vRole",
      "tiIsOffDay" : 1,
      "vShiftStartsAt" : "vShiftStartsAt",
      "day" : "day"
    }, {
      "iVehicleTypeId" : "iVehicleTypeId",
      "vRole" : "vRole",
      "tiIsOffDay" : 1,
      "vShiftStartsAt" : "vShiftStartsAt",
      "day" : "day"
    } ]
  },
  "responseMessage" : "responseMessage",
  "responseCode" : 0
}}]
     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  

     - returns: RequestBuilder<DriverWeeklyScheduleResponse> 
     */
    open class func weeklyScheduleWithRequestBuilder(authorization: String, accept: String, lang: String) -> RequestBuilder<DriverWeeklyScheduleResponse> {
        let path = "/driver/weekly-schedule"
        let URLString = SwaggerClientAPI.basePath + path
        let parameters: [String:Any]? = nil
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any?] = [
                        "Authorization": authorization,
                        "Accept": accept,
                        "Lang": lang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)

        let requestBuilder: RequestBuilder<DriverWeeklyScheduleResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()

        return requestBuilder.init(method: "GET", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
}
