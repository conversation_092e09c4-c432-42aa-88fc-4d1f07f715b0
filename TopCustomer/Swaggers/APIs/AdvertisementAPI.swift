//
// AdvertisementAPI.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation
import Alamofire


open class AdvertisementAPI {
    /**
     Show Advertisement

     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func advertisementShow(authorization: String, accept: String, lang: String, completion: @escaping ((_ data: AdvertisementResponse?,_ error: Error?) -> Void)) {
        advertisementShowWithRequestBuilder(authorization: authorization, accept: accept, lang: lang).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }


    /**
     Show Advertisement
     - GET /advertisement/show
     - 

     - :
       - type: http
       - name: bearerAuth
     - examples: [{contentType=application/json, example={
  "responseData" : {
    "vImage" : "vImage",
    "vSocialLink" : "vSocialLink",
    "vAdTitle" : "vAdTitle",
    "iProductCategoryId" : 5,
    "tiIsActive" : 2,
    "vCategoryName" : "vCategoryName",
    "iOfferId" : 5,
    "vButtonText" : "vButtonText",
    "iAdvertisementId" : 6,
    "biProductId" : 1
  },
  "responseMessage" : "responseMessage",
  "responseCode" : 0
}}]
     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  

     - returns: RequestBuilder<AdvertisementResponse> 
     */
    open class func advertisementShowWithRequestBuilder(authorization: String, accept: String, lang: String) -> RequestBuilder<AdvertisementResponse> {
        let path = "/advertisement/all-ads"
        let URLString = SwaggerClientAPI.basePath + path
        let parameters: [String:Any]? = nil
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any?] = [
                        "Authorization": authorization,
                        "Accept": accept,
                        "Lang": lang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)

        let requestBuilder: RequestBuilder<AdvertisementResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()

        return requestBuilder.init(method: "GET", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
}
