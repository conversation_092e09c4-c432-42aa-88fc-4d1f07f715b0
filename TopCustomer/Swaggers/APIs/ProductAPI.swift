//
// ProductAPI.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation
import Alamofire


open class ProductAPI {
    /**
     To clear home screen cache
     
     - parameter accept: (header)
     - parameter authorization: (header)  (optional, default to Bear<PERSON> )
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func clearCache(accept: String, authorization: String? = nil, completion: @escaping ((_ data: CacheResponse?,_ error: Error?) -> Void)) {
        clearCacheWithRequestBuilder(accept: accept, authorization: authorization).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }
    
    
    /**
     To clear home screen cache
     - GET /product/clear-cache
     -
     
     - :
     - type: http
     - name: bearerAuth
     - examples: [{contentType=application/json, example={
     "responseMessage" : "responseMessage",
     "responseCode" : 0
     }}]
     - parameter accept: (header)
     - parameter authorization: (header)  (optional, default to Bearer )
     
     - returns: RequestBuilder<CacheResponse>
     */
    open class func clearCacheWithRequestBuilder(accept: String, authorization: String? = nil) -> RequestBuilder<CacheResponse> {
        let path = "/product/clear-cache"
        let URLString = SwaggerClientAPI.basePath + path
        let parameters: [String:Any]? = nil
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any?] = [
            "Authorization": authorization,
            "Accept": accept
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)
        
        let requestBuilder: RequestBuilder<CacheResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()
        
        return requestBuilder.init(method: "GET", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    /**
     Dynamic Home Listing
     
     - parameter authorization: (header)
     - parameter accept: (header)
     - parameter lang: (header)
     - parameter iCategoryId: (query) iCategoryId (optional, default to 1)
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func dynamicHomeListing(authorization: String, accept: String, lang: String, iCategoryId: Int? = nil, completion: @escaping ((_ data: LatestHomeListingResponse?,_ error: Error?) -> Void)) {
        dynamicHomeListingWithRequestBuilder(authorization: authorization, accept: accept, lang: lang, iCategoryId: iCategoryId).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }
    
    
    /**
     Dynamic Home Listing
     - GET /product/dynamic-home-listing
     -
     
     - :
     - type: http
     - name: bearerAuth
     - examples: [{contentType=application/json, example={
     "responseData" : {
     "newlyArrivedProducts" : [ null, null ],
     "bestSellerProducts" : [ null, null ],
     "categories" : [ {
     "vName" : "vName",
     "vImage" : "vImage",
     "iProductCategoryId" : 5,
     "vCategoryName" : "vCategoryName",
     "id" : 6,
     "iOfferId" : 5,
     "biProductId" : 1,
     "products" : [ {
     "tiIsFavourite" : 2,
     "vProductName" : "vProductName",
     "iProductCategoryId" : "iProductCategoryId",
     "dDiscountedProductPrice" : "dDiscountedProductPrice",
     "dbOriginalProductPrice" : "dbOriginalProductPrice",
     "vProductImage" : "vProductImage",
     "biProductId" : 2,
     "isLowQuantity" : 9,
     "dDiscountAmount" : "dDiscountAmount",
     "youMayAlsoLikeProducts" : [ null, null ],
     "iHowMuchLeftInStock" : 3,
     "maxQtyInfo" : "maxQtyInfo",
     "txProductDescription" : "txProductDescription",
     "tiDiscountType" : 7,
     "vProductSKU" : "vProductSKU",
     "vProductUnit" : "vProductUnit"
     }, {
     "tiIsFavourite" : 2,
     "vProductName" : "vProductName",
     "iProductCategoryId" : "iProductCategoryId",
     "dDiscountedProductPrice" : "dDiscountedProductPrice",
     "dbOriginalProductPrice" : "dbOriginalProductPrice",
     "vProductImage" : "vProductImage",
     "biProductId" : 2,
     "isLowQuantity" : 9,
     "dDiscountAmount" : "dDiscountAmount",
     "youMayAlsoLikeProducts" : [ null, null ],
     "iHowMuchLeftInStock" : 3,
     "maxQtyInfo" : "maxQtyInfo",
     "txProductDescription" : "txProductDescription",
     "tiDiscountType" : 7,
     "vProductSKU" : "vProductSKU",
     "vProductUnit" : "vProductUnit"
     } ]
     }, {
     "vName" : "vName",
     "vImage" : "vImage",
     "iProductCategoryId" : 5,
     "vCategoryName" : "vCategoryName",
     "id" : 6,
     "iOfferId" : 5,
     "biProductId" : 1,
     "products" : [ {
     "tiIsFavourite" : 2,
     "vProductName" : "vProductName",
     "iProductCategoryId" : "iProductCategoryId",
     "dDiscountedProductPrice" : "dDiscountedProductPrice",
     "dbOriginalProductPrice" : "dbOriginalProductPrice",
     "vProductImage" : "vProductImage",
     "biProductId" : 2,
     "isLowQuantity" : 9,
     "dDiscountAmount" : "dDiscountAmount",
     "youMayAlsoLikeProducts" : [ null, null ],
     "iHowMuchLeftInStock" : 3,
     "maxQtyInfo" : "maxQtyInfo",
     "txProductDescription" : "txProductDescription",
     "tiDiscountType" : 7,
     "vProductSKU" : "vProductSKU",
     "vProductUnit" : "vProductUnit"
     }, {
     "tiIsFavourite" : 2,
     "vProductName" : "vProductName",
     "iProductCategoryId" : "iProductCategoryId",
     "dDiscountedProductPrice" : "dDiscountedProductPrice",
     "dbOriginalProductPrice" : "dbOriginalProductPrice",
     "vProductImage" : "vProductImage",
     "biProductId" : 2,
     "isLowQuantity" : 9,
     "dDiscountAmount" : "dDiscountAmount",
     "youMayAlsoLikeProducts" : [ null, null ],
     "iHowMuchLeftInStock" : 3,
     "maxQtyInfo" : "maxQtyInfo",
     "txProductDescription" : "txProductDescription",
     "tiDiscountType" : 7,
     "vProductSKU" : "vProductSKU",
     "vProductUnit" : "vProductUnit"
     } ]
     } ],
     "banners" : [ {
     "vName" : "vName",
     "vImage" : "vImage",
     "tBannerDescription" : "tBannerDescription",
     "iProductCategoryId" : 5,
     "vCategoryName" : "vCategoryName",
     "id" : 6,
     "iOfferId" : 5,
     "biProductId" : 1
     }, {
     "vName" : "vName",
     "vImage" : "vImage",
     "tBannerDescription" : "tBannerDescription",
     "iProductCategoryId" : 5,
     "vCategoryName" : "vCategoryName",
     "id" : 6,
     "iOfferId" : 5,
     "biProductId" : 1
     } ],
     "sections" : [ {
     "category_name" : "category_name",
     "category_id" : 7,
     "vImage" : "vImage",
     "type" : 4,
     "banners" : [ {
     "tiLinkType" : 1,
     "iMarketingBannerId" : 1,
     "iProductCategoryId" : 7,
     "tiIsActive" : 1,
     "vCategoryName" : "vCategoryName",
     "iOfferId" : 1,
     "marketing_banner_image" : [ {
     "vImage" : "vImage",
     "iMarketingBannerId" : 4
     }, {
     "vImage" : "vImage",
     "iMarketingBannerId" : 4
     } ],
     "vTitle" : "vTitle",
     "biProductId" : 6,
     "vUrl" : "vUrl"
     }, {
     "tiLinkType" : 1,
     "iMarketingBannerId" : 1,
     "iProductCategoryId" : 7,
     "tiIsActive" : 1,
     "vCategoryName" : "vCategoryName",
     "iOfferId" : 1,
     "marketing_banner_image" : [ {
     "vImage" : "vImage",
     "iMarketingBannerId" : 4
     }, {
     "vImage" : "vImage",
     "iMarketingBannerId" : 4
     } ],
     "vTitle" : "vTitle",
     "biProductId" : 6,
     "vUrl" : "vUrl"
     } ],
     "products" : [ null, null ]
     }, {
     "category_name" : "category_name",
     "category_id" : 7,
     "vImage" : "vImage",
     "type" : 4,
     "banners" : [ {
     "tiLinkType" : 1,
     "iMarketingBannerId" : 1,
     "iProductCategoryId" : 7,
     "tiIsActive" : 1,
     "vCategoryName" : "vCategoryName",
     "iOfferId" : 1,
     "marketing_banner_image" : [ {
     "vImage" : "vImage",
     "iMarketingBannerId" : 4
     }, {
     "vImage" : "vImage",
     "iMarketingBannerId" : 4
     } ],
     "vTitle" : "vTitle",
     "biProductId" : 6,
     "vUrl" : "vUrl"
     }, {
     "tiLinkType" : 1,
     "iMarketingBannerId" : 1,
     "iProductCategoryId" : 7,
     "tiIsActive" : 1,
     "vCategoryName" : "vCategoryName",
     "iOfferId" : 1,
     "marketing_banner_image" : [ {
     "vImage" : "vImage",
     "iMarketingBannerId" : 4
     }, {
     "vImage" : "vImage",
     "iMarketingBannerId" : 4
     } ],
     "vTitle" : "vTitle",
     "biProductId" : 6,
     "vUrl" : "vUrl"
     } ],
     "products" : [ null, null ]
     } ],
     "dVatNumber" : "dVatNumber"
     },
     "responseMessage" : "responseMessage",
     "responseCode" : 0
     }}]
     - parameter authorization: (header)
     - parameter accept: (header)
     - parameter lang: (header)
     - parameter iCategoryId: (query) iCategoryId (optional, default to 1)
     
     - returns: RequestBuilder<LatestHomeListingResponse>
     */
    open class func dynamicHomeListingWithRequestBuilder(authorization: String, accept: String, lang: String, iCategoryId: Int? = nil) -> RequestBuilder<LatestHomeListingResponse> {
        let path = "/product/dynamic-home-listing"
        let URLString = SwaggerClientAPI.basePath + path
        let parameters: [String:Any]? = nil
        var url = URLComponents(string: URLString)
        url?.queryItems = APIHelper.mapValuesToQueryItems([
            "iCategoryId": iCategoryId?.encodeToJSON()
        ])
        let nillableHeaders: [String: Any?] = [
            "Authorization": authorization,
            "Accept": accept,
            "Lang": lang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)
        
        let requestBuilder: RequestBuilder<LatestHomeListingResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()
        
        return requestBuilder.init(method: "GET", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    /**
     Dynamic Home Listing Banner
     
     - parameter authorization: (header)
     - parameter accept: (header)
     - parameter lang: (header)
     - parameter iCategoryId: (query) iCategoryId (optional, default to 1)
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func dynamicHomeListingBanner(authorization: String, accept: String, lang: String, iCategoryId: Int? = nil, completion: @escaping ((_ data: LatestHomeListingResponse?,_ error: Error?) -> Void)) {
        dynamicHomeListingBannerWithRequestBuilder(authorization: authorization, accept: accept, lang: lang, iCategoryId: iCategoryId).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }
    
    open class func dynamicMarketHomeListingBanner(authorization: String, accept: String, lang: String, iCategoryId: Int? = nil, completion: @escaping ((_ data: LatestHomeListingResponse?,_ error: Error?) -> Void)) {
        dynamicMarketHomeListingBannerWithRequestBuilder(authorization: authorization, accept: accept, lang: lang, iCategoryId: iCategoryId).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }
    
    open class func dynamicNewMarketHomeListingBanner(authorization: String, accept: String, lang: String, iCategoryId: Int? = nil, completion: @escaping ((_ data: NewMainHomeModel?,_ error: Error?) -> Void)) {
        dynamicNewMarketHomeListingBannerWithRequestBuilder(authorization: authorization, accept: accept, lang: lang, iCategoryId: iCategoryId).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }
    
    
    /**
     Dynamic Home Listing Banner
     - GET /product/dynamic-home-listing-banner
     -
     
     - :
     - type: http
     - name: bearerAuth
     - examples: [{contentType=application/json, example={
     "responseData" : {
     "newlyArrivedProducts" : [ null, null ],
     "bestSellerProducts" : [ null, null ],
     "categories" : [ {
     "vName" : "vName",
     "vImage" : "vImage",
     "iProductCategoryId" : 5,
     "vCategoryName" : "vCategoryName",
     "id" : 6,
     "iOfferId" : 5,
     "biProductId" : 1,
     "products" : [ {
     "tiIsFavourite" : 2,
     "vProductName" : "vProductName",
     "iProductCategoryId" : "iProductCategoryId",
     "dDiscountedProductPrice" : "dDiscountedProductPrice",
     "dbOriginalProductPrice" : "dbOriginalProductPrice",
     "vProductImage" : "vProductImage",
     "biProductId" : 2,
     "isLowQuantity" : 9,
     "dDiscountAmount" : "dDiscountAmount",
     "youMayAlsoLikeProducts" : [ null, null ],
     "iHowMuchLeftInStock" : 3,
     "maxQtyInfo" : "maxQtyInfo",
     "txProductDescription" : "txProductDescription",
     "tiDiscountType" : 7,
     "vProductSKU" : "vProductSKU",
     "vProductUnit" : "vProductUnit"
     }, {
     "tiIsFavourite" : 2,
     "vProductName" : "vProductName",
     "iProductCategoryId" : "iProductCategoryId",
     "dDiscountedProductPrice" : "dDiscountedProductPrice",
     "dbOriginalProductPrice" : "dbOriginalProductPrice",
     "vProductImage" : "vProductImage",
     "biProductId" : 2,
     "isLowQuantity" : 9,
     "dDiscountAmount" : "dDiscountAmount",
     "youMayAlsoLikeProducts" : [ null, null ],
     "iHowMuchLeftInStock" : 3,
     "maxQtyInfo" : "maxQtyInfo",
     "txProductDescription" : "txProductDescription",
     "tiDiscountType" : 7,
     "vProductSKU" : "vProductSKU",
     "vProductUnit" : "vProductUnit"
     } ]
     }, {
     "vName" : "vName",
     "vImage" : "vImage",
     "iProductCategoryId" : 5,
     "vCategoryName" : "vCategoryName",
     "id" : 6,
     "iOfferId" : 5,
     "biProductId" : 1,
     "products" : [ {
     "tiIsFavourite" : 2,
     "vProductName" : "vProductName",
     "iProductCategoryId" : "iProductCategoryId",
     "dDiscountedProductPrice" : "dDiscountedProductPrice",
     "dbOriginalProductPrice" : "dbOriginalProductPrice",
     "vProductImage" : "vProductImage",
     "biProductId" : 2,
     "isLowQuantity" : 9,
     "dDiscountAmount" : "dDiscountAmount",
     "youMayAlsoLikeProducts" : [ null, null ],
     "iHowMuchLeftInStock" : 3,
     "maxQtyInfo" : "maxQtyInfo",
     "txProductDescription" : "txProductDescription",
     "tiDiscountType" : 7,
     "vProductSKU" : "vProductSKU",
     "vProductUnit" : "vProductUnit"
     }, {
     "tiIsFavourite" : 2,
     "vProductName" : "vProductName",
     "iProductCategoryId" : "iProductCategoryId",
     "dDiscountedProductPrice" : "dDiscountedProductPrice",
     "dbOriginalProductPrice" : "dbOriginalProductPrice",
     "vProductImage" : "vProductImage",
     "biProductId" : 2,
     "isLowQuantity" : 9,
     "dDiscountAmount" : "dDiscountAmount",
     "youMayAlsoLikeProducts" : [ null, null ],
     "iHowMuchLeftInStock" : 3,
     "maxQtyInfo" : "maxQtyInfo",
     "txProductDescription" : "txProductDescription",
     "tiDiscountType" : 7,
     "vProductSKU" : "vProductSKU",
     "vProductUnit" : "vProductUnit"
     } ]
     } ],
     "banners" : [ {
     "vName" : "vName",
     "vImage" : "vImage",
     "tBannerDescription" : "tBannerDescription",
     "iProductCategoryId" : 5,
     "vCategoryName" : "vCategoryName",
     "id" : 6,
     "iOfferId" : 5,
     "biProductId" : 1
     }, {
     "vName" : "vName",
     "vImage" : "vImage",
     "tBannerDescription" : "tBannerDescription",
     "iProductCategoryId" : 5,
     "vCategoryName" : "vCategoryName",
     "id" : 6,
     "iOfferId" : 5,
     "biProductId" : 1
     } ],
     "sections" : [ {
     "category_name" : "category_name",
     "category_id" : 7,
     "vImage" : "vImage",
     "type" : 4,
     "banners" : [ {
     "tiLinkType" : 1,
     "iMarketingBannerId" : 1,
     "iProductCategoryId" : 7,
     "tiIsActive" : 1,
     "vCategoryName" : "vCategoryName",
     "iOfferId" : 1,
     "marketing_banner_image" : [ {
     "vImage" : "vImage",
     "iMarketingBannerId" : 4
     }, {
     "vImage" : "vImage",
     "iMarketingBannerId" : 4
     } ],
     "vTitle" : "vTitle",
     "biProductId" : 6,
     "vUrl" : "vUrl"
     }, {
     "tiLinkType" : 1,
     "iMarketingBannerId" : 1,
     "iProductCategoryId" : 7,
     "tiIsActive" : 1,
     "vCategoryName" : "vCategoryName",
     "iOfferId" : 1,
     "marketing_banner_image" : [ {
     "vImage" : "vImage",
     "iMarketingBannerId" : 4
     }, {
     "vImage" : "vImage",
     "iMarketingBannerId" : 4
     } ],
     "vTitle" : "vTitle",
     "biProductId" : 6,
     "vUrl" : "vUrl"
     } ],
     "products" : [ null, null ]
     }, {
     "category_name" : "category_name",
     "category_id" : 7,
     "vImage" : "vImage",
     "type" : 4,
     "banners" : [ {
     "tiLinkType" : 1,
     "iMarketingBannerId" : 1,
     "iProductCategoryId" : 7,
     "tiIsActive" : 1,
     "vCategoryName" : "vCategoryName",
     "iOfferId" : 1,
     "marketing_banner_image" : [ {
     "vImage" : "vImage",
     "iMarketingBannerId" : 4
     }, {
     "vImage" : "vImage",
     "iMarketingBannerId" : 4
     } ],
     "vTitle" : "vTitle",
     "biProductId" : 6,
     "vUrl" : "vUrl"
     }, {
     "tiLinkType" : 1,
     "iMarketingBannerId" : 1,
     "iProductCategoryId" : 7,
     "tiIsActive" : 1,
     "vCategoryName" : "vCategoryName",
     "iOfferId" : 1,
     "marketing_banner_image" : [ {
     "vImage" : "vImage",
     "iMarketingBannerId" : 4
     }, {
     "vImage" : "vImage",
     "iMarketingBannerId" : 4
     } ],
     "vTitle" : "vTitle",
     "biProductId" : 6,
     "vUrl" : "vUrl"
     } ],
     "products" : [ null, null ]
     } ],
     "dVatNumber" : "dVatNumber"
     },
     "responseMessage" : "responseMessage",
     "responseCode" : 0
     }}]
     - parameter authorization: (header)
     - parameter accept: (header)
     - parameter lang: (header)
     - parameter iCategoryId: (query) iCategoryId (optional, default to 1)
     
     - returns: RequestBuilder<LatestHomeListingResponse>
     */
    open class func dynamicHomeListingBannerWithRequestBuilder(authorization: String, accept: String, lang: String, iCategoryId: Int? = nil) -> RequestBuilder<LatestHomeListingResponse> {
        let path = "/product/newHomeListing"
        let URLString = SwaggerClientAPI.basePath + path
        let parameters: [String:Any]? = nil
        var url = URLComponents(string: URLString)
        url?.queryItems = APIHelper.mapValuesToQueryItems([
            "iCategoryId": iCategoryId?.encodeToJSON()
        ])
        let nillableHeaders: [String: Any?] = [
            "Authorization": authorization,
            "Accept": accept,
            "Lang": lang,
//            "warehouse" : Constant.shared.SELECTED_WAREHOUSE_ID == 0 ? "" : Constant.shared.SELECTED_WAREHOUSE_ID
        ]
        
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)
        debugPrint(headerParameters)
        debugPrint(url)
        let requestBuilder: RequestBuilder<LatestHomeListingResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()
        
        return requestBuilder.init(method: "GET", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    
    open class func dynamicMarketHomeListingBannerWithRequestBuilder(authorization: String, accept: String, lang: String, iCategoryId: Int? = nil) -> RequestBuilder<LatestHomeListingResponse> {
        let path = "/home"
        let URLString = SwaggerClientAPI.baseMarketPath + path
        let parameters: [String:Any]? = nil
        var url = URLComponents(string: URLString)
        url?.queryItems = APIHelper.mapValuesToQueryItems([
            "iCategoryId": iCategoryId?.encodeToJSON()
        ])
        let nillableHeaders: [String: Any?] = [
            "Authorization": authorization,
            "Accept": accept,
            "Lang": lang,
            "warehouse" : Constant.shared.SELECTED_WAREHOUSE_ID == 0 ? "" : Constant.shared.SELECTED_WAREHOUSE_ID
        ]
        
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)
        debugPrint(headerParameters)
        let requestBuilder: RequestBuilder<LatestHomeListingResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()
        
        return requestBuilder.init(method: "GET", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    
    open class func dynamicNewMarketHomeListingBannerWithRequestBuilder(authorization: String, accept: String, lang: String, iCategoryId: Int? = nil) -> RequestBuilder<NewMainHomeModel> {
        let path = "/market-new-home"
        let URLString = SwaggerClientAPI.baseMarketPath + path
        let parameters: [String:Any]? = nil
        var url = URLComponents(string: URLString)
//        url?.queryItems = APIHelper.mapValuesToQueryItems([
//            "iCategoryId": iCategoryId?.encodeToJSON()
//        ])
        let nillableHeaders: [String: Any?] = [
            "Authorization": authorization,
            "Accept": accept,
            "Lang": lang,
            "warehouse" : Constant.shared.SELECTED_WAREHOUSE_ID == 0 ? "" : Constant.shared.SELECTED_WAREHOUSE_ID
        ]
        
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)
        debugPrint(headerParameters)
        let requestBuilder: RequestBuilder<NewMainHomeModel>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()
        
        return requestBuilder.init(method: "GET", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    /**
     Home Listing
     
     - parameter authorization: (header)
     - parameter accept: (header)
     - parameter lang: (header)
     - parameter iCategoryId: (query) iCategoryId (optional, default to 1)
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func homeListing(authorization: String, accept: String, lang: String, iCategoryId: Int? = nil, completion: @escaping ((_ data: HomeListingResponse?,_ error: Error?) -> Void)) {
        homeListingWithRequestBuilder(authorization: authorization, accept: accept, lang: lang, iCategoryId: iCategoryId).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }
    
    
    /**
     Home Listing
     - GET /product/home-listing
     -
     
     - :
     - type: http
     - name: bearerAuth
     - examples: [{contentType=application/json, example={
     "responseData" : {
     "isCurrentOrderAvailable" : 1,
     "cartProductCount" : 6,
     "newlyArrivedProducts" : [ {
     "tiIsFavourite" : 2,
     "vProductName" : "vProductName",
     "iProductCategoryId" : "iProductCategoryId",
     "dDiscountedProductPrice" : "dDiscountedProductPrice",
     "dbOriginalProductPrice" : "dbOriginalProductPrice",
     "vProductImage" : "vProductImage",
     "biProductId" : 2,
     "isLowQuantity" : 9,
     "dDiscountAmount" : "dDiscountAmount",
     "youMayAlsoLikeProducts" : [ null, null ],
     "iHowMuchLeftInStock" : 3,
     "maxQtyInfo" : "maxQtyInfo",
     "txProductDescription" : "txProductDescription",
     "tiDiscountType" : 7,
     "vProductSKU" : "vProductSKU",
     "vProductUnit" : "vProductUnit"
     }, {
     "tiIsFavourite" : 2,
     "vProductName" : "vProductName",
     "iProductCategoryId" : "iProductCategoryId",
     "dDiscountedProductPrice" : "dDiscountedProductPrice",
     "dbOriginalProductPrice" : "dbOriginalProductPrice",
     "vProductImage" : "vProductImage",
     "biProductId" : 2,
     "isLowQuantity" : 9,
     "dDiscountAmount" : "dDiscountAmount",
     "youMayAlsoLikeProducts" : [ null, null ],
     "iHowMuchLeftInStock" : 3,
     "maxQtyInfo" : "maxQtyInfo",
     "txProductDescription" : "txProductDescription",
     "tiDiscountType" : 7,
     "vProductSKU" : "vProductSKU",
     "vProductUnit" : "vProductUnit"
     } ],
     "bestSellerProducts" : [ null, null ],
     "categories" : [ null, null ],
     "banners" : [ {
     "vName" : "vName",
     "vImage" : "vImage",
     "tBannerDescription" : "tBannerDescription",
     "iProductCategoryId" : 5,
     "vCategoryName" : "vCategoryName",
     "id" : 6,
     "iOfferId" : 5,
     "biProductId" : 1
     }, {
     "vName" : "vName",
     "vImage" : "vImage",
     "tBannerDescription" : "tBannerDescription",
     "iProductCategoryId" : 5,
     "vCategoryName" : "vCategoryName",
     "id" : 6,
     "iOfferId" : 5,
     "biProductId" : 1
     } ]
     },
     "responseMessage" : "responseMessage",
     "responseCode" : 0
     }}]
     - parameter authorization: (header)
     - parameter accept: (header)
     - parameter lang: (header)
     - parameter iCategoryId: (query) iCategoryId (optional, default to 1)
     
     - returns: RequestBuilder<HomeListingResponse>
     */
    open class func homeListingWithRequestBuilder(authorization: String, accept: String, lang: String, iCategoryId: Int? = nil) -> RequestBuilder<HomeListingResponse> {
        let path = "/product/home-listing"
        let URLString = SwaggerClientAPI.basePath + path
        let parameters: [String:Any]? = nil
        var url = URLComponents(string: URLString)
        url?.queryItems = APIHelper.mapValuesToQueryItems([
            "iCategoryId": iCategoryId?.encodeToJSON()
        ])
        let nillableHeaders: [String: Any?] = [
            "Authorization": authorization,
            "Accept": accept,
            "Lang": lang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)
        
        let requestBuilder: RequestBuilder<HomeListingResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()
        
        return requestBuilder.init(method: "GET", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    /**
     Latest Home Listing
     
     - parameter authorization: (header)
     - parameter accept: (header)
     - parameter lang: (header)
     - parameter iCategoryId: (query) iCategoryId (optional, default to 1)
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func latestHomeListing(authorization: String, accept: String, lang: String, iCategoryId: Int? = nil, completion: @escaping ((_ data: LatestHomeListingResponse?,_ error: Error?) -> Void)) {
        latestHomeListingWithRequestBuilder(authorization: authorization, accept: accept, lang: lang, iCategoryId: iCategoryId).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }
    
    
    /**
     Latest Home Listing
     - GET /product/latest-home-listing
     -
     
     - :
     - type: http
     - name: bearerAuth
     - examples: [{contentType=application/json, example={
     "responseData" : {
     "newlyArrivedProducts" : [ null, null ],
     "bestSellerProducts" : [ null, null ],
     "categories" : [ {
     "vName" : "vName",
     "vImage" : "vImage",
     "iProductCategoryId" : 5,
     "vCategoryName" : "vCategoryName",
     "id" : 6,
     "iOfferId" : 5,
     "biProductId" : 1,
     "products" : [ {
     "tiIsFavourite" : 2,
     "vProductName" : "vProductName",
     "iProductCategoryId" : "iProductCategoryId",
     "dDiscountedProductPrice" : "dDiscountedProductPrice",
     "dbOriginalProductPrice" : "dbOriginalProductPrice",
     "vProductImage" : "vProductImage",
     "biProductId" : 2,
     "isLowQuantity" : 9,
     "dDiscountAmount" : "dDiscountAmount",
     "youMayAlsoLikeProducts" : [ null, null ],
     "iHowMuchLeftInStock" : 3,
     "maxQtyInfo" : "maxQtyInfo",
     "txProductDescription" : "txProductDescription",
     "tiDiscountType" : 7,
     "vProductSKU" : "vProductSKU",
     "vProductUnit" : "vProductUnit"
     }, {
     "tiIsFavourite" : 2,
     "vProductName" : "vProductName",
     "iProductCategoryId" : "iProductCategoryId",
     "dDiscountedProductPrice" : "dDiscountedProductPrice",
     "dbOriginalProductPrice" : "dbOriginalProductPrice",
     "vProductImage" : "vProductImage",
     "biProductId" : 2,
     "isLowQuantity" : 9,
     "dDiscountAmount" : "dDiscountAmount",
     "youMayAlsoLikeProducts" : [ null, null ],
     "iHowMuchLeftInStock" : 3,
     "maxQtyInfo" : "maxQtyInfo",
     "txProductDescription" : "txProductDescription",
     "tiDiscountType" : 7,
     "vProductSKU" : "vProductSKU",
     "vProductUnit" : "vProductUnit"
     } ]
     }, {
     "vName" : "vName",
     "vImage" : "vImage",
     "iProductCategoryId" : 5,
     "vCategoryName" : "vCategoryName",
     "id" : 6,
     "iOfferId" : 5,
     "biProductId" : 1,
     "products" : [ {
     "tiIsFavourite" : 2,
     "vProductName" : "vProductName",
     "iProductCategoryId" : "iProductCategoryId",
     "dDiscountedProductPrice" : "dDiscountedProductPrice",
     "dbOriginalProductPrice" : "dbOriginalProductPrice",
     "vProductImage" : "vProductImage",
     "biProductId" : 2,
     "isLowQuantity" : 9,
     "dDiscountAmount" : "dDiscountAmount",
     "youMayAlsoLikeProducts" : [ null, null ],
     "iHowMuchLeftInStock" : 3,
     "maxQtyInfo" : "maxQtyInfo",
     "txProductDescription" : "txProductDescription",
     "tiDiscountType" : 7,
     "vProductSKU" : "vProductSKU",
     "vProductUnit" : "vProductUnit"
     }, {
     "tiIsFavourite" : 2,
     "vProductName" : "vProductName",
     "iProductCategoryId" : "iProductCategoryId",
     "dDiscountedProductPrice" : "dDiscountedProductPrice",
     "dbOriginalProductPrice" : "dbOriginalProductPrice",
     "vProductImage" : "vProductImage",
     "biProductId" : 2,
     "isLowQuantity" : 9,
     "dDiscountAmount" : "dDiscountAmount",
     "youMayAlsoLikeProducts" : [ null, null ],
     "iHowMuchLeftInStock" : 3,
     "maxQtyInfo" : "maxQtyInfo",
     "txProductDescription" : "txProductDescription",
     "tiDiscountType" : 7,
     "vProductSKU" : "vProductSKU",
     "vProductUnit" : "vProductUnit"
     } ]
     } ],
     "banners" : [ {
     "vName" : "vName",
     "vImage" : "vImage",
     "tBannerDescription" : "tBannerDescription",
     "iProductCategoryId" : 5,
     "vCategoryName" : "vCategoryName",
     "id" : 6,
     "iOfferId" : 5,
     "biProductId" : 1
     }, {
     "vName" : "vName",
     "vImage" : "vImage",
     "tBannerDescription" : "tBannerDescription",
     "iProductCategoryId" : 5,
     "vCategoryName" : "vCategoryName",
     "id" : 6,
     "iOfferId" : 5,
     "biProductId" : 1
     } ],
     "sections" : [ {
     "category_name" : "category_name",
     "category_id" : 7,
     "vImage" : "vImage",
     "type" : 4,
     "banners" : [ {
     "tiLinkType" : 1,
     "iMarketingBannerId" : 1,
     "iProductCategoryId" : 7,
     "tiIsActive" : 1,
     "vCategoryName" : "vCategoryName",
     "iOfferId" : 1,
     "marketing_banner_image" : [ {
     "vImage" : "vImage",
     "iMarketingBannerId" : 4
     }, {
     "vImage" : "vImage",
     "iMarketingBannerId" : 4
     } ],
     "vTitle" : "vTitle",
     "biProductId" : 6,
     "vUrl" : "vUrl"
     }, {
     "tiLinkType" : 1,
     "iMarketingBannerId" : 1,
     "iProductCategoryId" : 7,
     "tiIsActive" : 1,
     "vCategoryName" : "vCategoryName",
     "iOfferId" : 1,
     "marketing_banner_image" : [ {
     "vImage" : "vImage",
     "iMarketingBannerId" : 4
     }, {
     "vImage" : "vImage",
     "iMarketingBannerId" : 4
     } ],
     "vTitle" : "vTitle",
     "biProductId" : 6,
     "vUrl" : "vUrl"
     } ],
     "products" : [ null, null ]
     }, {
     "category_name" : "category_name",
     "category_id" : 7,
     "vImage" : "vImage",
     "type" : 4,
     "banners" : [ {
     "tiLinkType" : 1,
     "iMarketingBannerId" : 1,
     "iProductCategoryId" : 7,
     "tiIsActive" : 1,
     "vCategoryName" : "vCategoryName",
     "iOfferId" : 1,
     "marketing_banner_image" : [ {
     "vImage" : "vImage",
     "iMarketingBannerId" : 4
     }, {
     "vImage" : "vImage",
     "iMarketingBannerId" : 4
     } ],
     "vTitle" : "vTitle",
     "biProductId" : 6,
     "vUrl" : "vUrl"
     }, {
     "tiLinkType" : 1,
     "iMarketingBannerId" : 1,
     "iProductCategoryId" : 7,
     "tiIsActive" : 1,
     "vCategoryName" : "vCategoryName",
     "iOfferId" : 1,
     "marketing_banner_image" : [ {
     "vImage" : "vImage",
     "iMarketingBannerId" : 4
     }, {
     "vImage" : "vImage",
     "iMarketingBannerId" : 4
     } ],
     "vTitle" : "vTitle",
     "biProductId" : 6,
     "vUrl" : "vUrl"
     } ],
     "products" : [ null, null ]
     } ],
     "dVatNumber" : "dVatNumber"
     },
     "responseMessage" : "responseMessage",
     "responseCode" : 0
     }}]
     - parameter authorization: (header)
     - parameter accept: (header)
     - parameter lang: (header)
     - parameter iCategoryId: (query) iCategoryId (optional, default to 1)
     
     - returns: RequestBuilder<LatestHomeListingResponse>
     */
    open class func latestHomeListingWithRequestBuilder(authorization: String, accept: String, lang: String, iCategoryId: Int? = nil) -> RequestBuilder<LatestHomeListingResponse> {
        let path = "/product/latest-home-listing"
        let URLString = SwaggerClientAPI.basePath + path
        let parameters: [String:Any]? = nil
        var url = URLComponents(string: URLString)
        url?.queryItems = APIHelper.mapValuesToQueryItems([
            "iCategoryId": iCategoryId?.encodeToJSON()
        ])
        let nillableHeaders: [String: Any?] = [
            "Authorization": authorization,
            "Accept": accept,
            "Lang": lang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)
        
        let requestBuilder: RequestBuilder<LatestHomeListingResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()
        
        return requestBuilder.init(method: "GET", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    /**
     Latest Home Web Listing
     
     - parameter accept: (header)
     - parameter lang: (header)
     - parameter authorization: (header)  (optional, default to Bearer )
     - parameter iCategoryId: (query) iCategoryId (optional, default to 1)
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func latestHomeWebListing(accept: String, lang: String, authorization: String? = nil, iCategoryId: Int? = nil, completion: @escaping ((_ data: LatestHomeWebListingResponse?,_ error: Error?) -> Void)) {
        latestHomeWebListingWithRequestBuilder(accept: accept, lang: lang, authorization: authorization, iCategoryId: iCategoryId).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }
    
    
    /**
     Latest Home Web Listing
     - GET /product/latest-home-web-listing
     -
     
     - :
     - type: http
     - name: bearerAuth
     - examples: [{contentType=application/json, example={
     "responseData" : {
     "newlyArrivedProducts" : [ {
     "vProductName" : "vProductName",
     "iProductCategoryId" : "iProductCategoryId",
     "dDiscountedProductPrice" : "dDiscountedProductPrice",
     "dbOriginalProductPrice" : "dbOriginalProductPrice",
     "vProductImage" : "vProductImage",
     "biProductId" : 1,
     "isLowQuantity" : 5,
     "dDiscountAmount" : "dDiscountAmount",
     "iHowMuchLeftInStock" : 2,
     "txProductDescription" : "txProductDescription",
     "tiDiscountType" : 5,
     "vProductSKU" : "vProductSKU",
     "vProductUnit" : "vProductUnit"
     }, {
     "vProductName" : "vProductName",
     "iProductCategoryId" : "iProductCategoryId",
     "dDiscountedProductPrice" : "dDiscountedProductPrice",
     "dbOriginalProductPrice" : "dbOriginalProductPrice",
     "vProductImage" : "vProductImage",
     "biProductId" : 1,
     "isLowQuantity" : 5,
     "dDiscountAmount" : "dDiscountAmount",
     "iHowMuchLeftInStock" : 2,
     "txProductDescription" : "txProductDescription",
     "tiDiscountType" : 5,
     "vProductSKU" : "vProductSKU",
     "vProductUnit" : "vProductUnit"
     } ],
     "bestSellerProducts" : [ null, null ],
     "categories" : [ {
     "vName" : "vName",
     "vImage" : "vImage",
     "id" : 6
     }, {
     "vName" : "vName",
     "vImage" : "vImage",
     "id" : 6
     } ]
     },
     "responseMessage" : "responseMessage",
     "responseCode" : 0
     }}]
     - parameter accept: (header)
     - parameter lang: (header)
     - parameter authorization: (header)  (optional, default to Bearer )
     - parameter iCategoryId: (query) iCategoryId (optional, default to 1)
     
     - returns: RequestBuilder<LatestHomeWebListingResponse>
     */
    open class func latestHomeWebListingWithRequestBuilder(accept: String, lang: String, authorization: String? = nil, iCategoryId: Int? = nil) -> RequestBuilder<LatestHomeWebListingResponse> {
        let path = "/product/latest-home-web-listing"
        let URLString = SwaggerClientAPI.basePath + path
        let parameters: [String:Any]? = nil
        var url = URLComponents(string: URLString)
        url?.queryItems = APIHelper.mapValuesToQueryItems([
            "iCategoryId": iCategoryId?.encodeToJSON()
        ])
        let nillableHeaders: [String: Any?] = [
            "Authorization": authorization,
            "Accept": accept,
            "Lang": lang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)
        
        let requestBuilder: RequestBuilder<LatestHomeWebListingResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()
        
        return requestBuilder.init(method: "GET", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    /**
     Product Listing (on view more click)
     
     - parameter authorization: (header)
     - parameter accept: (header)
     - parameter lang: (header)
     - parameter iProductType: (form)  (optional)
     - parameter iCategoryId: (form)  (optional)
     - parameter vSerchString: (form)  (optional)
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func latestProductListing(authorization: String, accept: String, lang: String, iProductType: Int? = nil, iCategoryId: Int? = nil, vSerchString: String? = nil, completion: @escaping ((_ data: LatestProductListResponse?,_ error: Error?) -> Void)) {
        latestProductListingWithRequestBuilder(authorization: authorization, accept: accept, lang: lang, iProductType: iProductType, iCategoryId: iCategoryId, vSerchString: vSerchString).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }
    
    
    /**
     Product Listing (on view more click)
     - POST /product/latest-product-listing
     -
     
     - :
     - type: http
     - name: bearerAuth
     - examples: [{contentType=application/json, example={
     "responseMessage" : "responseMessage",
     "productList" : [ {
     "tiIsFavourite" : 2,
     "vProductName" : "vProductName",
     "iProductCategoryId" : "iProductCategoryId",
     "dDiscountedProductPrice" : "dDiscountedProductPrice",
     "dbOriginalProductPrice" : "dbOriginalProductPrice",
     "vProductImage" : "vProductImage",
     "biProductId" : 2,
     "isLowQuantity" : 9,
     "dDiscountAmount" : "dDiscountAmount",
     "youMayAlsoLikeProducts" : [ null, null ],
     "iHowMuchLeftInStock" : 3,
     "maxQtyInfo" : "maxQtyInfo",
     "txProductDescription" : "txProductDescription",
     "tiDiscountType" : 7,
     "vProductSKU" : "vProductSKU",
     "vProductUnit" : "vProductUnit"
     }, {
     "tiIsFavourite" : 2,
     "vProductName" : "vProductName",
     "iProductCategoryId" : "iProductCategoryId",
     "dDiscountedProductPrice" : "dDiscountedProductPrice",
     "dbOriginalProductPrice" : "dbOriginalProductPrice",
     "vProductImage" : "vProductImage",
     "biProductId" : 2,
     "isLowQuantity" : 9,
     "dDiscountAmount" : "dDiscountAmount",
     "youMayAlsoLikeProducts" : [ null, null ],
     "iHowMuchLeftInStock" : 3,
     "maxQtyInfo" : "maxQtyInfo",
     "txProductDescription" : "txProductDescription",
     "tiDiscountType" : 7,
     "vProductSKU" : "vProductSKU",
     "vProductUnit" : "vProductUnit"
     } ],
     "responseCode" : 0
     }}]
     - parameter authorization: (header)
     - parameter accept: (header)
     - parameter lang: (header)
     - parameter iProductType: (form)  (optional)
     - parameter iCategoryId: (form)  (optional)
     - parameter vSerchString: (form)  (optional)
     
     - returns: RequestBuilder<LatestProductListResponse>
     */
    open class func latestProductListingWithRequestBuilder(authorization: String, accept: String, lang: String, iProductType: Int? = nil, iCategoryId: Int? = nil, vSerchString: String? = nil) -> RequestBuilder<LatestProductListResponse> {
        let path = "/product/latest-product-listing"
        let URLString = SwaggerClientAPI.basePath + path
        let formParams: [String:Any?] = [
            "iProductType": iProductType?.encodeToJSON(),
            "iCategoryId": iCategoryId?.encodeToJSON(),
            "vSerchString": vSerchString
        ]
        
        let nonNullParameters = APIHelper.rejectNil(formParams)
        let parameters = APIHelper.convertBoolToString(nonNullParameters)
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any?] = [
            "Authorization": authorization,
            "Accept": accept,
            "Lang": lang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)
        
        let requestBuilder: RequestBuilder<LatestProductListResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()
        
        return requestBuilder.init(method: "POST", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    /**
     Product Details
     
     - parameter authorization: (header)
     - parameter accept: (header)
     - parameter lang: (header)
     - parameter biProductId: (path)
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func productDetails(authorization: String, accept: String, lang: String, biProductId: Int, completion: @escaping ((_ data: ProductDetailResponse?,_ error: Error?) -> Void)) {
        productDetailsWithRequestBuilder(authorization: authorization, accept: accept, lang: lang, biProductId: biProductId).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }
    
    open class func marketProductDetails(authorization: String, accept: String, lang: String, biProductId: Int, completion: @escaping ((_ data: ProductDetailResponse?,_ error: Error?) -> Void)) {
        marketProductDetailsWithRequestBuilder(authorization: authorization, accept: accept, lang: lang, biProductId: biProductId).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }
    
    
    /**
     Product Details
     - GET /product/product-details/{biProductId}
     -
     
     - :
     - type: http
     - name: bearerAuth
     - examples: [{contentType=application/json, example={
     "responseData" : {
     "productDetails" : {
     "tiIsFavourite" : 2,
     "vProductName" : "vProductName",
     "iProductCategoryId" : "iProductCategoryId",
     "dDiscountedProductPrice" : "dDiscountedProductPrice",
     "dbOriginalProductPrice" : "dbOriginalProductPrice",
     "vProductImage" : "vProductImage",
     "biProductId" : 2,
     "isLowQuantity" : 9,
     "dDiscountAmount" : "dDiscountAmount",
     "youMayAlsoLikeProducts" : [ null, null ],
     "iHowMuchLeftInStock" : 3,
     "maxQtyInfo" : "maxQtyInfo",
     "txProductDescription" : "txProductDescription",
     "tiDiscountType" : 7,
     "vProductSKU" : "vProductSKU",
     "vProductUnit" : "vProductUnit"
     },
     "products" : [ null, null ]
     },
     "responseMessage" : "responseMessage",
     "responseCode" : 0
     }}]
     - parameter authorization: (header)
     - parameter accept: (header)
     - parameter lang: (header)
     - parameter biProductId: (path)
     
     - returns: RequestBuilder<ProductDetailResponse>
     */
    open class func productDetailsWithRequestBuilder(authorization: String, accept: String, lang: String, biProductId: Int) -> RequestBuilder<ProductDetailResponse> {
        var path = "/product/product-details/{biProductId}"
        let biProductIdPreEscape = "\(biProductId)"
        let biProductIdPostEscape = biProductIdPreEscape.addingPercentEncoding(withAllowedCharacters: .urlPathAllowed) ?? ""
        path = path.replacingOccurrences(of: "{biProductId}", with: biProductIdPostEscape, options: .literal, range: nil)
        let URLString = SwaggerClientAPI.basePath + path
        let parameters: [String:Any]? = nil
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any?] = [
            "Authorization": authorization,
            "Accept": accept,
            "Lang": lang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)
        
        let requestBuilder: RequestBuilder<ProductDetailResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()
        
        return requestBuilder.init(method: "GET", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    
    
    open class func marketProductDetailsWithRequestBuilder(authorization: String, accept: String, lang: String, biProductId: Int) -> RequestBuilder<ProductDetailResponse> {
        var path = "/product/product-details/{biProductId}"
        let biProductIdPreEscape = "\(biProductId)"
        let biProductIdPostEscape = biProductIdPreEscape.addingPercentEncoding(withAllowedCharacters: .urlPathAllowed) ?? ""
        path = path.replacingOccurrences(of: "{biProductId}", with: biProductIdPostEscape, options: .literal, range: nil)
        let URLString = SwaggerClientAPI.baseMarketPath + path
        let parameters: [String:Any]? = nil
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any?] = [
            "Authorization": authorization,
            "Accept": accept,
            "Lang": lang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)
        
        let requestBuilder: RequestBuilder<ProductDetailResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()
        
        return requestBuilder.init(method: "GET", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    /**
     Product info
     
     - parameter authorization: (header)
     - parameter accept: (header)
     - parameter lang: (header)
     - parameter biProductId: (form)  (optional)
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func productInfo(authorization: String, accept: String, lang: String, biProductId: Int? = nil, biBundleId: Int? = nil, completion: @escaping ((_ data: ProductInfoResponse?,_ error: Error?) -> Void)) {
        productInfoWithRequestBuilder(authorization: authorization, accept: accept, lang: lang, biProductId: biProductId, biBundleId: biBundleId).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }
    
    
    /**
     Product info
     - POST /product/product-info
     -
     
     - :
     - type: http
     - name: bearerAuth
     - examples: [{contentType=application/json, example={
     "responseData" : {
     "offer-description" : "offer-description",
     "max-qty" : "max-qty",
     "date-range" : "date-range"
     },
     "responseMessage" : "responseMessage",
     "responseCode" : 0
     }}]
     - parameter authorization: (header)
     - parameter accept: (header)
     - parameter lang: (header)
     - parameter biProductId: (form)  (optional)
     
     - returns: RequestBuilder<ProductInfoResponse>
     */
    open class func productInfoWithRequestBuilder(authorization: String, accept: String, lang: String, biProductId: Int? = nil, biBundleId: Int? = nil) -> RequestBuilder<ProductInfoResponse> {
        let path = "/product/product-info"
        let URLString = SwaggerClientAPI.basePath + path
        let formParams: [String:Any?] = [
            "biProductId": biProductId?.encodeToJSON(),
            "bunddelId": biBundleId?.encodeToJSON()
        ]
        
        let nonNullParameters = APIHelper.rejectNil(formParams)
        let parameters = APIHelper.convertBoolToString(nonNullParameters)
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any?] = [
            "Authorization": authorization,
            "Accept": accept,
            "Lang": lang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)
        
        let requestBuilder: RequestBuilder<ProductInfoResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()
        
        print(url)
        print(parameters)
        print(headerParameters)
        
        return requestBuilder.init(method: "POST", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    /**
     Product Listing (on view more click)
     
     - parameter authorization: (header)
     - parameter accept: (header)
     - parameter lang: (header)
     - parameter iProductType: (form)  (optional)
     - parameter iCategoryId: (form)  (optional)
     - parameter vSerchString: (form)  (optional)
     - parameter offset: (query) offset (optional, default to 0)
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func productListing(authorization: String, accept: String, lang: String, iProductType: Int? = nil, iCategoryId: Int? = nil, vSerchString: String? = nil, offset: Int? = nil, pageNo: Int, isOnlyBundle: Int? = 0, completion: @escaping ((_ data: ProductListResponse?,_ error: Error?) -> Void)) {
        productListingWithRequestBuilder(authorization: authorization, accept: accept, lang: lang, iProductType: iProductType, iCategoryId: iCategoryId, vSerchString: vSerchString, offset: offset, pageNo: pageNo, isOnlyBundle: isOnlyBundle).execute { (response, error) -> Void in
            print("--->>> \(response?.body)")
            completion(response?.body, error)
        }
    }
    
    open class func NewProductListing(authorization: String, accept: String, lang: String, iProductType: Int? = nil, iCategoryId: Int? = nil, vSerchString: String? = nil, offset: Int? = nil, pageNo: Int, isOnlyBundle: Int? = 0, completion: @escaping ((_ data: NewProductListResponse?,_ error: Error?) -> Void)) {
        NewProductListingWithRequestBuilder(authorization: authorization, accept: accept, lang: lang, iProductType: iProductType, iCategoryId: iCategoryId, vSerchString: vSerchString, offset: offset, pageNo: pageNo, isOnlyBundle: isOnlyBundle).execute { (response, error) -> Void in
            print("--->>> \(response?.body)")
            completion(response?.body, error)
        }
    }
    
    open class func NewViewProductListing(authorization: String, accept: String, lang: String, banner_id: Int? = nil, msi_id: Int? = nil, completion: @escaping ((_ data: NewProductListResponse?,_ error: Error?) -> Void)) {
        NewViewProductListingWithRequestBuilder(authorization: authorization, accept: accept , lang: lang, msi_id: msi_id, banner_id: banner_id).execute { (response, error) -> Void in
            print("--->>> \(response?.body)")
            completion(response?.body, error)
        }
    }
    
    open class func NewSubCategoryProductsListing(authorization: String, accept: String, lang: String, category_id: Int? = nil, sub_category_id: Int? = nil, completion: @escaping ((_ data: NewSubCategoryModel?,_ error: Error?) -> Void)) {
        NewSubCategoryProductsListingWithRequestBuilder(authorization: authorization, accept: accept, lang: lang , iCategoryId: category_id , iSubCategoryId: sub_category_id).execute { (response, error) -> Void in
            print("--->>> \(response?.body)")
            completion(response?.body, error)
        }
    }
    
    open class func productSearchListing(authorization: String, accept: String, lang: String, iProductType: Int? = nil, iCategoryId: Int? = nil, vSerchString: String? = nil, offset: Int? = nil, pageNo: Int, isOnlyBundle: Int? = 0, completion: @escaping ((_ data: ProductListResponse?,_ error: Error?) -> Void)) {
        productSearchListingWithRequestBuilder(authorization: authorization, accept: accept, lang: lang, iProductType: iProductType, iCategoryId: iCategoryId, vSerchString: vSerchString, offset: offset, pageNo: pageNo, isOnlyBundle: isOnlyBundle).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }
    
    
    /**
     Product Listing (on view more click)
     - POST /product/product-listing
     -
     
     - :
     - type: http
     - name: bearerAuth
     - examples: [{contentType=application/json, example={
     "responseData" : {
     "totalPage" : 5,
     "limit" : 6,
     "totalRecord" : 1,
     "productList" : [ {
     "tiIsFavourite" : 2,
     "vProductName" : "vProductName",
     "iProductCategoryId" : "iProductCategoryId",
     "dDiscountedProductPrice" : "dDiscountedProductPrice",
     "dbOriginalProductPrice" : "dbOriginalProductPrice",
     "vProductImage" : "vProductImage",
     "biProductId" : 2,
     "isLowQuantity" : 9,
     "dDiscountAmount" : "dDiscountAmount",
     "youMayAlsoLikeProducts" : [ null, null ],
     "iHowMuchLeftInStock" : 3,
     "maxQtyInfo" : "maxQtyInfo",
     "txProductDescription" : "txProductDescription",
     "tiDiscountType" : 7,
     "vProductSKU" : "vProductSKU",
     "vProductUnit" : "vProductUnit"
     }, {
     "tiIsFavourite" : 2,
     "vProductName" : "vProductName",
     "iProductCategoryId" : "iProductCategoryId",
     "dDiscountedProductPrice" : "dDiscountedProductPrice",
     "dbOriginalProductPrice" : "dbOriginalProductPrice",
     "vProductImage" : "vProductImage",
     "biProductId" : 2,
     "isLowQuantity" : 9,
     "dDiscountAmount" : "dDiscountAmount",
     "youMayAlsoLikeProducts" : [ null, null ],
     "iHowMuchLeftInStock" : 3,
     "maxQtyInfo" : "maxQtyInfo",
     "txProductDescription" : "txProductDescription",
     "tiDiscountType" : 7,
     "vProductSKU" : "vProductSKU",
     "vProductUnit" : "vProductUnit"
     } ]
     },
     "responseMessage" : "responseMessage",
     "responseCode" : 0
     }}]
     - parameter authorization: (header)
     - parameter accept: (header)
     - parameter lang: (header)
     - parameter iProductType: (form)  (optional)
     - parameter iCategoryId: (form)  (optional)
     - parameter vSerchString: (form)  (optional)
     - parameter offset: (query) offset (optional, default to 0)
     
     - returns: RequestBuilder<ProductListResponse>
     */
    open class func productListingWithRequestBuilder(authorization: String, accept: String, lang: String, iProductType: Int? = nil, iCategoryId: Int? = nil, vSerchString: String? = nil, offset: Int? = nil, pageNo: Int? = nil, isOnlyBundle: Int? = 0) -> RequestBuilder<ProductListResponse> {
        let path = "/product/newProductListing"
                let URLString = SwaggerClientAPI.basePath + path
                let formParams: [String:Any?] = [
                    "iProductCategoryId": iCategoryId ?? 0 == 0 ? "" : iCategoryId?.encodeToJSON(),
                    "onlyBunddel": isOnlyBundle == 0 ? "0".encodeToJSON() : "1".encodeToJSON(),
                    "vSearchString": vSerchString,
                    "page": pageNo?.encodeToJSON()
                ]
        debugPrint("formParams ==>> ",formParams)
        let nonNullParameters = APIHelper.rejectNil(formParams)
        let parameters = APIHelper.convertBoolToString(nonNullParameters)
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any?] = [
            "Authorization": authorization,
            "Accept": accept,
            "Lang": lang,
            "warehouse": Constant.shared.SELECTED_WAREHOUSE_ID > 0 ? "\(Constant.shared.SELECTED_WAREHOUSE_ID)" : nil
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)
        
        debugPrint("headerParameters ==>> ",headerParameters)
        debugPrint("Final Full URL: \((url?.string ?? URLString))")
        
        let requestBuilder: RequestBuilder<ProductListResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()
        
        return requestBuilder.init(method: "GET", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    
    open class func NewSubCategoryProductsListingWithRequestBuilder(authorization: String, accept: String, lang: String, iCategoryId: Int? = nil , iSubCategoryId: Int? = nil) -> RequestBuilder<NewSubCategoryModel> {
        let path = "/market-new-category"
        let URLString = SwaggerClientAPI.baseMarketUrl + path
        debugPrint("URLString ==>> ",URLString)
        let formParams: [String:Any?] = [
            "sub_category_id": (iSubCategoryId != nil && iSubCategoryId != 0) ? iSubCategoryId!.encodeToJSON() : nil,
            "get_products" : 1,
            "category_id" : (iCategoryId != nil && iCategoryId != 0) ? iCategoryId!.encodeToJSON() : nil,
        ]
        debugPrint("formParams ==>> ",formParams)
        let nonNullParameters = APIHelper.rejectNil(formParams)
        let parameters = APIHelper.convertBoolToString(nonNullParameters)
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any?] = [
            "Authorization": authorization,
            "Accept": accept,
            "Lang": lang,
            "warehouse": Constant.shared.SELECTED_WAREHOUSE_ID > 0 ? "\(Constant.shared.SELECTED_WAREHOUSE_ID)" : nil
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)
        
        debugPrint("headerParameters ==>> ",headerParameters)
        debugPrint("Final Full URL: \((url?.string ?? URLString))")
        
        let requestBuilder: RequestBuilder<NewSubCategoryModel>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()
        
        return requestBuilder.init(method: "GET", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    
    
    open class func NewProductListingWithRequestBuilder(authorization: String, accept: String, lang: String, iProductType: Int? = nil, iCategoryId: Int? = nil, vSerchString: String? = nil, offset: Int? = nil, pageNo: Int? = nil, isOnlyBundle: Int? = 0) -> RequestBuilder<NewProductListResponse> {
        let path = "/products"
        let URLString = SwaggerClientAPI.baseMarketUrl + path
        debugPrint("URLString ==>> ",URLString)
        let formParams: [String:Any?] = [
            "sub_category_id": (iCategoryId != nil && iCategoryId != 0) ? iCategoryId!.encodeToJSON() : nil,
//            "onlyBunddel": isOnlyBundle == 0 ? "0".encodeToJSON() : "1".encodeToJSON(),
            "search_product_name": vSerchString,
//            "page": pageNo?.encodeToJSON()
        ]
        debugPrint("formParams ==>> ",formParams)
        let nonNullParameters = APIHelper.rejectNil(formParams)
        let parameters = APIHelper.convertBoolToString(nonNullParameters)
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any?] = [
            "Authorization": authorization,
            "Accept": accept,
            "Lang": lang,
            "warehouse": Constant.shared.SELECTED_WAREHOUSE_ID > 0 ? "\(Constant.shared.SELECTED_WAREHOUSE_ID)" : nil
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)
        
        debugPrint("headerParameters ==>> ",headerParameters)
        debugPrint("Final Full URL: \((url?.string ?? URLString))")
        
        let requestBuilder: RequestBuilder<NewProductListResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()
        
        return requestBuilder.init(method: "GET", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    
    open class func NewViewProductListingWithRequestBuilder(authorization: String, accept: String, lang: String, msi_id: Int? = nil, banner_id: Int? = nil) -> RequestBuilder<NewProductListResponse> {
        let path = "/market-new-products"
        let URLString = SwaggerClientAPI.baseMarketUrl + path
        debugPrint("URLString ==>> ",URLString)
        let formParams: [String:Any?] = [
            "banner_id": (banner_id != nil && banner_id != 0) ? banner_id!.encodeToJSON() : nil,
            "msi_id": (msi_id != nil && msi_id != 0) ? msi_id!.encodeToJSON() : nil,
        ]
        debugPrint("formParams ==>> ",formParams)
        let nonNullParameters = APIHelper.rejectNil(formParams)
        let parameters = APIHelper.convertBoolToString(nonNullParameters)
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any?] = [
            "Authorization": authorization,
            "Accept": accept,
            "Lang": lang,
            "warehouse": Constant.shared.SELECTED_WAREHOUSE_ID > 0 ? "\(Constant.shared.SELECTED_WAREHOUSE_ID)" : nil
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)
        
        debugPrint("headerParameters ==>> ",headerParameters)
        debugPrint("Final Full URL: \((url?.string ?? URLString))")
        
        let requestBuilder: RequestBuilder<NewProductListResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()
        
        return requestBuilder.init(method: "GET", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    
    open class func productSearchListingWithRequestBuilder(authorization: String, accept: String, lang: String, iProductType: Int? = nil, iCategoryId: Int? = nil, vSerchString: String? = nil, offset: Int? = nil, pageNo: Int? = nil, isOnlyBundle: Int? = 0) -> RequestBuilder<ProductListResponse> {
        let path = "/product/product-listing?offset=\(pageNo?.string ?? "1")"
        let URLString = SwaggerClientAPI.basePath + path
        let formParams: [String:Any?] = [
            "vSerchString": vSerchString
        ]
        debugPrint("formParams ==>> ",formParams)
        let nonNullParameters = APIHelper.rejectNil(formParams)
        let parameters = APIHelper.convertBoolToString(nonNullParameters)
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any?] = [
            "Authorization": authorization,
            "Accept": accept,
            "Lang": lang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)
        
        let requestBuilder: RequestBuilder<ProductListResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()
        
        return requestBuilder.init(method: "POST", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    /**
     product suggestion
     
     - parameter authorization: (header)
     - parameter accept: (header)
     - parameter lang: (header)
     - parameter vProductName: (form)  (optional)
     - parameter iCategoryId: (form)  (optional)
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func productSuggestion(authorization: String, accept: String, lang: String, vProductName: String? = nil, iCategoryId: Int? = nil, completion: @escaping ((_ data: ProductSuggestionResponse?,_ error: Error?) -> Void)) {
        productSuggestionWithRequestBuilder(authorization: authorization, accept: accept, lang: lang, vProductName: vProductName, iCategoryId: iCategoryId).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }
    
    
    /**
     product suggestion
     - POST /product/product-suggestion
     -
     
     - :
     - type: http
     - name: bearerAuth
     - examples: [{contentType=application/json, example={
     "responseData" : [ {
     "vProductName" : "vProductName"
     }, {
     "vProductName" : "vProductName"
     } ],
     "responseMessage" : "responseMessage",
     "responseCode" : 0
     }}]
     - parameter authorization: (header)
     - parameter accept: (header)
     - parameter lang: (header)
     - parameter vProductName: (form)  (optional)
     - parameter iCategoryId: (form)  (optional)
     
     - returns: RequestBuilder<ProductSuggestionResponse>
     */
    open class func productSuggestionWithRequestBuilder(authorization: String, accept: String, lang: String, vProductName: String? = nil, iCategoryId: Int? = nil) -> RequestBuilder<ProductSuggestionResponse> {
        let path = "/product/product-suggestion"
        let URLString = SwaggerClientAPI.basePath + path
        let formParams: [String:Any?] = [
            "vProductName": vProductName,
            "iCategoryId": iCategoryId?.encodeToJSON()
        ]
        
        let nonNullParameters = APIHelper.rejectNil(formParams)
        let parameters = APIHelper.convertBoolToString(nonNullParameters)
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any?] = [
            "Authorization": authorization,
            "Accept": accept,
            "Lang": lang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)
        
        let requestBuilder: RequestBuilder<ProductSuggestionResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()
        
        return requestBuilder.init(method: "POST", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    /**
     Product Web Listing (on view more click)
     
     - parameter accept: (header)
     - parameter lang: (header)
     - parameter iProductType: (form)  (optional)
     - parameter iCategoryId: (form)  (optional)
     - parameter vSerchString: (form)  (optional)
     - parameter authorization: (header)  (optional, default to Bearer )
     - parameter offset: (query) offset (optional, default to 1)
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func productWebListing(accept: String, lang: String, iProductType: Int? = nil, iCategoryId: Int? = nil, vSerchString: String? = nil, authorization: String? = nil, offset: Int? = nil, completion: @escaping ((_ data: ProductListResponse?,_ error: Error?) -> Void)) {
        productWebListingWithRequestBuilder(accept: accept, lang: lang, iProductType: iProductType, iCategoryId: iCategoryId, vSerchString: vSerchString, authorization: authorization, offset: offset).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }
    
    
    /**
     Product Web Listing (on view more click)
     - POST /product/product-web-listing
     -
     
     - :
     - type: http
     - name: bearerAuth
     - examples: [{contentType=application/json, example={
     "responseData" : {
     "totalPage" : 5,
     "limit" : 6,
     "totalRecord" : 1,
     "productList" : [ {
     "tiIsFavourite" : 2,
     "vProductName" : "vProductName",
     "iProductCategoryId" : "iProductCategoryId",
     "dDiscountedProductPrice" : "dDiscountedProductPrice",
     "dbOriginalProductPrice" : "dbOriginalProductPrice",
     "vProductImage" : "vProductImage",
     "biProductId" : 2,
     "isLowQuantity" : 9,
     "dDiscountAmount" : "dDiscountAmount",
     "youMayAlsoLikeProducts" : [ null, null ],
     "iHowMuchLeftInStock" : 3,
     "maxQtyInfo" : "maxQtyInfo",
     "txProductDescription" : "txProductDescription",
     "tiDiscountType" : 7,
     "vProductSKU" : "vProductSKU",
     "vProductUnit" : "vProductUnit"
     }, {
     "tiIsFavourite" : 2,
     "vProductName" : "vProductName",
     "iProductCategoryId" : "iProductCategoryId",
     "dDiscountedProductPrice" : "dDiscountedProductPrice",
     "dbOriginalProductPrice" : "dbOriginalProductPrice",
     "vProductImage" : "vProductImage",
     "biProductId" : 2,
     "isLowQuantity" : 9,
     "dDiscountAmount" : "dDiscountAmount",
     "youMayAlsoLikeProducts" : [ null, null ],
     "iHowMuchLeftInStock" : 3,
     "maxQtyInfo" : "maxQtyInfo",
     "txProductDescription" : "txProductDescription",
     "tiDiscountType" : 7,
     "vProductSKU" : "vProductSKU",
     "vProductUnit" : "vProductUnit"
     } ]
     },
     "responseMessage" : "responseMessage",
     "responseCode" : 0
     }}]
     - parameter accept: (header)
     - parameter lang: (header)
     - parameter iProductType: (form)  (optional)
     - parameter iCategoryId: (form)  (optional)
     - parameter vSerchString: (form)  (optional)
     - parameter authorization: (header)  (optional, default to Bearer )
     - parameter offset: (query) offset (optional, default to 1)
     
     - returns: RequestBuilder<ProductListResponse>
     */
    open class func productWebListingWithRequestBuilder(accept: String, lang: String, iProductType: Int? = nil, iCategoryId: Int? = nil, vSerchString: String? = nil, authorization: String? = nil, offset: Int? = nil) -> RequestBuilder<ProductListResponse> {
        let path = "/product/product-web-listing"
        let URLString = SwaggerClientAPI.basePath + path
        let formParams: [String:Any?] = [
            "iProductType": iProductType?.encodeToJSON(),
            "iCategoryId": iCategoryId?.encodeToJSON(),
            "vSerchString": vSerchString
        ]
        
        let nonNullParameters = APIHelper.rejectNil(formParams)
        let parameters = APIHelper.convertBoolToString(nonNullParameters)
        var url = URLComponents(string: URLString)
        url?.queryItems = APIHelper.mapValuesToQueryItems([
            "offset": offset?.encodeToJSON()
        ])
        let nillableHeaders: [String: Any?] = [
            "Authorization": authorization,
            "Accept": accept,
            "Lang": lang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)
        
        let requestBuilder: RequestBuilder<ProductListResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()
        
        return requestBuilder.init(method: "POST", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    
    open class func newCategoryListing(categoryListId: String? = nil, pageNo: String? = nil, completion: @escaping ((_ data: SuperCategoryListResponseData?,_ error: Error?) -> Void)) {
        newCategoryListingWithRequestBuilder(categoryListId: categoryListId, pageNo: pageNo).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }
    
    open class func newCategoryListingWithRequestBuilder(categoryListId: String? = nil, pageNo: String? = nil) -> RequestBuilder<SuperCategoryListResponseData> {
        let path = "/product/newCategoryListing"
        let URLString = SwaggerClientAPI.basePath + path
        let formParams: [String:Any?] = [
            "CategoryListId": categoryListId?.encodeToJSON() ?? "1",
            "page": pageNo?.encodeToJSON() ?? "1"
        ]
        
        let nonNullParameters = APIHelper.rejectNil(formParams)
        let parameters = APIHelper.convertBoolToString(nonNullParameters)
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any?] = [
            "Authorization": getAuthorizationText(),
            "Accept": "application/json",
            "Lang": CurrentAppLang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)
        
        let requestBuilder: RequestBuilder<SuperCategoryListResponseData>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()
        debugPrint("product/newCategoryListing parameters =>",parameters)
        return requestBuilder.init(method: "GET", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    
}
