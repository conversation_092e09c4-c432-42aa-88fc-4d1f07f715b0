//
// UserWalletAPI.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation
import Alamofire


open class UserWalletAPI {
    /**
     This API is used to add money to wallet.

     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter vTransactionRef: (form)  (optional)
     - parameter vAmount: (form)  (optional)
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func addMoneyToWallet(authorization: String, accept: String, lang: String, vTransactionRef: String? = nil, vAmount: String? = nil, completion: @escaping ((_ data: TransactionResponse?,_ error: Error?) -> Void)) {
        addMoneyToWalletWithRequestBuilder(authorization: authorization, accept: accept, lang: lang, vTransactionRef: vTransactionRef, vAmount: vAmount).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }


    /**
     This API is used to add money to wallet.
     - POST /user/add-money-to-wallet
     - 

     - :
       - type: http
       - name: bearerAuth
     - examples: [{contentType=application/json, example={
  "responseData" : {
    "latestWalletBalance" : "latestWalletBalance"
  },
  "responseMessage" : "responseMessage",
  "responseCode" : 0
}}]
     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter vTransactionRef: (form)  (optional)
     - parameter vAmount: (form)  (optional)

     - returns: RequestBuilder<TransactionResponse> 
     */
    open class func addMoneyToWalletWithRequestBuilder(authorization: String, accept: String, lang: String, vTransactionRef: String? = nil, vAmount: String? = nil) -> RequestBuilder<TransactionResponse> {
        let path = "/user/add-money-to-wallet"
        let URLString = SwaggerClientAPI.basePath + path
        let formParams: [String:Any?] = [
                "vTransactionRef": vTransactionRef,
                "vAmount": vAmount
        ]

        let nonNullParameters = APIHelper.rejectNil(formParams)
        let parameters = APIHelper.convertBoolToString(nonNullParameters)
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any?] = [
                        "Authorization": authorization,
                        "Accept": accept,
                        "Lang": lang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)

        let requestBuilder: RequestBuilder<TransactionResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()

        return requestBuilder.init(method: "POST", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    /**
     This API is used to fetch wallet details.

     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter offset: (query) offset (optional, default to 1)
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func getWalletDetails(authorization: String, accept: String, lang: String, offset: Int? = nil, completion: @escaping ((_ data: WalletDetailsResponse?,_ error: Error?) -> Void)) {
        getWalletDetailsWithRequestBuilder(authorization: authorization, accept: accept, lang: lang, offset: offset).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }


    /**
     This API is used to fetch wallet details.
     - GET /user/get-wallet-details
     - 

     - :
       - type: http
       - name: bearerAuth
     - examples: [{contentType=application/json, example={
  "responseData" : {
    "totalAmount" : "totalAmount",
    "adminCommisionInPerc" : "adminCommisionInPerc",
    "totalPage" : 5,
    "limit" : 6,
    "details" : [ {
      "tiAction" : "tiAction",
      "vAmount" : "vAmount",
      "tsAddedAt" : "tsAddedAt",
      "tsExpiryAt" : "tsExpiryAt",
      "iWalletId" : 5
    }, {
      "tiAction" : "tiAction",
      "vAmount" : "vAmount",
      "tsAddedAt" : "tsAddedAt",
      "tsExpiryAt" : "tsExpiryAt",
      "iWalletId" : 5
    } ],
    "totalRecord" : 1
  },
  "responseMessage" : "responseMessage",
  "responseCode" : 0
}}]
     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter offset: (query) offset (optional, default to 1)

     - returns: RequestBuilder<WalletDetailsResponse> 
     */
    open class func getWalletDetailsWithRequestBuilder(authorization: String, accept: String, lang: String, offset: Int? = nil) -> RequestBuilder<WalletDetailsResponse> {
        let path = "/user/get-wallet-details"
        let URLString = SwaggerClientAPI.basePath + path
        let parameters: [String:Any]? = nil
        var url = URLComponents(string: URLString)
        url?.queryItems = APIHelper.mapValuesToQueryItems([
                        "offset": offset?.encodeToJSON()
        ])
        let nillableHeaders: [String: Any?] = [
                        "Authorization": authorization,
                        "Accept": accept,
                        "Lang": lang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)

        let requestBuilder: RequestBuilder<WalletDetailsResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()

        return requestBuilder.init(method: "GET", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
}
