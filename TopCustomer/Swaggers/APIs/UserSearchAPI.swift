//
//  UserSearchAPI.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 10/09/2023.
//  Copyright © 2023 SOTSYS203. All rights reserved.
//

import Foundation
import Alamofire

open class UserSearchAPI {
    
    open class func getUserSearch(completion: @escaping ((_ data: UserSearchResponse?,_ error: Error?) -> Void)) {
        getUserSearchWithRequestBuilder().execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }
    
    open class func getUserSearchWithRequestBuilder() -> RequestBuilder<UserSearchResponse> {
        let path = "/user/get-user-search"
        let URLString = SwaggerClientAPI.basePath + path
        let formParams: [String: Any] = [
            "iUserId": User.shared.iUserId?.encodeToJSON() ?? 0
        ]
        let nonNullParameters = APIHelper.rejectNil(formParams)
        let parameters = APIHelper.convertBoolToString(nonNullParameters)
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any] = [
            "Authorization": getAuthorizationText(),
            "Accept": "application/json",
            "Lang": CurrentAppLang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)
        let requestBuilder: RequestBuilder<UserSearchResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()
        return requestBuilder.init(method: "POST", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    
    open class func addUserSearch(searchtext: String, completion: @escaping ((_ data: BaseResponseModel?,_ error: Error?) -> Void)) {
        addUserSearchWithRequestBuilder(searchtext: searchtext).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }
    
    
    open class func addUserSearchWithRequestBuilder(searchtext: String) -> RequestBuilder<BaseResponseModel> {
        let path = "/user/add-user-search"
        let URLString = SwaggerClientAPI.basePath + path
        let parameters = ["iUserId": User.shared.iUserId?.encodeToJSON() ?? 0,
                          "searchtext": searchtext.encodeToJSON()] as [String : Any]
        let url = URLComponents(string: URLString)
        
        let nillableHeaders: [String: Any?] = [
            "Authorization": getAuthorizationText(),
            "Accept": "application/json",
            "Lang": CurrentAppLang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)
        let requestBuilder: RequestBuilder<BaseResponseModel>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()
        return requestBuilder.init(method: "POST", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    
    open class func deleteOneUserSearch(searchId: Int, completion: @escaping ((_ data: BaseResponseModel?,_ error: Error?) -> Void)) {
        deleteOneUserSearchWithRequestBuilder(searchId: searchId).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }
    
    open class func deleteOneUserSearchWithRequestBuilder(searchId: Int) -> RequestBuilder<BaseResponseModel> {
        let path = "/user/delete-one-search"
        
        let URLString = SwaggerClientAPI.basePath + path
        let parameters = ["SearchId": searchId.encodeToJSON()]
        let url = URLComponents(string: URLString)
        
        let nillableHeaders: [String: Any?] = [
            "Authorization": getAuthorizationText(),
            "Accept": "application/json",
            "Lang": CurrentAppLang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)
        
        let requestBuilder: RequestBuilder<BaseResponseModel>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()
        
        return requestBuilder.init(method: "POST", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    
    open class func deleteAllUserSearch(completion: @escaping ((_ data: BaseResponseModel?,_ error: Error?) -> Void)) {
        deleteAllUserSearchWithRequestBuilder().execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }
    
    open class func deleteAllUserSearchWithRequestBuilder() -> RequestBuilder<BaseResponseModel> {
        var path = "/user/delete-user-search"
        
        let URLString = SwaggerClientAPI.basePath + path
        let parameters = ["iUserId": User.shared.iUserId?.encodeToJSON() ?? 0]
        let url = URLComponents(string: URLString)
        
        let nillableHeaders: [String: Any?] = [
            "Authorization": getAuthorizationText(),
            "Accept": "application/json",
            "Lang": CurrentAppLang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)
        let requestBuilder: RequestBuilder<BaseResponseModel>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()
        return requestBuilder.init(method: "POST", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    
    open class func getQrCode(completion: @escaping ((_ data: UserQRCodeResponse?,_ error: Error?) -> Void)) {
        getQrCodehWithRequestBuilder().execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }
    
    open class func getQrCodehWithRequestBuilder() -> RequestBuilder<UserQRCodeResponse> {
        var path = "/user/getQrCode"
        let URLString = SwaggerClientAPI.basePath + path
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any?] = [
            "Authorization": getAuthorizationText(),
            "Accept": "application/json",
            "Lang": CurrentAppLang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)
        let requestBuilder: RequestBuilder<UserQRCodeResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()
        return requestBuilder.init(method: "GET", URLString: (url?.string ?? URLString), parameters: nil, isBody: false, headers: headerParameters)
    }
}
