//
// UserAPI.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation
import Alamofire


open class UserAPI {
    /**
     Add user address

     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter iTypeId: (form)  (optional)
     - parameter txAddress: (form)  (optional)
     - parameter vTypeTitle: (form)  (optional)
     - parameter vZipCode: (form)  (optional)
     - parameter txAptSuite: (form)  (optional)
     - parameter dLatitude: (form)  (optional)
     - parameter dLongitude: (form)  (optional)
     - parameter vCity: (form)  (optional)
     - parameter vState: (form)  (optional)
     - parameter vCountryCode: (form)  (optional)
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func addAddress(authorization: String, accept: String, lang: String, iTypeId: Int? = nil, txAddress: String? = nil, vTypeTitle: String? = nil, vZipCode: String? = nil, txAptSuite: String? = nil, dLatitude: String? = nil, dLongitude: String? = nil, vCity: String? = nil, vState: String? = nil, vCountryCode: String? = nil, vIsMosques: String? = nil, completion: @escaping ((_ data: AddressResponse?,_ error: Error?) -> Void)) {
        addAddressWithRequestBuilder(authorization: authorization, accept: accept, lang: lang, iTypeId: iTypeId, txAddress: txAddress, vTypeTitle: vTypeTitle, vZipCode: vZipCode, txAptSuite: txAptSuite, dLatitude: dLatitude, dLongitude: dLongitude, vCity: vCity, vState: vState, vCountryCode: vCountryCode, vIsMosques: vIsMosques).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }


    /**
     Add user address
     - POST /user/add-address
     - 

     - :
       - type: http
       - name: bearerAuth
     - examples: [{contentType=application/json, example={
  "responseData" : {
    "iUserId" : 1,
    "dLatitude" : "dLatitude",
    "vType" : "vType",
    "iAddressId" : 6,
    "vState" : "vState",
    "dLongitude" : "dLongitude",
    "vZipCode" : "vZipCode",
    "txAptSuite" : "txAptSuite",
    "vCountryCode" : "vCountryCode",
    "txAddress" : "txAddress",
    "vCity" : "vCity"
  },
  "responseMessage" : "responseMessage",
  "responseCode" : 0
}}]
     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter iTypeId: (form)  (optional)
     - parameter txAddress: (form)  (optional)
     - parameter vTypeTitle: (form)  (optional)
     - parameter vZipCode: (form)  (optional)
     - parameter txAptSuite: (form)  (optional)
     - parameter dLatitude: (form)  (optional)
     - parameter dLongitude: (form)  (optional)
     - parameter vCity: (form)  (optional)
     - parameter vState: (form)  (optional)
     - parameter vCountryCode: (form)  (optional)

     - returns: RequestBuilder<AddressResponse> 
     */
    open class func addAddressWithRequestBuilder(authorization: String, accept: String, lang: String, iTypeId: Int? = nil, txAddress: String? = nil, vTypeTitle: String? = nil, vZipCode: String? = nil, txAptSuite: String? = nil, dLatitude: String? = nil, dLongitude: String? = nil, vCity: String? = nil, vState: String? = nil, vCountryCode: String? = nil, vIsMosques: String? = nil) -> RequestBuilder<AddressResponse> {
        let path = "/user/add-address"
        let URLString = SwaggerClientAPI.basePath + path
        let formParams: [String:Any?] = [
                "iTypeId": iTypeId?.encodeToJSON(),
                "txAddress": txAddress,
                "vTypeTitle": vTypeTitle,
                "vZipCode": vZipCode,
                "txAptSuite": txAptSuite,
                "dLatitude": dLatitude,
                "dLongitude": dLongitude,
                "vCity": vCity,
                "vState": vState,
                "vCountryCode": vCountryCode,
                "isMosques": vIsMosques
        ]

        let nonNullParameters = APIHelper.rejectNil(formParams)
        let parameters = APIHelper.convertBoolToString(nonNullParameters)
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any?] = [
                        "Authorization": authorization,
                        "Accept": accept,
                        "Lang": lang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)

        let requestBuilder: RequestBuilder<AddressResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()

        return requestBuilder.init(method: "POST", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    /**
     Change Password

     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter vCurrentPassword: (form)  (optional)
     - parameter vNewPassword: (form)  (optional)
     - parameter vConfirmPassword: (form)  (optional)
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func changePassword(authorization: String, accept: String, lang: String, vCurrentPassword: String? = nil, vNewPassword: String? = nil, vConfirmPassword: String? = nil, completion: @escaping ((_ data: CommonFields?,_ error: Error?) -> Void)) {
        changePasswordWithRequestBuilder(authorization: authorization, accept: accept, lang: lang, vCurrentPassword: vCurrentPassword, vNewPassword: vNewPassword, vConfirmPassword: vConfirmPassword).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }


    /**
     Change Password
     - POST /user/change-password
     - 

     - :
       - type: http
       - name: bearerAuth
     - examples: [{contentType=application/json, example={
  "responseMessage" : "responseMessage",
  "responseCode" : 0
}}]
     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter vCurrentPassword: (form)  (optional)
     - parameter vNewPassword: (form)  (optional)
     - parameter vConfirmPassword: (form)  (optional)

     - returns: RequestBuilder<CommonFields> 
     */
    open class func changePasswordWithRequestBuilder(authorization: String, accept: String, lang: String, vCurrentPassword: String? = nil, vNewPassword: String? = nil, vConfirmPassword: String? = nil) -> RequestBuilder<CommonFields> {
        let path = "/user/change-password"
        let URLString = SwaggerClientAPI.basePath + path
        let formParams: [String:Any?] = [
                "vCurrentPassword": vCurrentPassword,
                "vNewPassword": vNewPassword,
                "vConfirmPassword": vConfirmPassword
        ]

        let nonNullParameters = APIHelper.rejectNil(formParams)
        let parameters = APIHelper.convertBoolToString(nonNullParameters)
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any?] = [
                        "Authorization": authorization,
                        "Accept": accept,
                        "Lang": lang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)

        let requestBuilder: RequestBuilder<CommonFields>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()

        return requestBuilder.init(method: "POST", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    /**
     Delete User Account

     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func deleteAccount(authorization: String, accept: String, lang: String, completion: @escaping ((_ data: CommonFields?,_ error: Error?) -> Void)) {
        deleteAccountWithRequestBuilder(authorization: authorization, accept: accept, lang: lang).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }


    /**
     Delete User Account
     - DELETE /user/delete-account
     - 

     - :
       - type: http
       - name: bearerAuth
     - examples: [{contentType=application/json, example={
  "responseMessage" : "responseMessage",
  "responseCode" : 0
}}]
     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  

     - returns: RequestBuilder<CommonFields> 
     */
    open class func deleteAccountWithRequestBuilder(authorization: String, accept: String, lang: String) -> RequestBuilder<CommonFields> {
        let path = "/user/delete-account"
        let URLString = SwaggerClientAPI.basePath + path
        let parameters: [String:Any]? = nil
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any?] = [
                        "Authorization": authorization,
                        "Accept": accept,
                        "Lang": lang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)

        let requestBuilder: RequestBuilder<CommonFields>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()

        return requestBuilder.init(method: "DELETE", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    /**
     This API is use to delete user address

     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter iAddressId: (path)  
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func deleteUserAddress(authorization: String, accept: String, lang: String, iAddressId: Int, completion: @escaping ((_ data: CommonFields?,_ error: Error?) -> Void)) {
        deleteUserAddressWithRequestBuilder(authorization: authorization, accept: accept, lang: lang, iAddressId: iAddressId).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }


    /**
     This API is use to delete user address
     - DELETE /user/delete-user-address/{iAddressId}
     - 

     - :
       - type: http
       - name: bearerAuth
     - examples: [{contentType=application/json, example={
  "responseMessage" : "responseMessage",
  "responseCode" : 0
}}]
     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter iAddressId: (path)  

     - returns: RequestBuilder<CommonFields> 
     */
    open class func deleteUserAddressWithRequestBuilder(authorization: String, accept: String, lang: String, iAddressId: Int) -> RequestBuilder<CommonFields> {
        var path = "/user/delete-user-address/{iAddressId}"
        let iAddressIdPreEscape = "\(iAddressId)"
        let iAddressIdPostEscape = iAddressIdPreEscape.addingPercentEncoding(withAllowedCharacters: .urlPathAllowed) ?? ""
        path = path.replacingOccurrences(of: "{iAddressId}", with: iAddressIdPostEscape, options: .literal, range: nil)
        let URLString = SwaggerClientAPI.basePath + path
        let parameters: [String:Any]? = nil
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any?] = [
                        "Authorization": authorization,
                        "Accept": accept,
                        "Lang": lang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)

        let requestBuilder: RequestBuilder<CommonFields>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()

        return requestBuilder.init(method: "DELETE", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    /**
     * enum for parameter tiGender
     */
    public enum TiGender_editProfile: Int { 
        case _1 = 1
        case _2 = 2
        case _3 = 3
    }

    /**
     Edit user profile

     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter vName: (form)  (optional)
     - parameter vISDCode: (form)  (optional)
     - parameter vMobileNumber: (form)  (optional)
     - parameter vEmailId: (form)  (optional)
     - parameter iDob: (form)  (optional)
     - parameter tiGender: (form)  (optional)
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func editProfile(authorization: String, accept: String, lang: String, vName: String? = nil, vISDCode: String? = nil, vMobileNumber: String? = nil, vEmailId: String? = nil, iDob: String? = nil, tiGender: TiGender_editProfile? = nil, completion: @escaping ((_ data: UserResponse?,_ error: Error?) -> Void)) {
        editProfileWithRequestBuilder(authorization: authorization, accept: accept, lang: lang, vName: vName, vISDCode: vISDCode, vMobileNumber: vMobileNumber, vEmailId: vEmailId, iDob: iDob, tiGender: tiGender).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }


    /**
     Edit user profile
     - POST /user/profile
     - 

     - :
       - type: http
       - name: bearerAuth
     - examples: [{contentType=application/json, example={
  "responseData" : {
    "vEmailId" : "vEmailId",
    "vName" : "vName",
    "vISDCode" : "vISDCode",
    "vNewISDCode" : "vNewISDCode",
    "vNewMobileNumber" : "vNewMobileNumber",
    "tiIsMobileVerified" : 1,
    "iUserId" : 6,
    "tiIsSocialLogin" : 5,
    "vLanguage" : "vLanguage",
    "tiGender" : 5,
    "vReferalPlatform" : "vReferalPlatform",
    "vAccessToken" : "vAccessToken",
    "iDob" : "iDob",
    "vMobileNumber" : "vMobileNumber"
  },
  "responseMessage" : "responseMessage",
  "responseCode" : 0
}}]
     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter vName: (form)  (optional)
     - parameter vISDCode: (form)  (optional)
     - parameter vMobileNumber: (form)  (optional)
     - parameter vEmailId: (form)  (optional)
     - parameter iDob: (form)  (optional)
     - parameter tiGender: (form)  (optional)

     - returns: RequestBuilder<UserResponse> 
     */
    open class func editProfileWithRequestBuilder(authorization: String, accept: String, lang: String, vName: String? = nil, vISDCode: String? = nil, vMobileNumber: String? = nil, vEmailId: String? = nil, iDob: String? = nil, tiGender: TiGender_editProfile? = nil) -> RequestBuilder<UserResponse> {
        let path = "/user/profile"
        let URLString = SwaggerClientAPI.basePath + path
        let formParams: [String:Any?] = [
                "vName": vName,
                "vISDCode": vISDCode,
                "vMobileNumber": vMobileNumber,
                "vEmailId": vEmailId,
                "iDob": iDob,
                "tiGender": tiGender?.rawValue
        ]

        let nonNullParameters = APIHelper.rejectNil(formParams)
        let parameters = APIHelper.convertBoolToString(nonNullParameters)
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any?] = [
                        "Authorization": authorization,
                        "Accept": accept,
                        "Lang": lang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)

        let requestBuilder: RequestBuilder<UserResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()

        return requestBuilder.init(method: "POST", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    /**
     User profile

     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func getProfile(authorization: String, accept: String, lang: String, completion: @escaping ((_ data: UserResponse?,_ error: Error?) -> Void)) {
        getProfileWithRequestBuilder(authorization: authorization, accept: accept, lang: lang).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }


    /**
     User profile
     - GET /user/profile
     - 

     - :
       - type: http
       - name: bearerAuth
     - examples: [{contentType=application/json, example={
  "responseData" : {
    "vEmailId" : "vEmailId",
    "vName" : "vName",
    "vISDCode" : "vISDCode",
    "vNewISDCode" : "vNewISDCode",
    "vNewMobileNumber" : "vNewMobileNumber",
    "tiIsMobileVerified" : 1,
    "iUserId" : 6,
    "tiIsSocialLogin" : 5,
    "vLanguage" : "vLanguage",
    "tiGender" : 5,
    "vReferalPlatform" : "vReferalPlatform",
    "vAccessToken" : "vAccessToken",
    "iDob" : "iDob",
    "vMobileNumber" : "vMobileNumber"
  },
  "responseMessage" : "responseMessage",
  "responseCode" : 0
}}]
     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  

     - returns: RequestBuilder<UserResponse> 
     */
    open class func getProfileWithRequestBuilder(authorization: String, accept: String, lang: String) -> RequestBuilder<UserResponse> {
        let path = "/user/profile"
        let URLString = SwaggerClientAPI.basePath + path
        let parameters: [String:Any]? = nil
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any?] = [
                        "Authorization": authorization,
                        "Accept": accept,
                        "Lang": lang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)

        let requestBuilder: RequestBuilder<UserResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()

        return requestBuilder.init(method: "GET", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    /**
     Logout User

     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter vDeviceToken: (path)  
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func logout(authorization: String, accept: String, lang: String, vDeviceToken: String, completion: @escaping ((_ data: CommonFields?,_ error: Error?) -> Void)) {
        logoutWithRequestBuilder(authorization: authorization, accept: accept, lang: lang, vDeviceToken: vDeviceToken).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }


    /**
     Logout User
     - GET /user/logout/{vDeviceToken}
     - 

     - :
       - type: http
       - name: bearerAuth
     - examples: [{contentType=application/json, example={
  "responseMessage" : "responseMessage",
  "responseCode" : 0
}}]
     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter vDeviceToken: (path)  

     - returns: RequestBuilder<CommonFields> 
     */
    open class func logoutWithRequestBuilder(authorization: String, accept: String, lang: String, vDeviceToken: String) -> RequestBuilder<CommonFields> {
        var path = "/user/logout/{vDeviceToken}"
        let vDeviceTokenPreEscape = "\(vDeviceToken)"
        let vDeviceTokenPostEscape = vDeviceTokenPreEscape.addingPercentEncoding(withAllowedCharacters: .urlPathAllowed) ?? ""
        path = path.replacingOccurrences(of: "{vDeviceToken}", with: vDeviceTokenPostEscape, options: .literal, range: nil)
        let URLString = SwaggerClientAPI.basePath + path
        let parameters: [String:Any]? = nil
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any?] = [
                        "Authorization": authorization,
                        "Accept": accept,
                        "Lang": lang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)

        let requestBuilder: RequestBuilder<CommonFields>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()

        return requestBuilder.init(method: "GET", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    /**
     Update Device Token

     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter oldToken: (form)  (optional)
     - parameter newToken: (form)  (optional)
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func updateDeviceToken(authorization: String, accept: String, lang: String, oldToken: String? = nil, newToken: String? = nil, completion: @escaping ((_ data: CommonFields?,_ error: Error?) -> Void)) {
        updateDeviceTokenWithRequestBuilder(authorization: authorization, accept: accept, lang: lang, oldToken: oldToken, newToken: newToken).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }


    /**
     Update Device Token
     - POST /user/update-device-token
     - 

     - :
       - type: http
       - name: bearerAuth
     - examples: [{contentType=application/json, example={
  "responseMessage" : "responseMessage",
  "responseCode" : 0
}}]
     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter oldToken: (form)  (optional)
     - parameter newToken: (form)  (optional)

     - returns: RequestBuilder<CommonFields> 
     */
    open class func updateDeviceTokenWithRequestBuilder(authorization: String, accept: String, lang: String, oldToken: String? = nil, newToken: String? = nil) -> RequestBuilder<CommonFields> {
        let path = "/user/update-device-token"
        let URLString = SwaggerClientAPI.basePath + path
        let formParams: [String:Any?] = [
                "oldToken": oldToken,
                "newToken": newToken
        ]

        let nonNullParameters = APIHelper.rejectNil(formParams)
        let parameters = APIHelper.convertBoolToString(nonNullParameters)
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any?] = [
                        "Authorization": authorization,
                        "Accept": accept,
                        "Lang": lang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)

        let requestBuilder: RequestBuilder<CommonFields>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()

        return requestBuilder.init(method: "POST", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    /**
     User Addresses

     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func userAddresses(authorization: String, accept: String, lang: String, completion: @escaping ((_ data: AddressListResponse?,_ error: Error?) -> Void)) {
        userAddressesWithRequestBuilder(authorization: authorization, accept: accept, lang: lang).execute { (response, error) -> Void in
            print(response.debugDescription.string)
            completion(response?.body, error)
        }
    }


    /**
     User Addresses
     - GET /user/get-addresses
     - 

     - :
       - type: http
       - name: bearerAuth
     - examples: [{contentType=application/json, example={
  "responseData" : [ {
    "iUserId" : 1,
    "dLatitude" : "dLatitude",
    "vType" : "vType",
    "iAddressId" : 6,
    "vState" : "vState",
    "dLongitude" : "dLongitude",
    "vZipCode" : "vZipCode",
    "txAptSuite" : "txAptSuite",
    "vCountryCode" : "vCountryCode",
    "txAddress" : "txAddress",
    "vCity" : "vCity"
  }, {
    "iUserId" : 1,
    "dLatitude" : "dLatitude",
    "vType" : "vType",
    "iAddressId" : 6,
    "vState" : "vState",
    "dLongitude" : "dLongitude",
    "vZipCode" : "vZipCode",
    "txAptSuite" : "txAptSuite",
    "vCountryCode" : "vCountryCode",
    "txAddress" : "txAddress",
    "vCity" : "vCity"
  } ],
  "responseMessage" : "responseMessage",
  "responseCode" : 0
}}]
     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  

     - returns: RequestBuilder<AddressListResponse> 
     */
    open class func userAddressesWithRequestBuilder(authorization: String, accept: String, lang: String) -> RequestBuilder<AddressListResponse> {
        let path = "/user/get-addresses"
        let URLString = SwaggerClientAPI.basePath + path
        let parameters: [String:Any]? = nil
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any?] = [
                        "Authorization": authorization,
                        "Accept": accept,
                        "Lang": lang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)

        let requestBuilder: RequestBuilder<AddressListResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()
        
        debugPrint(url)
        debugPrint(parameters)
        debugPrint(headerParameters)
        

        return requestBuilder.init(method: "GET", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    /**
     User Specific Details List

     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func userDetails(authorization: String, accept: String, lang: String, completion: @escaping ((_ data: UserDetailListingResponse?,_ error: Error?) -> Void)) {
        userDetailsWithRequestBuilder(authorization: authorization, accept: accept, lang: lang).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }


    /**
     User Specific Details List
     - GET /user/user-details
     - 

     - :
       - type: http
       - name: bearerAuth
     - examples: [{contentType=application/json, example={
  "responseData" : {
    "iOrderId" : "iOrderId",
    "isCurrentOrderAvailable" : 1,
    "cartProductCount" : 6,
    "tiIsRated" : 5,
    "latestWalletBalance" : "latestWalletBalance",
    "tiIsRatingSkip" : 5
  },
  "responseMessage" : "responseMessage",
  "responseCode" : 0
}}]
     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  

     - returns: RequestBuilder<UserDetailListingResponse> 
     */
    open class func userDetailsWithRequestBuilder(authorization: String, accept: String, lang: String) -> RequestBuilder<UserDetailListingResponse> {
        let path = "/user/user-details"
        let URLString = SwaggerClientAPI.basePath + path
        let parameters: [String:Any]? = nil
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any?] = [
                        "Authorization": authorization,
                        "Accept": accept,
                        "Lang": lang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)

        let requestBuilder: RequestBuilder<UserDetailListingResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()

        return requestBuilder.init(method: "GET", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    /**
     * enum for parameter vLanguage
     */
    public enum VLanguage_userLanguage: String { 
        case en = "en"
        case ar = "ar"
    }

    /**
     change user language

     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter vLanguage: (form)  (optional)
     - parameter vDeviceToken: (form)  (optional)
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func userLanguage(authorization: String, accept: String, lang: String, vLanguage: VLanguage_userLanguage? = nil, vDeviceToken: String? = nil, completion: @escaping ((_ data: UpdateLanguageResponse?,_ error: Error?) -> Void)) {
        userLanguageWithRequestBuilder(authorization: authorization, accept: accept, lang: lang, vLanguage: vLanguage, vDeviceToken: vDeviceToken).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }


    /**
     change user language
     - POST /user/user-language
     - 

     - :
       - type: http
       - name: bearerAuth
     - examples: [{contentType=application/json, example={
  "responseData" : {
    "vLanguage" : "vLanguage"
  },
  "responseMessage" : "responseMessage",
  "responseCode" : 0
}}]
     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter vLanguage: (form)  (optional)
     - parameter vDeviceToken: (form)  (optional)

     - returns: RequestBuilder<UpdateLanguageResponse> 
     */
    open class func userLanguageWithRequestBuilder(authorization: String, accept: String, lang: String, vLanguage: VLanguage_userLanguage? = nil, vDeviceToken: String? = nil) -> RequestBuilder<UpdateLanguageResponse> {
        let path = "/user/user-language"
        let URLString = SwaggerClientAPI.basePath + path
        let formParams: [String:Any?] = [
                "vLanguage": vLanguage?.rawValue,
                "vDeviceToken": vDeviceToken
        ]

        let nonNullParameters = APIHelper.rejectNil(formParams)
        let parameters = APIHelper.convertBoolToString(nonNullParameters)
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any?] = [
                        "Authorization": authorization,
                        "Accept": accept,
                        "Lang": lang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)

        let requestBuilder: RequestBuilder<UpdateLanguageResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()

        return requestBuilder.init(method: "POST", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    
    open class func getPointsHistory(completion: @escaping ((_ data: PointsHostoryResponse?,_ error: Error?) -> Void)) {
        getPointsHistoryWithRequestBuilder().execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }
    
    open class func getPointsHistoryWithRequestBuilder() -> RequestBuilder<PointsHostoryResponse> {
        let path = "/user/getPointsHistory"
        let URLString = SwaggerClientAPI.basePath + path
        let parameters: [String:Any]? = nil
        
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any] = [
            "Authorization": getAuthorizationText(),
            "Accept": "application/json",
            "Lang": CurrentAppLang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)
        let requestBuilder: RequestBuilder<PointsHostoryResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()
        return requestBuilder.init(method: "GET", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    
    open class func getPointsLevels(completion: @escaping ((_ data: PointsLevelsResponse?,_ error: Error?) -> Void)) {
        getPointsLevelsWithRequestBuilder().execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }
    
    open class func getPointsLevelsWithRequestBuilder() -> RequestBuilder<PointsLevelsResponse> {
        let path = "/user/getPointsLevels"
        let URLString = SwaggerClientAPI.basePath + path
        let parameters: [String:Any]? = nil
        
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any] = [
            "Authorization": getAuthorizationText(),
            "Accept": "application/json",
            "Lang": CurrentAppLang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)
        let requestBuilder: RequestBuilder<PointsLevelsResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()
        return requestBuilder.init(method: "GET", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    
    open class func calculateUserPointsToSAR(points: Int, completion: @escaping ((_ data: CalculateUserPointsToSARResponse?,_ error: Error?) -> Void)) {
        calculateUserPointsToSARWithRequestBuilder(points: points).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }
    
    open class func calculateUserPointsToSARWithRequestBuilder(points: Int) -> RequestBuilder<CalculateUserPointsToSARResponse> {
        let path = "/user/calculateUserPointsToSAR"
        let URLString = SwaggerClientAPI.basePath + path
        
        let formParams: [String:Any?] = [
            "points": points]
        let nonNullParameters = APIHelper.rejectNil(formParams)
        let parameters = APIHelper.convertBoolToString(nonNullParameters)
        
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any] = [
            "Authorization": getAuthorizationText(),
            "Accept": "application/json",
            "Lang": CurrentAppLang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)
        let requestBuilder: RequestBuilder<CalculateUserPointsToSARResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()
        return requestBuilder.init(method: "GET", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    
    open class func calculateUserAmountToPoints(amount: Double, type: ConvertSARToPointsType, completion: @escaping ((_ data: convertSARToPointsResponse?,_ error: Error?) -> Void)) {
        calculateUserAmountToPointsWithRequestBuilder(amount: amount, type: type).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }
    
    open class func calculateUserAmountToPointsWithRequestBuilder(amount: Double, type: ConvertSARToPointsType) -> RequestBuilder<convertSARToPointsResponse> {
        let path = "/user/convertSARToPoints"
        let URLString = SwaggerClientAPI.basePath + path
        
        let formParams: [String:Any?] = [
            "money_amount": amount,
            "type": type.rawValue.encodeToJSON()]
        let nonNullParameters = APIHelper.rejectNil(formParams)
        let parameters = APIHelper.convertBoolToString(nonNullParameters)
        
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any] = [
            "Authorization": getAuthorizationText(),
            "Accept": "application/json",
            "Lang": CurrentAppLang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)
        let requestBuilder: RequestBuilder<convertSARToPointsResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()
        return requestBuilder.init(method: "GET", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    
    open class func getPointsDescription(completion: @escaping ((_ data: GetPointsDescriptionResponse?,_ error: Error?) -> Void)) {
        getPointsDescriptionWithRequestBuilder().execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }
    
    open class func getPointsDescriptionWithRequestBuilder() -> RequestBuilder<GetPointsDescriptionResponse> {
        let path = "/user/getPointsDescription"
        let URLString = SwaggerClientAPI.basePath + path
        
        let formParams: [String:Any?] = [:]
        let nonNullParameters = APIHelper.rejectNil(formParams)
        let parameters = APIHelper.convertBoolToString(nonNullParameters)
        
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any] = [
            "Authorization": getAuthorizationText(),
            "Accept": "application/json",
            "Lang": CurrentAppLang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)
        let requestBuilder: RequestBuilder<GetPointsDescriptionResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()
        return requestBuilder.init(method: "GET", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    
}
