//
// UserCardsAPI.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation
import Alamofire


open class UserCardsAPI {
    /**
     This API is used to save user cards

     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter vToken: (form)  (optional)
     - parameter vTransactionId: (form)  (optional)
     - parameter vPaymentDescription: (form)  (optional)
     - parameter vCardType: (form)  (optional)
     - parameter vCardScheme: (form)  (optional)
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func addUserCards(authorization: String, accept: String, lang: String, vToken: String? = nil, vTransactionId: String? = nil, vPaymentDescription: String? = nil, vCardType: String? = nil, vCardScheme: String? = nil, completion: @escaping ((_ data: CommonFields?,_ error: Error?) -> Void)) {
        addUserCardsWithRequestBuilder(authorization: authorization, accept: accept, lang: lang, vToken: vToken, vTransactionId: vTransactionId, vPaymentDescription: vPaymentDescription, vCardType: vCardType, vCardScheme: vCardScheme).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }


    /**
     This API is used to save user cards
     - POST /user/add-user-cards
     - 

     - :
       - type: http
       - name: bearerAuth
     - examples: [{contentType=application/json, example={
  "responseMessage" : "responseMessage",
  "responseCode" : 0
}}]
     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter vToken: (form)  (optional)
     - parameter vTransactionId: (form)  (optional)
     - parameter vPaymentDescription: (form)  (optional)
     - parameter vCardType: (form)  (optional)
     - parameter vCardScheme: (form)  (optional)

     - returns: RequestBuilder<CommonFields> 
     */
    open class func addUserCardsWithRequestBuilder(authorization: String, accept: String, lang: String, vToken: String? = nil, vTransactionId: String? = nil, vPaymentDescription: String? = nil, vCardType: String? = nil, vCardScheme: String? = nil) -> RequestBuilder<CommonFields> {
        let path = "/user/add-user-cards"
        let URLString = SwaggerClientAPI.basePath + path
        let formParams: [String:Any?] = [
                "vToken": vToken,
                "vTransactionId": vTransactionId,
                "vPaymentDescription": vPaymentDescription,
                "vCardType": vCardType,
                "vCardScheme": vCardScheme
        ]

        let nonNullParameters = APIHelper.rejectNil(formParams)
        let parameters = APIHelper.convertBoolToString(nonNullParameters)
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any?] = [
                        "Authorization": authorization,
                        "Accept": accept,
                        "Lang": lang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)

        let requestBuilder: RequestBuilder<CommonFields>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()

        return requestBuilder.init(method: "POST", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    /**
     This API is use to delete user card

     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter iCardId: (path)  
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func deleteCard(authorization: String, accept: String, lang: String, iCardId: Int, completion: @escaping ((_ data: CommonFields?,_ error: Error?) -> Void)) {
        deleteCardWithRequestBuilder(authorization: authorization, accept: accept, lang: lang, iCardId: iCardId).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }


    /**
     This API is use to delete user card
     - DELETE /user/delete-card/{iCardId}
     - 

     - :
       - type: http
       - name: bearerAuth
     - examples: [{contentType=application/json, example={
  "responseMessage" : "responseMessage",
  "responseCode" : 0
}}]
     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter iCardId: (path)  

     - returns: RequestBuilder<CommonFields> 
     */
    open class func deleteCardWithRequestBuilder(authorization: String, accept: String, lang: String, iCardId: Int) -> RequestBuilder<CommonFields> {
        var path = "/user/delete-card/{iCardId}"
        let iCardIdPreEscape = "\(iCardId)"
        let iCardIdPostEscape = iCardIdPreEscape.addingPercentEncoding(withAllowedCharacters: .urlPathAllowed) ?? ""
        path = path.replacingOccurrences(of: "{iCardId}", with: iCardIdPostEscape, options: .literal, range: nil)
        let URLString = SwaggerClientAPI.basePath + path
        let parameters: [String:Any]? = nil
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any?] = [
                        "Authorization": authorization,
                        "Accept": accept,
                        "Lang": lang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)

        let requestBuilder: RequestBuilder<CommonFields>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()

        return requestBuilder.init(method: "DELETE", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    /**
     This API is use to view user cards

     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func viewUserCards(authorization: String, accept: String, lang: String, completion: @escaping ((_ data: CardResponse?,_ error: Error?) -> Void)) {
        viewUserCardsWithRequestBuilder(authorization: authorization, accept: accept, lang: lang).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }


    /**
     This API is use to view user cards
     - GET /user/view-user-cards
     - 

     - :
       - type: http
       - name: bearerAuth
     - examples: [{contentType=application/json, example={
  "responseData" : {
    "user_card" : [ {
      "vTransactionId" : "vTransactionId",
      "vCardScheme" : "vCardScheme",
      "vCardType" : "vCardType",
      "vPaymentDescription" : "vPaymentDescription",
      "iCardId" : 6,
      "vToken" : "vToken"
    }, {
      "vTransactionId" : "vTransactionId",
      "vCardScheme" : "vCardScheme",
      "vCardType" : "vCardType",
      "vPaymentDescription" : "vPaymentDescription",
      "iCardId" : 6,
      "vToken" : "vToken"
    } ],
    "latestWalletBalance" : "latestWalletBalance"
  },
  "responseMessage" : "responseMessage",
  "responseCode" : 0
}}]
     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  

     - returns: RequestBuilder<CardResponse> 
     */
    open class func viewUserCardsWithRequestBuilder(authorization: String, accept: String, lang: String) -> RequestBuilder<CardResponse> {
        let path = "/user/view-user-cards"
        let URLString = SwaggerClientAPI.basePath + path
        let parameters: [String:Any]? = nil
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any?] = [
                        "Authorization": authorization,
                        "Accept": accept,
                        "Lang": lang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)

        let requestBuilder: RequestBuilder<CardResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()

        return requestBuilder.init(method: "GET", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
}
