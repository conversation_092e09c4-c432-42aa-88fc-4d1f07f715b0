//
// APPVersionCheckAPI.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation
import Alamofire


open class APPVersionCheckAPI {
    /**
     * enum for parameter tiOsType
     */
    public enum TiOsType_checkAppVersion: Int { 
        case _1 = 1
        case _2 = 2
    }

    /**
     This API is used to check the app version. (tiUpdateType: 1 - Regular, 2 - Force Update, 3 - Maintenance)

     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter tiOsType: (form)  (optional)
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func checkAppVersion(accept: String, lang: String, tiOsType: TiOsType_checkAppVersion? = nil, completion: @escaping ((_ data: AppVersionResponse?,_ error: Error?) -> Void)) {
        checkAppVersionWithRequestBuilder(accept: accept, lang: lang, tiOsType: tiOsType).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }


    /**
     This API is used to check the app version. (tiUpdateType: 1 - Regular, 2 - Force Update, 3 - Maintenance)
     - POST /check-app-version
     - 

     - examples: [{contentType=application/json, example={
  "responseData" : {
    "vVersionNumber" : "vVersionNumber",
    "tiOsType" : 6,
    "tiUpdateType" : 1
  },
  "responseMessage" : "responseMessage",
  "responseCode" : 0
}}]
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter tiOsType: (form)  (optional)

     - returns: RequestBuilder<AppVersionResponse> 
     */
    open class func checkAppVersionWithRequestBuilder(accept: String, lang: String, tiOsType: TiOsType_checkAppVersion? = nil) -> RequestBuilder<AppVersionResponse> {
        let path = "/check-app-version"
        let URLString = SwaggerClientAPI.basePath + path
        let formParams: [String:Any?] = [
                "tiOsType": tiOsType?.rawValue
        ]

        let nonNullParameters = APIHelper.rejectNil(formParams)
        let parameters = APIHelper.convertBoolToString(nonNullParameters)
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any?] = [
                        "Accept": accept,
                        "Lang": lang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)

        let requestBuilder: RequestBuilder<AppVersionResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()

        return requestBuilder.init(method: "POST", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
}
