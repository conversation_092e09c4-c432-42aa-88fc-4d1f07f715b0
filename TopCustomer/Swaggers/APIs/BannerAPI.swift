//
// BannerAPI.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation
import Alamofire


open class BannerAPI {
    /**
     Banner Details

     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter iBannerId: (path)  
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func bannerDetails(authorization: String, accept: String, lang: String, iBannerId: Int, completion: @escaping ((_ data: BannerResponse?,_ error: Error?) -> Void)) {
        bannerDetailsWithRequestBuilder(authorization: authorization, accept: accept, lang: lang, iBannerId: iBannerId).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }


    /**
     Banner Details
     - GET /banner/banner-details/{iBannerId}
     - 

     - :
       - type: http
       - name: bearerAuth
     - examples: [{contentType=application/json, example={
  "responseData" : {
    "vName" : "vName",
    "vImage" : "vImage",
    "tBannerDescription" : "tBannerDescription",
    "iProductCategoryId" : 5,
    "vCategoryName" : "vCategoryName",
    "id" : 6,
    "iOfferId" : 5,
    "biProductId" : 1
  },
  "responseMessage" : "responseMessage",
  "responseCode" : 0
}}]
     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter iBannerId: (path)  

     - returns: RequestBuilder<BannerResponse> 
     */
    open class func bannerDetailsWithRequestBuilder(authorization: String, accept: String, lang: String, iBannerId: Int) -> RequestBuilder<BannerResponse> {
        var path = "/banner/banner-details/{iBannerId}"
        let iBannerIdPreEscape = "\(iBannerId)"
        let iBannerIdPostEscape = iBannerIdPreEscape.addingPercentEncoding(withAllowedCharacters: .urlPathAllowed) ?? ""
        path = path.replacingOccurrences(of: "{iBannerId}", with: iBannerIdPostEscape, options: .literal, range: nil)
        let URLString = SwaggerClientAPI.basePath + path
        let parameters: [String:Any]? = nil
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any?] = [
                        "Authorization": authorization,
                        "Accept": accept,
                        "Lang": lang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)

        let requestBuilder: RequestBuilder<BannerResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()

        return requestBuilder.init(method: "GET", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
}
