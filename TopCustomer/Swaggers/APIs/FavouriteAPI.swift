//
// FavouriteAPI.swift
//
// Generated by swagger-codegen
// https://github.com/swagger-api/swagger-codegen
//

import Foundation
import Alamofire


open class FavouriteAPI {
    /**
     favourite Listing

     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter offset: (query) offset (optional, default to 0)
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func favouriteListing(authorization: String, accept: String, lang: String, offset: Int? = nil, completion: @escaping ((_ data: FavouriteListResponse?,_ error: Error?) -> Void)) {
        favouriteListingWithRequestBuilder(authorization: authorization, accept: accept, lang: lang, offset: offset).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }


    /**
     favourite Listing
     - POST /favourite/list
     - 

     - :
       - type: http
       - name: bearerAuth
     - examples: [{contentType=application/json, example={
  "responseData" : {
    "totalPage" : 5,
    "limit" : 6,
    "totalRecord" : 1,
    "productList" : [ {
      "tiIsFavourite" : 2,
      "vProductName" : "vProductName",
      "iProductCategoryId" : "iProductCategoryId",
      "dDiscountedProductPrice" : "dDiscountedProductPrice",
      "dbOriginalProductPrice" : "dbOriginalProductPrice",
      "vProductImage" : "vProductImage",
      "biProductId" : 2,
      "isLowQuantity" : 9,
      "dDiscountAmount" : "dDiscountAmount",
      "youMayAlsoLikeProducts" : [ null, null ],
      "iHowMuchLeftInStock" : 3,
      "maxQtyInfo" : "maxQtyInfo",
      "txProductDescription" : "txProductDescription",
      "tiDiscountType" : 7,
      "vProductSKU" : "vProductSKU",
      "vProductUnit" : "vProductUnit"
    }, {
      "tiIsFavourite" : 2,
      "vProductName" : "vProductName",
      "iProductCategoryId" : "iProductCategoryId",
      "dDiscountedProductPrice" : "dDiscountedProductPrice",
      "dbOriginalProductPrice" : "dbOriginalProductPrice",
      "vProductImage" : "vProductImage",
      "biProductId" : 2,
      "isLowQuantity" : 9,
      "dDiscountAmount" : "dDiscountAmount",
      "youMayAlsoLikeProducts" : [ null, null ],
      "iHowMuchLeftInStock" : 3,
      "maxQtyInfo" : "maxQtyInfo",
      "txProductDescription" : "txProductDescription",
      "tiDiscountType" : 7,
      "vProductSKU" : "vProductSKU",
      "vProductUnit" : "vProductUnit"
    } ]
  },
  "responseMessage" : "responseMessage",
  "responseCode" : 0
}}]
     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter offset: (query) offset (optional, default to 0)

     - returns: RequestBuilder<FavouriteListResponse> 
     */
    open class func favouriteListingWithRequestBuilder(authorization: String, accept: String, lang: String, offset: Int? = nil) -> RequestBuilder<FavouriteListResponse> {
        let path = "/favourite/list"
        let URLString = SwaggerClientAPI.basePath + path
        let parameters: [String:Any]? = nil
        var url = URLComponents(string: URLString)
        url?.queryItems = APIHelper.mapValuesToQueryItems([
                        "offset": offset?.encodeToJSON()
        ])
        let nillableHeaders: [String: Any?] = [
                        "Authorization": authorization,
                        "Accept": accept,
                        "Lang": lang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)

        let requestBuilder: RequestBuilder<FavouriteListResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()

        return requestBuilder.init(method: "POST", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    /**
     Mark favourite

     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter biProductId: (form)  (optional)
     - parameter tiIsFavourite: (form)  (optional)
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func markFavourite(authorization: String, accept: String, lang: String, biProductId: Int? = nil, tiIsFavourite: Int? = nil, completion: @escaping ((_ data: FavouriteResponse?,_ error: Error?) -> Void)) {
        markFavouriteWithRequestBuilder(authorization: authorization, accept: accept, lang: lang, biProductId: biProductId, tiIsFavourite: tiIsFavourite).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }


    /**
     Mark favourite
     - POST /favourite/mark-favourite
     - 

     - :
       - type: http
       - name: bearerAuth
     - examples: [{contentType=application/json, example={
  "responseData" : {
    "iUserId" : 5,
    "tiIsFavourite" : 5,
    "iFavouriteId" : 6,
    "biProductId" : 1
  },
  "responseMessage" : "responseMessage",
  "responseCode" : 0
}}]
     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter biProductId: (form)  (optional)
     - parameter tiIsFavourite: (form)  (optional)

     - returns: RequestBuilder<FavouriteResponse> 
     */
    open class func markFavouriteWithRequestBuilder(authorization: String, accept: String, lang: String, biProductId: Int? = nil, tiIsFavourite: Int? = nil) -> RequestBuilder<FavouriteResponse> {
        let path = "/favourite/mark-favourite"
        let URLString = SwaggerClientAPI.basePath + path
        let formParams: [String:Any?] = [
                "biProductId": biProductId?.encodeToJSON(),
                "tiIsFavourite": tiIsFavourite?.encodeToJSON()
        ]

        let nonNullParameters = APIHelper.rejectNil(formParams)
        let parameters = APIHelper.convertBoolToString(nonNullParameters)
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any?] = [
                        "Authorization": authorization,
                        "Accept": accept,
                        "Lang": lang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)

        let requestBuilder: RequestBuilder<FavouriteResponse>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()

        return requestBuilder.init(method: "POST", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
    /**
     Remove all from favourite list

     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  
     - parameter completion: completion handler to receive the data and the error objects
     */
    open class func removeFavourite(authorization: String, accept: String, lang: String, completion: @escaping ((_ data: CommonFields?,_ error: Error?) -> Void)) {
        removeFavouriteWithRequestBuilder(authorization: authorization, accept: accept, lang: lang).execute { (response, error) -> Void in
            completion(response?.body, error)
        }
    }


    /**
     Remove all from favourite list
     - DELETE /favourite/remove-favourite
     - 

     - :
       - type: http
       - name: bearerAuth
     - examples: [{contentType=application/json, example={
  "responseMessage" : "responseMessage",
  "responseCode" : 0
}}]
     - parameter authorization: (header)  
     - parameter accept: (header)  
     - parameter lang: (header)  

     - returns: RequestBuilder<CommonFields> 
     */
    open class func removeFavouriteWithRequestBuilder(authorization: String, accept: String, lang: String) -> RequestBuilder<CommonFields> {
        let path = "/favourite/remove-favourite"
        let URLString = SwaggerClientAPI.basePath + path
        let parameters: [String:Any]? = nil
        let url = URLComponents(string: URLString)
        let nillableHeaders: [String: Any?] = [
                        "Authorization": authorization,
                        "Accept": accept,
                        "Lang": lang
        ]
        let headerParameters = APIHelper.rejectNilHeaders(nillableHeaders)

        let requestBuilder: RequestBuilder<CommonFields>.Type = SwaggerClientAPI.requestBuilderFactory.getBuilder()

        return requestBuilder.init(method: "DELETE", URLString: (url?.string ?? URLString), parameters: parameters, isBody: false, headers: headerParameters)
    }
}
