<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>UNNotificationExtensionDefaultContentHidden</key>
	<false/>
	<key>UNNotificationExtensionUserInteractionEnabled</key>
	<true/>
	<key>CFBundleDisplayName</key>
	<string>Material</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PROJECT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>SKAdNetworkItems</key>
	<array>
		<dict>
			<key>SKAdNetworkIdentifier</key>
			<string>cDkw7geQsH.skadnetwork</string>
		</dict>
		<dict>
			<key>SKAdNetworkIdentifier</key>
			<string>v9w8w9Dk7H.skadnetwork</string>
		</dict>
	</array>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>com.material.customer</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>material</string>
				<string>greatapp</string>
				<string>fb1406590183265596</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>FacebookAdvertiserIDCollectionEnabled</key>
	<true/>
	<key>FacebookAppID</key>
	<string>1406590183265596</string>
	<key>FacebookAutoLogAppEventsEnabled</key>
	<true/>
	<key>FacebookClientToken</key>
	<string>********************************</string>
	<key>FacebookDisplayName</key>
	<string>Wahtsapp</string>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>whatsapp</string>
		<string>fbapi</string>
		<string>fb-messenger-share-api</string>
		<string>fbauth2</string>
		<string>fbshareextension</string>
		<string>fb-messenger-api</string>
		<string>comgooglemaps</string>
	</array>
	<key>LSMinimumSystemVersion</key>
	<string>13.0.0</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAdvertisingAttributionReportEndpoint</key>
	<string>https://appsflyer-skadnetwork.com/</string>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
	</dict>
	<key>NSCameraUsageDescription</key>
	<string>We use your camera to scan card while making payment. This is an optional feature.</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>We use your location to map your delivery address and provide a smooth checkout experience.</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>We use your location to map your delivery address and provide a smooth checkout experience.</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>$(PRODUCT_NAME) would like to use your microphone to search.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>This allows Payment Gateway to select a photo</string>
	<key>NSSpeechRecognitionUsageDescription</key>
	<string>$(PRODUCT_NAME) would like to recognize a speech to search.</string>
	<key>NSUserTrackingUsageDescription</key>
	<string>Allowing app tracking will improve the customization of ads</string>
	<key>UIAppFonts</key>
	<array>
		<string>LoewNextArabic-Bold.ttf</string>
		<string>LoewNextArabic-Medium.ttf</string>
		<string>LoewNextArabic-ExtraBold.ttf</string>
		<string>LoewNextArabic-Heavy.ttf</string>
		<string>segoeUI.ttf</string>
	</array>
	<key>UIBackgroundModes</key>
	<array>
		<string>remote-notification</string>
		<string>fetch</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>Main</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>armv7</string>
	</array>
	<key>UIStatusBarStyle</key>
	<string>UIStatusBarStyleDarkContent</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIUserInterfaceStyle</key>
	<string>Light</string>
	<key>branch_key</key>
	<dict>
		<key>live</key>
		<string>key_live_bo68DBtOVmDXtOn577kBvibgArcUzhgc</string>
		<key>test</key>
		<string>key_test_nl1XqFvLLbF5COf788DgRcahwDdHtclF</string>
	</dict>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>We need your location even when the app is in the background.</string>
	<key>branch_universal_link_domains</key>
	<array>
		<string>applinks:5l988.app.link</string>
		<string>applinks:5l988-alternate.app.link</string>
		<string>applinks:5l988.test-app.link</string>
		<string>applinks:5l988-alternate.test-app.link</string>
	</array>
</dict>
</plist>
