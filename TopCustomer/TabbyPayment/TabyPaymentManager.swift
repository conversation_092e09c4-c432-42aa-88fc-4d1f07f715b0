//
//  TabyPaymentManager.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 28/08/2024.
//  Copyright © 2024 SOTSYS203. All rights reserved.
//


import SwiftUI
import Tabby
import WebKit

protocol TabbyPaymentResultDelegate: AnyObject {
    func checkPaymentResult(isPaymentDone: Bool)
}

protocol TabbyPaymentManagerDelegate: AnyObject {
    func initPayment(refrencePaymentId: String, total: String)
    func startPayment()
}

class TabyPaymentManager<T: UIViewController>: UIViewController, TabbyPaymentManagerDelegate {
    
    // MARK: - Properties
    final var vc = T()
    weak var delegate: TabbyPaymentResultDelegate?
    var isPaymentDone: Bool = false
    // get phone number
    var phoneNumber: String {
        get {
#if DEVRELEASE
            return "+966500000001"
#else
            return "\(SACountryCode)\(User.shared.vMobileNumber ?? "")"
#endif
        }
    }
    var webView = WKWebView()
    
    // MARK: - Init
    func initPayment(refrencePaymentId: String, total: String) {
        let payment = Payment(amount: total, currency: .SAR, description: "tabby Store Order #\(refrencePaymentId)", buyer: Buyer(
            email: "<EMAIL>",
            phone: self.phoneNumber,
            name: User.shared.vName ?? "",
            dob: nil
        ), buyer_history: BuyerHistory(
            registered_since: Date().getFullDateTimeStamp(),
            loyalty_level: 0
        ), order: Order(
            reference_id: "\(refrencePaymentId)",
            items: [
                OrderItem(
                    description: "",
                    product_url: "https://material.sa",
                    quantity: 1,
                    reference_id: "SKU\(refrencePaymentId)",
                    title: "Material Items",
                    unit_price: "0",
                    category: "Materials"
                )],
            shipping_amount: "0",
            tax_amount: "0"
        ), order_history: [
            OrderHistory(
                purchased_at: Date().getFullDateTimeStamp(),
                amount: "0",
                status: .new,
                shipping_address: ShippingAddress(
                    address: "Riyadh, North Al-Muather",
                    city: "Riyadh",
                    zip: "01234"
                )
            )
        ], shipping_address: ShippingAddress(
            address: "Riyadh, North Al-Muather",
            city: "Riyadh",
            zip: "01234")
        )
        
        let myTestPayment = TabbyCheckoutPayload(merchant_code: "Materialsau", lang: UserDefaults.standard.isCurrentLanguageArabic() ? .ar : .en, payment: payment)
        
        TabbySDK.shared.configure(forPayment: myTestPayment) { result in
            switch result {
            case .success(let s):
                // 1. Do something with sessionId (this step is optional)
                print("sessionId: \(s.sessionId)")
                // 2. Do something with paymentId (this step is optional)
                print("paymentId: \(s.paymentId)")
                // 3. Grab avaibable products from session and enable proper
                // payment method buttons in your UI (this step is required)
                // Feel free to ignore products you don't need or don't want to handle in your App
                print("tabby available products: \(s.tabbyProductTypes)")
                // If you want to handle installments product - check for .installments in response
                if (s.tabbyProductTypes.contains(.installments)) {
                    debugPrint("installments")
                }
                self.startPayment()
            case .failure(let error):
                // Do something when Tabby checkout session POST requiest failed
                print(error)
                self.vc.showAlert1(title: "error".localized, message: "payment_failed_please_try_again_or_change_type_of_pay".localized)
            }
        }
    }
    
    // MARK: - Func
    func startPayment() {
        if #available(iOS 14.0, *) {
            let paymentVC = UIHostingController(rootView: TabbyCheckout(productType: .installments, onResult: { result in
                print("TABBY RESULT: \(result)!!!")
                switch result {
                case .authorized:
                    // Do something else when Tabby authorized customer
                    // probably navigation back to Home screen, refetching, etc.
                    debugPrint("payment is done")
                    self.isPaymentDone = true
                    break
                case .rejected:
                    // Do something else when Tabby rejected customer
                    self.isPaymentDone = false
                    break
                case .close:
                    // Do something else when customer closed Tabby checkout
                    self.isPaymentDone = false
                    break
                case .expired:
                    // Do something else when session expired
                    // We strongly recommend to create new session here by calling
                    self.isPaymentDone = false
                    break
                }
                self.vc.dismiss(animated: true)
                self.delegate?.checkPaymentResult(isPaymentDone: self.isPaymentDone)
            }))
            paymentVC.modalPresentationStyle = .fullScreen
            paymentVC.modalTransitionStyle = .crossDissolve
            self.vc.present(paymentVC, animated: true)
        }
    }
    
    func startPaymentWithURL(_ url: String) {
        let webView = WKWebView(frame: CGRect(x: 0, y: 0, width: self.view.frame.size.width, height: self.view.frame.size.height))
        self.vc.view.addSubview(webView)
        
        if let url = URL(string: url) {
            let requestObj = URLRequest(url: url)
            self.webView.load(requestObj)
        }
    }
    
}
