<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="21701" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21678"/>
        <capability name="Named colors" minToolsVersion="9.0"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="LoewNextArabic-Bold.ttf">
            <string>LoewNextArabic-Bold</string>
        </array>
        <array key="LoewNextArabic-ExtraBold.ttf">
            <string>LoewNextArabic-ExtraBold</string>
        </array>
        <array key="LoewNextArabic-Medium.ttf">
            <string>LoewNextArabic-Medium</string>
        </array>
        <array key="Montserrat-SemiBold_0.otf">
            <string>Montserrat-SemiBold</string>
        </array>
    </customFonts>
    <scenes>
        <!--Static Page View Controller-->
        <scene sceneID="wnb-sc-DCV">
            <objects>
                <viewController storyboardIdentifier="StaticPageViewController" id="qFn-kW-ug1" customClass="StaticPageViewController" customModule="TopCustomer" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="daj-T9-mA1">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <wkWebView contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="CLI-3K-vxI">
                                <rect key="frame" x="10" y="118" width="394" height="734"/>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <wkWebViewConfiguration key="configuration">
                                    <audiovisualMediaTypes key="mediaTypesRequiringUserActionForPlayback" none="YES"/>
                                    <wkPreferences key="preferences"/>
                                </wkWebViewConfiguration>
                            </wkWebView>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="YKZ-a9-dXa" customClass="CustomTopViewForShadow" customModule="TopCustomer" customModuleProvider="target">
                                <rect key="frame" x="0.0" y="48" width="414" height="60"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="iOT-M0-aMs" customClass="MaterialLocalizeButton" customModule="TopCustomer" customModuleProvider="target">
                                        <rect key="frame" x="25" y="12.5" width="35" height="35"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="35" id="DEh-Rk-z5m"/>
                                            <constraint firstAttribute="width" constant="35" id="Omv-rg-A9D"/>
                                        </constraints>
                                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                        <state key="normal" image="icn_back"/>
                                        <connections>
                                            <action selector="actionBack:" destination="qFn-kW-ug1" eventType="touchUpInside" id="ASe-53-KdU"/>
                                        </connections>
                                    </button>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Privacy Policy" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Oze-KP-mGy">
                                        <rect key="frame" x="125" y="18.5" width="164.5" height="23"/>
                                        <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="23"/>
                                        <color key="textColor" name="AppTheme_BlueColor_#012CDA"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstItem="Oze-KP-mGy" firstAttribute="centerY" secondItem="YKZ-a9-dXa" secondAttribute="centerY" id="FDd-aR-BXN"/>
                                    <constraint firstItem="Oze-KP-mGy" firstAttribute="centerX" secondItem="YKZ-a9-dXa" secondAttribute="centerX" id="ZRM-Lc-57h"/>
                                    <constraint firstAttribute="height" constant="60" id="ZTV-1E-8ko"/>
                                    <constraint firstItem="iOT-M0-aMs" firstAttribute="leading" secondItem="YKZ-a9-dXa" secondAttribute="leading" constant="25" id="iE8-bJ-jSy"/>
                                    <constraint firstItem="iOT-M0-aMs" firstAttribute="centerY" secondItem="YKZ-a9-dXa" secondAttribute="centerY" id="yd1-Bm-nMp"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="uwa-lt-LX7"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="YKZ-a9-dXa" firstAttribute="top" secondItem="uwa-lt-LX7" secondAttribute="top" id="1JM-ut-gdV"/>
                            <constraint firstItem="uwa-lt-LX7" firstAttribute="bottom" secondItem="CLI-3K-vxI" secondAttribute="bottom" constant="10" id="C1X-b3-Nyj"/>
                            <constraint firstItem="CLI-3K-vxI" firstAttribute="leading" secondItem="uwa-lt-LX7" secondAttribute="leading" constant="10" id="SpW-2l-nUC"/>
                            <constraint firstItem="YKZ-a9-dXa" firstAttribute="leading" secondItem="uwa-lt-LX7" secondAttribute="leading" id="hYr-fZ-9tp"/>
                            <constraint firstItem="YKZ-a9-dXa" firstAttribute="trailing" secondItem="uwa-lt-LX7" secondAttribute="trailing" id="qj3-is-TOu"/>
                            <constraint firstItem="CLI-3K-vxI" firstAttribute="top" secondItem="YKZ-a9-dXa" secondAttribute="bottom" constant="10" id="wmP-DA-WGy"/>
                            <constraint firstItem="uwa-lt-LX7" firstAttribute="trailing" secondItem="CLI-3K-vxI" secondAttribute="trailing" constant="10" id="xwA-tP-r7O"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="lblTitle" destination="Oze-KP-mGy" id="lUU-i1-Yaw"/>
                        <outlet property="webView" destination="CLI-3K-vxI" id="1Hd-US-X53"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="ZwR-Ke-ple" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="324.**************" y="51.5625"/>
        </scene>
        <!--Account View Controller-->
        <scene sceneID="I6y-MM-tPc">
            <objects>
                <viewController storyboardIdentifier="AccountViewController" useStoryboardIdentifierAsRestorationIdentifier="YES" id="3Uo-QQ-Af0" customClass="AccountViewController" customModule="TopCustomer" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="AXY-PB-Rfs">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" translatesAutoresizingMaskIntoConstraints="NO" id="78X-01-8sl">
                                <rect key="frame" x="0.0" y="108" width="414" height="625.5"/>
                                <subviews>
                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="30" translatesAutoresizingMaskIntoConstraints="NO" id="aFv-8W-tUu">
                                        <rect key="frame" x="30" y="20" width="354" height="503"/>
                                        <subviews>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="qmx-Vx-U3u">
                                                <rect key="frame" x="0.0" y="0.0" width="354" height="78"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Name" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="tKy-te-hUu" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="0.0" width="354" height="16"/>
                                                        <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="16"/>
                                                        <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="5ac-Wb-32I" customClass="CustomViewForFields" customModule="TopCustomer" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="28" width="354" height="50"/>
                                                        <subviews>
                                                            <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="Type your name here" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="7WR-si-I3z" customClass="CustomTextfieldWithFontStyle" customModule="TopCustomer" customModuleProvider="target">
                                                                <rect key="frame" x="15" y="0.0" width="324" height="50"/>
                                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                <textInputTraits key="textInputTraits" autocapitalizationType="words" autocorrectionType="no"/>
                                                                <connections>
                                                                    <outlet property="delegate" destination="3Uo-QQ-Af0" id="7ch-dF-smb"/>
                                                                </connections>
                                                            </textField>
                                                        </subviews>
                                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <constraints>
                                                            <constraint firstAttribute="trailing" secondItem="7WR-si-I3z" secondAttribute="trailing" constant="15" id="0sT-eN-P9G"/>
                                                            <constraint firstItem="7WR-si-I3z" firstAttribute="top" secondItem="5ac-Wb-32I" secondAttribute="top" id="1TR-H3-p1Z"/>
                                                            <constraint firstItem="7WR-si-I3z" firstAttribute="leading" secondItem="5ac-Wb-32I" secondAttribute="leading" constant="15" id="QHT-v8-aSg"/>
                                                            <constraint firstAttribute="bottom" secondItem="7WR-si-I3z" secondAttribute="bottom" id="bDg-De-AdA"/>
                                                            <constraint firstAttribute="height" constant="50" id="x3J-1h-7Av"/>
                                                        </constraints>
                                                    </view>
                                                </subviews>
                                            </stackView>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="J8g-Ko-gml">
                                                <rect key="frame" x="0.0" y="108" width="354" height="78"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Mobile number" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="9Wb-5O-NIr" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="0.0" width="354" height="16"/>
                                                        <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="16"/>
                                                        <nil key="textColor"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="OUZ-Zw-lLv" customClass="CustomViewForFields" customModule="TopCustomer" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="28" width="354" height="50"/>
                                                        <subviews>
                                                            <button opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="bpR-LX-C2H">
                                                                <rect key="frame" x="0.0" y="5" width="46" height="40"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="40" id="LRl-xK-FU9"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="16"/>
                                                                <inset key="contentEdgeInsets" minX="10" minY="0.0" maxX="10" maxY="0.0"/>
                                                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                                <state key="normal" title="+91">
                                                                    <color key="titleColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                </state>
                                                                <connections>
                                                                    <action selector="actionCountryCode:" destination="3Uo-QQ-Af0" eventType="touchUpInside" id="Cmu-jb-kAl"/>
                                                                </connections>
                                                            </button>
                                                            <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="isd-qR-Sil">
                                                                <rect key="frame" x="46" y="0.0" width="0.5" height="50"/>
                                                                <color key="backgroundColor" name="AppTheme_BorderColor_#1F1F1F"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="width" constant="0.25" id="c0r-8a-Xlg"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                                <nil key="textColor"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                            <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="lKU-LP-8DA" customClass="CustomTextfieldWithFontStyle" customModule="TopCustomer" customModuleProvider="target">
                                                                <rect key="frame" x="61.5" y="0.0" width="277.5" height="50"/>
                                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                <textInputTraits key="textInputTraits" autocorrectionType="no" keyboardType="ASCIICapableNumberPad"/>
                                                                <connections>
                                                                    <outlet property="delegate" destination="3Uo-QQ-Af0" id="HgA-u8-XO3"/>
                                                                </connections>
                                                            </textField>
                                                        </subviews>
                                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <constraints>
                                                            <constraint firstAttribute="trailing" secondItem="lKU-LP-8DA" secondAttribute="trailing" constant="15" id="8UJ-FQ-fus"/>
                                                            <constraint firstItem="lKU-LP-8DA" firstAttribute="leading" secondItem="isd-qR-Sil" secondAttribute="trailing" constant="15" id="Bce-N5-cYs"/>
                                                            <constraint firstAttribute="bottom" secondItem="isd-qR-Sil" secondAttribute="bottom" id="DOD-sH-Uem"/>
                                                            <constraint firstItem="bpR-LX-C2H" firstAttribute="centerY" secondItem="OUZ-Zw-lLv" secondAttribute="centerY" id="Wm1-wU-3c9"/>
                                                            <constraint firstAttribute="height" constant="50" id="dW0-mo-BoI"/>
                                                            <constraint firstItem="isd-qR-Sil" firstAttribute="top" secondItem="OUZ-Zw-lLv" secondAttribute="top" id="dlN-IN-frB"/>
                                                            <constraint firstItem="isd-qR-Sil" firstAttribute="leading" secondItem="bpR-LX-C2H" secondAttribute="trailing" id="jh4-l2-b78"/>
                                                            <constraint firstItem="lKU-LP-8DA" firstAttribute="top" secondItem="OUZ-Zw-lLv" secondAttribute="top" id="jsy-ic-PS6"/>
                                                            <constraint firstItem="bpR-LX-C2H" firstAttribute="leading" secondItem="OUZ-Zw-lLv" secondAttribute="leading" id="noR-5V-seb"/>
                                                            <constraint firstAttribute="bottom" secondItem="lKU-LP-8DA" secondAttribute="bottom" id="yVW-KW-pBq"/>
                                                        </constraints>
                                                    </view>
                                                </subviews>
                                            </stackView>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="x3t-J3-cre">
                                                <rect key="frame" x="0.0" y="216" width="354" height="78"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Email Address" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="OPB-Mx-cXs" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="0.0" width="354" height="16"/>
                                                        <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="16"/>
                                                        <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="w1a-am-CRI" customClass="CustomViewForFields" customModule="TopCustomer" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="28" width="354" height="50"/>
                                                        <subviews>
                                                            <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="Type your Email here" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="fec-k9-eNi" customClass="CustomTextfieldWithFontStyle" customModule="TopCustomer" customModuleProvider="target">
                                                                <rect key="frame" x="15" y="0.0" width="339" height="50"/>
                                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                <textInputTraits key="textInputTraits" autocorrectionType="no" keyboardType="emailAddress"/>
                                                            </textField>
                                                        </subviews>
                                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <constraints>
                                                            <constraint firstItem="fec-k9-eNi" firstAttribute="top" secondItem="w1a-am-CRI" secondAttribute="top" id="6vx-iD-NMe"/>
                                                            <constraint firstAttribute="bottom" secondItem="fec-k9-eNi" secondAttribute="bottom" id="Ecj-2D-wza"/>
                                                            <constraint firstAttribute="height" constant="50" id="Mh7-tZ-4AH"/>
                                                            <constraint firstItem="fec-k9-eNi" firstAttribute="leading" secondItem="w1a-am-CRI" secondAttribute="leading" constant="15" id="fUm-od-IEx"/>
                                                            <constraint firstAttribute="trailing" secondItem="fec-k9-eNi" secondAttribute="trailing" id="hrC-zU-wC7"/>
                                                        </constraints>
                                                    </view>
                                                </subviews>
                                            </stackView>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="uhL-de-7di">
                                                <rect key="frame" x="0.0" y="324" width="354" height="78"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Date of Birth" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="RaW-FD-3yo" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="0.0" width="354" height="16"/>
                                                        <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="16"/>
                                                        <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="L4m-xw-d3w" customClass="CustomViewForFields" customModule="TopCustomer" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="28" width="354" height="50"/>
                                                        <subviews>
                                                            <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="Date of Birth" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="2I9-Ml-DGy" customClass="CustomTextfieldWithFontStyle" customModule="TopCustomer" customModuleProvider="target">
                                                                <rect key="frame" x="15" y="0.0" width="339" height="50"/>
                                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                <textInputTraits key="textInputTraits" autocorrectionType="no" keyboardType="emailAddress"/>
                                                            </textField>
                                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="down_arrow" translatesAutoresizingMaskIntoConstraints="NO" id="BFY-mR-cPS">
                                                                <rect key="frame" x="328" y="21.5" width="11" height="7"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="7" id="1q1-N2-sJw"/>
                                                                    <constraint firstAttribute="width" constant="11" id="6I7-iE-jwG"/>
                                                                </constraints>
                                                            </imageView>
                                                        </subviews>
                                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <constraints>
                                                            <constraint firstAttribute="trailing" secondItem="BFY-mR-cPS" secondAttribute="trailing" constant="15" id="79G-EQ-2Z8"/>
                                                            <constraint firstAttribute="bottom" secondItem="2I9-Ml-DGy" secondAttribute="bottom" id="LsL-I8-REf"/>
                                                            <constraint firstItem="BFY-mR-cPS" firstAttribute="centerY" secondItem="L4m-xw-d3w" secondAttribute="centerY" id="eXM-zb-X9K"/>
                                                            <constraint firstAttribute="trailing" secondItem="2I9-Ml-DGy" secondAttribute="trailing" id="hGQ-iv-ujL"/>
                                                            <constraint firstItem="2I9-Ml-DGy" firstAttribute="top" secondItem="L4m-xw-d3w" secondAttribute="top" id="m2N-hD-BDV"/>
                                                            <constraint firstAttribute="height" constant="50" id="xJJ-cF-Tob"/>
                                                            <constraint firstItem="2I9-Ml-DGy" firstAttribute="leading" secondItem="L4m-xw-d3w" secondAttribute="leading" constant="15" id="xat-NE-IhD"/>
                                                        </constraints>
                                                    </view>
                                                </subviews>
                                            </stackView>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="5" translatesAutoresizingMaskIntoConstraints="NO" id="JB2-ap-26C">
                                                <rect key="frame" x="0.0" y="432" width="354" height="71"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Gender" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="ur8-aK-cIc" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="0.0" width="354" height="16"/>
                                                        <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="16"/>
                                                        <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="4Wa-Rm-J2L">
                                                        <rect key="frame" x="0.0" y="21" width="354" height="50"/>
                                                        <subviews>
                                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Hgh-cO-moB" customClass="MaterialLocalizeButton" customModule="TopCustomer" customModuleProvider="target">
                                                                <rect key="frame" x="0.0" y="0.0" width="95" height="50"/>
                                                                <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="14"/>
                                                                <inset key="contentEdgeInsets" minX="20" minY="0.0" maxX="0.0" maxY="0.0"/>
                                                                <inset key="imageEdgeInsets" minX="-20" minY="0.0" maxX="0.0" maxY="0.0"/>
                                                                <state key="normal" title="Female" image="icn_radioUnselected">
                                                                    <color key="titleColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                </state>
                                                                <state key="selected" image="icn_radioSelected"/>
                                                                <connections>
                                                                    <action selector="btnFemale:" destination="3Uo-QQ-Af0" eventType="touchUpInside" id="qaS-q3-rQl"/>
                                                                </connections>
                                                            </button>
                                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="uXq-oo-N6Z" customClass="MaterialLocalizeButton" customModule="TopCustomer" customModuleProvider="target">
                                                                <rect key="frame" x="120" y="0.0" width="78" height="50"/>
                                                                <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="14"/>
                                                                <inset key="contentEdgeInsets" minX="20" minY="0.0" maxX="0.0" maxY="0.0"/>
                                                                <inset key="imageEdgeInsets" minX="-20" minY="0.0" maxX="0.0" maxY="0.0"/>
                                                                <state key="normal" title="Male" image="icn_radioUnselected">
                                                                    <color key="titleColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                </state>
                                                                <state key="selected" image="icn_radioSelected"/>
                                                                <connections>
                                                                    <action selector="actionMale:" destination="3Uo-QQ-Af0" eventType="touchUpInside" id="xUT-aU-ltU"/>
                                                                </connections>
                                                            </button>
                                                        </subviews>
                                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <constraints>
                                                            <constraint firstItem="uXq-oo-N6Z" firstAttribute="top" secondItem="4Wa-Rm-J2L" secondAttribute="top" id="4mX-o4-gpl"/>
                                                            <constraint firstItem="Hgh-cO-moB" firstAttribute="top" secondItem="4Wa-Rm-J2L" secondAttribute="top" id="6LK-ex-idp"/>
                                                            <constraint firstAttribute="height" constant="50" id="Lr8-OW-RMM"/>
                                                            <constraint firstAttribute="bottom" secondItem="uXq-oo-N6Z" secondAttribute="bottom" id="RCl-8f-3Dt"/>
                                                            <constraint firstItem="uXq-oo-N6Z" firstAttribute="centerY" secondItem="4Wa-Rm-J2L" secondAttribute="centerY" id="Ze1-eQ-pex"/>
                                                            <constraint firstItem="Hgh-cO-moB" firstAttribute="centerY" secondItem="4Wa-Rm-J2L" secondAttribute="centerY" id="a6z-tG-XQM"/>
                                                            <constraint firstItem="uXq-oo-N6Z" firstAttribute="leading" secondItem="Hgh-cO-moB" secondAttribute="trailing" constant="25" id="apz-Pn-Daq"/>
                                                            <constraint firstAttribute="bottom" secondItem="Hgh-cO-moB" secondAttribute="bottom" id="c47-qa-nLY"/>
                                                        </constraints>
                                                    </view>
                                                </subviews>
                                                <constraints>
                                                    <constraint firstItem="Hgh-cO-moB" firstAttribute="leading" secondItem="ur8-aK-cIc" secondAttribute="leading" id="9U7-yO-oe7"/>
                                                </constraints>
                                            </stackView>
                                        </subviews>
                                    </stackView>
                                </subviews>
                                <constraints>
                                    <constraint firstItem="aFv-8W-tUu" firstAttribute="leading" secondItem="78X-01-8sl" secondAttribute="leading" constant="30" id="0jZ-hK-Cxf"/>
                                    <constraint firstItem="aFv-8W-tUu" firstAttribute="top" secondItem="78X-01-8sl" secondAttribute="top" constant="20" id="EZT-ng-J4X"/>
                                    <constraint firstAttribute="bottom" secondItem="aFv-8W-tUu" secondAttribute="bottom" constant="20" id="ODR-9e-IRP"/>
                                    <constraint firstAttribute="trailing" secondItem="aFv-8W-tUu" secondAttribute="trailing" constant="30" id="dwF-uJ-mAM"/>
                                    <constraint firstItem="aFv-8W-tUu" firstAttribute="centerX" secondItem="78X-01-8sl" secondAttribute="centerX" id="ilV-1d-mJg"/>
                                </constraints>
                            </scrollView>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="QY3-B0-Zck" customClass="CustomRoundedButtton" customModule="TopCustomer" customModuleProvider="target">
                                <rect key="frame" x="30" y="753.5" width="354" height="40"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="40" id="1gm-PX-hrB"/>
                                </constraints>
                                <state key="normal" title="Update"/>
                                <connections>
                                    <action selector="actionUpdate:" destination="3Uo-QQ-Af0" eventType="touchUpInside" id="Zgd-ay-BPc"/>
                                </connections>
                            </button>
                            <button opaque="NO" contentMode="scaleToFill" semanticContentAttribute="forceLeftToRight" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" hasAttributedTitle="YES" translatesAutoresizingMaskIntoConstraints="NO" id="owF-ej-axq">
                                <rect key="frame" x="30" y="813.5" width="354" height="28.5"/>
                                <inset key="titleEdgeInsets" minX="10" minY="0.0" maxX="0.0" maxY="0.0"/>
                                <state key="normal">
                                    <attributedString key="attributedTitle">
                                        <fragment content="Delete my Account">
                                            <attributes>
                                                <color key="NSColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <font key="NSFont" metaFont="system" size="14"/>
                                                <paragraphStyle key="NSParagraphStyle" alignment="natural" lineBreakMode="wordWrapping" baseWritingDirection="natural" tighteningFactorForTruncation="0.0"/>
                                                <integer key="NSUnderline" value="1"/>
                                            </attributes>
                                        </fragment>
                                    </attributedString>
                                </state>
                                <connections>
                                    <action selector="actionDeleteMyAccount:" destination="3Uo-QQ-Af0" eventType="touchUpInside" id="QRY-uO-h1O"/>
                                </connections>
                            </button>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="8rH-bU-oKj" customClass="CustomTopViewForShadow" customModule="TopCustomer" customModuleProvider="target">
                                <rect key="frame" x="0.0" y="48" width="414" height="60"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="3tZ-zM-uNj" customClass="MaterialLocalizeButton" customModule="TopCustomer" customModuleProvider="target">
                                        <rect key="frame" x="25" y="12.5" width="35" height="35"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="35" id="RPs-ez-Mif"/>
                                            <constraint firstAttribute="width" constant="35" id="SiV-Gj-iPv"/>
                                        </constraints>
                                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                        <state key="normal" image="icn_back"/>
                                        <connections>
                                            <action selector="actionBack:" destination="3Uo-QQ-Af0" eventType="touchUpInside" id="vkn-hS-AJa"/>
                                        </connections>
                                    </button>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Account" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="aQN-As-gWB">
                                        <rect key="frame" x="157" y="18.5" width="100.5" height="23"/>
                                        <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="23"/>
                                        <color key="textColor" name="AppTheme_BlueColor_#012CDA"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstItem="3tZ-zM-uNj" firstAttribute="leading" secondItem="8rH-bU-oKj" secondAttribute="leading" constant="25" id="Pwg-gs-vlV"/>
                                    <constraint firstItem="aQN-As-gWB" firstAttribute="centerX" secondItem="8rH-bU-oKj" secondAttribute="centerX" id="Yjk-oB-qte"/>
                                    <constraint firstItem="3tZ-zM-uNj" firstAttribute="centerY" secondItem="8rH-bU-oKj" secondAttribute="centerY" id="aO4-Vc-lwz"/>
                                    <constraint firstItem="aQN-As-gWB" firstAttribute="centerY" secondItem="8rH-bU-oKj" secondAttribute="centerY" id="sSf-jj-mvw"/>
                                    <constraint firstAttribute="height" constant="60" id="zIM-MO-vDs"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="YOc-Px-av8"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="78X-01-8sl" firstAttribute="leading" secondItem="YOc-Px-av8" secondAttribute="leading" id="0oh-t4-h3W"/>
                            <constraint firstItem="8rH-bU-oKj" firstAttribute="leading" secondItem="YOc-Px-av8" secondAttribute="leading" id="7O9-lw-wS1"/>
                            <constraint firstItem="owF-ej-axq" firstAttribute="top" secondItem="QY3-B0-Zck" secondAttribute="bottom" constant="20" id="81J-jB-7Dh"/>
                            <constraint firstItem="owF-ej-axq" firstAttribute="leading" secondItem="QY3-B0-Zck" secondAttribute="leading" id="AS4-x6-hkE"/>
                            <constraint firstItem="78X-01-8sl" firstAttribute="top" secondItem="8rH-bU-oKj" secondAttribute="bottom" id="FDc-49-cG1"/>
                            <constraint firstItem="YOc-Px-av8" firstAttribute="bottom" secondItem="owF-ej-axq" secondAttribute="bottom" constant="20" id="MTl-Nk-3Cq"/>
                            <constraint firstItem="owF-ej-axq" firstAttribute="trailing" secondItem="QY3-B0-Zck" secondAttribute="trailing" id="NMo-fn-omN"/>
                            <constraint firstItem="QY3-B0-Zck" firstAttribute="top" secondItem="78X-01-8sl" secondAttribute="bottom" constant="20" id="VND-bg-oiH"/>
                            <constraint firstItem="YOc-Px-av8" firstAttribute="trailing" secondItem="QY3-B0-Zck" secondAttribute="trailing" constant="30" id="cee-cR-zh7"/>
                            <constraint firstItem="QY3-B0-Zck" firstAttribute="leading" secondItem="YOc-Px-av8" secondAttribute="leading" constant="30" id="fg3-T0-k8u"/>
                            <constraint firstItem="YOc-Px-av8" firstAttribute="trailing" secondItem="8rH-bU-oKj" secondAttribute="trailing" id="rSX-gd-sut"/>
                            <constraint firstItem="YOc-Px-av8" firstAttribute="trailing" secondItem="78X-01-8sl" secondAttribute="trailing" id="t3i-2S-sdB"/>
                            <constraint firstItem="8rH-bU-oKj" firstAttribute="top" secondItem="YOc-Px-av8" secondAttribute="top" id="yOh-jQ-3Uh"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="btnCountryCode" destination="bpR-LX-C2H" id="gez-tG-o1X"/>
                        <outlet property="btnDeleteMyAccount" destination="owF-ej-axq" id="PFx-bS-VVV"/>
                        <outlet property="btnFemale" destination="Hgh-cO-moB" id="yeM-uZ-sC6"/>
                        <outlet property="btnMale" destination="uXq-oo-N6Z" id="cWC-Ea-rXi"/>
                        <outlet property="btnUpdate" destination="QY3-B0-Zck" id="ihz-Gp-tEh"/>
                        <outlet property="lblDOBTitle" destination="RaW-FD-3yo" id="V0X-PT-V5Z"/>
                        <outlet property="lblEmailTitle" destination="OPB-Mx-cXs" id="fbN-8s-ETa"/>
                        <outlet property="lblGender" destination="ur8-aK-cIc" id="bTQ-2X-Q3H"/>
                        <outlet property="lblMobNoTitle" destination="9Wb-5O-NIr" id="K1m-Cf-dEz"/>
                        <outlet property="lblNameTitle" destination="tKy-te-hUu" id="mUd-LG-ypw"/>
                        <outlet property="lblTitle" destination="aQN-As-gWB" id="JJg-IX-69E"/>
                        <outlet property="txtDateOfBirth" destination="2I9-Ml-DGy" id="kyT-QA-VZE"/>
                        <outlet property="txtEmailAddress" destination="fec-k9-eNi" id="8g9-o0-pay"/>
                        <outlet property="txtName" destination="7WR-si-I3z" id="sLV-3l-Igm"/>
                        <outlet property="txtPhoneNumber" destination="lKU-LP-8DA" id="K55-5s-4oZ"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="ntX-2e-uky" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="324.**************" y="-715.17857142857144"/>
        </scene>
        <!--Contact Us View Controller-->
        <scene sceneID="Sus-74-9c5">
            <objects>
                <viewController storyboardIdentifier="ContactUsViewController" useStoryboardIdentifierAsRestorationIdentifier="YES" id="xPO-8J-eOH" customClass="ContactUsViewController" customModule="TopCustomer" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="i7Y-dp-EO3">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" translatesAutoresizingMaskIntoConstraints="NO" id="IvJ-6M-7wR">
                                <rect key="frame" x="0.0" y="108" width="414" height="754"/>
                                <subviews>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Contact Information" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="iio-HT-uKb" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                        <rect key="frame" x="30" y="60" width="354" height="16"/>
                                        <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="16"/>
                                        <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="qVn-39-nKj">
                                        <rect key="frame" x="30" y="91" width="354" height="102"/>
                                        <subviews>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="kiI-ZT-eIH">
                                                <rect key="frame" x="0.0" y="0.0" width="354" height="50"/>
                                                <subviews>
                                                    <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="Name" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="LO7-m8-M3T" customClass="CustomTextfieldWithFontStyle" customModule="TopCustomer" customModuleProvider="target">
                                                        <rect key="frame" x="15" y="0.0" width="324" height="50"/>
                                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                                        <textInputTraits key="textInputTraits" autocapitalizationType="words" autocorrectionType="no"/>
                                                        <connections>
                                                            <outlet property="delegate" destination="xPO-8J-eOH" id="Jrr-J4-oVT"/>
                                                        </connections>
                                                    </textField>
                                                </subviews>
                                                <color key="backgroundColor" name="AppTheme_FieldBGColor_#F7F7F7"/>
                                                <constraints>
                                                    <constraint firstItem="LO7-m8-M3T" firstAttribute="leading" secondItem="kiI-ZT-eIH" secondAttribute="leading" constant="15" id="3VC-Wt-Usf"/>
                                                    <constraint firstAttribute="trailing" secondItem="LO7-m8-M3T" secondAttribute="trailing" constant="15" id="5SV-w2-dUt"/>
                                                    <constraint firstItem="LO7-m8-M3T" firstAttribute="top" secondItem="kiI-ZT-eIH" secondAttribute="top" id="J8z-Oz-Ssa"/>
                                                    <constraint firstAttribute="bottom" secondItem="LO7-m8-M3T" secondAttribute="bottom" id="PkY-uJ-UhL"/>
                                                    <constraint firstAttribute="height" constant="50" id="Q8O-c3-gy6"/>
                                                </constraints>
                                            </view>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="wqA-nE-4RM">
                                                <rect key="frame" x="0.0" y="50" width="354" height="2"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="NP1-uU-1mG">
                                                        <rect key="frame" x="10" y="0.0" width="334" height="2"/>
                                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="2" id="NDg-Nm-y04"/>
                                                        </constraints>
                                                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                        <nil key="textColor"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                </subviews>
                                                <color key="backgroundColor" name="AppTheme_FieldBGColor_#F7F7F7"/>
                                                <constraints>
                                                    <constraint firstItem="NP1-uU-1mG" firstAttribute="top" secondItem="wqA-nE-4RM" secondAttribute="top" id="AZa-Rp-Ecp"/>
                                                    <constraint firstAttribute="trailing" secondItem="NP1-uU-1mG" secondAttribute="trailing" constant="10" id="Yd9-YO-vBz"/>
                                                    <constraint firstAttribute="bottom" secondItem="NP1-uU-1mG" secondAttribute="bottom" id="ilN-oj-RNl"/>
                                                    <constraint firstItem="NP1-uU-1mG" firstAttribute="leading" secondItem="wqA-nE-4RM" secondAttribute="leading" constant="10" id="yEU-Fd-zl2"/>
                                                </constraints>
                                            </view>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="hZi-hi-dFK">
                                                <rect key="frame" x="0.0" y="52" width="354" height="50"/>
                                                <subviews>
                                                    <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="Email Address" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="q97-0f-WRX" customClass="CustomTextfieldWithFontStyle" customModule="TopCustomer" customModuleProvider="target">
                                                        <rect key="frame" x="15" y="0.0" width="339" height="50"/>
                                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                                        <textInputTraits key="textInputTraits" autocorrectionType="no" keyboardType="emailAddress"/>
                                                    </textField>
                                                </subviews>
                                                <color key="backgroundColor" name="AppTheme_FieldBGColor_#F7F7F7"/>
                                                <constraints>
                                                    <constraint firstItem="q97-0f-WRX" firstAttribute="top" secondItem="hZi-hi-dFK" secondAttribute="top" id="Ihv-FC-tYt"/>
                                                    <constraint firstItem="q97-0f-WRX" firstAttribute="leading" secondItem="hZi-hi-dFK" secondAttribute="leading" constant="15" id="Oau-i5-Ccf"/>
                                                    <constraint firstAttribute="trailing" secondItem="q97-0f-WRX" secondAttribute="trailing" id="VEG-FU-hRU"/>
                                                    <constraint firstAttribute="height" constant="50" id="W28-Fe-Tep"/>
                                                    <constraint firstAttribute="bottom" secondItem="q97-0f-WRX" secondAttribute="bottom" id="eCv-Sw-pQQ"/>
                                                </constraints>
                                            </view>
                                        </subviews>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                <real key="value" value="9"/>
                                            </userDefinedRuntimeAttribute>
                                        </userDefinedRuntimeAttributes>
                                    </stackView>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="How Can we help you?" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="EYv-Eu-KpD" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                        <rect key="frame" x="30" y="241" width="354" height="16"/>
                                        <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="16"/>
                                        <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="5bv-lD-ZLK" customClass="CustomViewForFields" customModule="TopCustomer" customModuleProvider="target">
                                        <rect key="frame" x="30" y="272" width="354" height="120"/>
                                        <subviews>
                                            <textView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="LTn-hh-he7" customClass="IQTextView" customModule="IQKeyboardManagerSwift">
                                                <rect key="frame" x="10" y="5" width="334" height="115"/>
                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <color key="textColor" systemColor="labelColor"/>
                                                <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="16"/>
                                                <textInputTraits key="textInputTraits" autocapitalizationType="sentences"/>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="string" keyPath="placeholder" value="Right your message here…"/>
                                                </userDefinedRuntimeAttributes>
                                            </textView>
                                        </subviews>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="trailing" secondItem="LTn-hh-he7" secondAttribute="trailing" constant="10" id="6Jf-jm-BXh"/>
                                            <constraint firstItem="LTn-hh-he7" firstAttribute="top" secondItem="5bv-lD-ZLK" secondAttribute="top" constant="5" id="BXv-o2-Gjh"/>
                                            <constraint firstAttribute="height" constant="120" id="W9L-ux-pXC"/>
                                            <constraint firstAttribute="bottom" secondItem="LTn-hh-he7" secondAttribute="bottom" id="p1n-cp-0uA"/>
                                            <constraint firstItem="LTn-hh-he7" firstAttribute="leading" secondItem="5bv-lD-ZLK" secondAttribute="leading" constant="10" id="zcw-d4-Fbj"/>
                                        </constraints>
                                    </view>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="BXv-cJ-qNm" customClass="CustomRoundedButtton" customModule="TopCustomer" customModuleProvider="target">
                                        <rect key="frame" x="30" y="419" width="354" height="40"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="40" id="XIv-ZF-cxR"/>
                                        </constraints>
                                        <state key="normal" title="Send"/>
                                        <connections>
                                            <action selector="actionSend:" destination="xPO-8J-eOH" eventType="touchUpInside" id="iQn-uW-s1v"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Ub0-hS-yT1">
                                        <rect key="frame" x="170" y="505" width="74" height="27"/>
                                        <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="15"/>
                                        <state key="normal" title="Follow Us">
                                            <color key="titleColor" name="AppTheme_SelectedTabColor_#F4BA45"/>
                                        </state>
                                    </button>
                                    <stackView opaque="NO" contentMode="scaleToFill" spacing="10" translatesAutoresizingMaskIntoConstraints="NO" id="wlc-CX-JXH">
                                        <rect key="frame" x="144.5" y="547" width="125" height="35"/>
                                        <subviews>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="FhB-UK-hKk">
                                                <rect key="frame" x="0.0" y="0.0" width="35" height="35"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="35" id="hAz-Tr-x1d"/>
                                                    <constraint firstAttribute="height" constant="35" id="w9t-7x-Iuo"/>
                                                </constraints>
                                                <state key="normal" image="icn_instagram"/>
                                                <connections>
                                                    <action selector="actionInstagram:" destination="xPO-8J-eOH" eventType="touchUpInside" id="RPe-ut-Fn0"/>
                                                </connections>
                                            </button>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="kx6-ei-RS0">
                                                <rect key="frame" x="45" y="0.0" width="35" height="35"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="35" id="aua-hk-aYn"/>
                                                    <constraint firstAttribute="height" constant="35" id="jT7-DP-FuH"/>
                                                </constraints>
                                                <state key="normal" image="icn_twitter"/>
                                                <connections>
                                                    <action selector="actionTwitter:" destination="xPO-8J-eOH" eventType="touchUpInside" id="Vvk-8e-Par"/>
                                                </connections>
                                            </button>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="TM6-nU-rYi">
                                                <rect key="frame" x="90" y="0.0" width="35" height="35"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="35" id="ETf-hN-7U0"/>
                                                    <constraint firstAttribute="height" constant="35" id="gWr-oX-vce"/>
                                                </constraints>
                                                <state key="normal" image="icn_phone"/>
                                                <connections>
                                                    <action selector="actionPhone:" destination="xPO-8J-eOH" eventType="touchUpInside" id="d0o-0D-itK"/>
                                                </connections>
                                            </button>
                                        </subviews>
                                    </stackView>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Ups-9c-3Vv" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                        <rect key="frame" x="40" y="612" width="334" height="0.0"/>
                                        <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="14"/>
                                        <color key="textColor" white="0.33333333329999998" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                                <constraints>
                                    <constraint firstItem="iio-HT-uKb" firstAttribute="leading" secondItem="IvJ-6M-7wR" secondAttribute="leading" constant="30" id="2fW-3g-BDh"/>
                                    <constraint firstItem="qVn-39-nKj" firstAttribute="top" secondItem="iio-HT-uKb" secondAttribute="bottom" constant="15" id="3HG-5v-kAK"/>
                                    <constraint firstItem="Ups-9c-3Vv" firstAttribute="leading" secondItem="IvJ-6M-7wR" secondAttribute="leading" constant="40" id="6Pp-aW-0en"/>
                                    <constraint firstItem="qVn-39-nKj" firstAttribute="trailing" secondItem="iio-HT-uKb" secondAttribute="trailing" id="9Xc-UJ-dYe"/>
                                    <constraint firstItem="wlc-CX-JXH" firstAttribute="top" secondItem="Ub0-hS-yT1" secondAttribute="bottom" constant="15" id="C0K-wq-bWc"/>
                                    <constraint firstItem="iio-HT-uKb" firstAttribute="top" secondItem="IvJ-6M-7wR" secondAttribute="top" constant="60" id="GHw-uT-QIe"/>
                                    <constraint firstItem="qVn-39-nKj" firstAttribute="leading" secondItem="iio-HT-uKb" secondAttribute="leading" id="GLq-uc-1IT"/>
                                    <constraint firstAttribute="trailing" secondItem="Ups-9c-3Vv" secondAttribute="trailing" constant="40" id="T32-aX-Ppw"/>
                                    <constraint firstItem="EYv-Eu-KpD" firstAttribute="leading" secondItem="qVn-39-nKj" secondAttribute="leading" id="T3a-mX-QU1"/>
                                    <constraint firstItem="Ub0-hS-yT1" firstAttribute="top" secondItem="BXv-cJ-qNm" secondAttribute="bottom" constant="46" id="c9h-UR-tHJ"/>
                                    <constraint firstAttribute="trailing" secondItem="iio-HT-uKb" secondAttribute="trailing" constant="30" id="e2v-a7-Ltg"/>
                                    <constraint firstItem="Ups-9c-3Vv" firstAttribute="top" secondItem="wlc-CX-JXH" secondAttribute="bottom" constant="30" id="ejf-ZM-t2c"/>
                                    <constraint firstItem="wlc-CX-JXH" firstAttribute="centerX" secondItem="IvJ-6M-7wR" secondAttribute="centerX" id="hjN-0g-y5W"/>
                                    <constraint firstAttribute="trailing" secondItem="BXv-cJ-qNm" secondAttribute="trailing" constant="30" id="hpg-Rm-rDw"/>
                                    <constraint firstItem="EYv-Eu-KpD" firstAttribute="top" secondItem="qVn-39-nKj" secondAttribute="bottom" constant="48" id="iGc-Yz-Sm0"/>
                                    <constraint firstItem="BXv-cJ-qNm" firstAttribute="leading" secondItem="IvJ-6M-7wR" secondAttribute="leading" constant="30" id="iOZ-DK-smf"/>
                                    <constraint firstItem="Ub0-hS-yT1" firstAttribute="centerX" secondItem="IvJ-6M-7wR" secondAttribute="centerX" id="kUm-W4-KQt"/>
                                    <constraint firstItem="EYv-Eu-KpD" firstAttribute="trailing" secondItem="qVn-39-nKj" secondAttribute="trailing" id="kb0-wx-H0k"/>
                                    <constraint firstItem="BXv-cJ-qNm" firstAttribute="centerX" secondItem="IvJ-6M-7wR" secondAttribute="centerX" id="p3B-QO-KBL"/>
                                    <constraint firstAttribute="bottom" secondItem="Ups-9c-3Vv" secondAttribute="bottom" constant="25" id="qLl-QV-fhC"/>
                                    <constraint firstItem="iio-HT-uKb" firstAttribute="centerX" secondItem="IvJ-6M-7wR" secondAttribute="centerX" id="s17-IV-ZN1"/>
                                    <constraint firstItem="BXv-cJ-qNm" firstAttribute="top" secondItem="5bv-lD-ZLK" secondAttribute="bottom" constant="27" id="vLk-Uc-GcL"/>
                                    <constraint firstItem="5bv-lD-ZLK" firstAttribute="trailing" secondItem="EYv-Eu-KpD" secondAttribute="trailing" id="wDd-yA-tip"/>
                                    <constraint firstItem="5bv-lD-ZLK" firstAttribute="leading" secondItem="EYv-Eu-KpD" secondAttribute="leading" id="xz0-TD-H1i"/>
                                    <constraint firstItem="5bv-lD-ZLK" firstAttribute="top" secondItem="EYv-Eu-KpD" secondAttribute="bottom" constant="15" id="yQd-0m-WjW"/>
                                </constraints>
                            </scrollView>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="3ju-l7-bDW" customClass="CustomTopViewForShadow" customModule="TopCustomer" customModuleProvider="target">
                                <rect key="frame" x="0.0" y="48" width="414" height="60"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="ZEo-GQ-00U" customClass="MaterialLocalizeButton" customModule="TopCustomer" customModuleProvider="target">
                                        <rect key="frame" x="25" y="12.5" width="35" height="35"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="35" id="CRw-gJ-eNk"/>
                                            <constraint firstAttribute="width" constant="35" id="VhN-sa-siL"/>
                                        </constraints>
                                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                        <state key="normal" image="icn_back"/>
                                        <connections>
                                            <action selector="actionBack:" destination="xPO-8J-eOH" eventType="touchUpInside" id="3e1-8C-eJM"/>
                                        </connections>
                                    </button>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Contact Us" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Dlp-qC-BSr">
                                        <rect key="frame" x="141.5" y="18.5" width="131.5" height="23"/>
                                        <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="23"/>
                                        <color key="textColor" name="AppTheme_BlueColor_#012CDA"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstItem="Dlp-qC-BSr" firstAttribute="centerX" secondItem="3ju-l7-bDW" secondAttribute="centerX" id="9QN-mm-qUD"/>
                                    <constraint firstAttribute="height" constant="60" id="Ez2-Dq-Dp4"/>
                                    <constraint firstItem="ZEo-GQ-00U" firstAttribute="leading" secondItem="3ju-l7-bDW" secondAttribute="leading" constant="25" id="h6M-Nw-Gdt"/>
                                    <constraint firstItem="Dlp-qC-BSr" firstAttribute="centerY" secondItem="3ju-l7-bDW" secondAttribute="centerY" id="jUJ-wm-6Tm"/>
                                    <constraint firstItem="ZEo-GQ-00U" firstAttribute="centerY" secondItem="3ju-l7-bDW" secondAttribute="centerY" id="kf9-zJ-bxA"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="h5Y-rt-8Iv"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="IvJ-6M-7wR" firstAttribute="leading" secondItem="h5Y-rt-8Iv" secondAttribute="leading" id="7EF-WP-8TE"/>
                            <constraint firstItem="IvJ-6M-7wR" firstAttribute="top" secondItem="3ju-l7-bDW" secondAttribute="bottom" id="CA4-vN-VhV"/>
                            <constraint firstItem="h5Y-rt-8Iv" firstAttribute="bottom" secondItem="IvJ-6M-7wR" secondAttribute="bottom" id="COc-bD-kgV"/>
                            <constraint firstItem="h5Y-rt-8Iv" firstAttribute="trailing" secondItem="IvJ-6M-7wR" secondAttribute="trailing" id="KGQ-Tr-aGY"/>
                            <constraint firstItem="3ju-l7-bDW" firstAttribute="leading" secondItem="h5Y-rt-8Iv" secondAttribute="leading" id="NK2-Cw-coj"/>
                            <constraint firstItem="h5Y-rt-8Iv" firstAttribute="trailing" secondItem="3ju-l7-bDW" secondAttribute="trailing" id="Ye0-AU-dZR"/>
                            <constraint firstItem="3ju-l7-bDW" firstAttribute="top" secondItem="h5Y-rt-8Iv" secondAttribute="top" id="jHZ-eM-nCS"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="btnFollowUs" destination="Ub0-hS-yT1" id="Vxl-pA-Oyu"/>
                        <outlet property="btnSend" destination="BXv-cJ-qNm" id="O6e-3n-Pti"/>
                        <outlet property="lblContactInfo" destination="iio-HT-uKb" id="qni-aV-mBx"/>
                        <outlet property="lblHowCan" destination="EYv-Eu-KpD" id="rVN-iL-MVa"/>
                        <outlet property="lblTitle" destination="Dlp-qC-BSr" id="Xbf-Wg-uRJ"/>
                        <outlet property="lblVersion" destination="Ups-9c-3Vv" id="OgB-BI-uUZ"/>
                        <outlet property="txtEmail" destination="q97-0f-WRX" id="CTt-OY-9ne"/>
                        <outlet property="txtMessage" destination="LTn-hh-he7" id="Sxb-Yw-sTb"/>
                        <outlet property="txtName" destination="LO7-m8-M3T" id="bYf-is-NpM"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="5mv-5S-kcA" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="325" y="790"/>
        </scene>
        <!--Select Language View Controller-->
        <scene sceneID="rpq-NH-54t">
            <objects>
                <viewController storyboardIdentifier="SelectLanguageViewController" useStoryboardIdentifierAsRestorationIdentifier="YES" id="1co-wh-ldK" customClass="SelectLanguageViewController" customModule="TopCustomer" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="bTq-ge-ajY">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="fillEqually" spacing="10" translatesAutoresizingMaskIntoConstraints="NO" id="axr-Ge-YdO">
                                <rect key="frame" x="15" y="63" width="384" height="800"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Szo-Fg-Tqe">
                                        <rect key="frame" x="0.0" y="0.0" width="384" height="192.5"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Select Language" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="7Au-Aq-kE9" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                <rect key="frame" x="15" y="15" width="354" height="162.5"/>
                                                <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="17"/>
                                                <color key="textColor" name="AppTheme_BlueColor_#012CDA"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="94X-fh-7mg">
                                                <rect key="frame" x="0.0" y="191.5" width="384" height="1"/>
                                                <color key="backgroundColor" name="AppTheme_LightGrayColor_#CECECE"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="1" id="HGs-Vx-j2s"/>
                                                </constraints>
                                            </view>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstAttribute="bottom" secondItem="7Au-Aq-kE9" secondAttribute="bottom" constant="15" id="1Pc-Dh-ivU"/>
                                            <constraint firstItem="94X-fh-7mg" firstAttribute="leading" secondItem="Szo-Fg-Tqe" secondAttribute="leading" id="2OY-lr-9aA"/>
                                            <constraint firstAttribute="trailing" secondItem="94X-fh-7mg" secondAttribute="trailing" id="9aP-VI-ymf"/>
                                            <constraint firstAttribute="height" constant="50" id="CH0-07-71O"/>
                                            <constraint firstItem="7Au-Aq-kE9" firstAttribute="top" secondItem="Szo-Fg-Tqe" secondAttribute="top" constant="15" id="KVZ-7x-RSU"/>
                                            <constraint firstAttribute="bottom" secondItem="94X-fh-7mg" secondAttribute="bottom" id="M4d-Hy-dGH"/>
                                            <constraint firstAttribute="trailing" secondItem="7Au-Aq-kE9" secondAttribute="trailing" constant="15" id="QKB-8b-BOx"/>
                                            <constraint firstItem="7Au-Aq-kE9" firstAttribute="leading" secondItem="Szo-Fg-Tqe" secondAttribute="leading" constant="15" id="fHw-R7-N2D"/>
                                        </constraints>
                                    </view>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="oTC-DE-7ux">
                                        <rect key="frame" x="0.0" y="202.5" width="384" height="192.5"/>
                                        <subviews>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="uaR-WA-CBi" customClass="MaterialLocalizeButton" customModule="TopCustomer" customModuleProvider="target">
                                                <rect key="frame" x="15" y="82" width="354" height="29"/>
                                                <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="17"/>
                                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                <state key="normal" title="English">
                                                    <color key="titleColor" name="AppTheme_TextColor_#222222"/>
                                                </state>
                                                <connections>
                                                    <action selector="actionEnglish:" destination="1co-wh-ldK" eventType="touchUpInside" id="p5j-2D-5iO"/>
                                                </connections>
                                            </button>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="7Zl-vr-6Y7">
                                                <rect key="frame" x="0.0" y="191.5" width="384" height="1"/>
                                                <color key="backgroundColor" name="AppTheme_LightGrayColor_#CECECE"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="1" id="zdj-YK-oM5"/>
                                                </constraints>
                                            </view>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstItem="uaR-WA-CBi" firstAttribute="centerY" secondItem="oTC-DE-7ux" secondAttribute="centerY" id="7fW-Ca-Mvg"/>
                                            <constraint firstItem="uaR-WA-CBi" firstAttribute="leading" secondItem="oTC-DE-7ux" secondAttribute="leading" constant="15" id="TTx-kT-b3L"/>
                                            <constraint firstAttribute="trailing" secondItem="7Zl-vr-6Y7" secondAttribute="trailing" id="bYr-0b-tef"/>
                                            <constraint firstItem="7Zl-vr-6Y7" firstAttribute="leading" secondItem="oTC-DE-7ux" secondAttribute="leading" id="ed7-bF-nSz"/>
                                            <constraint firstAttribute="bottom" secondItem="7Zl-vr-6Y7" secondAttribute="bottom" id="kdH-CK-h8n"/>
                                            <constraint firstAttribute="trailing" secondItem="uaR-WA-CBi" secondAttribute="trailing" constant="15" id="mlW-vK-D4U"/>
                                        </constraints>
                                    </view>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ZgA-na-GrD">
                                        <rect key="frame" x="0.0" y="405" width="384" height="192.5"/>
                                        <subviews>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="xyl-8s-see" customClass="MaterialLocalizeButton" customModule="TopCustomer" customModuleProvider="target">
                                                <rect key="frame" x="15" y="82" width="354" height="29"/>
                                                <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="17"/>
                                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                <state key="normal" title="عربي">
                                                    <color key="titleColor" name="AppTheme_TextColor_#222222"/>
                                                </state>
                                                <connections>
                                                    <action selector="actionArabic:" destination="1co-wh-ldK" eventType="touchUpInside" id="U4s-hV-YPq"/>
                                                </connections>
                                            </button>
                                            <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="qAZ-NV-miV">
                                                <rect key="frame" x="0.0" y="191.5" width="384" height="1"/>
                                                <color key="backgroundColor" name="AppTheme_LightGrayColor_#CECECE"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="1" id="aKU-oM-vUa"/>
                                                </constraints>
                                            </view>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstItem="xyl-8s-see" firstAttribute="leading" secondItem="ZgA-na-GrD" secondAttribute="leading" constant="15" id="3WM-jN-qSN"/>
                                            <constraint firstItem="qAZ-NV-miV" firstAttribute="leading" secondItem="ZgA-na-GrD" secondAttribute="leading" id="GNE-wU-oy2"/>
                                            <constraint firstAttribute="trailing" secondItem="qAZ-NV-miV" secondAttribute="trailing" id="ToK-fy-SML"/>
                                            <constraint firstAttribute="trailing" secondItem="xyl-8s-see" secondAttribute="trailing" constant="15" id="cGL-hA-nB6"/>
                                            <constraint firstAttribute="bottom" secondItem="qAZ-NV-miV" secondAttribute="bottom" id="fqR-RH-Xih"/>
                                            <constraint firstItem="xyl-8s-see" firstAttribute="centerY" secondItem="ZgA-na-GrD" secondAttribute="centerY" id="oKW-x0-ka5"/>
                                        </constraints>
                                    </view>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="8M2-qe-Suv">
                                        <rect key="frame" x="0.0" y="607.5" width="384" height="192.5"/>
                                        <subviews>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Q4j-yq-Wr0">
                                                <rect key="frame" x="15" y="76.5" width="354" height="40"/>
                                                <color key="backgroundColor" name="AppTheme_BlueColor_#012CDA"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="40" id="QGg-Ux-vMI"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="17"/>
                                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                <state key="normal" title="Cancel">
                                                    <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                </state>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                        <real key="value" value="7"/>
                                                    </userDefinedRuntimeAttribute>
                                                </userDefinedRuntimeAttributes>
                                                <connections>
                                                    <action selector="actionCancel:" destination="1co-wh-ldK" eventType="touchUpInside" id="4XV-da-app"/>
                                                </connections>
                                            </button>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstAttribute="trailing" secondItem="Q4j-yq-Wr0" secondAttribute="trailing" constant="15" id="moG-PX-UMM"/>
                                            <constraint firstItem="Q4j-yq-Wr0" firstAttribute="centerY" secondItem="8M2-qe-Suv" secondAttribute="centerY" id="sJD-PX-TJ4"/>
                                            <constraint firstItem="Q4j-yq-Wr0" firstAttribute="leading" secondItem="8M2-qe-Suv" secondAttribute="leading" constant="15" id="to9-Zq-ARV"/>
                                        </constraints>
                                    </view>
                                </subviews>
                            </stackView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="5D8-EG-A0z"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="5D8-EG-A0z" firstAttribute="bottom" secondItem="axr-Ge-YdO" secondAttribute="bottom" id="3d2-QG-mcG"/>
                            <constraint firstItem="axr-Ge-YdO" firstAttribute="leading" secondItem="5D8-EG-A0z" secondAttribute="leading" constant="15" id="VfO-08-2aN"/>
                            <constraint firstItem="5D8-EG-A0z" firstAttribute="trailing" secondItem="axr-Ge-YdO" secondAttribute="trailing" constant="15" id="aVd-KF-W6L"/>
                            <constraint firstItem="axr-Ge-YdO" firstAttribute="top" secondItem="5D8-EG-A0z" secondAttribute="top" constant="15" id="gPq-sY-DNo"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="btnCancel" destination="Q4j-yq-Wr0" id="YYa-Mt-sb6"/>
                        <outlet property="lblSelectLang" destination="7Au-Aq-kE9" id="jKA-ak-UGO"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="DcT-Ll-Ose" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="325" y="1517"/>
        </scene>
        <!--Select Location View Controller-->
        <scene sceneID="67r-Gs-vKI">
            <objects>
                <viewController storyboardIdentifier="SelectLocationViewController" useStoryboardIdentifierAsRestorationIdentifier="YES" id="dW9-kt-qT6" customClass="SelectLocationViewController" customModule="TopCustomer" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="eqY-lP-EgG">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <stackView opaque="NO" contentMode="scaleToFill" distribution="fillProportionally" translatesAutoresizingMaskIntoConstraints="NO" id="oX3-e1-xEs">
                                <rect key="frame" x="15" y="63" width="384" height="50"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="2G6-YL-MmM">
                                        <rect key="frame" x="0.0" y="0.0" width="384" height="50"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Location" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="rVJ-B0-CXw">
                                                <rect key="frame" x="10" y="0.0" width="204" height="50"/>
                                                <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="17"/>
                                                <color key="textColor" name="AppTheme_BlueColor_#012CDA"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <button opaque="NO" contentMode="scaleToFill" semanticContentAttribute="forceRightToLeft" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" hasAttributedTitle="YES" translatesAutoresizingMaskIntoConstraints="NO" id="x0W-GR-V9S">
                                                <rect key="frame" x="234" y="0.0" width="150" height="50"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="150" id="QtA-ie-2je"/>
                                                </constraints>
                                                <inset key="imageEdgeInsets" minX="30" minY="0.0" maxX="0.0" maxY="0.0"/>
                                                <state key="normal" image="plus">
                                                    <attributedString key="attributedTitle">
                                                        <fragment content="New Address">
                                                            <attributes>
                                                                <color key="NSColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <font key="NSFont" metaFont="system" size="14"/>
                                                                <integer key="NSUnderline" value="1"/>
                                                            </attributes>
                                                        </fragment>
                                                    </attributedString>
                                                </state>
                                                <connections>
                                                    <action selector="actionNewAddress:" destination="dW9-kt-qT6" eventType="touchUpInside" id="5Zb-Cp-O2R"/>
                                                </connections>
                                            </button>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstAttribute="trailing" secondItem="x0W-GR-V9S" secondAttribute="trailing" id="BBh-Lp-5Nb"/>
                                            <constraint firstItem="rVJ-B0-CXw" firstAttribute="leading" secondItem="2G6-YL-MmM" secondAttribute="leading" constant="10" id="DCf-4W-7yq"/>
                                            <constraint firstAttribute="bottom" secondItem="x0W-GR-V9S" secondAttribute="bottom" id="HNN-dW-lk7"/>
                                            <constraint firstItem="x0W-GR-V9S" firstAttribute="leading" secondItem="rVJ-B0-CXw" secondAttribute="trailing" constant="20" id="i7u-DE-SiA"/>
                                            <constraint firstItem="x0W-GR-V9S" firstAttribute="top" secondItem="2G6-YL-MmM" secondAttribute="top" id="j5B-xE-9G6"/>
                                            <constraint firstItem="rVJ-B0-CXw" firstAttribute="top" secondItem="2G6-YL-MmM" secondAttribute="top" id="l6O-bn-4nv"/>
                                            <constraint firstAttribute="height" constant="50" id="vzv-Rx-clP"/>
                                            <constraint firstAttribute="bottom" secondItem="rVJ-B0-CXw" secondAttribute="bottom" id="ycj-go-7jZ"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <constraints>
                                    <constraint firstAttribute="trailing" secondItem="2G6-YL-MmM" secondAttribute="trailing" id="IGc-uk-kwe"/>
                                    <constraint firstItem="2G6-YL-MmM" firstAttribute="leading" secondItem="oX3-e1-xEs" secondAttribute="leading" id="cqM-FX-Buk"/>
                                    <constraint firstItem="2G6-YL-MmM" firstAttribute="top" secondItem="oX3-e1-xEs" secondAttribute="top" id="eII-qk-KfO"/>
                                    <constraint firstAttribute="bottom" secondItem="2G6-YL-MmM" secondAttribute="bottom" id="hFe-NT-lOr"/>
                                </constraints>
                            </stackView>
                            <tableView clipsSubviews="YES" contentMode="scaleToFill" bounces="NO" alwaysBounceVertical="YES" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" bouncesZoom="NO" dataMode="prototypes" style="plain" separatorStyle="none" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="-1" estimatedSectionHeaderHeight="-1" sectionFooterHeight="-1" estimatedSectionFooterHeight="-1" translatesAutoresizingMaskIntoConstraints="NO" id="qhJ-Wc-gg4">
                                <rect key="frame" x="15" y="113" width="384" height="749"/>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <prototypes>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" preservesSuperviewLayoutMargins="YES" selectionStyle="default" indentationWidth="10" reuseIdentifier="AddressListTableViewCell" rowHeight="99" id="y9B-CW-bMa" customClass="AddressListTableViewCell" customModule="TopCustomer" customModuleProvider="target">
                                        <rect key="frame" x="0.0" y="50" width="384" height="99"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" preservesSuperviewLayoutMargins="YES" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="y9B-CW-bMa" id="djk-mY-6JZ">
                                            <rect key="frame" x="0.0" y="0.0" width="384" height="99"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Y8b-tu-SIx">
                                                    <rect key="frame" x="0.0" y="0.0" width="384" height="99"/>
                                                    <subviews>
                                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="location_pin_deselected" translatesAutoresizingMaskIntoConstraints="NO" id="s50-a4-Esd">
                                                            <rect key="frame" x="10" y="37" width="19" height="25"/>
                                                            <constraints>
                                                                <constraint firstAttribute="width" constant="19" id="NRh-QE-Pw3"/>
                                                                <constraint firstAttribute="height" constant="25" id="lyR-xG-inY"/>
                                                            </constraints>
                                                        </imageView>
                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Home" lineBreakMode="wordWrap" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="x75-mk-FwV" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                            <rect key="frame" x="44" y="10" width="275" height="62"/>
                                                            <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="11"/>
                                                            <nil key="textColor"/>
                                                            <nil key="highlightedColor"/>
                                                        </label>
                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="252" text="112522, Takhasusi street" lineBreakMode="wordWrap" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="A1W-wh-ART" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                            <rect key="frame" x="44" y="77" width="275" height="11"/>
                                                            <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="11"/>
                                                            <color key="textColor" name="AppTheme_LightGrayColor_#CECECE"/>
                                                            <nil key="highlightedColor"/>
                                                        </label>
                                                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="right" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="3jv-7y-fyX" customClass="MaterialLocalizeButton" customModule="TopCustomer" customModuleProvider="target">
                                                            <rect key="frame" x="329" y="23.5" width="35" height="35"/>
                                                            <constraints>
                                                                <constraint firstAttribute="height" constant="35" id="dQm-Lm-Jui"/>
                                                                <constraint firstAttribute="width" constant="35" id="va6-g1-Rn7"/>
                                                            </constraints>
                                                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                            <state key="normal" image="icn_delete_grey"/>
                                                        </button>
                                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="P4a-yD-oVb">
                                                            <rect key="frame" x="0.0" y="98" width="384" height="1"/>
                                                            <color key="backgroundColor" name="AppTheme_LightGrayColor_#CECECE"/>
                                                            <constraints>
                                                                <constraint firstAttribute="height" constant="1" id="5GZ-0t-9DZ"/>
                                                            </constraints>
                                                        </view>
                                                    </subviews>
                                                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                    <constraints>
                                                        <constraint firstAttribute="trailing" secondItem="P4a-yD-oVb" secondAttribute="trailing" id="6Kw-Ub-0kY"/>
                                                        <constraint firstItem="P4a-yD-oVb" firstAttribute="leading" secondItem="Y8b-tu-SIx" secondAttribute="leading" id="AZe-Hu-uKp"/>
                                                        <constraint firstAttribute="trailing" secondItem="3jv-7y-fyX" secondAttribute="trailing" constant="20" id="FWL-kd-cds"/>
                                                        <constraint firstItem="P4a-yD-oVb" firstAttribute="top" secondItem="A1W-wh-ART" secondAttribute="bottom" constant="10" id="L9E-L1-9Fm"/>
                                                        <constraint firstItem="3jv-7y-fyX" firstAttribute="leading" secondItem="x75-mk-FwV" secondAttribute="trailing" constant="10" id="SEH-aY-9Q4"/>
                                                        <constraint firstItem="x75-mk-FwV" firstAttribute="top" secondItem="Y8b-tu-SIx" secondAttribute="top" constant="10" id="SSi-Hb-ZTg"/>
                                                        <constraint firstItem="A1W-wh-ART" firstAttribute="top" secondItem="x75-mk-FwV" secondAttribute="bottom" constant="5" id="UOd-Vb-Sjc"/>
                                                        <constraint firstItem="s50-a4-Esd" firstAttribute="centerY" secondItem="Y8b-tu-SIx" secondAttribute="centerY" id="UQd-yt-HbJ"/>
                                                        <constraint firstItem="A1W-wh-ART" firstAttribute="leading" secondItem="x75-mk-FwV" secondAttribute="leading" id="bSS-wy-OY5"/>
                                                        <constraint firstItem="x75-mk-FwV" firstAttribute="leading" secondItem="s50-a4-Esd" secondAttribute="trailing" constant="15" id="jRh-Ig-lk4"/>
                                                        <constraint firstItem="s50-a4-Esd" firstAttribute="leading" secondItem="Y8b-tu-SIx" secondAttribute="leading" constant="10" id="jyA-lk-jXj"/>
                                                        <constraint firstItem="A1W-wh-ART" firstAttribute="trailing" secondItem="x75-mk-FwV" secondAttribute="trailing" id="tzZ-Uo-JLg"/>
                                                        <constraint firstAttribute="bottom" secondItem="P4a-yD-oVb" secondAttribute="bottom" id="wcm-Yy-zHe"/>
                                                        <constraint firstItem="3jv-7y-fyX" firstAttribute="centerY" secondItem="x75-mk-FwV" secondAttribute="centerY" id="yOm-bo-Rgi"/>
                                                    </constraints>
                                                </view>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="Y8b-tu-SIx" firstAttribute="top" secondItem="djk-mY-6JZ" secondAttribute="top" id="5Wt-14-V5a"/>
                                                <constraint firstItem="Y8b-tu-SIx" firstAttribute="leading" secondItem="djk-mY-6JZ" secondAttribute="leading" id="dX9-s8-VIV"/>
                                                <constraint firstAttribute="bottom" secondItem="Y8b-tu-SIx" secondAttribute="bottom" id="oZa-vP-7W8"/>
                                                <constraint firstAttribute="trailing" secondItem="Y8b-tu-SIx" secondAttribute="trailing" id="q1p-ZN-HL5"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                        <connections>
                                            <outlet property="btnDelete" destination="3jv-7y-fyX" id="jbP-GB-3kS"/>
                                            <outlet property="imgType" destination="s50-a4-Esd" id="GcP-Ut-Liz"/>
                                            <outlet property="lblAddress" destination="A1W-wh-ART" id="6Qe-9i-qXs"/>
                                            <outlet property="lblType" destination="x75-mk-FwV" id="HGe-HY-tLt"/>
                                        </connections>
                                    </tableViewCell>
                                </prototypes>
                                <connections>
                                    <outlet property="dataSource" destination="dW9-kt-qT6" id="CNY-7c-uZs"/>
                                    <outlet property="delegate" destination="dW9-kt-qT6" id="Rcm-FU-1AB"/>
                                </connections>
                            </tableView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="S93-gP-Jpc"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="qhJ-Wc-gg4" firstAttribute="leading" secondItem="oX3-e1-xEs" secondAttribute="leading" id="CV7-77-QE3"/>
                            <constraint firstItem="S93-gP-Jpc" firstAttribute="bottom" secondItem="qhJ-Wc-gg4" secondAttribute="bottom" id="HrV-2P-8HG"/>
                            <constraint firstItem="S93-gP-Jpc" firstAttribute="trailing" secondItem="oX3-e1-xEs" secondAttribute="trailing" constant="15" id="UxN-k4-fTy"/>
                            <constraint firstItem="qhJ-Wc-gg4" firstAttribute="top" secondItem="oX3-e1-xEs" secondAttribute="bottom" id="c6p-o7-DRr"/>
                            <constraint firstItem="oX3-e1-xEs" firstAttribute="leading" secondItem="S93-gP-Jpc" secondAttribute="leading" constant="15" id="gT5-ys-hSR"/>
                            <constraint firstItem="qhJ-Wc-gg4" firstAttribute="trailing" secondItem="oX3-e1-xEs" secondAttribute="trailing" id="t0p-Cu-hSd"/>
                            <constraint firstItem="oX3-e1-xEs" firstAttribute="top" secondItem="S93-gP-Jpc" secondAttribute="top" constant="15" id="ysh-2l-nTY"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="btnNewAddress" destination="x0W-GR-V9S" id="sj4-FH-PwM"/>
                        <outlet property="lblLocation" destination="rVJ-B0-CXw" id="0aD-SG-rMe"/>
                        <outlet property="tblAddress" destination="qhJ-Wc-gg4" id="18k-0t-CT7"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="goP-BV-jqu" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1289.8550724637682" y="1516.7410714285713"/>
        </scene>
        <!--History Orders View Controller-->
        <scene sceneID="d41-Tr-0IB">
            <objects>
                <viewController storyboardIdentifier="HistoryOrdersViewController" useStoryboardIdentifierAsRestorationIdentifier="YES" id="VE8-AA-XXu" customClass="HistoryOrdersViewController" customModule="TopCustomer" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="egZ-zi-jWX">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="vGd-oo-CSg">
                                <rect key="frame" x="0.0" y="108" width="414" height="754"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="bXe-nb-ips">
                                        <rect key="frame" x="0.0" y="218.5" width="414" height="317"/>
                                        <subviews>
                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="no_order_box" translatesAutoresizingMaskIntoConstraints="NO" id="yQH-TK-v4P" customClass="MaterialLocalizeImageView" customModule="TopCustomer" customModuleProvider="target">
                                                <rect key="frame" x="147" y="20" width="120" height="120"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="120" id="ym9-Yw-Lev"/>
                                                    <constraint firstAttribute="height" constant="120" id="z2n-B9-akn"/>
                                                </constraints>
                                            </imageView>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="No Order Found !" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="XD0-Mk-eU7">
                                                <rect key="frame" x="20" y="182" width="374" height="15"/>
                                                <fontDescription key="fontDescription" name="LoewNextArabic-ExtraBold" family="Loew Next Arabic" pointSize="15"/>
                                                <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Looks like you haven’t made your order yet." textAlignment="center" lineBreakMode="wordWrap" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="nOo-zs-80E">
                                                <rect key="frame" x="20" y="212" width="374" height="15"/>
                                                <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="15"/>
                                                <color key="textColor" name="AppTheme_SubLabelLightGrayColor_#B2B2B2"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="0Xv-QY-sed" customClass="CustomRoundedButtton" customModule="TopCustomer" customModuleProvider="target">
                                                <rect key="frame" x="132" y="277" width="150" height="40"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="150" id="wZA-ao-YDc"/>
                                                    <constraint firstAttribute="height" constant="40" id="x0q-oH-Hoe"/>
                                                </constraints>
                                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                <state key="normal" title="Go Shopping"/>
                                                <connections>
                                                    <action selector="btnGoShoppingAction:" destination="VE8-AA-XXu" eventType="touchUpInside" id="hX4-JY-w0b"/>
                                                </connections>
                                            </button>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstItem="nOo-zs-80E" firstAttribute="top" secondItem="XD0-Mk-eU7" secondAttribute="bottom" constant="15" id="1sk-p3-FqX"/>
                                            <constraint firstItem="nOo-zs-80E" firstAttribute="trailing" secondItem="XD0-Mk-eU7" secondAttribute="trailing" id="4kS-Mq-g6R"/>
                                            <constraint firstAttribute="bottom" secondItem="0Xv-QY-sed" secondAttribute="bottom" id="8Ab-Wz-oeI"/>
                                            <constraint firstItem="XD0-Mk-eU7" firstAttribute="centerX" secondItem="bXe-nb-ips" secondAttribute="centerX" id="AE0-Cd-nC6"/>
                                            <constraint firstItem="yQH-TK-v4P" firstAttribute="top" secondItem="bXe-nb-ips" secondAttribute="top" constant="20" id="IBJ-IL-pWT"/>
                                            <constraint firstItem="nOo-zs-80E" firstAttribute="leading" secondItem="XD0-Mk-eU7" secondAttribute="leading" id="OCz-aq-F7f"/>
                                            <constraint firstItem="XD0-Mk-eU7" firstAttribute="leading" secondItem="bXe-nb-ips" secondAttribute="leading" constant="20" id="R7Q-cy-K87"/>
                                            <constraint firstItem="XD0-Mk-eU7" firstAttribute="top" secondItem="yQH-TK-v4P" secondAttribute="bottom" constant="42" id="VrX-Wi-XG9"/>
                                            <constraint firstItem="0Xv-QY-sed" firstAttribute="centerX" secondItem="bXe-nb-ips" secondAttribute="centerX" id="WjA-yt-X2l"/>
                                            <constraint firstAttribute="trailing" secondItem="XD0-Mk-eU7" secondAttribute="trailing" constant="20" id="dT7-T2-XdS"/>
                                            <constraint firstItem="0Xv-QY-sed" firstAttribute="top" secondItem="nOo-zs-80E" secondAttribute="bottom" constant="50" id="eex-gA-l0c"/>
                                            <constraint firstItem="yQH-TK-v4P" firstAttribute="centerX" secondItem="bXe-nb-ips" secondAttribute="centerX" id="to4-FR-Pe3"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstItem="bXe-nb-ips" firstAttribute="centerY" secondItem="vGd-oo-CSg" secondAttribute="centerY" id="8nV-Lu-etv"/>
                                    <constraint firstAttribute="trailing" secondItem="bXe-nb-ips" secondAttribute="trailing" id="Cpf-9G-Xfo"/>
                                    <constraint firstItem="bXe-nb-ips" firstAttribute="centerX" secondItem="vGd-oo-CSg" secondAttribute="centerX" id="Swe-QP-lhM"/>
                                    <constraint firstItem="bXe-nb-ips" firstAttribute="leading" secondItem="vGd-oo-CSg" secondAttribute="leading" id="sfv-JY-slK"/>
                                </constraints>
                            </view>
                            <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" dataMode="prototypes" style="plain" separatorStyle="none" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="-1" estimatedSectionHeaderHeight="-1" sectionFooterHeight="-1" estimatedSectionFooterHeight="-1" translatesAutoresizingMaskIntoConstraints="NO" id="FSS-8d-xyP">
                                <rect key="frame" x="0.0" y="108" width="414" height="754"/>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                            </tableView>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="hw9-0x-TYU" customClass="CustomTopViewForShadow" customModule="TopCustomer" customModuleProvider="target">
                                <rect key="frame" x="0.0" y="48" width="414" height="60"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="d7b-Xn-azi" customClass="MaterialLocalizeButton" customModule="TopCustomer" customModuleProvider="target">
                                        <rect key="frame" x="25" y="12.5" width="35" height="35"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="35" id="9cQ-OV-qag"/>
                                            <constraint firstAttribute="height" constant="35" id="kL0-Dg-XKZ"/>
                                        </constraints>
                                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                        <state key="normal" image="icn_back"/>
                                        <connections>
                                            <action selector="actionBack:" destination="VE8-AA-XXu" eventType="touchUpInside" id="rjd-GA-SLi"/>
                                        </connections>
                                    </button>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="History Orders" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="9va-xn-N4N">
                                        <rect key="frame" x="121" y="18.5" width="172.5" height="23"/>
                                        <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="23"/>
                                        <color key="textColor" name="AppTheme_BlueColor_#012CDA"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstItem="9va-xn-N4N" firstAttribute="centerX" secondItem="hw9-0x-TYU" secondAttribute="centerX" id="CMd-UJ-eOI"/>
                                    <constraint firstAttribute="height" constant="60" id="GwQ-FE-86n"/>
                                    <constraint firstItem="d7b-Xn-azi" firstAttribute="leading" secondItem="hw9-0x-TYU" secondAttribute="leading" constant="25" id="dWj-Qv-Wkm"/>
                                    <constraint firstItem="9va-xn-N4N" firstAttribute="centerY" secondItem="hw9-0x-TYU" secondAttribute="centerY" id="pX4-OS-mU7"/>
                                    <constraint firstItem="d7b-Xn-azi" firstAttribute="centerY" secondItem="hw9-0x-TYU" secondAttribute="centerY" id="pwI-B5-5Rd"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="qr0-la-ubO"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="hw9-0x-TYU" firstAttribute="trailing" secondItem="qr0-la-ubO" secondAttribute="trailing" id="38O-Pg-dE2"/>
                            <constraint firstItem="vGd-oo-CSg" firstAttribute="leading" secondItem="qr0-la-ubO" secondAttribute="leading" id="5Cc-lF-aWi"/>
                            <constraint firstItem="qr0-la-ubO" firstAttribute="bottom" secondItem="vGd-oo-CSg" secondAttribute="bottom" id="5r9-uE-7TC"/>
                            <constraint firstItem="FSS-8d-xyP" firstAttribute="leading" secondItem="qr0-la-ubO" secondAttribute="leading" id="6SF-Fe-dAe"/>
                            <constraint firstItem="vGd-oo-CSg" firstAttribute="top" secondItem="hw9-0x-TYU" secondAttribute="bottom" id="HAt-qP-vfd"/>
                            <constraint firstItem="qr0-la-ubO" firstAttribute="bottom" secondItem="FSS-8d-xyP" secondAttribute="bottom" id="Lc8-t2-cwx"/>
                            <constraint firstItem="hw9-0x-TYU" firstAttribute="top" secondItem="qr0-la-ubO" secondAttribute="top" id="PD1-3a-M2D"/>
                            <constraint firstItem="vGd-oo-CSg" firstAttribute="trailing" secondItem="qr0-la-ubO" secondAttribute="trailing" id="Pwf-7W-Efo"/>
                            <constraint firstItem="qr0-la-ubO" firstAttribute="trailing" secondItem="FSS-8d-xyP" secondAttribute="trailing" id="XeI-B8-t5Y"/>
                            <constraint firstItem="FSS-8d-xyP" firstAttribute="top" secondItem="hw9-0x-TYU" secondAttribute="bottom" id="ltt-of-yFj"/>
                            <constraint firstItem="hw9-0x-TYU" firstAttribute="leading" secondItem="qr0-la-ubO" secondAttribute="leading" id="nTf-s7-ihc"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="btnGoShopping" destination="0Xv-QY-sed" id="3Dp-wX-dm0"/>
                        <outlet property="lblLooksLike" destination="nOo-zs-80E" id="jnf-Cz-WnS"/>
                        <outlet property="lblNoOrderFound" destination="XD0-Mk-eU7" id="Es3-KS-ngi"/>
                        <outlet property="lblTitle" destination="9va-xn-N4N" id="iN0-rh-22F"/>
                        <outlet property="tblHistoryOrders" destination="FSS-8d-xyP" id="1Ef-xU-xCH"/>
                        <outlet property="viewNoData" destination="vGd-oo-CSg" id="JbQ-cg-Nce"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="ZIR-LG-cMs" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="324.**************" y="2250"/>
        </scene>
        <!--History Order Details View Controller-->
        <scene sceneID="Gr9-10-lUS">
            <objects>
                <viewController storyboardIdentifier="HistoryOrderDetailsViewController" useStoryboardIdentifierAsRestorationIdentifier="YES" id="Wkn-Jg-lmO" customClass="HistoryOrderDetailsViewController" customModule="TopCustomer" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="dNE-eK-h2v">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="sWL-xt-Qn7">
                                <rect key="frame" x="0.0" y="48" width="414" height="814"/>
                                <connections>
                                    <action selector="btnDimViewAction:" destination="Wkn-Jg-lmO" eventType="touchUpInside" id="rBl-40-Asa"/>
                                </connections>
                            </button>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="aiM-8d-3JA">
                                <rect key="frame" x="15" y="118" width="384" height="674"/>
                                <subviews>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="History Orders" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="neC-W5-Dqb">
                                        <rect key="frame" x="106" y="23" width="172.5" height="23"/>
                                        <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="23"/>
                                        <color key="textColor" name="AppTheme_BlueColor_#012CDA"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" translatesAutoresizingMaskIntoConstraints="NO" id="O8g-C7-JBK">
                                        <rect key="frame" x="0.0" y="46" width="384" height="628"/>
                                        <subviews>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Kps-TR-ZD1">
                                                <rect key="frame" x="0.0" y="22" width="384" height="46"/>
                                                <subviews>
                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="2" translatesAutoresizingMaskIntoConstraints="NO" id="MhK-5e-Brg">
                                                        <rect key="frame" x="27" y="10" width="200" height="26"/>
                                                        <subviews>
                                                            <stackView opaque="NO" contentMode="scaleToFill" spacing="3" translatesAutoresizingMaskIntoConstraints="NO" id="1t4-bf-b8o">
                                                                <rect key="frame" x="0.0" y="0.0" width="200" height="12"/>
                                                                <subviews>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="252" verticalHuggingPriority="251" text="Order No. :" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="nAC-UM-uFq">
                                                                        <rect key="frame" x="0.0" y="0.0" width="65" height="12"/>
                                                                        <fontDescription key="fontDescription" name="LoewNextArabic-ExtraBold" family="Loew Next Arabic" pointSize="12"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="77829022" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="W98-KF-04Z">
                                                                        <rect key="frame" x="68" y="0.0" width="132" height="12"/>
                                                                        <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="12"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                            </stackView>
                                                            <stackView opaque="NO" contentMode="scaleToFill" spacing="3" translatesAutoresizingMaskIntoConstraints="NO" id="46H-hZ-QVe">
                                                                <rect key="frame" x="0.0" y="14" width="200" height="12"/>
                                                                <subviews>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="252" verticalHuggingPriority="251" text="Order Date:" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="uOn-Xx-4ev">
                                                                        <rect key="frame" x="0.0" y="0.0" width="70.5" height="12"/>
                                                                        <fontDescription key="fontDescription" name="LoewNextArabic-ExtraBold" family="Loew Next Arabic" pointSize="12"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="18-02-2022 3:05 P.M." textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="aek-A1-9jL">
                                                                        <rect key="frame" x="73.5" y="0.0" width="126.5" height="12"/>
                                                                        <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="12"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                            </stackView>
                                                        </subviews>
                                                    </stackView>
                                                    <button hidden="YES" opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" hasAttributedTitle="YES" translatesAutoresizingMaskIntoConstraints="NO" id="f8B-sn-IDV">
                                                        <rect key="frame" x="317" y="0.0" width="40" height="26.5"/>
                                                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                        <state key="normal">
                                                            <attributedString key="attributedTitle">
                                                                <fragment content="Invoice">
                                                                    <attributes>
                                                                        <color key="NSColor" name="AppTheme_BlueColor_#012CDA"/>
                                                                        <font key="NSFont" metaFont="cellTitle"/>
                                                                        <integer key="NSUnderline" value="1"/>
                                                                    </attributes>
                                                                </fragment>
                                                            </attributedString>
                                                        </state>
                                                        <connections>
                                                            <action selector="actionInvoice:" destination="Wkn-Jg-lmO" eventType="touchUpInside" id="BkI-kj-gpP"/>
                                                        </connections>
                                                    </button>
                                                </subviews>
                                                <color key="backgroundColor" name="AppTheme_LightGrayOrderColor_#EFEFEF"/>
                                                <constraints>
                                                    <constraint firstItem="f8B-sn-IDV" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="MhK-5e-Brg" secondAttribute="trailing" constant="10" id="2wH-B6-KXa"/>
                                                    <constraint firstItem="f8B-sn-IDV" firstAttribute="top" secondItem="Kps-TR-ZD1" secondAttribute="top" id="Da8-4G-DdB"/>
                                                    <constraint firstAttribute="bottom" secondItem="MhK-5e-Brg" secondAttribute="bottom" constant="10" id="EaA-Bd-4Rs"/>
                                                    <constraint firstItem="MhK-5e-Brg" firstAttribute="top" secondItem="Kps-TR-ZD1" secondAttribute="top" constant="10" id="Fmc-VG-WO4"/>
                                                    <constraint firstItem="MhK-5e-Brg" firstAttribute="leading" secondItem="Kps-TR-ZD1" secondAttribute="leading" constant="27" id="YJP-3Y-aOE"/>
                                                    <constraint firstAttribute="trailing" secondItem="f8B-sn-IDV" secondAttribute="trailing" constant="27" id="m15-gZ-O2b"/>
                                                </constraints>
                                            </view>
                                            <tableView clipsSubviews="YES" contentMode="scaleToFill" bounces="NO" scrollEnabled="NO" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" bouncesZoom="NO" dataMode="prototypes" style="plain" separatorStyle="none" allowsSelection="NO" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="-1" estimatedSectionHeaderHeight="-1" sectionFooterHeight="-1" estimatedSectionFooterHeight="-1" translatesAutoresizingMaskIntoConstraints="NO" id="1n2-3i-VOh">
                                                <rect key="frame" x="0.0" y="88" width="384" height="100"/>
                                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="100" id="4qS-0r-QtK"/>
                                                </constraints>
                                            </tableView>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Delivered" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="n46-vk-syB">
                                                <rect key="frame" x="27" y="225" width="63.5" height="13"/>
                                                <fontDescription key="fontDescription" name="LoewNextArabic-ExtraBold" family="Loew Next Arabic" pointSize="13"/>
                                                <color key="textColor" name="AppTheme_SelectedTabColor_#F4BA45"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <button hidden="YES" opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" hasAttributedTitle="YES" translatesAutoresizingMaskIntoConstraints="NO" id="PKk-fq-RdN">
                                                <rect key="frame" x="312" y="218" width="45" height="26.5"/>
                                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                <state key="normal">
                                                    <attributedString key="attributedTitle">
                                                        <fragment content="Reorder">
                                                            <attributes>
                                                                <color key="NSColor" name="AppTheme_BlueColor_#012CDA"/>
                                                                <font key="NSFont" metaFont="cellTitle"/>
                                                                <integer key="NSUnderline" value="1"/>
                                                            </attributes>
                                                        </fragment>
                                                    </attributedString>
                                                </state>
                                                <connections>
                                                    <action selector="actionReOrder:" destination="Wkn-Jg-lmO" eventType="touchUpInside" id="Xf0-Il-kIO"/>
                                                </connections>
                                            </button>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="CCi-xj-VQa">
                                                <rect key="frame" x="18" y="274.5" width="348" height="178.5"/>
                                                <subviews>
                                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" hasAttributedTitle="YES" translatesAutoresizingMaskIntoConstraints="NO" id="hra-mN-CGK">
                                                        <rect key="frame" x="13" y="10" width="30" height="26.5"/>
                                                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                        <state key="normal">
                                                            <attributedString key="attributedTitle">
                                                                <fragment content="Help">
                                                                    <attributes>
                                                                        <color key="NSColor" name="AppTheme_BlueColor_#012CDA"/>
                                                                        <font key="NSFont" metaFont="cellTitle"/>
                                                                        <integer key="NSUnderline" value="1"/>
                                                                    </attributes>
                                                                </fragment>
                                                            </attributedString>
                                                        </state>
                                                        <connections>
                                                            <action selector="actionHelp:" destination="Wkn-Jg-lmO" eventType="touchUpInside" id="gUh-iC-H0m"/>
                                                        </connections>
                                                    </button>
                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="10" translatesAutoresizingMaskIntoConstraints="NO" id="YKP-7h-OY1">
                                                        <rect key="frame" x="13" y="51.5" width="322" height="99"/>
                                                        <subviews>
                                                            <stackView opaque="NO" contentMode="scaleToFill" spacing="3" translatesAutoresizingMaskIntoConstraints="NO" id="nCq-OX-D0G">
                                                                <rect key="frame" x="0.0" y="0.0" width="322" height="12"/>
                                                                <subviews>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="252" verticalHuggingPriority="251" text="Subtotal: " lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="PJh-ff-rCI" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                                        <rect key="frame" x="0.0" y="0.0" width="58" height="12"/>
                                                                        <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="12"/>
                                                                        <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="100 SAR" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="OJs-cf-r5H" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                                        <rect key="frame" x="61" y="0.0" width="261" height="12"/>
                                                                        <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="12"/>
                                                                        <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                            </stackView>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="0Ka-RC-ckR">
                                                                <rect key="frame" x="0.0" y="22" width="322" height="1"/>
                                                                <color key="backgroundColor" name="AppTheme_LightGrayColor_#B4B2B2"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="1" id="181-ii-3Bf"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                                <nil key="textColor"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                            <stackView hidden="YES" opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="3" translatesAutoresizingMaskIntoConstraints="NO" id="oXx-ww-Icc">
                                                                <rect key="frame" x="0.0" y="28" width="322" height="0.0"/>
                                                                <subviews>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="252" verticalHuggingPriority="251" text="Discount" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="6k6-8Q-UME" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                                        <rect key="frame" x="0.0" y="0.0" width="159.5" height="0.0"/>
                                                                        <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="12"/>
                                                                        <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="0 SAR" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="RKN-mo-cxM" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                                        <rect key="frame" x="162.5" y="0.0" width="159.5" height="0.0"/>
                                                                        <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="12"/>
                                                                        <color key="textColor" name="AppTheme_DiscountGreenColor_#05B13E"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                            </stackView>
                                                            <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="230-zU-WWI">
                                                                <rect key="frame" x="0.0" y="28" width="322" height="1"/>
                                                                <color key="backgroundColor" name="AppTheme_LightGrayColor_#B4B2B2"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="1" id="4Bq-ys-ngC"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                                <nil key="textColor"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                            <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="3" translatesAutoresizingMaskIntoConstraints="NO" id="vS1-tO-kYZ">
                                                                <rect key="frame" x="0.0" y="33" width="322" height="12"/>
                                                                <subviews>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="252" verticalHuggingPriority="251" text="Delivery: " lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="5Sj-XW-URn" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                                        <rect key="frame" x="0.0" y="0.0" width="159.5" height="12"/>
                                                                        <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="12"/>
                                                                        <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="15 SAR" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Mkd-zB-RAZ" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                                        <rect key="frame" x="162.5" y="0.0" width="159.5" height="12"/>
                                                                        <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="12"/>
                                                                        <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                            </stackView>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="le4-Ob-T0C">
                                                                <rect key="frame" x="0.0" y="55" width="322" height="1"/>
                                                                <color key="backgroundColor" name="AppTheme_LightGrayColor_#B4B2B2"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="1" id="4WQ-Pz-OwE"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                                <nil key="textColor"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                            <stackView hidden="YES" opaque="NO" contentMode="scaleToFill" spacing="3" translatesAutoresizingMaskIntoConstraints="NO" id="fth-H8-g59">
                                                                <rect key="frame" x="0.0" y="61" width="322" height="0.0"/>
                                                                <subviews>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="252" verticalHuggingPriority="251" text="Delivery Discount" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="hME-H5-dqm" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                                        <rect key="frame" x="0.0" y="0.0" width="109" height="0.0"/>
                                                                        <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="12"/>
                                                                        <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="15 SAR" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="hla-CR-KsX" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                                        <rect key="frame" x="112" y="0.0" width="210" height="0.0"/>
                                                                        <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="12"/>
                                                                        <color key="textColor" name="AppTheme_DiscountGreenColor_#05B13E"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                            </stackView>
                                                            <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="zqm-Rc-Ngy">
                                                                <rect key="frame" x="0.0" y="61" width="322" height="1"/>
                                                                <color key="backgroundColor" name="AppTheme_LightGrayColor_#B4B2B2"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="1" id="uQc-wE-I4f"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                                <nil key="textColor"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                            <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="3" translatesAutoresizingMaskIntoConstraints="NO" id="Hfo-Cb-yaG">
                                                                <rect key="frame" x="0.0" y="66" width="322" height="12"/>
                                                                <subviews>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="252" verticalHuggingPriority="251" text="Total: " lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="a6A-Np-cUx" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                                        <rect key="frame" x="0.0" y="0.0" width="159.5" height="12"/>
                                                                        <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="12"/>
                                                                        <color key="textColor" name="AppTheme_BlueColor_#012CDA"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="115 SAR" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="ML9-6B-8Br" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                                        <rect key="frame" x="162.5" y="0.0" width="159.5" height="12"/>
                                                                        <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="12"/>
                                                                        <color key="textColor" name="AppTheme_BlueColor_#012CDA"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                            </stackView>
                                                            <stackView opaque="NO" contentMode="scaleToFill" spacing="3" translatesAutoresizingMaskIntoConstraints="NO" id="h94-rL-9Oy">
                                                                <rect key="frame" x="0.0" y="88" width="322" height="11"/>
                                                                <subviews>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="252" verticalHuggingPriority="251" text="*(VAT included 15.0%: 000 SAR)" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Qdk-aO-5WB" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                                        <rect key="frame" x="0.0" y="0.0" width="322" height="11"/>
                                                                        <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="11"/>
                                                                        <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                            </stackView>
                                                        </subviews>
                                                    </stackView>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="252" verticalHuggingPriority="251" text="All prices Includes VAT" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="dfJ-ia-pam" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                        <rect key="frame" x="13" y="155.5" width="322" height="10"/>
                                                        <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="10"/>
                                                        <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                </subviews>
                                                <color key="backgroundColor" name="AppTheme_LightGrayOrderColor_#EFEFEF"/>
                                                <constraints>
                                                    <constraint firstItem="YKP-7h-OY1" firstAttribute="leading" secondItem="CCi-xj-VQa" secondAttribute="leading" constant="13" id="92F-Mm-sA2"/>
                                                    <constraint firstItem="hra-mN-CGK" firstAttribute="leading" secondItem="YKP-7h-OY1" secondAttribute="leading" id="Ftj-Uq-265"/>
                                                    <constraint firstItem="hra-mN-CGK" firstAttribute="top" secondItem="CCi-xj-VQa" secondAttribute="top" constant="10" id="I2Y-MC-Hta"/>
                                                    <constraint firstAttribute="bottom" secondItem="dfJ-ia-pam" secondAttribute="bottom" constant="13" id="PcQ-NQ-cKs"/>
                                                    <constraint firstItem="dfJ-ia-pam" firstAttribute="leading" secondItem="CCi-xj-VQa" secondAttribute="leading" constant="13" id="QiY-wF-Z63"/>
                                                    <constraint firstAttribute="trailing" secondItem="YKP-7h-OY1" secondAttribute="trailing" constant="13" id="WBg-2m-IlF"/>
                                                    <constraint firstAttribute="trailing" secondItem="dfJ-ia-pam" secondAttribute="trailing" constant="13" id="Yeh-2N-Jva"/>
                                                    <constraint firstItem="YKP-7h-OY1" firstAttribute="top" secondItem="hra-mN-CGK" secondAttribute="bottom" constant="15" id="x1L-Hc-Sbh"/>
                                                    <constraint firstItem="dfJ-ia-pam" firstAttribute="centerX" secondItem="CCi-xj-VQa" secondAttribute="centerX" id="xeC-PP-Kdi"/>
                                                    <constraint firstItem="dfJ-ia-pam" firstAttribute="top" secondItem="YKP-7h-OY1" secondAttribute="bottom" constant="5" id="zob-f4-ktH"/>
                                                </constraints>
                                            </view>
                                            <view clipsSubviews="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="k5b-Im-bXm">
                                                <rect key="frame" x="18" y="468" width="348" height="94"/>
                                                <subviews>
                                                    <label opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Payment Method" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="BeC-xH-e7D" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="15" width="348" height="14"/>
                                                        <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="14"/>
                                                        <color key="textColor" name="AppTheme_SelectedTabColor_#F4BA45"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <view clipsSubviews="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="sag-je-reA">
                                                        <rect key="frame" x="0.0" y="44" width="348" height="40"/>
                                                        <subviews>
                                                            <label opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Apple Pay" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Afp-lr-9G5" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                                <rect key="frame" x="15" y="13.5" width="318" height="13"/>
                                                                <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="13"/>
                                                                <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                        </subviews>
                                                        <color key="backgroundColor" name="AppTheme_LightGrayOrderColor_#EFEFEF"/>
                                                        <constraints>
                                                            <constraint firstAttribute="trailing" secondItem="Afp-lr-9G5" secondAttribute="trailing" constant="15" id="OuS-cD-Ox7"/>
                                                            <constraint firstItem="Afp-lr-9G5" firstAttribute="centerY" secondItem="sag-je-reA" secondAttribute="centerY" id="lWb-Nt-cif"/>
                                                            <constraint firstAttribute="height" constant="40" id="q6f-bC-0bb"/>
                                                            <constraint firstItem="Afp-lr-9G5" firstAttribute="leading" secondItem="sag-je-reA" secondAttribute="leading" constant="15" id="sYy-QA-h7m"/>
                                                        </constraints>
                                                    </view>
                                                </subviews>
                                                <constraints>
                                                    <constraint firstItem="sag-je-reA" firstAttribute="leading" secondItem="BeC-xH-e7D" secondAttribute="leading" id="2qV-bU-4CT"/>
                                                    <constraint firstItem="sag-je-reA" firstAttribute="trailing" secondItem="BeC-xH-e7D" secondAttribute="trailing" id="80f-1Q-Sw9"/>
                                                    <constraint firstAttribute="height" constant="94" id="Dfj-jd-04Q"/>
                                                    <constraint firstAttribute="bottom" secondItem="sag-je-reA" secondAttribute="bottom" constant="10" id="Eu4-YA-OM5"/>
                                                    <constraint firstAttribute="trailing" secondItem="BeC-xH-e7D" secondAttribute="trailing" id="FwH-v8-vN1"/>
                                                    <constraint firstItem="BeC-xH-e7D" firstAttribute="top" secondItem="k5b-Im-bXm" secondAttribute="top" constant="15" id="myi-U8-a5T"/>
                                                    <constraint firstItem="BeC-xH-e7D" firstAttribute="leading" secondItem="k5b-Im-bXm" secondAttribute="leading" id="sdK-Oa-Hif"/>
                                                    <constraint firstItem="sag-je-reA" firstAttribute="top" secondItem="BeC-xH-e7D" secondAttribute="bottom" constant="15" id="yyW-hd-GRC"/>
                                                </constraints>
                                            </view>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="NHy-Nn-S1e">
                                                <rect key="frame" x="18" y="567" width="348" height="74"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Delivery Address" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="VbH-nb-FEb" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="10" width="348" height="14"/>
                                                        <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="14"/>
                                                        <color key="textColor" name="AppTheme_SelectedTabColor_#F4BA45"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="K8Z-go-dB9">
                                                        <rect key="frame" x="0.0" y="39" width="348" height="30"/>
                                                        <subviews>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" lineBreakMode="wordWrap" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="mLt-s4-q78" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                                <rect key="frame" x="15" y="10" width="318" height="0.0"/>
                                                                <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="13"/>
                                                                <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" lineBreakMode="wordWrap" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="vFj-XM-ttl" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                                <rect key="frame" x="15" y="20" width="318" height="0.0"/>
                                                                <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="12"/>
                                                                <color key="textColor" name="AppTheme_GrayTextColor_#767676"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                        </subviews>
                                                        <color key="backgroundColor" name="AppTheme_LightGrayOrderColor_#EFEFEF"/>
                                                        <constraints>
                                                            <constraint firstItem="vFj-XM-ttl" firstAttribute="leading" secondItem="mLt-s4-q78" secondAttribute="leading" id="20Y-sD-NvO"/>
                                                            <constraint firstItem="vFj-XM-ttl" firstAttribute="top" secondItem="mLt-s4-q78" secondAttribute="bottom" constant="10" id="9RE-kd-fle"/>
                                                            <constraint firstAttribute="bottom" secondItem="vFj-XM-ttl" secondAttribute="bottom" constant="10" id="GdE-jB-EWI"/>
                                                            <constraint firstAttribute="trailing" secondItem="mLt-s4-q78" secondAttribute="trailing" constant="15" id="d62-vE-TWW"/>
                                                            <constraint firstItem="vFj-XM-ttl" firstAttribute="trailing" secondItem="mLt-s4-q78" secondAttribute="trailing" id="otS-2x-qQJ"/>
                                                            <constraint firstItem="mLt-s4-q78" firstAttribute="top" secondItem="K8Z-go-dB9" secondAttribute="top" constant="10" id="yMj-Cd-fG7"/>
                                                            <constraint firstItem="mLt-s4-q78" firstAttribute="leading" secondItem="K8Z-go-dB9" secondAttribute="leading" constant="15" id="zTw-cG-Gei"/>
                                                        </constraints>
                                                    </view>
                                                </subviews>
                                                <constraints>
                                                    <constraint firstItem="K8Z-go-dB9" firstAttribute="trailing" secondItem="VbH-nb-FEb" secondAttribute="trailing" id="0sP-lf-bgi"/>
                                                    <constraint firstAttribute="trailing" secondItem="VbH-nb-FEb" secondAttribute="trailing" id="B5y-we-tlL"/>
                                                    <constraint firstItem="K8Z-go-dB9" firstAttribute="top" secondItem="VbH-nb-FEb" secondAttribute="bottom" constant="15" id="XoU-Jo-cBh"/>
                                                    <constraint firstItem="VbH-nb-FEb" firstAttribute="leading" secondItem="NHy-Nn-S1e" secondAttribute="leading" id="i6x-pn-Pvu"/>
                                                    <constraint firstItem="K8Z-go-dB9" firstAttribute="leading" secondItem="VbH-nb-FEb" secondAttribute="leading" id="nb2-0g-HmV"/>
                                                    <constraint firstItem="VbH-nb-FEb" firstAttribute="top" secondItem="NHy-Nn-S1e" secondAttribute="top" constant="10" id="rIy-Hd-BgG"/>
                                                    <constraint firstAttribute="bottom" secondItem="K8Z-go-dB9" secondAttribute="bottom" constant="5" id="yCs-xA-HFC"/>
                                                </constraints>
                                            </view>
                                            <view clipsSubviews="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="61L-BM-O3e">
                                                <rect key="frame" x="18" y="646" width="348" height="94"/>
                                                <subviews>
                                                    <label opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Delivery Shift" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="BJb-t8-D3P" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="15" width="348" height="14"/>
                                                        <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="14"/>
                                                        <color key="textColor" name="AppTheme_SelectedTabColor_#F4BA45"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <view clipsSubviews="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="cME-Gl-VmT">
                                                        <rect key="frame" x="0.0" y="44" width="348" height="40"/>
                                                        <subviews>
                                                            <label opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Morning" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="6ku-bH-9eT" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                                <rect key="frame" x="15" y="13.5" width="318" height="13"/>
                                                                <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="13"/>
                                                                <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                        </subviews>
                                                        <color key="backgroundColor" name="AppTheme_LightGrayOrderColor_#EFEFEF"/>
                                                        <constraints>
                                                            <constraint firstItem="6ku-bH-9eT" firstAttribute="leading" secondItem="cME-Gl-VmT" secondAttribute="leading" constant="15" id="9jj-c4-tOp"/>
                                                            <constraint firstItem="6ku-bH-9eT" firstAttribute="centerY" secondItem="cME-Gl-VmT" secondAttribute="centerY" id="gep-ef-j3N"/>
                                                            <constraint firstAttribute="trailing" secondItem="6ku-bH-9eT" secondAttribute="trailing" constant="15" id="n3I-F0-gp5"/>
                                                            <constraint firstAttribute="height" constant="40" id="xbT-Yb-Guj"/>
                                                        </constraints>
                                                    </view>
                                                </subviews>
                                                <constraints>
                                                    <constraint firstAttribute="trailing" secondItem="BJb-t8-D3P" secondAttribute="trailing" id="0lO-dk-NP2"/>
                                                    <constraint firstItem="cME-Gl-VmT" firstAttribute="trailing" secondItem="BJb-t8-D3P" secondAttribute="trailing" id="5ts-l7-Gf3"/>
                                                    <constraint firstAttribute="bottom" secondItem="cME-Gl-VmT" secondAttribute="bottom" constant="10" id="ESn-es-f2f"/>
                                                    <constraint firstAttribute="height" constant="94" id="Mlm-3B-Uou"/>
                                                    <constraint firstItem="cME-Gl-VmT" firstAttribute="leading" secondItem="BJb-t8-D3P" secondAttribute="leading" id="UNK-9g-wBk"/>
                                                    <constraint firstItem="BJb-t8-D3P" firstAttribute="top" secondItem="61L-BM-O3e" secondAttribute="top" constant="15" id="crG-vV-GBo"/>
                                                    <constraint firstItem="BJb-t8-D3P" firstAttribute="leading" secondItem="61L-BM-O3e" secondAttribute="leading" id="sze-YA-f9e"/>
                                                    <constraint firstItem="cME-Gl-VmT" firstAttribute="top" secondItem="BJb-t8-D3P" secondAttribute="bottom" constant="15" id="uyn-tu-T02"/>
                                                </constraints>
                                            </view>
                                            <view clipsSubviews="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="pth-YA-0az">
                                                <rect key="frame" x="18" y="740" width="348" height="94"/>
                                                <subviews>
                                                    <label opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Order Type" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="8qZ-Mt-Az7" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="15" width="348" height="14"/>
                                                        <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="14"/>
                                                        <color key="textColor" name="AppTheme_SelectedTabColor_#F4BA45"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <view clipsSubviews="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="t1d-Kq-rxf">
                                                        <rect key="frame" x="0.0" y="44" width="348" height="40"/>
                                                        <subviews>
                                                            <label opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Only Once" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="74P-D3-9cc" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                                <rect key="frame" x="15" y="13.5" width="318" height="13"/>
                                                                <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="13"/>
                                                                <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                        </subviews>
                                                        <color key="backgroundColor" name="AppTheme_LightGrayOrderColor_#EFEFEF"/>
                                                        <constraints>
                                                            <constraint firstItem="74P-D3-9cc" firstAttribute="centerY" secondItem="t1d-Kq-rxf" secondAttribute="centerY" id="Xpx-yr-t5H"/>
                                                            <constraint firstAttribute="trailing" secondItem="74P-D3-9cc" secondAttribute="trailing" constant="15" id="hZR-0C-CjX"/>
                                                            <constraint firstAttribute="height" constant="40" id="n6n-cK-841"/>
                                                            <constraint firstItem="74P-D3-9cc" firstAttribute="leading" secondItem="t1d-Kq-rxf" secondAttribute="leading" constant="15" id="vP5-aU-RtO"/>
                                                        </constraints>
                                                    </view>
                                                </subviews>
                                                <constraints>
                                                    <constraint firstItem="t1d-Kq-rxf" firstAttribute="leading" secondItem="8qZ-Mt-Az7" secondAttribute="leading" id="7Zh-iK-Rfs"/>
                                                    <constraint firstItem="t1d-Kq-rxf" firstAttribute="top" secondItem="8qZ-Mt-Az7" secondAttribute="bottom" constant="15" id="HWO-ev-V3M"/>
                                                    <constraint firstAttribute="height" constant="94" id="Ku5-di-ePU"/>
                                                    <constraint firstItem="8qZ-Mt-Az7" firstAttribute="top" secondItem="pth-YA-0az" secondAttribute="top" constant="15" id="Mhw-g2-8FV"/>
                                                    <constraint firstItem="t1d-Kq-rxf" firstAttribute="trailing" secondItem="8qZ-Mt-Az7" secondAttribute="trailing" id="ZbI-ue-H3A"/>
                                                    <constraint firstAttribute="bottom" secondItem="t1d-Kq-rxf" secondAttribute="bottom" constant="10" id="hGL-eQ-k1u"/>
                                                    <constraint firstItem="8qZ-Mt-Az7" firstAttribute="leading" secondItem="pth-YA-0az" secondAttribute="leading" id="mn1-d5-vdV"/>
                                                    <constraint firstAttribute="trailing" secondItem="8qZ-Mt-Az7" secondAttribute="trailing" id="rHw-EG-xJv"/>
                                                </constraints>
                                            </view>
                                            <view clipsSubviews="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="XXo-4x-vPv">
                                                <rect key="frame" x="18" y="834" width="348" height="15"/>
                                                <subviews>
                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="15" translatesAutoresizingMaskIntoConstraints="NO" id="9i2-xi-yGZ">
                                                        <rect key="frame" x="0.0" y="15" width="348" height="0.0"/>
                                                        <subviews>
                                                            <label hidden="YES" opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Notes" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="5Ni-xy-Ze1" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                                <rect key="frame" x="0.0" y="0.0" width="321" height="0.0"/>
                                                                <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="14"/>
                                                                <color key="textColor" name="AppTheme_SelectedTabColor_#F4BA45"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                            <view hidden="YES" clipsSubviews="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="TJn-eu-ClG">
                                                                <rect key="frame" x="0.0" y="0.0" width="321" height="24"/>
                                                                <subviews>
                                                                    <label opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Note" lineBreakMode="wordWrap" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="zIJ-ZS-REi" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                                        <rect key="frame" x="15" y="12" width="291" height="0.0"/>
                                                                        <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="13"/>
                                                                        <color key="textColor" name="AppTheme_GrayTextColor_#767676"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                                <color key="backgroundColor" name="AppTheme_LightGrayOrderColor_#EFEFEF"/>
                                                                <constraints>
                                                                    <constraint firstItem="zIJ-ZS-REi" firstAttribute="leading" secondItem="TJn-eu-ClG" secondAttribute="leading" constant="15" id="6Ex-gR-1Qc"/>
                                                                    <constraint firstItem="zIJ-ZS-REi" firstAttribute="top" secondItem="TJn-eu-ClG" secondAttribute="top" constant="12" id="Cfi-Ws-0gq"/>
                                                                    <constraint firstAttribute="bottom" secondItem="zIJ-ZS-REi" secondAttribute="bottom" constant="12" id="EFl-RQ-qzI"/>
                                                                    <constraint firstAttribute="trailing" secondItem="zIJ-ZS-REi" secondAttribute="trailing" constant="15" id="WIO-Yk-yPg"/>
                                                                </constraints>
                                                            </view>
                                                        </subviews>
                                                    </stackView>
                                                </subviews>
                                                <constraints>
                                                    <constraint firstItem="9i2-xi-yGZ" firstAttribute="top" secondItem="XXo-4x-vPv" secondAttribute="top" constant="15" id="Gqi-No-Tfi"/>
                                                    <constraint firstAttribute="bottom" secondItem="9i2-xi-yGZ" secondAttribute="bottom" id="VEs-dE-TwD"/>
                                                    <constraint firstItem="9i2-xi-yGZ" firstAttribute="leading" secondItem="XXo-4x-vPv" secondAttribute="leading" id="XnK-Pd-8qp"/>
                                                    <constraint firstAttribute="trailing" secondItem="9i2-xi-yGZ" secondAttribute="trailing" id="gGg-8X-oot"/>
                                                </constraints>
                                            </view>
                                        </subviews>
                                        <constraints>
                                            <constraint firstAttribute="trailing" secondItem="1n2-3i-VOh" secondAttribute="trailing" id="3sO-pI-xMK"/>
                                            <constraint firstItem="NHy-Nn-S1e" firstAttribute="trailing" secondItem="CCi-xj-VQa" secondAttribute="trailing" id="5nb-uC-EWX"/>
                                            <constraint firstItem="XXo-4x-vPv" firstAttribute="top" secondItem="pth-YA-0az" secondAttribute="bottom" id="9x5-VG-kMd"/>
                                            <constraint firstItem="XXo-4x-vPv" firstAttribute="trailing" secondItem="CCi-xj-VQa" secondAttribute="trailing" id="FBs-Or-KXI"/>
                                            <constraint firstItem="CCi-xj-VQa" firstAttribute="leading" secondItem="O8g-C7-JBK" secondAttribute="leading" constant="18" id="Gol-11-N2y"/>
                                            <constraint firstAttribute="bottom" secondItem="XXo-4x-vPv" secondAttribute="bottom" constant="40" id="HO4-mW-tlN"/>
                                            <constraint firstItem="1n2-3i-VOh" firstAttribute="leading" secondItem="O8g-C7-JBK" secondAttribute="leading" id="IEp-ne-J4S"/>
                                            <constraint firstItem="XXo-4x-vPv" firstAttribute="leading" secondItem="CCi-xj-VQa" secondAttribute="leading" id="KHg-tQ-7rQ"/>
                                            <constraint firstItem="61L-BM-O3e" firstAttribute="top" secondItem="NHy-Nn-S1e" secondAttribute="bottom" constant="5" id="MQx-Wr-BSy"/>
                                            <constraint firstItem="k5b-Im-bXm" firstAttribute="top" secondItem="CCi-xj-VQa" secondAttribute="bottom" constant="15" id="OcW-25-5Ol"/>
                                            <constraint firstItem="Kps-TR-ZD1" firstAttribute="leading" secondItem="O8g-C7-JBK" secondAttribute="leading" id="PA4-Q7-Tgo"/>
                                            <constraint firstItem="pth-YA-0az" firstAttribute="leading" secondItem="CCi-xj-VQa" secondAttribute="leading" id="PNg-s2-S8F"/>
                                            <constraint firstItem="CCi-xj-VQa" firstAttribute="top" secondItem="PKk-fq-RdN" secondAttribute="bottom" constant="30" id="SQT-tC-Tjo"/>
                                            <constraint firstItem="n46-vk-syB" firstAttribute="leading" secondItem="O8g-C7-JBK" secondAttribute="leading" constant="27" id="Sb2-v1-xlW"/>
                                            <constraint firstItem="n46-vk-syB" firstAttribute="centerY" secondItem="PKk-fq-RdN" secondAttribute="centerY" id="TtT-AY-wyQ"/>
                                            <constraint firstItem="k5b-Im-bXm" firstAttribute="leading" secondItem="CCi-xj-VQa" secondAttribute="leading" id="VtI-b3-UzL"/>
                                            <constraint firstItem="1n2-3i-VOh" firstAttribute="top" secondItem="Kps-TR-ZD1" secondAttribute="bottom" constant="20" id="XTU-gA-HFZ"/>
                                            <constraint firstItem="NHy-Nn-S1e" firstAttribute="top" secondItem="k5b-Im-bXm" secondAttribute="bottom" constant="5" id="ZbV-CH-UFm"/>
                                            <constraint firstItem="k5b-Im-bXm" firstAttribute="trailing" secondItem="CCi-xj-VQa" secondAttribute="trailing" id="Zgt-Ti-GQR"/>
                                            <constraint firstItem="pth-YA-0az" firstAttribute="top" secondItem="61L-BM-O3e" secondAttribute="bottom" id="bTO-Ih-Zsk"/>
                                            <constraint firstItem="pth-YA-0az" firstAttribute="trailing" secondItem="CCi-xj-VQa" secondAttribute="trailing" id="cDu-tQ-Gq7"/>
                                            <constraint firstItem="Kps-TR-ZD1" firstAttribute="centerX" secondItem="O8g-C7-JBK" secondAttribute="centerX" id="dKl-eB-o0b"/>
                                            <constraint firstAttribute="trailing" secondItem="CCi-xj-VQa" secondAttribute="trailing" constant="18" id="efd-Wh-bhr"/>
                                            <constraint firstItem="61L-BM-O3e" firstAttribute="trailing" secondItem="CCi-xj-VQa" secondAttribute="trailing" id="gfL-fW-Htn"/>
                                            <constraint firstItem="Kps-TR-ZD1" firstAttribute="top" secondItem="O8g-C7-JBK" secondAttribute="top" constant="22" id="kOy-Ux-oJb"/>
                                            <constraint firstItem="NHy-Nn-S1e" firstAttribute="leading" secondItem="CCi-xj-VQa" secondAttribute="leading" id="oO6-Me-ryJ"/>
                                            <constraint firstAttribute="trailing" secondItem="Kps-TR-ZD1" secondAttribute="trailing" id="pKE-ot-zJX"/>
                                            <constraint firstItem="61L-BM-O3e" firstAttribute="leading" secondItem="CCi-xj-VQa" secondAttribute="leading" id="q3K-7N-fpz"/>
                                            <constraint firstItem="PKk-fq-RdN" firstAttribute="top" secondItem="1n2-3i-VOh" secondAttribute="bottom" constant="30" id="wny-7F-Ale"/>
                                            <constraint firstAttribute="trailing" secondItem="PKk-fq-RdN" secondAttribute="trailing" constant="27" id="zo2-Tv-Q7G"/>
                                        </constraints>
                                    </scrollView>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstAttribute="trailing" secondItem="O8g-C7-JBK" secondAttribute="trailing" id="8Ah-An-Ru7"/>
                                    <constraint firstItem="O8g-C7-JBK" firstAttribute="top" secondItem="neC-W5-Dqb" secondAttribute="bottom" id="F0o-Fw-MFA"/>
                                    <constraint firstAttribute="bottom" secondItem="O8g-C7-JBK" secondAttribute="bottom" id="HMT-PS-tFC"/>
                                    <constraint firstItem="neC-W5-Dqb" firstAttribute="top" secondItem="aiM-8d-3JA" secondAttribute="top" constant="23" id="isA-t1-saa"/>
                                    <constraint firstItem="neC-W5-Dqb" firstAttribute="centerX" secondItem="aiM-8d-3JA" secondAttribute="centerX" id="lud-sF-rMK"/>
                                    <constraint firstItem="O8g-C7-JBK" firstAttribute="leading" secondItem="aiM-8d-3JA" secondAttribute="leading" id="rW3-hE-luX"/>
                                </constraints>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="boolean" keyPath="isViewShadow" value="YES"/>
                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                        <real key="value" value="39"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="c0g-Z0-kdo"/>
                        <color key="backgroundColor" white="0.0" alpha="0.5" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="c0g-Z0-kdo" firstAttribute="bottom" secondItem="sWL-xt-Qn7" secondAttribute="bottom" id="6Ww-hU-hEd"/>
                            <constraint firstItem="c0g-Z0-kdo" firstAttribute="bottom" secondItem="aiM-8d-3JA" secondAttribute="bottom" constant="70" id="776-pg-A04"/>
                            <constraint firstItem="aiM-8d-3JA" firstAttribute="leading" secondItem="c0g-Z0-kdo" secondAttribute="leading" constant="15" id="Z8K-dy-9dy"/>
                            <constraint firstItem="c0g-Z0-kdo" firstAttribute="trailing" secondItem="sWL-xt-Qn7" secondAttribute="trailing" id="bsC-s4-oRl"/>
                            <constraint firstItem="c0g-Z0-kdo" firstAttribute="trailing" secondItem="aiM-8d-3JA" secondAttribute="trailing" constant="15" id="gCk-xE-q5e"/>
                            <constraint firstItem="sWL-xt-Qn7" firstAttribute="leading" secondItem="c0g-Z0-kdo" secondAttribute="leading" id="gxc-pi-KWj"/>
                            <constraint firstItem="aiM-8d-3JA" firstAttribute="top" secondItem="c0g-Z0-kdo" secondAttribute="top" constant="70" id="o03-Vr-6GI"/>
                            <constraint firstItem="sWL-xt-Qn7" firstAttribute="top" secondItem="c0g-Z0-kdo" secondAttribute="top" id="sHs-AU-xA6"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="btnHelp" destination="hra-mN-CGK" id="sJz-cE-qjK"/>
                        <outlet property="btnInvoice" destination="f8B-sn-IDV" id="6gn-wC-xto"/>
                        <outlet property="btnReOrder" destination="PKk-fq-RdN" id="zvc-hs-KhC"/>
                        <outlet property="cons_bottom_viewNotes" destination="HO4-mW-tlN" id="NcU-0U-KWG"/>
                        <outlet property="cons_height_viewPaymentMethod" destination="Dfj-jd-04Q" id="QhJ-hT-TTx"/>
                        <outlet property="lblAddress" destination="vFj-XM-ttl" id="gbw-Wb-cqZ"/>
                        <outlet property="lblAllPrices" destination="dfJ-ia-pam" id="QFc-e9-2YF"/>
                        <outlet property="lblDelivery" destination="Mkd-zB-RAZ" id="gnQ-AH-GC7"/>
                        <outlet property="lblDeliveryAddressTitle" destination="VbH-nb-FEb" id="h4d-Zg-CxT"/>
                        <outlet property="lblDeliveryDiscountTitle" destination="hME-H5-dqm" id="iZs-S4-noo"/>
                        <outlet property="lblDeliveryDiscountValue" destination="hla-CR-KsX" id="7G2-0f-RQC"/>
                        <outlet property="lblDeliveryShiftTitle" destination="BJb-t8-D3P" id="S0i-Y2-p3J"/>
                        <outlet property="lblDeliveryTitle" destination="5Sj-XW-URn" id="uqK-IZ-FvK"/>
                        <outlet property="lblDiscountTitle" destination="6k6-8Q-UME" id="64Z-Kt-leL"/>
                        <outlet property="lblDiscountValue" destination="RKN-mo-cxM" id="NFS-ub-UTD"/>
                        <outlet property="lblLineDeliveryDiscount" destination="zqm-Rc-Ngy" id="hBc-Vb-7hW"/>
                        <outlet property="lblLineDiscount" destination="230-zU-WWI" id="1ky-U3-1Vt"/>
                        <outlet property="lblNote" destination="zIJ-ZS-REi" id="Q1U-qE-Do3"/>
                        <outlet property="lblNotesTitle" destination="5Ni-xy-Ze1" id="pcB-W4-HNI"/>
                        <outlet property="lblOrderDate" destination="aek-A1-9jL" id="85g-Sz-IdQ"/>
                        <outlet property="lblOrderDateTitle" destination="uOn-Xx-4ev" id="pZj-YO-aeJ"/>
                        <outlet property="lblOrderNo" destination="W98-KF-04Z" id="oBi-2n-xIu"/>
                        <outlet property="lblOrderNoTitle" destination="nAC-UM-uFq" id="qWt-SH-sSA"/>
                        <outlet property="lblOrderTypeTitle" destination="8qZ-Mt-Az7" id="WTO-VS-ksj"/>
                        <outlet property="lblOrderTypeValue" destination="74P-D3-9cc" id="6kc-EY-KJZ"/>
                        <outlet property="lblPaymentMethodTitle" destination="BeC-xH-e7D" id="bmT-tE-USF"/>
                        <outlet property="lblPaymentMethodValue" destination="Afp-lr-9G5" id="ymQ-em-878"/>
                        <outlet property="lblShift" destination="6ku-bH-9eT" id="SIc-su-whO"/>
                        <outlet property="lblStatus" destination="n46-vk-syB" id="lb1-Fo-hLi"/>
                        <outlet property="lblSubTotal" destination="OJs-cf-r5H" id="apk-xe-BjF"/>
                        <outlet property="lblSubTotalTitle" destination="PJh-ff-rCI" id="8kQ-3r-grh"/>
                        <outlet property="lblTitle" destination="neC-W5-Dqb" id="e9L-7V-X0B"/>
                        <outlet property="lblTotal" destination="ML9-6B-8Br" id="oUB-DT-PD1"/>
                        <outlet property="lblTotalTitle" destination="a6A-Np-cUx" id="hJr-Uw-TN9"/>
                        <outlet property="lblType" destination="mLt-s4-q78" id="ZKg-3C-LBh"/>
                        <outlet property="lblVat" destination="Qdk-aO-5WB" id="hyK-GC-q0g"/>
                        <outlet property="stackViewDeliveryDiscount" destination="fth-H8-g59" id="R9I-HD-ybo"/>
                        <outlet property="stackViewDiscount" destination="oXx-ww-Icc" id="dBY-4D-gKx"/>
                        <outlet property="tblOrders" destination="1n2-3i-VOh" id="drN-Bg-fz6"/>
                        <outlet property="tblOrdersHeightConstraints" destination="4qS-0r-QtK" id="iIy-SG-nAf"/>
                        <outlet property="viewNote" destination="TJn-eu-ClG" id="8rA-rq-WG1"/>
                        <outlet property="viewPaymentMethod" destination="k5b-Im-bXm" id="htK-7X-W5W"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="BvK-Li-MOj" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1159" y="2250"/>
        </scene>
        <!--Invoice View Controller-->
        <scene sceneID="RAE-Y2-KSU">
            <objects>
                <viewController storyboardIdentifier="InvoiceViewController" id="VCy-aT-sRa" customClass="InvoiceViewController" customModule="TopCustomer" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="yBc-eD-hEi">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="Tm6-e2-G8d">
                                <rect key="frame" x="0.0" y="108" width="414" height="754"/>
                            </imageView>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="jLS-QL-M3P" customClass="CustomTopViewForShadow" customModule="TopCustomer" customModuleProvider="target">
                                <rect key="frame" x="0.0" y="48" width="414" height="60"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="UEm-s3-iQn" customClass="MaterialLocalizeButton" customModule="TopCustomer" customModuleProvider="target">
                                        <rect key="frame" x="25" y="12.5" width="35" height="35"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="35" id="f5H-Av-PQ8"/>
                                            <constraint firstAttribute="height" constant="35" id="tbU-ka-f9K"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="12"/>
                                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                        <state key="normal" image="icn_back">
                                            <color key="titleColor" name="AppTheme_BlueColor_#012CDA"/>
                                        </state>
                                        <connections>
                                            <action selector="btnBackAction:" destination="VCy-aT-sRa" eventType="touchUpInside" id="Alh-58-sO3"/>
                                        </connections>
                                    </button>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Tax Invoice" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Uwe-ph-8Ci">
                                        <rect key="frame" x="142" y="18.5" width="130.5" height="23"/>
                                        <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="23"/>
                                        <color key="textColor" name="AppTheme_BlueColor_#012CDA"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="60" id="EZo-5u-He4"/>
                                    <constraint firstItem="Uwe-ph-8Ci" firstAttribute="centerY" secondItem="jLS-QL-M3P" secondAttribute="centerY" id="GgB-JU-fTl"/>
                                    <constraint firstItem="UEm-s3-iQn" firstAttribute="centerY" secondItem="jLS-QL-M3P" secondAttribute="centerY" id="MZj-XI-J9M"/>
                                    <constraint firstItem="Uwe-ph-8Ci" firstAttribute="centerX" secondItem="jLS-QL-M3P" secondAttribute="centerX" id="Okd-rL-f5Y"/>
                                    <constraint firstItem="UEm-s3-iQn" firstAttribute="leading" secondItem="jLS-QL-M3P" secondAttribute="leading" constant="25" id="ue6-bA-vr3"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="2V0-B3-Nqh"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="jLS-QL-M3P" firstAttribute="top" secondItem="2V0-B3-Nqh" secondAttribute="top" id="1EC-L9-PpN"/>
                            <constraint firstItem="jLS-QL-M3P" firstAttribute="leading" secondItem="2V0-B3-Nqh" secondAttribute="leading" id="8ye-6r-NDq"/>
                            <constraint firstItem="Tm6-e2-G8d" firstAttribute="top" secondItem="jLS-QL-M3P" secondAttribute="bottom" id="Tmu-Le-R14"/>
                            <constraint firstItem="2V0-B3-Nqh" firstAttribute="bottom" secondItem="Tm6-e2-G8d" secondAttribute="bottom" id="e6m-d6-cM2"/>
                            <constraint firstItem="jLS-QL-M3P" firstAttribute="trailing" secondItem="2V0-B3-Nqh" secondAttribute="trailing" id="iSJ-GQ-KTN"/>
                            <constraint firstItem="2V0-B3-Nqh" firstAttribute="trailing" secondItem="Tm6-e2-G8d" secondAttribute="trailing" id="vsT-X0-QV7"/>
                            <constraint firstItem="Tm6-e2-G8d" firstAttribute="leading" secondItem="2V0-B3-Nqh" secondAttribute="leading" id="zZb-wf-K64"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="btnCancel" destination="UEm-s3-iQn" id="mwe-K1-O8C"/>
                        <outlet property="lblTitle" destination="Uwe-ph-8Ci" id="4HM-rW-5jH"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="ofR-eB-Dwg" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1149" y="790"/>
        </scene>
        <!--Rating View Controller-->
        <scene sceneID="HgG-sf-pBI">
            <objects>
                <viewController storyboardIdentifier="RatingViewController" id="o1d-i2-UU8" customClass="RatingViewController" customModule="TopCustomer" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="Fqp-ua-60W">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="D6l-1J-2i7">
                                <rect key="frame" x="0.0" y="48" width="414" height="814"/>
                                <connections>
                                    <action selector="btnDimViewAction:" destination="o1d-i2-UU8" eventType="touchUpInside" id="xcg-Tm-njk"/>
                                </connections>
                            </button>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Jjd-Fm-2qm">
                                <rect key="frame" x="15" y="229.5" width="384" height="437"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="right" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" hasAttributedTitle="YES" translatesAutoresizingMaskIntoConstraints="NO" id="Wqi-Zd-xec">
                                        <rect key="frame" x="329" y="15" width="30" height="35"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="35" id="dQP-6q-g2S"/>
                                        </constraints>
                                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                        <state key="normal">
                                            <attributedString key="attributedTitle">
                                                <fragment content="Skip">
                                                    <attributes>
                                                        <color key="NSColor" red="0.46274509803921571" green="0.46274509803921571" blue="0.46274509803921571" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                        <font key="NSFont" metaFont="system" size="14"/>
                                                        <paragraphStyle key="NSParagraphStyle" alignment="natural" lineBreakMode="wordWrapping" baseWritingDirection="natural" tighteningFactorForTruncation="0.0"/>
                                                        <integer key="NSUnderline" value="1"/>
                                                    </attributes>
                                                </fragment>
                                            </attributedString>
                                        </state>
                                        <connections>
                                            <action selector="actionSkip:" destination="o1d-i2-UU8" eventType="touchUpInside" id="87g-oV-UVp"/>
                                        </connections>
                                    </button>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Are you satisfied with our service?" textAlignment="center" lineBreakMode="wordWrap" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="jnT-Iu-epN" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                        <rect key="frame" x="20" y="75" width="344" height="16"/>
                                        <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="16"/>
                                        <color key="textColor" name="AppTheme_BlueColor_#012CDA"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Please rate" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="LMC-qz-s0G" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                        <rect key="frame" x="20" y="101" width="344" height="16"/>
                                        <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="16"/>
                                        <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    </label>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="hfc-l1-Hky" customClass="FloatRatingView" customModule="TopCustomer" customModuleProvider="target">
                                        <rect key="frame" x="67" y="147" width="250" height="40"/>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="250" id="Tx0-fN-PZ5"/>
                                            <constraint firstAttribute="height" constant="40" id="dZx-c6-yke"/>
                                        </constraints>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="image" keyPath="emptyImage" value="icn_star_unselected_1"/>
                                            <userDefinedRuntimeAttribute type="image" keyPath="fullImage" value="icn_star_selected_1"/>
                                            <userDefinedRuntimeAttribute type="number" keyPath="minRating">
                                                <integer key="value" value="0"/>
                                            </userDefinedRuntimeAttribute>
                                            <userDefinedRuntimeAttribute type="number" keyPath="maxRating">
                                                <integer key="value" value="5"/>
                                            </userDefinedRuntimeAttribute>
                                            <userDefinedRuntimeAttribute type="number" keyPath="rating">
                                                <real key="value" value="3"/>
                                            </userDefinedRuntimeAttribute>
                                        </userDefinedRuntimeAttributes>
                                    </view>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="6AN-AD-fOF" customClass="CustomViewForFields" customModule="TopCustomer" customModuleProvider="target">
                                        <rect key="frame" x="20" y="227" width="344" height="120"/>
                                        <subviews>
                                            <textView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="VQq-Kg-os1" customClass="IQTextView" customModule="IQKeyboardManagerSwift">
                                                <rect key="frame" x="10" y="5" width="324" height="115"/>
                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="16"/>
                                                <textInputTraits key="textInputTraits" autocapitalizationType="sentences"/>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="string" keyPath="placeholder" value="Leave a review (Optional)"/>
                                                </userDefinedRuntimeAttributes>
                                            </textView>
                                        </subviews>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="bottom" secondItem="VQq-Kg-os1" secondAttribute="bottom" id="9vh-G3-PZZ"/>
                                            <constraint firstItem="VQq-Kg-os1" firstAttribute="leading" secondItem="6AN-AD-fOF" secondAttribute="leading" constant="10" id="esV-Wd-GtF"/>
                                            <constraint firstAttribute="height" constant="120" id="f3P-Gy-oaJ"/>
                                            <constraint firstAttribute="trailing" secondItem="VQq-Kg-os1" secondAttribute="trailing" constant="10" id="kpQ-Lq-nE1"/>
                                            <constraint firstItem="VQq-Kg-os1" firstAttribute="top" secondItem="6AN-AD-fOF" secondAttribute="top" constant="5" id="vKa-DX-Wug"/>
                                        </constraints>
                                    </view>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="rRE-XZ-eMp" customClass="CustomRoundedButtton" customModule="TopCustomer" customModuleProvider="target">
                                        <rect key="frame" x="20" y="367" width="344" height="40"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="40" id="Pau-Dm-RcC"/>
                                        </constraints>
                                        <state key="normal" title="Send"/>
                                        <connections>
                                            <action selector="actionSend:" destination="o1d-i2-UU8" eventType="touchUpInside" id="I2g-Gv-mE3"/>
                                        </connections>
                                    </button>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstItem="rRE-XZ-eMp" firstAttribute="trailing" secondItem="6AN-AD-fOF" secondAttribute="trailing" id="0EH-Kg-Wv9"/>
                                    <constraint firstItem="hfc-l1-Hky" firstAttribute="top" secondItem="LMC-qz-s0G" secondAttribute="bottom" constant="30" id="260-6k-Zf5"/>
                                    <constraint firstAttribute="trailing" secondItem="Wqi-Zd-xec" secondAttribute="trailing" constant="25" id="2xA-n0-IwJ"/>
                                    <constraint firstItem="6AN-AD-fOF" firstAttribute="top" secondItem="hfc-l1-Hky" secondAttribute="bottom" constant="40" id="389-l3-0ja"/>
                                    <constraint firstItem="rRE-XZ-eMp" firstAttribute="top" secondItem="6AN-AD-fOF" secondAttribute="bottom" constant="20" id="3HP-sZ-BLr"/>
                                    <constraint firstItem="hfc-l1-Hky" firstAttribute="centerX" secondItem="Jjd-Fm-2qm" secondAttribute="centerX" id="68Z-Gt-fTh"/>
                                    <constraint firstItem="LMC-qz-s0G" firstAttribute="top" secondItem="jnT-Iu-epN" secondAttribute="bottom" constant="10" id="6CO-7u-Zgl"/>
                                    <constraint firstItem="LMC-qz-s0G" firstAttribute="trailing" secondItem="jnT-Iu-epN" secondAttribute="trailing" id="BpJ-2j-Bdr"/>
                                    <constraint firstItem="6AN-AD-fOF" firstAttribute="leading" secondItem="LMC-qz-s0G" secondAttribute="leading" id="D19-Kx-gnJ"/>
                                    <constraint firstItem="6AN-AD-fOF" firstAttribute="trailing" secondItem="LMC-qz-s0G" secondAttribute="trailing" id="HsM-gU-oL6"/>
                                    <constraint firstItem="rRE-XZ-eMp" firstAttribute="leading" secondItem="6AN-AD-fOF" secondAttribute="leading" id="Tr3-RK-5e2"/>
                                    <constraint firstItem="jnT-Iu-epN" firstAttribute="top" secondItem="Wqi-Zd-xec" secondAttribute="bottom" constant="25" id="V9f-BA-3Ul"/>
                                    <constraint firstItem="Wqi-Zd-xec" firstAttribute="top" secondItem="Jjd-Fm-2qm" secondAttribute="top" constant="15" id="m0T-X4-hDa"/>
                                    <constraint firstAttribute="bottom" secondItem="rRE-XZ-eMp" secondAttribute="bottom" constant="30" id="ndH-8K-PqI"/>
                                    <constraint firstItem="jnT-Iu-epN" firstAttribute="leading" secondItem="Jjd-Fm-2qm" secondAttribute="leading" constant="20" id="qTx-4o-WDv"/>
                                    <constraint firstAttribute="trailing" secondItem="jnT-Iu-epN" secondAttribute="trailing" constant="20" id="uus-39-PJz"/>
                                    <constraint firstItem="LMC-qz-s0G" firstAttribute="leading" secondItem="jnT-Iu-epN" secondAttribute="leading" id="wa8-C4-g7c"/>
                                </constraints>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                        <real key="value" value="39"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="bdK-uW-kcC"/>
                        <color key="backgroundColor" white="0.0" alpha="0.5" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="D6l-1J-2i7" firstAttribute="trailing" secondItem="bdK-uW-kcC" secondAttribute="trailing" id="07i-GZ-rW8"/>
                            <constraint firstItem="Jjd-Fm-2qm" firstAttribute="centerY" secondItem="Fqp-ua-60W" secondAttribute="centerY" id="4Wm-DY-gRS"/>
                            <constraint firstItem="bdK-uW-kcC" firstAttribute="bottom" secondItem="D6l-1J-2i7" secondAttribute="bottom" id="CD5-Eh-Acd"/>
                            <constraint firstItem="bdK-uW-kcC" firstAttribute="bottom" secondItem="Jjd-Fm-2qm" secondAttribute="bottom" constant="70" id="Nfq-Rb-TpU"/>
                            <constraint firstItem="Jjd-Fm-2qm" firstAttribute="top" secondItem="bdK-uW-kcC" secondAttribute="top" constant="70" id="VZy-wr-Yc7"/>
                            <constraint firstItem="D6l-1J-2i7" firstAttribute="top" secondItem="bdK-uW-kcC" secondAttribute="top" id="c4L-Ju-NwY"/>
                            <constraint firstItem="D6l-1J-2i7" firstAttribute="leading" secondItem="bdK-uW-kcC" secondAttribute="leading" id="e1o-Gh-WIG"/>
                            <constraint firstItem="Jjd-Fm-2qm" firstAttribute="leading" secondItem="bdK-uW-kcC" secondAttribute="leading" constant="15" id="eU0-k1-PKu"/>
                            <constraint firstItem="bdK-uW-kcC" firstAttribute="trailing" secondItem="Jjd-Fm-2qm" secondAttribute="trailing" constant="15" id="lFJ-km-bCD"/>
                        </constraints>
                        <variation key="default">
                            <mask key="constraints">
                                <exclude reference="Nfq-Rb-TpU"/>
                                <exclude reference="VZy-wr-Yc7"/>
                            </mask>
                        </variation>
                    </view>
                    <connections>
                        <outlet property="btnSend" destination="rRE-XZ-eMp" id="9fa-Q2-DHD"/>
                        <outlet property="btnSkip" destination="Wqi-Zd-xec" id="AvO-Hv-YkB"/>
                        <outlet property="lblDidYouEnjoy" destination="jnT-Iu-epN" id="w09-a9-pWI"/>
                        <outlet property="lblPleaseRate" destination="LMC-qz-s0G" id="KDG-I4-hWM"/>
                        <outlet property="textView" destination="VQq-Kg-os1" id="siV-W7-6Q9"/>
                        <outlet property="viewRating" destination="hfc-l1-Hky" id="I46-GM-hSy"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="tFL-Kk-lx9" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="3537" y="-885"/>
        </scene>
        <!--Custom Popup Alert View Controller-->
        <scene sceneID="Tbf-Mw-0nb">
            <objects>
                <viewController storyboardIdentifier="CustomPopupAlertViewController" automaticallyAdjustsScrollViewInsets="NO" id="WPC-Az-9dX" customClass="CustomPopupAlertViewController" customModule="TopCustomer" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="cEj-bl-BXK">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="88q-SU-jZ3">
                                <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                            </button>
                            <view clipsSubviews="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="cDE-aV-iLr">
                                <rect key="frame" x="50" y="390.5" width="314" height="115"/>
                                <subviews>
                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="25" translatesAutoresizingMaskIntoConstraints="NO" id="zzK-q7-4Vz">
                                        <rect key="frame" x="30" y="25" width="254" height="65"/>
                                        <subviews>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="10" translatesAutoresizingMaskIntoConstraints="NO" id="BMq-G6-wzt">
                                                <rect key="frame" x="0.0" y="0.0" width="254" height="0.0"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="center" lineBreakMode="wordWrap" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="fdP-gS-7MY">
                                                        <rect key="frame" x="0.0" y="0.0" width="254" height="0.0"/>
                                                        <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="16"/>
                                                        <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                </subviews>
                                            </stackView>
                                            <view hidden="YES" clipsSubviews="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="tWS-p4-pTT">
                                                <rect key="frame" x="0.0" y="12.5" width="254" height="42"/>
                                                <subviews>
                                                    <button opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" adjustsImageWhenHighlighted="NO" adjustsImageWhenDisabled="NO" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="YbA-mH-0fk" customClass="CustomRoundedButtton" customModule="TopCustomer" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="0.0" width="254" height="42"/>
                                                        <color key="backgroundColor" name="AppTheme_BlueColor_#012CDA"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="42" id="H09-Qq-E13"/>
                                                            <constraint firstAttribute="width" constant="223" id="WfR-Cn-aUT"/>
                                                        </constraints>
                                                        <fontDescription key="fontDescription" name="Montserrat-SemiBold" family="Montserrat" pointSize="16"/>
                                                        <state key="normal" title="OK">
                                                            <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        </state>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                                <real key="value" value="5"/>
                                                            </userDefinedRuntimeAttribute>
                                                        </userDefinedRuntimeAttributes>
                                                        <variation key="default">
                                                            <mask key="constraints">
                                                                <exclude reference="WfR-Cn-aUT"/>
                                                            </mask>
                                                        </variation>
                                                        <connections>
                                                            <action selector="btnCenterAction:" destination="WPC-Az-9dX" eventType="touchUpInside" id="eBj-zW-78U"/>
                                                        </connections>
                                                    </button>
                                                </subviews>
                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstItem="YbA-mH-0fk" firstAttribute="leading" secondItem="tWS-p4-pTT" secondAttribute="leading" id="4qq-dE-BG4"/>
                                                    <constraint firstAttribute="trailing" secondItem="YbA-mH-0fk" secondAttribute="trailing" id="D1W-HZ-7yb"/>
                                                    <constraint firstItem="YbA-mH-0fk" firstAttribute="top" secondItem="tWS-p4-pTT" secondAttribute="top" id="YN8-X5-fCp"/>
                                                    <constraint firstAttribute="bottom" secondItem="YbA-mH-0fk" secondAttribute="bottom" id="ozZ-ZE-WXP"/>
                                                    <constraint firstItem="YbA-mH-0fk" firstAttribute="centerX" secondItem="tWS-p4-pTT" secondAttribute="centerX" id="v5r-HO-kRd"/>
                                                </constraints>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                        <real key="value" value="6"/>
                                                    </userDefinedRuntimeAttribute>
                                                </userDefinedRuntimeAttributes>
                                                <variation key="default">
                                                    <mask key="constraints">
                                                        <exclude reference="v5r-HO-kRd"/>
                                                    </mask>
                                                </variation>
                                            </view>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ijh-EE-djc">
                                                <rect key="frame" x="0.0" y="25" width="254" height="40"/>
                                                <subviews>
                                                    <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="10" translatesAutoresizingMaskIntoConstraints="NO" id="mZL-z6-bzw">
                                                        <rect key="frame" x="0.0" y="0.0" width="254" height="40"/>
                                                        <subviews>
                                                            <button opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" adjustsImageWhenHighlighted="NO" adjustsImageWhenDisabled="NO" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="2yx-lJ-FFy" customClass="CustomRoundedSecondaryButtton" customModule="TopCustomer" customModuleProvider="target">
                                                                <rect key="frame" x="0.0" y="0.0" width="122" height="40"/>
                                                                <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="14"/>
                                                                <state key="normal" title="OK">
                                                                    <color key="titleColor" name="AppTheme_BlueColor_#012CDA"/>
                                                                </state>
                                                                <userDefinedRuntimeAttributes>
                                                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                                        <real key="value" value="5"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                </userDefinedRuntimeAttributes>
                                                                <connections>
                                                                    <action selector="btnLeftAction:" destination="WPC-Az-9dX" eventType="touchUpInside" id="SlN-iI-rjv"/>
                                                                </connections>
                                                            </button>
                                                            <button opaque="NO" clipsSubviews="YES" tag="1" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" adjustsImageWhenHighlighted="NO" adjustsImageWhenDisabled="NO" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Ri0-C2-aMj" customClass="CustomRoundedButtton" customModule="TopCustomer" customModuleProvider="target">
                                                                <rect key="frame" x="132" y="0.0" width="122" height="40"/>
                                                                <color key="backgroundColor" name="AppTheme_BlueColor_#012CDA"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="40" id="9jg-57-aWv"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" name="Montserrat-SemiBold" family="Montserrat" pointSize="15"/>
                                                                <state key="normal" title="OK">
                                                                    <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                </state>
                                                                <userDefinedRuntimeAttributes>
                                                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                                        <real key="value" value="5"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                </userDefinedRuntimeAttributes>
                                                                <connections>
                                                                    <action selector="btnRightAction:" destination="WPC-Az-9dX" eventType="touchUpInside" id="wbK-AU-iVh"/>
                                                                </connections>
                                                            </button>
                                                        </subviews>
                                                    </stackView>
                                                </subviews>
                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstAttribute="bottom" secondItem="mZL-z6-bzw" secondAttribute="bottom" id="BXa-65-oLx"/>
                                                    <constraint firstItem="mZL-z6-bzw" firstAttribute="leading" secondItem="ijh-EE-djc" secondAttribute="leading" id="J0e-sP-Miz"/>
                                                    <constraint firstItem="mZL-z6-bzw" firstAttribute="top" secondItem="ijh-EE-djc" secondAttribute="top" id="cMw-Cg-1sR"/>
                                                    <constraint firstAttribute="trailing" secondItem="mZL-z6-bzw" secondAttribute="trailing" id="zzJ-49-daF"/>
                                                </constraints>
                                            </view>
                                        </subviews>
                                    </stackView>
                                </subviews>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="trailing" secondItem="zzK-q7-4Vz" secondAttribute="trailing" constant="30" id="9rg-Yp-HQA"/>
                                    <constraint firstAttribute="bottom" secondItem="zzK-q7-4Vz" secondAttribute="bottom" constant="25" id="KjW-vW-uwT"/>
                                    <constraint firstItem="zzK-q7-4Vz" firstAttribute="top" secondItem="cDE-aV-iLr" secondAttribute="top" constant="25" id="aqH-h7-k0A"/>
                                    <constraint firstItem="zzK-q7-4Vz" firstAttribute="leading" secondItem="cDE-aV-iLr" secondAttribute="leading" constant="30" id="eAu-sw-Bh1"/>
                                </constraints>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                        <real key="value" value="16"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="jlL-yr-6l4"/>
                        <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="0.5" colorSpace="custom" customColorSpace="calibratedRGB"/>
                        <constraints>
                            <constraint firstItem="cDE-aV-iLr" firstAttribute="top" relation="greaterThanOrEqual" secondItem="jlL-yr-6l4" secondAttribute="top" constant="20" id="4t3-Ef-oGx"/>
                            <constraint firstItem="88q-SU-jZ3" firstAttribute="leading" secondItem="jlL-yr-6l4" secondAttribute="leading" id="Ec8-i0-dME"/>
                            <constraint firstAttribute="bottom" secondItem="88q-SU-jZ3" secondAttribute="bottom" id="LaS-UX-Jhf"/>
                            <constraint firstAttribute="trailing" secondItem="88q-SU-jZ3" secondAttribute="trailing" id="PZP-Ha-WZE"/>
                            <constraint firstItem="jlL-yr-6l4" firstAttribute="bottom" relation="greaterThanOrEqual" secondItem="cDE-aV-iLr" secondAttribute="bottom" constant="20" id="VQ0-q0-S7u"/>
                            <constraint firstItem="cDE-aV-iLr" firstAttribute="leading" secondItem="jlL-yr-6l4" secondAttribute="leading" constant="50" id="VXT-w6-tU6"/>
                            <constraint firstItem="jlL-yr-6l4" firstAttribute="trailing" secondItem="cDE-aV-iLr" secondAttribute="trailing" constant="50" id="fAd-jB-d1E"/>
                            <constraint firstItem="cDE-aV-iLr" firstAttribute="centerY" secondItem="cEj-bl-BXK" secondAttribute="centerY" id="itj-rG-r5m"/>
                            <constraint firstItem="88q-SU-jZ3" firstAttribute="top" secondItem="cEj-bl-BXK" secondAttribute="top" id="ni9-A3-tZZ"/>
                            <constraint firstItem="cDE-aV-iLr" firstAttribute="centerX" secondItem="cEj-bl-BXK" secondAttribute="centerX" id="zqo-hh-6bC"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="btnCenterOk" destination="YbA-mH-0fk" id="hkj-uU-MYT"/>
                        <outlet property="btnLeft" destination="2yx-lJ-FFy" id="9u3-so-VU8"/>
                        <outlet property="btnRight" destination="Ri0-C2-aMj" id="DGf-S6-1AZ"/>
                        <outlet property="lblDescription" destination="fdP-gS-7MY" id="fo1-g5-tXf"/>
                        <outlet property="stackView" destination="mZL-z6-bzw" id="9Oz-Rr-qcS"/>
                        <outlet property="viewCenterButton" destination="tWS-p4-pTT" id="5AR-l4-DMW"/>
                        <outlet property="viewMultipleButton" destination="ijh-EE-djc" id="EaN-dD-YdU"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="Wu7-au-One" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1471" y="-177"/>
        </scene>
    </scenes>
    <resources>
        <image name="down_arrow" width="13.5" height="8"/>
        <image name="icn_back" width="13.5" height="21.5"/>
        <image name="icn_delete_grey" width="15.5" height="16.5"/>
        <image name="icn_instagram" width="14.5" height="14.5"/>
        <image name="icn_phone" width="17.5" height="14.5"/>
        <image name="icn_radioSelected" width="22" height="21"/>
        <image name="icn_radioUnselected" width="22" height="22"/>
        <image name="icn_star_selected_1" width="96" height="96"/>
        <image name="icn_star_unselected_1" width="96" height="96"/>
        <image name="icn_twitter" width="17.5" height="14.5"/>
        <image name="location_pin_deselected" width="19" height="25"/>
        <image name="no_order_box" width="258" height="258"/>
        <image name="plus" width="14" height="14"/>
        <namedColor name="AppTheme_BlueColor_#012CDA">
            <color red="0.0039215686274509803" green="0.17254901960784313" blue="0.85490196078431369" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="AppTheme_BorderColor_#1F1F1F">
            <color red="0.12156862745098039" green="0.12156862745098039" blue="0.12156862745098039" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="AppTheme_DiscountGreenColor_#05B13E">
            <color red="0.019607843137254902" green="0.69411764705882351" blue="0.24313725490196078" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="AppTheme_FieldBGColor_#F7F7F7">
            <color red="0.96862745098039216" green="0.96862745098039216" blue="0.96862745098039216" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="AppTheme_GrayTextColor_#767676">
            <color red="0.46274509803921571" green="0.46274509803921571" blue="0.46274509803921571" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="AppTheme_LightGrayColor_#B4B2B2">
            <color red="0.70588235294117652" green="0.69803921568627447" blue="0.69803921568627447" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="AppTheme_LightGrayColor_#CECECE">
            <color red="0.80784313725490198" green="0.80784313725490198" blue="0.80784313725490198" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="AppTheme_LightGrayOrderColor_#EFEFEF">
            <color red="0.93725490196078431" green="0.93725490196078431" blue="0.93725490196078431" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="AppTheme_SelectedTabColor_#F4BA45">
            <color red="0.95686274509803926" green="0.72941176470588232" blue="0.27058823529411763" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="AppTheme_SubLabelLightGrayColor_#B2B2B2">
            <color red="0.69803921568627447" green="0.69803921568627447" blue="0.69803921568627447" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="AppTheme_TextColor_#222222">
            <color red="0.13333333333333333" green="0.13333333333333333" blue="0.13333333333333333" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <systemColor name="labelColor">
            <color red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </systemColor>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
