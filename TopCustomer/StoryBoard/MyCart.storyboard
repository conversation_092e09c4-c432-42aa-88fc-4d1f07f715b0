<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="32700.99.1234" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22685"/>
        <capability name="Named colors" minToolsVersion="9.0"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="collection view cell content view" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="LoewNextArabic-Bold.ttf">
            <string>LoewNextArabic-Bold</string>
        </array>
        <array key="LoewNextArabic-Heavy.ttf">
            <string>LoewNextArabic-Heavy</string>
        </array>
        <array key="LoewNextArabic-Medium.ttf">
            <string>LoewNextArabic-Medium</string>
        </array>
    </customFonts>
    <scenes>
        <!--My Cart View Controller-->
        <scene sceneID="s0d-6b-0kx">
            <objects>
                <viewController storyboardIdentifier="MyCartViewController" id="Y6W-OH-hqX" customClass="MyCartViewController" customModule="TopCustomer" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="5EZ-qb-Rvc">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" dataMode="prototypes" style="plain" separatorStyle="none" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="28" sectionFooterHeight="28" translatesAutoresizingMaskIntoConstraints="NO" id="MNs-Fa-SBh">
                                <rect key="frame" x="0.0" y="108" width="414" height="624"/>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <connections>
                                    <outlet property="dataSource" destination="Y6W-OH-hqX" id="Pkx-kK-xGe"/>
                                    <outlet property="delegate" destination="Y6W-OH-hqX" id="VDd-Ms-eZK"/>
                                </connections>
                            </tableView>
                            <stackView hidden="YES" opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="30" translatesAutoresizingMaskIntoConstraints="NO" id="skz-Vn-90X">
                                <rect key="frame" x="50" y="279" width="314" height="338.5"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="4it-rA-HZ8">
                                        <rect key="frame" x="0.0" y="0.0" width="314" height="213.5"/>
                                        <subviews>
                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="icn_cart_blue" translatesAutoresizingMaskIntoConstraints="NO" id="KuQ-Sx-HLz" customClass="MaterialLocalizeImageView" customModule="TopCustomer" customModuleProvider="target">
                                                <rect key="frame" x="67" y="20" width="180" height="173.5"/>
                                            </imageView>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstItem="KuQ-Sx-HLz" firstAttribute="top" secondItem="4it-rA-HZ8" secondAttribute="top" constant="20" id="9kr-LK-8pe"/>
                                            <constraint firstAttribute="bottom" secondItem="KuQ-Sx-HLz" secondAttribute="bottom" constant="20" id="MfS-03-N9o"/>
                                            <constraint firstItem="KuQ-Sx-HLz" firstAttribute="centerX" secondItem="4it-rA-HZ8" secondAttribute="centerX" id="WH3-WL-4J8"/>
                                            <constraint firstItem="KuQ-Sx-HLz" firstAttribute="centerY" secondItem="4it-rA-HZ8" secondAttribute="centerY" id="tk1-9g-Jzv"/>
                                        </constraints>
                                    </view>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="252" verticalHuggingPriority="251" text="Your Cart is Currently Empty !" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="dO0-1v-NLR">
                                        <rect key="frame" x="0.0" y="243.5" width="314" height="15"/>
                                        <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="15"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="CWL-ST-0zF">
                                        <rect key="frame" x="0.0" y="288.5" width="314" height="50"/>
                                        <subviews>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="qld-w9-A05" customClass="CustomRoundedButtton" customModule="TopCustomer" customModuleProvider="target">
                                                <rect key="frame" x="97.5" y="5" width="119" height="40"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="40" id="afA-PL-tFn"/>
                                                </constraints>
                                                <inset key="contentEdgeInsets" minX="15" minY="0.0" maxX="15" maxY="0.0"/>
                                                <state key="normal" title="Go Shopping"/>
                                                <connections>
                                                    <action selector="btnGoShoppingAction:" destination="Y6W-OH-hqX" eventType="touchUpInside" id="RPZ-tv-pwN"/>
                                                </connections>
                                            </button>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstItem="qld-w9-A05" firstAttribute="top" secondItem="CWL-ST-0zF" secondAttribute="top" constant="5" id="0d2-gn-nMN"/>
                                            <constraint firstItem="qld-w9-A05" firstAttribute="centerX" secondItem="CWL-ST-0zF" secondAttribute="centerX" id="IrS-0k-Tou"/>
                                            <constraint firstAttribute="bottom" secondItem="qld-w9-A05" secondAttribute="bottom" constant="5" id="Lk4-tz-dM8"/>
                                        </constraints>
                                    </view>
                                </subviews>
                            </stackView>
                            <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="jyE-OG-SpL">
                                <rect key="frame" x="0.0" y="752" width="414" height="30"/>
                                <subviews>
                                    <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="10" translatesAutoresizingMaskIntoConstraints="NO" id="TyL-jK-1tC">
                                        <rect key="frame" x="38" y="20" width="338" height="10"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Subtotal" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="H33-9b-VCk" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                <rect key="frame" x="0.0" y="0.0" width="164" height="10"/>
                                                <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="10"/>
                                                <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="2ti-jJ-ywp" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                <rect key="frame" x="174" y="0.0" width="164" height="10"/>
                                                <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="9"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                        </subviews>
                                    </stackView>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="9Qt-IZ-oHG">
                                        <rect key="frame" x="38" y="37" width="338" height="1"/>
                                        <color key="backgroundColor" name="AppTheme_LightGrayColor_#B4B2B2"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="1" id="PzO-DV-vfL"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                        <nil key="textColor"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <stackView hidden="YES" opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="10" translatesAutoresizingMaskIntoConstraints="NO" id="6DY-7t-z2e">
                                        <rect key="frame" x="38" y="30" width="338" height="0.0"/>
                                        <subviews>
                                            <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Delivery" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="sjf-Ku-omU">
                                                <rect key="frame" x="0.0" y="0.0" width="0.0" height="10"/>
                                                <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="10"/>
                                                <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Tfz-GG-8l2">
                                                <rect key="frame" x="0.0" y="0.0" width="0.0" height="10"/>
                                                <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="9"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                        </subviews>
                                    </stackView>
                                    <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Pdl-1q-kmt">
                                        <rect key="frame" x="38" y="37" width="338" height="1"/>
                                        <color key="backgroundColor" name="AppTheme_LightGrayColor_#B4B2B2"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="1" id="NfK-ZD-xf1"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                        <nil key="textColor"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <stackView hidden="YES" opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="10" translatesAutoresizingMaskIntoConstraints="NO" id="gwr-8h-90s">
                                        <rect key="frame" x="38" y="30" width="338" height="0.0"/>
                                        <subviews>
                                            <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Vat" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="0NN-JV-TXI">
                                                <rect key="frame" x="0.0" y="0.0" width="0.0" height="10"/>
                                                <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="10"/>
                                                <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="1A3-Jx-8sz">
                                                <rect key="frame" x="0.0" y="0.0" width="0.0" height="10"/>
                                                <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="9"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                        </subviews>
                                    </stackView>
                                    <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="3IG-B6-9u9">
                                        <rect key="frame" x="38" y="37" width="338" height="1"/>
                                        <color key="backgroundColor" name="AppTheme_LightGrayColor_#B4B2B2"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="1" id="At3-Gb-khu"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                        <nil key="textColor"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <stackView hidden="YES" opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="10" translatesAutoresizingMaskIntoConstraints="NO" id="Jez-TL-aid">
                                        <rect key="frame" x="38" y="30" width="338" height="0.0"/>
                                        <subviews>
                                            <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Total" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="tQa-my-mBF">
                                                <rect key="frame" x="0.0" y="0.0" width="0.0" height="10"/>
                                                <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="10"/>
                                                <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="SBc-ag-KUO">
                                                <rect key="frame" x="0.0" y="0.0" width="0.0" height="10"/>
                                                <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="9"/>
                                                <color key="textColor" name="AppTheme_BlueColor_#012CDA"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                        </subviews>
                                    </stackView>
                                </subviews>
                                <constraints>
                                    <constraint firstItem="3IG-B6-9u9" firstAttribute="leading" secondItem="TyL-jK-1tC" secondAttribute="leading" id="4z7-hd-vT4"/>
                                    <constraint firstItem="gwr-8h-90s" firstAttribute="top" secondItem="6DY-7t-z2e" secondAttribute="bottom" id="6Yg-xK-NmJ"/>
                                    <constraint firstItem="TyL-jK-1tC" firstAttribute="leading" secondItem="jyE-OG-SpL" secondAttribute="leading" constant="38" id="891-Vf-BeP"/>
                                    <constraint firstAttribute="trailing" secondItem="TyL-jK-1tC" secondAttribute="trailing" constant="38" id="B6L-fd-CuP"/>
                                    <constraint firstItem="3IG-B6-9u9" firstAttribute="top" secondItem="gwr-8h-90s" secondAttribute="bottom" constant="7" id="Q1X-hg-370"/>
                                    <constraint firstItem="gwr-8h-90s" firstAttribute="leading" secondItem="TyL-jK-1tC" secondAttribute="leading" id="SV6-j7-987"/>
                                    <constraint firstItem="3IG-B6-9u9" firstAttribute="trailing" secondItem="TyL-jK-1tC" secondAttribute="trailing" id="UfZ-aM-YCb"/>
                                    <constraint firstItem="Pdl-1q-kmt" firstAttribute="top" secondItem="6DY-7t-z2e" secondAttribute="bottom" constant="7" id="eg6-nD-J59"/>
                                    <constraint firstItem="gwr-8h-90s" firstAttribute="trailing" secondItem="TyL-jK-1tC" secondAttribute="trailing" id="g0R-33-k1A"/>
                                    <constraint firstItem="9Qt-IZ-oHG" firstAttribute="trailing" secondItem="TyL-jK-1tC" secondAttribute="trailing" id="h5c-hK-Ofu"/>
                                    <constraint firstItem="Jez-TL-aid" firstAttribute="leading" secondItem="TyL-jK-1tC" secondAttribute="leading" id="jtq-Zh-Xde"/>
                                    <constraint firstItem="6DY-7t-z2e" firstAttribute="top" secondItem="TyL-jK-1tC" secondAttribute="bottom" id="kjg-ZF-8He"/>
                                    <constraint firstItem="Pdl-1q-kmt" firstAttribute="trailing" secondItem="TyL-jK-1tC" secondAttribute="trailing" id="lnt-XF-fx7"/>
                                    <constraint firstItem="6DY-7t-z2e" firstAttribute="trailing" secondItem="TyL-jK-1tC" secondAttribute="trailing" id="mig-hT-Hne"/>
                                    <constraint firstItem="Jez-TL-aid" firstAttribute="trailing" secondItem="TyL-jK-1tC" secondAttribute="trailing" id="n2j-5M-i7N"/>
                                    <constraint firstItem="Jez-TL-aid" firstAttribute="top" secondItem="gwr-8h-90s" secondAttribute="bottom" id="o13-Kf-Vm5"/>
                                    <constraint firstAttribute="bottom" secondItem="Jez-TL-aid" secondAttribute="bottom" id="pCf-pv-ASy"/>
                                    <constraint firstItem="Pdl-1q-kmt" firstAttribute="leading" secondItem="TyL-jK-1tC" secondAttribute="leading" id="phH-Iv-BMP"/>
                                    <constraint firstItem="9Qt-IZ-oHG" firstAttribute="leading" secondItem="TyL-jK-1tC" secondAttribute="leading" id="rDs-DB-puL"/>
                                    <constraint firstItem="TyL-jK-1tC" firstAttribute="top" secondItem="jyE-OG-SpL" secondAttribute="top" constant="20" id="rEm-F0-4ze"/>
                                    <constraint firstItem="6DY-7t-z2e" firstAttribute="leading" secondItem="TyL-jK-1tC" secondAttribute="leading" id="rI9-2H-VqE"/>
                                    <constraint firstItem="9Qt-IZ-oHG" firstAttribute="top" secondItem="TyL-jK-1tC" secondAttribute="bottom" constant="7" id="uJV-dL-60x"/>
                                </constraints>
                            </view>
                            <button hidden="YES" opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="79h-Sf-gHK" customClass="CustomRoundedButtton" customModule="TopCustomer" customModuleProvider="target">
                                <rect key="frame" x="38" y="802" width="338" height="40"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="40" id="uFB-9w-dat"/>
                                </constraints>
                                <inset key="contentEdgeInsets" minX="15" minY="0.0" maxX="15" maxY="0.0"/>
                                <state key="normal" title="Checkout now"/>
                                <connections>
                                    <action selector="btnCheckoutNowAction:" destination="Y6W-OH-hqX" eventType="touchUpInside" id="s5n-41-em8"/>
                                </connections>
                            </button>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="fBC-pj-ofK" customClass="CustomTopViewForShadow" customModule="TopCustomer" customModuleProvider="target">
                                <rect key="frame" x="0.0" y="48" width="414" height="60"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="NG8-uD-YeK" customClass="MaterialLocalizeButton" customModule="TopCustomer" customModuleProvider="target">
                                        <rect key="frame" x="15" y="12.5" width="35" height="35"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="35" id="84q-af-RMb"/>
                                            <constraint firstAttribute="width" constant="35" id="r3O-o3-kcw"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="12"/>
                                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                        <state key="normal" image="icn_back">
                                            <color key="titleColor" name="AppTheme_BlueColor_#012CDA"/>
                                        </state>
                                        <connections>
                                            <action selector="btnBackAction:" destination="Y6W-OH-hqX" eventType="touchUpInside" id="kmg-Rx-t90"/>
                                        </connections>
                                    </button>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="My Cart" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="FMl-eW-0Am">
                                        <rect key="frame" x="160" y="18.5" width="94.5" height="23"/>
                                        <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="23"/>
                                        <color key="textColor" name="AppTheme_BlueColor_#012CDA"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="60" id="BWY-8P-cE9"/>
                                    <constraint firstItem="NG8-uD-YeK" firstAttribute="centerY" secondItem="fBC-pj-ofK" secondAttribute="centerY" id="BjB-08-vVL"/>
                                    <constraint firstItem="NG8-uD-YeK" firstAttribute="leading" secondItem="fBC-pj-ofK" secondAttribute="leading" constant="15" id="gC7-5v-mBH"/>
                                    <constraint firstItem="FMl-eW-0Am" firstAttribute="centerX" secondItem="fBC-pj-ofK" secondAttribute="centerX" id="vnT-rZ-PBy"/>
                                    <constraint firstItem="FMl-eW-0Am" firstAttribute="centerY" secondItem="fBC-pj-ofK" secondAttribute="centerY" id="w6M-5J-UD9"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="vDu-zF-Fre"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="vDu-zF-Fre" firstAttribute="bottom" secondItem="79h-Sf-gHK" secondAttribute="bottom" constant="20" id="3ix-jP-1wR"/>
                            <constraint firstItem="fBC-pj-ofK" firstAttribute="leading" secondItem="vDu-zF-Fre" secondAttribute="leading" id="7zs-AC-xPV"/>
                            <constraint firstAttribute="trailing" secondItem="fBC-pj-ofK" secondAttribute="trailing" id="CcY-YV-lBs"/>
                            <constraint firstItem="vDu-zF-Fre" firstAttribute="trailing" secondItem="MNs-Fa-SBh" secondAttribute="trailing" id="Dhh-xI-awf"/>
                            <constraint firstItem="79h-Sf-gHK" firstAttribute="leading" secondItem="vDu-zF-Fre" secondAttribute="leading" constant="38" id="IDO-Di-f54"/>
                            <constraint firstItem="jyE-OG-SpL" firstAttribute="leading" secondItem="vDu-zF-Fre" secondAttribute="leading" id="Llr-Fy-aeJ"/>
                            <constraint firstItem="vDu-zF-Fre" firstAttribute="trailing" secondItem="79h-Sf-gHK" secondAttribute="trailing" constant="38" id="NIe-I7-uXK"/>
                            <constraint firstItem="fBC-pj-ofK" firstAttribute="top" secondItem="vDu-zF-Fre" secondAttribute="top" id="Ugb-oj-SWg"/>
                            <constraint firstItem="skz-Vn-90X" firstAttribute="centerY" secondItem="5EZ-qb-Rvc" secondAttribute="centerY" id="aXf-UG-5kw"/>
                            <constraint firstItem="vDu-zF-Fre" firstAttribute="trailing" secondItem="skz-Vn-90X" secondAttribute="trailing" constant="50" id="hiF-sD-JPt"/>
                            <constraint firstItem="MNs-Fa-SBh" firstAttribute="top" secondItem="fBC-pj-ofK" secondAttribute="bottom" id="iMk-gf-hX1"/>
                            <constraint firstItem="vDu-zF-Fre" firstAttribute="trailing" secondItem="jyE-OG-SpL" secondAttribute="trailing" id="nFz-j6-BMn"/>
                            <constraint firstItem="skz-Vn-90X" firstAttribute="leading" secondItem="vDu-zF-Fre" secondAttribute="leading" constant="50" id="nfk-Um-BBh"/>
                            <constraint firstItem="MNs-Fa-SBh" firstAttribute="leading" secondItem="vDu-zF-Fre" secondAttribute="leading" id="qz3-Ar-esL"/>
                            <constraint firstItem="skz-Vn-90X" firstAttribute="centerX" secondItem="5EZ-qb-Rvc" secondAttribute="centerX" id="tjf-dJ-dL1"/>
                            <constraint firstItem="79h-Sf-gHK" firstAttribute="centerX" secondItem="5EZ-qb-Rvc" secondAttribute="centerX" id="wy8-Nd-hQL"/>
                            <constraint firstItem="jyE-OG-SpL" firstAttribute="top" secondItem="MNs-Fa-SBh" secondAttribute="bottom" constant="20" id="yN4-sJ-Cyz"/>
                            <constraint firstItem="79h-Sf-gHK" firstAttribute="top" secondItem="jyE-OG-SpL" secondAttribute="bottom" constant="20" id="zJu-J5-TDq"/>
                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" id="UK5-Zw-xsE"/>
                    <connections>
                        <outlet property="btnCheckoutNow" destination="79h-Sf-gHK" id="hMh-n1-rFK"/>
                        <outlet property="btnGoShopping" destination="qld-w9-A05" id="e99-av-Kte"/>
                        <outlet property="lblDelivery" destination="Tfz-GG-8l2" id="fpg-nc-zsy"/>
                        <outlet property="lblDeliveryTitle" destination="sjf-Ku-omU" id="kOy-vr-SXk"/>
                        <outlet property="lblEmptyCart" destination="dO0-1v-NLR" id="Vji-S3-1Wk"/>
                        <outlet property="lblSubTotal" destination="2ti-jJ-ywp" id="wTm-Os-ZIx"/>
                        <outlet property="lblSubTotalTitle" destination="H33-9b-VCk" id="n0n-Y2-B69"/>
                        <outlet property="lblTitle" destination="FMl-eW-0Am" id="R9D-cZ-kG8"/>
                        <outlet property="lblTotal" destination="SBc-ag-KUO" id="BBt-7C-9EA"/>
                        <outlet property="lblTotalTitle" destination="tQa-my-mBF" id="LQS-dB-DG7"/>
                        <outlet property="lblVat" destination="1A3-Jx-8sz" id="VAw-SE-Ko6"/>
                        <outlet property="lblVatTitle" destination="0NN-JV-TXI" id="6LT-Ja-Seb"/>
                        <outlet property="stackEmptyCart" destination="skz-Vn-90X" id="iL0-Od-HSY"/>
                        <outlet property="tblMyCart" destination="MNs-Fa-SBh" id="Ynk-nJ-qRW"/>
                        <outlet property="viewPrices" destination="jyE-OG-SpL" id="K97-MF-6iJ"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="Ief-a0-LHa" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="782.60869565217399" y="55.580357142857139"/>
        </scene>
        <!--Checkout View Controller-->
        <scene sceneID="p3t-vF-6Zq">
            <objects>
                <viewController storyboardIdentifier="CheckoutViewController" id="yg8-rb-Hfq" customClass="CheckoutViewController" customModule="TopCustomer" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="cRc-wo-CE5">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="1200"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" ambiguous="YES" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Xch-Fl-o5n">
                                <rect key="frame" x="0.0" y="112" width="414" height="1200"/>
                                <subviews>
                                    <view contentMode="scaleToFill" ambiguous="YES" translatesAutoresizingMaskIntoConstraints="NO" id="z6Y-6U-e5S">
                                        <rect key="frame" x="0.0" y="0.0" width="414" height="1250"/>
                                        <subviews>
                                            <stackView opaque="NO" contentMode="scaleToFill" ambiguous="YES" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="RTb-5t-QpU">
                                                <rect key="frame" x="0.0" y="4" width="414" height="225"/>
                                                <subviews>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="X1P-Vc-Srj">
                                                        <rect key="frame" x="0.0" y="0.0" width="414" height="60"/>
                                                        <subviews>
                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="GTW-GJ-J7N">
                                                                <rect key="frame" x="20" y="2" width="374" height="56"/>
                                                                <subviews>
                                                                    <stackView opaque="NO" contentMode="scaleToFill" alignment="center" spacing="3" translatesAutoresizingMaskIntoConstraints="NO" id="KI2-eE-fTA">
                                                                        <rect key="frame" x="0.0" y="0.0" width="374" height="56"/>
                                                                        <subviews>
                                                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="aLm-QO-Zpq">
                                                                                <rect key="frame" x="0.0" y="0.0" width="60" height="56"/>
                                                                                <subviews>
                                                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="WOc-HA-fYZ">
                                                                                        <rect key="frame" x="0.0" y="0.0" width="60" height="56"/>
                                                                                        <subviews>
                                                                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="dollar-coins" translatesAutoresizingMaskIntoConstraints="NO" id="j8h-oC-UAo">
                                                                                                <rect key="frame" x="0.0" y="-2" width="60" height="60"/>
                                                                                                <constraints>
                                                                                                    <constraint firstAttribute="width" constant="60" id="Mvc-zx-XLD"/>
                                                                                                    <constraint firstAttribute="height" constant="60" id="kWh-NF-arc"/>
                                                                                                </constraints>
                                                                                            </imageView>
                                                                                        </subviews>
                                                                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                        <constraints>
                                                                                            <constraint firstItem="j8h-oC-UAo" firstAttribute="centerX" secondItem="WOc-HA-fYZ" secondAttribute="centerX" id="RCj-lK-KuT"/>
                                                                                            <constraint firstItem="j8h-oC-UAo" firstAttribute="centerY" secondItem="WOc-HA-fYZ" secondAttribute="centerY" id="pDq-Vy-ZjV"/>
                                                                                            <constraint firstAttribute="width" constant="60" id="xxM-uF-oTN"/>
                                                                                        </constraints>
                                                                                    </view>
                                                                                </subviews>
                                                                            </stackView>
                                                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="5" translatesAutoresizingMaskIntoConstraints="NO" id="YXY-vU-4q8">
                                                                                <rect key="frame" x="63" y="22" width="280" height="12"/>
                                                                                <subviews>
                                                                                    <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Congrats! " lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="yKC-iC-Zg6" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                                                        <rect key="frame" x="0.0" y="0.0" width="280" height="0.0"/>
                                                                                        <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="12"/>
                                                                                        <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                        <nil key="highlightedColor"/>
                                                                                    </label>
                                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="You will get coins by completing your order." lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="SXN-Ig-Ipo" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                                                        <rect key="frame" x="0.0" y="0.0" width="280" height="12"/>
                                                                                        <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="12"/>
                                                                                        <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                        <nil key="highlightedColor"/>
                                                                                    </label>
                                                                                </subviews>
                                                                            </stackView>
                                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="qzt-oN-ABN">
                                                                                <rect key="frame" x="346" y="0.0" width="20" height="56"/>
                                                                                <subviews>
                                                                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="information-icon" translatesAutoresizingMaskIntoConstraints="NO" id="tlD-hl-2bh">
                                                                                        <rect key="frame" x="3" y="21" width="14" height="14"/>
                                                                                        <constraints>
                                                                                            <constraint firstAttribute="width" constant="14" id="02d-DZ-wup"/>
                                                                                            <constraint firstAttribute="height" constant="14" id="nHN-P0-3cd"/>
                                                                                        </constraints>
                                                                                    </imageView>
                                                                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="LRC-LB-NJ6">
                                                                                        <rect key="frame" x="0.0" y="0.0" width="20" height="56"/>
                                                                                        <state key="normal" title="Button"/>
                                                                                        <buttonConfiguration key="configuration" style="plain" title=" "/>
                                                                                        <connections>
                                                                                            <action selector="actionInfoPoints:" destination="yg8-rb-Hfq" eventType="touchUpInside" id="hL0-Ut-pqo"/>
                                                                                        </connections>
                                                                                    </button>
                                                                                </subviews>
                                                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                <constraints>
                                                                                    <constraint firstItem="tlD-hl-2bh" firstAttribute="centerX" secondItem="qzt-oN-ABN" secondAttribute="centerX" id="QDK-Ca-gLk"/>
                                                                                    <constraint firstItem="LRC-LB-NJ6" firstAttribute="leading" secondItem="qzt-oN-ABN" secondAttribute="leading" id="Rhc-fx-ggc"/>
                                                                                    <constraint firstAttribute="width" constant="20" id="lgz-bZ-HMP"/>
                                                                                    <constraint firstAttribute="trailing" secondItem="LRC-LB-NJ6" secondAttribute="trailing" id="nKy-w2-MHS"/>
                                                                                    <constraint firstAttribute="bottom" secondItem="LRC-LB-NJ6" secondAttribute="bottom" id="o9E-9G-fS5"/>
                                                                                    <constraint firstItem="LRC-LB-NJ6" firstAttribute="top" secondItem="qzt-oN-ABN" secondAttribute="top" id="uXI-yB-Rl9"/>
                                                                                    <constraint firstItem="tlD-hl-2bh" firstAttribute="centerY" secondItem="qzt-oN-ABN" secondAttribute="centerY" id="xpB-Ab-jU6"/>
                                                                                </constraints>
                                                                            </view>
                                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ueI-Vc-5HU">
                                                                                <rect key="frame" x="369" y="0.0" width="5" height="56"/>
                                                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                <constraints>
                                                                                    <constraint firstAttribute="width" constant="5" id="7bJ-uX-182"/>
                                                                                </constraints>
                                                                            </view>
                                                                        </subviews>
                                                                    </stackView>
                                                                </subviews>
                                                                <color key="backgroundColor" name="AppTheme_VeryLightStatusLineColor_#EFEFEF"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="trailing" secondItem="KI2-eE-fTA" secondAttribute="trailing" id="JKz-tw-qiE"/>
                                                                    <constraint firstAttribute="bottom" secondItem="KI2-eE-fTA" secondAttribute="bottom" id="UaT-u8-vfZ"/>
                                                                    <constraint firstItem="KI2-eE-fTA" firstAttribute="leading" secondItem="GTW-GJ-J7N" secondAttribute="leading" id="bJZ-jw-6Iv"/>
                                                                    <constraint firstItem="KI2-eE-fTA" firstAttribute="top" secondItem="GTW-GJ-J7N" secondAttribute="top" id="bZh-NJ-IpZ"/>
                                                                </constraints>
                                                                <userDefinedRuntimeAttributes>
                                                                    <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                                                                        <real key="value" value="1"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                    <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                                                                        <color key="value" name="AppTheme_BorderColor_#1F1F1F"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                                        <real key="value" value="7"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                </userDefinedRuntimeAttributes>
                                                            </view>
                                                        </subviews>
                                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <constraints>
                                                            <constraint firstAttribute="trailing" secondItem="GTW-GJ-J7N" secondAttribute="trailing" constant="20" id="HvS-GC-KfS"/>
                                                            <constraint firstAttribute="bottom" secondItem="GTW-GJ-J7N" secondAttribute="bottom" constant="2" id="Uoq-39-C2O"/>
                                                            <constraint firstItem="GTW-GJ-J7N" firstAttribute="leading" secondItem="X1P-Vc-Srj" secondAttribute="leading" constant="20" id="efJ-Eb-iZY"/>
                                                            <constraint firstItem="GTW-GJ-J7N" firstAttribute="top" secondItem="X1P-Vc-Srj" secondAttribute="top" constant="2" id="tm7-m9-fWm"/>
                                                            <constraint firstAttribute="height" constant="60" id="wTG-GC-beg"/>
                                                        </constraints>
                                                    </view>
                                                    <tableView clipsSubviews="YES" contentMode="scaleToFill" ambiguous="YES" alwaysBounceVertical="YES" scrollEnabled="NO" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" dataMode="prototypes" style="plain" separatorStyle="none" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="1" sectionFooterHeight="1" translatesAutoresizingMaskIntoConstraints="NO" id="xDI-wE-j0g" customClass="ContentSizedTableView" customModule="TopCustomer" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="60" width="414" height="165"/>
                                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" relation="greaterThanOrEqual" priority="1" constant="225" id="k81-8c-8QI"/>
                                                        </constraints>
                                                        <connections>
                                                            <outlet property="dataSource" destination="yg8-rb-Hfq" id="DjV-o3-Zmp"/>
                                                            <outlet property="delegate" destination="yg8-rb-Hfq" id="hTe-Su-oAD"/>
                                                        </connections>
                                                    </tableView>
                                                </subviews>
                                                <constraints>
                                                    <constraint firstAttribute="height" relation="greaterThanOrEqual" constant="225" id="HcZ-d9-PGI"/>
                                                    <constraint firstItem="X1P-Vc-Srj" firstAttribute="top" secondItem="RTb-5t-QpU" secondAttribute="top" id="Xf3-FE-ka3"/>
                                                    <constraint firstItem="X1P-Vc-Srj" firstAttribute="leading" secondItem="RTb-5t-QpU" secondAttribute="leading" id="dki-HJ-Ps7"/>
                                                    <constraint firstAttribute="trailing" secondItem="X1P-Vc-Srj" secondAttribute="trailing" id="o3f-Bp-0wh"/>
                                                    <constraint firstItem="xDI-wE-j0g" firstAttribute="top" secondItem="X1P-Vc-Srj" secondAttribute="bottom" id="vgj-sY-IKn"/>
                                                </constraints>
                                            </stackView>
                                            <view clipsSubviews="YES" contentMode="scaleToFill" ambiguous="YES" translatesAutoresizingMaskIntoConstraints="NO" id="Eqk-fH-JBy">
                                                <rect key="frame" x="20" y="239" width="374" height="61"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Driver will call" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="oC5-US-QyS" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                        <rect key="frame" x="20" y="10" width="98.5" height="14"/>
                                                        <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="14"/>
                                                        <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <button opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="xrL-oh-uVS">
                                                        <rect key="frame" x="133.5" y="0.0" width="40" height="34"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="34" id="1g8-ka-2vc"/>
                                                            <constraint firstAttribute="width" constant="40" id="KrD-wJ-jde"/>
                                                        </constraints>
                                                        <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="11"/>
                                                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                        <state key="normal">
                                                            <color key="titleColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        </state>
                                                        <connections>
                                                            <action selector="actionCountryCode:" destination="yg8-rb-Hfq" eventType="touchUpInside" id="XhJ-JS-OHf"/>
                                                        </connections>
                                                    </button>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="CKC-Lc-veG">
                                                        <rect key="frame" x="133.5" y="24" width="40" height="1"/>
                                                        <color key="backgroundColor" name="AppTheme_LightGrayColor_#B4B2B2"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="1" id="gdR-7a-1FA"/>
                                                        </constraints>
                                                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                        <nil key="textColor"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="0qr-mj-YPh">
                                                        <rect key="frame" x="183.5" y="0.0" width="90" height="34"/>
                                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <constraints>
                                                            <constraint firstAttribute="width" constant="90" id="Dqr-Hn-0Qf"/>
                                                            <constraint firstAttribute="height" constant="34" id="ozM-Th-WWL"/>
                                                        </constraints>
                                                        <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="11"/>
                                                        <textInputTraits key="textInputTraits" autocorrectionType="no" keyboardType="ASCIICapableNumberPad"/>
                                                        <connections>
                                                            <outlet property="delegate" destination="yg8-rb-Hfq" id="5ZL-rq-RWr"/>
                                                        </connections>
                                                    </textField>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="LGA-wB-7Yo">
                                                        <rect key="frame" x="183.5" y="24" width="90" height="1"/>
                                                        <color key="backgroundColor" name="AppTheme_LightGrayColor_#B4B2B2"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="1" id="FIn-Lz-TiO"/>
                                                        </constraints>
                                                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                        <nil key="textColor"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Upon arrival, driver will call this number" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="nEZ-He-goZ" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                        <rect key="frame" x="20" y="39" width="334" height="12"/>
                                                        <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="12"/>
                                                        <color key="textColor" name="AppTheme_DarkGrayColor_#5D5D5D"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                </subviews>
                                                <color key="backgroundColor" name="AppTheme_FieldBGColor_#F7F7F7"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" id="54a-4x-mFK"/>
                                                    <constraint firstItem="LGA-wB-7Yo" firstAttribute="leading" secondItem="0qr-mj-YPh" secondAttribute="leading" id="5ju-E6-2Tu"/>
                                                    <constraint firstAttribute="trailing" secondItem="nEZ-He-goZ" secondAttribute="trailing" constant="20" id="CH8-4s-KEQ"/>
                                                    <constraint firstItem="nEZ-He-goZ" firstAttribute="leading" secondItem="oC5-US-QyS" secondAttribute="leading" id="Vyo-df-E2H"/>
                                                    <constraint firstItem="oC5-US-QyS" firstAttribute="top" secondItem="Eqk-fH-JBy" secondAttribute="top" constant="10" id="YoK-T7-27A"/>
                                                    <constraint firstItem="CKC-Lc-veG" firstAttribute="trailing" secondItem="xrL-oh-uVS" secondAttribute="trailing" id="c03-Zx-fjw"/>
                                                    <constraint firstItem="LGA-wB-7Yo" firstAttribute="top" secondItem="0qr-mj-YPh" secondAttribute="bottom" constant="-10" id="c5h-T0-cEr"/>
                                                    <constraint firstItem="xrL-oh-uVS" firstAttribute="centerY" secondItem="oC5-US-QyS" secondAttribute="centerY" id="cDb-ae-YqI"/>
                                                    <constraint firstAttribute="bottom" secondItem="nEZ-He-goZ" secondAttribute="bottom" constant="10" id="g4E-ds-IlM"/>
                                                    <constraint firstItem="CKC-Lc-veG" firstAttribute="leading" secondItem="xrL-oh-uVS" secondAttribute="leading" id="iUn-Dl-6wY"/>
                                                    <constraint firstItem="oC5-US-QyS" firstAttribute="leading" secondItem="Eqk-fH-JBy" secondAttribute="leading" constant="20" id="iwb-ul-yEc"/>
                                                    <constraint firstItem="nEZ-He-goZ" firstAttribute="top" secondItem="oC5-US-QyS" secondAttribute="bottom" constant="15" id="lTh-ws-ox0"/>
                                                    <constraint firstItem="xrL-oh-uVS" firstAttribute="leading" secondItem="oC5-US-QyS" secondAttribute="trailing" constant="15" id="pFF-L8-oTY"/>
                                                    <constraint firstItem="LGA-wB-7Yo" firstAttribute="trailing" secondItem="0qr-mj-YPh" secondAttribute="trailing" id="qQK-ht-fXy"/>
                                                    <constraint firstItem="0qr-mj-YPh" firstAttribute="leading" secondItem="xrL-oh-uVS" secondAttribute="trailing" constant="10" id="quw-4x-cxL"/>
                                                    <constraint firstItem="0qr-mj-YPh" firstAttribute="centerY" secondItem="oC5-US-QyS" secondAttribute="centerY" id="wXl-8S-0gX"/>
                                                    <constraint firstItem="CKC-Lc-veG" firstAttribute="top" secondItem="xrL-oh-uVS" secondAttribute="bottom" constant="-10" id="xoD-F5-Rbu"/>
                                                </constraints>
                                                <variation key="default">
                                                    <mask key="constraints">
                                                        <exclude reference="54a-4x-mFK"/>
                                                    </mask>
                                                </variation>
                                            </view>
                                            <view contentMode="scaleToFill" ambiguous="YES" translatesAutoresizingMaskIntoConstraints="NO" id="MER-Mv-B1C">
                                                <rect key="frame" x="0.0" y="310" width="414" height="79"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Delivery Address" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="ZIl-M2-fBp" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                        <rect key="frame" x="20" y="10" width="309" height="14"/>
                                                        <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="14"/>
                                                        <color key="textColor" name="AppTheme_SelectedTabColor_#F4BA45"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" hasAttributedTitle="YES" translatesAutoresizingMaskIntoConstraints="NO" id="9QD-le-8o2">
                                                        <rect key="frame" x="344" y="-3" width="50" height="40"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="40" id="Cmk-vc-zbS"/>
                                                            <constraint firstAttribute="width" constant="50" id="pmr-C2-UeQ"/>
                                                        </constraints>
                                                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                        <state key="normal">
                                                            <attributedString key="attributedTitle">
                                                                <fragment content="Change">
                                                                    <attributes>
                                                                        <color key="NSColor" name="AppTheme_LightGrayColor_#B4B2B2"/>
                                                                        <font key="NSFont" metaFont="cellTitle"/>
                                                                        <paragraphStyle key="NSParagraphStyle" alignment="center" lineBreakMode="wordWrapping" baseWritingDirection="natural" tighteningFactorForTruncation="0.0"/>
                                                                        <integer key="NSUnderline" value="1"/>
                                                                    </attributes>
                                                                </fragment>
                                                            </attributedString>
                                                        </state>
                                                        <connections>
                                                            <action selector="actionChangeAddress:" destination="yg8-rb-Hfq" eventType="touchUpInside" id="d50-dm-8Tz"/>
                                                        </connections>
                                                    </button>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="NVt-1V-iBN">
                                                        <rect key="frame" x="20" y="39" width="309" height="36"/>
                                                        <subviews>
                                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="DkQ-9j-Hyf">
                                                                <rect key="frame" x="0.0" y="10" width="299" height="26"/>
                                                                <subviews>
                                                                    <stackView opaque="NO" contentMode="scaleToFill" spacing="2" translatesAutoresizingMaskIntoConstraints="NO" id="gb4-rn-GaO">
                                                                        <rect key="frame" x="0.0" y="0.0" width="299" height="26"/>
                                                                        <subviews>
                                                                            <imageView hidden="YES" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="mosque-icon" translatesAutoresizingMaskIntoConstraints="NO" id="gen-A3-W2G">
                                                                                <rect key="frame" x="0.0" y="0.0" width="26" height="26"/>
                                                                                <constraints>
                                                                                    <constraint firstAttribute="width" constant="26" id="BSm-jx-G4D"/>
                                                                                </constraints>
                                                                            </imageView>
                                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" lineBreakMode="wordWrap" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="A8l-J0-VKa" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                                                <rect key="frame" x="0.0" y="0.0" width="299" height="26"/>
                                                                                <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="13"/>
                                                                                <color key="textColor" name="AppTheme_DarkGrayAddressColor_#454545"/>
                                                                                <nil key="highlightedColor"/>
                                                                            </label>
                                                                        </subviews>
                                                                    </stackView>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" lineBreakMode="wordWrap" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="TqE-aH-LXs" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                                        <rect key="frame" x="0.0" y="26" width="299" height="0.0"/>
                                                                        <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="11"/>
                                                                        <color key="textColor" name="AppTheme_LightGrayColor_#CECECE"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                            </stackView>
                                                        </subviews>
                                                        <constraints>
                                                            <constraint firstItem="DkQ-9j-Hyf" firstAttribute="leading" secondItem="NVt-1V-iBN" secondAttribute="leading" id="BjD-Fv-uto"/>
                                                            <constraint firstItem="DkQ-9j-Hyf" firstAttribute="top" secondItem="NVt-1V-iBN" secondAttribute="top" constant="10" id="MSL-io-1IB"/>
                                                            <constraint firstAttribute="bottom" secondItem="DkQ-9j-Hyf" secondAttribute="bottom" id="Mug-Em-4rI"/>
                                                            <constraint firstAttribute="height" constant="60" id="Y8d-eD-rVG"/>
                                                            <constraint firstAttribute="trailing" secondItem="DkQ-9j-Hyf" secondAttribute="trailing" constant="10" id="xUq-r5-TL9"/>
                                                        </constraints>
                                                        <variation key="default">
                                                            <mask key="constraints">
                                                                <exclude reference="Y8d-eD-rVG"/>
                                                            </mask>
                                                        </variation>
                                                    </view>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="0cB-lQ-3B2">
                                                        <rect key="frame" x="20" y="84" width="374" height="1"/>
                                                        <color key="backgroundColor" name="AppTheme_LightGrayColor_#B4B2B2"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="1" id="A40-Yq-A7N"/>
                                                        </constraints>
                                                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                        <nil key="textColor"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                </subviews>
                                                <constraints>
                                                    <constraint firstItem="ZIl-M2-fBp" firstAttribute="leading" secondItem="MER-Mv-B1C" secondAttribute="leading" constant="20" id="161-hE-MdP"/>
                                                    <constraint firstAttribute="trailing" secondItem="9QD-le-8o2" secondAttribute="trailing" constant="20" id="2Lq-QP-ANs"/>
                                                    <constraint firstItem="0cB-lQ-3B2" firstAttribute="trailing" secondItem="9QD-le-8o2" secondAttribute="trailing" id="8PZ-Rp-tip"/>
                                                    <constraint firstItem="9QD-le-8o2" firstAttribute="centerY" secondItem="ZIl-M2-fBp" secondAttribute="centerY" id="A2W-3b-V0z"/>
                                                    <constraint firstAttribute="bottom" secondItem="NVt-1V-iBN" secondAttribute="bottom" constant="10" id="QdY-zh-7LW"/>
                                                    <constraint firstItem="NVt-1V-iBN" firstAttribute="top" secondItem="ZIl-M2-fBp" secondAttribute="bottom" constant="15" id="Ubp-q2-Px8"/>
                                                    <constraint firstItem="9QD-le-8o2" firstAttribute="leading" secondItem="ZIl-M2-fBp" secondAttribute="trailing" constant="15" id="UyZ-sK-Vsu"/>
                                                    <constraint firstItem="NVt-1V-iBN" firstAttribute="trailing" secondItem="ZIl-M2-fBp" secondAttribute="trailing" id="aD4-xt-owY"/>
                                                    <constraint firstItem="NVt-1V-iBN" firstAttribute="leading" secondItem="ZIl-M2-fBp" secondAttribute="leading" id="cQs-tg-etD"/>
                                                    <constraint firstItem="ZIl-M2-fBp" firstAttribute="top" secondItem="MER-Mv-B1C" secondAttribute="top" constant="10" id="iSc-PY-QuT"/>
                                                    <constraint firstAttribute="bottom" secondItem="0cB-lQ-3B2" secondAttribute="bottom" id="n6R-4T-nw9"/>
                                                    <constraint firstItem="0cB-lQ-3B2" firstAttribute="leading" secondItem="ZIl-M2-fBp" secondAttribute="leading" id="puT-x7-348"/>
                                                </constraints>
                                            </view>
                                            <view contentMode="scaleToFill" ambiguous="YES" translatesAutoresizingMaskIntoConstraints="NO" id="83m-rv-Lia">
                                                <rect key="frame" x="0.0" y="399" width="414" height="109"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Delivery Shift" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="p6r-Vg-D2P" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                        <rect key="frame" x="20" y="0.0" width="374" height="20"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="20" id="ixo-h8-VG8"/>
                                                        </constraints>
                                                        <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="14"/>
                                                        <color key="textColor" name="AppTheme_SelectedTabColor_#F4BA45"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <stackView opaque="NO" contentMode="scaleToFill" ambiguous="YES" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="Eyk-cd-hcg">
                                                        <rect key="frame" x="20" y="25" width="374" height="70"/>
                                                        <subviews>
                                                            <collectionView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" ambiguous="YES" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" dataMode="prototypes" translatesAutoresizingMaskIntoConstraints="NO" id="8Wd-fh-h0M">
                                                                <rect key="frame" x="0.0" y="0.0" width="374" height="69"/>
                                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="40" id="rAb-ST-3YA"/>
                                                                </constraints>
                                                                <collectionViewFlowLayout key="collectionViewLayout" scrollDirection="horizontal" minimumLineSpacing="0.0" minimumInteritemSpacing="0.0" id="bxw-G3-w5E">
                                                                    <size key="itemSize" width="128" height="128"/>
                                                                    <size key="headerReferenceSize" width="0.0" height="0.0"/>
                                                                    <size key="footerReferenceSize" width="0.0" height="0.0"/>
                                                                    <inset key="sectionInset" minX="0.0" minY="0.0" maxX="0.0" maxY="0.0"/>
                                                                </collectionViewFlowLayout>
                                                                <cells/>
                                                            </collectionView>
                                                            <collectionView hidden="YES" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" dataMode="prototypes" translatesAutoresizingMaskIntoConstraints="NO" id="YZT-AB-88Z">
                                                                <rect key="frame" x="0.0" y="69" width="374" height="100"/>
                                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="100" id="czA-pM-0pa"/>
                                                                </constraints>
                                                                <collectionViewFlowLayout key="collectionViewLayout" scrollDirection="horizontal" minimumLineSpacing="0.0" minimumInteritemSpacing="0.0" id="5ia-q6-873">
                                                                    <size key="itemSize" width="128" height="128"/>
                                                                    <size key="headerReferenceSize" width="0.0" height="0.0"/>
                                                                    <size key="footerReferenceSize" width="0.0" height="0.0"/>
                                                                    <inset key="sectionInset" minX="0.0" minY="0.0" maxX="0.0" maxY="0.0"/>
                                                                </collectionViewFlowLayout>
                                                                <cells/>
                                                            </collectionView>
                                                            <stackView hidden="YES" opaque="NO" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Z8F-gY-6E3">
                                                                <rect key="frame" x="0.0" y="69" width="374" height="29"/>
                                                                <subviews>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Delivery Shift" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="xfG-OR-ppg" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                                        <rect key="frame" x="0.0" y="0.0" width="314" height="29"/>
                                                                        <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="11"/>
                                                                        <color key="textColor" name="AppTheme_DiffBlackColor_#101113"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="kX4-2A-KxL">
                                                                        <rect key="frame" x="314" y="0.0" width="60" height="29"/>
                                                                        <subviews>
                                                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" hasAttributedTitle="YES" translatesAutoresizingMaskIntoConstraints="NO" id="sMr-9m-N0F">
                                                                                <rect key="frame" x="0.0" y="0.0" width="60" height="29"/>
                                                                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                                                <state key="normal">
                                                                                    <attributedString key="attributedTitle">
                                                                                        <fragment content="Change">
                                                                                            <attributes>
                                                                                                <color key="NSColor" name="AppTheme_LightGrayColor_#B4B2B2"/>
                                                                                                <font key="NSFont" metaFont="cellTitle"/>
                                                                                                <paragraphStyle key="NSParagraphStyle" alignment="center" lineBreakMode="wordWrapping" baseWritingDirection="natural" tighteningFactorForTruncation="0.0"/>
                                                                                                <integer key="NSUnderline" value="1"/>
                                                                                            </attributes>
                                                                                        </fragment>
                                                                                    </attributedString>
                                                                                </state>
                                                                                <connections>
                                                                                    <action selector="actionChangeDeliveryShift:" destination="yg8-rb-Hfq" eventType="touchUpInside" id="b3J-ss-Clq"/>
                                                                                </connections>
                                                                            </button>
                                                                        </subviews>
                                                                        <constraints>
                                                                            <constraint firstAttribute="bottom" secondItem="sMr-9m-N0F" secondAttribute="bottom" id="747-9Y-Ixd"/>
                                                                            <constraint firstAttribute="height" constant="29" id="C3r-u1-xlf"/>
                                                                            <constraint firstItem="sMr-9m-N0F" firstAttribute="top" secondItem="kX4-2A-KxL" secondAttribute="top" id="N3a-0O-8qb"/>
                                                                            <constraint firstAttribute="width" constant="60" id="WOu-ob-BZ9"/>
                                                                            <constraint firstItem="sMr-9m-N0F" firstAttribute="leading" secondItem="kX4-2A-KxL" secondAttribute="leading" id="YVf-fc-xls"/>
                                                                            <constraint firstAttribute="trailing" secondItem="sMr-9m-N0F" secondAttribute="trailing" id="yAr-oU-w20"/>
                                                                        </constraints>
                                                                    </view>
                                                                </subviews>
                                                            </stackView>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" ambiguous="YES" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="pTf-Uj-E0x">
                                                                <rect key="frame" x="0.0" y="69" width="374" height="1"/>
                                                                <color key="backgroundColor" name="AppTheme_LightGrayColor_#B4B2B2"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="1" id="pT0-TC-Y18"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                                <nil key="textColor"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                        </subviews>
                                                        <constraints>
                                                            <constraint firstAttribute="trailing" secondItem="YZT-AB-88Z" secondAttribute="trailing" id="Jtn-PL-dsj"/>
                                                            <constraint firstItem="YZT-AB-88Z" firstAttribute="leading" secondItem="Eyk-cd-hcg" secondAttribute="leading" id="NnG-1P-hzf"/>
                                                            <constraint firstAttribute="height" relation="greaterThanOrEqual" constant="70" id="mHV-Cv-FVD"/>
                                                        </constraints>
                                                    </stackView>
                                                    <stackView hidden="YES" opaque="NO" contentMode="scaleToFill" distribution="fillEqually" translatesAutoresizingMaskIntoConstraints="NO" id="RcQ-Q7-dXz">
                                                        <rect key="frame" x="20" y="35" width="374" height="40"/>
                                                        <subviews>
                                                            <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="agk-xv-2eY">
                                                                <rect key="frame" x="0.0" y="0.0" width="32" height="50"/>
                                                                <subviews>
                                                                    <button hidden="YES" opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="leading" contentVerticalAlignment="center" lineBreakMode="wordWrap" translatesAutoresizingMaskIntoConstraints="NO" id="iRW-aZ-mCr">
                                                                        <rect key="frame" x="0.0" y="0.0" width="32" height="50"/>
                                                                        <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="12"/>
                                                                        <inset key="titleEdgeInsets" minX="10" minY="0.0" maxX="0.0" maxY="0.0"/>
                                                                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                                        <state key="normal" title="Morning" image="icn_radioUnselected">
                                                                            <color key="titleColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                        </state>
                                                                        <state key="selected" image="icn_radioSelected"/>
                                                                    </button>
                                                                    <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="From 9 AM to 5 PM." lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="93F-qd-JcS" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                                        <rect key="frame" x="32" y="40" width="0.0" height="10"/>
                                                                        <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="10"/>
                                                                        <color key="textColor" name="AppTheme_DarkGrayColor_#A09F9F"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                                <constraints>
                                                                    <constraint firstItem="93F-qd-JcS" firstAttribute="leading" secondItem="iRW-aZ-mCr" secondAttribute="leading" constant="32" id="0fj-f5-QZY"/>
                                                                    <constraint firstItem="93F-qd-JcS" firstAttribute="trailing" secondItem="iRW-aZ-mCr" secondAttribute="trailing" id="DLv-zv-kqh"/>
                                                                    <constraint firstAttribute="trailing" secondItem="iRW-aZ-mCr" secondAttribute="trailing" id="JL3-Kc-LcL"/>
                                                                    <constraint firstAttribute="bottom" secondItem="iRW-aZ-mCr" secondAttribute="bottom" id="PRL-yM-xf8"/>
                                                                    <constraint firstItem="iRW-aZ-mCr" firstAttribute="top" secondItem="agk-xv-2eY" secondAttribute="top" id="WBR-PJ-buq"/>
                                                                    <constraint firstItem="93F-qd-JcS" firstAttribute="bottom" secondItem="iRW-aZ-mCr" secondAttribute="bottom" id="jWf-0j-CI3"/>
                                                                    <constraint firstItem="iRW-aZ-mCr" firstAttribute="leading" secondItem="agk-xv-2eY" secondAttribute="leading" id="lmI-mH-Y4R"/>
                                                                </constraints>
                                                            </view>
                                                            <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="mGu-mp-wLZ">
                                                                <rect key="frame" x="0.0" y="0.0" width="32" height="50"/>
                                                                <subviews>
                                                                    <button hidden="YES" opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="leading" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="egK-sc-hUQ">
                                                                        <rect key="frame" x="0.0" y="0.0" width="32" height="50"/>
                                                                        <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="12"/>
                                                                        <inset key="titleEdgeInsets" minX="10" minY="0.0" maxX="0.0" maxY="0.0"/>
                                                                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                                        <state key="normal" title="Evening" image="icn_radioUnselected">
                                                                            <color key="titleColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                        </state>
                                                                        <state key="selected" image="icn_radioSelected"/>
                                                                    </button>
                                                                    <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="From 5 PM to 12 AM." lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="cBO-c9-Sk6" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                                        <rect key="frame" x="32" y="40" width="0.0" height="10"/>
                                                                        <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="10"/>
                                                                        <color key="textColor" name="AppTheme_DarkGrayColor_#A09F9F"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                                <constraints>
                                                                    <constraint firstItem="cBO-c9-Sk6" firstAttribute="trailing" secondItem="egK-sc-hUQ" secondAttribute="trailing" id="280-UF-PxI"/>
                                                                    <constraint firstItem="egK-sc-hUQ" firstAttribute="leading" secondItem="mGu-mp-wLZ" secondAttribute="leading" id="EG0-v3-NaL"/>
                                                                    <constraint firstAttribute="trailing" secondItem="egK-sc-hUQ" secondAttribute="trailing" id="IZ2-CM-cdF"/>
                                                                    <constraint firstItem="egK-sc-hUQ" firstAttribute="top" secondItem="mGu-mp-wLZ" secondAttribute="top" id="Ij8-ci-UYX"/>
                                                                    <constraint firstItem="cBO-c9-Sk6" firstAttribute="leading" secondItem="egK-sc-hUQ" secondAttribute="leading" constant="32" id="LCq-O7-sSa"/>
                                                                    <constraint firstAttribute="bottom" secondItem="egK-sc-hUQ" secondAttribute="bottom" id="bSM-HY-e5a"/>
                                                                    <constraint firstItem="cBO-c9-Sk6" firstAttribute="bottom" secondItem="egK-sc-hUQ" secondAttribute="bottom" id="nBh-wz-CJo"/>
                                                                </constraints>
                                                            </view>
                                                            <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="BLM-Up-Nq3">
                                                                <rect key="frame" x="0.0" y="0.0" width="32" height="50"/>
                                                                <subviews>
                                                                    <button hidden="YES" opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="leading" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="xJs-iZ-TST">
                                                                        <rect key="frame" x="0.0" y="0.0" width="32" height="50"/>
                                                                        <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="12"/>
                                                                        <inset key="titleEdgeInsets" minX="10" minY="0.0" maxX="0.0" maxY="0.0"/>
                                                                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                                        <state key="normal" title="Ramdan" image="icn_radioUnselected">
                                                                            <color key="titleColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                        </state>
                                                                        <state key="selected" image="icn_radioSelected"/>
                                                                    </button>
                                                                    <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="From 8 PM to 3 AM." lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="fLL-gX-ky0" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                                        <rect key="frame" x="32" y="40" width="0.0" height="10"/>
                                                                        <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="10"/>
                                                                        <color key="textColor" name="AppTheme_DarkGrayColor_#A09F9F"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                                <constraints>
                                                                    <constraint firstItem="xJs-iZ-TST" firstAttribute="leading" secondItem="BLM-Up-Nq3" secondAttribute="leading" id="El3-LP-yEa"/>
                                                                    <constraint firstItem="fLL-gX-ky0" firstAttribute="trailing" secondItem="xJs-iZ-TST" secondAttribute="trailing" id="H4P-jS-dvo"/>
                                                                    <constraint firstItem="fLL-gX-ky0" firstAttribute="bottom" secondItem="xJs-iZ-TST" secondAttribute="bottom" id="KNp-YP-W7i"/>
                                                                    <constraint firstAttribute="bottom" secondItem="xJs-iZ-TST" secondAttribute="bottom" id="Rhw-p6-b61"/>
                                                                    <constraint firstAttribute="trailing" secondItem="xJs-iZ-TST" secondAttribute="trailing" id="glI-ej-klY"/>
                                                                    <constraint firstItem="xJs-iZ-TST" firstAttribute="top" secondItem="BLM-Up-Nq3" secondAttribute="top" id="n8K-yQ-Mwn"/>
                                                                    <constraint firstItem="fLL-gX-ky0" firstAttribute="leading" secondItem="xJs-iZ-TST" secondAttribute="leading" constant="32" id="pTE-jS-zcc"/>
                                                                </constraints>
                                                            </view>
                                                        </subviews>
                                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="40" id="ok6-Ri-Sbn"/>
                                                        </constraints>
                                                    </stackView>
                                                </subviews>
                                                <constraints>
                                                    <constraint firstItem="p6r-Vg-D2P" firstAttribute="top" secondItem="83m-rv-Lia" secondAttribute="top" id="9lM-DE-7XY"/>
                                                    <constraint firstAttribute="height" relation="greaterThanOrEqual" constant="120" id="OHo-Wh-WeD"/>
                                                    <constraint firstItem="RcQ-Q7-dXz" firstAttribute="top" secondItem="p6r-Vg-D2P" secondAttribute="bottom" constant="15" id="Wh5-Ub-ddF"/>
                                                    <constraint firstAttribute="bottom" secondItem="RcQ-Q7-dXz" secondAttribute="bottom" constant="10" id="YtM-we-m9A"/>
                                                    <constraint firstItem="RcQ-Q7-dXz" firstAttribute="leading" secondItem="83m-rv-Lia" secondAttribute="leading" constant="20" id="grZ-zb-l4f"/>
                                                    <constraint firstItem="Eyk-cd-hcg" firstAttribute="leading" secondItem="83m-rv-Lia" secondAttribute="leading" constant="20" id="hgP-bL-pNa"/>
                                                    <constraint firstItem="Eyk-cd-hcg" firstAttribute="top" secondItem="p6r-Vg-D2P" secondAttribute="bottom" constant="5" id="jBQ-5G-3Di"/>
                                                    <constraint firstItem="p6r-Vg-D2P" firstAttribute="leading" secondItem="83m-rv-Lia" secondAttribute="leading" constant="20" id="k34-df-I12"/>
                                                    <constraint firstAttribute="trailing" secondItem="Eyk-cd-hcg" secondAttribute="trailing" constant="20" id="oFr-ef-1Zj"/>
                                                    <constraint firstAttribute="trailing" secondItem="RcQ-Q7-dXz" secondAttribute="trailing" constant="20" id="unl-6j-9xJ"/>
                                                    <constraint firstAttribute="trailing" secondItem="p6r-Vg-D2P" secondAttribute="trailing" constant="20" id="wJ7-gL-Cds"/>
                                                </constraints>
                                            </view>
                                            <view contentMode="scaleToFill" ambiguous="YES" translatesAutoresizingMaskIntoConstraints="NO" id="1Jn-2r-AKt">
                                                <rect key="frame" x="0.0" y="510" width="414" height="89"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Schedule Order" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="HF3-Lr-HCH" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                        <rect key="frame" x="20" y="0.0" width="374" height="14"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="14" id="kU9-9C-Ssx"/>
                                                        </constraints>
                                                        <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="14"/>
                                                        <color key="textColor" name="AppTheme_SelectedTabColor_#F4BA45"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" translatesAutoresizingMaskIntoConstraints="NO" id="SCR-HF-TAW">
                                                        <rect key="frame" x="0.0" y="29" width="414" height="40"/>
                                                        <subviews>
                                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="N1D-i5-3XR">
                                                                <rect key="frame" x="0.0" y="0.0" width="103.5" height="40"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="40" id="Th5-Xt-UZa"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="8"/>
                                                                <inset key="titleEdgeInsets" minX="10" minY="0.0" maxX="0.0" maxY="0.0"/>
                                                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                                <state key="normal" title="Only Once" image="icn_radioUnselected">
                                                                    <color key="titleColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                </state>
                                                                <state key="selected" image="icn_radioSelected"/>
                                                                <connections>
                                                                    <action selector="onlyOnceAction:" destination="yg8-rb-Hfq" eventType="touchUpInside" id="fTd-7P-Phc"/>
                                                                </connections>
                                                            </button>
                                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="DQJ-J1-i9R">
                                                                <rect key="frame" x="103.5" y="0.0" width="103.5" height="40"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="40" id="Be8-Mq-ikj"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="8"/>
                                                                <inset key="titleEdgeInsets" minX="10" minY="0.0" maxX="0.0" maxY="0.0"/>
                                                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                                <state key="normal" title="Every week" image="icn_radioUnselected">
                                                                    <color key="titleColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                </state>
                                                                <state key="selected" image="icn_radioSelected"/>
                                                                <connections>
                                                                    <action selector="everyWeekAction:" destination="yg8-rb-Hfq" eventType="touchUpInside" id="yLD-WM-l75"/>
                                                                </connections>
                                                            </button>
                                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="niu-rn-J0d">
                                                                <rect key="frame" x="207" y="0.0" width="103.5" height="40"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="40" id="QZg-av-pPf"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="8"/>
                                                                <inset key="titleEdgeInsets" minX="10" minY="0.0" maxX="0.0" maxY="0.0"/>
                                                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                                <state key="normal" title="Every 2 weeks" image="icn_radioUnselected">
                                                                    <color key="titleColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                </state>
                                                                <state key="selected" image="icn_radioSelected"/>
                                                                <connections>
                                                                    <action selector="everyTwoWeeksAction:" destination="yg8-rb-Hfq" eventType="touchUpInside" id="yIg-fY-UdU"/>
                                                                </connections>
                                                            </button>
                                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="qXj-J7-lVA">
                                                                <rect key="frame" x="310.5" y="0.0" width="103.5" height="40"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="40" id="EKy-i5-psn"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="8"/>
                                                                <inset key="titleEdgeInsets" minX="10" minY="0.0" maxX="0.0" maxY="0.0"/>
                                                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                                <state key="normal" title="Every Month" image="icn_radioUnselected">
                                                                    <color key="titleColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                </state>
                                                                <state key="selected" image="icn_radioSelected"/>
                                                                <connections>
                                                                    <action selector="everyMonthAction:" destination="yg8-rb-Hfq" eventType="touchUpInside" id="T8j-c7-gKt"/>
                                                                </connections>
                                                            </button>
                                                        </subviews>
                                                    </stackView>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="zdd-pZ-1DW">
                                                        <rect key="frame" x="20" y="78" width="374" height="1"/>
                                                        <color key="backgroundColor" name="AppTheme_LightGrayColor_#B4B2B2"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="1" id="6Gd-36-sB5"/>
                                                        </constraints>
                                                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                        <nil key="textColor"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                </subviews>
                                                <constraints>
                                                    <constraint firstItem="zdd-pZ-1DW" firstAttribute="top" secondItem="SCR-HF-TAW" secondAttribute="bottom" constant="9" id="1tL-Jk-RDd"/>
                                                    <constraint firstItem="SCR-HF-TAW" firstAttribute="top" secondItem="HF3-Lr-HCH" secondAttribute="bottom" constant="15" id="4p2-qc-DSn"/>
                                                    <constraint firstItem="HF3-Lr-HCH" firstAttribute="leading" secondItem="1Jn-2r-AKt" secondAttribute="leading" constant="20" id="6Cs-MV-eMT"/>
                                                    <constraint firstAttribute="trailing" secondItem="SCR-HF-TAW" secondAttribute="trailing" id="HbC-ry-tBy"/>
                                                    <constraint firstAttribute="trailing" secondItem="HF3-Lr-HCH" secondAttribute="trailing" constant="20" id="P6f-Cp-eTD"/>
                                                    <constraint firstItem="SCR-HF-TAW" firstAttribute="leading" secondItem="1Jn-2r-AKt" secondAttribute="leading" id="RV2-ji-5Bj"/>
                                                    <constraint firstItem="HF3-Lr-HCH" firstAttribute="top" secondItem="1Jn-2r-AKt" secondAttribute="top" id="UYg-sa-aIh"/>
                                                    <constraint firstItem="zdd-pZ-1DW" firstAttribute="leading" secondItem="HF3-Lr-HCH" secondAttribute="leading" id="YgA-tj-syN"/>
                                                    <constraint firstAttribute="bottom" secondItem="zdd-pZ-1DW" secondAttribute="bottom" id="gyb-Jo-z3v"/>
                                                    <constraint firstItem="zdd-pZ-1DW" firstAttribute="trailing" secondItem="HF3-Lr-HCH" secondAttribute="trailing" id="ko7-Cz-Kz6"/>
                                                </constraints>
                                            </view>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" ambiguous="YES" text="Notes" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="4hB-6p-je0" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                <rect key="frame" x="20" y="607" width="374" height="14"/>
                                                <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="14"/>
                                                <color key="textColor" name="AppTheme_SelectedTabColor_#F4BA45"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <view contentMode="scaleToFill" ambiguous="YES" translatesAutoresizingMaskIntoConstraints="NO" id="1Gu-T2-9tv">
                                                <rect key="frame" x="20" y="631" width="374" height="60"/>
                                                <subviews>
                                                    <textView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Jfh-hI-vCL" customClass="IQTextView" customModule="IQKeyboardManagerSwift">
                                                        <rect key="frame" x="5" y="5" width="364" height="55"/>
                                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <color key="textColor" systemColor="labelColor"/>
                                                        <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="14"/>
                                                        <textInputTraits key="textInputTraits" autocapitalizationType="sentences" autocorrectionType="no"/>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="string" keyPath="placeholder" value="Write your notes here…"/>
                                                        </userDefinedRuntimeAttributes>
                                                    </textView>
                                                </subviews>
                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstItem="Jfh-hI-vCL" firstAttribute="top" secondItem="1Gu-T2-9tv" secondAttribute="top" constant="5" id="8eN-MA-IzW"/>
                                                    <constraint firstItem="Jfh-hI-vCL" firstAttribute="leading" secondItem="1Gu-T2-9tv" secondAttribute="leading" constant="5" id="Wbg-x0-VA0"/>
                                                    <constraint firstAttribute="trailing" secondItem="Jfh-hI-vCL" secondAttribute="trailing" constant="5" id="lyH-bB-Qdq"/>
                                                    <constraint firstAttribute="height" constant="60" id="srp-ff-AMa"/>
                                                    <constraint firstAttribute="bottom" secondItem="Jfh-hI-vCL" secondAttribute="bottom" id="x4M-Zz-KqI"/>
                                                </constraints>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                        <real key="value" value="9"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                                                        <real key="value" value="1"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                                                        <color key="value" name="AppTheme_LightGrayColor_#CECECE"/>
                                                    </userDefinedRuntimeAttribute>
                                                </userDefinedRuntimeAttributes>
                                            </view>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" ambiguous="YES" text="Promo Code" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="W73-fi-0Ws" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                <rect key="frame" x="20" y="711" width="374" height="14"/>
                                                <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="14"/>
                                                <color key="textColor" name="AppTheme_SelectedTabColor_#F4BA45"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <view contentMode="scaleToFill" ambiguous="YES" translatesAutoresizingMaskIntoConstraints="NO" id="AoB-LS-CoW">
                                                <rect key="frame" x="20" y="735" width="374" height="50"/>
                                                <subviews>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="eFb-aQ-lLT">
                                                        <rect key="frame" x="0.0" y="0.0" width="274" height="50"/>
                                                        <subviews>
                                                            <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="Enter promo code here" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="Eyr-rc-CuQ" customClass="MaterialLocalizeTextfield" customModule="TopCustomer" customModuleProvider="target">
                                                                <rect key="frame" x="10" y="0.0" width="254" height="50"/>
                                                                <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="14"/>
                                                                <textInputTraits key="textInputTraits"/>
                                                            </textField>
                                                        </subviews>
                                                        <constraints>
                                                            <constraint firstItem="Eyr-rc-CuQ" firstAttribute="leading" secondItem="eFb-aQ-lLT" secondAttribute="leading" constant="10" id="34V-kO-Prj"/>
                                                            <constraint firstAttribute="trailing" secondItem="Eyr-rc-CuQ" secondAttribute="trailing" constant="10" id="ZBK-pm-CdK"/>
                                                            <constraint firstItem="Eyr-rc-CuQ" firstAttribute="top" secondItem="eFb-aQ-lLT" secondAttribute="top" id="hR0-Op-eVB"/>
                                                            <constraint firstAttribute="height" constant="50" id="sj3-1A-maR"/>
                                                            <constraint firstAttribute="bottom" secondItem="Eyr-rc-CuQ" secondAttribute="bottom" id="yxl-rA-of3"/>
                                                        </constraints>
                                                    </view>
                                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="9EG-9I-elh">
                                                        <rect key="frame" x="294" y="0.0" width="80" height="50"/>
                                                        <constraints>
                                                            <constraint firstAttribute="width" constant="80" id="TPf-NZ-3QE"/>
                                                            <constraint firstAttribute="height" constant="50" id="sjX-v7-7Qd"/>
                                                        </constraints>
                                                        <fontDescription key="fontDescription" name="LoewNextArabic-Heavy" family="Loew Next Arabic" pointSize="14"/>
                                                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                        <state key="normal" title="APPLY">
                                                            <color key="titleColor" name="AppTheme_BlueColor_#012CDA"/>
                                                        </state>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                                <real key="value" value="18"/>
                                                            </userDefinedRuntimeAttribute>
                                                        </userDefinedRuntimeAttributes>
                                                        <connections>
                                                            <action selector="applyPromoCodeAction:" destination="yg8-rb-Hfq" eventType="touchUpInside" id="4Yq-9e-6Uu"/>
                                                        </connections>
                                                    </button>
                                                </subviews>
                                                <constraints>
                                                    <constraint firstItem="9EG-9I-elh" firstAttribute="leading" secondItem="eFb-aQ-lLT" secondAttribute="trailing" constant="20" id="2TP-ce-8L2"/>
                                                    <constraint firstItem="eFb-aQ-lLT" firstAttribute="top" secondItem="AoB-LS-CoW" secondAttribute="top" id="3FW-dB-YVD"/>
                                                    <constraint firstItem="9EG-9I-elh" firstAttribute="centerY" secondItem="AoB-LS-CoW" secondAttribute="centerY" id="529-AX-JtY"/>
                                                    <constraint firstAttribute="height" constant="50" id="G6l-C5-tjY"/>
                                                    <constraint firstAttribute="trailing" secondItem="9EG-9I-elh" secondAttribute="trailing" id="Pwj-Hm-EtB"/>
                                                    <constraint firstItem="eFb-aQ-lLT" firstAttribute="leading" secondItem="AoB-LS-CoW" secondAttribute="leading" id="i8y-f0-TvN"/>
                                                    <constraint firstAttribute="bottom" secondItem="eFb-aQ-lLT" secondAttribute="bottom" id="r2x-m3-ex1"/>
                                                </constraints>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                        <real key="value" value="9"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                                                        <real key="value" value="1"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                                                        <color key="value" name="AppTheme_LightGrayColor_#CECECE"/>
                                                    </userDefinedRuntimeAttribute>
                                                </userDefinedRuntimeAttributes>
                                                <variation key="default">
                                                    <mask key="constraints">
                                                        <exclude reference="G6l-C5-tjY"/>
                                                    </mask>
                                                </variation>
                                            </view>
                                            <view contentMode="scaleToFill" ambiguous="YES" translatesAutoresizingMaskIntoConstraints="NO" id="igF-vU-ag9">
                                                <rect key="frame" x="20" y="805" width="374" height="359"/>
                                                <subviews>
                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="15" translatesAutoresizingMaskIntoConstraints="NO" id="aqZ-sC-2L8">
                                                        <rect key="frame" x="0.0" y="0.0" width="374" height="359"/>
                                                        <subviews>
                                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="3" translatesAutoresizingMaskIntoConstraints="NO" id="zh4-Jq-tTS">
                                                                <rect key="frame" x="0.0" y="0.0" width="374" height="40"/>
                                                                <subviews>
                                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="rLb-uw-JoH">
                                                                        <rect key="frame" x="0.0" y="0.0" width="374" height="40"/>
                                                                        <subviews>
                                                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="background-pay-with-points" translatesAutoresizingMaskIntoConstraints="NO" id="x0j-Ej-4aF">
                                                                                <rect key="frame" x="0.0" y="0.0" width="374" height="40"/>
                                                                            </imageView>
                                                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="ozy-RU-Oxn">
                                                                                <rect key="frame" x="72" y="8" width="230" height="24"/>
                                                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="12"/>
                                                                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                                                <state key="normal" title="Get 0 SAR discount using your points">
                                                                                    <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                </state>
                                                                                <userDefinedRuntimeAttributes>
                                                                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                                                        <real key="value" value="9"/>
                                                                                    </userDefinedRuntimeAttribute>
                                                                                </userDefinedRuntimeAttributes>
                                                                                <connections>
                                                                                    <action selector="actionDiscountUsingPoints:" destination="yg8-rb-Hfq" eventType="touchUpInside" id="PCZ-kg-xuA"/>
                                                                                </connections>
                                                                            </button>
                                                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="oey-vg-6Gk">
                                                                                <rect key="frame" x="0.0" y="0.0" width="374" height="40"/>
                                                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                <state key="normal" title="Button"/>
                                                                                <buttonConfiguration key="configuration" style="plain" title=" "/>
                                                                                <connections>
                                                                                    <action selector="actionDiscountUsingPoints:" destination="yg8-rb-Hfq" eventType="touchUpInside" id="TMq-m2-sRe"/>
                                                                                </connections>
                                                                            </button>
                                                                        </subviews>
                                                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                        <constraints>
                                                                            <constraint firstItem="oey-vg-6Gk" firstAttribute="leading" secondItem="rLb-uw-JoH" secondAttribute="leading" id="8S8-Td-yTp"/>
                                                                            <constraint firstItem="oey-vg-6Gk" firstAttribute="top" secondItem="rLb-uw-JoH" secondAttribute="top" id="EeC-dX-T7B"/>
                                                                            <constraint firstAttribute="trailing" secondItem="x0j-Ej-4aF" secondAttribute="trailing" id="Fcy-zC-bwB"/>
                                                                            <constraint firstAttribute="height" constant="40" id="GlN-z7-fZN"/>
                                                                            <constraint firstItem="x0j-Ej-4aF" firstAttribute="top" secondItem="rLb-uw-JoH" secondAttribute="top" id="LCf-HM-vBX"/>
                                                                            <constraint firstAttribute="bottom" secondItem="x0j-Ej-4aF" secondAttribute="bottom" id="N1W-7v-NOQ"/>
                                                                            <constraint firstAttribute="bottom" secondItem="oey-vg-6Gk" secondAttribute="bottom" id="PNn-xX-Ovb"/>
                                                                            <constraint firstAttribute="trailing" secondItem="oey-vg-6Gk" secondAttribute="trailing" id="TYB-bE-uIe"/>
                                                                            <constraint firstItem="ozy-RU-Oxn" firstAttribute="centerX" secondItem="rLb-uw-JoH" secondAttribute="centerX" id="TZx-8Q-TT0"/>
                                                                            <constraint firstItem="x0j-Ej-4aF" firstAttribute="leading" secondItem="rLb-uw-JoH" secondAttribute="leading" id="ct4-ll-thX"/>
                                                                            <constraint firstItem="ozy-RU-Oxn" firstAttribute="centerY" secondItem="rLb-uw-JoH" secondAttribute="centerY" id="ziI-k1-wa8"/>
                                                                        </constraints>
                                                                        <userDefinedRuntimeAttributes>
                                                                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                                                <real key="value" value="7"/>
                                                                            </userDefinedRuntimeAttribute>
                                                                        </userDefinedRuntimeAttributes>
                                                                    </view>
                                                                </subviews>
                                                            </stackView>
                                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="15" translatesAutoresizingMaskIntoConstraints="NO" id="QZB-jS-HO6">
                                                                <rect key="frame" x="0.0" y="55" width="374" height="115"/>
                                                                <subviews>
                                                                    <stackView opaque="NO" contentMode="scaleToFill" spacing="3" translatesAutoresizingMaskIntoConstraints="NO" id="fcI-dZ-MyP">
                                                                        <rect key="frame" x="0.0" y="0.0" width="374" height="11"/>
                                                                        <subviews>
                                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="252" verticalHuggingPriority="251" text="Subtotal" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="lSe-B7-cWx" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                                                <rect key="frame" x="0.0" y="0.0" width="48" height="11"/>
                                                                                <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="11"/>
                                                                                <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                <nil key="highlightedColor"/>
                                                                            </label>
                                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="100 SAR" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="5h4-Lq-M28" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                                                <rect key="frame" x="51" y="0.0" width="323" height="11"/>
                                                                                <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="11"/>
                                                                                <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                <nil key="highlightedColor"/>
                                                                            </label>
                                                                        </subviews>
                                                                    </stackView>
                                                                    <stackView hidden="YES" opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="3" translatesAutoresizingMaskIntoConstraints="NO" id="czc-3Q-9hJ">
                                                                        <rect key="frame" x="0.0" y="18.5" width="374" height="0.0"/>
                                                                        <subviews>
                                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="252" verticalHuggingPriority="251" text="Discount" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="aKQ-rB-I92" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                                                <rect key="frame" x="0.0" y="0.0" width="185.5" height="0.0"/>
                                                                                <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="11"/>
                                                                                <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                <nil key="highlightedColor"/>
                                                                            </label>
                                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="0 SAR" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="sOC-Od-5rC" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                                                <rect key="frame" x="188.5" y="0.0" width="185.5" height="0.0"/>
                                                                                <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="11"/>
                                                                                <color key="textColor" name="AppTheme_DiscountGreenColor_#05B13E"/>
                                                                                <nil key="highlightedColor"/>
                                                                            </label>
                                                                        </subviews>
                                                                    </stackView>
                                                                    <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="3" translatesAutoresizingMaskIntoConstraints="NO" id="aTS-Uy-4uO">
                                                                        <rect key="frame" x="0.0" y="26" width="374" height="11"/>
                                                                        <subviews>
                                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="252" verticalHuggingPriority="251" text="Delivery" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="MKV-dG-muH" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                                                <rect key="frame" x="0.0" y="0.0" width="185.5" height="11"/>
                                                                                <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="11"/>
                                                                                <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                <nil key="highlightedColor"/>
                                                                            </label>
                                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="15 SAR" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Hpl-7u-11F" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                                                <rect key="frame" x="188.5" y="0.0" width="185.5" height="11"/>
                                                                                <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="11"/>
                                                                                <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                <nil key="highlightedColor"/>
                                                                            </label>
                                                                        </subviews>
                                                                    </stackView>
                                                                    <stackView hidden="YES" opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="3" translatesAutoresizingMaskIntoConstraints="NO" id="xgC-aE-SdE">
                                                                        <rect key="frame" x="0.0" y="44.5" width="374" height="0.0"/>
                                                                        <subviews>
                                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="252" verticalHuggingPriority="251" text="Delivery Discount" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="5vL-1J-olg" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                                                <rect key="frame" x="0.0" y="0.0" width="185.5" height="0.0"/>
                                                                                <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="11"/>
                                                                                <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                <nil key="highlightedColor"/>
                                                                            </label>
                                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="15 SAR" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="9Ak-aI-JAh" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                                                <rect key="frame" x="188.5" y="0.0" width="185.5" height="0.0"/>
                                                                                <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="11"/>
                                                                                <color key="textColor" name="AppTheme_DiscountGreenColor_#05B13E"/>
                                                                                <nil key="highlightedColor"/>
                                                                            </label>
                                                                        </subviews>
                                                                    </stackView>
                                                                    <stackView hidden="YES" opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="3" translatesAutoresizingMaskIntoConstraints="NO" id="Fy3-Qq-3q6">
                                                                        <rect key="frame" x="0.0" y="44.5" width="374" height="0.0"/>
                                                                        <subviews>
                                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="(VAT includes 15%)" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="u9Y-5O-SD6" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                                                <rect key="frame" x="0.0" y="0.0" width="374" height="0.0"/>
                                                                                <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="11"/>
                                                                                <color key="textColor" name="AppTheme_LightGrayColor_#B4B2B2"/>
                                                                                <nil key="highlightedColor"/>
                                                                            </label>
                                                                        </subviews>
                                                                    </stackView>
                                                                    <stackView hidden="YES" opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="3" translatesAutoresizingMaskIntoConstraints="NO" id="QNO-o4-qul">
                                                                        <rect key="frame" x="0.0" y="44.5" width="374" height="0.0"/>
                                                                        <subviews>
                                                                            <stackView opaque="NO" contentMode="scaleToFill" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="3ZX-UA-oH3">
                                                                                <rect key="frame" x="0.0" y="0.0" width="185.5" height="0.0"/>
                                                                                <subviews>
                                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="252" verticalHuggingPriority="251" ambiguous="YES" text="Discount" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="rsF-be-XqA" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                                                        <rect key="frame" x="0.0" y="0.0" width="51" height="0.0"/>
                                                                                        <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="11"/>
                                                                                        <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                        <nil key="highlightedColor"/>
                                                                                    </label>
                                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="252" verticalHuggingPriority="251" ambiguous="YES" text="(Coins discount)" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Vhw-Ju-TiR" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                                                        <rect key="frame" x="59" y="0.0" width="126.5" height="0.0"/>
                                                                                        <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="11"/>
                                                                                        <color key="textColor" name="AppTheme_DarkGrayColor_#A09F9F"/>
                                                                                        <nil key="highlightedColor"/>
                                                                                    </label>
                                                                                </subviews>
                                                                            </stackView>
                                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="0 SAR" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="vnz-N8-Qe9" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                                                <rect key="frame" x="188.5" y="0.0" width="185.5" height="0.0"/>
                                                                                <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="11"/>
                                                                                <color key="textColor" name="AppTheme_DiscountGreenColor_#05B13E"/>
                                                                                <nil key="highlightedColor"/>
                                                                            </label>
                                                                        </subviews>
                                                                    </stackView>
                                                                    <stackView opaque="NO" contentMode="scaleToFill" spacing="3" translatesAutoresizingMaskIntoConstraints="NO" id="CNG-RF-n4e">
                                                                        <rect key="frame" x="0.0" y="52" width="374" height="11"/>
                                                                        <subviews>
                                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="252" verticalHuggingPriority="251" text="VAT" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="dQF-7P-AE3" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                                                <rect key="frame" x="0.0" y="0.0" width="23.5" height="11"/>
                                                                                <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="11"/>
                                                                                <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                <nil key="highlightedColor"/>
                                                                            </label>
                                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="252" verticalHuggingPriority="251" text="(VAT includes 15%)" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="dBf-Dk-rdj" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                                                <rect key="frame" x="26.5" y="0.0" width="83.5" height="11"/>
                                                                                <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="9"/>
                                                                                <color key="textColor" name="AppTheme_LightGrayColor_#B4B2B2"/>
                                                                                <nil key="highlightedColor"/>
                                                                            </label>
                                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="15 SAR" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="jsV-nG-QJg" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                                                <rect key="frame" x="113" y="0.0" width="261" height="11"/>
                                                                                <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="11"/>
                                                                                <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                <nil key="highlightedColor"/>
                                                                            </label>
                                                                        </subviews>
                                                                    </stackView>
                                                                    <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="3" translatesAutoresizingMaskIntoConstraints="NO" id="HIx-Ud-XFU">
                                                                        <rect key="frame" x="0.0" y="78" width="374" height="11"/>
                                                                        <subviews>
                                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="252" verticalHuggingPriority="251" text="Total" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="dfz-RF-iBy" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                                                <rect key="frame" x="0.0" y="0.0" width="185.5" height="11"/>
                                                                                <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="11"/>
                                                                                <color key="textColor" name="AppTheme_BlueColor_#012CDA"/>
                                                                                <nil key="highlightedColor"/>
                                                                            </label>
                                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="115 SAR" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="rLa-up-MTb" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                                                <rect key="frame" x="188.5" y="0.0" width="185.5" height="11"/>
                                                                                <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="11"/>
                                                                                <color key="textColor" name="AppTheme_BlueColor_#012CDA"/>
                                                                                <nil key="highlightedColor"/>
                                                                            </label>
                                                                        </subviews>
                                                                    </stackView>
                                                                    <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="2eW-wT-Tyb">
                                                                        <rect key="frame" x="0.0" y="96.5" width="374" height="1"/>
                                                                        <color key="backgroundColor" name="AppTheme_LightGrayColor_#B4B2B2"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="height" constant="1" id="gNg-m1-yQ8"/>
                                                                        </constraints>
                                                                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                                        <nil key="textColor"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                    <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="3" translatesAutoresizingMaskIntoConstraints="NO" id="CwF-aK-PtF">
                                                                        <rect key="frame" x="0.0" y="104" width="374" height="11"/>
                                                                        <subviews>
                                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="252" verticalHuggingPriority="251" text="VAT reg. no: 998800000" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="v47-58-xoN" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                                                <rect key="frame" x="0.0" y="0.0" width="374" height="11"/>
                                                                                <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="11"/>
                                                                                <color key="textColor" name="AppTheme_DarkGrayColor_#A09F9F"/>
                                                                                <nil key="highlightedColor"/>
                                                                            </label>
                                                                        </subviews>
                                                                    </stackView>
                                                                </subviews>
                                                            </stackView>
                                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="3" translatesAutoresizingMaskIntoConstraints="NO" id="049-Cc-94F">
                                                                <rect key="frame" x="0.0" y="185" width="374" height="54"/>
                                                                <subviews>
                                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="AjK-Sj-6bF">
                                                                        <rect key="frame" x="0.0" y="0.0" width="374" height="40"/>
                                                                        <subviews>
                                                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="background-pay-with-points" translatesAutoresizingMaskIntoConstraints="NO" id="bya-ss-o3e">
                                                                                <rect key="frame" x="0.0" y="0.0" width="374" height="40"/>
                                                                            </imageView>
                                                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="8Zd-JV-KQM">
                                                                                <rect key="frame" x="143" y="8" width="88" height="24"/>
                                                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="12"/>
                                                                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                                                <state key="normal" title="Pay with coins">
                                                                                    <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                </state>
                                                                                <userDefinedRuntimeAttributes>
                                                                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                                                        <real key="value" value="9"/>
                                                                                    </userDefinedRuntimeAttribute>
                                                                                </userDefinedRuntimeAttributes>
                                                                                <connections>
                                                                                    <action selector="actionPayWithPoints:" destination="yg8-rb-Hfq" eventType="touchUpInside" id="fMe-Jb-P8W"/>
                                                                                </connections>
                                                                            </button>
                                                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="pfJ-N2-Dee">
                                                                                <rect key="frame" x="0.0" y="0.0" width="374" height="40"/>
                                                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                <state key="normal" title="Button"/>
                                                                                <buttonConfiguration key="configuration" style="plain" title=" "/>
                                                                                <connections>
                                                                                    <action selector="actionPayWithPoints:" destination="yg8-rb-Hfq" eventType="touchUpInside" id="FCt-Kq-RoS"/>
                                                                                </connections>
                                                                            </button>
                                                                        </subviews>
                                                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                                        <constraints>
                                                                            <constraint firstItem="bya-ss-o3e" firstAttribute="leading" secondItem="AjK-Sj-6bF" secondAttribute="leading" id="0Xp-98-BpG"/>
                                                                            <constraint firstAttribute="trailing" secondItem="pfJ-N2-Dee" secondAttribute="trailing" id="5io-Ik-Ekv"/>
                                                                            <constraint firstAttribute="trailing" secondItem="bya-ss-o3e" secondAttribute="trailing" id="9tx-2P-QRR"/>
                                                                            <constraint firstItem="8Zd-JV-KQM" firstAttribute="centerY" secondItem="AjK-Sj-6bF" secondAttribute="centerY" id="H9h-gc-SM7"/>
                                                                            <constraint firstAttribute="bottom" secondItem="pfJ-N2-Dee" secondAttribute="bottom" id="M8V-Yg-JUc"/>
                                                                            <constraint firstItem="bya-ss-o3e" firstAttribute="top" secondItem="AjK-Sj-6bF" secondAttribute="top" id="O5p-eM-FNk"/>
                                                                            <constraint firstAttribute="bottom" secondItem="bya-ss-o3e" secondAttribute="bottom" id="XXc-WJ-kZu"/>
                                                                            <constraint firstAttribute="height" constant="40" id="f9p-1U-NgF"/>
                                                                            <constraint firstItem="8Zd-JV-KQM" firstAttribute="centerX" secondItem="AjK-Sj-6bF" secondAttribute="centerX" id="gbb-KI-zKS"/>
                                                                            <constraint firstItem="pfJ-N2-Dee" firstAttribute="top" secondItem="AjK-Sj-6bF" secondAttribute="top" id="moI-Y8-0hZ"/>
                                                                            <constraint firstItem="pfJ-N2-Dee" firstAttribute="leading" secondItem="AjK-Sj-6bF" secondAttribute="leading" id="nHU-Yk-izg"/>
                                                                        </constraints>
                                                                        <userDefinedRuntimeAttributes>
                                                                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                                                <real key="value" value="7"/>
                                                                            </userDefinedRuntimeAttribute>
                                                                        </userDefinedRuntimeAttributes>
                                                                    </view>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="252" verticalHuggingPriority="251" text="*You will use points." lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="03X-ch-yPh" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                                        <rect key="frame" x="0.0" y="43" width="374" height="11"/>
                                                                        <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="11"/>
                                                                        <color key="textColor" name="AppTheme_DiffBlackColor_#101113"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                            </stackView>
                                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="bd7-2S-Gol">
                                                                <rect key="frame" x="0.0" y="254" width="374" height="40"/>
                                                                <color key="backgroundColor" name="AppTheme_BlueColor_#012CDA"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="40" id="G4s-47-WDh"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="12"/>
                                                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                                <state key="normal" title="Pay Now">
                                                                    <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                </state>
                                                                <userDefinedRuntimeAttributes>
                                                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                                        <real key="value" value="9"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                </userDefinedRuntimeAttributes>
                                                                <connections>
                                                                    <action selector="payNowAction:" destination="yg8-rb-Hfq" eventType="touchUpInside" id="D9b-C7-1bS"/>
                                                                </connections>
                                                            </button>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="252" verticalHuggingPriority="251" text="" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Khz-oV-Xqr" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                                <rect key="frame" x="0.0" y="309" width="374" height="50"/>
                                                                <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="11"/>
                                                                <color key="textColor" name="AppTheme_DarkGrayColor_#A09F9F"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                        </subviews>
                                                    </stackView>
                                                </subviews>
                                                <constraints>
                                                    <constraint firstItem="aqZ-sC-2L8" firstAttribute="leading" secondItem="igF-vU-ag9" secondAttribute="leading" id="Smh-hN-eeG"/>
                                                    <constraint firstAttribute="bottom" secondItem="aqZ-sC-2L8" secondAttribute="bottom" id="apU-qw-uIQ"/>
                                                    <constraint firstItem="aqZ-sC-2L8" firstAttribute="top" secondItem="igF-vU-ag9" secondAttribute="top" id="cBC-ed-FtA"/>
                                                    <constraint firstAttribute="trailing" secondItem="aqZ-sC-2L8" secondAttribute="trailing" id="r6h-HE-lMC"/>
                                                </constraints>
                                            </view>
                                            <visualEffectView hidden="YES" opaque="NO" alpha="0.55000000000000004" contentMode="scaleToFill" ambiguous="YES" translatesAutoresizingMaskIntoConstraints="NO" id="WWK-kU-eP9">
                                                <rect key="frame" x="0.0" y="62" width="414" height="1188"/>
                                                <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" ambiguous="YES" insetsLayoutMarginsFromSafeArea="NO" id="zSJ-YR-XdQ">
                                                    <rect key="frame" x="0.0" y="0.0" width="414" height="1188"/>
                                                    <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                                </view>
                                                <blurEffect style="regular"/>
                                            </visualEffectView>
                                            <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="cnQ-Nc-gxu">
                                                <rect key="frame" x="156" y="62" width="235" height="200"/>
                                                <subviews>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="pYY-y4-Xob">
                                                        <rect key="frame" x="0.0" y="0.0" width="235" height="200"/>
                                                        <subviews>
                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="eSF-E6-PtA">
                                                                <rect key="frame" x="210" y="0.0" width="17" height="17"/>
                                                                <subviews>
                                                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="Polygon-top-icon" translatesAutoresizingMaskIntoConstraints="NO" id="VQV-t2-BTb">
                                                                        <rect key="frame" x="0.0" y="0.0" width="17" height="17"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="width" constant="17" id="82H-dZ-wDd"/>
                                                                            <constraint firstAttribute="height" constant="17" id="zKC-nA-Yhc"/>
                                                                        </constraints>
                                                                    </imageView>
                                                                </subviews>
                                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="trailing" secondItem="VQV-t2-BTb" secondAttribute="trailing" id="3D2-Hi-4zP"/>
                                                                    <constraint firstItem="VQV-t2-BTb" firstAttribute="top" secondItem="eSF-E6-PtA" secondAttribute="top" id="4Fv-NR-mrX"/>
                                                                    <constraint firstAttribute="height" constant="17" id="G2C-ze-O5o"/>
                                                                    <constraint firstItem="VQV-t2-BTb" firstAttribute="leading" secondItem="eSF-E6-PtA" secondAttribute="leading" id="IOd-zq-AB7"/>
                                                                    <constraint firstAttribute="width" constant="17" id="YK8-Er-jkS"/>
                                                                    <constraint firstAttribute="bottom" secondItem="VQV-t2-BTb" secondAttribute="bottom" id="ZID-cJ-zYO"/>
                                                                </constraints>
                                                            </view>
                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="dSC-uX-bwd">
                                                                <rect key="frame" x="0.0" y="17" width="245" height="183"/>
                                                                <subviews>
                                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="20" translatesAutoresizingMaskIntoConstraints="NO" id="nsn-Me-SER">
                                                                        <rect key="frame" x="10" y="20" width="225" height="163"/>
                                                                        <subviews>
                                                                            <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" dataMode="prototypes" style="plain" allowsSelection="NO" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="-1" estimatedSectionHeaderHeight="-1" sectionFooterHeight="-1" estimatedSectionFooterHeight="-1" translatesAutoresizingMaskIntoConstraints="NO" id="Dhc-va-64W">
                                                                                <rect key="frame" x="0.0" y="0.0" width="225" height="163"/>
                                                                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                                            </tableView>
                                                                            <stackView hidden="YES" opaque="NO" contentMode="scaleToFill" alignment="top" spacing="2" translatesAutoresizingMaskIntoConstraints="NO" id="lgI-uf-dhK">
                                                                                <rect key="frame" x="0.0" y="0.0" width="225" height="0.0"/>
                                                                                <subviews>
                                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="1." lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="wXv-1K-07Y" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                                                        <rect key="frame" x="0.0" y="0.0" width="18" height="0.0"/>
                                                                                        <constraints>
                                                                                            <constraint firstAttribute="width" constant="18" id="f4x-pp-PnG"/>
                                                                                        </constraints>
                                                                                        <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="12"/>
                                                                                        <color key="textColor" name="AppTheme_BlueColor_#012CDA"/>
                                                                                        <nil key="highlightedColor"/>
                                                                                    </label>
                                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="bIT-rz-2l6" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                                                        <rect key="frame" x="20" y="0.0" width="205" height="0.0"/>
                                                                                        <string key="text" base64-UTF8="YES">
U3RhcnQgY29sbGVjdGluZyBjb2lucyB3aXRob3V0IGFueSBtaW5pbXVtIHB1cmNoYXNlIHJlcXVpcmVt
ZW50Lgs
</string>
                                                                                        <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="12"/>
                                                                                        <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                        <nil key="highlightedColor"/>
                                                                                    </label>
                                                                                </subviews>
                                                                            </stackView>
                                                                            <stackView hidden="YES" opaque="NO" contentMode="scaleToFill" alignment="top" spacing="2" translatesAutoresizingMaskIntoConstraints="NO" id="mIn-8b-sgC">
                                                                                <rect key="frame" x="0.0" y="0.0" width="225" height="0.0"/>
                                                                                <subviews>
                                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="2." lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="2jZ-4d-iTQ" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                                                        <rect key="frame" x="0.0" y="0.0" width="18" height="0.0"/>
                                                                                        <constraints>
                                                                                            <constraint firstAttribute="width" constant="18" id="KAA-lF-Ymn"/>
                                                                                        </constraints>
                                                                                        <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="12"/>
                                                                                        <color key="textColor" name="AppTheme_BlueColor_#012CDA"/>
                                                                                        <nil key="highlightedColor"/>
                                                                                    </label>
                                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Redeem your coins to receive discounts on your orders or even cover the entire purchase amount." lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="4FW-Nk-1BC" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                                                        <rect key="frame" x="20" y="0.0" width="205" height="0.0"/>
                                                                                        <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="12"/>
                                                                                        <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                        <nil key="highlightedColor"/>
                                                                                    </label>
                                                                                </subviews>
                                                                            </stackView>
                                                                            <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Ty8-Tg-aQJ">
                                                                                <rect key="frame" x="0.0" y="0.0" width="225" height="0.0"/>
                                                                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                                            </view>
                                                                        </subviews>
                                                                    </stackView>
                                                                </subviews>
                                                                <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                                <constraints>
                                                                    <constraint firstItem="nsn-Me-SER" firstAttribute="leading" secondItem="dSC-uX-bwd" secondAttribute="leading" constant="10" id="5Xx-PF-Q2z"/>
                                                                    <constraint firstAttribute="trailing" secondItem="nsn-Me-SER" secondAttribute="trailing" constant="10" id="YdN-Hx-19X"/>
                                                                    <constraint firstAttribute="bottom" secondItem="nsn-Me-SER" secondAttribute="bottom" id="cra-4u-n4s"/>
                                                                    <constraint firstItem="nsn-Me-SER" firstAttribute="top" secondItem="dSC-uX-bwd" secondAttribute="top" constant="20" id="tAO-yP-5ly"/>
                                                                </constraints>
                                                                <userDefinedRuntimeAttributes>
                                                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                                        <real key="value" value="20"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                </userDefinedRuntimeAttributes>
                                                            </view>
                                                        </subviews>
                                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <constraints>
                                                            <constraint firstAttribute="bottom" secondItem="dSC-uX-bwd" secondAttribute="bottom" id="W5r-nI-7ZG"/>
                                                            <constraint firstItem="eSF-E6-PtA" firstAttribute="top" secondItem="pYY-y4-Xob" secondAttribute="top" id="WE7-zk-iVZ"/>
                                                            <constraint firstItem="dSC-uX-bwd" firstAttribute="leading" secondItem="pYY-y4-Xob" secondAttribute="leading" id="d4a-xC-ikM"/>
                                                            <constraint firstItem="dSC-uX-bwd" firstAttribute="top" secondItem="pYY-y4-Xob" secondAttribute="top" constant="17" id="g59-pX-27o"/>
                                                            <constraint firstAttribute="trailing" secondItem="dSC-uX-bwd" secondAttribute="trailing" constant="-10" id="qmk-Ed-yRj"/>
                                                            <constraint firstAttribute="trailing" secondItem="eSF-E6-PtA" secondAttribute="trailing" constant="8" id="r4r-y0-eLU"/>
                                                        </constraints>
                                                    </view>
                                                </subviews>
                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstAttribute="bottom" secondItem="pYY-y4-Xob" secondAttribute="bottom" id="UHG-GH-dpr"/>
                                                    <constraint firstAttribute="width" constant="235" id="cGG-zE-cAo"/>
                                                    <constraint firstItem="pYY-y4-Xob" firstAttribute="leading" secondItem="cnQ-Nc-gxu" secondAttribute="leading" id="dmG-SP-Oj3"/>
                                                    <constraint firstAttribute="height" constant="200" id="s5K-a5-rBu"/>
                                                    <constraint firstAttribute="trailing" secondItem="pYY-y4-Xob" secondAttribute="trailing" id="wCQ-YK-JKb"/>
                                                    <constraint firstItem="pYY-y4-Xob" firstAttribute="top" secondItem="cnQ-Nc-gxu" secondAttribute="top" id="z9A-Mv-qp7"/>
                                                </constraints>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                        <real key="value" value="20"/>
                                                    </userDefinedRuntimeAttribute>
                                                </userDefinedRuntimeAttributes>
                                            </view>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstItem="WWK-kU-eP9" firstAttribute="top" secondItem="z6Y-6U-e5S" secondAttribute="top" constant="62" id="0c8-Na-iWg"/>
                                            <constraint firstItem="cnQ-Nc-gxu" firstAttribute="trailing" secondItem="LRC-LB-NJ6" secondAttribute="trailing" constant="5" id="1je-lz-al2"/>
                                            <constraint firstAttribute="trailing" secondItem="83m-rv-Lia" secondAttribute="trailing" id="4ig-hA-Isj"/>
                                            <constraint firstAttribute="trailing" secondItem="Eqk-fH-JBy" secondAttribute="trailing" constant="20" id="4pn-Uu-PDf"/>
                                            <constraint firstAttribute="trailing" secondItem="MER-Mv-B1C" secondAttribute="trailing" id="5Jd-II-rMs"/>
                                            <constraint firstItem="1Gu-T2-9tv" firstAttribute="top" secondItem="4hB-6p-je0" secondAttribute="bottom" constant="10" id="6QU-fM-RG8"/>
                                            <constraint firstItem="W73-fi-0Ws" firstAttribute="top" secondItem="1Gu-T2-9tv" secondAttribute="bottom" constant="20" id="6dn-pJ-0de"/>
                                            <constraint firstItem="W73-fi-0Ws" firstAttribute="trailing" secondItem="4hB-6p-je0" secondAttribute="trailing" id="6kA-7F-SMw"/>
                                            <constraint firstItem="igF-vU-ag9" firstAttribute="leading" secondItem="AoB-LS-CoW" secondAttribute="leading" id="BAe-83-Dg5"/>
                                            <constraint firstAttribute="trailing" secondItem="RTb-5t-QpU" secondAttribute="trailing" id="BUq-ed-OSc"/>
                                            <constraint firstItem="1Gu-T2-9tv" firstAttribute="trailing" secondItem="4hB-6p-je0" secondAttribute="trailing" id="Bpl-xd-gZ8"/>
                                            <constraint firstAttribute="trailing" secondItem="WWK-kU-eP9" secondAttribute="trailing" id="D66-6E-fCI"/>
                                            <constraint firstItem="igF-vU-ag9" firstAttribute="top" secondItem="AoB-LS-CoW" secondAttribute="bottom" constant="20" id="IUe-kw-bpJ"/>
                                            <constraint firstItem="Eqk-fH-JBy" firstAttribute="top" secondItem="RTb-5t-QpU" secondAttribute="bottom" constant="10" id="LUT-5K-9va"/>
                                            <constraint firstItem="AoB-LS-CoW" firstAttribute="leading" secondItem="1Gu-T2-9tv" secondAttribute="leading" id="P5F-ou-x6D"/>
                                            <constraint firstItem="cnQ-Nc-gxu" firstAttribute="top" secondItem="LRC-LB-NJ6" secondAttribute="bottom" id="Rqe-i8-gYl"/>
                                            <constraint firstItem="4hB-6p-je0" firstAttribute="leading" secondItem="z6Y-6U-e5S" secondAttribute="leading" constant="20" id="S2s-bH-Kpl"/>
                                            <constraint firstItem="1Jn-2r-AKt" firstAttribute="leading" secondItem="z6Y-6U-e5S" secondAttribute="leading" id="SmG-8T-iBj"/>
                                            <constraint firstItem="4hB-6p-je0" firstAttribute="top" secondItem="1Jn-2r-AKt" secondAttribute="bottom" constant="20" id="VPa-tw-wda"/>
                                            <constraint firstItem="WWK-kU-eP9" firstAttribute="leading" secondItem="z6Y-6U-e5S" secondAttribute="leading" id="Xam-7w-5vt"/>
                                            <constraint firstItem="RTb-5t-QpU" firstAttribute="leading" secondItem="z6Y-6U-e5S" secondAttribute="leading" id="gaQ-bw-a9z"/>
                                            <constraint firstItem="AoB-LS-CoW" firstAttribute="top" secondItem="W73-fi-0Ws" secondAttribute="bottom" constant="10" id="he9-qC-iXX"/>
                                            <constraint firstAttribute="trailing" secondItem="1Jn-2r-AKt" secondAttribute="trailing" id="iAM-GP-WuZ"/>
                                            <constraint firstItem="Eqk-fH-JBy" firstAttribute="leading" secondItem="z6Y-6U-e5S" secondAttribute="leading" constant="20" id="j0M-w7-KV7"/>
                                            <constraint firstAttribute="bottom" secondItem="igF-vU-ag9" secondAttribute="bottom" constant="35" id="jf1-RK-hqB"/>
                                            <constraint firstItem="83m-rv-Lia" firstAttribute="leading" secondItem="z6Y-6U-e5S" secondAttribute="leading" id="kGc-nC-7gr"/>
                                            <constraint firstItem="MER-Mv-B1C" firstAttribute="top" secondItem="Eqk-fH-JBy" secondAttribute="bottom" constant="10" id="nV1-fd-Mz3"/>
                                            <constraint firstItem="83m-rv-Lia" firstAttribute="top" secondItem="MER-Mv-B1C" secondAttribute="bottom" constant="20" id="pLv-lF-Cax"/>
                                            <constraint firstItem="RTb-5t-QpU" firstAttribute="top" secondItem="z6Y-6U-e5S" secondAttribute="top" constant="4" id="pf3-V2-5iT"/>
                                            <constraint firstItem="W73-fi-0Ws" firstAttribute="leading" secondItem="4hB-6p-je0" secondAttribute="leading" id="s9O-1A-SLY"/>
                                            <constraint firstItem="1Gu-T2-9tv" firstAttribute="leading" secondItem="4hB-6p-je0" secondAttribute="leading" id="t53-WJ-8n5"/>
                                            <constraint firstItem="MER-Mv-B1C" firstAttribute="leading" secondItem="z6Y-6U-e5S" secondAttribute="leading" id="tnM-xz-6Rx"/>
                                            <constraint firstAttribute="trailing" secondItem="4hB-6p-je0" secondAttribute="trailing" constant="20" id="ujM-yA-EDQ"/>
                                            <constraint firstItem="AoB-LS-CoW" firstAttribute="trailing" secondItem="1Gu-T2-9tv" secondAttribute="trailing" id="vrK-Rz-QvY"/>
                                            <constraint firstAttribute="bottom" secondItem="WWK-kU-eP9" secondAttribute="bottom" id="xAO-bf-HsH"/>
                                            <constraint firstItem="igF-vU-ag9" firstAttribute="trailing" secondItem="AoB-LS-CoW" secondAttribute="trailing" id="y6x-jZ-yTS"/>
                                            <constraint firstItem="1Jn-2r-AKt" firstAttribute="top" secondItem="83m-rv-Lia" secondAttribute="bottom" constant="2" id="zSf-t4-mrY"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <constraints>
                                    <constraint firstAttribute="trailing" secondItem="z6Y-6U-e5S" secondAttribute="trailing" id="3cJ-Of-hN3"/>
                                    <constraint firstItem="z6Y-6U-e5S" firstAttribute="centerX" secondItem="Xch-Fl-o5n" secondAttribute="centerX" id="GpH-eW-qoO"/>
                                    <constraint firstItem="z6Y-6U-e5S" firstAttribute="leading" secondItem="Xch-Fl-o5n" secondAttribute="leading" id="Ttz-1L-CNv"/>
                                    <constraint firstAttribute="bottom" secondItem="z6Y-6U-e5S" secondAttribute="bottom" id="h6F-FF-KPX"/>
                                    <constraint firstItem="z6Y-6U-e5S" firstAttribute="top" secondItem="Xch-Fl-o5n" secondAttribute="top" id="hWa-PR-0jG"/>
                                </constraints>
                            </scrollView>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="NTS-MN-RC0" customClass="CustomTopViewForShadow" customModule="TopCustomer" customModuleProvider="target">
                                <rect key="frame" x="0.0" y="48" width="414" height="60"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="02l-ZE-Oek" customClass="MaterialLocalizeButton" customModule="TopCustomer" customModuleProvider="target">
                                        <rect key="frame" x="15" y="12.5" width="35" height="35"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="35" id="KnD-w9-pKo"/>
                                            <constraint firstAttribute="width" constant="35" id="xZa-2W-AHn"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="12"/>
                                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                        <state key="normal" image="icn_back">
                                            <color key="titleColor" name="AppTheme_BlueColor_#012CDA"/>
                                        </state>
                                        <connections>
                                            <action selector="btnBackAction:" destination="yg8-rb-Hfq" eventType="touchUpInside" id="Ehg-qL-UX5"/>
                                        </connections>
                                    </button>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="My Cart" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="QQ2-58-30M">
                                        <rect key="frame" x="160" y="18.5" width="94.5" height="23"/>
                                        <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="23"/>
                                        <color key="textColor" name="AppTheme_BlueColor_#012CDA"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstItem="02l-ZE-Oek" firstAttribute="leading" secondItem="NTS-MN-RC0" secondAttribute="leading" constant="15" id="4st-PD-0G8"/>
                                    <constraint firstItem="QQ2-58-30M" firstAttribute="centerX" secondItem="NTS-MN-RC0" secondAttribute="centerX" id="9Lj-lg-RDE"/>
                                    <constraint firstItem="02l-ZE-Oek" firstAttribute="centerY" secondItem="NTS-MN-RC0" secondAttribute="centerY" id="AOG-Eq-VKD"/>
                                    <constraint firstAttribute="height" constant="60" id="Mo7-Nn-GmB"/>
                                    <constraint firstItem="QQ2-58-30M" firstAttribute="centerY" secondItem="NTS-MN-RC0" secondAttribute="centerY" id="VOv-N8-60D"/>
                                </constraints>
                            </view>
                            <visualEffectView hidden="YES" opaque="NO" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="gfw-Vw-fya">
                                <rect key="frame" x="0.0" y="0.0" width="414" height="1200"/>
                                <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" insetsLayoutMarginsFromSafeArea="NO" id="k33-KD-78j">
                                    <rect key="frame" x="0.0" y="0.0" width="414" height="1200"/>
                                    <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                </view>
                                <blurEffect style="regular"/>
                            </visualEffectView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="qf1-By-MS8"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="NTS-MN-RC0" firstAttribute="leading" secondItem="qf1-By-MS8" secondAttribute="leading" id="5WR-Kx-87h"/>
                            <constraint firstAttribute="trailing" secondItem="NTS-MN-RC0" secondAttribute="trailing" id="Cuk-ol-HdW"/>
                            <constraint firstItem="Xch-Fl-o5n" firstAttribute="top" secondItem="NTS-MN-RC0" secondAttribute="bottom" constant="4" id="KRD-7I-cde"/>
                            <constraint firstItem="Xch-Fl-o5n" firstAttribute="leading" secondItem="qf1-By-MS8" secondAttribute="leading" id="LLi-pv-7xE"/>
                            <constraint firstItem="NTS-MN-RC0" firstAttribute="top" secondItem="qf1-By-MS8" secondAttribute="top" id="OWF-RC-nL4"/>
                            <constraint firstAttribute="bottom" secondItem="gfw-Vw-fya" secondAttribute="bottom" id="P03-L7-yEW"/>
                            <constraint firstItem="Xch-Fl-o5n" firstAttribute="trailing" secondItem="qf1-By-MS8" secondAttribute="trailing" id="SkM-Rf-aKS"/>
                            <constraint firstItem="gfw-Vw-fya" firstAttribute="top" secondItem="cRc-wo-CE5" secondAttribute="top" id="TRN-LR-2PI"/>
                            <constraint firstItem="qf1-By-MS8" firstAttribute="bottom" secondItem="Xch-Fl-o5n" secondAttribute="bottom" id="ihh-fR-5jt"/>
                            <constraint firstItem="gfw-Vw-fya" firstAttribute="trailing" secondItem="qf1-By-MS8" secondAttribute="trailing" id="mwe-TP-x5l"/>
                            <constraint firstItem="gfw-Vw-fya" firstAttribute="leading" secondItem="qf1-By-MS8" secondAttribute="leading" id="oP2-Mb-gKV"/>
                        </constraints>
                        <variation key="default">
                            <mask key="subviews">
                                <exclude reference="gfw-Vw-fya"/>
                            </mask>
                        </variation>
                    </view>
                    <navigationItem key="navigationItem" id="FJt-N6-Y76"/>
                    <size key="freeformSize" width="414" height="1200"/>
                    <connections>
                        <outlet property="BtnChangeDeliveryShift" destination="sMr-9m-N0F" id="EpE-H9-hVi"/>
                        <outlet property="LblDeliveryShiftTitleSelected" destination="xfG-OR-ppg" id="Op1-wX-dQC"/>
                        <outlet property="btnApply" destination="9EG-9I-elh" id="ckf-cR-tka"/>
                        <outlet property="btnChangeDeliveryAddress" destination="9QD-le-8o2" id="mJK-L1-y5M"/>
                        <outlet property="btnCountryCode" destination="xrL-oh-uVS" id="9vP-Cg-41a"/>
                        <outlet property="btnDiscountUsingPoints" destination="ozy-RU-Oxn" id="bhy-Tf-KgW"/>
                        <outlet property="btnDiscountUsingPointsTwo" destination="oey-vg-6Gk" id="CPF-VK-bLG"/>
                        <outlet property="btnEvery2Weeks" destination="niu-rn-J0d" id="Unr-zs-RTO"/>
                        <outlet property="btnEveryMonth" destination="qXj-J7-lVA" id="0ah-mC-A36"/>
                        <outlet property="btnEveryWeek" destination="DQJ-J1-i9R" id="8Eb-pq-WBW"/>
                        <outlet property="btnOnlyOnce" destination="N1D-i5-3XR" id="C4e-fx-410"/>
                        <outlet property="btnPayNow" destination="bd7-2S-Gol" id="JhA-gf-52x"/>
                        <outlet property="btnPayWithPoints" destination="8Zd-JV-KQM" id="Yi1-cW-8JO"/>
                        <outlet property="collectionViewShifts" destination="8Wd-fh-h0M" id="ok9-oY-PUi"/>
                        <outlet property="collectionViewShiftsByDate" destination="YZT-AB-88Z" id="Vyr-IH-RmU"/>
                        <outlet property="constraintShecduleShift" destination="zSf-t4-mrY" id="aiS-bT-0EL"/>
                        <outlet property="imgMosqueIcon" destination="gen-A3-W2G" id="JE9-Na-9ZA"/>
                        <outlet property="lblAddress" destination="TqE-aH-LXs" id="ZiI-LJ-7ek"/>
                        <outlet property="lblCoinsDiscount" destination="rsF-be-XqA" id="2sV-HZ-OWL"/>
                        <outlet property="lblCoinsDiscountHint" destination="Vhw-Ju-TiR" id="rFG-nO-9VX"/>
                        <outlet property="lblCoinsDiscountValue" destination="vnz-N8-Qe9" id="bqO-i6-YeT"/>
                        <outlet property="lblCongrats" destination="yKC-iC-Zg6" id="oHs-mr-GMk"/>
                        <outlet property="lblCongratsValue" destination="SXN-Ig-Ipo" id="lyF-fr-33G"/>
                        <outlet property="lblDeliveryAddressTitle" destination="ZIl-M2-fBp" id="nPM-4e-moz"/>
                        <outlet property="lblDeliveryDiscountTitle" destination="5vL-1J-olg" id="CIE-kl-d1t"/>
                        <outlet property="lblDeliveryDiscountValue" destination="9Ak-aI-JAh" id="puC-Mf-KH8"/>
                        <outlet property="lblDeliveryShiftTitle" destination="p6r-Vg-D2P" id="0pO-aF-6jR"/>
                        <outlet property="lblDeliveryTitle" destination="MKV-dG-muH" id="3h9-BX-PwW"/>
                        <outlet property="lblDeliveryValue" destination="Hpl-7u-11F" id="cmT-Ef-9yT"/>
                        <outlet property="lblDiscountTitle" destination="aKQ-rB-I92" id="opY-0q-nby"/>
                        <outlet property="lblDiscountValue" destination="sOC-Od-5rC" id="fYC-fx-yrG"/>
                        <outlet property="lblDriverWillCall" destination="oC5-US-QyS" id="NSw-Tj-qhX"/>
                        <outlet property="lblNotesTitle" destination="4hB-6p-je0" id="0Qh-FA-k3O"/>
                        <outlet property="lblPromoCodeTitle" destination="W73-fi-0Ws" id="uWM-Gb-hUt"/>
                        <outlet property="lblScheduleOrderTitle" destination="HF3-Lr-HCH" id="x0P-oM-Ped"/>
                        <outlet property="lblSubTotalTitle" destination="lSe-B7-cWx" id="QUF-5Y-fBZ"/>
                        <outlet property="lblSubTotalValue" destination="5h4-Lq-M28" id="pGL-Pj-Utd"/>
                        <outlet property="lblTitle" destination="QQ2-58-30M" id="SVq-iC-ev6"/>
                        <outlet property="lblTotalTitle" destination="dfz-RF-iBy" id="6Dj-4d-tb5"/>
                        <outlet property="lblTotalValue" destination="rLa-up-MTb" id="815-ya-zTM"/>
                        <outlet property="lblType" destination="A8l-J0-VKa" id="P1o-w6-TD1"/>
                        <outlet property="lblUponArrival" destination="nEZ-He-goZ" id="c4l-2i-Wu9"/>
                        <outlet property="lblUsePoints" destination="03X-ch-yPh" id="tAY-Uw-A26"/>
                        <outlet property="lblVATIncludes" destination="dBf-Dk-rdj" id="WVM-E1-fzt"/>
                        <outlet property="lblVATRegNo" destination="v47-58-xoN" id="wA5-6f-d0E"/>
                        <outlet property="lblVATTitle" destination="dQF-7P-AE3" id="oYT-52-Lnf"/>
                        <outlet property="lblVATValue" destination="jsV-nG-QJg" id="SMH-Gu-ooM"/>
                        <outlet property="stackCoinsDiscount" destination="QNO-o4-qul" id="9Y5-94-QI0"/>
                        <outlet property="stackViewDeliveryDiscount" destination="xgC-aE-SdE" id="NNO-sv-4ig"/>
                        <outlet property="stackViewDiscount" destination="czc-3Q-9hJ" id="cTi-M3-8Cq"/>
                        <outlet property="stackViewDiscountUsingPoints" destination="zh4-Jq-tTS" id="eW8-p3-r1V"/>
                        <outlet property="stackViewPayWithPoints" destination="049-Cc-94F" id="i4c-Yz-iCK"/>
                        <outlet property="tblCheckout" destination="xDI-wE-j0g" id="QP6-55-o0M"/>
                        <outlet property="tblRewardDetails" destination="Dhc-va-64W" id="u7B-Ek-kk7"/>
                        <outlet property="textViewNotes" destination="Jfh-hI-vCL" id="gXg-vK-wGk"/>
                        <outlet property="txtPhoneNumber" destination="0qr-mj-YPh" id="dWp-Ep-bHE"/>
                        <outlet property="txtPromoCode" destination="Eyr-rc-CuQ" id="u7x-Q7-SMd"/>
                        <outlet property="viewCongrats" destination="X1P-Vc-Srj" id="IeQ-ff-G0B"/>
                        <outlet property="viewInfoPoints" destination="cnQ-Nc-gxu" id="ag2-Fd-Roy"/>
                        <outlet property="viewMain" destination="z6Y-6U-e5S" id="ual-rg-OcK"/>
                        <outlet property="visualEffectViewInfoPoints" destination="WWK-kU-eP9" id="lbO-ZF-5dg"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="97g-9p-od2" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1544.9275362318842" y="55.580357142857139"/>
        </scene>
        <!--Choose Delivery DateVC-->
        <scene sceneID="roQ-qC-zXy">
            <objects>
                <viewController storyboardIdentifier="ChooseDeliveryDateVC" useStoryboardIdentifierAsRestorationIdentifier="YES" id="JDO-4y-XUd" customClass="ChooseDeliveryDateVC" customModule="TopCustomer" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="NLT-FA-Pv9">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="9bh-oP-Vr8">
                                <rect key="frame" x="0.0" y="0.0" width="414" height="862"/>
                                <state key="normal" title="Button"/>
                                <buttonConfiguration key="configuration" style="plain" title=" "/>
                                <connections>
                                    <action selector="closeBtnTapped:" destination="JDO-4y-XUd" eventType="touchUpInside" id="rPm-HC-pao"/>
                                </connections>
                            </button>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="RUM-Ms-vrH">
                                <rect key="frame" x="20" y="198" width="374" height="500"/>
                                <subviews>
                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="25" translatesAutoresizingMaskIntoConstraints="NO" id="htG-W0-xYr">
                                        <rect key="frame" x="20" y="47" width="334" height="406"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Choose a Delivery Date" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="iG6-gT-NHe" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                <rect key="frame" x="0.0" y="0.0" width="334" height="16"/>
                                                <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="16"/>
                                                <color key="textColor" name="AppTheme_BlueColor_#012CDA"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <collectionView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" dataMode="prototypes" translatesAutoresizingMaskIntoConstraints="NO" id="6pC-l3-SB8">
                                                <rect key="frame" x="0.0" y="41" width="334" height="300"/>
                                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" relation="greaterThanOrEqual" constant="300" id="jDf-1a-8PT"/>
                                                </constraints>
                                                <collectionViewFlowLayout key="collectionViewLayout" minimumLineSpacing="10" minimumInteritemSpacing="10" id="VM2-jf-k8f">
                                                    <size key="itemSize" width="100" height="100"/>
                                                    <size key="headerReferenceSize" width="0.0" height="0.0"/>
                                                    <size key="footerReferenceSize" width="0.0" height="0.0"/>
                                                    <inset key="sectionInset" minX="0.0" minY="0.0" maxX="0.0" maxY="0.0"/>
                                                </collectionViewFlowLayout>
                                                <cells>
                                                    <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" id="DTe-gk-gzY">
                                                        <rect key="frame" x="0.0" y="0.0" width="100" height="100"/>
                                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                                        <collectionViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" insetsLayoutMarginsFromSafeArea="NO" id="7D9-Xx-VFX">
                                                            <rect key="frame" x="0.0" y="0.0" width="100" height="100"/>
                                                            <autoresizingMask key="autoresizingMask"/>
                                                        </collectionViewCellContentView>
                                                    </collectionViewCell>
                                                </cells>
                                            </collectionView>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="3JL-Yu-nGn">
                                                <rect key="frame" x="0.0" y="366" width="334" height="40"/>
                                                <color key="backgroundColor" name="AppTheme_BlueColor_#012CDA"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="40" id="LvQ-5Z-hIJ"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="12"/>
                                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                <state key="normal" title="Confirm">
                                                    <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                </state>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                        <real key="value" value="9"/>
                                                    </userDefinedRuntimeAttribute>
                                                </userDefinedRuntimeAttributes>
                                                <connections>
                                                    <action selector="actionConfirm:" destination="JDO-4y-XUd" eventType="touchUpInside" id="ZBI-BE-0Sd"/>
                                                    <action selector="payNowAction:" destination="yg8-rb-Hfq" eventType="touchUpInside" id="yzk-GA-o08"/>
                                                </connections>
                                            </button>
                                        </subviews>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="406" id="EHX-Rw-5r0"/>
                                        </constraints>
                                    </stackView>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstItem="htG-W0-xYr" firstAttribute="centerX" secondItem="RUM-Ms-vrH" secondAttribute="centerX" id="5RR-mu-g3c"/>
                                    <constraint firstAttribute="height" constant="500" id="QYR-3A-NwL"/>
                                    <constraint firstItem="htG-W0-xYr" firstAttribute="centerY" secondItem="RUM-Ms-vrH" secondAttribute="centerY" id="ToG-dw-63x"/>
                                    <constraint firstItem="htG-W0-xYr" firstAttribute="leading" secondItem="RUM-Ms-vrH" secondAttribute="leading" constant="20" id="Ufr-RA-Kxi"/>
                                    <constraint firstAttribute="trailing" secondItem="htG-W0-xYr" secondAttribute="trailing" constant="20" id="lxA-ss-hp7"/>
                                </constraints>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                        <real key="value" value="35"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="R14-wF-q0O"/>
                        <color key="backgroundColor" white="0.0" alpha="0.5" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="R14-wF-q0O" firstAttribute="bottom" secondItem="9bh-oP-Vr8" secondAttribute="bottom" id="3cl-Z0-fxp"/>
                            <constraint firstItem="9bh-oP-Vr8" firstAttribute="top" secondItem="NLT-FA-Pv9" secondAttribute="top" id="POg-1r-ObF"/>
                            <constraint firstItem="RUM-Ms-vrH" firstAttribute="leading" secondItem="R14-wF-q0O" secondAttribute="leading" constant="20" id="TPX-YA-ic3"/>
                            <constraint firstItem="RUM-Ms-vrH" firstAttribute="centerY" secondItem="NLT-FA-Pv9" secondAttribute="centerY" id="Tta-mX-aqp"/>
                            <constraint firstItem="RUM-Ms-vrH" firstAttribute="centerX" secondItem="NLT-FA-Pv9" secondAttribute="centerX" id="fYt-d3-p2x"/>
                            <constraint firstItem="R14-wF-q0O" firstAttribute="trailing" secondItem="9bh-oP-Vr8" secondAttribute="trailing" id="gtP-PH-nFF"/>
                            <constraint firstItem="R14-wF-q0O" firstAttribute="trailing" secondItem="RUM-Ms-vrH" secondAttribute="trailing" constant="20" id="h4f-ao-drd"/>
                            <constraint firstItem="9bh-oP-Vr8" firstAttribute="leading" secondItem="R14-wF-q0O" secondAttribute="leading" id="pId-pQ-YZy"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="btnConfirm" destination="3JL-Yu-nGn" id="jXb-H5-cSJ"/>
                        <outlet property="collectionChooseDeliveryDate" destination="6pC-l3-SB8" id="htj-AU-OaR"/>
                        <outlet property="lblChooseDeliveryDate" destination="iG6-gT-NHe" id="IoO-aQ-TQ9"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="Eay-Em-em7" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="2273.913043478261" y="-46.205357142857139"/>
        </scene>
    </scenes>
    <resources>
        <image name="Polygon-top-icon" width="17" height="17"/>
        <image name="background-pay-with-points" width="378" height="31"/>
        <image name="dollar-coins" width="65" height="65"/>
        <image name="icn_back" width="13.5" height="21.5"/>
        <image name="icn_cart_blue" width="180" height="173.5"/>
        <image name="icn_radioSelected" width="22" height="21"/>
        <image name="icn_radioUnselected" width="22" height="22"/>
        <image name="information-icon" width="14" height="14"/>
        <image name="mosque-icon" width="26" height="26"/>
        <namedColor name="AppTheme_BlueColor_#012CDA">
            <color red="0.0039215686274509803" green="0.17254901960784313" blue="0.85490196078431369" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="AppTheme_BorderColor_#1F1F1F">
            <color red="0.12156862745098039" green="0.12156862745098039" blue="0.12156862745098039" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="AppTheme_DarkGrayAddressColor_#454545">
            <color red="0.27058823529411763" green="0.27058823529411763" blue="0.27058823529411763" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="AppTheme_DarkGrayColor_#5D5D5D">
            <color red="0.36470588235294116" green="0.36470588235294116" blue="0.36470588235294116" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="AppTheme_DarkGrayColor_#A09F9F">
            <color red="0.62745098039215685" green="0.62352941176470589" blue="0.62352941176470589" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="AppTheme_DiffBlackColor_#101113">
            <color red="0.062745098039215685" green="0.066666666666666666" blue="0.074509803921568626" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="AppTheme_DiscountGreenColor_#05B13E">
            <color red="0.019607843137254902" green="0.69411764705882351" blue="0.24313725490196078" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="AppTheme_FieldBGColor_#F7F7F7">
            <color red="0.96862745098039216" green="0.96862745098039216" blue="0.96862745098039216" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="AppTheme_LightGrayColor_#B4B2B2">
            <color red="0.70588235294117652" green="0.69803921568627447" blue="0.69803921568627447" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="AppTheme_LightGrayColor_#CECECE">
            <color red="0.80784313725490198" green="0.80784313725490198" blue="0.80784313725490198" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="AppTheme_SelectedTabColor_#F4BA45">
            <color red="0.95686274509803926" green="0.72941176470588232" blue="0.27058823529411763" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="AppTheme_VeryLightStatusLineColor_#EFEFEF">
            <color red="0.93725490196078431" green="0.93725490196078431" blue="0.93725490196078431" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <systemColor name="labelColor">
            <color white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
