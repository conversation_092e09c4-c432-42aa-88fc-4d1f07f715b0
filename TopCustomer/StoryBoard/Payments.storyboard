<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="23504" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="23506"/>
        <capability name="Named colors" minToolsVersion="9.0"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="LoewNextArabic-Bold.ttf">
            <string>LoewNextArabic-Bold</string>
        </array>
        <array key="LoewNextArabic-Medium.ttf">
            <string>LoewNextArabic-Medium</string>
        </array>
    </customFonts>
    <scenes>
        <!--Payments View Controller-->
        <scene sceneID="s0d-6b-0kx">
            <objects>
                <viewController storyboardIdentifier="PaymentsViewController" id="Y6W-OH-hqX" customClass="PaymentsViewController" customModule="TopCustomer" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="5EZ-qb-Rvc">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view clipsSubviews="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="hDV-LC-80q">
                                <rect key="frame" x="0.0" y="123" width="414" height="64"/>
                                <subviews>
                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="Wallet" translatesAutoresizingMaskIntoConstraints="NO" id="D3I-Ri-ReP">
                                        <rect key="frame" x="30" y="10" width="33" height="31"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="31" id="7qT-Xr-Dgh"/>
                                            <constraint firstAttribute="width" constant="33" id="rfg-k1-lzy"/>
                                        </constraints>
                                    </imageView>
                                    <label opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Wallet" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="emc-RQ-PRZ" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                        <rect key="frame" x="81" y="18" width="279" height="15"/>
                                        <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="15"/>
                                        <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <label opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="0.00 SAR" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="eWr-Hp-I3f" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                        <rect key="frame" x="81" y="41" width="279" height="12"/>
                                        <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="12"/>
                                        <color key="textColor" name="AppTheme_LightGrayDescColor_#A8A8A8"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <label opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="wFh-9D-Uei">
                                        <rect key="frame" x="20" y="63" width="374" height="1"/>
                                        <color key="backgroundColor" name="AppTheme_LightGrayColor_#CECECE"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="1" id="LnP-hA-wal"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                        <color key="textColor" name="AppTheme_LightGrayColor_#CECECE"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <imageView hidden="YES" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="arrow_settings" translatesAutoresizingMaskIntoConstraints="NO" id="Pez-jD-BFc">
                                        <rect key="frame" x="370" y="14.5" width="14" height="22"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="14" id="Cn6-F6-Tbp"/>
                                            <constraint firstAttribute="height" constant="22" id="SFQ-Ps-Zbm"/>
                                        </constraints>
                                    </imageView>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstItem="Pez-jD-BFc" firstAttribute="leading" secondItem="emc-RQ-PRZ" secondAttribute="trailing" constant="10" id="1bt-HU-sy8"/>
                                    <constraint firstAttribute="trailing" secondItem="Pez-jD-BFc" secondAttribute="trailing" constant="30" id="3k6-Xn-Zn6"/>
                                    <constraint firstItem="eWr-Hp-I3f" firstAttribute="top" secondItem="emc-RQ-PRZ" secondAttribute="bottom" constant="8" id="Iah-7h-Dxi"/>
                                    <constraint firstItem="emc-RQ-PRZ" firstAttribute="leading" secondItem="D3I-Ri-ReP" secondAttribute="trailing" constant="18" id="LYe-wt-FJr"/>
                                    <constraint firstItem="D3I-Ri-ReP" firstAttribute="top" secondItem="hDV-LC-80q" secondAttribute="top" constant="10" id="MCa-GA-4m0"/>
                                    <constraint firstItem="eWr-Hp-I3f" firstAttribute="trailing" secondItem="emc-RQ-PRZ" secondAttribute="trailing" id="PkZ-hf-Rto"/>
                                    <constraint firstItem="wFh-9D-Uei" firstAttribute="leading" secondItem="hDV-LC-80q" secondAttribute="leading" constant="20" id="SBS-L4-qVs"/>
                                    <constraint firstAttribute="bottom" secondItem="wFh-9D-Uei" secondAttribute="bottom" id="WuX-lT-XaC"/>
                                    <constraint firstItem="D3I-Ri-ReP" firstAttribute="leading" secondItem="hDV-LC-80q" secondAttribute="leading" constant="30" id="Zzm-e3-hwb"/>
                                    <constraint firstItem="wFh-9D-Uei" firstAttribute="top" secondItem="eWr-Hp-I3f" secondAttribute="bottom" constant="10" id="aGX-t4-qMq"/>
                                    <constraint firstAttribute="trailing" secondItem="wFh-9D-Uei" secondAttribute="trailing" constant="20" id="bTC-cU-e2I"/>
                                    <constraint firstItem="eWr-Hp-I3f" firstAttribute="leading" secondItem="emc-RQ-PRZ" secondAttribute="leading" id="ba6-4e-Fy6"/>
                                    <constraint firstItem="Pez-jD-BFc" firstAttribute="centerY" secondItem="D3I-Ri-ReP" secondAttribute="centerY" id="chM-Os-Q67"/>
                                    <constraint firstItem="emc-RQ-PRZ" firstAttribute="centerY" secondItem="D3I-Ri-ReP" secondAttribute="centerY" id="ils-nK-7UR"/>
                                </constraints>
                            </view>
                            <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" dataMode="prototypes" style="plain" separatorStyle="none" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="-1" estimatedSectionHeaderHeight="-1" sectionFooterHeight="-1" estimatedSectionFooterHeight="-1" translatesAutoresizingMaskIntoConstraints="NO" id="bCg-eH-Ygf">
                                <rect key="frame" x="0.0" y="202" width="414" height="645"/>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <prototypes>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" preservesSuperviewLayoutMargins="YES" selectionStyle="default" indentationWidth="10" reuseIdentifier="PaymentCardListTableViewCell" id="XAa-AK-YoF" customClass="PaymentCardListTableViewCell" customModule="TopCustomer" customModuleProvider="target">
                                        <rect key="frame" x="0.0" y="50" width="414" height="61"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" preservesSuperviewLayoutMargins="YES" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="XAa-AK-YoF" id="MJK-6U-1hY">
                                            <rect key="frame" x="0.0" y="0.0" width="414" height="61"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="mP8-H9-kYP">
                                                    <rect key="frame" x="0.0" y="0.0" width="414" height="61"/>
                                                    <subviews>
                                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="card" translatesAutoresizingMaskIntoConstraints="NO" id="2Gu-rb-Ld4" customClass="MaterialLocalizeImageView" customModule="TopCustomer" customModuleProvider="target">
                                                            <rect key="frame" x="30" y="10" width="37" height="25"/>
                                                            <constraints>
                                                                <constraint firstAttribute="height" constant="25" id="C4M-Mv-8Bw"/>
                                                                <constraint firstAttribute="width" constant="37" id="urr-IK-t0g"/>
                                                            </constraints>
                                                        </imageView>
                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Made Card" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="DRz-je-87Q" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                            <rect key="frame" x="85" y="15" width="254" height="15"/>
                                                            <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="15"/>
                                                            <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                            <nil key="highlightedColor"/>
                                                        </label>
                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Visa" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="7jh-oK-otb" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                            <rect key="frame" x="85" y="38" width="254" height="12"/>
                                                            <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="12"/>
                                                            <color key="textColor" name="AppTheme_LightGrayDescColor_#A8A8A8"/>
                                                            <nil key="highlightedColor"/>
                                                        </label>
                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="YEw-7T-29W">
                                                            <rect key="frame" x="20" y="60" width="374" height="1"/>
                                                            <color key="backgroundColor" name="AppTheme_LightGrayColor_#CECECE"/>
                                                            <constraints>
                                                                <constraint firstAttribute="height" constant="1" id="8X7-GL-WFz"/>
                                                            </constraints>
                                                            <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                            <color key="textColor" name="AppTheme_LightGrayColor_#CECECE"/>
                                                            <nil key="highlightedColor"/>
                                                        </label>
                                                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="right" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="byu-lO-chU" customClass="MaterialLocalizeButton" customModule="TopCustomer" customModuleProvider="target">
                                                            <rect key="frame" x="349" y="5" width="35" height="35"/>
                                                            <constraints>
                                                                <constraint firstAttribute="height" constant="35" id="0jQ-ld-erM"/>
                                                                <constraint firstAttribute="width" constant="35" id="QyU-jT-Iqx"/>
                                                            </constraints>
                                                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                            <state key="normal" image="icn_delete_grey"/>
                                                        </button>
                                                    </subviews>
                                                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                    <constraints>
                                                        <constraint firstItem="byu-lO-chU" firstAttribute="centerY" secondItem="2Gu-rb-Ld4" secondAttribute="centerY" id="2fp-B4-sW3"/>
                                                        <constraint firstItem="7jh-oK-otb" firstAttribute="top" secondItem="DRz-je-87Q" secondAttribute="bottom" constant="8" id="CGv-zv-sja"/>
                                                        <constraint firstAttribute="trailing" secondItem="byu-lO-chU" secondAttribute="trailing" constant="30" id="K6h-cP-EzK"/>
                                                        <constraint firstItem="2Gu-rb-Ld4" firstAttribute="top" secondItem="mP8-H9-kYP" secondAttribute="top" constant="10" id="QYe-4g-BQF"/>
                                                        <constraint firstItem="7jh-oK-otb" firstAttribute="leading" secondItem="DRz-je-87Q" secondAttribute="leading" id="R1i-b5-Ijk"/>
                                                        <constraint firstAttribute="trailing" secondItem="YEw-7T-29W" secondAttribute="trailing" constant="20" id="Rak-dN-h3y"/>
                                                        <constraint firstItem="DRz-je-87Q" firstAttribute="centerY" secondItem="2Gu-rb-Ld4" secondAttribute="centerY" id="Stg-uJ-wFF"/>
                                                        <constraint firstItem="YEw-7T-29W" firstAttribute="leading" secondItem="mP8-H9-kYP" secondAttribute="leading" constant="20" id="TNV-Ub-x2g"/>
                                                        <constraint firstItem="2Gu-rb-Ld4" firstAttribute="leading" secondItem="mP8-H9-kYP" secondAttribute="leading" constant="30" id="Ufk-ay-nGn"/>
                                                        <constraint firstItem="YEw-7T-29W" firstAttribute="top" secondItem="7jh-oK-otb" secondAttribute="bottom" constant="10" id="f3c-NM-sVh"/>
                                                        <constraint firstAttribute="bottom" secondItem="YEw-7T-29W" secondAttribute="bottom" id="j0L-rs-U4A"/>
                                                        <constraint firstItem="7jh-oK-otb" firstAttribute="trailing" secondItem="DRz-je-87Q" secondAttribute="trailing" id="kdq-ar-t6x"/>
                                                        <constraint firstItem="byu-lO-chU" firstAttribute="leading" secondItem="DRz-je-87Q" secondAttribute="trailing" constant="10" id="kgZ-IE-YaB"/>
                                                        <constraint firstItem="DRz-je-87Q" firstAttribute="leading" secondItem="2Gu-rb-Ld4" secondAttribute="trailing" constant="18" id="nUc-o2-ro8"/>
                                                    </constraints>
                                                </view>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="mP8-H9-kYP" firstAttribute="leading" secondItem="MJK-6U-1hY" secondAttribute="leading" id="BtM-W8-SqP"/>
                                                <constraint firstAttribute="trailing" secondItem="mP8-H9-kYP" secondAttribute="trailing" id="Fy3-QE-9FF"/>
                                                <constraint firstAttribute="bottom" secondItem="mP8-H9-kYP" secondAttribute="bottom" id="P87-u8-B0b"/>
                                                <constraint firstItem="mP8-H9-kYP" firstAttribute="top" secondItem="MJK-6U-1hY" secondAttribute="top" id="bbC-3S-XKd"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                        <connections>
                                            <outlet property="btnDelete" destination="byu-lO-chU" id="RWm-EG-Yby"/>
                                            <outlet property="lblCardNumber" destination="DRz-je-87Q" id="Clz-0I-F62"/>
                                            <outlet property="lblCardScheme" destination="7jh-oK-otb" id="3wv-gU-fQt"/>
                                        </connections>
                                    </tableViewCell>
                                </prototypes>
                                <connections>
                                    <outlet property="dataSource" destination="Y6W-OH-hqX" id="0QX-Lw-cW4"/>
                                    <outlet property="delegate" destination="Y6W-OH-hqX" id="f3l-JF-JRI"/>
                                </connections>
                            </tableView>
                            <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="PXP-FR-M3z">
                                <rect key="frame" x="10" y="524.5" width="394" height="0.0"/>
                                <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="19"/>
                                <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="qEw-5M-xMd" customClass="CustomTopViewForShadow" customModule="TopCustomer" customModuleProvider="target">
                                <rect key="frame" x="0.0" y="48" width="414" height="60"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="rXl-BF-H2Q" customClass="MaterialLocalizeButton" customModule="TopCustomer" customModuleProvider="target">
                                        <rect key="frame" x="25" y="12.5" width="35" height="35"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="35" id="c8f-DC-sid"/>
                                            <constraint firstAttribute="width" constant="35" id="yqU-aZ-h5H"/>
                                        </constraints>
                                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                        <state key="normal" image="icn_back"/>
                                        <connections>
                                            <action selector="actionBack:" destination="Y6W-OH-hqX" eventType="touchUpInside" id="NvW-bM-pCd"/>
                                        </connections>
                                    </button>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Payments" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="b5n-Y0-QaZ">
                                        <rect key="frame" x="148" y="18.5" width="118" height="23"/>
                                        <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="23"/>
                                        <color key="textColor" name="AppTheme_BlueColor_#012CDA"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstItem="rXl-BF-H2Q" firstAttribute="centerY" secondItem="qEw-5M-xMd" secondAttribute="centerY" id="AZF-82-PUn"/>
                                    <constraint firstAttribute="height" constant="60" id="EVN-Pf-o4k"/>
                                    <constraint firstItem="b5n-Y0-QaZ" firstAttribute="centerX" secondItem="qEw-5M-xMd" secondAttribute="centerX" id="JYK-nw-4Fv"/>
                                    <constraint firstItem="rXl-BF-H2Q" firstAttribute="leading" secondItem="qEw-5M-xMd" secondAttribute="leading" constant="25" id="lmy-4c-XiC"/>
                                    <constraint firstItem="b5n-Y0-QaZ" firstAttribute="centerY" secondItem="qEw-5M-xMd" secondAttribute="centerY" id="mcY-Vo-uqa"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="vDu-zF-Fre"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="qEw-5M-xMd" firstAttribute="trailing" secondItem="vDu-zF-Fre" secondAttribute="trailing" id="1lC-bO-Zzt"/>
                            <constraint firstItem="PXP-FR-M3z" firstAttribute="centerY" secondItem="bCg-eH-Ygf" secondAttribute="centerY" id="7sF-V6-TcU"/>
                            <constraint firstItem="hDV-LC-80q" firstAttribute="trailing" secondItem="vDu-zF-Fre" secondAttribute="trailing" id="FLy-OR-CZx"/>
                            <constraint firstItem="vDu-zF-Fre" firstAttribute="bottom" secondItem="hDV-LC-80q" secondAttribute="bottom" constant="10" id="FTi-VH-twc"/>
                            <constraint firstItem="bCg-eH-Ygf" firstAttribute="top" secondItem="hDV-LC-80q" secondAttribute="bottom" constant="15" id="GjL-7m-BDY"/>
                            <constraint firstItem="PXP-FR-M3z" firstAttribute="centerX" secondItem="5EZ-qb-Rvc" secondAttribute="centerX" id="LRm-63-5ZS"/>
                            <constraint firstItem="bCg-eH-Ygf" firstAttribute="leading" secondItem="vDu-zF-Fre" secondAttribute="leading" id="Oai-Dw-kem"/>
                            <constraint firstItem="vDu-zF-Fre" firstAttribute="bottom" secondItem="bCg-eH-Ygf" secondAttribute="bottom" constant="15" id="WKK-4a-zQ1"/>
                            <constraint firstItem="PXP-FR-M3z" firstAttribute="leading" secondItem="5EZ-qb-Rvc" secondAttribute="leading" constant="10" id="WhP-pE-8RP"/>
                            <constraint firstItem="qEw-5M-xMd" firstAttribute="leading" secondItem="vDu-zF-Fre" secondAttribute="leading" id="a2b-o3-SHu"/>
                            <constraint firstItem="bCg-eH-Ygf" firstAttribute="trailing" secondItem="vDu-zF-Fre" secondAttribute="trailing" id="cmT-FM-Krt"/>
                            <constraint firstItem="hDV-LC-80q" firstAttribute="top" secondItem="qEw-5M-xMd" secondAttribute="bottom" constant="15" id="icN-eh-QgO"/>
                            <constraint firstItem="hDV-LC-80q" firstAttribute="leading" secondItem="vDu-zF-Fre" secondAttribute="leading" id="tzN-6s-tgj"/>
                            <constraint firstAttribute="trailing" secondItem="PXP-FR-M3z" secondAttribute="trailing" constant="10" id="yDm-WD-T8x"/>
                            <constraint firstItem="qEw-5M-xMd" firstAttribute="top" secondItem="vDu-zF-Fre" secondAttribute="top" id="zra-nQ-F1X"/>
                        </constraints>
                        <variation key="default">
                            <mask key="constraints">
                                <exclude reference="FTi-VH-twc"/>
                            </mask>
                        </variation>
                    </view>
                    <connections>
                        <outlet property="lblBalance" destination="eWr-Hp-I3f" id="VYf-DM-O62"/>
                        <outlet property="lblNoRecord" destination="PXP-FR-M3z" id="5Hp-em-pxu"/>
                        <outlet property="lblTitle" destination="b5n-Y0-QaZ" id="eN4-tR-hWi"/>
                        <outlet property="lblWallet" destination="emc-RQ-PRZ" id="rME-sL-Xa7"/>
                        <outlet property="tblCards" destination="bCg-eH-Ygf" id="QpZ-Th-oFf"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="Ief-a0-LHa" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="131.8840579710145" y="85.714285714285708"/>
        </scene>
        <!--Choose Payment View Controller-->
        <scene sceneID="qLb-Z0-w2U">
            <objects>
                <viewController storyboardIdentifier="ChoosePaymentViewController" id="NSb-a7-G9b" customClass="ChoosePaymentViewController" customModule="TopCustomer" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="19M-Ah-CFb">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="oF4-bK-Ffp">
                                <rect key="frame" x="0.0" y="48" width="414" height="814"/>
                                <connections>
                                    <action selector="btnDimViewAction:" destination="NSb-a7-G9b" eventType="touchUpInside" id="Brn-ww-lT3"/>
                                </connections>
                            </button>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="tst-nZ-t0Q">
                                <rect key="frame" x="15" y="118" width="384" height="674"/>
                                <subviews>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="0 SAR" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="dRk-wi-ZAv" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                        <rect key="frame" x="35" y="25" width="314" height="20"/>
                                        <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="20"/>
                                        <color key="textColor" red="0.0" green="0.0039215686274509803" blue="0.65490196078431373" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Choose a payment method" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="nlT-gZ-g3B" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                        <rect key="frame" x="35" y="70" width="314" height="17"/>
                                        <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="17"/>
                                        <color key="textColor" name="AppTheme_BlueColor_#012CDA"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Eg3-TY-JnU">
                                        <rect key="frame" x="15" y="102" width="354" height="1"/>
                                        <color key="backgroundColor" name="AppTheme_LightGrayColor_#CECECE"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="1" id="gow-rC-1EF"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                        <color key="textColor" name="AppTheme_LightGrayColor_#CECECE"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" dataMode="prototypes" style="plain" separatorStyle="none" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="-1" estimatedSectionHeaderHeight="-1" sectionFooterHeight="-1" estimatedSectionFooterHeight="-1" translatesAutoresizingMaskIntoConstraints="NO" id="6At-6W-pqN">
                                        <rect key="frame" x="0.0" y="108" width="384" height="45"/>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="45" id="bNn-jq-RQE"/>
                                        </constraints>
                                        <prototypes>
                                            <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" preservesSuperviewLayoutMargins="YES" selectionStyle="default" indentationWidth="10" reuseIdentifier="PaymentCardListTableViewCell" id="0Yz-uo-Nb8" customClass="PaymentCardListTableViewCell" customModule="TopCustomer" customModuleProvider="target">
                                                <rect key="frame" x="0.0" y="50" width="384" height="45"/>
                                                <autoresizingMask key="autoresizingMask"/>
                                                <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" preservesSuperviewLayoutMargins="YES" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="0Yz-uo-Nb8" id="8fh-RQ-9tz">
                                                    <rect key="frame" x="0.0" y="0.0" width="384" height="45"/>
                                                    <autoresizingMask key="autoresizingMask"/>
                                                    <subviews>
                                                        <view contentMode="scaleToFill" ambiguous="YES" translatesAutoresizingMaskIntoConstraints="NO" id="An2-qP-fzh">
                                                            <rect key="frame" x="0.0" y="0.0" width="384" height="45"/>
                                                            <subviews>
                                                                <button opaque="NO" userInteractionEnabled="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="lDN-T5-rQA">
                                                                    <rect key="frame" x="25" y="2.5" width="40" height="40"/>
                                                                    <constraints>
                                                                        <constraint firstAttribute="height" constant="40" id="0FL-BD-iJA"/>
                                                                        <constraint firstAttribute="width" constant="40" id="9Vi-8e-rIJ"/>
                                                                    </constraints>
                                                                    <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="8"/>
                                                                    <inset key="titleEdgeInsets" minX="10" minY="0.0" maxX="0.0" maxY="0.0"/>
                                                                    <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                                    <state key="normal" image="icn_radioUnselected">
                                                                        <color key="titleColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                    </state>
                                                                    <state key="selected" image="icn_radioSelected"/>
                                                                </button>
                                                                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="card" translatesAutoresizingMaskIntoConstraints="NO" id="rXA-CM-i0Z" customClass="MaterialLocalizeImageView" customModule="TopCustomer" customModuleProvider="target">
                                                                    <rect key="frame" x="90" y="10" width="37" height="25"/>
                                                                    <constraints>
                                                                        <constraint firstAttribute="width" constant="37" id="0tK-OJ-Qcy"/>
                                                                        <constraint firstAttribute="height" constant="25" id="W1K-tN-Ohf"/>
                                                                    </constraints>
                                                                </imageView>
                                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" ambiguous="YES" text="" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="ehA-ED-KE1" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                                    <rect key="frame" x="152" y="22.5" width="214" height="0.0"/>
                                                                    <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="15"/>
                                                                    <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                    <nil key="highlightedColor"/>
                                                                </label>
                                                            </subviews>
                                                            <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                            <constraints>
                                                                <constraint firstItem="ehA-ED-KE1" firstAttribute="centerY" secondItem="rXA-CM-i0Z" secondAttribute="centerY" id="4IR-9i-meL"/>
                                                                <constraint firstItem="ehA-ED-KE1" firstAttribute="leading" secondItem="rXA-CM-i0Z" secondAttribute="trailing" constant="25" id="F2r-xZ-hhH"/>
                                                                <constraint firstAttribute="bottom" secondItem="rXA-CM-i0Z" secondAttribute="bottom" constant="10" id="Hfx-IR-2gQ"/>
                                                                <constraint firstItem="lDN-T5-rQA" firstAttribute="leading" secondItem="An2-qP-fzh" secondAttribute="leading" constant="25" id="N4J-vf-JVS"/>
                                                                <constraint firstItem="rXA-CM-i0Z" firstAttribute="top" secondItem="An2-qP-fzh" secondAttribute="top" constant="10" id="QHL-a9-19i"/>
                                                                <constraint firstItem="lDN-T5-rQA" firstAttribute="centerY" secondItem="rXA-CM-i0Z" secondAttribute="centerY" id="RsA-BS-bVM"/>
                                                                <constraint firstItem="rXA-CM-i0Z" firstAttribute="leading" secondItem="lDN-T5-rQA" secondAttribute="trailing" constant="25" id="cWl-Vg-F0p"/>
                                                                <constraint firstAttribute="trailing" secondItem="ehA-ED-KE1" secondAttribute="trailing" constant="18" id="cgX-uz-uCy"/>
                                                                <constraint firstItem="rXA-CM-i0Z" firstAttribute="centerY" secondItem="An2-qP-fzh" secondAttribute="centerY" id="j69-5Q-twG"/>
                                                            </constraints>
                                                        </view>
                                                    </subviews>
                                                    <constraints>
                                                        <constraint firstAttribute="bottom" secondItem="An2-qP-fzh" secondAttribute="bottom" id="WXw-eY-Ctl"/>
                                                        <constraint firstItem="An2-qP-fzh" firstAttribute="leading" secondItem="8fh-RQ-9tz" secondAttribute="leading" id="ZQt-Nb-aty"/>
                                                        <constraint firstAttribute="trailing" secondItem="An2-qP-fzh" secondAttribute="trailing" id="hBZ-Nq-AkE"/>
                                                        <constraint firstItem="An2-qP-fzh" firstAttribute="top" secondItem="8fh-RQ-9tz" secondAttribute="top" id="rkX-qz-MRq"/>
                                                    </constraints>
                                                </tableViewCellContentView>
                                                <connections>
                                                    <outlet property="btnSelection" destination="lDN-T5-rQA" id="RNS-iB-17I"/>
                                                    <outlet property="lblCardNumber" destination="ehA-ED-KE1" id="T6s-AB-zCK"/>
                                                </connections>
                                            </tableViewCell>
                                        </prototypes>
                                        <connections>
                                            <outlet property="dataSource" destination="NSb-a7-G9b" id="7mB-u4-gNF"/>
                                            <outlet property="delegate" destination="NSb-a7-G9b" id="RhI-0T-oDS"/>
                                        </connections>
                                    </tableView>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="X0w-3c-al6" customClass="MaterialLocalizeButton" customModule="TopCustomer" customModuleProvider="target">
                                        <rect key="frame" x="23" y="168" width="338" height="40"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="40" id="SDK-HT-2Iq"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="17"/>
                                        <inset key="titleEdgeInsets" minX="30" minY="0.0" maxX="0.0" maxY="0.0"/>
                                        <inset key="imageEdgeInsets" minX="15" minY="0.0" maxX="0.0" maxY="0.0"/>
                                        <state key="normal" title="Pay with a new card" image="plus">
                                            <color key="titleColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        </state>
                                        <connections>
                                            <action selector="payWithNewCardAction:" destination="NSb-a7-G9b" eventType="touchUpInside" id="YyR-KC-0Dw"/>
                                        </connections>
                                    </button>
                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="10" translatesAutoresizingMaskIntoConstraints="NO" id="QOk-sP-ITb">
                                        <rect key="frame" x="0.0" y="215" width="384" height="194"/>
                                        <subviews>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="7" translatesAutoresizingMaskIntoConstraints="NO" id="Q1G-c5-562" userLabel="COD">
                                                <rect key="frame" x="0.0" y="0.0" width="384" height="58"/>
                                                <subviews>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="EBU-AN-lER">
                                                        <rect key="frame" x="0.0" y="0.0" width="384" height="1"/>
                                                        <subviews>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="2yT-4x-thY">
                                                                <rect key="frame" x="10" y="0.0" width="364" height="1"/>
                                                                <color key="backgroundColor" name="AppTheme_LightGrayColor_#CECECE"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="1" id="PRv-Oo-jYD"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                                <color key="textColor" name="AppTheme_LightGrayColor_#CECECE"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                        </subviews>
                                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                        <constraints>
                                                            <constraint firstAttribute="bottom" secondItem="2yT-4x-thY" secondAttribute="bottom" id="Hgo-4D-9Rk"/>
                                                            <constraint firstItem="2yT-4x-thY" firstAttribute="leading" secondItem="EBU-AN-lER" secondAttribute="leading" constant="10" id="WbT-rB-Hgb"/>
                                                            <constraint firstAttribute="trailing" secondItem="2yT-4x-thY" secondAttribute="trailing" constant="10" id="aVv-5S-0hc"/>
                                                            <constraint firstAttribute="height" constant="1" id="gho-HV-qzx"/>
                                                            <constraint firstItem="2yT-4x-thY" firstAttribute="top" secondItem="EBU-AN-lER" secondAttribute="top" id="vd1-if-xdA"/>
                                                        </constraints>
                                                    </view>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="xbD-iO-cwS">
                                                        <rect key="frame" x="0.0" y="8" width="384" height="50"/>
                                                        <subviews>
                                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="m6Y-oC-dnA">
                                                                <rect key="frame" x="25" y="5" width="40" height="40"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="40" id="PqX-eo-Fgt"/>
                                                                    <constraint firstAttribute="width" constant="40" id="q1W-Ut-Leb"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="8"/>
                                                                <inset key="titleEdgeInsets" minX="10" minY="0.0" maxX="0.0" maxY="0.0"/>
                                                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                                <state key="normal" image="icn_radioUnselected">
                                                                    <color key="titleColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                </state>
                                                                <state key="selected" image="icn_radioSelected"/>
                                                            </button>
                                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="card_delivery" translatesAutoresizingMaskIntoConstraints="NO" id="5DM-Ck-C27" customClass="MaterialLocalizeImageView" customModule="TopCustomer" customModuleProvider="target">
                                                                <rect key="frame" x="90" y="14" width="51" height="22"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="width" constant="51" id="JfJ-Wk-se7"/>
                                                                    <constraint firstAttribute="height" constant="22" id="uOf-cY-Zyo"/>
                                                                </constraints>
                                                            </imageView>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Card on delivery" lineBreakMode="wordWrap" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="y5N-lZ-DQ3" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                                <rect key="frame" x="159" y="8" width="207" height="34"/>
                                                                <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="15"/>
                                                                <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="P5g-L3-rlM">
                                                                <rect key="frame" x="0.0" y="0.0" width="384" height="50"/>
                                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                                <connections>
                                                                    <action selector="cardOnDeliveryAction:" destination="NSb-a7-G9b" eventType="touchUpInside" id="TEy-Pw-URF"/>
                                                                </connections>
                                                            </button>
                                                        </subviews>
                                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                        <constraints>
                                                            <constraint firstItem="5DM-Ck-C27" firstAttribute="top" secondItem="xbD-iO-cwS" secondAttribute="top" constant="10" id="58g-6r-Cpt"/>
                                                            <constraint firstItem="5DM-Ck-C27" firstAttribute="centerY" secondItem="xbD-iO-cwS" secondAttribute="centerY" id="5jo-br-tsX"/>
                                                            <constraint firstItem="y5N-lZ-DQ3" firstAttribute="top" secondItem="xbD-iO-cwS" secondAttribute="top" constant="8" id="7Lq-av-SuW"/>
                                                            <constraint firstAttribute="trailing" secondItem="P5g-L3-rlM" secondAttribute="trailing" id="A6l-Oo-DZQ"/>
                                                            <constraint firstItem="y5N-lZ-DQ3" firstAttribute="centerY" secondItem="5DM-Ck-C27" secondAttribute="centerY" id="CSt-cQ-QnR"/>
                                                            <constraint firstItem="m6Y-oC-dnA" firstAttribute="centerY" secondItem="5DM-Ck-C27" secondAttribute="centerY" id="L7s-te-DFx"/>
                                                            <constraint firstItem="m6Y-oC-dnA" firstAttribute="leading" secondItem="xbD-iO-cwS" secondAttribute="leading" constant="25" id="Nza-bP-2s1"/>
                                                            <constraint firstItem="y5N-lZ-DQ3" firstAttribute="leading" secondItem="5DM-Ck-C27" secondAttribute="trailing" constant="18" id="Q5X-DQ-NhN"/>
                                                            <constraint firstItem="5DM-Ck-C27" firstAttribute="leading" secondItem="m6Y-oC-dnA" secondAttribute="trailing" constant="25" id="Wiz-ru-b43"/>
                                                            <constraint firstAttribute="trailing" secondItem="y5N-lZ-DQ3" secondAttribute="trailing" constant="18" id="ZQl-fh-Q0f"/>
                                                            <constraint firstAttribute="bottom" secondItem="P5g-L3-rlM" secondAttribute="bottom" id="hIA-uy-rxa"/>
                                                            <constraint firstItem="P5g-L3-rlM" firstAttribute="top" secondItem="xbD-iO-cwS" secondAttribute="top" id="kZo-Bc-TZH"/>
                                                            <constraint firstItem="P5g-L3-rlM" firstAttribute="leading" secondItem="xbD-iO-cwS" secondAttribute="leading" id="unq-rB-YNp"/>
                                                            <constraint firstAttribute="bottom" secondItem="y5N-lZ-DQ3" secondAttribute="bottom" constant="8" id="xmq-AT-ikj"/>
                                                        </constraints>
                                                        <variation key="default">
                                                            <mask key="constraints">
                                                                <exclude reference="58g-6r-Cpt"/>
                                                            </mask>
                                                        </variation>
                                                    </view>
                                                </subviews>
                                            </stackView>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="7" translatesAutoresizingMaskIntoConstraints="NO" id="UU0-Ub-ZmC" userLabel="Apply Pay">
                                                <rect key="frame" x="0.0" y="68" width="384" height="58"/>
                                                <subviews>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="4cM-rr-P8Q">
                                                        <rect key="frame" x="0.0" y="0.0" width="384" height="1"/>
                                                        <subviews>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="GSn-6h-z5A">
                                                                <rect key="frame" x="10" y="0.0" width="364" height="1"/>
                                                                <color key="backgroundColor" name="AppTheme_LightGrayColor_#CECECE"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="1" id="N6b-MM-bhF"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                                <color key="textColor" name="AppTheme_LightGrayColor_#CECECE"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                        </subviews>
                                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                        <constraints>
                                                            <constraint firstItem="GSn-6h-z5A" firstAttribute="leading" secondItem="4cM-rr-P8Q" secondAttribute="leading" constant="10" id="ILd-4c-0e9"/>
                                                            <constraint firstItem="GSn-6h-z5A" firstAttribute="top" secondItem="4cM-rr-P8Q" secondAttribute="top" id="Vap-K0-dnk"/>
                                                            <constraint firstAttribute="bottom" secondItem="GSn-6h-z5A" secondAttribute="bottom" id="jbO-E8-pmm"/>
                                                            <constraint firstAttribute="height" constant="1" id="rhz-Kh-eDI"/>
                                                            <constraint firstAttribute="trailing" secondItem="GSn-6h-z5A" secondAttribute="trailing" constant="10" id="xSf-zi-QJS"/>
                                                        </constraints>
                                                    </view>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Ild-az-73I">
                                                        <rect key="frame" x="0.0" y="8" width="384" height="50"/>
                                                        <subviews>
                                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="xtr-BZ-YtZ">
                                                                <rect key="frame" x="25" y="5" width="40" height="40"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="40" id="9qA-DD-G1s"/>
                                                                    <constraint firstAttribute="width" constant="40" id="ozt-Uz-3AX"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="8"/>
                                                                <inset key="titleEdgeInsets" minX="10" minY="0.0" maxX="0.0" maxY="0.0"/>
                                                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                                <state key="normal" image="icn_radioUnselected">
                                                                    <color key="titleColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                </state>
                                                                <state key="selected" image="icn_radioSelected"/>
                                                            </button>
                                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="apple_pay" translatesAutoresizingMaskIntoConstraints="NO" id="YVZ-mX-FJK">
                                                                <rect key="frame" x="90" y="11" width="43" height="28"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="28" id="Kwv-WS-IAH"/>
                                                                    <constraint firstAttribute="width" constant="43" id="j79-dL-KN4"/>
                                                                </constraints>
                                                            </imageView>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Apple Pay" lineBreakMode="wordWrap" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="yvy-iE-7BH" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                                <rect key="frame" x="151" y="8" width="215" height="34"/>
                                                                <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="15"/>
                                                                <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="gcM-pZ-bPe">
                                                                <rect key="frame" x="0.0" y="0.0" width="384" height="50"/>
                                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                                <connections>
                                                                    <action selector="applePayAction:" destination="NSb-a7-G9b" eventType="touchUpInside" id="b3X-gM-bgC"/>
                                                                </connections>
                                                            </button>
                                                        </subviews>
                                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                        <constraints>
                                                            <constraint firstAttribute="trailing" secondItem="gcM-pZ-bPe" secondAttribute="trailing" id="2LD-5F-732"/>
                                                            <constraint firstItem="xtr-BZ-YtZ" firstAttribute="leading" secondItem="Ild-az-73I" secondAttribute="leading" constant="25" id="6VZ-Zk-i3m"/>
                                                            <constraint firstItem="yvy-iE-7BH" firstAttribute="centerY" secondItem="YVZ-mX-FJK" secondAttribute="centerY" id="H61-Om-2HO"/>
                                                            <constraint firstItem="YVZ-mX-FJK" firstAttribute="centerY" secondItem="Ild-az-73I" secondAttribute="centerY" id="HFB-UN-Hpj"/>
                                                            <constraint firstAttribute="bottom" secondItem="gcM-pZ-bPe" secondAttribute="bottom" id="KGw-rm-27t"/>
                                                            <constraint firstItem="yvy-iE-7BH" firstAttribute="top" secondItem="Ild-az-73I" secondAttribute="top" constant="8" id="KTS-Z6-7FB"/>
                                                            <constraint firstItem="gcM-pZ-bPe" firstAttribute="leading" secondItem="Ild-az-73I" secondAttribute="leading" id="Xgr-iD-ylm"/>
                                                            <constraint firstAttribute="trailing" secondItem="yvy-iE-7BH" secondAttribute="trailing" constant="18" id="f0n-nf-gBT"/>
                                                            <constraint firstItem="xtr-BZ-YtZ" firstAttribute="centerY" secondItem="YVZ-mX-FJK" secondAttribute="centerY" id="le3-da-Vvh"/>
                                                            <constraint firstItem="yvy-iE-7BH" firstAttribute="leading" secondItem="YVZ-mX-FJK" secondAttribute="trailing" constant="18" id="myn-0O-pWc"/>
                                                            <constraint firstItem="gcM-pZ-bPe" firstAttribute="top" secondItem="Ild-az-73I" secondAttribute="top" id="sqX-jm-GFi"/>
                                                            <constraint firstItem="YVZ-mX-FJK" firstAttribute="leading" secondItem="xtr-BZ-YtZ" secondAttribute="trailing" constant="25" id="viF-0j-MbC"/>
                                                            <constraint firstAttribute="bottom" secondItem="yvy-iE-7BH" secondAttribute="bottom" constant="8" id="xXB-0D-9kV"/>
                                                        </constraints>
                                                    </view>
                                                </subviews>
                                            </stackView>
                                            <stackView hidden="YES" opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="7" translatesAutoresizingMaskIntoConstraints="NO" id="yRm-ay-Ei8" userLabel="Tabby">
                                                <rect key="frame" x="0.0" y="131" width="384" height="8"/>
                                                <subviews>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="EKv-3P-TJw">
                                                        <rect key="frame" x="0.0" y="0.0" width="384" height="1"/>
                                                        <subviews>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="qVk-Oo-PeP">
                                                                <rect key="frame" x="10" y="0.0" width="364" height="1"/>
                                                                <color key="backgroundColor" name="AppTheme_LightGrayColor_#CECECE"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="1" id="xaD-pi-gyO"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                                <color key="textColor" name="AppTheme_LightGrayColor_#CECECE"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                        </subviews>
                                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                        <constraints>
                                                            <constraint firstAttribute="trailing" secondItem="qVk-Oo-PeP" secondAttribute="trailing" constant="10" id="1XU-3B-JcZ"/>
                                                            <constraint firstAttribute="bottom" secondItem="qVk-Oo-PeP" secondAttribute="bottom" id="4eQ-9E-Pgf"/>
                                                            <constraint firstItem="qVk-Oo-PeP" firstAttribute="leading" secondItem="EKv-3P-TJw" secondAttribute="leading" constant="10" id="8Lj-Uu-cRs"/>
                                                            <constraint firstItem="qVk-Oo-PeP" firstAttribute="top" secondItem="EKv-3P-TJw" secondAttribute="top" id="MjV-cX-38o"/>
                                                            <constraint firstAttribute="height" constant="1" id="hzV-aa-xRH"/>
                                                        </constraints>
                                                    </view>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="1g0-2d-bab">
                                                        <rect key="frame" x="0.0" y="8" width="384" height="0.0"/>
                                                        <subviews>
                                                            <stackView opaque="NO" contentMode="scaleToFill" spacing="10" translatesAutoresizingMaskIntoConstraints="NO" id="q6U-Ds-U2n">
                                                                <rect key="frame" x="20" y="-14" width="344" height="28"/>
                                                                <subviews>
                                                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="aNu-As-1aP">
                                                                        <rect key="frame" x="0.0" y="0.0" width="50" height="28"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="width" constant="50" id="utZ-FN-zZA"/>
                                                                        </constraints>
                                                                        <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="8"/>
                                                                        <inset key="titleEdgeInsets" minX="10" minY="0.0" maxX="0.0" maxY="0.0"/>
                                                                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                                        <state key="normal" image="icn_radioUnselected">
                                                                            <color key="titleColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                        </state>
                                                                        <state key="selected" image="icn_radioSelected"/>
                                                                    </button>
                                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="CgW-OB-iVu">
                                                                        <rect key="frame" x="60" y="0.0" width="60" height="28"/>
                                                                        <subviews>
                                                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="tabby_icon" translatesAutoresizingMaskIntoConstraints="NO" id="FHC-oZ-xAq">
                                                                                <rect key="frame" x="5" y="-1" width="50" height="30"/>
                                                                                <constraints>
                                                                                    <constraint firstAttribute="height" constant="30" id="bWU-OY-FrJ"/>
                                                                                    <constraint firstAttribute="width" constant="50" id="vSR-QN-4Jc"/>
                                                                                </constraints>
                                                                            </imageView>
                                                                        </subviews>
                                                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="width" constant="60" id="0uP-BS-Vpd"/>
                                                                            <constraint firstItem="FHC-oZ-xAq" firstAttribute="centerY" secondItem="CgW-OB-iVu" secondAttribute="centerY" id="Woa-Gp-4p0"/>
                                                                            <constraint firstItem="FHC-oZ-xAq" firstAttribute="centerX" secondItem="CgW-OB-iVu" secondAttribute="centerX" id="YA9-ls-3UR"/>
                                                                        </constraints>
                                                                    </view>
                                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="fillProportionally" spacing="1" translatesAutoresizingMaskIntoConstraints="NO" id="02o-Rm-Qkh">
                                                                        <rect key="frame" x="130" y="0.0" width="214" height="28"/>
                                                                        <subviews>
                                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Tabby" lineBreakMode="wordWrap" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="gdC-bn-MrK" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                                                <rect key="frame" x="0.0" y="0.0" width="214" height="15"/>
                                                                                <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="15"/>
                                                                                <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                <nil key="highlightedColor"/>
                                                                            </label>
                                                                            <label opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="divide your invoice on 4 months" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="2b2-lT-Xqd" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                                                <rect key="frame" x="0.0" y="16" width="214" height="12"/>
                                                                                <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="12"/>
                                                                                <color key="textColor" name="AppTheme_LightGrayDescColor_#A8A8A8"/>
                                                                                <nil key="highlightedColor"/>
                                                                            </label>
                                                                        </subviews>
                                                                    </stackView>
                                                                </subviews>
                                                            </stackView>
                                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="sH6-TN-Y0g">
                                                                <rect key="frame" x="0.0" y="0.0" width="384" height="0.0"/>
                                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                                <connections>
                                                                    <action selector="tabbyPaymentAction:" destination="NSb-a7-G9b" eventType="touchUpInside" id="cZh-r0-dnh"/>
                                                                </connections>
                                                            </button>
                                                        </subviews>
                                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                        <constraints>
                                                            <constraint firstAttribute="trailing" secondItem="sH6-TN-Y0g" secondAttribute="trailing" id="1OR-rH-zE6"/>
                                                            <constraint firstAttribute="bottom" secondItem="sH6-TN-Y0g" secondAttribute="bottom" id="DKU-eY-kmH"/>
                                                            <constraint firstItem="sH6-TN-Y0g" firstAttribute="leading" secondItem="1g0-2d-bab" secondAttribute="leading" id="HVk-h0-HDY"/>
                                                            <constraint firstItem="q6U-Ds-U2n" firstAttribute="leading" secondItem="1g0-2d-bab" secondAttribute="leading" constant="20" id="Hlt-nm-K3u"/>
                                                            <constraint firstItem="sH6-TN-Y0g" firstAttribute="top" secondItem="1g0-2d-bab" secondAttribute="top" id="ZOY-UJ-6bH"/>
                                                            <constraint firstItem="q6U-Ds-U2n" firstAttribute="centerY" secondItem="1g0-2d-bab" secondAttribute="centerY" id="mHa-hy-DYg"/>
                                                            <constraint firstAttribute="trailing" secondItem="q6U-Ds-U2n" secondAttribute="trailing" constant="20" id="o2e-wp-4ym"/>
                                                        </constraints>
                                                    </view>
                                                </subviews>
                                            </stackView>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="6" translatesAutoresizingMaskIntoConstraints="NO" id="ZHt-xm-Pd5" userLabel="Wallet">
                                                <rect key="frame" x="0.0" y="136" width="384" height="58"/>
                                                <subviews>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="yVf-Qa-I3b">
                                                        <rect key="frame" x="0.0" y="0.0" width="384" height="1"/>
                                                        <subviews>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="XYd-WW-GUI">
                                                                <rect key="frame" x="10" y="0.0" width="364" height="1"/>
                                                                <color key="backgroundColor" name="AppTheme_LightGrayColor_#CECECE"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="1" id="Q0d-zL-vgW"/>
                                                                    <constraint firstAttribute="height" relation="greaterThanOrEqual" constant="1" id="e6g-2L-cL2"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                                <color key="textColor" name="AppTheme_LightGrayColor_#CECECE"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                        </subviews>
                                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                        <constraints>
                                                            <constraint firstAttribute="bottom" secondItem="XYd-WW-GUI" secondAttribute="bottom" id="2hj-Fn-BS0"/>
                                                            <constraint firstItem="XYd-WW-GUI" firstAttribute="top" secondItem="yVf-Qa-I3b" secondAttribute="top" id="7Dy-GQ-krB"/>
                                                            <constraint firstAttribute="trailing" secondItem="XYd-WW-GUI" secondAttribute="trailing" constant="10" id="AW3-bg-6H5"/>
                                                            <constraint firstItem="XYd-WW-GUI" firstAttribute="leading" secondItem="yVf-Qa-I3b" secondAttribute="leading" constant="10" id="Xh1-cu-tSc"/>
                                                            <constraint firstAttribute="height" constant="1" id="iyT-2y-hf1"/>
                                                        </constraints>
                                                    </view>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="BbR-Ph-NDs">
                                                        <rect key="frame" x="0.0" y="7" width="384" height="51"/>
                                                        <subviews>
                                                            <button hidden="YES" opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="4wO-Gp-ykY">
                                                                <rect key="frame" x="25" y="5.5" width="40" height="40"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="40" id="MaO-Nc-mMS"/>
                                                                    <constraint firstAttribute="width" constant="40" id="XaG-H9-67E"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="8"/>
                                                                <inset key="titleEdgeInsets" minX="10" minY="0.0" maxX="0.0" maxY="0.0"/>
                                                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                                <state key="normal" image="icn_radioUnselected">
                                                                    <color key="titleColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                </state>
                                                                <state key="selected" image="icn_radioSelected"/>
                                                            </button>
                                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="43Z-1c-DCF" customClass="MaterialLocalizeButton" customModule="TopCustomer" customModuleProvider="target">
                                                                <rect key="frame" x="25" y="5.5" width="40" height="40"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="width" constant="40" id="M5o-g0-Peo"/>
                                                                    <constraint firstAttribute="height" constant="40" id="yV8-T4-O9D"/>
                                                                </constraints>
                                                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                                <state key="normal" image="untick"/>
                                                                <state key="selected" image="tick_new"/>
                                                            </button>
                                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="Wallet" translatesAutoresizingMaskIntoConstraints="NO" id="6LU-Qp-HT3" customClass="MaterialLocalizeImageView" customModule="TopCustomer" customModuleProvider="target">
                                                                <rect key="frame" x="90" y="10" width="33" height="31"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="31" id="AiA-qs-cB4"/>
                                                                    <constraint firstAttribute="width" constant="33" id="KwZ-B7-K0s"/>
                                                                </constraints>
                                                            </imageView>
                                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="tWv-8o-F4q">
                                                                <rect key="frame" x="141" y="8" width="225" height="35"/>
                                                                <subviews>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Wallet" lineBreakMode="wordWrap" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="xxI-sY-eQN" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                                        <rect key="frame" x="0.0" y="0.0" width="225" height="15"/>
                                                                        <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="15"/>
                                                                        <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                    <label opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="0.00 SAR" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="xOO-J6-q6W" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                                        <rect key="frame" x="0.0" y="23" width="225" height="12"/>
                                                                        <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="12"/>
                                                                        <color key="textColor" name="AppTheme_LightGrayDescColor_#A8A8A8"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                            </stackView>
                                                            <view hidden="YES" userInteractionEnabled="NO" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="10P-bW-shj">
                                                                <rect key="frame" x="25" y="0.0" width="341" height="51"/>
                                                                <color key="backgroundColor" white="0.0" alpha="0.20000000000000001" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                            </view>
                                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Wah-y2-akk">
                                                                <rect key="frame" x="0.0" y="0.0" width="384" height="51"/>
                                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                                <connections>
                                                                    <action selector="walletAction:" destination="NSb-a7-G9b" eventType="touchUpInside" id="viR-i0-oyC"/>
                                                                </connections>
                                                            </button>
                                                        </subviews>
                                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                        <constraints>
                                                            <constraint firstItem="Wah-y2-akk" firstAttribute="top" secondItem="BbR-Ph-NDs" secondAttribute="top" id="31H-3G-bGs"/>
                                                            <constraint firstItem="tWv-8o-F4q" firstAttribute="top" secondItem="BbR-Ph-NDs" secondAttribute="top" constant="8" id="9e2-NU-j7R"/>
                                                            <constraint firstAttribute="bottom" secondItem="10P-bW-shj" secondAttribute="bottom" id="CFf-3E-maP"/>
                                                            <constraint firstItem="10P-bW-shj" firstAttribute="trailing" secondItem="tWv-8o-F4q" secondAttribute="trailing" id="Cwv-hb-36c"/>
                                                            <constraint firstItem="10P-bW-shj" firstAttribute="leading" secondItem="4wO-Gp-ykY" secondAttribute="leading" id="I1d-2l-VMr"/>
                                                            <constraint firstAttribute="trailing" secondItem="Wah-y2-akk" secondAttribute="trailing" id="JIn-Fy-raW"/>
                                                            <constraint firstAttribute="trailing" secondItem="tWv-8o-F4q" secondAttribute="trailing" constant="18" id="Lsz-NC-8Zi"/>
                                                            <constraint firstItem="6LU-Qp-HT3" firstAttribute="leading" secondItem="43Z-1c-DCF" secondAttribute="trailing" constant="25" id="PXI-ai-1cK"/>
                                                            <constraint firstItem="4wO-Gp-ykY" firstAttribute="leading" secondItem="BbR-Ph-NDs" secondAttribute="leading" constant="25" id="ROz-Ae-kel"/>
                                                            <constraint firstItem="10P-bW-shj" firstAttribute="leading" secondItem="43Z-1c-DCF" secondAttribute="leading" id="Rzm-Sx-XSa"/>
                                                            <constraint firstItem="10P-bW-shj" firstAttribute="top" secondItem="BbR-Ph-NDs" secondAttribute="top" id="Shf-Id-1P3"/>
                                                            <constraint firstAttribute="bottom" secondItem="tWv-8o-F4q" secondAttribute="bottom" constant="8" id="VH3-2R-33c"/>
                                                            <constraint firstItem="tWv-8o-F4q" firstAttribute="leading" secondItem="6LU-Qp-HT3" secondAttribute="trailing" constant="18" id="YYv-gc-Eb0"/>
                                                            <constraint firstAttribute="bottom" secondItem="Wah-y2-akk" secondAttribute="bottom" id="apX-Jb-iuG"/>
                                                            <constraint firstItem="Wah-y2-akk" firstAttribute="leading" secondItem="BbR-Ph-NDs" secondAttribute="leading" id="b6U-vX-23Z"/>
                                                            <constraint firstItem="6LU-Qp-HT3" firstAttribute="leading" secondItem="4wO-Gp-ykY" secondAttribute="trailing" constant="25" id="dhr-sS-qgj"/>
                                                            <constraint firstItem="43Z-1c-DCF" firstAttribute="leading" secondItem="BbR-Ph-NDs" secondAttribute="leading" constant="25" id="hQT-3g-cTa"/>
                                                            <constraint firstItem="6LU-Qp-HT3" firstAttribute="centerY" secondItem="43Z-1c-DCF" secondAttribute="centerY" id="kR7-dG-5iy"/>
                                                            <constraint firstItem="4wO-Gp-ykY" firstAttribute="centerY" secondItem="6LU-Qp-HT3" secondAttribute="centerY" id="nKx-Iv-Oaa"/>
                                                            <constraint firstItem="6LU-Qp-HT3" firstAttribute="centerY" secondItem="BbR-Ph-NDs" secondAttribute="centerY" id="nMF-by-RfN"/>
                                                        </constraints>
                                                    </view>
                                                </subviews>
                                            </stackView>
                                        </subviews>
                                    </stackView>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="rA0-4D-thG">
                                        <rect key="frame" x="23" y="614" width="338" height="40"/>
                                        <color key="backgroundColor" name="AppTheme_BlueColor_#012CDA"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="40" id="CKV-TY-lPq"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="12"/>
                                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                        <state key="normal" title="Save">
                                            <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        </state>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                <real key="value" value="7"/>
                                            </userDefinedRuntimeAttribute>
                                        </userDefinedRuntimeAttributes>
                                        <connections>
                                            <action selector="saveAction:" destination="NSb-a7-G9b" eventType="touchUpInside" id="hP7-mX-vHq"/>
                                        </connections>
                                    </button>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstItem="nlT-gZ-g3B" firstAttribute="leading" secondItem="tst-nZ-t0Q" secondAttribute="leading" constant="35" id="1uP-pl-Yc8"/>
                                    <constraint firstItem="dRk-wi-ZAv" firstAttribute="leading" secondItem="nlT-gZ-g3B" secondAttribute="leading" id="3Fh-zL-wyO"/>
                                    <constraint firstItem="rA0-4D-thG" firstAttribute="centerX" secondItem="tst-nZ-t0Q" secondAttribute="centerX" id="3Va-pq-vka"/>
                                    <constraint firstItem="nlT-gZ-g3B" firstAttribute="top" secondItem="dRk-wi-ZAv" secondAttribute="bottom" constant="25" id="3wj-KG-62a"/>
                                    <constraint firstItem="X0w-3c-al6" firstAttribute="top" secondItem="6At-6W-pqN" secondAttribute="bottom" constant="15" id="7x6-Zx-Nff"/>
                                    <constraint firstItem="dRk-wi-ZAv" firstAttribute="trailing" secondItem="nlT-gZ-g3B" secondAttribute="trailing" id="Baf-Md-nNw"/>
                                    <constraint firstItem="rA0-4D-thG" firstAttribute="leading" secondItem="tst-nZ-t0Q" secondAttribute="leading" constant="23" id="KGb-aO-lrc"/>
                                    <constraint firstItem="dRk-wi-ZAv" firstAttribute="top" secondItem="tst-nZ-t0Q" secondAttribute="top" constant="25" id="LuW-nl-Bk5"/>
                                    <constraint firstAttribute="bottom" secondItem="rA0-4D-thG" secondAttribute="bottom" constant="20" id="N6M-U0-f8D"/>
                                    <constraint firstItem="QOk-sP-ITb" firstAttribute="leading" secondItem="tst-nZ-t0Q" secondAttribute="leading" id="RbI-zQ-WtI"/>
                                    <constraint firstAttribute="trailing" secondItem="X0w-3c-al6" secondAttribute="trailing" constant="23" id="UL7-wX-gl2"/>
                                    <constraint firstItem="X0w-3c-al6" firstAttribute="leading" secondItem="tst-nZ-t0Q" secondAttribute="leading" constant="23" id="XEw-Qt-qE8"/>
                                    <constraint firstItem="Eg3-TY-JnU" firstAttribute="top" secondItem="nlT-gZ-g3B" secondAttribute="bottom" constant="15" id="cJd-pt-xyR"/>
                                    <constraint firstItem="Eg3-TY-JnU" firstAttribute="leading" secondItem="tst-nZ-t0Q" secondAttribute="leading" constant="15" id="eFM-3F-5dn"/>
                                    <constraint firstItem="QOk-sP-ITb" firstAttribute="top" secondItem="X0w-3c-al6" secondAttribute="bottom" constant="7" id="kCF-tS-wLn"/>
                                    <constraint firstItem="6At-6W-pqN" firstAttribute="leading" secondItem="tst-nZ-t0Q" secondAttribute="leading" id="lwC-Sw-soU"/>
                                    <constraint firstAttribute="trailing" secondItem="nlT-gZ-g3B" secondAttribute="trailing" constant="35" id="oqr-yg-WZ6"/>
                                    <constraint firstItem="6At-6W-pqN" firstAttribute="top" secondItem="Eg3-TY-JnU" secondAttribute="bottom" constant="5" id="ppg-1t-UiH"/>
                                    <constraint firstAttribute="trailing" secondItem="Eg3-TY-JnU" secondAttribute="trailing" constant="15" id="uly-AT-Qct"/>
                                    <constraint firstAttribute="trailing" secondItem="QOk-sP-ITb" secondAttribute="trailing" id="vKz-JU-Q1X"/>
                                    <constraint firstAttribute="trailing" secondItem="rA0-4D-thG" secondAttribute="trailing" constant="23" id="yda-zC-q3p"/>
                                    <constraint firstAttribute="trailing" secondItem="6At-6W-pqN" secondAttribute="trailing" id="zpZ-ox-Pqm"/>
                                </constraints>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                        <real key="value" value="39"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="T3E-To-E5e"/>
                        <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="0.5" colorSpace="custom" customColorSpace="calibratedRGB"/>
                        <constraints>
                            <constraint firstItem="T3E-To-E5e" firstAttribute="trailing" secondItem="tst-nZ-t0Q" secondAttribute="trailing" constant="15" id="23P-8V-lQ9"/>
                            <constraint firstItem="T3E-To-E5e" firstAttribute="bottom" secondItem="oF4-bK-Ffp" secondAttribute="bottom" id="31r-0f-r04"/>
                            <constraint firstItem="tst-nZ-t0Q" firstAttribute="leading" secondItem="T3E-To-E5e" secondAttribute="leading" constant="15" id="Dy8-k0-K4l"/>
                            <constraint firstItem="T3E-To-E5e" firstAttribute="trailing" secondItem="oF4-bK-Ffp" secondAttribute="trailing" id="Ow6-lv-9QS"/>
                            <constraint firstItem="oF4-bK-Ffp" firstAttribute="leading" secondItem="T3E-To-E5e" secondAttribute="leading" id="Wpr-fu-scf"/>
                            <constraint firstItem="tst-nZ-t0Q" firstAttribute="top" secondItem="T3E-To-E5e" secondAttribute="top" constant="70" id="ZwS-ta-njy"/>
                            <constraint firstItem="T3E-To-E5e" firstAttribute="bottom" secondItem="tst-nZ-t0Q" secondAttribute="bottom" constant="70" id="ftq-1b-cWt"/>
                            <constraint firstItem="oF4-bK-Ffp" firstAttribute="top" secondItem="T3E-To-E5e" secondAttribute="top" id="hbL-eZ-2s8"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="btnApplePay" destination="xtr-BZ-YtZ" id="Ups-9d-l89"/>
                        <outlet property="btnCardDuring" destination="m6Y-oC-dnA" id="kSF-uH-pDh"/>
                        <outlet property="btnPayWithNewCard" destination="X0w-3c-al6" id="DXu-0W-gbV"/>
                        <outlet property="btnSave" destination="rA0-4D-thG" id="UeA-M7-2jF"/>
                        <outlet property="btnTabby" destination="aNu-As-1aP" id="A9K-oi-bdb"/>
                        <outlet property="btnWallet" destination="43Z-1c-DCF" id="NVW-LL-Iws"/>
                        <outlet property="cons_tblCards_height" destination="bNn-jq-RQE" id="DsE-a3-wJR"/>
                        <outlet property="lblApplePay" destination="yvy-iE-7BH" id="Mgm-E2-ZUK"/>
                        <outlet property="lblCardOnDelivery" destination="y5N-lZ-DQ3" id="dTg-gm-lM7"/>
                        <outlet property="lblChoosePaymentMethod" destination="nlT-gZ-g3B" id="eem-WM-bGO"/>
                        <outlet property="lblGrandTotal" destination="dRk-wi-ZAv" id="bab-ro-J8m"/>
                        <outlet property="lblTabby" destination="gdC-bn-MrK" id="Uk0-8V-t6N"/>
                        <outlet property="lblTabbyDesc" destination="2b2-lT-Xqd" id="cMc-PQ-JzP"/>
                        <outlet property="lblWalletBalance" destination="xOO-J6-q6W" id="YuE-H3-tOg"/>
                        <outlet property="lblWalletTitle" destination="xxI-sY-eQN" id="xUE-gt-Jmw"/>
                        <outlet property="tblCards" destination="6At-6W-pqN" id="4LF-Xf-xly"/>
                        <outlet property="viewDisableWallet" destination="10P-bW-shj" id="yMC-MX-Vcg"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="cO2-6n-gIJ" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1005.7971014492755" y="85.714285714285708"/>
        </scene>
        <!--Add Card View Controller-->
        <scene sceneID="l1Q-jD-TiH">
            <objects>
                <viewController storyboardIdentifier="AddCardViewController" id="3xo-Ef-UAi" customClass="AddCardViewController" customModule="TopCustomer" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="Zvk-yx-o5P">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" translatesAutoresizingMaskIntoConstraints="NO" id="brW-rb-zj6">
                                <rect key="frame" x="0.0" y="108" width="414" height="754"/>
                                <subviews>
                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="30" translatesAutoresizingMaskIntoConstraints="NO" id="cNp-rc-zGX">
                                        <rect key="frame" x="30" y="42" width="354" height="294"/>
                                        <subviews>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="Y5O-tX-vcF">
                                                <rect key="frame" x="0.0" y="0.0" width="354" height="78"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Card Number" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="1uB-c7-Xyg">
                                                        <rect key="frame" x="0.0" y="0.0" width="354" height="16"/>
                                                        <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="16"/>
                                                        <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="TEy-LF-aQj" customClass="CustomViewForFields" customModule="TopCustomer" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="28" width="354" height="50"/>
                                                        <subviews>
                                                            <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="9kR-qm-OaZ" customClass="CustomTextfieldWithFontStyle" customModule="TopCustomer" customModuleProvider="target">
                                                                <rect key="frame" x="15" y="0.0" width="339" height="50"/>
                                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                <textInputTraits key="textInputTraits" autocorrectionType="no" keyboardType="emailAddress"/>
                                                            </textField>
                                                        </subviews>
                                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <constraints>
                                                            <constraint firstItem="9kR-qm-OaZ" firstAttribute="leading" secondItem="TEy-LF-aQj" secondAttribute="leading" constant="15" id="1uC-Cd-i24"/>
                                                            <constraint firstAttribute="height" constant="50" id="8j4-Ys-qzo"/>
                                                            <constraint firstAttribute="trailing" secondItem="9kR-qm-OaZ" secondAttribute="trailing" id="BdJ-Nw-6MR"/>
                                                            <constraint firstItem="9kR-qm-OaZ" firstAttribute="top" secondItem="TEy-LF-aQj" secondAttribute="top" id="WwS-T0-LUc"/>
                                                            <constraint firstAttribute="bottom" secondItem="9kR-qm-OaZ" secondAttribute="bottom" id="rIm-GB-1N4"/>
                                                        </constraints>
                                                    </view>
                                                </subviews>
                                            </stackView>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="2Qd-z3-tcT">
                                                <rect key="frame" x="0.0" y="108" width="354" height="78"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Name" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Lbu-rW-QYf">
                                                        <rect key="frame" x="0.0" y="0.0" width="354" height="16"/>
                                                        <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="16"/>
                                                        <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="cJe-tz-ZLo" customClass="CustomViewForFields" customModule="TopCustomer" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="28" width="354" height="50"/>
                                                        <subviews>
                                                            <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="FXk-xR-3uA" customClass="CustomTextfieldWithFontStyle" customModule="TopCustomer" customModuleProvider="target">
                                                                <rect key="frame" x="15" y="0.0" width="324" height="50"/>
                                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                <textInputTraits key="textInputTraits" autocorrectionType="no"/>
                                                                <connections>
                                                                    <outlet property="delegate" destination="3xo-Ef-UAi" id="HBs-vl-9ct"/>
                                                                </connections>
                                                            </textField>
                                                        </subviews>
                                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="50" id="66k-0U-OTs"/>
                                                            <constraint firstAttribute="bottom" secondItem="FXk-xR-3uA" secondAttribute="bottom" id="AWz-0v-qkv"/>
                                                            <constraint firstItem="FXk-xR-3uA" firstAttribute="top" secondItem="cJe-tz-ZLo" secondAttribute="top" id="SyQ-i1-tLF"/>
                                                            <constraint firstItem="FXk-xR-3uA" firstAttribute="leading" secondItem="cJe-tz-ZLo" secondAttribute="leading" constant="15" id="b7g-3r-kxd"/>
                                                            <constraint firstAttribute="trailing" secondItem="FXk-xR-3uA" secondAttribute="trailing" constant="15" id="zFG-8l-hLi"/>
                                                        </constraints>
                                                    </view>
                                                </subviews>
                                            </stackView>
                                            <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="40" translatesAutoresizingMaskIntoConstraints="NO" id="oRD-9q-qIX">
                                                <rect key="frame" x="0.0" y="216" width="354" height="78"/>
                                                <subviews>
                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="hE2-SQ-1RX">
                                                        <rect key="frame" x="0.0" y="0.0" width="157" height="78"/>
                                                        <subviews>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Expiry date" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="5MQ-TH-WY1">
                                                                <rect key="frame" x="0.0" y="0.0" width="157" height="16"/>
                                                                <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="16"/>
                                                                <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="WJu-w9-e7z" customClass="CustomViewForFields" customModule="TopCustomer" customModuleProvider="target">
                                                                <rect key="frame" x="0.0" y="28" width="157" height="50"/>
                                                                <subviews>
                                                                    <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="jAG-8l-9Wq" customClass="CustomTextfieldWithFontStyle" customModule="TopCustomer" customModuleProvider="target">
                                                                        <rect key="frame" x="15" y="0.0" width="142" height="50"/>
                                                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                        <textInputTraits key="textInputTraits" autocorrectionType="no" keyboardType="emailAddress"/>
                                                                    </textField>
                                                                </subviews>
                                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstItem="jAG-8l-9Wq" firstAttribute="leading" secondItem="WJu-w9-e7z" secondAttribute="leading" constant="15" id="1Me-c8-SnY"/>
                                                                    <constraint firstAttribute="bottom" secondItem="jAG-8l-9Wq" secondAttribute="bottom" id="8G6-cw-k1m"/>
                                                                    <constraint firstAttribute="height" constant="50" id="8Vq-OM-EFc"/>
                                                                    <constraint firstItem="jAG-8l-9Wq" firstAttribute="top" secondItem="WJu-w9-e7z" secondAttribute="top" id="X3p-kn-hGq"/>
                                                                    <constraint firstAttribute="trailing" secondItem="jAG-8l-9Wq" secondAttribute="trailing" id="eic-bY-3UV"/>
                                                                </constraints>
                                                            </view>
                                                        </subviews>
                                                    </stackView>
                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="5hp-dd-wws">
                                                        <rect key="frame" x="197" y="0.0" width="157" height="78"/>
                                                        <subviews>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="CVV code" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="veD-ge-hM9">
                                                                <rect key="frame" x="0.0" y="0.0" width="157" height="16"/>
                                                                <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="16"/>
                                                                <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="FBS-OY-QAw" customClass="CustomViewForFields" customModule="TopCustomer" customModuleProvider="target">
                                                                <rect key="frame" x="0.0" y="28" width="157" height="50"/>
                                                                <subviews>
                                                                    <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="PRk-1W-b9f" customClass="CustomTextfieldWithFontStyle" customModule="TopCustomer" customModuleProvider="target">
                                                                        <rect key="frame" x="15" y="0.0" width="142" height="50"/>
                                                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                        <textInputTraits key="textInputTraits" autocorrectionType="no" keyboardType="emailAddress"/>
                                                                    </textField>
                                                                </subviews>
                                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="50" id="Ga3-M6-cAU"/>
                                                                    <constraint firstAttribute="trailing" secondItem="PRk-1W-b9f" secondAttribute="trailing" id="MH8-R8-riO"/>
                                                                    <constraint firstItem="PRk-1W-b9f" firstAttribute="leading" secondItem="FBS-OY-QAw" secondAttribute="leading" constant="15" id="O1H-eN-ajB"/>
                                                                    <constraint firstAttribute="bottom" secondItem="PRk-1W-b9f" secondAttribute="bottom" id="eFi-d9-X8a"/>
                                                                    <constraint firstItem="PRk-1W-b9f" firstAttribute="top" secondItem="FBS-OY-QAw" secondAttribute="top" id="wdH-gN-Vcx"/>
                                                                </constraints>
                                                            </view>
                                                        </subviews>
                                                    </stackView>
                                                </subviews>
                                            </stackView>
                                        </subviews>
                                    </stackView>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="2uy-EE-XaA" customClass="CustomRoundedButtton" customModule="TopCustomer" customModuleProvider="target">
                                        <rect key="frame" x="30" y="430" width="354" height="40"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="40" id="aO4-ax-9Gt"/>
                                        </constraints>
                                        <state key="normal" title="Add"/>
                                    </button>
                                </subviews>
                                <constraints>
                                    <constraint firstItem="2uy-EE-XaA" firstAttribute="centerX" secondItem="brW-rb-zj6" secondAttribute="centerX" id="6ln-jt-p0p"/>
                                    <constraint firstItem="cNp-rc-zGX" firstAttribute="centerX" secondItem="brW-rb-zj6" secondAttribute="centerX" id="9KB-YD-xGs"/>
                                    <constraint firstAttribute="bottom" secondItem="2uy-EE-XaA" secondAttribute="bottom" constant="20" id="F4L-hQ-8Tm"/>
                                    <constraint firstItem="cNp-rc-zGX" firstAttribute="leading" secondItem="brW-rb-zj6" secondAttribute="leading" constant="30" id="FCQ-0U-c6C"/>
                                    <constraint firstAttribute="trailing" secondItem="cNp-rc-zGX" secondAttribute="trailing" constant="30" id="LNc-PO-bPh"/>
                                    <constraint firstItem="cNp-rc-zGX" firstAttribute="top" secondItem="brW-rb-zj6" secondAttribute="top" constant="42" id="Swo-n4-aC2"/>
                                    <constraint firstAttribute="trailing" secondItem="2uy-EE-XaA" secondAttribute="trailing" constant="30" id="VmK-OM-ZIX"/>
                                    <constraint firstItem="2uy-EE-XaA" firstAttribute="top" secondItem="cNp-rc-zGX" secondAttribute="bottom" constant="94" id="i9z-hm-MxI"/>
                                    <constraint firstItem="2uy-EE-XaA" firstAttribute="leading" secondItem="brW-rb-zj6" secondAttribute="leading" constant="30" id="wt0-Ji-958"/>
                                </constraints>
                            </scrollView>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="zvu-M9-kVe" customClass="CustomTopViewForShadow" customModule="TopCustomer" customModuleProvider="target">
                                <rect key="frame" x="0.0" y="48" width="414" height="60"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="QST-tY-2cv">
                                        <rect key="frame" x="25" y="12.5" width="43" height="35"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="35" id="GId-WX-JSi"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="12"/>
                                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                        <state key="normal" title="Cancel">
                                            <color key="titleColor" name="AppTheme_BlueColor_#012CDA"/>
                                        </state>
                                        <connections>
                                            <action selector="btnBackAction:" destination="3xo-Ef-UAi" eventType="touchUpInside" id="0d1-O6-hoZ"/>
                                        </connections>
                                    </button>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Add new card" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="nJF-ps-kX1">
                                        <rect key="frame" x="127" y="18.5" width="160.5" height="23"/>
                                        <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="23"/>
                                        <color key="textColor" name="AppTheme_BlueColor_#012CDA"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstItem="nJF-ps-kX1" firstAttribute="centerY" secondItem="zvu-M9-kVe" secondAttribute="centerY" id="1aW-0s-1vk"/>
                                    <constraint firstItem="QST-tY-2cv" firstAttribute="leading" secondItem="zvu-M9-kVe" secondAttribute="leading" constant="25" id="2cg-lo-dIR"/>
                                    <constraint firstAttribute="height" constant="60" id="Nku-XN-sSe"/>
                                    <constraint firstItem="QST-tY-2cv" firstAttribute="centerY" secondItem="zvu-M9-kVe" secondAttribute="centerY" id="Oyh-6L-iEi"/>
                                    <constraint firstItem="nJF-ps-kX1" firstAttribute="centerX" secondItem="zvu-M9-kVe" secondAttribute="centerX" id="y40-E9-Okb"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="cgZ-x2-RFc"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="zvu-M9-kVe" firstAttribute="top" secondItem="cgZ-x2-RFc" secondAttribute="top" id="74g-Kq-X4s"/>
                            <constraint firstItem="zvu-M9-kVe" firstAttribute="leading" secondItem="cgZ-x2-RFc" secondAttribute="leading" id="B3O-2F-2hE"/>
                            <constraint firstItem="cgZ-x2-RFc" firstAttribute="bottom" secondItem="brW-rb-zj6" secondAttribute="bottom" id="Jkg-gq-MAu"/>
                            <constraint firstItem="brW-rb-zj6" firstAttribute="leading" secondItem="cgZ-x2-RFc" secondAttribute="leading" id="Ni7-Gm-BQf"/>
                            <constraint firstItem="zvu-M9-kVe" firstAttribute="trailing" secondItem="cgZ-x2-RFc" secondAttribute="trailing" id="Y9r-ma-PhT"/>
                            <constraint firstItem="brW-rb-zj6" firstAttribute="top" secondItem="zvu-M9-kVe" secondAttribute="bottom" id="jMK-ge-hYP"/>
                            <constraint firstItem="brW-rb-zj6" firstAttribute="trailing" secondItem="cgZ-x2-RFc" secondAttribute="trailing" id="sEL-cp-UAS"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="btnAdd" destination="2uy-EE-XaA" id="XEe-kP-2R2"/>
                        <outlet property="btnCancel" destination="QST-tY-2cv" id="d4P-vu-ukc"/>
                        <outlet property="lblCVVCode" destination="veD-ge-hM9" id="7FT-E0-Kct"/>
                        <outlet property="lblCardNo" destination="1uB-c7-Xyg" id="5JP-lc-gcG"/>
                        <outlet property="lblExpiryDate" destination="5MQ-TH-WY1" id="IyJ-kI-eCD"/>
                        <outlet property="lblName" destination="Lbu-rW-QYf" id="XZR-Do-daQ"/>
                        <outlet property="lblTitle" destination="nJF-ps-kX1" id="iE9-Ws-q5d"/>
                        <outlet property="txtName" destination="FXk-xR-3uA" id="F8Q-fg-WnS"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="O3z-M4-vOU" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1879.7101449275365" y="85.714285714285708"/>
        </scene>
    </scenes>
    <resources>
        <image name="Wallet" width="32.5" height="31"/>
        <image name="apple_pay" width="43" height="27.5"/>
        <image name="arrow_settings" width="13.5" height="21.5"/>
        <image name="card" width="36.5" height="24.5"/>
        <image name="card_delivery" width="51" height="21.5"/>
        <image name="icn_back" width="13.5" height="21.5"/>
        <image name="icn_delete_grey" width="15.5" height="16.5"/>
        <image name="icn_radioSelected" width="22" height="21"/>
        <image name="icn_radioUnselected" width="22" height="22"/>
        <image name="plus" width="14" height="14"/>
        <image name="tabby_icon" width="225" height="225"/>
        <image name="tick_new" width="19.5" height="18.5"/>
        <image name="untick" width="19.5" height="18.5"/>
        <namedColor name="AppTheme_BlueColor_#012CDA">
            <color red="0.0039215686274509803" green="0.17254901960784313" blue="0.85490196078431369" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="AppTheme_LightGrayColor_#CECECE">
            <color red="0.80784313725490198" green="0.80784313725490198" blue="0.80784313725490198" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="AppTheme_LightGrayDescColor_#A8A8A8">
            <color red="0.64300000667572021" green="0.64300000667572021" blue="0.64300000667572021" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
