<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="23504" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES" initialViewController="k5f-9U-ioc">
    <device id="retina4_7" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="23506"/>
        <capability name="Named colors" minToolsVersion="9.0"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="LoewNextArabic-Bold.ttf">
            <string>LoewNextArabic-Bold</string>
        </array>
        <array key="LoewNextArabic-Medium.ttf">
            <string>LoewNextArabic-Medium</string>
        </array>
    </customFonts>
    <scenes>
        <!--Navigation Controller-->
        <scene sceneID="pMp-DR-DZn">
            <objects>
                <navigationController storyboardIdentifier="SplashViewController" navigationBarHidden="YES" id="k5f-9U-ioc" sceneMemberID="viewController">
                    <nil key="simulatedTopBarMetrics"/>
                    <navigationBar key="navigationBar" contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" id="rkL-sV-8tq">
                        <autoresizingMask key="autoresizingMask"/>
                    </navigationBar>
                    <connections>
                        <segue destination="waf-Ar-Qcn" kind="relationship" relationship="rootViewController" id="Ai1-gP-MpE"/>
                    </connections>
                </navigationController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="M4e-2Q-Uss" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-634" y="-177"/>
        </scene>
        <!--Login View Controller-->
        <scene sceneID="sBN-rC-IRO">
            <objects>
                <viewController storyboardIdentifier="LoginViewController" id="JU9-z8-eTw" customClass="LoginViewController" customModule="TopCustomer" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="hfO-x8-5Q1">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="1000"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="logo_login_big" translatesAutoresizingMaskIntoConstraints="NO" id="BGm-MQ-gu0">
                                <rect key="frame" x="85" y="170" width="205" height="68"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="68" id="6Z3-QG-dad"/>
                                    <constraint firstAttribute="width" constant="205" id="YiR-I6-nzK"/>
                                </constraints>
                            </imageView>
                            <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" translatesAutoresizingMaskIntoConstraints="NO" id="1fW-aY-t5f">
                                <rect key="frame" x="0.0" y="298" width="375" height="667"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Zga-kM-CMr">
                                        <rect key="frame" x="0.0" y="0.0" width="375" height="391"/>
                                        <subviews>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="25" translatesAutoresizingMaskIntoConstraints="NO" id="uAO-p2-X2O">
                                                <rect key="frame" x="35" y="20" width="305" height="320"/>
                                                <subviews>
                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="o49-25-C7s">
                                                        <rect key="frame" x="0.0" y="0.0" width="305" height="50"/>
                                                        <subviews>
                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="7Hj-cS-uqc" customClass="CustomViewForFields" customModule="TopCustomer" customModuleProvider="target">
                                                                <rect key="frame" x="0.0" y="0.0" width="305" height="50"/>
                                                                <subviews>
                                                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="user" translatesAutoresizingMaskIntoConstraints="NO" id="DgJ-b2-CDY">
                                                                        <rect key="frame" x="15" y="15" width="20" height="20"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="width" constant="20" id="B89-sI-lX0"/>
                                                                            <constraint firstAttribute="height" constant="20" id="qR4-hp-9BD"/>
                                                                        </constraints>
                                                                    </imageView>
                                                                    <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="Name" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="ft1-UZ-UMl" customClass="CustomTextfieldWithFontStyle" customModule="TopCustomer" customModuleProvider="target">
                                                                        <rect key="frame" x="60" y="0.0" width="230" height="50"/>
                                                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                        <textInputTraits key="textInputTraits" autocapitalizationType="words" autocorrectionType="no"/>
                                                                        <connections>
                                                                            <outlet property="delegate" destination="JU9-z8-eTw" id="gO7-KS-4Qh"/>
                                                                        </connections>
                                                                    </textField>
                                                                </subviews>
                                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstItem="DgJ-b2-CDY" firstAttribute="leading" secondItem="7Hj-cS-uqc" secondAttribute="leading" constant="15" id="0oX-hR-7Wb"/>
                                                                    <constraint firstItem="ft1-UZ-UMl" firstAttribute="leading" secondItem="DgJ-b2-CDY" secondAttribute="trailing" constant="25" id="NSx-Mu-ewq"/>
                                                                    <constraint firstAttribute="bottom" secondItem="ft1-UZ-UMl" secondAttribute="bottom" id="Xd0-9b-ydN"/>
                                                                    <constraint firstAttribute="trailing" secondItem="ft1-UZ-UMl" secondAttribute="trailing" constant="15" id="dmM-RG-ET5"/>
                                                                    <constraint firstItem="DgJ-b2-CDY" firstAttribute="centerY" secondItem="7Hj-cS-uqc" secondAttribute="centerY" id="iVH-zW-Gpt"/>
                                                                    <constraint firstAttribute="height" constant="50" id="t1c-ik-Ome"/>
                                                                    <constraint firstItem="ft1-UZ-UMl" firstAttribute="top" secondItem="7Hj-cS-uqc" secondAttribute="top" id="xHU-EC-GE3"/>
                                                                </constraints>
                                                            </view>
                                                        </subviews>
                                                    </stackView>
                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="RKe-jo-LPA">
                                                        <rect key="frame" x="0.0" y="75" width="305" height="50"/>
                                                        <subviews>
                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="RSR-40-2AC" customClass="CustomViewForFields" customModule="TopCustomer" customModuleProvider="target">
                                                                <rect key="frame" x="0.0" y="0.0" width="305" height="50"/>
                                                                <subviews>
                                                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="smartphone-call" translatesAutoresizingMaskIntoConstraints="NO" id="Qrz-DK-VdF">
                                                                        <rect key="frame" x="15" y="15" width="20" height="20"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="width" constant="20" id="BK8-ec-XLb"/>
                                                                            <constraint firstAttribute="height" constant="20" id="Mxa-gK-jYa"/>
                                                                        </constraints>
                                                                    </imageView>
                                                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="ImK-bn-z1w">
                                                                        <rect key="frame" x="45" y="5" width="60" height="40"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="height" constant="40" id="6Ql-I1-Yiw"/>
                                                                            <constraint firstAttribute="width" constant="60" id="ca9-Hd-YuS"/>
                                                                        </constraints>
                                                                        <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="16"/>
                                                                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                                        <state key="normal">
                                                                            <color key="titleColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                        </state>
                                                                        <connections>
                                                                            <action selector="btnCountryCodePressed:" destination="JU9-z8-eTw" eventType="touchUpInside" id="zFF-HS-ydr"/>
                                                                        </connections>
                                                                    </button>
                                                                    <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="NB4-yU-aqA">
                                                                        <rect key="frame" x="105" y="0.0" width="0.5" height="50"/>
                                                                        <color key="backgroundColor" name="AppTheme_BorderColor_#1F1F1F"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="width" constant="0.25" id="BsK-MM-dm0"/>
                                                                        </constraints>
                                                                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                                        <nil key="textColor"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                    <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="Phone no." minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="kdv-sj-Bds" customClass="CustomTextfieldWithFontStyle" customModule="TopCustomer" customModuleProvider="target">
                                                                        <rect key="frame" x="120.5" y="0.0" width="169.5" height="50"/>
                                                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                        <textInputTraits key="textInputTraits" autocorrectionType="no" keyboardType="ASCIICapableNumberPad"/>
                                                                        <connections>
                                                                            <outlet property="delegate" destination="JU9-z8-eTw" id="tfg-vu-u1a"/>
                                                                        </connections>
                                                                    </textField>
                                                                </subviews>
                                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstItem="NB4-yU-aqA" firstAttribute="leading" secondItem="ImK-bn-z1w" secondAttribute="trailing" id="0M4-Cj-lvf"/>
                                                                    <constraint firstAttribute="trailing" secondItem="kdv-sj-Bds" secondAttribute="trailing" constant="15" id="0Oj-ZS-nHy"/>
                                                                    <constraint firstItem="Qrz-DK-VdF" firstAttribute="leading" secondItem="RSR-40-2AC" secondAttribute="leading" constant="15" id="3yX-Py-ngm"/>
                                                                    <constraint firstItem="ImK-bn-z1w" firstAttribute="leading" secondItem="Qrz-DK-VdF" secondAttribute="trailing" constant="10" id="77L-eR-aBD"/>
                                                                    <constraint firstItem="NB4-yU-aqA" firstAttribute="top" secondItem="RSR-40-2AC" secondAttribute="top" id="Af5-Iw-lMp"/>
                                                                    <constraint firstAttribute="bottom" secondItem="NB4-yU-aqA" secondAttribute="bottom" id="JsL-30-9lz"/>
                                                                    <constraint firstItem="ImK-bn-z1w" firstAttribute="centerY" secondItem="RSR-40-2AC" secondAttribute="centerY" id="MjF-Yo-VdD"/>
                                                                    <constraint firstItem="Qrz-DK-VdF" firstAttribute="centerY" secondItem="RSR-40-2AC" secondAttribute="centerY" id="RwO-dg-mbW"/>
                                                                    <constraint firstAttribute="bottom" secondItem="kdv-sj-Bds" secondAttribute="bottom" id="bDW-El-EOt"/>
                                                                    <constraint firstAttribute="height" constant="50" id="r2d-6S-qZa"/>
                                                                    <constraint firstItem="kdv-sj-Bds" firstAttribute="top" secondItem="RSR-40-2AC" secondAttribute="top" id="uhe-n1-9mu"/>
                                                                    <constraint firstItem="kdv-sj-Bds" firstAttribute="leading" secondItem="NB4-yU-aqA" secondAttribute="trailing" constant="15" id="z70-2d-ahe"/>
                                                                </constraints>
                                                            </view>
                                                        </subviews>
                                                    </stackView>
                                                    <stackView hidden="YES" opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="8Yt-TK-63w">
                                                        <rect key="frame" x="0.0" y="137.5" width="305" height="50"/>
                                                        <subviews>
                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="dc3-Aq-nUy" customClass="CustomViewForFields" customModule="TopCustomer" customModuleProvider="target">
                                                                <rect key="frame" x="0.0" y="0.0" width="305" height="50"/>
                                                                <subviews>
                                                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="KMX-rv-C9x">
                                                                        <rect key="frame" x="0.0" y="0.0" width="305" height="50"/>
                                                                        <state key="normal" title="Button"/>
                                                                        <buttonConfiguration key="configuration" style="plain" title=" "/>
                                                                        <connections>
                                                                            <action selector="btnSelectCityAction:" destination="JU9-z8-eTw" eventType="touchUpInside" id="bbo-TT-L39"/>
                                                                        </connections>
                                                                    </button>
                                                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="location-pin" translatesAutoresizingMaskIntoConstraints="NO" id="NaM-Mq-qk9">
                                                                        <rect key="frame" x="15" y="15" width="20" height="20"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="width" constant="20" id="UVa-F2-gd0"/>
                                                                            <constraint firstAttribute="height" constant="20" id="ZJp-pR-IdC"/>
                                                                        </constraints>
                                                                    </imageView>
                                                                    <textField opaque="NO" contentMode="scaleToFill" enabled="NO" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="Address" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="4sh-Pi-QVK" customClass="CustomTextfieldWithFontStyle" customModule="TopCustomer" customModuleProvider="target">
                                                                        <rect key="frame" x="60" y="0.0" width="230" height="50"/>
                                                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                        <textInputTraits key="textInputTraits" autocapitalizationType="words" autocorrectionType="no"/>
                                                                        <connections>
                                                                            <outlet property="delegate" destination="JU9-z8-eTw" id="Cuh-u8-EyR"/>
                                                                        </connections>
                                                                    </textField>
                                                                </subviews>
                                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="bottom" secondItem="4sh-Pi-QVK" secondAttribute="bottom" id="0zm-cM-7GL"/>
                                                                    <constraint firstAttribute="bottom" secondItem="KMX-rv-C9x" secondAttribute="bottom" id="1vz-Xq-5c6"/>
                                                                    <constraint firstItem="4sh-Pi-QVK" firstAttribute="top" secondItem="dc3-Aq-nUy" secondAttribute="top" id="7i4-2t-isX"/>
                                                                    <constraint firstAttribute="trailing" secondItem="4sh-Pi-QVK" secondAttribute="trailing" constant="15" id="CRZ-Mz-nr8"/>
                                                                    <constraint firstItem="NaM-Mq-qk9" firstAttribute="leading" secondItem="dc3-Aq-nUy" secondAttribute="leading" constant="15" id="DI4-j6-bSI"/>
                                                                    <constraint firstItem="KMX-rv-C9x" firstAttribute="top" secondItem="dc3-Aq-nUy" secondAttribute="top" id="Jfp-oG-0hY"/>
                                                                    <constraint firstItem="4sh-Pi-QVK" firstAttribute="leading" secondItem="NaM-Mq-qk9" secondAttribute="trailing" constant="25" id="KPt-ba-3Fm"/>
                                                                    <constraint firstAttribute="height" constant="50" id="Q7f-rD-VIP"/>
                                                                    <constraint firstItem="KMX-rv-C9x" firstAttribute="leading" secondItem="dc3-Aq-nUy" secondAttribute="leading" id="Rm2-p8-vZb"/>
                                                                    <constraint firstAttribute="trailing" secondItem="KMX-rv-C9x" secondAttribute="trailing" id="bvd-aV-kY1"/>
                                                                    <constraint firstItem="NaM-Mq-qk9" firstAttribute="centerY" secondItem="dc3-Aq-nUy" secondAttribute="centerY" id="kYx-QJ-GnM"/>
                                                                </constraints>
                                                            </view>
                                                        </subviews>
                                                    </stackView>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="7sO-ne-gtc">
                                                        <rect key="frame" x="0.0" y="150" width="305" height="40"/>
                                                        <subviews>
                                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="WNS-or-ANE" customClass="MaterialLocalizeButton" customModule="TopCustomer" customModuleProvider="target">
                                                                <rect key="frame" x="0.0" y="2.5" width="35" height="35"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="35" id="aDp-7l-vhU"/>
                                                                    <constraint firstAttribute="width" constant="35" id="jX1-eQ-ld2"/>
                                                                </constraints>
                                                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                                <state key="normal" image="untick"/>
                                                                <state key="selected" image="tick_new"/>
                                                                <connections>
                                                                    <action selector="btnCheckUncheckClicked:" destination="JU9-z8-eTw" eventType="touchUpInside" id="7B7-7f-9Lr"/>
                                                                </connections>
                                                            </button>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="I agree with Privacy Policy" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="VM8-cp-mzM" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                                <rect key="frame" x="35" y="5" width="260" height="30"/>
                                                                <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="13"/>
                                                                <color key="textColor" name="AppTheme_LightGrayColor_#A0A0A0"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="ofw-ht-oOZ">
                                                                <rect key="frame" x="35" y="5" width="260" height="30"/>
                                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                                <connections>
                                                                    <action selector="privacyPolicyClicked:" destination="JU9-z8-eTw" eventType="touchUpInside" id="fOQ-Rb-YI6"/>
                                                                </connections>
                                                            </button>
                                                        </subviews>
                                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                        <constraints>
                                                            <constraint firstItem="ofw-ht-oOZ" firstAttribute="leading" secondItem="VM8-cp-mzM" secondAttribute="leading" id="0yS-Dh-Sbo"/>
                                                            <constraint firstItem="ofw-ht-oOZ" firstAttribute="trailing" secondItem="VM8-cp-mzM" secondAttribute="trailing" id="2zc-UC-5f0"/>
                                                            <constraint firstItem="VM8-cp-mzM" firstAttribute="leading" secondItem="WNS-or-ANE" secondAttribute="trailing" id="HIv-Ni-Jfi"/>
                                                            <constraint firstItem="VM8-cp-mzM" firstAttribute="centerY" secondItem="WNS-or-ANE" secondAttribute="centerY" id="KvI-5S-FIJ"/>
                                                            <constraint firstAttribute="height" constant="40" id="LvG-pE-tVh"/>
                                                            <constraint firstItem="ofw-ht-oOZ" firstAttribute="bottom" secondItem="VM8-cp-mzM" secondAttribute="bottom" id="Pdv-i9-dXp"/>
                                                            <constraint firstItem="WNS-or-ANE" firstAttribute="centerY" secondItem="7sO-ne-gtc" secondAttribute="centerY" id="bkN-dM-wPz"/>
                                                            <constraint firstAttribute="trailing" secondItem="VM8-cp-mzM" secondAttribute="trailing" constant="10" id="iIL-pf-old"/>
                                                            <constraint firstItem="ofw-ht-oOZ" firstAttribute="top" secondItem="VM8-cp-mzM" secondAttribute="top" id="nrm-Wm-Xde"/>
                                                            <constraint firstItem="WNS-or-ANE" firstAttribute="leading" secondItem="7sO-ne-gtc" secondAttribute="leading" id="uOz-6E-NhV"/>
                                                        </constraints>
                                                    </view>
                                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="ORb-gA-gZK" customClass="CustomRoundedButtton" customModule="TopCustomer" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="215" width="305" height="40"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="40" id="WJc-cP-XYF"/>
                                                        </constraints>
                                                        <state key="normal" title="Sign in"/>
                                                        <connections>
                                                            <action selector="loginClicked:" destination="JU9-z8-eTw" eventType="touchUpInside" id="onA-LU-wRA"/>
                                                        </connections>
                                                    </button>
                                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="WeC-AB-9VP">
                                                        <rect key="frame" x="0.0" y="280" width="305" height="40"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="40" id="53V-Gd-97Z"/>
                                                        </constraints>
                                                        <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="13"/>
                                                        <state key="normal" title="Skip">
                                                            <color key="titleColor" white="0.33333333333333331" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        </state>
                                                        <connections>
                                                            <action selector="loginClicked:" destination="JU9-z8-eTw" eventType="touchUpInside" id="9FW-KY-X3d"/>
                                                            <action selector="skipNowAction:" destination="JU9-z8-eTw" eventType="touchUpInside" id="CcE-M5-zQy"/>
                                                        </connections>
                                                    </button>
                                                </subviews>
                                            </stackView>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstAttribute="trailing" secondItem="uAO-p2-X2O" secondAttribute="trailing" constant="35" id="1KS-yk-ObS"/>
                                            <constraint firstItem="uAO-p2-X2O" firstAttribute="leading" secondItem="Zga-kM-CMr" secondAttribute="leading" constant="35" id="Jhu-X6-UGp"/>
                                            <constraint firstItem="uAO-p2-X2O" firstAttribute="top" secondItem="Zga-kM-CMr" secondAttribute="top" constant="20" id="UNh-J2-aP5"/>
                                            <constraint firstItem="uAO-p2-X2O" firstAttribute="top" secondItem="Zga-kM-CMr" secondAttribute="top" constant="20" id="XL2-e1-MNG"/>
                                            <constraint firstAttribute="height" constant="391" id="xwI-sT-Aep"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="bottom" secondItem="Zga-kM-CMr" secondAttribute="bottom" id="1TI-nM-MYQ"/>
                                    <constraint firstItem="Zga-kM-CMr" firstAttribute="width" secondItem="1fW-aY-t5f" secondAttribute="width" id="2B2-2F-NXA"/>
                                    <constraint firstAttribute="trailing" secondItem="Zga-kM-CMr" secondAttribute="trailing" id="Bg3-81-js8"/>
                                    <constraint firstItem="Zga-kM-CMr" firstAttribute="top" secondItem="1fW-aY-t5f" secondAttribute="top" id="Fsn-dO-V5x"/>
                                    <constraint firstItem="Zga-kM-CMr" firstAttribute="leading" secondItem="1fW-aY-t5f" secondAttribute="leading" id="GGu-Uo-Wx2"/>
                                    <constraint firstItem="Zga-kM-CMr" firstAttribute="top" secondItem="1fW-aY-t5f" secondAttribute="top" id="dQ2-g0-rAX"/>
                                </constraints>
                            </scrollView>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Lpg-6X-zCg" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                <rect key="frame" x="20" y="985" width="335" height="0.0"/>
                                <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="14"/>
                                <color key="textColor" white="0.33333333329999998" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <nil key="highlightedColor"/>
                            </label>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="2A4-wn-hNE"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="1fW-aY-t5f" firstAttribute="centerX" secondItem="hfO-x8-5Q1" secondAttribute="centerX" id="55C-dm-i14"/>
                            <constraint firstItem="1fW-aY-t5f" firstAttribute="top" secondItem="BGm-MQ-gu0" secondAttribute="bottom" constant="60" id="CuY-MD-zYn"/>
                            <constraint firstItem="BGm-MQ-gu0" firstAttribute="top" secondItem="2A4-wn-hNE" secondAttribute="top" constant="150" id="RKH-IF-efD"/>
                            <constraint firstItem="Lpg-6X-zCg" firstAttribute="top" secondItem="1fW-aY-t5f" secondAttribute="bottom" constant="20" id="TbT-Bm-pQ7"/>
                            <constraint firstItem="1fW-aY-t5f" firstAttribute="leading" secondItem="2A4-wn-hNE" secondAttribute="leading" id="bAp-hE-4uc"/>
                            <constraint firstItem="BGm-MQ-gu0" firstAttribute="centerX" secondItem="2A4-wn-hNE" secondAttribute="centerX" id="cUt-2h-ZQN"/>
                            <constraint firstItem="2A4-wn-hNE" firstAttribute="bottom" secondItem="Lpg-6X-zCg" secondAttribute="bottom" constant="15" id="cWz-rX-ewP"/>
                            <constraint firstItem="2A4-wn-hNE" firstAttribute="trailing" secondItem="1fW-aY-t5f" secondAttribute="trailing" id="i7a-40-6uI"/>
                            <constraint firstItem="2A4-wn-hNE" firstAttribute="trailing" secondItem="Lpg-6X-zCg" secondAttribute="trailing" constant="20" id="mGW-8w-ABk"/>
                            <constraint firstItem="Lpg-6X-zCg" firstAttribute="leading" secondItem="2A4-wn-hNE" secondAttribute="leading" constant="20" id="nza-Dc-Gei"/>
                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" id="fF7-dn-8kY"/>
                    <size key="freeformSize" width="375" height="1000"/>
                    <connections>
                        <outlet property="btnCheckUncheck" destination="WNS-or-ANE" id="YUY-uV-iO5"/>
                        <outlet property="btnCountryCode" destination="ImK-bn-z1w" id="sM6-eN-Ilb"/>
                        <outlet property="btnSelectCity" destination="KMX-rv-C9x" id="gzY-TJ-H5J"/>
                        <outlet property="btnSignIn" destination="ORb-gA-gZK" id="Xvz-58-ngs"/>
                        <outlet property="btnSkipNow" destination="WeC-AB-9VP" id="LuT-mR-9k1"/>
                        <outlet property="lblIAgree" destination="VM8-cp-mzM" id="Lwv-De-WFj"/>
                        <outlet property="lblVersion" destination="Lpg-6X-zCg" id="vFa-yC-tsU"/>
                        <outlet property="txtName" destination="ft1-UZ-UMl" id="AV1-qa-Rr5"/>
                        <outlet property="txtPhoneNo" destination="kdv-sj-Bds" id="1tF-hA-msA"/>
                        <outlet property="txtSelectCity" destination="4sh-Pi-QVK" id="6mB-0b-S1X"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="tiO-wU-Sp1" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1102" y="-188"/>
        </scene>
        <!--Verification View Controller-->
        <scene sceneID="vQI-kE-DfU">
            <objects>
                <viewController storyboardIdentifier="OTPVerificationViewController" useStoryboardIdentifierAsRestorationIdentifier="YES" id="ugl-40-bhS" customClass="OTPVerificationViewController" customModule="TopCustomer" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="8x5-7W-YBq">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" translatesAutoresizingMaskIntoConstraints="NO" id="24s-ID-fNl">
                                <rect key="frame" x="0.0" y="20" width="375" height="647"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="yjv-n3-qiA" customClass="MaterialLocalizeButton" customModule="TopCustomer" customModuleProvider="target">
                                        <rect key="frame" x="25" y="20" width="35" height="35"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="35" id="GLm-5Z-kyj"/>
                                            <constraint firstAttribute="width" constant="35" id="i0A-H9-Nf9"/>
                                        </constraints>
                                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                        <state key="normal" image="icn_back"/>
                                        <connections>
                                            <action selector="actionBack:" destination="ugl-40-bhS" eventType="touchUpInside" id="FQK-qf-z5U"/>
                                        </connections>
                                    </button>
                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="logo_login_big" translatesAutoresizingMaskIntoConstraints="NO" id="o5D-B6-Sug">
                                        <rect key="frame" x="113" y="70" width="149" height="49"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="49" id="7uu-ey-xYs"/>
                                            <constraint firstAttribute="width" constant="149" id="skq-pk-f38"/>
                                        </constraints>
                                    </imageView>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="HELLO AHMED!" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="76D-Mp-zJG">
                                        <rect key="frame" x="54" y="221" width="267" height="32"/>
                                        <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="32"/>
                                        <color key="textColor" name="AppTheme_SelectedTabColor_#F4BA45"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="5" translatesAutoresizingMaskIntoConstraints="NO" id="yDB-ne-xBr">
                                        <rect key="frame" x="54" y="278" width="267" height="44"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Welcome to MATERIAL" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="OEv-9X-gU2">
                                                <rect key="frame" x="0.0" y="0.0" width="267" height="13"/>
                                                <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="13"/>
                                                <color key="textColor" name="AppTheme_GrayTextColor_#767676"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="please enter the veriﬁed code that sent to your phone no." textAlignment="center" lineBreakMode="wordWrap" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="0jN-Z3-lGd">
                                                <rect key="frame" x="0.0" y="18" width="267" height="26"/>
                                                <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="13"/>
                                                <color key="textColor" name="AppTheme_GrayTextColor_#767676"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                        </subviews>
                                    </stackView>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Y2Z-Wl-g0T" customClass="OTPFieldView" customModule="TopCustomer" customModuleProvider="target">
                                        <rect key="frame" x="54" y="349" width="267" height="55"/>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="55" id="OHs-JC-6du"/>
                                        </constraints>
                                    </view>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="03:49" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="RH1-zI-TOo">
                                        <rect key="frame" x="169" y="424" width="37.5" height="13"/>
                                        <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="13"/>
                                        <color key="textColor" name="AppTheme_BlueColor_#012CDA"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="ar4-vh-HKG">
                                        <rect key="frame" x="124.5" y="454" width="126" height="24"/>
                                        <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="12"/>
                                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                        <state key="normal" title="Send the code again">
                                            <color key="titleColor" name="AppTheme_GrayTextColor_#767676"/>
                                        </state>
                                        <connections>
                                            <action selector="actionSendCodeAgain:" destination="ugl-40-bhS" eventType="touchUpInside" id="Upm-Cz-QXz"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="sps-jA-GNp" customClass="CustomRoundedButtton" customModule="TopCustomer" customModuleProvider="target">
                                        <rect key="frame" x="35" y="535" width="305" height="40"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="40" id="eTc-6v-Mee"/>
                                        </constraints>
                                        <state key="normal" title="Verify"/>
                                        <connections>
                                            <action selector="actionVerify:" destination="ugl-40-bhS" eventType="touchUpInside" id="uvE-3d-vc1"/>
                                        </connections>
                                    </button>
                                </subviews>
                                <constraints>
                                    <constraint firstItem="sps-jA-GNp" firstAttribute="centerX" secondItem="24s-ID-fNl" secondAttribute="centerX" id="5Pc-zz-xep"/>
                                    <constraint firstItem="RH1-zI-TOo" firstAttribute="centerX" secondItem="24s-ID-fNl" secondAttribute="centerX" id="A8B-XA-9u3"/>
                                    <constraint firstItem="ar4-vh-HKG" firstAttribute="centerX" secondItem="24s-ID-fNl" secondAttribute="centerX" id="Eoz-tL-Cya"/>
                                    <constraint firstItem="76D-Mp-zJG" firstAttribute="centerX" secondItem="24s-ID-fNl" secondAttribute="centerX" id="FBU-lw-Lbu"/>
                                    <constraint firstItem="yDB-ne-xBr" firstAttribute="leading" secondItem="76D-Mp-zJG" secondAttribute="leading" id="GwO-AM-Rq8"/>
                                    <constraint firstItem="Y2Z-Wl-g0T" firstAttribute="trailing" secondItem="yDB-ne-xBr" secondAttribute="trailing" id="HoW-xY-Qvi"/>
                                    <constraint firstItem="yDB-ne-xBr" firstAttribute="trailing" secondItem="76D-Mp-zJG" secondAttribute="trailing" id="JYD-Q4-Jsw"/>
                                    <constraint firstItem="76D-Mp-zJG" firstAttribute="leading" secondItem="24s-ID-fNl" secondAttribute="leading" constant="54" id="KTY-9v-jX2"/>
                                    <constraint firstItem="sps-jA-GNp" firstAttribute="leading" secondItem="24s-ID-fNl" secondAttribute="leading" constant="35" id="Kts-kV-dYD"/>
                                    <constraint firstAttribute="trailing" secondItem="76D-Mp-zJG" secondAttribute="trailing" constant="54" id="N3X-kn-a8X"/>
                                    <constraint firstAttribute="trailing" secondItem="sps-jA-GNp" secondAttribute="trailing" constant="35" id="Oeu-kz-0Fg"/>
                                    <constraint firstItem="yjv-n3-qiA" firstAttribute="top" secondItem="24s-ID-fNl" secondAttribute="top" constant="20" id="RUC-x7-siU"/>
                                    <constraint firstAttribute="bottom" secondItem="sps-jA-GNp" secondAttribute="bottom" constant="20" id="WnI-Za-ZKU"/>
                                    <constraint firstItem="RH1-zI-TOo" firstAttribute="top" secondItem="Y2Z-Wl-g0T" secondAttribute="bottom" constant="20" id="Yd6-EM-IJ8"/>
                                    <constraint firstItem="o5D-B6-Sug" firstAttribute="top" secondItem="yjv-n3-qiA" secondAttribute="bottom" constant="15" id="asi-HS-8Mn"/>
                                    <constraint firstItem="76D-Mp-zJG" firstAttribute="top" secondItem="o5D-B6-Sug" secondAttribute="bottom" constant="102" id="ej1-WV-y3I"/>
                                    <constraint firstItem="ar4-vh-HKG" firstAttribute="top" secondItem="Y2Z-Wl-g0T" secondAttribute="bottom" constant="50" id="i1g-nG-SJP"/>
                                    <constraint firstItem="Y2Z-Wl-g0T" firstAttribute="top" secondItem="yDB-ne-xBr" secondAttribute="bottom" constant="27" id="lhi-ZP-o7s"/>
                                    <constraint firstItem="yjv-n3-qiA" firstAttribute="leading" secondItem="24s-ID-fNl" secondAttribute="leading" constant="25" id="msJ-jP-rn0"/>
                                    <constraint firstItem="yDB-ne-xBr" firstAttribute="top" secondItem="76D-Mp-zJG" secondAttribute="bottom" constant="25" id="qic-9U-PUw"/>
                                    <constraint firstItem="sps-jA-GNp" firstAttribute="top" secondItem="ar4-vh-HKG" secondAttribute="bottom" constant="57" id="sge-hU-sQh"/>
                                    <constraint firstItem="Y2Z-Wl-g0T" firstAttribute="leading" secondItem="yDB-ne-xBr" secondAttribute="leading" id="wnL-dO-T5J"/>
                                </constraints>
                            </scrollView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="mtT-Mu-gV6"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="mtT-Mu-gV6" firstAttribute="bottom" secondItem="24s-ID-fNl" secondAttribute="bottom" id="PvF-U7-5vc"/>
                            <constraint firstItem="o5D-B6-Sug" firstAttribute="centerX" secondItem="mtT-Mu-gV6" secondAttribute="centerX" id="aeG-Ot-Bpv"/>
                            <constraint firstItem="24s-ID-fNl" firstAttribute="top" secondItem="mtT-Mu-gV6" secondAttribute="top" id="cVN-Kz-Q86"/>
                            <constraint firstItem="mtT-Mu-gV6" firstAttribute="trailing" secondItem="24s-ID-fNl" secondAttribute="trailing" id="hUA-sP-TSG"/>
                            <constraint firstItem="24s-ID-fNl" firstAttribute="leading" secondItem="mtT-Mu-gV6" secondAttribute="leading" id="rPq-e3-vhC"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="btnSendCodeAgain" destination="ar4-vh-HKG" id="q0C-pr-wa2"/>
                        <outlet property="btnVerify" destination="sps-jA-GNp" id="ju4-JN-sdK"/>
                        <outlet property="lblPleaseEnter" destination="0jN-Z3-lGd" id="wHd-dc-544"/>
                        <outlet property="lblTimer" destination="RH1-zI-TOo" id="8A3-aT-XHF"/>
                        <outlet property="lblUserName" destination="76D-Mp-zJG" id="AsR-wT-ezv"/>
                        <outlet property="lblWelcomeTo" destination="OEv-9X-gU2" id="ZdA-58-oJC"/>
                        <outlet property="viewOTP" destination="Y2Z-Wl-g0T" id="aFj-r4-nqs"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="w3N-ki-gBv" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1791" y="-337"/>
        </scene>
        <!--Splash View Controller-->
        <scene sceneID="4kD-FK-MJD">
            <objects>
                <viewController id="waf-Ar-Qcn" customClass="SplashViewController" customModule="TopCustomer" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="aMp-xH-6Eg">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <viewLayoutGuide key="safeArea" id="MUS-jz-G5E"/>
                        <color key="backgroundColor" name="AppTheme_BlueColor_#012CDA"/>
                    </view>
                    <navigationItem key="navigationItem" id="jiy-we-jyi"/>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="F12-NS-MVf" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="282" y="-178"/>
        </scene>
    </scenes>
    <resources>
        <image name="icn_back" width="13.5" height="21.5"/>
        <image name="location-pin" width="24" height="24"/>
        <image name="logo_login_big" width="205" height="67.5"/>
        <image name="smartphone-call" width="25" height="25"/>
        <image name="tick_new" width="19.5" height="18.5"/>
        <image name="untick" width="19.5" height="18.5"/>
        <image name="user" width="19" height="19"/>
        <namedColor name="AppTheme_BlueColor_#012CDA">
            <color red="0.0039215686274509803" green="0.17254901960784313" blue="0.85490196078431369" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="AppTheme_BorderColor_#1F1F1F">
            <color red="0.12156862745098039" green="0.12156862745098039" blue="0.12156862745098039" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="AppTheme_GrayTextColor_#767676">
            <color red="0.46274509803921571" green="0.46274509803921571" blue="0.46274509803921571" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="AppTheme_LightGrayColor_#A0A0A0">
            <color red="0.62745098039215685" green="0.62745098039215685" blue="0.62745098039215685" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="AppTheme_SelectedTabColor_#F4BA45">
            <color red="0.95686274509803926" green="0.72941176470588232" blue="0.27058823529411763" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
