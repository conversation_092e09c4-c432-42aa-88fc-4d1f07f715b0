<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="20037" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" launchScreen="YES" useTraitCollections="YES" colorMatched="YES" initialViewController="qFX-3i-4Y3">
    <device id="retina4_7" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="20020"/>
        <capability name="Named colors" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--View Controller-->
        <scene sceneID="FoJ-yg-wi5">
            <objects>
                <viewController id="FYq-f8-jWJ" sceneMemberID="viewController">
                    <layoutGuides>
                        <viewControllerLayoutGuide type="top" id="prK-hv-2Pz"/>
                        <viewControllerLayoutGuide type="bottom" id="9iy-mw-4if"/>
                    </layoutGuides>
                    <view key="view" contentMode="scaleToFill" id="H0r-AU-oBk">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="main_logo" translatesAutoresizingMaskIntoConstraints="NO" id="uCm-Pl-hRL">
                                <rect key="frame" x="73" y="216" width="229" height="235.5"/>
                            </imageView>
                        </subviews>
                        <color key="backgroundColor" name="AppTheme_BlueColor_#012CDA"/>
                        <constraints>
                            <constraint firstItem="uCm-Pl-hRL" firstAttribute="centerY" secondItem="H0r-AU-oBk" secondAttribute="centerY" id="ECz-g6-XmO"/>
                            <constraint firstItem="uCm-Pl-hRL" firstAttribute="centerX" secondItem="H0r-AU-oBk" secondAttribute="centerX" id="p81-2w-SBv"/>
                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" id="rLL-p7-vh5"/>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="fnP-bn-NhW" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-132" y="507.79610194902551"/>
        </scene>
        <!--Navigation Controller-->
        <scene sceneID="nzh-ha-QmS">
            <objects>
                <navigationController navigationBarHidden="YES" id="qFX-3i-4Y3" sceneMemberID="viewController">
                    <navigationBar key="navigationBar" contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" id="09p-Yw-naj">
                        <autoresizingMask key="autoresizingMask"/>
                    </navigationBar>
                    <connections>
                        <segue destination="FYq-f8-jWJ" kind="relationship" relationship="rootViewController" id="O0i-Az-gKd"/>
                    </connections>
                </navigationController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="4qQ-Sl-a9f" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-876" y="508"/>
        </scene>
    </scenes>
    <resources>
        <image name="main_logo" width="228.5" height="235.5"/>
        <namedColor name="AppTheme_BlueColor_#012CDA">
            <color red="0.0039215686274509803" green="0.17254901960784313" blue="0.85490196078431369" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
    </resources>
</document>
