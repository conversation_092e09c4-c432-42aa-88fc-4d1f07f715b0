
import UIKit

protocol ScheduledOrdersPresentationProtocol {
    func apiCallForGetScheduledOrders(dictData:[String:Any], flagLoader: Bool)
    func apiResponseGetScheduledOrders(response:OrderListResponse?,error:Error?)

    func apiCallForCancelOrder(dictData:[String:Any])
    func apiResponseCancelOrder(response:CommonFields?,error:Error?)

}

class ScheduledOrdersPresenter: ScheduledOrdersPresentationProtocol {
    
    // MARK: Objects & Variables
    weak var viewControllerScheduledOrders: ScheduledOrdersProtocol?
    var interactorScheduledOrders: ScheduledOrdersInteractorProtocol?
    
    func apiCallForGetScheduledOrders(dictData:[String:Any], flagLoader: Bool) {
        interactorScheduledOrders?.apiCallForGetScheduledOrders(dictData: dictData, flagLoader: flagLoader)
    }

    func apiResponseGetScheduledOrders(response: OrderListResponse?, error: Error?) {
        if let vc = self.viewControllerScheduledOrders as? BaseViewController {
            DispatchQueue.main.async {
                vc.endRefresing()
            }
        }
        if let error = error  {
            viewControllerScheduledOrders?.displayAlert(string: error.localizedDescription)
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            //            viewController?.displayAlert(strTitle: "", strMessage: KeyMessages.kInvalidateResponse)
            return
        }
        
        if code == APIINVALIDAUTHORIZATIONCODE401 {
//            AppSingletonObj.goToLoginScreen(message: response.responseMessage ?? "")
            AppSingletonObj.displaySessionExpiredAlert(strMsg: response.responseMessage ?? "")
            return
        }

        if code == APICODE400 {
            viewControllerScheduledOrders?.displayAlert(string: response.responseMessage ?? "")
            return
        }
        
        guard let model = response.responseData,code == APISUCCESSCODE200  else {
            return
        }
        self.viewControllerScheduledOrders?.loadOrderList(model: model)
    }

    func apiCallForCancelOrder(dictData:[String:Any]) {
        interactorScheduledOrders?.apiCallForCancelOrder(dictData: dictData)
    }

    func apiResponseCancelOrder(response:CommonFields?,error:Error?) {
//        if let vc = self.viewControllerScheduledOrders as? BaseViewController {
//            DispatchQueue.main.async {
//                vc.endRefresing()
//            }
//        }
        if let error = error  {
            viewControllerScheduledOrders?.displayAlert(string: error.localizedDescription)
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            //            viewController?.displayAlert(strTitle: "", strMessage: KeyMessages.kInvalidateResponse)
            return
        }
        
        if code == APIINVALIDAUTHORIZATIONCODE401 {
//            AppSingletonObj.goToLoginScreen(message: response.responseMessage ?? "")
            AppSingletonObj.displaySessionExpiredAlert(strMsg: response.responseMessage ?? "")
            return
        }

        if code == APICODE400 {
            viewControllerScheduledOrders?.displayAlert(string: response.responseMessage ?? "")
            return
        }
        
//        guard let model = response.responseData,code == APISUCCESSCODE200  else {
//            return
//        }
        self.viewControllerScheduledOrders?.showAlertAndReload(strMessage: response.responseMessage ?? "")
    }

}
