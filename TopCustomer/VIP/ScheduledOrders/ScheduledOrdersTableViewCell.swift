
import UIKit

class ScheduledOrdersTableViewCell: UITableViewCell {

    @IBOutlet weak var lblOrderNoTitle: UILabel!
    @IBOutlet weak var lblOrderNoValue: UILabel!
    @IBOutlet weak var lblDateTitle: UILabel!
    @IBOutlet weak var lblDateValue: UILabel!
//    @IBOutlet weak var lblDelivered: UILabel!
    @IBOutlet weak var viewOuter: UIView!
    @IBOutlet weak var lblExpectedDeliveryTime: UILabel!

    
    override func awakeFromNib() {
        super.awakeFromNib()
        
        lblOrderNoTitle.text = "\(ObjKeymessages.kLABEL_ORDER_NO):"
        lblDateTitle.text = "\(ObjKeymessages.kLABEL_DELIVERY_DATE):"
//        lblDelivered.text = ObjKeymessages.kLABEL_DELIVERED
        lblExpectedDeliveryTime.text = ObjKeymessages.kLABEL_EXPECTED_DELIVERY_TIME
    }

    override func setSelected(_ selected: Bo<PERSON>, animated: <PERSON><PERSON>) {
        super.setSelected(selected, animated: animated)

        // Configure the view for the selected state
    }

}
