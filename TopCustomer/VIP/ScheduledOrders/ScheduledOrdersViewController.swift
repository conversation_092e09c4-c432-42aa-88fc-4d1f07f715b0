
import UIKit

protocol ScheduledOrdersProtocol: AnyObject {
    func displayAlert(string:String)
    func loadOrderList(model : OrderListResponseFields)
    func showAlertAndReload(strMessage :  String)
//    func displaySessionExpiredAlert(strMsg: String)

}

class ScheduledOrdersViewController: BaseViewController {

    // MARK: Objects & Variables
    var presenterScheduledOrders: ScheduledOrdersPresentationProtocol?

    private var totalCount: Int = 0
    private var offset: Int = 1
    var arrScheduledOrders: [OrderResponseFields] = []

    // MARK: IBOutlets
    @IBOutlet weak var tblScheduled: UITableView!
    @IBOutlet weak var lblNoOrderFound: UILabel!
    @IBOutlet weak var lblLooksLike: UILabel!
    @IBOutlet weak var btnGoShopping: CustomRoundedButtton!
    @IBOutlet weak var viewNoData: UIView!

    // MARK: Object lifecycle
    /*
    override init(nibName nibNameOrNil: String?, bundle nibBundleOrNil: Bundle?) {
        super.init(nibName: nibNameOrNil, bundle: nibBundleOrNil)
        setup()
    }
    */
    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
        setup()
    }
    
    // MARK: Setup
    
    private func setup() {
        let viewController = self
        let interactor = ScheduledOrdersInteractor()
        let presenter = ScheduledOrdersPresenter()
        
        //View Controller will communicate with only presenter
        viewController.presenterScheduledOrders = presenter
        
        //Presenter will communicate with Interector and Viewcontroller
        presenter.viewControllerScheduledOrders = viewController
        presenter.interactorScheduledOrders = interactor
        
        //Interactor will communucate with only presenter.
        interactor.presenterScheduledOrders = presenter
    }
    
    
    // MARK: View lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        self.setTexts()
        self.setupRefreshControlInTableView(tableView: tblScheduled)

    }
    
    override func viewWillAppear(_ animated: Bool) {
//        self.viewWillAppear(animated)
        addObservers()

        self.getScheduledOrders(flagLoader: true)
    }

    func addObservers() {
        removeObservers()
        NotificationCenter.default.addObserver(self, selector: #selector(openDetailScreen), name: NSNotification.Name(rawValue: "OpenScheduledOrderDetail"), object: nil)

    }

    @objc func openDetailScreen(_ notification: NSNotification) {
        if let strOrderId = (notification.object as? [String:Any]) {
            if let id = strOrderId["iOrderId"] as? String {
                let vc = ProductPopupStoryboard.instantiate(ScheduledOrderDetailViewController.self)
        //            vc.modalPresentationStyle = .fullScreen
                vc.modalPresentationStyle = .overFullScreen
                vc.modalTransitionStyle = .crossDissolve
                vc.orderId = Int(id) ?? 0
                vc.showCancelScheduledOrderPopup = { [weak self] () in
                    DispatchQueue.main.async {
                        
                        /*let alert = UIAlertController(title: AppName, message: ObjKeymessages.kMSG_CANCEL_ORDER_CONFIRMATION, preferredStyle: .alert)
                        let noButton = UIAlertAction(title: ObjKeymessages.kLABEL_NO, style: .default, handler: {(_ action: UIAlertAction) -> Void in
                        })
                        let yesButton = UIAlertAction(title: ObjKeymessages.kLABEL_YES, style: .default, handler: {(_ action: UIAlertAction) -> Void in
                            
                            var dictParam : [String:Any] = [:]
                            dictParam["iOrderId"] = Int(id) ?? 0
                            
                            if AppSingletonObj.isConnectedToNetwork(){
                                self?.presenterScheduledOrders?.apiCallForCancelOrder(dictData: dictParam)
                            }

                        })
                        alert.addAction(noButton)
                        alert.addAction(yesButton)
                        self?.present(alert, animated: true) {() -> Void in }*/
                        
                        /*let vc = settingsStoryboard.instantiate(CustomPopupAlertViewController.self)
                        vc.modalPresentationStyle = .overFullScreen
                        vc.modalTransitionStyle = .crossDissolve
                        vc.strDescription = ObjKeymessages.kMSG_CANCEL_ORDER_CONFIRMATION
                        vc.strLeftButtonTitle = ObjKeymessages.kLABEL_NO
                        vc.isNeedToSwitchButton = true
                        vc.strRightButtonTitle = ObjKeymessages.kLABEL_YES
                        vc.isCenterButton = false
                        vc.completion = { (index) in
                            if index == 0 {
                            }else{
                                var dictParam : [String:Any] = [:]
                                dictParam["iOrderId"] = Int(id) ?? 0
                                
                                if AppSingletonObj.isConnectedToNetwork(){
                                    self?.presenterScheduledOrders?.apiCallForCancelOrder(dictData: dictParam)
                                }

                            }
                        }
                        self?.presentVC(vc)*/
                        
                        AppSingleton.shared.showCustomPopUpWithYesNoButton(strButton1Title: ObjKeymessages.kLABEL_YES, strButton2Title: ObjKeymessages.kLABEL_NO, strMessage: ObjKeymessages.kMSG_CANCEL_ORDER_CONFIRMATION, showOnTopVC: false) { (isOk) in
                            if isOk == true {
                                var dictParam : [String:Any] = [:]
                                dictParam["iOrderId"] = Int(id) ?? 0
                                
                                if AppSingletonObj.isConnectedToNetwork(){
                                    self?.presenterScheduledOrders?.apiCallForCancelOrder(dictData: dictParam)
                                }
                            }
                        }
                    }
                }
                
                vc.showUpdateTransactionAlert = { [weak self] (strMsg) in
                    DispatchQueue.main.async {
                        /*let alert = UIAlertController(title: AppName, message: strMsg, preferredStyle: .alert)
                        let okButton = UIAlertAction(title: ObjKeymessages.kLABEL_OK, style: .default, handler: {(_ action: UIAlertAction) -> Void in
                        })
                        alert.addAction(okButton)
                        self?.present(alert, animated: true) {() -> Void in }*/
                        
                        AppSingleton.shared.showCustomPopUpWithOkButton(strButtonTitle: ObjKeymessages.kLABEL_OK, strMessage: strMsg, showOnTopVC: false) { (isOk) in
                        }
                    }
                }

                self.presentVC(vc)
            }
        }
    }

    func removeObservers() {
        NotificationCenter.default.removeObserver(self)
    }

    override func viewDidDisappear(_ animated: Bool) {
        super.viewDidDisappear(animated)
        NotificationCenter.default.removeObserver(self)
    }

    private func getScheduledOrders(flagLoader: Bool) {
        offset = 1
        var dictParam : [String:Any] = [:]
        dictParam["offset"] = offset

        if AppSingletonObj.isConnectedToNetwork(){
            self.presenterScheduledOrders?.apiCallForGetScheduledOrders(dictData: dictParam, flagLoader: flagLoader)
        }
    }

    private func setTexts() {
        lblNoOrderFound.text = ObjKeymessages.kLABEL_NO_ORDER_FOUND
        lblLooksLike.text = ObjKeymessages.kLABEL_NO_ORDER_MADE
        btnGoShopping.setTitle(ObjKeymessages.kLABEL_GO_SHOPPING, for: .normal)

    }

    @IBAction func btnGoShoppingAction(_ sender: Any) {
        /*for controller in self.navigationController!.viewControllers {
            if (controller is MainTabbarViewController) {
                let tmpController = controller as? MainTabbarViewController
                tmpController?.selectedIndex = 0
                self.navigationController?.popToViewController(controller, animated: false)
                break
            }
        }*/
        
        self.tabBarController?.selectedIndex = 0
        
    }

    override func didPullToRefresh(_ sender: UIRefreshControl) {
        if AppSingletonObj.isConnectedToNetwork(){
            self.tblScheduled.refreshControl?.beginRefreshing()
            offset = 1
            self.getScheduledOrders(flagLoader: false)
        }
        else {
            ez.runThisAfterDelay(seconds: 1.0) {
                self.endRefresing()
            }
        }
    }

}

extension ScheduledOrdersViewController : UITableViewDelegate, UITableViewDataSource{
    func numberOfSections(in tableView: UITableView) -> Int {
        return 1
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return arrScheduledOrders.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        guard let cell = tblScheduled.dequeueReusableCell(withIdentifier: CellIdentifiers.kSCHEDULED_ORDERS_CELL) as? ScheduledOrdersTableViewCell else{
            return UITableViewCell()
        }
        
        cell.lblOrderNoValue.text = arrScheduledOrders[indexPath.row].vOrderNumber
//        cell.lblDateValue.text = arrScheduledOrders[indexPath.row].tsOrderedAt
//        cell.lblDelivered.text = ObjKeymessages.kLABEL_ORDER_RECEIVED

        // convert date to desired format
        let strDate = arrScheduledOrders[indexPath.row].tsOrderedAt
        let strFormattedDate = strDate?.utcToLocal(dateStr: strDate ?? "")
        cell.lblDateValue.text = strFormattedDate

//        cell.lblExpectedDeliveryTime.text = "\(ObjKeymessages.kLABEL_EXPECTED_DELIVERY_TIME): 3:00 p.m. to 4:00 p.m."

        return cell
    }
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        let vc = ProductPopupStoryboard.instantiate(ScheduledOrderDetailViewController.self)
//            vc.modalPresentationStyle = .fullScreen
        vc.modalPresentationStyle = .overFullScreen
        vc.modalTransitionStyle = .crossDissolve
        vc.orderId = arrScheduledOrders[indexPath.row].iOrderId ?? 0
        vc.showCancelScheduledOrderPopup = { [weak self] () in
            DispatchQueue.main.async {
                
                /*let alert = UIAlertController(title: AppName, message: ObjKeymessages.kMSG_CANCEL_ORDER_CONFIRMATION, preferredStyle: .alert)
                let noButton = UIAlertAction(title: ObjKeymessages.kLABEL_NO, style: .default, handler: {(_ action: UIAlertAction) -> Void in
                })
                let yesButton = UIAlertAction(title: ObjKeymessages.kLABEL_YES, style: .default, handler: {(_ action: UIAlertAction) -> Void in
                    
                    var dictParam : [String:Any] = [:]
                    dictParam["iOrderId"] = self?.arrScheduledOrders[indexPath.row].iOrderId ?? 0
                    print(self?.arrScheduledOrders[indexPath.row].iOrderId ?? 0)
                    
                    if AppSingletonObj.isConnectedToNetwork(){
                        self?.presenterScheduledOrders?.apiCallForCancelOrder(dictData: dictParam)
                    }

                })
                alert.addAction(noButton)
                alert.addAction(yesButton)
                self?.present(alert, animated: true) {() -> Void in }*/
                
                /*let vc = settingsStoryboard.instantiate(CustomPopupAlertViewController.self)
                vc.modalPresentationStyle = .overFullScreen
                vc.modalTransitionStyle = .crossDissolve
                vc.strDescription = ObjKeymessages.kMSG_CANCEL_ORDER_CONFIRMATION
                vc.strLeftButtonTitle = ObjKeymessages.kLABEL_NO
                vc.isNeedToSwitchButton = true
                vc.strRightButtonTitle = ObjKeymessages.kLABEL_YES
                vc.isCenterButton = false
                vc.completion = { (index) in
                    if index == 0 {
                    }else{
                        var dictParam : [String:Any] = [:]
                        dictParam["iOrderId"] = self?.arrScheduledOrders[indexPath.row].iOrderId ?? 0
                        print(self?.arrScheduledOrders[indexPath.row].iOrderId ?? 0)
                        
                        if AppSingletonObj.isConnectedToNetwork(){
                            self?.presenterScheduledOrders?.apiCallForCancelOrder(dictData: dictParam)
                        }

                    }
                }
                self?.presentVC(vc)*/
                
                AppSingleton.shared.showCustomPopUpWithYesNoButton(strButton1Title: ObjKeymessages.kLABEL_YES, strButton2Title: ObjKeymessages.kLABEL_NO, strMessage: ObjKeymessages.kMSG_CANCEL_ORDER_CONFIRMATION, showOnTopVC: false) { (isOk) in
                    if isOk == true {
                        var dictParam : [String:Any] = [:]
                        dictParam["iOrderId"] = self?.arrScheduledOrders[indexPath.row].iOrderId ?? 0
                        print(self?.arrScheduledOrders[indexPath.row].iOrderId ?? 0)
                        
                        if AppSingletonObj.isConnectedToNetwork(){
                            self?.presenterScheduledOrders?.apiCallForCancelOrder(dictData: dictParam)
                        }
                    }
                }
            }
        }
        
        /*vc.dismissScreen = { [weak self] () in
            
            DispatchQueue.main.async {
                self?.popVC()
                self?.tabBarController?.selectedIndex = 3
            }
            
        }*/
        
        vc.showUpdateTransactionAlert = { [weak self] (strMsg) in
            DispatchQueue.main.async {
                /*let alert = UIAlertController(title: AppName, message: strMsg, preferredStyle: .alert)
                let okButton = UIAlertAction(title: ObjKeymessages.kLABEL_OK, style: .default, handler: {(_ action: UIAlertAction) -> Void in
                })
                alert.addAction(okButton)
                self?.present(alert, animated: true) {() -> Void in }*/
                
                AppSingleton.shared.showCustomPopUpWithOkButton(strButtonTitle: ObjKeymessages.kLABEL_OK, strMessage: strMsg, showOnTopVC: false) { (isOk) in
                }
            }
        }

        self.presentVC(vc)
    }
    
    func tableView(_ tableView: UITableView, willDisplay cell: UITableViewCell, forRowAt indexPath: IndexPath) {
        if indexPath.row == (self.arrScheduledOrders.count ) - 1  {
            if (self.arrScheduledOrders.count ) < totalCount {
                offset = offset + 1
                
                var dictParam : [String:Any] = [:]
                dictParam["offset"] = offset

                if AppSingletonObj.isConnectedToNetwork(){
                    self.presenterScheduledOrders?.apiCallForGetScheduledOrders(dictData: dictParam, flagLoader: false)
                }
            }
        }
    }

}

extension ScheduledOrdersViewController: ScheduledOrdersProtocol {
    func displayAlert(string: String) {
//        self.showAlert(title: AppName, message: string)
        AppSingleton.shared.showCustomPopUpWithOkButton(strButtonTitle: ObjKeymessages.kLABEL_OK, strMessage: string, showOnTopVC: false) { (isOk) in
        }
    }

    func loadOrderList(model : OrderListResponseFields) {
        self.endRefresing()

        self.totalCount = model.totalRecord ?? 0

        // emtpy the array if it is initial call
        if self.offset == 1 {
            self.arrScheduledOrders.removeAll()
        }
        self.arrScheduledOrders.append(contentsOf: model.orders ?? [])

        if self.arrScheduledOrders.count <= 0 {  // show no data view
            viewNoData.isHidden = false
            tblScheduled.isHidden = true
        }
        else {
            viewNoData.isHidden = true
            tblScheduled.isHidden = false
        }
        
        tblScheduled.reloadData()
    }

    func showAlertAndReload(strMessage :  String) {
//        showAlert(title: AppName, message: strMessage, buttonTitles: [ObjKeymessages.kLABEL_OK], highlightedButtonIndex: 0) { void in
        AppSingletonObj.showAlert(strMessage: strMessage)
            self.offset = 1
            var dictParam : [String:Any] = [:]
            dictParam["offset"] = self.offset
            if AppSingletonObj.isConnectedToNetwork(){
                self.presenterScheduledOrders?.apiCallForGetScheduledOrders(dictData: dictParam, flagLoader: true)
            }
//        }
    }

    /*func displaySessionExpiredAlert(strMsg: String) {
        let alert = UIAlertController(title: AppName, message: strMsg, preferredStyle: .alert)
        let okButton = UIAlertAction(title: ObjKeymessages.kLABEL_OK, style: .default, handler: {(_ action: UIAlertAction) -> Void in
            AppDel?.restartApp()
            self.dismiss(animated: true, completion: nil)
        })
        alert.addAction(okButton)
        self.present(alert, animated: true) {() -> Void in }
    }*/

}
