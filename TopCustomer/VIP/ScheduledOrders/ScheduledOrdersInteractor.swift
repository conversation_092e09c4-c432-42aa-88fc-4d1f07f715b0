
import UIKit

protocol ScheduledOrdersInteractorProtocol {
    func apiCallForGetScheduledOrders(dictData:[String:Any], flagLoader: Bool)
    func apiCallForCancelOrder(dictData:[String:Any])

}

protocol ScheduledOrdersDataStore {
    //{ get set }
}

class ScheduledOrdersInteractor: ScheduledOrdersInteractorProtocol, ScheduledOrdersDataStore {

    // MARK: Objects & Variables
    var presenterScheduledOrders: ScheduledOrdersPresentationProtocol?
 
    func apiCallForGetScheduledOrders(dictData:[String:Any], flagLoader: Bool) {
        
        if flagLoader == true {
            ActivityIndicator.shared.showCentralSpinner()
        }

        let authorization = getAuthorizationText()
        
        OrderAPI.getOrderList(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, iOrderType: OrderAPI.IOrderType_getOrderList._2, offset: Int(setDataInString(dictData["offset"] as AnyObject)) ?? 0) { data, error in
                ActivityIndicator.shared.hideCentralSpinner()
                self.presenterScheduledOrders?.apiResponseGetScheduledOrders(response: data, error: error)

        }
        
    }

    func apiCallForCancelOrder(dictData:[String:Any]) {
        
        ActivityIndicator.shared.showCentralSpinner()
        
        let authorization = getAuthorizationText()
        
        OrderAPI.cancelSchedule(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, iOrderId: Int(setDataInString(dictData["iOrderId"] as AnyObject)) ?? 0) { data, error in
            ActivityIndicator.shared.hideCentralSpinner()
            self.presenterScheduledOrders?.apiResponseCancelOrder(response: data, error: error)

        }
        
    }

}
