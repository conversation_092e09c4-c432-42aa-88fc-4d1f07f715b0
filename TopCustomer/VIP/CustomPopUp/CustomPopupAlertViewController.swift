
import UIKit

class CustomPopupAlertViewController: BaseViewController {

    var strDescription : String = ""
    var strCenterButtonTitle = ""
    var strLeftButtonTitle = ""
    var strRightButtonTitle = ""
    var isCenterButton : Bool = true
    var isNeedToSwitchButton : Bool = true
    var completion : ((Int) -> Void)?
    
//    @IBOutlet weak var lblTitle: UILabel!
    @IBOutlet weak var lblDescription: UILabel!
    @IBOutlet weak var btnCenterOk: UIButton!
    @IBOutlet weak var viewCenterButton: UIView!
    @IBOutlet weak var btnLeft: UIButton!
    @IBOutlet weak var btnRight: UIButton!
    @IBOutlet weak var viewMultipleButton: UIView!
    @IBOutlet weak var stackView: UIStackView!
    
    override func viewDidLoad() {
        super.viewDidLoad()

        self.setUI()
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        self.setUI()
    }
    
    override func viewWillLayoutSubviews() {
        super.viewWillLayoutSubviews()
        self.setUI()
    }
    
    func setUI() {
        
        btnCenterOk.setTitle(strCenterButtonTitle, for: .normal)
        btnLeft.setTitle(strLeftButtonTitle, for: .normal)
        btnRight.setTitle(strRightButtonTitle, for: .normal)
        
        lblDescription.text = strDescription
        if strDescription.isEmpty {
            lblDescription.isHidden = true
        }
        if isCenterButton {
            viewCenterButton.isHidden = false
            viewMultipleButton.isHidden = true
        }else{
            viewCenterButton.isHidden = true
            viewMultipleButton.isHidden = false
        }
        
        if isNeedToSwitchButton {
            let array = stackView.arrangedSubviews
            stackView.removeArrangedSubview(array[0])
            stackView.addArrangedSubview(array[0])
        }
    }

    @IBAction func btnCenterAction(_ sender: UIButton) { // if OK is pressed
        self.dismiss(animated: true) {
            ez.runThisInMainThread {
                self.completion?(0)
            }
        }
    }
    
    @IBAction func btnLeftAction(_ sender: UIButton) { // if NO is pressed
        self.dismiss(animated: true) {
            ez.runThisInMainThread {
                self.completion?(0)
            }
        }
    }
    
    @IBAction func btnRightAction(_ sender: UIButton) { // if YES is pressed
        
        self.dismiss(animated: true) {
            ez.runThisInMainThread {
                self.completion?(1)
            }
        }
    }
    
}

