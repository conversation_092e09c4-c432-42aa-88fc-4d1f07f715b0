
import UIKit

protocol StaticPageInteractorProtocol {
    func apiCallForContentPage(dictData : [String:Any])

}

protocol StaticPageDataStore {
    //{ get set }
}

class StaticPageInteractor: StaticPageInteractorProtocol, StaticPageDataStore {

    // MARK: Objects & Variables
    var presenterStaticPage: StaticPagePresentationProtocol?
 
    func apiCallForContentPage(dictData: [String : Any]) {
        ActivityIndicator.shared.showCentralSpinner()
        
        let authorization = getAuthorizationText()

        CommonAPI.contentPages(accept: AcceptParamForHeader, lang: CurrentAppLang, vSlug: setDataInString(dictData["vSlug"] as AnyObject), authorization: authorization, isTerm: 1) { data, error in
            self.presenterStaticPage?.apiResponseContentPage(response: data, error: error)
        }
            
    }

}
