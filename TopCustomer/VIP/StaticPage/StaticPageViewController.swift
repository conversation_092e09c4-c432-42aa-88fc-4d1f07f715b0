
import UIKit
import WebKit

protocol StaticPageProtocol: AnyObject {
    func displayAlert(string:String)
    func showDataInWebview(model : ContentPageResponseFields)

}

class StaticPageViewController: BaseViewController {

    // MARK: Objects & Variables
    var presenterStaticPage: StaticPagePresentationProtocol?

    // MARK: IBOutlets
    @IBOutlet weak var webView: WKWebView!
    @IBOutlet weak var lblTitle: UILabel!
    
    
    
    var strFromWhichScreen = ""

    // MARK: Object lifecycle
    /*
    override init(nibName nibNameOrNil: String?, bundle nibBundleOrNil: Bundle?) {
        super.init(nibName: nibNameOrNil, bundle: nibBundleOrNil)
        setup()
    }
    */
    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
        setup()
    }
    
    // MARK: Setup
    
    private func setup() {
        let viewController = self
        let interactor = StaticPageInteractor()
        let presenter = StaticPagePresenter()
        
        //View Controller will communicate with only presenter
        viewController.presenterStaticPage = presenter
        
        //Presenter will communicate with Interector and Viewcontroller
        presenter.viewControllerStaticPage = viewController
        presenter.interactorStaticPage = interactor
        
        //Interactor will communucate with only presenter.
        interactor.presenterStaticPage = presenter
    }
    
    
    // MARK: View lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        webView.scrollView.showsHorizontalScrollIndicator = false
        webView.scrollView.showsVerticalScrollIndicator = false
        
        var strSlug = ""
        if strFromWhichScreen == "PrivacyPolicy" {
            lblTitle.text = ObjKeymessages.kLABEL_PRIVACY_POLICY
            strSlug = SlugPrivacyPolicy
            self.setMixpanelEvent(strVc: "PrivacyPolicy", isStart: true)
        }
        else if strFromWhichScreen == "Terms" {
            lblTitle.text = ObjKeymessages.kLABEL_TERMS_OF_SERVICE
            strSlug = SlugTermsAndConditions
            self.setMixpanelEvent(strVc: "Terms", isStart: true)
        }

        if AppSingletonObj.isConnectedToNetwork(){
            var dictParam : [String:Any] = [:]
            dictParam["vSlug"] = strSlug
            presenterStaticPage?.apiCallForContentPage(dictData: dictParam)
        }
        
        
    }
    
    override func viewDidDisappear(_ animated: Bool) {
        super.viewDidDisappear(animated)
        if strFromWhichScreen == "PrivacyPolicy" {
            self.setMixpanelEvent(strVc: "PrivacyPolicy", isStart: false)
        }
        else if strFromWhichScreen == "Terms" {
            self.setMixpanelEvent(strVc: "Terms", isStart: false)
        }
    }

    //MARK: - Actions
    @IBAction func actionBack(_ sender: Any) {
        self.popVC()
    }

}

extension StaticPageViewController : WKNavigationDelegate {
    func webView(_ webView: WKWebView, didFailProvisionalNavigation navigation: WKNavigation!, withError error: Error) {
        ActivityIndicator.shared.hideCentralSpinner()
        print(error.localizedDescription)
    }
    
    func webView(_ webView: WKWebView, didStartProvisionalNavigation navigation: WKNavigation!) {
        print("Strat to load")
    }
    
    func webView(_ webView: WKWebView, didFinish navigation: WKNavigation!) {
        ActivityIndicator.shared.hideCentralSpinner()
        print("finish to load")
    }
}

extension StaticPageViewController: StaticPageProtocol {
    func displayAlert(string: String) {
//        self.showAlert(title: AppName, message: string)
        AppSingleton.shared.showCustomPopUpWithOkButton(strButtonTitle: ObjKeymessages.kLABEL_OK, strMessage: string, showOnTopVC: false) { (isOk) in
        }
    }
    
    func showDataInWebview(model: ContentPageResponseFields) {
        let headerString = "<header><meta name='viewport' content='width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no'></header>"
        webView.navigationDelegate = self
        webView.loadHTMLString(headerString + (model.txContent!), baseURL: nil)
    }
}
