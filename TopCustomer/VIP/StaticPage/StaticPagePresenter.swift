
import UIKit

protocol StaticPagePresentationProtocol {
    func apiCallForContentPage(dictData: [String : Any])
    func apiResponseContentPage(response:ContentPageResponse?,error:Error?)

}

class StaticPagePresenter: StaticPagePresentationProtocol {
    
    // MARK: Objects & Variables
    weak var viewControllerStaticPage: StaticPageProtocol?
    var interactorStaticPage: StaticPageInteractorProtocol?
    
    func apiCallForContentPage(dictData: [String : Any]) {
        interactorStaticPage?.apiCallForContentPage(dictData: dictData)
    }
    
    func apiResponseContentPage(response: ContentPageResponse?, error: Error?) {
        if let error = error  {
            viewControllerStaticPage?.displayAlert(string: error.localizedDescription)
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            //            viewController?.displayAlert(strTitle: "", strMessage: KeyMessages.kInvalidateResponse)
            return
        }
        
        if code == APICODE400 {
            viewControllerStaticPage?.displayAlert(string: response.responseMessage ?? "")
            return
        }
        
        guard let model = response.responseData,code == APISUCCESSCODE200  else {
            return
        }
        self.viewControllerStaticPage?.showDataInWebview(model: model)
    }

}
