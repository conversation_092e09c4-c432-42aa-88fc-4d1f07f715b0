
import UIKit

protocol GoToDetailScreenDelegate {
    func goToDetailScreen(obj: ProductResponseFields)
}

class HomeTableViewCell: UITableViewCell {

    @IBOutlet weak var lblTitle: UILabel!
    @IBOutlet weak var btnMore: UIButton!
    @IBOutlet weak var collectionView: UICollectionView!
    @IBOutlet weak var lblNoData: UILabel!
    @IBOutlet weak var btnLabelClick: UIButton!

    var arrProducts: [ProductResponseFields] = []
    var delegate : GoToDetailScreenDelegate?

    override func awakeFromNib() {
        super.awakeFromNib()
        self.setTexts()
        
        self.collectionView.registerCell(cell: ProductsCollectionViewCellSmall.self)
        self.collectionView.delegate = self
        self.collectionView.dataSource = self
    }

    override func setSelected(_ selected: Bool, animated: Bool) {
        super.setSelected(selected, animated: animated)

    }

    private func setTexts() {
        let string = ObjKeymessages.kLABEL_MORE
        let range = (string as NSString).range(of: ObjKeymessages.kLABEL_MORE)
        let attributedString = NSMutableAttributedString(string: string)
        attributedString.addAttribute(NSAttributedString.Key.underlineStyle, value: NSNumber(value: 1), range: range)
        attributedString.addAttribute(NSAttributedString.Key.underlineColor, value: UIColor.black, range: range)
        attributedString.addAttribute(NSAttributedString.Key.font, value: UIFont(name: Fonts.LoewNextArabicMedium, size: 14)!, range: range)
        attributedString.addAttribute(NSAttributedString.Key.foregroundColor, value: UIColor.black, range: range)
        btnMore.setAttributedTitle(attributedString, for: .normal)

        self.lblNoData.text = ObjKeymessages.kLABEL_NO_DATA_FOUND
    }
}

extension HomeTableViewCell: UICollectionViewDelegate, UICollectionViewDataSource, UICollectionViewDelegateFlowLayout {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return arrProducts.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeue(with: ProductsCollectionViewCellSmall.self, for: indexPath)
        cell.isAppadvertisement(value: arrProducts[indexPath.row].isAppAdvertisement == 1 ? false : true)
        cell.isQuantitiesDiscountHidden(value: arrProducts[indexPath.row].quantityDiscount == 1 ? false : true)
        cell.imgProduct.kf.indicatorType = .activity
        let url = URL(string: arrProducts[indexPath.row].vProductImage ?? "")
        cell.imgProduct.kf.setImage(with: url , placeholder: UIImage(named: "placeholder_product"))

        cell.lblProductName.text = "\(arrProducts[indexPath.row].name ?? "")"
        cell.lblPriceTitle.text = "\(arrProducts[indexPath.row].vProductUnit ?? "")"
        
        cell.lblPrice.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value:  "\(forTrailingZero(temp: arrProducts[indexPath.row].price ?? 0.0))")

        if arrProducts[indexPath.row].offertitle != nil && arrProducts[indexPath.row].offertitle != "" {
            cell.viewOfferTitle.isHidden = false
            cell.lblOfferTitle.text = "\("Offer".localized)"
        } else {
            cell.viewOfferTitle.isHidden = true
        }
        
        if arrProducts[indexPath.row].isLowQuantity == 0 {  // 0 - hide low quantity label
            cell.viewLowQuantity.isHidden = true
        }
        else {  // 1 - show low quantity label
            cell.viewLowQuantity.isHidden = false
        }
        
        // discount logic
        if arrProducts[indexPath.row].tiDiscountType == 1 {  // no discount
            cell.lblPrice.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: arrProducts[indexPath.row].price ?? 0.0))")
            cell.lblOldPrice.isHidden = true
            cell.lblOffer.isHidden = true
        }
        else {  // discount is there
            cell.lblPrice.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: arrProducts[indexPath.row].discountPrice ?? 0.0))")
            cell.lblOldPrice.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: arrProducts[indexPath.row].price ?? 0.0))", isDiscounted: true)
            cell.lblOldPrice.isHidden = false
            
            cell.lblOffer.isHidden = false
            cell.lblOffer.text = "( \(arrProducts[indexPath.row].discountPrice ?? 0.0)% \(ObjKeymessages.kLABEL_OFF) )"
            let originalPrice = Double(arrProducts[indexPath.row].price ?? 0.0)
            let discountedPrice = Double(arrProducts[indexPath.row].discountPrice ?? 0.0)
            let a = originalPrice - discountedPrice
            let b = a / originalPrice
            let percent = b * 100
            cell.lblOffer.text = "(\(forTrailingZero(temp: percent.rounded(.up)))% \(ObjKeymessages.kLABEL_OFF))"
        }
        // discount logic ends here
        
        if arrProducts[indexPath.row].quantityDiscount ?? 1 == 0 {
            cell.stackViewDiscountPrice.isHidden = false
        } else {
            let priceMin = self.arrProducts[indexPath.row].prices?.map({$0.price?.toDouble() ?? 0.0}).min() ?? 0.0
            let priceMax = self.arrProducts[indexPath.row].prices?.map({$0.price?.toDouble() ?? 0.0}).max() ?? 0.0
            cell.lblPrice.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: priceMin)) - \(forTrailingZero(temp: priceMax))")
            cell.stackViewDiscountPrice.isHidden = true
        }
        
        return cell
        
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        let width = (collectionView.frame.width + 50) / 2.6 // Adjust for padding and number of columns
        return CGSize(width: width, height: 230) // Fixed height for items
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, minimumLineSpacingForSectionAt section: Int) -> CGFloat {
        return 0
    }
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        self.goToProductDetailScreen(obj: arrProducts[indexPath.row] )
    }
    
    private func goToProductDetailScreen(obj: ProductResponseFields) {
        delegate?.goToDetailScreen(obj: obj)
    }
    
}
