
import UIKit
import Alamofire
import Kingfisher
import STTabbar
import Lottie
import ActionSheetPicker_3_0
import TikTokBusinessSDK
import FirebaseAnalytics
import SwiftUI

typealias CheckResultHandler = (Bool)->()

protocol NewHomeProtocol: AnyObject {
    func displayAlert(string:String)
    func refreshData(model : LatestHomeListingResponseFields)
    func refreshNewProducts(model : ProductListResponseFields)
    func saveUserSpecificData(model: UserDetailListingResponseFields)
    func showAdvertisementPopup(model: [AdvertisementResponseFields]?)
    func decideForSoftUpdateAlert(model: AppVersionResponseFields, strMsg: String)
    func getFreeDeliveryValue(value: String)
    func getPointsLevels(value: Int,
                         maxLevel: Int,
                         remainingPoint: Int,
                         rewardPoint: Int,
                         levelPoint: Int,
                         maxPoint: Int)
    func getCalculateUserPointsToSAR(model: CalculateUserPointsToSARResponseFields)
    func getPointsDescription(model: GetPointsDescriptionResponse)
    func displayAlertAndDismiss(string:String, cartCount: Int)
    func getSuperCategoriesData(model : SuperCategoryListResponse)
}

class NewHomeViewController: BaseViewController {

    // MARK: Objects & Variables
    var presenterHome: NewHomePresentationProtocol?

    // MARK: IBOutlets
    @IBOutlet weak var txtSearch: CustomTextfieldWithFontStyle!
    @IBOutlet weak var collCategories: UICollectionView!
    @IBOutlet weak var collNewProductsHeightConstraint: NSLayoutConstraint!
    @IBOutlet weak var lblCurrentAddress: UILabel!
    @IBOutlet weak var scrollView: UIScrollView!
    @IBOutlet weak var collNewProductsBottomConstraint: NSLayoutConstraint!
    @IBOutlet weak var lblNoDataProductList: UILabel!
    @IBOutlet weak var collProductList: UICollectionView!
    @IBOutlet weak var stackViewActivityIndicatorView: UIStackView!
    @IBOutlet weak var activityIndicatorView: UIActivityIndicatorView!
    @IBOutlet weak var cons_activityIndicatorView_height: NSLayoutConstraint!
    @IBOutlet weak var colCategoryLoader: UIView!
    @IBOutlet weak var tblSections: UITableView!
    @IBOutlet weak var cons_tblSections_height: NSLayoutConstraint!
    @IBOutlet weak var colSectionTableLoader: UIView!
    @IBOutlet weak var viewSpace: UIView!
    @IBOutlet weak var selectAddressIcon: UIImageView!
    @IBOutlet weak var lblFreeDeliveryTitle: UILabel!
    @IBOutlet weak var lblFreeDeliveryValue: UILabel!
    @IBOutlet weak var pagerView: FSPagerView! {
        didSet {
            self.pagerView.contentMode = UserDefaults.standard.isCurrentLanguageArabic() ? .right : .left
            self.pagerView.register(UINib(nibName: "FSPagerViewCell", bundle: nil), forCellWithReuseIdentifier: "FSPagerViewCell")
        }
    }
    
    @IBOutlet weak var pageControl: FSPageControl! {
        didSet {
            self.pageControl.contentHorizontalAlignment = .center
            self.pageControl.setFillColor(UIColor.AppTheme_BlueColor_012CDA, for: .selected)
            self.pageControl.hidesForSinglePage = true
        }
    }
    @IBOutlet weak var viewMyPointsShape: UIView!
    @IBOutlet weak var lblMypoints: UILabel!
    @IBOutlet weak var viewCoinsAnimation: AnimationView!
    @IBOutlet weak var loadingIndicatorView: UIActivityIndicatorView!
    @IBOutlet weak var collSuperCategories: UICollectionView!
    @IBOutlet weak var viewCollCategories: UIView!
    @IBOutlet weak var stackViewInviteFriends: UIStackView!
    @IBOutlet weak var lblInviteFriend: UILabel!
    @IBOutlet weak var collMainSlider: UICollectionView!
    // MARK: - Variables
    var arrBanners: [ListResponseFields] = []
    var arrCategories: [CategoryListResponseFields] = []
    var arrCategoriesLoaded: [CategoryListResponseFields] = []
    var arrProductList: [ProductResponseFields] = []
    var arrSections: [DynamicSectionResponseFields] = []
    var arrSectionsLoaded: [DynamicSectionResponseFields] = []
    var arrBundelsList: [BundelsListResponseFields] = []
    var arrSuperCategories: [SuperCategoryListResponseFields] = []
    
    private var totalCount: Int = 0
    private var offset: Int = 2
    private var totalPage: Int = 1

    var selectedCategoryIndex = -1
    var selectedSuperCategoryIndex = -1
    var colletionHeightObserver : NSKeyValueObservation?
    var isSyncing = false
    
    var tableHeightObserver : NSKeyValueObservation?
    var timer: Timer?
    var isFirstOpenDone = false
    var pageNo = 0
    var selectedCategoryID = 0
    var isLoadingMoreData = false
    var inviteGiftValue = ""
    var cites = CityModel.getData()
    var citySelected = CityModel()
    var actionSheetCitesPicker = ActionSheetStringPicker()
    private lazy var floatingButtonView = FloatingButtonView.loadFromNib()
    
    // MARK: Object lifecycle
    /*
    override init(nibName nibNameOrNil: String?, bundle nibBundleOrNil: Bundle?) {
        super.init(nibName: nibNameOrNil, bundle: nibBundleOrNil)
        setup()
    }
    */
    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
        setup()
    }
    
    // MARK: Setup
    
    private func setup() {
        let viewController = self
        let interactor = NewHomeInteractor()
        let presenter = NewHomePresenter()
        
        //View Controller will communicate with only presenter
        viewController.presenterHome = presenter
        
        //Presenter will communicate with Interector and Viewcontroller
        presenter.viewControllerHome = viewController
        presenter.interactorHome = interactor
        
        //Interactor will communucate with only presenter.
        interactor.presenterHome = presenter
    }
    
    
    // MARK: View lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setlocalized()
        setupWhatsAppView()
        delay(5.0) {
            self.manageVersionSoftUpdate()
        }

        self.callHomeScreenAPI(flagLoader: false)
        
        self.checkForNotificationPermission()

        // call API to get Advertisements
        if User.shared.checkUserLoginStatus() {
            self.getAdvertisements()
        }

        self.setupRefreshControlInScrollView(scrollView: scrollView)

        self.setupUI()
        self.setTexts()

        colletionHeightObserver = collProductList.observe(\.contentSize, changeHandler: { _, _ in
            guard self.collProductList.contentSize.height > 177 else{
                self.collNewProductsHeightConstraint.constant = self.collProductList.contentSize.height // 177
                return
            }
            self.collNewProductsHeightConstraint.constant = self.collProductList.contentSize.height
        })
        
        tableHeightObserver = tblSections.observe(\.contentSize, changeHandler: { _, _ in
            self.cons_tblSections_height.constant = self.tblSections.contentSize.height
        })
        // select address location
        let lblLocation = UITapGestureRecognizer(target: self, action: #selector(actionAddress(_:)))
        let iconLocation = UITapGestureRecognizer(target: self, action: #selector(actionAddress(_:)))
        lblCurrentAddress.isUserInteractionEnabled = true
        lblCurrentAddress.addGestureRecognizer(lblLocation)
        selectAddressIcon.isUserInteractionEnabled = true
        selectAddressIcon.addGestureRecognizer(iconLocation)
        self.presenterHome?.apiCallForGetFreeDeliveryValue()
        // update badge counter of cart
        NotificationCenter.default.addObserver(self, selector: #selector(self.getUserSpecificData), name: .GetUserSpecificData, object: nil)
        // check if get a friend invitation gift?
        if Constant.shared.IS_FRIEND_INVITATION_GIFT {
            self.inviteFriendGift()
        }
    }
    
    private func getData() {
        selectedSuperCategoryIndex = -1
        selectedCategoryIndex = -1
        self.collProductList.isHidden = true
        self.tblSections.isHidden = false
        self.arrCategories = self.arrCategoriesLoaded
        self.arrSections = self.arrSectionsLoaded
        collCategories.reloadData()
        self.scrollToItemAccordingToLanguage()
        tblSections.reloadData()
        reloadSuperCategoriesData { _ in
            DispatchQueue.main.async {
                self.collSuperCategories.reloadData()
            }
        }
        self.collNewProductsBottomConstraint.priority = .defaultHigh
        self.loadingIndicatorView.isHidden = true
    }
    
    private func reloadSuperCategoriesData(completion: CheckResultHandler? = nil) {
        var index = 0
        for _ in self.arrSuperCategories {
            self.arrSuperCategories[index].isSelectedItem = false
            index += 1
        }
        completion?(true)
    }
    
    private func setlocalized() {
        self.lblFreeDeliveryTitle.text = "Free Delivery".localized
        self.lblFreeDeliveryValue.text = "Coming Soon".localized
        self.lblInviteFriend.text = "invite_get_money".localized.replacingOccurrences(of: "%2d", with: "\(self.inviteGiftValue)")
    }
    
    private func setCitesData() {
        ActionSheetStringPicker.show(withTitle: "select_city".localized ,
                                     rows: self.cites.map{ $0.cityName.localized } ,
                                     initialSelection: 0,
                                     doneBlock: {[weak self] picker, index, value in
            guard let self = self else {return}
            picker?.title = "Done".localized
            if self.cites.count == 0 { return }
            if UserDefaults.standard.selectedCityID != self.cites[index].cityId {
                UserDefaults.standard.selectedCityID = self.cites[index].cityId
                self.lblCurrentAddress.text = self.cites[index].cityName
                self.citySelected = self.cites[index]
                self.clearCartAndReloadData()
            }
            return
        },
                                     cancel: { picker in
            picker?.title = "Cancel".localized
            return
        },
                                     origin: self.view)
    }
    
    private func clearCartAndReloadData() {
        self.startLoading()
        getData()
        if AppSingletonObj.isConnectedToNetwork() {
            offset = 1
            self.getCategories(categoryId: 0, flag1: true, flag2: true, flagLoader: true)
        }
        ez.runThisAfterDelay(seconds: 1.5) {
            self.endRefresing()
        }
    }
    
    func manageVersionSoftUpdate() {
        if AppSingletonObj.isConnectedToNetworkForCheckoutScreen(){
            self.presenterHome?.apiCallForSoftUpdate()
        }
    }

    private func checkVersionForSoftUpdate(obj: AppVersionResponseFields, strMsg: String) {
            
        let strAppVersion : String = (ez.appVersion ?? "").leaveFirstCharacterAndRemoveAll(strCharacter: ".")
        let strServerIOSVersion = obj.vVersionNumber?.leaveFirstCharacterAndRemoveAll(strCharacter: ".") ?? ""
        guard let appVersion = Float(strAppVersion),let serverIOSVersion = Float(strServerIOSVersion) else {
            return
        }
        
        if appVersion < serverIOSVersion {
            if obj.tiUpdateType == 1 { // Regular update
                self.openVersionPopup(flagRegular: true, strMsg: strMsg)
            }
            else if obj.tiUpdateType == 2 { // Force update
                self.openVersionPopup(flagRegular: false, strMsg: strMsg)
            }
        }
    }

    private func openVersionPopup(flagRegular: Bool, strMsg: String) {
        let vc = homeStoryboard.instantiate(VersionUpdateViewController.self)
        vc.modalPresentationStyle = .overFullScreen
        vc.modalTransitionStyle = .crossDissolve
        vc.strMsg = strMsg
        vc.flagRegular = flagRegular

        vc.navigateForUpdateApp = { [weak self] () in
            self?.openApplicationURL()
        }
        self.topMostViewController?.presentVC(vc)
    }
    
    func getFreeDeliveryValue(value: String) {
        debugPrint("getFreeDeliveryValue ==>", value)
        if value == "" {
            self.lblFreeDeliveryValue.text = "Coming Soon".localized
            Constant.shared.FREE_DELIVERY = 0.0
        } else {
            self.lblFreeDeliveryValue.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "+\(forTrailingZero(temp: value.toDouble() ?? 0.0))")
            Constant.shared.FREE_DELIVERY = value.toDouble() ?? 0.0
        }
    }
    
    private func openApplicationURL() {
        let strURL = APPLICATION_APPSTORE_URL
        guard let url = URL(string: strURL) else {
            return
        }
        if UIApplication.shared.canOpenURL(url) {
            UIApplication.shared.open(url, options: [:], completionHandler: nil)
        }
    }

    func checkForNotificationPermission() {
        UNUserNotificationCenter.current().requestAuthorization(options: [.alert, .sound, .badge]) {
            (granted, error) in
            print("Permission granted: \(granted)")
            guard granted else {
                // Check every three days ago if denied for push notification
                if UserDefaults.standard.allowPushNotificationsDate?.days(from: Date()) ?? 0 <= -3 {
                    self.showPermissionAlert()
                }
                return
            }
        }
    }

    func showPermissionAlert() {
        ez.runThisInMainThread {
            AppSingleton.shared.showCustomPopUpWithYesNoButton(strButton1Title: ObjKeymessages.kLABEL_ALLOW, strButton2Title: ObjKeymessages.kLABEL_CANCEL, strMessage: ObjKeymessages.kMSG_NOTIFICATION_PERMISSION, showOnTopVC: false) { (isOk) in
                if isOk == true {
                    self.gotoAppSettings()
                } else {
                    // set current date
                    UserDefaults.standard.allowPushNotificationsDate = Date()
                }
            }
        }
    }

    private func gotoAppSettings() {
        
        let url = UIApplication.appNotificationSettingsURL
        
        guard let settingsUrl = url else {
            return
        }
        
        if UIApplication.shared.canOpenURL(settingsUrl) {
            if #available(iOS 10.0, *) {
                UIApplication.shared.open(settingsUrl, options: [:], completionHandler: nil)
            } else {
                UIApplication.shared.openURL(settingsUrl)
                // Fallback on earlier versions
            }
        }
    }

    override func viewWillAppear(_ animated: Bool) {
        addObservers()
        self.setCurrentAddress()
        self.getCurrentLevelShape()
        self.getUserSpecificData()
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        self.presenterHome?.apiCalculateUserPointsToSAR(points: 0)
        self.presenterHome?.apiGetPointsDescription()
        self.tabBarController?.animateTabBarBadgeView()
        // stop animation of new offers after 10 sec
        DispatchQueue.main.asyncAfter(deadline: .now() + 10.0) {
            debugPrint("time is over")
            self.tabBarController?.tabBar.items?[1].badgeValue = nil
        }
        print(Constant.shared.SELECTED_WAREHOUSE_ID)
        if Constant.shared.SELECTED_WAREHOUSE_ID >= 1 {
            let vc = ProductPopupStoryboard.instantiate(AddAddressViewController.self)
            vc.modalPresentationStyle = .overFullScreen
            vc.isMustSelectMosque = false
            self.presentVC(vc)
        }
    }
    
    private func getCurrentLevelShape() {
        CoinsLevelsShape.shared.getPointsLevels() { level, isGetNextLevel in
            self.viewCoinsAnimation.stop()
            let path = Bundle.main.path(forResource: level,
                                        ofType: "json") ?? ""
            self.viewCoinsAnimation.animation = Animation.filepath(path)
            
            self.viewCoinsAnimation.contentMode = .scaleAspectFill
            self.viewCoinsAnimation.loopMode = .playOnce
            self.viewCoinsAnimation.play()
        }
    }
    
    override func viewDidDisappear(_ animated: Bool) {
        super.viewDidDisappear(animated)
        self.stopTimer()
    }
    
    private func callHomeScreenAPI(flagLoader: Bool) {
        selectedSuperCategoryIndex = -1
        selectedCategoryIndex = -1
        var flagCategory = false
        var flagNew = false
        
        if arrCategories.count <= 0 {
            flagCategory = true
        }
        if arrSections.count <= 0 {
            flagNew = true
        }

        // call API to get user specific data
        if User.shared.checkUserLoginStatus() {
            self.getUserSpecificData()
        }
        
        offset = 2
        self.getCategories(categoryId: 0, flag1: flagCategory, flag2: flagNew, flagLoader: flagLoader)
    }
    
    func showLoaderOn(_ loadingView: UIView, _ referenceView: UIView) {
        let lottieAnimationView = AnimationView(name: LoaderType.loading.rawValue)
        let size:CGFloat = 500 / 2
        lottieAnimationView.frame = CGRect(x: (loadingView.w - size)/2 , y: (loadingView.h - size)/2, width: size, height: size)
        lottieAnimationView.backgroundColor = .clear
        lottieAnimationView.cornerRadius = 10.0
        lottieAnimationView.contentMode = .scaleAspectFill
        
        lottieAnimationView.play()
        lottieAnimationView.loopMode = .loop
        
        lottieAnimationView.centerX = referenceView.centerX

        loadingView.addSubview(lottieAnimationView)
        loadingView.isHidden = false
    }

    func hideLoaderOn(_ loadingView: UIView) {
        loadingView.isHidden = true
        for subview in loadingView.subviews {
            autoreleasepool(invoking: { () -> () in
                if let loadingView = subview as? AnimationView {
                    loadingView.stop()
                    loadingView.removeFromSuperview()
                }
            })
        }
    }
    
    @objc private func getUserSpecificData() {
        if AppSingletonObj.isConnectedToNetwork(){
            self.presenterHome?.apiCallForGetUserSpecificData()
        }
    }
    private func getAdvertisements() {
        if AppSingletonObj.isConnectedToNetwork(){
            self.presenterHome?.apiCallForGetAdvertisements()
        }
    }
    
    @objc private func inviteFriendGift() {
        Constant.shared.IS_FRIEND_INVITATION_GIFT = false
        let vc = homeStoryboard.instantiate(InviteFriendsGiftVC.self)
        vc.modalPresentationStyle = .overFullScreen
        vc.modalTransitionStyle = .crossDissolve
        self.presentVC(vc)
    }

    func addObservers() {
        removeObservers()
        NotificationCenter.default.addObserver(self, selector: #selector(removeCartBadge), name: NSNotification.Name(rawValue: "RemoveCartBadge"), object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(openDetailScreen), name: NSNotification.Name(rawValue: "OpenProductDetailFromDeepLink"), object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(showRatingPopup), name: NSNotification.Name(rawValue: "OpenRatingPopupOnHome"), object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(openProductSearchScreen), name: NSNotification.Name(rawValue: "OpenProductSearchScreen"), object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(refreshHomeList), name: NSNotification.Name(rawValue: "RefreshHomeListing"), object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(openBannerInfoPopup), name: NSNotification.Name(rawValue: "OpenBannerInfoPopupOnHome"), object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(self.inviteFriendGift), name: .InviteFriends, object: nil)
    }

    @objc func refreshHomeList(_ notification: NSNotification) {
        selectedSuperCategoryIndex = -1
        selectedCategoryIndex = -1
        getData()
        self.collCategories.isHidden = false
        self.viewCollCategories.isHidden = false
    }
    
    @objc func openDetailScreen(_ notification: NSNotification) {
        if let strProductId = (notification.object as? [String:Any]) {
            guard let productId = strProductId["productId"] as? String else { return }
            debugPrint(productId)
            let vc = ProductPopupStoryboard.instantiate(ProductPopupViewController.self)
            vc.strFromWhichScreen = "CallAPI"
            vc.productId = Int.parse(from: productId) ?? 0
            vc.delegate = self
            vc.showYouMayLikePopup = { [weak self] (obj) in
                DispatchQueue.main.async {
                    self?.goToProductDetailScreen(obj: obj)
                }
            }
            self.topMostViewController?.presentVC(vc)
        }
    }

    @objc func showRatingPopup(_ notification: NSNotification) {
        if let strOrderId = (notification.object as? [String:Any]) {
            if let id = strOrderId["iOrderId"] as? String {
                print(id)
                
                if !(self.topMostViewController is RatingViewController) { // Show popup
                    let vc = settingsStoryboard.instantiate(RatingViewController.self)
                    vc.modalPresentationStyle = .overFullScreen
                    vc.modalTransitionStyle = .crossDissolve
                    vc.strFromWhichScreen = "Home"
                    vc.orderId = Int(id) ?? 0
                    self.topMostViewController?.presentVC(vc)
                }

            }
        }
    }

    @objc func openBannerInfoPopup(_ notification: NSNotification) {
        if let strBannerId = (notification.object as? [String:Any]) {
            if let id = strBannerId["bannerId"] as? String {
                let vc = ProductPopupStoryboard.instantiate(BannerInfoViewController.self)
                vc.modalPresentationStyle = .overFullScreen
                vc.modalTransitionStyle = .crossDissolve
                vc.bannerId = Int(id) ?? 0
                vc.strFromWhichScreen = "FromDeepLink"
                self.topMostViewController?.presentVC(vc)
            }
        }
    }

    @objc func openProductSearchScreen(_ notification: NSNotification) {
        if let strCategoryId = (notification.object as? [String:Any]) {
            if let id = strCategoryId["categoryId"] as? String {
                print(id)
                
                var strName = ""
                if let name = strCategoryId["categoryName"] as? String {
                    strName = name
                }

                let vc = ProductPopupStoryboard.instantiate(FilterSearchViewController.self)
                vc.strFromWhichScreen = "SideMenu"
                vc.iCategoryId = Int(id) ?? 0
                vc.strCategoryName = strName
                vc.delegate = self
                self.pushVC(vc)

            }
        }
    }

    func removeObservers() {
        NotificationCenter.default.removeObserver(self)
    }

    @objc func removeCartBadge() {
        // update my cart button count
        if let myTabbar = self.tabBarController?.tabBar as? STTabbar {
            myTabbar.label?.isHidden = true
        }
    }
    
    private func setCurrentAddress() {
        if let address = UserDefaults.standard.object(forKey: USERDEFAULTS_INFO_KEY.CURRENT_ADDRESS) as? [String:Any],let data = address.data {
            let decodeResult: (decodableObj: AddressResponseFields?, error: Error?) = CodableHelper.decode(AddressResponseFields.self, from: data)
            if let decodeObj = decodeResult.decodableObj {
                lblCurrentAddress.text = decodeObj.vType
            }
            else {
                lblCurrentAddress.text = ObjKeymessages.kLABEL_SELECT_ADDRESS
            }
        }
        else {
            lblCurrentAddress.text = ObjKeymessages.kLABEL_SELECT_ADDRESS
        }
    }
    
    private func getCategories(categoryId: Int, flag1: Bool, flag2: Bool, flagLoader: Bool) {
        if AppSingletonObj.isConnectedToNetwork(){
            var dictParam : [String:Any] = [:]
            dictParam["iCategoryId"] = categoryId

            if flag1 == true {
                showLoaderOn(self.colCategoryLoader, self.view)
            }
            if flag2 == true {
                showLoaderOn(self.colSectionTableLoader, self.view)
            }

            self.presenterHome?.apiCallForGetCategories(dictData: dictParam, flagLoader: flagLoader)
        }
    }

    func setupUI() {
        self.pageControl.numberOfPages = arrBanners.count
        self.pagerView.itemSize = CGSize(width: (self.pagerView.frame.size.width - 20) / 3, height: 150)
        self.collCategories.registerCell(cell: CategoriesCollectionViewCell.self)
        self.collSuperCategories.registerCell(cell: SuperCollectionViewCell.self)
        self.collMainSlider.registerCell(cell: MainSliderCVCell.self)
        self.collCategories.delegate = self
        self.collCategories.dataSource = self
        self.collSuperCategories.delegate = self
        self.collSuperCategories.dataSource = self
        self.collMainSlider.delegate = self
        self.collMainSlider.dataSource = self
        
        self.collProductList.registerCell(cell: ProductsCollectionViewCellSmall.self)
        self.collProductList.delegate = self
        self.collProductList.dataSource = self
        self.tblSections.registerCell(cell: LastOfferTableViewCell.self)
        self.tblSections.registerCell(cell: BundelsListTableViewCell.self)
    }
    
    private func setTexts() {
        self.title = ObjKeymessages.kLABEL_HOME
        txtSearch.placeholder = ObjKeymessages.kLABEL_SEARCH_HERE
        lblNoDataProductList.text = "" //ObjKeymessages.kLABEL_NO_DATA_FOUND
        // hidden for now
//        self.lblCurrentAddress.text = CityModel.getCurrentCityName
    }
    
    //MARK: - Actions
    @IBAction func actionFilter(_ sender: Any) {
        
        let vc = ProductPopupStoryboard.instantiate(FilterSideMenuViewController.self)
        vc.modalPresentationStyle = .overFullScreen
        vc.modalTransitionStyle = .crossDissolve
        vc.goToSearchProductScreen = { [weak self] (categoryId, categoryName) in
            DispatchQueue.main.async {
                let vc = ProductPopupStoryboard.instantiate(FilterSearchViewController.self)
                vc.iCategoryId = categoryId
                vc.strCategoryName = categoryName
                vc.strFromWhichScreen = "SideMenu"
                vc.delegate = self
                self?.pushVC(vc)
            }
        }
        
        self.presentVC(vc)
    }
        
    @IBAction func actionAddress(_ sender: Any) {
        if !User.shared.checkUserLoginStatus() {
            self.showNoLoginUserAlert(complation: nil)
            return
        }

        let vc = settingsStoryboard.instantiate(SelectLocationViewController.self)
        vc.delegate = self
        self.presentVC(vc)
        // hidden for now
//        self.setCitesData()
    }

    @IBAction func goToSearchScreen(_ sender: Any) {
        let vc = ProductPopupStoryboard.instantiate(FilterSearchViewController.self)
        vc.strFromWhichScreen = "HomeSearch"
        vc.delegate = self
        self.pushVC(vc)
    }
    
    
    @IBAction func yourRewardsBtnTapped(_ sender: Any) {
        CoinsLevelsShape.shared.getPointsLevels() { _, _ in }
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            let vc = homeStoryboard.instantiate(YourRewardsVC.self) // YourRewardsVC.self // CongratsRewardsVC.self)
            vc.modalPresentationStyle = .overFullScreen
            self.presentVC(vc)
        }
    }
    
    @IBAction func giftBtnTapped(_ sender: Any) {
        let vc = homeStoryboard.instantiate(InviteFriendsVC.self) // InviteFriendsVC , InviteFriendsGiftVC
        vc.modalPresentationStyle = .overFullScreen
        vc.modalTransitionStyle = .crossDissolve
        self.presentVC(vc)
    }
    
    func runTimer() {
        timer = Timer.scheduledTimer(timeInterval: 0.1, target: self, selector: (#selector(updateTimer)), userInfo: nil, repeats: false)
    }
    
    @objc func updateTimer() {
        if Constant.shared.SHAPE_SECONDS_TIMER_LOADING == 0 || Constant.shared.MY_POINTS_VALUE == 0 {
            Constant.shared.POINTS_LEVEL =  Constant.shared.MY_POINTS_VALUE
            CoinsLevelsShape.shared.getTotalUserPoints { total in
                self.lblMypoints.text = "\(total)"
            }
            self.stopTimer()
            return
        }
        Constant.shared.POINTS_LEVEL += 10
        self.lblMypoints.text = "\(Constant.shared.POINTS_LEVEL)"
        Constant.shared.SHAPE_SECONDS_TIMER_LOADING -= 1     //This will decrement(count down)the seconds.
    }
    
    func stopTimer() {
        DispatchQueue.main.async {
            self.timer?.invalidate()
            self.timer = nil
        }
    }
    
    override func didPullToRefresh(_ sender: UIRefreshControl) {
        if AppSingletonObj.isConnectedToNetwork(){
            offset = 2
            self.getCategories(categoryId: 0, flag1: false, flag2: false, flagLoader: false)
        }
        else {
            ez.runThisAfterDelay(seconds: 1.0) {
                self.endRefresing()
            }
        }
    }

    
    @objc func actionMore(sender: UIButton) {
        let vc = ProductPopupStoryboard.instantiate(FilterSearchViewController.self)
        if arrSections[sender.tag].products?.first?.categoryId == -1 {  // New Products
            vc.strFromWhichScreen = "MoreNewProducts"
        }
        else if arrSections[sender.tag].products?.first?.categoryId == -2 {  // Best Sellers
            vc.strFromWhichScreen = "MoreBestSellers"
        }
        else if arrSections[sender.tag].bundelsList?.first?.bunddelId != nil {  // Bundles
            vc.strFromWhichScreen = "DynamicHomeCategory"
            vc.isOnlyBundle = true
            vc.strCategoryName = "bundles".localized
        }
        else {  // particular category
            vc.strFromWhichScreen = "DynamicHomeCategory"
            vc.iCategoryId = arrSections[sender.tag].products?.first?.categoryId ?? 0
            vc.strCategoryName = arrSections[sender.tag].products?.first?.categoryName ?? ""
        }
        
        vc.delegate = self
        self.pushVC(vc)
    }
    
}

extension NewHomeViewController: UICollectionViewDelegate, UICollectionViewDataSource, UICollectionViewDelegateFlowLayout {
    
//    func numberOfSections(in collectionView: UICollectionView) -> Int {
//        if self.arrSuperCategories.count >= 1 {
//            return 2
//        } else {
//            return 1
//        }
//        
//    }
    
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        if collectionView == self.collSuperCategories {
            return self.arrSuperCategories.count
        } else if collectionView == self.collMainSlider {
            return arrBanners.count
        } else {
            if collectionView == self.collCategories {
                
                if (self.arrCategories.count == 0) {
//                    self.collCategories.setEmptyMessage("No data found".localized)
                  } else {
                      self.collCategories.restore()
                  }
                return self.arrCategories.count
            }
            else {  // product list
                return self.arrProductList.count
            }
        }
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        if collectionView == self.collSuperCategories {
            let cell = collectionView.dequeue(with: SuperCollectionViewCell.self, for: indexPath)
            if arrSuperCategories.count <= indexPath.row {
                collectionView.register(UICollectionViewCell.self, forCellWithReuseIdentifier: "default")
                return collectionView.dequeueReusableCell(withReuseIdentifier: "default", for: indexPath)
            }
            let url = URL(string: arrSuperCategories[indexPath.row].image ?? "")
            cell.imgViewCategory.kf.indicatorType = .activity
            cell.imgViewCategory.kf.setImage(with: url)
            cell.imgViewCategory.contentMode = .redraw
            cell.lblCategoryName.text = arrSuperCategories[indexPath.row].name
            if self.arrSuperCategories[indexPath.row].isSelectedItem == true {
                cell.setSelected()
            } else {
                cell.setUnselected()
            }
            return cell
        }
        else if collectionView == self.collMainSlider {
            let index = indexPath.row
            let cell = collectionView.dequeue(with: MainSliderCVCell.self, for: indexPath)
            cell.isAppadvertisement(value: arrBanners[index].isAppAdvertisement == 1 ? false : true)
            cell.imageView.kf.indicatorType = .activity
            let url = URL(string: arrBanners[index].vImage ?? "")
            cell.imageView.kf.setImage(with: url , placeholder: UIImage(named: "placeholder_banner"))
            cell.btnShare.tag = index
            cell.btnShare.addTarget(self, action: #selector(self.actionShareHomeBanner(sender:)), for: .touchUpInside)
            return cell
        }
        else if collectionView == self.collCategories {
            let cell = collectionView.dequeue(with: CategoriesCollectionViewCell.self, for: indexPath)
            if arrCategories.count <= indexPath.row {
                collectionView.register(UICollectionViewCell.self, forCellWithReuseIdentifier: "default")
                return collectionView.dequeueReusableCell(withReuseIdentifier: "default", for: indexPath)
            }
            cell.imgCategory.contentMode = .scaleAspectFit
            cell.isAppadvertisement(value: arrCategories[indexPath.row].isAppAdvertisement == 1 ? false : true)
            cell.imgCategory.kf.indicatorType = .activity
            let url = URL(string: arrCategories[indexPath.row].vImage ?? "")
            cell.imgCategory.kf.setImage(with: url , placeholder: UIImage(named: "placeholder_category"))

            cell.lblCategoryName.text = arrCategories[indexPath.row].vName
            
            // new changes
            if selectedCategoryIndex == indexPath.row {  // show category selected
                // apply blue border for selection
                cell.viewOuter.layer.borderWidth = 1.0
                cell.viewOuter.layer.borderColor = UIColor.AppTheme_BlueColor_012CDA.cgColor
            }
            else {
                cell.viewOuter.layer.borderWidth = 0.0
                cell.viewOuter.layer.borderColor = UIColor.clear.cgColor
            }
            
            return cell
        }
        else {  // product list while clicking on category
            let cell = collectionView.dequeue(with: ProductsCollectionViewCellSmall.self, for: indexPath)
            if arrProductList.count <= indexPath.row {
                collectionView.register(UICollectionViewCell.self, forCellWithReuseIdentifier: "default")
                return collectionView.dequeueReusableCell(withReuseIdentifier: "default", for: indexPath)
            }
            cell.isAppadvertisement(value: arrProductList[indexPath.row].isAppAdvertisement == 1 ? false : true)
            cell.isQuantitiesDiscountHidden(value: arrProductList[indexPath.row].quantityDiscount ?? 1 == 1 ? false : true)
            cell.imgProduct.kf.indicatorType = .activity
            let url = URL(string: arrProductList[indexPath.row].vProductImage ?? "")
            cell.imgProduct.kf.setImage(with: url , placeholder: UIImage(named: "placeholder_product"))

            cell.lblProductName.text = "\(arrProductList[indexPath.row].vProductName ?? "")"
            cell.lblPriceTitle.text = "\(arrProductList[indexPath.row].vProductUnit ?? "")"
            
            if arrProductList[indexPath.row].offertitle != nil && arrProductList[indexPath.row].offertitle != "" {
                cell.viewOfferTitle.isHidden = false
                cell.lblOfferTitle.text = "\("Offer".localized)"
            } else {
                cell.viewOfferTitle.isHidden = true
            }
            
            if arrProductList[indexPath.row].isLowQuantity == 0 {  // 0 - hide low quantity label
                cell.viewLowQuantity.isHidden = true
            }
            else {  // 1 - show low quantity label
                if arrProductList[indexPath.row].iHowMuchLeftInStock ?? 0 <= 0 {  // out of stock
                    cell.viewLowQuantity.isHidden = true
                }
                else {
                    cell.viewLowQuantity.isHidden = false
                }
            }
            
            // discount logic
            if arrProductList[indexPath.row].tiDiscountType == 1 {  // no discount
                cell.lblPrice.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: arrProductList[indexPath.row].price ?? 0.0))", isDiscounted: true)
                cell.lblOldPrice.isHidden = true
                cell.lblOffer.isHidden = true
            }
            else {  // discount is there
                cell.lblPrice.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: arrProductList[indexPath.row].discountPrice ?? 0.0))", isDiscounted: true)
                cell.lblOldPrice.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: arrProductList[indexPath.row].price ?? 0.0))", isDiscounted: true)
                
                let originalPrice = Double(arrProductList[indexPath.row].price ?? 0.0)
                let discountedPrice = Double(arrProductList[indexPath.row].discountPrice ?? 0.0)
                let a = originalPrice - discountedPrice
                let b = a / originalPrice
                let percent = b * 100
                cell.lblOffer.text = "(\(forTrailingZero(temp: percent.rounded(.up)))% \(ObjKeymessages.kLABEL_OFF))"
                
                cell.lblOldPrice.isHidden = false
                cell.lblOffer.isHidden = false
            }
            // discount logic ends here
            
            if arrProductList[indexPath.row].iHowMuchLeftInStock ?? 0 <= 0 {  // out of stock
                cell.viewOutOfStock.isHidden = false
            }
            else {
                cell.viewOutOfStock.isHidden = true
            }
            
            if arrProductList[indexPath.row].quantityDiscount ?? 1 == 0 {
                cell.stackViewDiscountPrice.isHidden = false
            } else {
                let priceMin = self.arrProductList[indexPath.row].prices?.map({$0.price?.toDouble() ?? 0.0}).min() ?? 0.0
                let priceMax = self.arrProductList[indexPath.row].prices?.map({$0.price?.toDouble() ?? 0.0}).max() ?? 0.0
                cell.lblPrice.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: priceMin)) - \(forTrailingZero(temp: priceMax))")
                cell.stackViewDiscountPrice.isHidden = true
            }
            
            return cell
        }

    }
    
    func collectionView(_ collectionView: UICollectionView, willDisplay cell: UICollectionViewCell, forItemAt indexPath: IndexPath) {
      if collectionView == self.collCategories {  // product list while clicking on category
            if indexPath.row <= arrProductList.count - 1 {
                if let cell = cell as? ProductsCollectionViewCellSmall {
                    if arrProductList[indexPath.row].iHowMuchLeftInStock ?? 0 <= 0 {  // out of stock
                        cell.isAppadvertisement(value: arrProductList[indexPath.row].isAppAdvertisement == 1 ? false : true)
                        cell.isQuantitiesDiscountHidden(value: arrProductList[indexPath.row].quantityDiscount ?? 1 == 1 ? false : true)
                        cell.imgProduct.kf.indicatorType = .activity
                        let url = URL(string: arrProductList[indexPath.row].vProductImage ?? "")
                        
                        cell.imgProduct.kf.setImage(with: url , placeholder: UIImage(named: "placeholder_product")) { result in
                            switch result {
                            case .success(let value):
                                print("Image: \(value.image). Got from: \(value.cacheType)")
                                cell.imgProduct.image = value.image.grayed
                            case .failure(let error):
                                print("Error: \(error)")
                            }
                        }
                        
                    }
                    else {
                        return
                    }
                }
            }
        }
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        if collectionView == self.collCategories {
            return CGSize(width: (self.collCategories.frame.width - 35) / 5, height: collCategories.h)
        } else if collectionView == self.collMainSlider {
            return CGSize(width: 110, height: 120)
        }
        else if collectionView == self.collSuperCategories {
            return CGSize(width: 150, height: 120)
        }
        else {  // product list
            return CGSize(width: (self.collProductList.frame.width - 20) / 3, height: 220)
        }

    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, minimumLineSpacingForSectionAt section: Int) -> CGFloat {
        if collectionView == self.collCategories || collectionView == self.collSuperCategories || collectionView == self.collMainSlider { // || collectionView == self.collSuperCategories
            return 5
        }
        else {  // product list
            return 0
        }
    }
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        if collectionView == self.collCategories { // load new product data accroding to selected category
            self.collNewProductsBottomConstraint.priority = .required
            self.selectedCategoryID = arrCategories[indexPath.row]._id ?? 0
            self.lblNoDataProductList.isHidden = true
            self.loadingIndicatorView.isHidden = false
            // get products for selected category from local array. Do not call API
            selectedCategoryIndex = indexPath.row
            arrProductList = self.arrCategories[indexPath.row].products ?? []
            collCategories.reloadData()
            collProductList.reloadData()
            offset = 1
            var dictParam : [String:Any] = [:]
            dictParam["iCategoryId"] = arrCategories[indexPath.row]._id
            dictParam["vSerchString"] = ""
            dictParam["offset"] = 1
            debugPrint(dictParam)
            self.pageNo = 1
            if AppSingletonObj.isConnectedToNetwork(){
                self.presenterHome?.apiCallForGetNewProducts(dictData: dictParam, flagLoader: false, pageNo: self.pageNo, isOnlyBundle: 0)
            }
        } else if collectionView == self.collMainSlider {
            let index = indexPath.row
            // manage navigation to category, product detail or offer detail
            if arrBanners[index].iProductCategoryId != -1 && arrBanners[index].iProductCategoryId != nil {  // category
                let vc = ProductPopupStoryboard.instantiate(FilterSearchViewController.self)
                vc.iCategoryId = arrBanners[index].iProductCategoryId ?? 0
                vc.strCategoryName = arrBanners[index].vCategoryName ?? ""
                vc.strFromWhichScreen = "Banner"
                vc.delegate = self
                self.pushVC(vc)
            }
            else if arrBanners[index].biProductId != -1 && arrBanners[index].biProductId != nil {  // product detail
                
                // new changes
                var flag = false
                for objCategory in self.arrCategories {
                    for objProduct in objCategory.products ?? [] {
                        if arrBanners[index].biProductId == objProduct.biProductId {
                            flag = true
                            let vc = ProductPopupStoryboard.instantiate(ProductPopupViewController.self)
                            vc.objProductDetails = objProduct
                            vc.arrMayLikeProducts = objProduct.youMayAlsoLikeProducts ?? []
                            vc.delegate = self
                            
                            vc.showYouMayLikePopup = { [weak self] (obj) in
                                DispatchQueue.main.async {
                                    self?.goToProductDetailScreen(obj: obj)
                                }
                            }

                            self.presentVC(vc)
                        }
                    }
                }
                
                if flag == false {  // Product not found, call API in detail screen
                    flag = true
                    let vc = ProductPopupStoryboard.instantiate(ProductPopupViewController.self)
                    vc.strFromWhichScreen = "CallAPI"
                    vc.productId = arrBanners[index].biProductId ?? 0
                    vc.delegate = self
                    
                    vc.showYouMayLikePopup = { [weak self] (obj) in
                        DispatchQueue.main.async {
                            self?.goToProductDetailScreen(obj: obj)
                        }
                    }

                    self.presentVC(vc)
                }
                
            }
            else if arrBanners[index].iOfferId != -1 && arrBanners[index].iOfferId != nil {  // offer detail
                
                if !User.shared.checkUserLoginStatus() {
                    self.showNoLoginUserAlert(complation: nil)
                    return
                }

                let vc = ProductPopupStoryboard.instantiate(OfferDetailsViewController.self)
                vc.modalPresentationStyle = .overFullScreen
                vc.modalTransitionStyle = .crossDissolve
                vc.offerId = arrBanners[index].iOfferId ?? 0
                vc.delegate = self
                self.presentVC(vc)
            }
            else if arrBanners[index].iProductCategoryId == nil && arrBanners[index].biProductId == nil && arrBanners[index].iOfferId == nil {  // info banner
                let vc = ProductPopupStoryboard.instantiate(BannerInfoViewController.self)
                vc.modalPresentationStyle = .overFullScreen
                vc.modalTransitionStyle = .crossDissolve
                vc.objBanner = arrBanners[index]
                self.presentVC(vc)
            }
        }
        else if collectionView == self.collSuperCategories {
            self.collNewProductsBottomConstraint.priority = .defaultHigh
            self.loadingIndicatorView.isHidden = false
            selectedCategoryIndex = -1
            if let cell = collectionView.cellForItem(at: indexPath) as? SuperCollectionViewCell {
                if self.arrSuperCategories[indexPath.row].isSelectedItem ?? false == false {
                    self.arrSuperCategories[indexPath.row].isSelectedItem = true
                    cell.setSelected()
                    self.getSelectedSuperCategoryDetails(indexPath: indexPath)
                } else {
                    self.arrSuperCategories[indexPath.row].isSelectedItem = false
                    self.getData()
                    cell.setUnselected()
                }
                selectedSuperCategoryIndex = indexPath.row
            }
        }
        else {  // product list
            self.goToProductDetailScreen(obj: arrProductList[indexPath.row])
        }
    }
    
    func collectionView(_ collectionView: UICollectionView, didDeselectItemAt indexPath: IndexPath) {
        if collectionView == self.collSuperCategories {
            self.selectedSuperCategoryIndex = indexPath.row
            if let cell = collectionView.cellForItem(at: indexPath) as? SuperCollectionViewCell {
                self.arrSuperCategories[indexPath.row].isSelectedItem = false
                cell.setUnselected()
            }
        }
    }
    
    private func getSelectedSuperCategoryDetails(indexPath: IndexPath) {
        offset = 1
        self.pageNo = 1
        var dictParam : [String:Any] = [:]
        dictParam["iSuperCategoryId"] = arrSuperCategories[indexPath.row]._id
        dictParam["pageNo"] = pageNo
        if AppSingletonObj.isConnectedToNetwork() {
            self.presenterHome?.apiCallForGetSuperCategoryList(dictData: dictParam)
        }
    }
    
    private func goToProductDetailScreen(obj: ProductResponseFields) {
        let vc = ProductPopupStoryboard.instantiate(ProductPopupViewController.self)
        print(obj.youMayAlsoLikeProducts ?? [])
        vc.objProductDetails = obj
        vc.arrMayLikeProducts = obj.youMayAlsoLikeProducts ?? []
        vc.delegate = self
        
        vc.showYouMayLikePopup = { [weak self] (obj) in
            DispatchQueue.main.async {
                self?.goToProductDetailScreen(obj: obj)
            }
        }
        
        self.presentVC(vc)
    }
    
    private func goToBundleDetailScreen(obj: BundelsListResponseFields) {
        let vc = ProductPopupStoryboard.instantiate(ProductPopupViewController.self)
        vc.objBundleDetails = obj
        vc.bundleID = obj.bunddelId ?? 0
        vc.delegate = self
        self.presentVC(vc)
    }
    
}

extension NewHomeViewController: UIScrollViewDelegate {
    
    func scrollViewDidScroll(_ scrollView: UIScrollView) {
        
        if (scrollView.contentOffset.y >= (scrollView.contentSize.height - scrollView.frame.size.height)) {
            // check if category select and drag
            if let categoryCollectionView = scrollView as? UICollectionView {
                return
            }
            //reach bottom
            if self.selectedCategoryID != 0 && self.isLoadingMoreData {
                self.pageNo += 1
                var dictParam : [String:Any] = [:]
                dictParam["iCategoryId"] = self.selectedCategoryID
                dictParam["vSerchString"] = ""
                print(dictParam)
                cons_activityIndicatorView_height.constant = 40
                stackViewActivityIndicatorView.isHidden = false
                activityIndicatorView.isHidden = false
                activityIndicatorView.startAnimating()
                if AppSingletonObj.isConnectedToNetwork() {
                    self.isLoadingMoreData.toggle()
                    self.presenterHome?.apiCallForGetNewProducts(dictData: dictParam, flagLoader: false, pageNo: self.pageNo, isOnlyBundle: 0)
                }
            }
        }
        
        if (scrollView.contentOffset.y < 0) {
            //reach top
            self.isLoadingMoreData = false
        }
        
        if (scrollView.contentOffset.y >= 0 && scrollView.contentOffset.y < (scrollView.contentSize.height - scrollView.frame.size.height)){
            //not top and not bottom
        }
    }
    
}

extension NewHomeViewController: FSPagerViewDataSource, FSPagerViewDelegate {
    public func numberOfItems(in pagerView: FSPagerView) -> Int {
        return arrBanners.count
    }
        
    public func pagerView(_ pagerView: FSPagerView, cellForItemAt index: Int) -> FSPagerViewCell {
        let cell  = pagerView.dequeueReusableCell(withReuseIdentifier: "FSPagerViewCell", at: index)
        cell.isAppadvertisement(value: arrBanners[index].isAppAdvertisement == 1 ? false : true)
        cell.imageView.contentMode = .redraw
        
        cell.imageView.kf.indicatorType = .activity
        let url = URL(string: arrBanners[index].vImage ?? "")
        cell.imageView.kf.setImage(with: url , placeholder: UIImage(named: "placeholder_banner"))

        cell.btnShare.tag = index
        cell.btnShare.addTarget(self, action: #selector(self.actionShareHomeBanner(sender:)), for: .touchUpInside)

        return cell
    }
    
    func pagerViewDidScroll(_ pagerView: FSPagerView) {
        self.pageControl.currentPage = pagerView.currentIndex
    }
    
    func pagerViewWillEndDragging(_ pagerView: FSPagerView, targetIndex: Int) {
        self.pageControl.currentPage = targetIndex
    }
    
    func pagerView(_ pagerView: FSPagerView, didSelectItemAt index: Int) {
        self.pagerView.deselectItem(at: index, animated: false)
        
        // manage navigation to category, product detail or offer detail
        if arrBanners[index].iProductCategoryId != -1 && arrBanners[index].iProductCategoryId != nil {  // category
            let vc = ProductPopupStoryboard.instantiate(FilterSearchViewController.self)
            vc.iCategoryId = arrBanners[index].iProductCategoryId ?? 0
            vc.strCategoryName = arrBanners[index].vCategoryName ?? ""
            vc.strFromWhichScreen = "Banner"
            vc.delegate = self
            self.pushVC(vc)
        }
        else if arrBanners[index].biProductId != -1 && arrBanners[index].biProductId != nil {  // product detail
            
            // new changes
            var flag = false
            for objCategory in self.arrCategories {
                for objProduct in objCategory.products ?? [] {
                    if arrBanners[index].biProductId == objProduct.biProductId {
                        flag = true
                        let vc = ProductPopupStoryboard.instantiate(ProductPopupViewController.self)
                        vc.objProductDetails = objProduct
                        vc.arrMayLikeProducts = objProduct.youMayAlsoLikeProducts ?? []
                        vc.delegate = self
                        
                        vc.showYouMayLikePopup = { [weak self] (obj) in
                            DispatchQueue.main.async {
                                self?.goToProductDetailScreen(obj: obj)
                            }
                        }

                        self.presentVC(vc)
                    }
                }
            }
            
            if flag == false {  // Product not found, call API in detail screen
                flag = true
                let vc = ProductPopupStoryboard.instantiate(ProductPopupViewController.self)
                vc.strFromWhichScreen = "CallAPI"
                vc.productId = arrBanners[index].biProductId ?? 0
                vc.delegate = self
                
                vc.showYouMayLikePopup = { [weak self] (obj) in
                    DispatchQueue.main.async {
                        self?.goToProductDetailScreen(obj: obj)
                    }
                }

                self.presentVC(vc)
            }
            
        }
        else if arrBanners[index].iOfferId != -1 && arrBanners[index].iOfferId != nil {  // offer detail
            
            if !User.shared.checkUserLoginStatus() {
                self.showNoLoginUserAlert(complation: nil)
                return
            }

            let vc = ProductPopupStoryboard.instantiate(OfferDetailsViewController.self)
            vc.modalPresentationStyle = .overFullScreen
            vc.modalTransitionStyle = .crossDissolve
            vc.offerId = arrBanners[index].iOfferId ?? 0
            vc.delegate = self
            self.presentVC(vc)
        }
        else if arrBanners[index].iProductCategoryId == nil && arrBanners[index].biProductId == nil && arrBanners[index].iOfferId == nil {  // info banner
            let vc = ProductPopupStoryboard.instantiate(BannerInfoViewController.self)
            vc.modalPresentationStyle = .overFullScreen
            vc.modalTransitionStyle = .crossDissolve
            vc.objBanner = arrBanners[index]
            self.presentVC(vc)

        }
    }
}

extension NewHomeViewController: NewHomeProtocol {
    func displayAlert(string: String) {
        AppSingleton.shared.showCustomPopUpWithOkButton(strButtonTitle: ObjKeymessages.kLABEL_OK, strMessage: string, showOnTopVC: false) { (isOk) in
        }
    }

    func refreshData(model: LatestHomeListingResponseFields) {
        // Do you have a friend invitation gift?
        Constant.shared.VALUE_GIFT = model.gifts?.giftValue ?? ""
        self.hideLoaderOn(self.colCategoryLoader)
        self.hideLoaderOn(self.colSectionTableLoader)
        if model.gifts?.giftValue == "0" || model.gifts?.giftValue == "0.0" || model.gifts?.giftValue == "" || model.gifts?.giftValue == nil {
            self.stackViewInviteFriends.isHidden = true
        } else {
            self.inviteGiftValue = model.gifts?.giftValue ?? ""
            self.lblInviteFriend.text = "invite_get_money".localized.replacingOccurrences(of: "%2d", with: "\(self.inviteGiftValue)")
            self.stackViewInviteFriends.isHidden = false
        }
        // new changes
        selectedCategoryIndex = -1
        selectedSuperCategoryIndex = -1
        tblSections.isHidden = false
        
        collProductList.isHidden = true
        lblNoDataProductList.isHidden = true
        collNewProductsBottomConstraint.priority = .defaultLow
        
        // new changes ends here
                
        arrBanners = model.banners ?? []
        arrCategories = model.categories ?? []
        arrCategoriesLoaded = arrCategories
        arrSections = model.sections ?? []
        arrSectionsLoaded = arrSections
        // set data for super category list
        self.arrSuperCategories = model.superCategoryList ?? []
        self.pageControl.numberOfPages = arrBanners.count
        pagerView.reloadData()
        
        if self.arrSuperCategories.count == 0 {
            self.collSuperCategories.isHidden = true
        } else {
            collSuperCategories.reloadData()
        }
        
        collCategories.reloadData()
        tblSections.reloadData()
        
        self.scrollToItemAccordingToLanguage()
        VatRegNumber = model.dVatNumber ?? ""
        
        if arrBanners.count <= 0 {  // hide banner and pagerview & pagecontrol view & space view
            self.collMainSlider.isHidden = true
            viewSpace.isHidden = true
        }
        else {
            self.collMainSlider.isHidden = false
            if arrBanners.count <= 1 {
                self.viewSpace.isHidden = false
            }
            else {
                self.viewSpace.isHidden = true
            }
        }
        // reload coins to get the latest data
        if isFirstOpenDone {
            self.getCurrentLevelShape()
        }
        self.presenterHome?.apiCalculateUserPointsToSAR(points: 0)
        self.isFirstOpenDone = true
        reloadSuperCategoriesData { _ in
            DispatchQueue.main.async {
                self.collSuperCategories.reloadData()
            }
        }
        
        // check if get a friend invitation gift?
        if model.gifts?.inviteFreind ?? false {
            self.inviteFriendGift()
        }
        if arrBanners.count >= 1 {
            self.collMainSlider.contentMode = .right
            self.collMainSlider.reloadData()
            DispatchQueue.main.async {
                self.collMainSlider.scrollToItem(at: IndexPath(item: UserDefaults.standard.isCurrentLanguageArabic() ? self.arrBanners.count - 1 : 0, section: 0), at: .right , animated: false)
                
            }
        }
    }
    
    fileprivate func scrollToItemAccordingToLanguage() {
        if arrSuperCategories.count > 0 {
            DispatchQueue.main.async {
                self.collSuperCategories.scrollToItem(at: IndexPath(item: 0, section: 0), at: UserDefaults.standard.isCurrentLanguageArabic() ? .right : .left, animated: false)
            }
        }
        
        if arrCategories.count > 0 {
            DispatchQueue.main.async {
                self.collCategories.scrollToItem(at: IndexPath(item: 0, section: 0), at: UserDefaults.standard.isCurrentLanguageArabic() ? .right : .left, animated: false)
            }
        }
    }

    func saveUserSpecificData(model: UserDetailListingResponseFields) {
        // manage badge on My orders tab
        if model.isCurrentOrderAvailable == 1 {  // order available
            self.tabBarController?.tabBar.items?[3].badgeValue = "●"
            self.tabBarController?.tabBar.items?[3].badgeColor = .clear
            self.tabBarController?.tabBar.items?[3].setBadgeTextAttributes([NSAttributedString.Key.foregroundColor: UIColor.AppTheme_SelectedTabColor_F4BA45], for: .normal)
            self.tabBarController?.tabBar.items?[3].setBadgeTextAttributes([NSAttributedString.Key.foregroundColor: UIColor.AppTheme_SelectedTabColor_F4BA45], for: .selected)

        }
        else {   // order not available
            self.tabBarController?.tabBar.items?[3].badgeValue = nil
        }

        // update my cart button count
        if let myTabbar = self.tabBarController?.tabBar as? STTabbar {
            if model.cartProductCount ?? 0 <= 0 {
                myTabbar.label?.isHidden = true
            }
            else {
                myTabbar.label?.isHidden = false
                myTabbar.label?.text = "\(model.cartProductCount ?? 0)"
            }
        }

        // save wallet balance
        let WalletBalance = Double(model.latestWalletBalance ?? "") ?? 0
        let strWalletBalance = "\(forTrailingZero(temp: WalletBalance))"
        let strWalletBalanceEnNumbers = "\(forTrailingZeroEnglishNumbersOnly(temp: Double(model.latestWalletBalance ?? "") ?? 0))"
        UserDefaults.standard.set(strWalletBalance, forKey: USERDEFAULTS_INFO_KEY.WALLET_BALANCE)
        UserDefaults.standard.set(strWalletBalanceEnNumbers, forKey: USERDEFAULTS_INFO_KEY.WALLET_BALANCE_EN_NUMBER)
        UserDefaults.standard.synchronize()

        // Show Ratings popup for last delivered order
                
        if model.tiIsRated == 0 && model.tiIsRatingSkip == 0 && !(self.topMostViewController is RatingViewController) { // Show popup
            let vc = settingsStoryboard.instantiate(RatingViewController.self)
            vc.modalPresentationStyle = .overFullScreen
            vc.modalTransitionStyle = .crossDissolve
            vc.strFromWhichScreen = "Home"
            vc.orderId = Int(model.iOrderId ?? "") ?? 0
            self.topMostViewController?.presentVC(vc)
        }
    }

    func showAdvertisementPopup(model: [AdvertisementResponseFields]?) {
        if model == nil {  // There is not any active advertisement. Do not show Adv popup
        }
        else { // Show Adv popup
            let vc = homeStoryboard.instantiate(AdvertisementPopupViewController.self)
            vc.modalPresentationStyle = .overFullScreen
            vc.modalTransitionStyle = .crossDissolve
            vc.objAdvDetails = model
            
            vc.navigateForAdvertisement = { [weak self] () in
                
                DispatchQueue.main.async {
                    if model?[Constant.shared.ADVERTISEMENT_INDEX].iProductCategoryId != -1 {  // category
                        let vc = ProductPopupStoryboard.instantiate(FilterSearchViewController.self)
                        vc.iCategoryId = model?[Constant.shared.ADVERTISEMENT_INDEX].iProductCategoryId ?? 0
                        vc.strCategoryName = model?[Constant.shared.ADVERTISEMENT_INDEX].vCategoryName ?? ""
                        vc.strFromWhichScreen = "Banner"
                        vc.delegate = self
                        self?.pushVC(vc)
                    }
                    else if model?[Constant.shared.ADVERTISEMENT_INDEX].biProductId != -1 {  // product detail
                        let vc = ProductPopupStoryboard.instantiate(ProductPopupViewController.self)
                        vc.strFromWhichScreen = "CallAPI"
                        vc.productId = model?[Constant.shared.ADVERTISEMENT_INDEX].biProductId ?? 0
                        vc.delegate = self
                        
                        vc.showYouMayLikePopup = { [weak self] (obj) in
                            DispatchQueue.main.async {
                                self?.goToProductDetailScreen(obj: obj)
                            }
                        }

                        self?.presentVC(vc)
                    }
                    else if model?[Constant.shared.ADVERTISEMENT_INDEX].iOfferId != -1 {  // offer detail
                        let vc = ProductPopupStoryboard.instantiate(OfferDetailsViewController.self)
                        vc.modalPresentationStyle = .overFullScreen
                        vc.modalTransitionStyle = .crossDissolve
                        vc.offerId = model?[Constant.shared.ADVERTISEMENT_INDEX].iOfferId ?? 0
                        vc.delegate = self
                        self?.presentVC(vc)
                    }
                    else if model?[Constant.shared.ADVERTISEMENT_INDEX].vSocialLink != "" {  // social link
                        guard let socialUrl = URL(string: model?[Constant.shared.ADVERTISEMENT_INDEX].vSocialLink ?? "") else {
                            return
                        }
                        if UIApplication.shared.canOpenURL(socialUrl) {
                            if #available(iOS 10.0, *) {
                                UIApplication.shared.open(socialUrl, options: [:], completionHandler: nil)
                            } else {
                                UIApplication.shared.openURL(socialUrl)
                                // Fallback on earlier versions
                            }
                        }
                    }
                }
            }
            self.topMostViewController?.presentVC(vc)

        }
    }

    func decideForSoftUpdateAlert(model: AppVersionResponseFields, strMsg: String) {
        self.checkVersionForSoftUpdate(obj: model, strMsg: strMsg)

    }

    func refreshNewProducts(model : ProductListResponseFields) {
        // Do you have a friend invitation gift?        
        debugPrint("INSIDE refreshNewProducts")
        DispatchQueue.main.async {
            self.cons_activityIndicatorView_height.constant = 0
            self.stackViewActivityIndicatorView.isHidden = true
            self.activityIndicatorView.stopAnimating()
            self.activityIndicatorView.isHidden = true
            self.endRefresing()
        }
        self.isSyncing = false
        self.totalCount = model.totalRecord ?? 0
        self.totalPage = model.totalPage ?? 0
                        
//        var indexPaths = [IndexPath]()
        var oldIndex = (arrProductList.count - 1)
        let newCount = model.productList?.count ?? 0
        if newCount <= 0 {
            self.loadingIndicatorView.isHidden = true
            self.lblNoDataProductList.isHidden = false
            return
        }
//        for _ in 0..<newCount{
//            oldIndex += 1
//            indexPaths.append(IndexPath(item: oldIndex, section: 0))
//        }
        
        let arr =  model.productList?.sorted(by: { $0.iHowMuchLeftInStock ?? 0 > $1.iHowMuchLeftInStock ?? 0
        })
        self.arrProductList.append(contentsOf: arr ?? [])
        
//        collProductList.performBatchUpdates {
//            collProductList.insertItems(at: indexPaths)
//        }
        
        self.loadingIndicatorView.isHidden = true
        if arrProductList.count <= 0 {
            lblNoDataProductList.isHidden = false
        }
        else {
            lblNoDataProductList.isHidden = true
        }
        self.toggleViews()
        self.isLoadingMoreData = true
        self.arrProductList = self.arrProductList.sorted(by: { $0.iHowMuchLeftInStock ?? 0 > $1.iHowMuchLeftInStock ?? 0 })
        self.collProductList.reloadData()
    }

    private func toggleViews() {
        tblSections.isHidden = true
        collProductList.isHidden = false
    }
    
    func getPointsLevels(value: Int, maxLevel: Int, remainingPoint: Int, rewardPoint: Int, levelPoint: Int, maxPoint: Int) {}
    
    private func setlottieFileAnaimation(fileName: String) {
        Constant.shared.FILE_NAME = fileName
        self.viewCoinsAnimation.stop()
        let path = Bundle.main.path(forResource: fileName,
                                        ofType: "json") ?? ""
        self.viewCoinsAnimation.animation = Animation.filepath(path)
        viewCoinsAnimation.contentMode = .scaleAspectFit
        viewCoinsAnimation.loopMode = .playOnce
        viewCoinsAnimation.play()
    }
    
    func getCalculateUserPointsToSAR(model: CalculateUserPointsToSARResponseFields) {
        var points = model.userPoints ?? 0
        var currentPoints: Double = Double(points) / Double(Constant.shared.POINTS_MAX_LEVELS)
        
        Constant.shared.MY_POINTS_VALUE = Int(Double(currentPoints) * Double(Constant.shared.SHAPE_MAX_VALUE)) - 25
        Constant.shared.USER_POINTS_BY_SAR = model.userPointsBySAR ?? 0.0
        Constant.shared.CURRENT_POINTS_VALUE = "\(model.totalUserPoints ?? 0)"
        self.runTimer()
    }
    
    func getPointsDescription(model: GetPointsDescriptionResponse) {
        Constant.shared.POINTS_DESCRIPTION = model.data ?? []
    }
    
    func getSuperCategoriesData(model: SuperCategoryListResponse) {
        debugPrint(model.categories)
        arrCategories = model.categories ?? []
        arrSections = model.sections ?? []
        collProductList.isHidden = true
        tblSections.isHidden = false
        tblSections.reloadData()
        if arrCategories.count == 0 {
            collCategories.isHidden = true
            self.viewCollCategories.isHidden = true
        } else {
            collCategories.isHidden = false
            self.viewCollCategories.isHidden = false
            collCategories.reloadData()
            self.scrollToItemAccordingToLanguage()
            if arrCategories.count > 0 {
                self.lblNoDataProductList.isHidden = true
            }
        }
        self.loadingIndicatorView.isHidden = true
    }
}

extension NewHomeViewController : AddressListProtocol {
    
    func addOREditAddress(address:AddressResponseFields?) {
        lblCurrentAddress.text = address?.vType
        CheckoutManager.shared.setAddress(id: address?.iAddressId ?? 0, type: address?.vType ?? "")
        
        if let address = address,let dict = address.dictionary {
            TikTokBusiness.trackEvent("change_add_address_loaction", withProperties: dict)
            Analytics.logEvent("change_add_address_loaction", parameters: dict)
            UserDefaults.standard.set(dict, forKey: USERDEFAULTS_INFO_KEY.CURRENT_ADDRESS)
            UserDefaults.standard.synchronize()
        }
    }
    
    func removeSelectedAddress() {
        TikTokBusiness.trackEvent("remove_address")
        Analytics.logEvent("remove_address", parameters: nil)
        setCurrentAddress()
    }
    
}

extension NewHomeViewController : UpdateCountProtocol {
    
    func updateCartCount(cartCount:Int?) {
        // update my cart button count
        if let myTabbar = self.tabBarController?.tabBar as? STTabbar {
            if cartCount ?? 0 <= 0 {
                myTabbar.label?.isHidden = true
            }
            else {
                myTabbar.label?.isHidden = false
                myTabbar.label?.text = "\(cartCount ?? 0)"
            }
        }

    }
}

extension NewHomeViewController : UpdateCountFromOfferProtocol {
    
    func updateCartCountFromOffer(cartCount:Int?) {
        // update my cart button count
        if let myTabbar = self.tabBarController?.tabBar as? STTabbar {
            if cartCount ?? 0 <= 0 {
                myTabbar.label?.isHidden = true
            }
            else {
                myTabbar.label?.isHidden = false
                myTabbar.label?.text = "\(cartCount ?? 0)"
            }
        }

    }
}

extension NewHomeViewController : UpdateCountFromSearchProtocol {
    
    func updateCartCountFromSearch(cartCount:Int?) {
        // update my cart button count
        if let myTabbar = self.tabBarController?.tabBar as? STTabbar {
            if cartCount ?? 0 <= 0 {
                myTabbar.label?.isHidden = true
            }
            else {
                myTabbar.label?.isHidden = false
                myTabbar.label?.text = "\(cartCount ?? 0)"
            }
        }

    }
}

extension NewHomeViewController : UITableViewDelegate, UITableViewDataSource{
    func numberOfSections(in tableView: UITableView) -> Int {
        return 1
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return arrSections.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        
        // type 1 --> Category
        // type 2 --> Banner
        // type 3 --> Last Offers
        if self.arrSections[indexPath.row].type == 1 { // type 1 --> Category
            let cell = tblSections.dequeueReusableCell(withIdentifier: "HomeTableViewCell") as! HomeTableViewCell
            
            // remove product with 0 quantity
            let arrProducts = self.arrSections[indexPath.row].products?.first?.products ?? [] //?.filter({$0.iHowMuchLeftInStock ?? 0 > 0})
            cell.arrProducts = arrProducts

            cell.lblTitle.text = arrSections[indexPath.row].products?.first?.categoryName
            
            cell.delegate = self
            
            cell.btnMore.tag = indexPath.row
            cell.btnMore.addTarget(self, action: #selector(self.actionMore(sender:)), for: .touchUpInside)

            cell.btnLabelClick.tag = indexPath.row
            cell.btnLabelClick.addTarget(self, action: #selector(self.actionMore(sender:)), for: .touchUpInside)

            if arrProducts.count <= 0 {
                cell.lblNoData.isHidden = false
                cell.btnMore.isHidden = true
                cell.btnLabelClick.isHidden = true
            }
            else {
                cell.lblNoData.isHidden = true
                cell.btnMore.isHidden = false
                cell.btnLabelClick.isHidden = false
            }

            cell.collectionView.reloadData()
            
            return cell
        
        } else if self.arrSections[indexPath.row].type == 3 { // type 3 --> Last Offers
            let cell = tableView.dequeueReusableCell(withIdentifier: "LastOfferTableViewCell") as! LastOfferTableViewCell
            cell.setupCell(lastOffers: self.arrSections[indexPath.row].lastOffers ?? [])
            cell.btnMoreOpenOffers.addTarget(self, action: #selector(self.openLastOffers(sender:)), for: .touchUpInside)
            cell.delegate = self
            cell.lastOfferCollectionView.reloadData()
            cell.layoutIfNeeded()
            return cell
        } else if self.arrSections[indexPath.row].type == 4 { // type 4 --> Bundle List
            let cell = tableView.dequeueReusableCell(withIdentifier: "BundelsListTableViewCell") as! BundelsListTableViewCell
            cell.setupCell(bundels: self.arrSections[indexPath.row].bundelsList ?? [])
            cell.btnMore.tag = indexPath.row
            cell.btnMore.addTarget(self, action: #selector(self.actionMore(sender:)), for: .touchUpInside)
            cell.btnMoreOpenOffers.tag = indexPath.row
            cell.btnMoreOpenOffers.addTarget(self, action: #selector(self.actionMore(sender:)), for: .touchUpInside)
            cell.delegate = self
            cell.bundelsCollectionView.reloadData()
            self.arrBundelsList = self.arrSections[indexPath.row].bundelsList ?? []
            cell.layoutIfNeeded()
            return cell
        }
        else { // type 2 --> Banner
            let cell = tblSections.dequeueReusableCell(withIdentifier: "HomeBannerCell") as! HomeBannerCell
            
            cell.imgBanner.contentMode = .scaleAspectFit
            cell.imgBanner.kf.indicatorType = .activity
            cell.isAppadvertisement(value: arrSections[indexPath.row].banners?[0].isAppadvertisement == 1 ? false : true)
            if arrSections[indexPath.row].banners?.count ?? 0 > 0 {
                if arrSections[indexPath.row].banners?[0].marketingBannerImage?.count ?? 0 > 0 {
                    let url = URL(string: arrSections[indexPath.row].banners?[0].marketingBannerImage ?? "")
                    cell.imgBanner.kf.setImage(with: url , placeholder: UIImage(named: "placeholder_banner"))
                }
                
                /*1:Product
                2: Category
               3: Offer
               4:External Link*/
                cell.btnShare.isHidden = arrSections[indexPath.row].banners?[0].tiLinkType == HomeBannerType.ExternalLink.rawValue ? true : false
            }
            
            cell.btnBannerAction.tag = indexPath.row
            cell.btnBannerAction.addTarget(self, action: #selector(self.actionBanner(sender:)), for: .touchUpInside)

            cell.btnShare.tag = indexPath.row
            cell.btnShare.addTarget(self, action: #selector(self.actionShare(sender:)), for: .touchUpInside)

            return cell
        }
    }
    
    @objc func actionShare(sender: UIButton) {
        if arrSections[sender.tag].banners?.count ?? 0 > 0 {
            
            let type = arrSections[sender.tag].banners?[0].tiLinkType
            switch type {
            case HomeBannerType.Product.rawValue:
                var strImage = ""
                if arrSections[sender.tag].banners?[0].marketingBannerImage?.count ?? 0 > 0 {
                    strImage = arrSections[sender.tag].banners?[0].marketingBannerImage ?? ""
                }
                FirebaseDeeplinkingHelper.shared.generateFeedPlanLink(strProductId: "\(arrSections[sender.tag].banners?[0].biProductId ?? 0)", strTitle: "\(arrSections[sender.tag].banners?[0].vTitle ?? "")", strDescription: "", strImageUrl: strImage) { url in
                    let strMsg = "\(self.arrSections[sender.tag].banners?[0].vTitle ?? "") \(ObjKeymessages.kLABEL_SHARE_TEXT)\n\n\(url ?? "")"
                    self.shareTextInDefaultShareKit(array: [strMsg])
                }
            case HomeBannerType.Category.rawValue:
                var strImage = ""
                if arrSections[sender.tag].banners?[0].marketingBannerImage?.count ?? 0 > 0 {
                    strImage = arrSections[sender.tag].banners?[0].marketingBannerImage ?? ""
                }
                FirebaseDeeplinkingHelper.shared.generateFeedPlanLinkForCategory(strCategoryId: "\(arrSections[sender.tag].banners?[0].iProductCategoryId ?? 0)", strTitle: "\(arrSections[sender.tag].banners?[0].vTitle ?? "")", strCategoryName: "\(arrSections[sender.tag].banners?[0].vCategoryName ?? "")", strDescription: "", strImageUrl: strImage) { url in
                    let strMsg = "\(self.arrSections[sender.tag].banners?[0].vTitle ?? "") \(ObjKeymessages.kLABEL_SHARE_TEXT)\n\n\(url ?? "")"
                    self.shareTextInDefaultShareKit(array: [strMsg])
                }
            case HomeBannerType.Offer.rawValue:
                var strImage = ""
                if arrSections[sender.tag].banners?[0].marketingBannerImage?.count ?? 0 > 0 {
                    strImage = arrSections[sender.tag].banners?[0].marketingBannerImage ?? ""
                }
                FirebaseDeeplinkingHelper.shared.generateFeedPlanLinkForOffer(strOfferId: "\(arrSections[sender.tag].banners?[0].iOfferId ?? 0)", strTitle: "\(arrSections[sender.tag].banners?[0].vTitle ?? "")", strDescription: "", strImageUrl: strImage) { url in
                    let strMsg = "\(self.arrSections[sender.tag].banners?[0].vTitle ?? "") \(ObjKeymessages.kLABEL_SHARE_TEXT)\n\n\(url ?? "")"
                    self.shareTextInDefaultShareKit(array: [strMsg])
                }
            default:
                break
            }
        }
    }
    
    @objc func actionShareHomeBanner(sender: UIButton) {
        if arrBanners[sender.tag].iProductCategoryId != -1 {  // category
            FirebaseDeeplinkingHelper.shared.generateFeedPlanLinkForCategory(strCategoryId: "\(arrBanners[sender.tag].iProductCategoryId ?? 0)", strTitle: "\(arrBanners[sender.tag].vName ?? "")", strCategoryName: "\(arrBanners[sender.tag].vCategoryName ?? "")", strDescription: "", strImageUrl: arrBanners[sender.tag].vImage ?? "") { url in
                let strMsg = "\(self.arrBanners[sender.tag].vName ?? "") \(ObjKeymessages.kLABEL_SHARE_TEXT)\n\n\(url ?? "")"
                self.shareTextInDefaultShareKit(array: [strMsg])
            }
        }
        else if arrBanners[sender.tag].iOfferId != -1 {  // offer detail
            if !User.shared.checkUserLoginStatus() {
                self.showNoLoginUserAlert(complation: nil)
                return
            }
            FirebaseDeeplinkingHelper.shared.generateFeedPlanLinkForOffer(strOfferId: "\(arrBanners[sender.tag].iOfferId ?? 0)", strTitle: "\(arrBanners[sender.tag].vName ?? "")", strDescription: "", strImageUrl: arrBanners[sender.tag].vImage ?? "") { url in
                let strMsg = "\(self.arrBanners[sender.tag].vName ?? "") \(ObjKeymessages.kLABEL_SHARE_TEXT)\n\n\(url ?? "")"
                self.shareTextInDefaultShareKit(array: [strMsg])
            }
        }
        else if arrBanners[sender.tag].biProductId != -1 {  // product detail
            FirebaseDeeplinkingHelper.shared.generateFeedPlanLink(strProductId: "\(arrBanners[sender.tag].biProductId ?? 0)", strTitle: "\(arrBanners[sender.tag].vName ?? "")", strDescription: "", strImageUrl: arrBanners[sender.tag].vImage ?? "") { url in
                let strMsg = "\(self.arrBanners[sender.tag].vName ?? "") \(ObjKeymessages.kLABEL_SHARE_TEXT)\n\n\(url ?? "")"
                self.shareTextInDefaultShareKit(array: [strMsg])
            }
        }
        else if arrBanners[sender.tag].iProductCategoryId == -1 && arrBanners[sender.tag].biProductId == -1 && arrBanners[sender.tag].iOfferId == -1 {  // info banner
            FirebaseDeeplinkingHelper.shared.generateFeedPlanLinkForBanner(strBannerId: "\(arrBanners[sender.tag]._id ?? 0)", strTitle: arrBanners[sender.tag].vName ?? "", strDescription: arrBanners[sender.tag].tBannerDescription ?? "", strImageUrl: arrBanners[sender.tag].vImage ?? "") { url in
                let strMsg = "\(self.arrBanners[sender.tag].tBannerDescription ?? "") \(ObjKeymessages.kLABEL_SHARE_TEXT)\n\n\(url ?? "")"
                print(strMsg)
                self.shareTextInDefaultShareKit(array: [strMsg])
            }
        }
    }

    func shareTextInDefaultShareKit(array:[Any],complation: (()->())? = nil) {
        let activityVC : UIActivityViewController = UIActivityViewController(activityItems: array, applicationActivities: nil)
        activityVC.completionWithItemsHandler = { (activityType,isCompleted,returnItems,error) in
            complation?()
        }
        self.presentVC(activityVC)
    }

    @objc func openLastOffers(sender: UIButton) {
        self.tabBarController?.selectedIndex = 1
    }
    
    @objc func openBundlesList(sender: UIButton) {
        debugPrint(sender.tag)
    }
    
    @objc func actionBanner(sender: UIButton) {
        if arrSections[sender.tag].banners?.count ?? 0 > 0 {
            // manage navigation to category, product detail or offer detail
            if arrSections[sender.tag].banners?[0].iProductCategoryId != nil {  // category
                let vc = ProductPopupStoryboard.instantiate(FilterSearchViewController.self)
                vc.iCategoryId = arrSections[sender.tag].banners?[0].iProductCategoryId ?? 0
                vc.strCategoryName = arrSections[sender.tag].banners?[0].vTitle ?? ""
                vc.strFromWhichScreen = "Banner"
                vc.delegate = self
                self.pushVC(vc)
            }
            else if arrSections[sender.tag].banners?[0].biProductId != nil {  // product detail
                
                // new changes
                var flag = false
                for objCategory in self.arrCategories {
                    for objProduct in objCategory.products ?? [] {
                        if arrSections[sender.tag].banners?[0].biProductId == objProduct.biProductId {
                            flag = true
                            let vc = ProductPopupStoryboard.instantiate(ProductPopupViewController.self)
                            print(objProduct.youMayAlsoLikeProducts ?? [])
                            vc.objProductDetails = objProduct
                            vc.arrMayLikeProducts = objProduct.youMayAlsoLikeProducts ?? []
                            vc.delegate = self
                            
                            vc.showYouMayLikePopup = { [weak self] (obj) in
                                DispatchQueue.main.async {
                                    self?.goToProductDetailScreen(obj: obj)
                                }
                            }

                            self.presentVC(vc)
                        }
                    }
                }
                
                if flag == false {  // Product not found, call API in detail screen
                    flag = true
                    let vc = ProductPopupStoryboard.instantiate(ProductPopupViewController.self)
                    vc.strFromWhichScreen = "CallAPI"
                    vc.productId = arrSections[sender.tag].banners?[0].biProductId ?? 0
                    vc.delegate = self
                    
                    vc.showYouMayLikePopup = { [weak self] (obj) in
                        DispatchQueue.main.async {
                            self?.goToProductDetailScreen(obj: obj)
                        }
                    }

                    self.presentVC(vc)
                }
                
            }
            else if arrSections[sender.tag].banners?[0].iOfferId != nil {  // offer detail
                
                if !User.shared.checkUserLoginStatus() {
                    self.showNoLoginUserAlert(complation: nil)
                    return
                }

                let vc = ProductPopupStoryboard.instantiate(OfferDetailsViewController.self)
                vc.modalPresentationStyle = .overFullScreen
                vc.modalTransitionStyle = .crossDissolve
                vc.offerId = arrSections[sender.tag].banners?[0].iOfferId ?? 0
                vc.delegate = self
                self.presentVC(vc)
            }
            else if arrSections[sender.tag].banners?[0].iProductCategoryId == nil && arrSections[sender.tag].banners?[0].biProductId == nil && arrSections[sender.tag].banners?[0].iOfferId == nil {  // External link
                if arrSections[sender.tag].banners?[0].vUrl != "" {  // social link
                    guard let socialUrl = URL(string: arrSections[sender.tag].banners?[0].vUrl ?? "") else {
                        return
                    }
                    if UIApplication.shared.canOpenURL(socialUrl) {
                        if #available(iOS 10.0, *) {
                            UIApplication.shared.open(socialUrl, options: [:], completionHandler: nil)
                        } else {
                            UIApplication.shared.openURL(socialUrl)
                            // Fallback on earlier versions
                        }
                    }
                }


            }
        }
    }
    
}

extension NewHomeViewController : GoToDetailScreenDelegate {
    func goToDetailScreen(obj: ProductResponseFields) {
        let vc = ProductPopupStoryboard.instantiate(ProductPopupViewController.self)
        print(obj.youMayAlsoLikeProducts ?? [])
        vc.objProductDetails = obj
        vc.productId = obj._id ?? 0
        vc.arrMayLikeProducts = obj.youMayAlsoLikeProducts ?? []
        vc.delegate = self
        
        vc.showYouMayLikePopup = { [weak self] (obj) in
            DispatchQueue.main.async {
                self?.goToProductDetailScreen(obj: obj)
            }
        }
        
        self.presentVC(vc)
    }
    
}

extension UIImage {
    var grayed: UIImage {
        guard let ciImage = CIImage(image: self)
            else { return self }
        let filterParameters = [ kCIInputColorKey: CIColor.white, kCIInputIntensityKey: 1.0 ] as [String: Any]
        let grayscale = ciImage.applyingFilter("CIColorMonochrome", parameters: filterParameters)
        return UIImage(ciImage: grayscale)
    }
    
}
extension UIApplication {
  private static let notificationSettingsURLString: String? = {
    if #available(iOS 16, *) {
      return UIApplication.openNotificationSettingsURLString
    }

    if #available(iOS 15.4, *) {
      return UIApplicationOpenNotificationSettingsURLString
    }

    if #available(iOS 8.0, *) {
      // just opens settings
      return UIApplication.openSettingsURLString
    }

    // lol bruh
    return nil
  }()

  static let appNotificationSettingsURL = URL(
    string: notificationSettingsURLString ?? ""
  )

}

extension NewHomeViewController: LastOfferTableViewCellDelegate {
    func openOfferDetails(offerID: Int) {
        let vc = ProductPopupStoryboard.instantiate(OfferDetailsViewController.self)
        vc.modalPresentationStyle = .overFullScreen
        vc.modalTransitionStyle = .crossDissolve
        vc.offerId = offerID
        vc.delegate = self
        self.topMostViewController?.presentVC(vc)
    }
    
}

// MARK: - BundelsListTableViewCellDelegate
extension NewHomeViewController: BundelsListTableViewCellDelegate {
    // MARK: - Func
    func openBundelsListDetails(bundleID: Int, index: Int) {
        debugPrint(bundleID)
        self.goToBundleDetailScreen(obj: self.arrBundelsList[index])
    }
    
    func addBundleToCart(bundle: Int) {
        debugPrint(bundle)
        if !User.shared.checkUserLoginStatus() {
            self.showNoLoginUserAlert(complation: nil)
            return
        }
        
        var dictParam : [String:Any] = [:]
        dictParam["biProductId"] = 0
        dictParam["bunddelId"] = self.arrBundelsList[bundle].bunddelId ?? 0
        dictParam["tiIsCheck"] = AddToCartIsCheck.Add.rawValue
        dictParam["iProductQuantity"] = "1"
        dictParam["dbPrice"] = self.arrBundelsList[bundle].price ?? 0.0
        if AppSingletonObj.isConnectedToNetwork(){
            self.presenterHome?.apiCallForAddToCart(dictData: dictParam)
        }
    }
    
    func displayAlertAndDismiss(string:String, cartCount: Int) {
        self.getUserSpecificData()
        self.stopLoading()
        // update my cart button count
        self.updateCartCount(cartCount: cartCount)
        self.vibrationDevice()
        AppSingletonObj.showAlert(strMessage: string)
    }
    
    func displayErrorAlert(strMsg: String) {
        AppSingleton.shared.showCustomPopUpWithOkButton(strButtonTitle: ObjKeymessages.kLABEL_OK, strMessage: strMsg, showOnTopVC: true) { (isOk) in
            if isOk == true {
                self.dismissVC(completion: nil)
            }
        }
    }
    
}
// MARK: - Extension setupWhatsAppView
extension NewHomeViewController {
    private func setupWhatsAppView() {
        view.addSubview(floatingButtonView)
        floatingButtonView.translatesAutoresizingMaskIntoConstraints = false
        activateConstraints()
    }
    
    private func activateConstraints() {
        NSLayoutConstraint.activate([
            floatingButtonView.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 15),
            floatingButtonView.bottomAnchor.constraint(equalTo: view.safeAreaLayoutGuide.bottomAnchor, constant: -20)
        ])
    }
}
