<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="22505" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22504"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="Stack View standard spacing" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="collection view cell content view" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="LoewNextArabic-Bold.ttf">
            <string>LoewNextArabic-Bold</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" reuseIdentifier="LastOfferTableViewCell" rowHeight="170" id="KGk-i7-Jjw" customClass="LastOfferTableViewCell" customModule="TopCustomer" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="414" height="170"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="414" height="170"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <view contentMode="scaleToFill" misplaced="YES" translatesAutoresizingMaskIntoConstraints="NO" id="A09-cs-etH">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="160"/>
                        <subviews>
                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacingType="standard" translatesAutoresizingMaskIntoConstraints="NO" id="QlD-4X-caE">
                                <rect key="frame" x="0.0" y="10" width="414" height="138"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="8Nj-TZ-G2W">
                                        <rect key="frame" x="0.0" y="0.0" width="414" height="25"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Title" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="J1d-eu-frf">
                                                <rect key="frame" x="20" y="0.0" width="374" height="18"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="18" id="32a-zz-bdP"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="20"/>
                                                <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="right" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" hasAttributedTitle="YES" translatesAutoresizingMaskIntoConstraints="NO" id="dB7-SH-Ssb">
                                                <rect key="frame" x="355" y="0.0" width="39" height="18"/>
                                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                <state key="normal">
                                                    <attributedString key="attributedTitle">
                                                        <fragment content="More">
                                                            <attributes>
                                                                <color key="NSColor" name="textColor" catalog="System" colorSpace="catalog"/>
                                                                <font key="NSFont" size="14" name="LoewNextArabic-Medium"/>
                                                                <paragraphStyle key="NSParagraphStyle" alignment="natural" lineBreakMode="wordWrapping" baseWritingDirection="natural" tighteningFactorForTruncation="0.0"/>
                                                                <integer key="NSUnderline" value="1"/>
                                                            </attributes>
                                                        </fragment>
                                                    </attributedString>
                                                </state>
                                            </button>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="7Jk-1l-6SQ">
                                                <rect key="frame" x="0.0" y="0.0" width="414" height="25"/>
                                                <state key="normal" title="Button"/>
                                                <buttonConfiguration key="configuration" style="plain" title=" "/>
                                            </button>
                                        </subviews>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="25" id="1eJ-aO-zEd"/>
                                            <constraint firstAttribute="trailing" secondItem="J1d-eu-frf" secondAttribute="trailing" constant="20" id="4xu-Fj-Zt7"/>
                                            <constraint firstItem="J1d-eu-frf" firstAttribute="leading" secondItem="8Nj-TZ-G2W" secondAttribute="leading" constant="20" id="EmV-Ib-GnQ"/>
                                            <constraint firstAttribute="bottom" secondItem="J1d-eu-frf" secondAttribute="bottom" constant="7" id="GP7-M2-5x6"/>
                                            <constraint firstAttribute="trailing" secondItem="dB7-SH-Ssb" secondAttribute="trailing" constant="20" id="NOQ-zX-ltf"/>
                                            <constraint firstItem="J1d-eu-frf" firstAttribute="top" secondItem="8Nj-TZ-G2W" secondAttribute="top" id="VQJ-WD-q2n"/>
                                            <constraint firstAttribute="bottom" secondItem="7Jk-1l-6SQ" secondAttribute="bottom" id="asr-bQ-X6z"/>
                                            <constraint firstItem="7Jk-1l-6SQ" firstAttribute="leading" secondItem="8Nj-TZ-G2W" secondAttribute="leading" id="iNC-ng-Kmy"/>
                                            <constraint firstItem="7Jk-1l-6SQ" firstAttribute="top" secondItem="8Nj-TZ-G2W" secondAttribute="top" id="mNt-ZN-MwL"/>
                                            <constraint firstAttribute="trailing" secondItem="7Jk-1l-6SQ" secondAttribute="trailing" id="oPt-za-awk"/>
                                            <constraint firstItem="dB7-SH-Ssb" firstAttribute="top" secondItem="8Nj-TZ-G2W" secondAttribute="top" id="olY-hL-bMK"/>
                                            <constraint firstAttribute="bottom" secondItem="dB7-SH-Ssb" secondAttribute="bottom" constant="7" id="qnD-kO-CO4"/>
                                        </constraints>
                                    </view>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Him-jv-EcE">
                                        <rect key="frame" x="0.0" y="33" width="414" height="105"/>
                                        <subviews>
                                            <collectionView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" dataMode="prototypes" translatesAutoresizingMaskIntoConstraints="NO" id="0ms-A9-574">
                                                <rect key="frame" x="10" y="0.0" width="394" height="105"/>
                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <collectionViewFlowLayout key="collectionViewLayout" scrollDirection="horizontal" automaticEstimatedItemSize="YES" minimumLineSpacing="0.0" minimumInteritemSpacing="0.0" id="ayr-Ek-IY4">
                                                    <size key="itemSize" width="205" height="105"/>
                                                    <size key="headerReferenceSize" width="0.0" height="0.0"/>
                                                    <size key="footerReferenceSize" width="0.0" height="0.0"/>
                                                    <inset key="sectionInset" minX="0.0" minY="0.0" maxX="0.0" maxY="0.0"/>
                                                </collectionViewFlowLayout>
                                                <cells>
                                                    <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" reuseIdentifier="LastOfferCollectionViewCell" id="sQG-pA-C0t">
                                                        <rect key="frame" x="0.0" y="0.0" width="205" height="105"/>
                                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                                        <collectionViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" insetsLayoutMarginsFromSafeArea="NO" id="HgE-ZJ-yiE">
                                                            <rect key="frame" x="0.0" y="0.0" width="205" height="105"/>
                                                            <autoresizingMask key="autoresizingMask"/>
                                                        </collectionViewCellContentView>
                                                    </collectionViewCell>
                                                </cells>
                                                <connections>
                                                    <outlet property="dataSource" destination="KGk-i7-Jjw" id="0KT-Dk-Bem"/>
                                                    <outlet property="delegate" destination="KGk-i7-Jjw" id="Cpv-2g-DCO"/>
                                                </connections>
                                            </collectionView>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstItem="0ms-A9-574" firstAttribute="top" secondItem="Him-jv-EcE" secondAttribute="top" id="3gJ-6W-gNn"/>
                                            <constraint firstItem="0ms-A9-574" firstAttribute="centerY" secondItem="Him-jv-EcE" secondAttribute="centerY" id="BB6-3E-nOa"/>
                                            <constraint firstAttribute="height" constant="105" id="lgu-Ob-rpD"/>
                                            <constraint firstItem="0ms-A9-574" firstAttribute="leading" secondItem="Him-jv-EcE" secondAttribute="leading" constant="10" id="mEG-Wf-7lA"/>
                                            <constraint firstItem="0ms-A9-574" firstAttribute="centerX" secondItem="Him-jv-EcE" secondAttribute="centerX" id="rHN-pk-IIg"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <constraints>
                                    <constraint firstItem="Him-jv-EcE" firstAttribute="leading" secondItem="QlD-4X-caE" secondAttribute="leading" id="B4f-z5-J4u"/>
                                    <constraint firstItem="Him-jv-EcE" firstAttribute="top" secondItem="8Nj-TZ-G2W" secondAttribute="bottom" constant="8" id="W6d-mn-prD"/>
                                    <constraint firstAttribute="bottom" secondItem="Him-jv-EcE" secondAttribute="bottom" id="rPc-dx-EPN"/>
                                    <constraint firstAttribute="trailing" secondItem="Him-jv-EcE" secondAttribute="trailing" id="vc8-1s-nsQ"/>
                                </constraints>
                            </stackView>
                        </subviews>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstAttribute="bottom" secondItem="QlD-4X-caE" secondAttribute="bottom" id="1zr-cW-eyq"/>
                            <constraint firstAttribute="trailing" secondItem="QlD-4X-caE" secondAttribute="trailing" id="T9t-AA-ejj"/>
                            <constraint firstItem="QlD-4X-caE" firstAttribute="leading" secondItem="A09-cs-etH" secondAttribute="leading" id="UX5-hF-Mza"/>
                            <constraint firstItem="QlD-4X-caE" firstAttribute="top" secondItem="A09-cs-etH" secondAttribute="top" constant="10" id="jIc-e7-cZ8"/>
                        </constraints>
                    </view>
                </subviews>
                <constraints>
                    <constraint firstItem="A09-cs-etH" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" id="RtW-mE-FXx"/>
                    <constraint firstItem="A09-cs-etH" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" id="cgI-hT-QDn"/>
                    <constraint firstAttribute="trailing" secondItem="A09-cs-etH" secondAttribute="trailing" id="eKf-Ui-zn3"/>
                    <constraint firstAttribute="bottom" secondItem="A09-cs-etH" secondAttribute="bottom" id="l5Q-fW-CQH"/>
                </constraints>
            </tableViewCellContentView>
            <viewLayoutGuide key="safeArea" id="njF-e1-oar"/>
            <connections>
                <outlet property="btnMore" destination="dB7-SH-Ssb" id="YsK-Ql-DT8"/>
                <outlet property="btnMoreOpenOffers" destination="7Jk-1l-6SQ" id="DY3-26-gOU"/>
                <outlet property="lastOfferCollectionView" destination="0ms-A9-574" id="H6f-8p-le9"/>
                <outlet property="lblTitle" destination="J1d-eu-frf" id="e23-IR-ymr"/>
            </connections>
            <point key="canvasLocation" x="80.916030534351137" y="19.718309859154932"/>
        </tableViewCell>
    </objects>
    <resources>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
