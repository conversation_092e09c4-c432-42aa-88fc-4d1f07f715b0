//
//  lastOfferCollectionViewCell.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 08/11/2023.
//  Copyright © 2023 SOTSYS203. All rights reserved.
//

import UIKit
import Kingfisher

class LastOfferCollectionViewCell: UICollectionViewCell {
    // MARK: - IBOutlets
    @IBOutlet weak var imageOffer: UIImageView!
    @IBOutlet weak var lblAppadvertisementTitle: MaterialLocalizeLable!
    @IBOutlet weak var viewBookmark: UIView!
    
    // MARK: - Func
    override func awakeFromNib() {
        super.awakeFromNib()
        // Initialization code
        self.lblAppadvertisementTitle.text = "Ad".localized
        self.setDefaultShadow()
    }
    
    func setupCell(imageUrl: String) {
        self.imageOffer.kf.indicatorType = .activity
        guard let url = URL(string: imageUrl) else {
            return
        }
        self.imageOffer.kf.setImage(with: url, placeholder: UIImage(named: "placeholder_product"))
    }
    
    func isAppadvertisement(value: Bool) {
        self.viewBookmark.isHidden = value
    }
    
}
