<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="21701" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21678"/>
        <capability name="Named colors" minToolsVersion="9.0"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="LoewNextArabic-Bold.ttf">
            <string>LoewNextArabic-Bold</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" reuseIdentifier="" id="gTV-IL-0wX" customClass="LastOfferCollectionViewCell" customModule="TopCustomer" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="221" height="95"/>
            <autoresizingMask key="autoresizingMask"/>
            <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center">
                <rect key="frame" x="0.0" y="0.0" width="221" height="95"/>
                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="uCD-IT-UJa">
                        <rect key="frame" x="8" y="0.0" width="205" height="95"/>
                        <subviews>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="redraw" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="placeholder_product" translatesAutoresizingMaskIntoConstraints="NO" id="EKS-WR-JZP">
                                <rect key="frame" x="0.0" y="0.0" width="205" height="95"/>
                            </imageView>
                            <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="DZC-kG-l1W">
                                <rect key="frame" x="0.0" y="0.0" width="30" height="18"/>
                                <subviews>
                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="sUI-Ka-g2z" customClass="MaterialLocalizeImageView" customModule="TopCustomer" customModuleProvider="target">
                                        <rect key="frame" x="0.0" y="0.0" width="30" height="18"/>
                                    </imageView>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Ad" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="NAN-Ic-mCx" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                        <rect key="frame" x="8.3333333333333321" y="4.6666666666666679" width="13.333333333333332" height="9"/>
                                        <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="9"/>
                                        <color key="textColor" name="AppTheme_DiffBlackColor_#101113"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="trailing" secondItem="sUI-Ka-g2z" secondAttribute="trailing" id="6Xw-WN-LW3"/>
                                    <constraint firstAttribute="width" constant="30" id="95v-gB-BMY"/>
                                    <constraint firstItem="sUI-Ka-g2z" firstAttribute="leading" secondItem="DZC-kG-l1W" secondAttribute="leading" id="Bf4-Dd-ilw"/>
                                    <constraint firstItem="sUI-Ka-g2z" firstAttribute="top" secondItem="DZC-kG-l1W" secondAttribute="top" id="H4U-yJ-NaM"/>
                                    <constraint firstItem="NAN-Ic-mCx" firstAttribute="centerY" secondItem="DZC-kG-l1W" secondAttribute="centerY" id="WsU-hc-axS"/>
                                    <constraint firstAttribute="height" constant="18" id="XeP-2k-EVs"/>
                                    <constraint firstAttribute="bottom" secondItem="sUI-Ka-g2z" secondAttribute="bottom" id="gOC-n7-JuA"/>
                                    <constraint firstItem="NAN-Ic-mCx" firstAttribute="centerX" secondItem="DZC-kG-l1W" secondAttribute="centerX" id="iC0-pG-dyq"/>
                                </constraints>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                        <real key="value" value="3"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                                        <real key="value" value="0.5"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                                        <color key="value" name="AppTheme_LightGrayColor_#A0A0A0"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </view>
                        </subviews>
                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="EKS-WR-JZP" firstAttribute="top" secondItem="uCD-IT-UJa" secondAttribute="top" id="Aco-zb-Rvu"/>
                            <constraint firstItem="DZC-kG-l1W" firstAttribute="leading" secondItem="uCD-IT-UJa" secondAttribute="leading" id="Oes-3g-UXR"/>
                            <constraint firstAttribute="height" constant="95" id="apv-JI-ZoH"/>
                            <constraint firstItem="EKS-WR-JZP" firstAttribute="leading" secondItem="uCD-IT-UJa" secondAttribute="leading" id="bQw-Ys-wwj"/>
                            <constraint firstAttribute="trailing" secondItem="EKS-WR-JZP" secondAttribute="trailing" id="c8u-ZG-psS"/>
                            <constraint firstAttribute="width" constant="205" id="csG-Qt-nvX"/>
                            <constraint firstItem="DZC-kG-l1W" firstAttribute="top" secondItem="uCD-IT-UJa" secondAttribute="top" id="iL4-b6-FvO"/>
                            <constraint firstAttribute="bottom" secondItem="EKS-WR-JZP" secondAttribute="bottom" id="y0K-Vl-r6G"/>
                        </constraints>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                <real key="value" value="7"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </view>
                </subviews>
            </view>
            <viewLayoutGuide key="safeArea" id="ZTg-uK-7eu"/>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstItem="uCD-IT-UJa" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" constant="8" id="Gbp-dy-ntm"/>
                <constraint firstItem="uCD-IT-UJa" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" id="Jxj-6s-flD"/>
                <constraint firstAttribute="trailing" secondItem="uCD-IT-UJa" secondAttribute="trailing" constant="8" id="U3u-2v-rUE"/>
                <constraint firstAttribute="bottom" secondItem="uCD-IT-UJa" secondAttribute="bottom" id="xYg-p0-my1"/>
            </constraints>
            <connections>
                <outlet property="imageOffer" destination="EKS-WR-JZP" id="5Rt-Cl-Qqh"/>
                <outlet property="lblAppadvertisementTitle" destination="NAN-Ic-mCx" id="JX9-YZ-Ugv"/>
                <outlet property="viewBookmark" destination="DZC-kG-l1W" id="bqo-wk-VXy"/>
            </connections>
            <point key="canvasLocation" x="51.908396946564885" y="19.718309859154932"/>
        </collectionViewCell>
    </objects>
    <resources>
        <image name="placeholder_product" width="80" height="73.666664123535156"/>
        <namedColor name="AppTheme_DiffBlackColor_#101113">
            <color red="0.062745098039215685" green="0.066666666666666666" blue="0.074509803921568626" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="AppTheme_LightGrayColor_#A0A0A0">
            <color red="0.62745098039215685" green="0.62745098039215685" blue="0.62745098039215685" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
    </resources>
</document>
