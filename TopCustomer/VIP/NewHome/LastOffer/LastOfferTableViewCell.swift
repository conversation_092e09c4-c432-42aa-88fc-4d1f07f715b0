//
//  LastOfferTableViewCell.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 08/11/2023.
//  Copyright © 2023 SOTSYS203. All rights reserved.
//

import UIKit
import Kingfisher

protocol LastOfferTableViewCellDelegate: AnyObject {
    func openOfferDetails(offerID: Int)
}

class LastOfferTableViewCell: UITableViewCell {
    
    // MARK: - IBOutlets
    @IBOutlet weak var lblTitle: UILabel!
    @IBOutlet weak var lastOfferCollectionView: UICollectionView!
    @IBOutlet weak var btnMore: UIButton!
    @IBOutlet weak var btnMoreOpenOffers: UIButton!
    // MARK: - Variables
    var lastOffers: [OfferResponseFields] = []
    weak var delegate: LastOfferTableViewCellDelegate?
    
    // MARK: - Func
    override func awakeFromNib() {
        super.awakeFromNib()
        // Initialization code
        self.setText()
        self.lastOfferCollectionView.delegate = self
        self.lastOfferCollectionView.dataSource = self
        self.lastOfferCollectionView.registerCell(cell: LastOfferCollectionViewCell.self)
        lastOfferCollectionView.semanticContentAttribute = UserDefaults.standard.isCurrentLanguageArabic() ? .forceRightToLeft : .forceLeftToRight
    }
    
    override func setSelected(_ selected: Bool, animated: Bool) {
        super.setSelected(selected, animated: animated)
        // Configure the view for the selected state
    }
    
    func setupCell(lastOffers: [OfferResponseFields]) {
        self.lblTitle.text = "Last Offers".localized
        self.lastOffers = lastOffers
    }
    
    private func setText() {
        let string = ObjKeymessages.kLABEL_MORE
        let range = (string as NSString).range(of: ObjKeymessages.kLABEL_MORE)
        let attributedString = NSMutableAttributedString(string: string)
        attributedString.addAttribute(NSAttributedString.Key.underlineStyle, value: NSNumber(value: 1), range: range)
        attributedString.addAttribute(NSAttributedString.Key.underlineColor, value: UIColor.black, range: range)
        attributedString.addAttribute(NSAttributedString.Key.font, value: UIFont(name: Fonts.LoewNextArabicMedium, size: 14)!, range: range)
        attributedString.addAttribute(NSAttributedString.Key.foregroundColor, value: UIColor.black, range: range)
        self.btnMore.setAttributedTitle(attributedString, for: .normal)
        self.lblTitle.text = "Last Offers".localized
        self.lblTitle.alignmentText()
    }
    
}

extension LastOfferTableViewCell: UICollectionViewDelegate, UICollectionViewDataSource, UICollectionViewDelegateFlowLayout {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return self.lastOffers.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeue(with: LastOfferCollectionViewCell.self, for: indexPath)
        cell.isAppadvertisement(value: self.lastOffers[indexPath.row].IsAppadvertisement == 1 ? false : true)
        cell.setupCell(imageUrl: self.lastOffers[indexPath.row].vOfferImage ?? "")
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        return CGSize(width: (self.lastOfferCollectionView.frame.width + 50) / 3, height: 95)
    }
    
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        self.delegate?.openOfferDetails(offerID: self.lastOffers[indexPath.row].iOfferId ?? 0)
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, insetForSectionAt section: Int) -> UIEdgeInsets {
        return UIEdgeInsets(top: 0, left: 0, bottom: 0, right: 0)
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, minimumLineSpacingForSectionAt section: Int) -> CGFloat {
        return 0
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, minimumInteritemSpacingForSectionAt section: Int) -> CGFloat {
        return 0
    }
    
}
