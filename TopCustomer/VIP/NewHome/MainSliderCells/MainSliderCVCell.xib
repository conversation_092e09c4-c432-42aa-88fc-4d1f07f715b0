<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="32700.99.1234" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22685"/>
        <capability name="Named colors" minToolsVersion="9.0"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="Lato-Medium.ttf">
            <string>Lato-Medium</string>
        </array>
        <array key="Lato-Semibold.ttf">
            <string>Lato-Semibold</string>
        </array>
        <array key="LoewNextArabic-Bold.ttf">
            <string>LoewNextArabic-Bold</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" reuseIdentifier="MainSliderCVCell" id="gTV-IL-0wX" customClass="MainSliderCVCell" customModule="TopCustomer" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="110" height="120"/>
            <autoresizingMask key="autoresizingMask"/>
            <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center">
                <rect key="frame" x="0.0" y="0.0" width="110" height="120"/>
                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="fj8-Re-FUO">
                        <rect key="frame" x="5" y="5" width="100" height="110"/>
                        <subviews>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="redraw" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="xRl-eA-PAB">
                                <rect key="frame" x="0.0" y="0.0" width="100" height="110"/>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="imageDirection">
                                        <integer key="value" value="1"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="boolean" keyPath="isSkeletonable" value="YES"/>
                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                        <real key="value" value="15"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </imageView>
                            <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="1tU-qw-sWW">
                                <rect key="frame" x="20" y="30" width="40" height="25"/>
                                <subviews>
                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="ZiR-1m-mat" customClass="MaterialLocalizeImageView" customModule="TopCustomer" customModuleProvider="target">
                                        <rect key="frame" x="0.0" y="0.0" width="40" height="25"/>
                                    </imageView>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Ad" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="4uD-0F-tVO" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                        <rect key="frame" x="13.333333333333336" y="8" width="13.333333333333336" height="9"/>
                                        <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="9"/>
                                        <color key="textColor" name="AppTheme_DiffBlackColor_#101113"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="25" id="4Ay-fV-LEb"/>
                                    <constraint firstAttribute="bottom" secondItem="ZiR-1m-mat" secondAttribute="bottom" id="7JN-y1-FMv"/>
                                    <constraint firstAttribute="width" constant="40" id="J2t-n1-24B"/>
                                    <constraint firstItem="ZiR-1m-mat" firstAttribute="leading" secondItem="1tU-qw-sWW" secondAttribute="leading" id="LWf-Cn-XLn"/>
                                    <constraint firstItem="4uD-0F-tVO" firstAttribute="centerY" secondItem="1tU-qw-sWW" secondAttribute="centerY" id="Xeb-HH-6On"/>
                                    <constraint firstAttribute="trailing" secondItem="ZiR-1m-mat" secondAttribute="trailing" id="icZ-Aa-YkC"/>
                                    <constraint firstItem="4uD-0F-tVO" firstAttribute="centerX" secondItem="1tU-qw-sWW" secondAttribute="centerX" id="mUa-RZ-6Cw"/>
                                    <constraint firstItem="ZiR-1m-mat" firstAttribute="top" secondItem="1tU-qw-sWW" secondAttribute="top" id="nVU-gA-De3"/>
                                </constraints>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                        <real key="value" value="3"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                                        <real key="value" value="0.5"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                                        <color key="value" name="AppTheme_LightGrayColor_#A0A0A0"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </view>
                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="5" translatesAutoresizingMaskIntoConstraints="NO" id="1gv-Kz-pYB">
                                <rect key="frame" x="10" y="10" width="80" height="0.0"/>
                                <subviews>
                                    <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Title" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="GAs-gR-tSR">
                                        <rect key="frame" x="0.0" y="0.0" width="102" height="0.0"/>
                                        <fontDescription key="fontDescription" name="Lato-Semibold" family="Lato" pointSize="28"/>
                                        <color key="textColor" name="AppColor1A"/>
                                        <nil key="highlightedColor"/>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="boolean" keyPath="isSkeletonable" value="YES"/>
                                        </userDefinedRuntimeAttributes>
                                    </label>
                                    <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Description" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="0tZ-pA-bQX">
                                        <rect key="frame" x="0.0" y="0.0" width="102" height="0.0"/>
                                        <fontDescription key="fontDescription" name="Lato-Medium" family="Lato" pointSize="20"/>
                                        <color key="textColor" name="AppColor1A"/>
                                        <nil key="highlightedColor"/>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="boolean" keyPath="isSkeletonable" value="YES"/>
                                        </userDefinedRuntimeAttributes>
                                    </label>
                                </subviews>
                            </stackView>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="ney-rF-1kH">
                                <rect key="frame" x="70" y="15" width="20" height="20"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="20" id="9Ut-rZ-iTU"/>
                                    <constraint firstAttribute="width" constant="20" id="z4M-hY-SMe"/>
                                </constraints>
                                <color key="tintColor" name="AppTheme_BlueColor_#012CDA"/>
                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                <state key="normal" image="share_round"/>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                                        <real key="value" value="0.0"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </button>
                            <imageView hidden="YES" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_play" translatesAutoresizingMaskIntoConstraints="NO" id="CEH-q9-PKo">
                                <rect key="frame" x="18" y="23" width="64" height="64"/>
                            </imageView>
                        </subviews>
                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="1gv-Kz-pYB" firstAttribute="leading" secondItem="fj8-Re-FUO" secondAttribute="leading" constant="10" id="4ly-IV-j5a"/>
                            <constraint firstItem="CEH-q9-PKo" firstAttribute="centerY" secondItem="fj8-Re-FUO" secondAttribute="centerY" id="5Ps-z2-eX3"/>
                            <constraint firstAttribute="height" constant="110" id="6d9-dL-3hu"/>
                            <constraint firstAttribute="trailing" secondItem="ney-rF-1kH" secondAttribute="trailing" constant="10" id="7EM-tC-9C8"/>
                            <constraint firstAttribute="trailing" secondItem="1gv-Kz-pYB" secondAttribute="trailing" constant="10" id="9ki-IX-Wue"/>
                            <constraint firstItem="xRl-eA-PAB" firstAttribute="top" secondItem="fj8-Re-FUO" secondAttribute="top" id="F1H-tX-Hf9"/>
                            <constraint firstAttribute="bottom" secondItem="xRl-eA-PAB" secondAttribute="bottom" id="Fe0-17-SnF"/>
                            <constraint firstItem="xRl-eA-PAB" firstAttribute="leading" secondItem="fj8-Re-FUO" secondAttribute="leading" id="LUg-Na-p4K"/>
                            <constraint firstItem="1tU-qw-sWW" firstAttribute="leading" secondItem="fj8-Re-FUO" secondAttribute="leading" constant="20" id="VMm-ew-fRO"/>
                            <constraint firstItem="CEH-q9-PKo" firstAttribute="centerX" secondItem="fj8-Re-FUO" secondAttribute="centerX" id="ayg-X8-f8b"/>
                            <constraint firstItem="ney-rF-1kH" firstAttribute="top" secondItem="fj8-Re-FUO" secondAttribute="top" constant="15" id="oo9-kT-Os7"/>
                            <constraint firstItem="1gv-Kz-pYB" firstAttribute="top" secondItem="fj8-Re-FUO" secondAttribute="top" constant="10" id="qTw-Hr-1nS"/>
                            <constraint firstAttribute="width" constant="100" id="tgO-RK-oTF"/>
                            <constraint firstAttribute="trailing" secondItem="xRl-eA-PAB" secondAttribute="trailing" id="uYj-pg-l7a"/>
                            <constraint firstItem="1tU-qw-sWW" firstAttribute="top" secondItem="1gv-Kz-pYB" secondAttribute="bottom" constant="20" id="yv0-Tb-4x6"/>
                        </constraints>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                <real key="value" value="15"/>
                            </userDefinedRuntimeAttribute>
                            <userDefinedRuntimeAttribute type="boolean" keyPath="isSkeletonable" value="YES"/>
                            <userDefinedRuntimeAttribute type="color" keyPath="shadowColor">
                                <color key="value" name="AppTheme_BorderColor_#1F1F1F"/>
                            </userDefinedRuntimeAttribute>
                            <userDefinedRuntimeAttribute type="size" keyPath="shadowOffset">
                                <size key="value" width="0.0" height="0.0"/>
                            </userDefinedRuntimeAttribute>
                            <userDefinedRuntimeAttribute type="number" keyPath="shadowBlur">
                                <real key="value" value="12"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </view>
                </subviews>
            </view>
            <viewLayoutGuide key="safeArea" id="ZTg-uK-7eu"/>
            <constraints>
                <constraint firstItem="fj8-Re-FUO" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" constant="5" id="1qM-v6-1pd"/>
                <constraint firstAttribute="trailing" secondItem="fj8-Re-FUO" secondAttribute="trailing" constant="5" id="Uvn-2c-XBO"/>
                <constraint firstAttribute="bottom" secondItem="fj8-Re-FUO" secondAttribute="bottom" constant="5" id="mAb-DQ-bKo"/>
                <constraint firstItem="fj8-Re-FUO" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" constant="5" id="wAy-lJ-7RN"/>
            </constraints>
            <userDefinedRuntimeAttributes>
                <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                    <real key="value" value="15"/>
                </userDefinedRuntimeAttribute>
            </userDefinedRuntimeAttributes>
            <connections>
                <outlet property="btnShare" destination="ney-rF-1kH" id="W9Q-IU-pIu"/>
                <outlet property="imageView" destination="xRl-eA-PAB" id="asf-kw-bdP"/>
                <outlet property="imgPlay" destination="CEH-q9-PKo" id="VYD-g2-XX7"/>
                <outlet property="lblAppadvertisementTitle" destination="4uD-0F-tVO" id="J3Z-Zi-EmM"/>
                <outlet property="lblDescription" destination="0tZ-pA-bQX" id="f7Q-JU-dZ4"/>
                <outlet property="lblTitle" destination="GAs-gR-tSR" id="2zS-BM-mDz"/>
                <outlet property="viewBookmark" destination="1tU-qw-sWW" id="1Z6-00-vuK"/>
            </connections>
            <point key="canvasLocation" x="-18" y="20"/>
        </collectionViewCell>
    </objects>
    <resources>
        <image name="ic_play" width="64" height="64"/>
        <image name="share_round" width="38" height="38"/>
        <namedColor name="AppColor1A">
            <color red="0.32899999618530273" green="0.32400000095367432" blue="0.4779999852180481" alpha="0.33000001311302185" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="AppTheme_BlueColor_#012CDA">
            <color red="0.0039215686274509803" green="0.17254901960784313" blue="0.85490196078431369" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="AppTheme_BorderColor_#1F1F1F">
            <color red="0.12156862745098039" green="0.12156862745098039" blue="0.12156862745098039" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="AppTheme_DiffBlackColor_#101113">
            <color red="0.062745098039215685" green="0.066666666666666666" blue="0.074509803921568626" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="AppTheme_LightGrayColor_#A0A0A0">
            <color red="0.62745098039215685" green="0.62745098039215685" blue="0.62745098039215685" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
    </resources>
</document>
