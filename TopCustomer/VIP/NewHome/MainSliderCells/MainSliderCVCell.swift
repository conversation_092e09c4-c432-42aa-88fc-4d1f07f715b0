//
//  MainSliderCVCell.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 10/10/2024.
//  Copyright © 2024 SOTSYS203. All rights reserved.
//

import UIKit

class MainSliderCVCell: UICollectionViewCell {

    @IBOutlet weak var imageView: UIImageView!
    @IBOutlet weak var lblTitle: UILabel!
    @IBOutlet weak var lblDescription: UILabel!
    @IBOutlet weak var imgPlay: UIImageView!
    @IBOutlet weak var btnShare: UIButton!
    @IBOutlet weak var lblAppadvertisementTitle: MaterialLocalizeLable!
    @IBOutlet weak var viewBookmark: UIView!
    

    override func awakeFromNib() {
        super.awakeFromNib()
        // Initialization code
        commonInit()
    }
    
    fileprivate func commonInit() {
        self.lblAppadvertisementTitle.text = "Ad".localized
        self.contentView.backgroundColor = UIColor.clear
        self.backgroundColor = UIColor.clear
    }
    
    func isAppadvertisement(value: Bool) {
        self.viewBookmark.isHidden = value
    }
}
