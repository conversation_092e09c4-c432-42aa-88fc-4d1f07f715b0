
import UIKit

protocol NewHomeInteractorProtocol {
    func apiCallForGetCategories(dictData:[String:Any], flagLoader: Bool)
    func apiCallForGetNewProducts(dictData:[String:Any], flagLoader: Bool, pageNo: Int, isOnlyBundle: Int)
    func apiCallForGetUserSpecificData()
    func apiCallForGetAdvertisements()
    func apiCallForSoftUpdate()
    func apiCallForGetFreeDeliveryValue()
    func apiCallForGetPointsLevels()
    func apiCallForCalculateUserPointsToSAR(points: Int)
    func apiCallForGetPointsDescription()
    func apiCallForAddToCart(dictData:[String:Any])
    func apiCallForGetSuperCategory(dictData:[String:Any])
}

protocol NewHomeDataStore {
    //{ get set }
}

class NewHomeInteractor: NewHomeInteractorProtocol, NewHomeDataStore {

    // MARK: Objects & Variables
    var presenterHome: NewHomePresentationProtocol?
 
    func apiCallForGetCategories(dictData:[String:Any], flagLoader: Bool) {

        if flagLoader == true {
            ActivityIndicator.shared.showCentralSpinner()
        }

        let authorization = User.shared.checkUserLoginStatus() ? getAuthorizationText() : GuestAccessToken
        
        ProductAPI.dynamicHomeListingBanner(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, iCategoryId: Int(setDataInString(dictData["iCategoryId"] as AnyObject)) ?? 0) { data, error in
            ActivityIndicator.shared.hideCentralSpinner()
            self.presenterHome?.apiResponseGetCategories(response: data, error: error)
        }
        
    }

    func apiCallForGetNewProducts(dictData:[String:Any], flagLoader: Bool, pageNo: Int, isOnlyBundle: Int) {
        
        if flagLoader == true {
            ActivityIndicator.shared.showCentralSpinner()
        }

        let authorization = User.shared.checkUserLoginStatus() ? getAuthorizationText() : GuestAccessToken

        ProductAPI.productListing(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, iCategoryId: Int(setDataInString(dictData["iCategoryId"] as AnyObject)) ?? 0, vSerchString: setDataInString(dictData["vSerchString"] as AnyObject), offset: Int(setDataInString(dictData["offset"] as AnyObject)) ?? 0, pageNo: pageNo, isOnlyBundle: isOnlyBundle) { data, error in
            ActivityIndicator.shared.hideCentralSpinner()
            self.presenterHome?.apiResponseGetNewProducts(response: data, error: error)
        }
        
    }

    func apiCallForGetUserSpecificData() {
        
        let authorization = User.shared.checkUserLoginStatus() ? getAuthorizationText() : GuestAccessToken

        UserAPI.userDetails(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang) { data, error in
            self.presenterHome?.apiResponseGetUserSpecificData(response: data, error: error)
        }
        
    }

    func apiCallForGetAdvertisements() {
        
        let authorization = User.shared.checkUserLoginStatus() ? getAuthorizationText() : GuestAccessToken
        
        AdvertisementAPI.advertisementShow(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang) { data, error in
            self.presenterHome?.apiResponseGetAdvertisements(response: data, error: error)
        }
        
    }

    func apiCallForSoftUpdate() {
        APPVersionCheckAPI.checkAppVersion(accept: AcceptParamForHeader, lang: CurrentAppLang, tiOsType: ._2) { data, error in
            self.presenterHome?.apiResponseForSoftUpdate(response: data, error: error)
        }
    }
    
    func apiCallForGetFreeDeliveryValue() {
        CartAPI.getFreeDeliveryValue() { data, error in
            self.presenterHome?.apiResponseForCallForGetFreeDeliveryValue(response: data, error: error)
        }
    }

    func apiCallForGetPointsLevels() {
        UserAPI.getPointsLevels() { data, error in
            self.presenterHome?.apiResponseForCallForGetPointsLevels(response: data, error: error)
        }
    }
    
    func apiCallForCalculateUserPointsToSAR(points: Int) {
        UserAPI.calculateUserPointsToSAR(points: points) { data, error in
            self.presenterHome?.apiResponseForCallForCalculateUserPointsToSAR(response: data, error: error)
        }
    }
    
    func apiCallForGetPointsDescription() {
        UserAPI.getPointsDescription { data, error in
            self.presenterHome?.apiResponseForCallForGetPointsDescription(response: data, error: error)
        }
    }

    func apiCallForAddToCart(dictData:[String:Any]) {
        ActivityIndicator.shared.showCentralSpinner()
        let authorization = getAuthorizationText()
        CartAPI.addToCart(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, biProductId: Int(setDataInString(dictData["biProductId"] as AnyObject)) ?? 0, tiIsCheck: Int(setDataInString(dictData["tiIsCheck"] as AnyObject)) ?? 0, iProductQuantity: Int(setDataInString(dictData["iProductQuantity"] as AnyObject)) ?? 0, dbPrice: setDataInString(dictData["dbPrice"] as AnyObject), dbBundleID: Int(setDataInString(dictData["bunddelId"] as AnyObject)) ?? 0) { data, error in
            ActivityIndicator.shared.hideCentralSpinner()
            self.presenterHome?.apiResponseAddToCart(response: data, error: error)
        }
    }
    
    func apiCallForGetSuperCategory(dictData:[String:Any]) {
        ProductAPI.newCategoryListing(categoryListId: String(setDataInString(dictData["iSuperCategoryId"] as AnyObject)) , pageNo: String(setDataInString(dictData["pageNo"] as AnyObject)) ) { data, error in
            ActivityIndicator.shared.hideCentralSpinner()
            self.presenterHome?.apiResponseGetSuperCategoryList(response: data, error: error)
        }
    }
    
}
