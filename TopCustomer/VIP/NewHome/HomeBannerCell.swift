
import UIKit

class HomeBannerCell: UITableViewCell {

    @IBOutlet weak var imgBanner: UIImageView!
    @IBOutlet weak var btnBannerAction: UIButton!
    @IBOutlet weak var btnShare: UIButton!
    @IBOutlet weak var lblTitle: MaterialLocalizeLable!
    @IBOutlet weak var viewBookmark: UIView!
    
    override func awakeFromNib() {
        super.awakeFromNib()
        // Initialization code
        self.lblTitle.text = "Ad".localized
    }

    override func setSelected(_ selected: <PERSON><PERSON>, animated: <PERSON><PERSON>) {
        super.setSelected(selected, animated: animated)
        // Configure the view for the selected state
    }

    func isAppadvertisement(value: Bool) {
        self.viewBookmark.isHidden = value
    }
    
}
