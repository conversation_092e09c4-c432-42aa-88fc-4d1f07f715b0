//
//  CategoriesTableViewCell.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 13/09/2023.
//  Copyright © 2023 SOTSYS203. All rights reserved.
//

import UIKit

protocol CategoriesTableViewCellDelegate: AnyObject {
    func didSelectRow(indexPath: IndexPath)
}

class CategoriesTableViewCell: UITableViewCell {
    
    // MARK: - IBOutlets
    @IBOutlet weak var collCategories: UICollectionView!
    
    // MARK: - Variables
    weak var delegate: CategoriesTableViewCellDelegate?
    var arrCategories: [CategoryResponseFields] = []
    var selectedCategoryIndex = -1
    
    // MARK: - Func
    override func awakeFromNib() {
        super.awakeFromNib()
        // Initialization code
        self.collCategories.registerCell(cell: CategoriesCollectionViewCell.self, bundle: .main)
        self.collCategories.delegate = self
        self.collCategories.dataSource = self
        
    }
    
    override func setSelected(_ selected: <PERSON><PERSON>, animated: <PERSON>ol) {
        super.setSelected(selected, animated: animated)
        // Configure the view for the selected state
    }
    
    func setupCell(arrCategories: [CategoryResponseFields]) {
        self.arrCategories = arrCategories
        self.collCategories.reloadData()
        
    }
}

// MARK: - Extension
extension CategoriesTableViewCell: UICollectionViewDelegate, UICollectionViewDataSource, UICollectionViewDelegateFlowLayout {
    
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return arrCategories.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeue(with: CategoriesCollectionViewCell.self, for: indexPath)
        
        cell.imgCategory.contentMode = .scaleAspectFit
        cell.isAppadvertisement(value: arrCategories[indexPath.row].IsAppadvertisement == 1 ? false : true)
        cell.imgCategory.kf.indicatorType = .activity
        let url = URL(string: arrCategories[indexPath.row].vImage ?? "")
        cell.imgCategory.kf.setImage(with: url , placeholder: UIImage(named: "placeholder_category"))
        
        cell.lblCategoryName.text = arrCategories[indexPath.row].vProductCategoryName
        
        // new changes
        if selectedCategoryIndex == indexPath.row {  // show category selected
            // apply blue border for selection
            cell.viewOuter.layer.borderWidth = 1.0
            cell.viewOuter.layer.borderColor = UIColor.AppTheme_BlueColor_012CDA.cgColor
        }
        else {
            cell.viewOuter.layer.borderWidth = 0.0
            cell.viewOuter.layer.borderColor = UIColor.clear.cgColor
        }
        return cell
        
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        return CGSize(width: (self.collCategories.frame.width - 35) / 5, height: collCategories.h)
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, minimumLineSpacingForSectionAt section: Int) -> CGFloat {
        return 5
    }
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        // get products for selected category from local array. Do not call API
        selectedCategoryIndex = indexPath.row
        delegate?.didSelectRow(indexPath: indexPath)
    }
    
}
