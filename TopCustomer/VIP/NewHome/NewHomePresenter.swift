
import UIKit

protocol NewHomePresentationProtocol {
    func apiCallForGetCategories(dictData:[String:Any], flagLoader: Bool)
    func apiResponseGetCategories(response:LatestHomeListingResponse?,error:Error?)

    func apiCallForGetNewProducts(dictData:[String:Any], flagLoader: Bool, pageNo: Int, isOnlyBundle: Int)
    func apiResponseGetNewProducts(response:ProductListResponse?,error:Error?)

    func apiCallForGetUserSpecificData()
    func apiResponseGetUserSpecificData(response:UserDetailListingResponse?,error:Error?)

    func apiCallForGetAdvertisements()
    func apiResponseGetAdvertisements(response:AdvertisementResponse?,error:Error?)

    func apiCallForSoftUpdate()
    func apiResponseForSoftUpdate(response:AppVersionResponse?,error:Error?)
    
    func apiCallForGetFreeDeliveryValue()
    func apiResponseForCallForGetFreeDeliveryValue(response:FreeDeliveryValueResponse?, error:Error?)
    
    func apiCallForGetPointsLevels()
    func apiResponseForCallForGetPointsLevels(response: PointsLevelsResponse?, error: Error?)
    
    func apiCalculateUserPointsToSAR(points: Int?)
    func apiResponseForCallForCalculateUserPointsToSAR(response: CalculateUserPointsToSARResponse?, error: Error?)
    
    func apiGetPointsDescription()
    func apiResponseForCallForGetPointsDescription(response: GetPointsDescriptionResponse?, error: Error?)
    
    func apiCallForAddToCart(dictData:[String:Any])
    func apiResponseAddToCart(response:AddToCartResponse?,error:Error?)
    
    func apiCallForGetSuperCategoryList(dictData:[String:Any])
    func apiResponseGetSuperCategoryList(response: SuperCategoryListResponseData?,error:Error?)
}

class NewHomePresenter: NewHomePresentationProtocol {
    
    // MARK: Objects & Variables
    weak var viewControllerHome: NewHomeProtocol?
    var interactorHome: NewHomeInteractorProtocol?
    
    func apiCallForGetCategories(dictData:[String:Any], flagLoader: Bool) {
        interactorHome?.apiCallForGetCategories(dictData: dictData, flagLoader: flagLoader)
    }

    func apiResponseGetCategories(response: LatestHomeListingResponse?, error: Error?) {
        if let vc = self.viewControllerHome as? BaseViewController {
            DispatchQueue.main.async {
                vc.endRefresing()
            }
        }
        if let error = error  {
            viewControllerHome?.displayAlert(string: error.localizedDescription)
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            return
        }
        
        if code == APIINVALIDAUTHORIZATIONCODE401 {
            AppSingletonObj.displaySessionExpiredAlert(strMsg: response.responseMessage ?? "")
            return
        }

        if code == APICODE400 {
            viewControllerHome?.displayAlert(string: response.responseMessage ?? "")
            return
        }
        
        guard let model = response.responseData,code == APISUCCESSCODE200  else {
            return
        }
        self.viewControllerHome?.refreshData(model: model)
    }

    func apiCallForGetNewProducts(dictData:[String:Any], flagLoader: Bool, pageNo: Int, isOnlyBundle: Int) {
        interactorHome?.apiCallForGetNewProducts(dictData: dictData, flagLoader: flagLoader,pageNo: pageNo, isOnlyBundle: isOnlyBundle)
    }

    func apiResponseGetNewProducts(response: ProductListResponse?, error: Error?) {
        if let vc = self.viewControllerHome as? BaseViewController {
            DispatchQueue.main.async {
                vc.endRefresing()
            }
        }
        if let error = error  {
            viewControllerHome?.displayAlert(string: error.localizedDescription)
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            return
        }
        
        if code == APIINVALIDAUTHORIZATIONCODE401 {
            AppSingletonObj.displaySessionExpiredAlert(strMsg: response.responseMessage ?? "")
            return
        }

        if code == APICODE400 {
            viewControllerHome?.displayAlert(string: response.responseMessage ?? "")
            return
        }
        
        guard let model = response.responseData,code == APISUCCESSCODE200  else {
            return
        }
        self.viewControllerHome?.refreshNewProducts(model: model)
    }

    func apiCallForGetUserSpecificData() {
        interactorHome?.apiCallForGetUserSpecificData()
    }

    func apiResponseGetUserSpecificData(response:UserDetailListingResponse?,error:Error?) {
        if let vc = self.viewControllerHome as? BaseViewController {
            DispatchQueue.main.async {
                vc.endRefresing()
            }
        }
        if let error = error  {
            viewControllerHome?.displayAlert(string: error.localizedDescription)
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            return
        }
        
        if code == APIINVALIDAUTHORIZATIONCODE401 {
            AppSingletonObj.displaySessionExpiredAlert(strMsg: response.responseMessage ?? "")
            return
        }

        if code == APICODE400 {
            viewControllerHome?.displayAlert(string: response.responseMessage ?? "")
            return
        }
        
        guard let model = response.responseData,code == APISUCCESSCODE200  else {
            return
        }
        self.viewControllerHome?.saveUserSpecificData(model: model)

    }

    func apiCallForGetAdvertisements() {
        interactorHome?.apiCallForGetAdvertisements()
    }

    func apiResponseGetAdvertisements(response:AdvertisementResponse?,error:Error?) {
        if let error = error  {
            viewControllerHome?.displayAlert(string: error.localizedDescription)
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            return
        }
        
        if code == APIINVALIDAUTHORIZATIONCODE401 {
            AppSingletonObj.displaySessionExpiredAlert(strMsg: response.responseMessage ?? "")
            return
        }

        if code == APICODE400 {
            viewControllerHome?.displayAlert(string: response.responseMessage ?? "")
            return
        }
        
        if code == APISUCCESSCODE200 {
            self.viewControllerHome?.showAdvertisementPopup(model: response.responseData)
        }

    }

    func apiCallForSoftUpdate() {
        interactorHome?.apiCallForSoftUpdate()
    }

    func apiResponseForSoftUpdate(response:AppVersionResponse?,error:Error?) {
        if let vc = self.viewControllerHome as? BaseViewController {
            DispatchQueue.main.async {
                vc.endRefresing()
            }
        }
        if let error = error  {
            viewControllerHome?.displayAlert(string: error.localizedDescription)
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            return
        }
        
        if code == APIINVALIDAUTHORIZATIONCODE401 {
            AppSingletonObj.displaySessionExpiredAlert(strMsg: response.responseMessage ?? "")
            return
        }

        if code == APICODE400 {
            viewControllerHome?.displayAlert(string: response.responseMessage ?? "")
            return
        }
        
        guard let model = response.responseData,code == APISUCCESSCODE200  else {
            return
        }
        self.viewControllerHome?.decideForSoftUpdateAlert(model: model, strMsg: response.responseMessage ?? "")

    }

    func apiCallForGetFreeDeliveryValue() {
        interactorHome?.apiCallForGetFreeDeliveryValue()
    }
    
    func apiResponseForCallForGetFreeDeliveryValue(response:FreeDeliveryValueResponse?, error:Error?) {
        if let vc = self.viewControllerHome as? BaseViewController {
            DispatchQueue.main.async {
                vc.endRefresing()
            }
        }
        if let error = error  {
            return
        }
        
        guard let response = response,let code = response.status_code else {
            return
        }
        
        guard let value = response.value,code == APISUCCESSCODE200  else {
            return
        }
        self.viewControllerHome?.getFreeDeliveryValue(value: value)
    }
    
    func apiCallForGetPointsLevels() {
        interactorHome?.apiCallForGetPointsLevels()
    }
    
    func apiResponseForCallForGetPointsLevels(response:PointsLevelsResponse?, error: Error?) {
        if let vc = self.viewControllerHome as? BaseViewController {
            DispatchQueue.main.async {
                vc.endRefresing()
            }
        }
        if let error = error  {
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            return
        }
        guard let value = response.responseData?.levels,code == APISUCCESSCODE200  else {
            return
        }
        self.viewControllerHome?.getPointsLevels(value: value.userPoint ?? 0, maxLevel: value.maxLevel ?? 0, remainingPoint: value.remainingPoint ?? 0, rewardPoint: value.rewardPoint ?? 0, levelPoint: value.levelPoint ?? 0, maxPoint: value.maxPoint ?? 0)
    }
    
    func apiCalculateUserPointsToSAR(points: Int? = 0) {
        interactorHome?.apiCallForCalculateUserPointsToSAR(points: points ?? 0)
    }
    
    func apiResponseForCallForCalculateUserPointsToSAR(response: CalculateUserPointsToSARResponse?, error: Error?) {
        if let vc = self.viewControllerHome as? BaseViewController {
            DispatchQueue.main.async {
                vc.endRefresing()
            }
        }
        if let error = error  {
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            return
        }
        
        if code != APISUCCESSCODE200 {
            return
        }
        
        self.viewControllerHome?.getCalculateUserPointsToSAR(model: CalculateUserPointsToSARResponseFields(pointsValueBySAR: response.pointsValueBySAR, userPoints: response.userPoints, userPointsBySAR: response.userPointsBySAR, totalUserPoints: response.totalUserPoints, totalUserPointsValueBySAR: response.totalUserPointsValueBySAR))
    }
    
    func apiGetPointsDescription() {
        interactorHome?.apiCallForGetPointsDescription()
    }
    
    func apiResponseForCallForGetPointsDescription(response: GetPointsDescriptionResponse?, error: Error?) {
        if let vc = self.viewControllerHome as? BaseViewController {
            DispatchQueue.main.async {
                vc.endRefresing()
            }
        }
        if let error = error  {
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            return
        }
        
        if code != APISUCCESSCODE200 {
            return
        }
        self.viewControllerHome?.getPointsDescription(model: response)
    }
    
    func apiCallForAddToCart(dictData:[String:Any]) {
        interactorHome?.apiCallForAddToCart(dictData: dictData)
    }

    func apiResponseAddToCart(response: AddToCartResponse?, error: Error?) {
        if let error = error  {
            viewControllerHome?.displayAlert(string: error.localizedDescription)
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            return
        }
        
        if code == APIINVALIDAUTHORIZATIONCODE401 {
            AppSingletonObj.displaySessionExpiredAlert(strMsg: response.responseMessage ?? "")
            return
        }
        
        if code == APICODE400 {
            viewControllerHome?.displayAlert(string: response.responseMessage ?? "")
            return
        }
        
        if code == APICODE203 {
            self.viewControllerHome?.displayAlert(string: response.responseMessage ?? "")
            return
        }
        
        guard let model = response.responseData,code == APISUCCESSCODE200  else {
            return
        }
        self.viewControllerHome?.displayAlertAndDismiss(string: response.responseMessage ?? "", cartCount: model.getCartProducts ?? 0)
    }
    
    func apiCallForGetSuperCategoryList(dictData:[String:Any]) {
        interactorHome?.apiCallForGetSuperCategory(dictData: dictData)
    }
    
    func apiResponseGetSuperCategoryList(response: SuperCategoryListResponseData?, error: Error?) {
        if let error = error  {
            viewControllerHome?.displayAlert(string: error.localizedDescription)
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            return
        }
        
        if code == APIINVALIDAUTHORIZATIONCODE401 {
            AppSingletonObj.displaySessionExpiredAlert(strMsg: response.responseMessage ?? "")
            return
        }
        
        if code == APICODE400 {
            viewControllerHome?.displayAlert(string: response.responseMessage ?? "")
            return
        }
        
        if code == APICODE203 {
            self.viewControllerHome?.displayAlert(string: response.responseMessage ?? "")
            return
        }
        
        guard let model = response.responseData,code == APISUCCESSCODE200  else {
            return
        }
        self.viewControllerHome?.getSuperCategoriesData(model: model)
    }
    
}
