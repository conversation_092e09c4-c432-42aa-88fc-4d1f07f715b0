<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="22505" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22504"/>
        <capability name="Named colors" minToolsVersion="9.0"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="Stack View standard spacing" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="LoewNextArabic-Bold.ttf">
            <string>LoewNextArabic-Bold</string>
        </array>
        <array key="LoewNextArabic-Medium.ttf">
            <string>LoewNextArabic-Medium</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" reuseIdentifier="BundelsListCollectionViewCell" id="gTV-IL-0wX" customClass="BundelsListCollectionViewCell" customModule="TopCustomer" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="150" height="195"/>
            <autoresizingMask key="autoresizingMask"/>
            <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center">
                <rect key="frame" x="0.0" y="0.0" width="150" height="195"/>
                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="6iB-Nb-uaK">
                        <rect key="frame" x="5" y="5" width="140" height="185"/>
                        <subviews>
                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacingType="standard" translatesAutoresizingMaskIntoConstraints="NO" id="zq7-yQ-XVw">
                                <rect key="frame" x="5" y="0.0" width="130" height="175"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="AJl-RW-Bnh">
                                        <rect key="frame" x="0.0" y="0.0" width="130" height="96"/>
                                        <subviews>
                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="redraw" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="placeholder_product" translatesAutoresizingMaskIntoConstraints="NO" id="m7n-26-4Qz">
                                                <rect key="frame" x="5" y="0.0" width="120" height="96"/>
                                            </imageView>
                                        </subviews>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="trailing" secondItem="m7n-26-4Qz" secondAttribute="trailing" constant="5" id="3ay-A9-FjF"/>
                                            <constraint firstItem="m7n-26-4Qz" firstAttribute="top" secondItem="AJl-RW-Bnh" secondAttribute="top" id="INE-cF-QNt"/>
                                            <constraint firstItem="m7n-26-4Qz" firstAttribute="leading" secondItem="AJl-RW-Bnh" secondAttribute="leading" constant="5" id="TIa-oe-6Yo"/>
                                            <constraint firstAttribute="bottom" secondItem="m7n-26-4Qz" secondAttribute="bottom" id="kjM-RL-oPI"/>
                                            <constraint firstAttribute="height" constant="96" id="xDT-3A-Sle"/>
                                        </constraints>
                                    </view>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="5OG-yY-5VC">
                                        <rect key="frame" x="0.0" y="104" width="130" height="0.6666666666666714"/>
                                        <color key="backgroundColor" name="AppTheme_LightGrayColor_#CECECE"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="0.75" id="qoG-1g-xUk"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                        <color key="textColor" name="AppTheme_StrikeThroughColor_#707070"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacingType="standard" translatesAutoresizingMaskIntoConstraints="NO" id="hjt-jg-khF">
                                        <rect key="frame" x="0.0" y="112.66666666666667" width="130" height="62.333333333333329"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="252" verticalCompressionResistancePriority="749" text="Arwa Water 330ml" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="7xT-IJ-wec" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                <rect key="frame" x="0.0" y="0.0" width="130" height="10"/>
                                                <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="10"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="252" verticalCompressionResistancePriority="749" text="Arwa Water 330ml" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="gTC-WS-zo5" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                <rect key="frame" x="0.0" y="17.999999999999986" width="130" height="8"/>
                                                <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="8"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="SAR 19.00" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="8Qn-mN-xPa" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                <rect key="frame" x="0.0" y="33.999999999999986" width="130" height="12.333333333333336"/>
                                                <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="12"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <stackView opaque="NO" contentMode="scaleToFill" spacing="3" translatesAutoresizingMaskIntoConstraints="NO" id="Pvz-UL-uOb">
                                                <rect key="frame" x="0.0" y="54.333333333333329" width="130" height="8"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="1000" verticalHuggingPriority="251" horizontalCompressionResistancePriority="1000" ambiguous="YES" text="19.00 SAR" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="0GK-Ej-djF" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="0.0" width="41.666666666666664" height="8"/>
                                                        <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="8"/>
                                                        <color key="textColor" name="AppTheme_RedColor_#D82828"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="1000" verticalHuggingPriority="251" horizontalCompressionResistancePriority="1000" ambiguous="YES" text="discount off" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Nkq-zq-85v" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                        <rect key="frame" x="44.666666666666664" y="0.0" width="85.333333333333343" height="8"/>
                                                        <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="8"/>
                                                        <color key="textColor" name="AppTheme_RedColor_#D82828"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                </subviews>
                                            </stackView>
                                        </subviews>
                                    </stackView>
                                    <button hidden="YES" opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="tailTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="ePO-jj-5TW" customClass="CustomRoundedButtton" customModule="TopCustomer" customModuleProvider="target">
                                        <rect key="frame" x="0.0" y="179" width="130" height="25"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="25" id="OeJ-0T-nOy"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="9"/>
                                        <inset key="contentEdgeInsets" minX="10" minY="0.0" maxX="10" maxY="0.0"/>
                                        <state key="normal" title="Add  bundle to cart"/>
                                    </button>
                                </subviews>
                            </stackView>
                            <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="9fm-qs-M3i">
                                <rect key="frame" x="0.0" y="0.0" width="30" height="18"/>
                                <subviews>
                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="R0p-FQ-jNy" customClass="MaterialLocalizeImageView" customModule="TopCustomer" customModuleProvider="target">
                                        <rect key="frame" x="0.0" y="0.0" width="30" height="18"/>
                                    </imageView>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Ad" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="bkQ-Eo-37a" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                        <rect key="frame" x="8.3333333333333321" y="4.6666666666666661" width="13.333333333333332" height="9"/>
                                        <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="9"/>
                                        <color key="textColor" name="AppTheme_DiffBlackColor_#101113"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="bkQ-Eo-37a" firstAttribute="centerY" secondItem="9fm-qs-M3i" secondAttribute="centerY" id="4zi-QB-24s"/>
                                    <constraint firstAttribute="bottom" secondItem="R0p-FQ-jNy" secondAttribute="bottom" id="BUi-Fn-7ZI"/>
                                    <constraint firstAttribute="width" constant="30" id="SjL-u6-Olf"/>
                                    <constraint firstItem="R0p-FQ-jNy" firstAttribute="top" secondItem="9fm-qs-M3i" secondAttribute="top" id="fMM-Bs-sO2"/>
                                    <constraint firstItem="R0p-FQ-jNy" firstAttribute="leading" secondItem="9fm-qs-M3i" secondAttribute="leading" id="gVV-uD-6MD"/>
                                    <constraint firstItem="bkQ-Eo-37a" firstAttribute="centerX" secondItem="9fm-qs-M3i" secondAttribute="centerX" id="mNa-MG-mEm"/>
                                    <constraint firstAttribute="height" constant="18" id="yUl-iX-MMa"/>
                                    <constraint firstAttribute="trailing" secondItem="R0p-FQ-jNy" secondAttribute="trailing" id="ylQ-d0-uiC"/>
                                </constraints>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                        <real key="value" value="3"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                                        <real key="value" value="0.5"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                                        <color key="value" name="AppTheme_LightGrayColor_#A0A0A0"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </view>
                        </subviews>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstAttribute="width" constant="140" id="1ST-cm-R4o"/>
                            <constraint firstItem="9fm-qs-M3i" firstAttribute="top" secondItem="6iB-Nb-uaK" secondAttribute="top" id="2FO-fN-7rz"/>
                            <constraint firstItem="zq7-yQ-XVw" firstAttribute="top" secondItem="6iB-Nb-uaK" secondAttribute="top" id="5v2-UF-0eA"/>
                            <constraint firstItem="9fm-qs-M3i" firstAttribute="leading" secondItem="6iB-Nb-uaK" secondAttribute="leading" id="ImW-S2-n1F"/>
                            <constraint firstItem="zq7-yQ-XVw" firstAttribute="leading" secondItem="6iB-Nb-uaK" secondAttribute="leading" constant="5" id="Lgt-Mn-Xd4"/>
                            <constraint firstAttribute="bottom" secondItem="zq7-yQ-XVw" secondAttribute="bottom" constant="10" id="MIC-C7-YBQ"/>
                            <constraint firstAttribute="trailing" secondItem="zq7-yQ-XVw" secondAttribute="trailing" constant="5" id="q4o-NS-YhR"/>
                            <constraint firstAttribute="height" constant="185" id="y7d-Z3-JpT"/>
                        </constraints>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                <real key="value" value="10"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </view>
                    <view hidden="YES" userInteractionEnabled="NO" alpha="0.64999997615814209" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="hBg-C5-R4u">
                        <rect key="frame" x="5" y="5" width="140" height="185"/>
                        <color key="backgroundColor" white="0.0" alpha="0.20000000000000001" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                <real key="value" value="7"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </view>
                </subviews>
            </view>
            <viewLayoutGuide key="safeArea" id="ZTg-uK-7eu"/>
            <constraints>
                <constraint firstAttribute="trailing" secondItem="hBg-C5-R4u" secondAttribute="trailing" constant="5" id="4lX-fL-MjA"/>
                <constraint firstAttribute="bottom" secondItem="6iB-Nb-uaK" secondAttribute="bottom" constant="5" id="Gps-dt-UWi"/>
                <constraint firstAttribute="trailing" secondItem="6iB-Nb-uaK" secondAttribute="trailing" constant="5" id="Te6-Dt-bzt"/>
                <constraint firstAttribute="bottom" secondItem="hBg-C5-R4u" secondAttribute="bottom" constant="5" id="UtD-w8-pXc"/>
                <constraint firstItem="hBg-C5-R4u" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" constant="5" id="dFU-7G-pyV"/>
                <constraint firstItem="6iB-Nb-uaK" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" constant="5" id="hBy-LH-WBx"/>
                <constraint firstItem="6iB-Nb-uaK" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" constant="5" id="kI8-A7-LaI"/>
                <constraint firstItem="hBg-C5-R4u" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" constant="5" id="tUe-Mk-kxe"/>
            </constraints>
            <connections>
                <outlet property="btnAddToCart" destination="ePO-jj-5TW" id="GWq-qq-Nja"/>
                <outlet property="discountStackView" destination="Pvz-UL-uOb" id="wfr-ag-Jry"/>
                <outlet property="imageBundle" destination="m7n-26-4Qz" id="PTB-eK-Yd6"/>
                <outlet property="lblAppadvertisementTitle" destination="bkQ-Eo-37a" id="ijY-s2-cJZ"/>
                <outlet property="lblBundleDescription" destination="gTC-WS-zo5" id="qG7-SU-8MB"/>
                <outlet property="lblBundleName" destination="7xT-IJ-wec" id="GyU-1l-DIv"/>
                <outlet property="lblDiscountOff" destination="Nkq-zq-85v" id="j5h-QF-F35"/>
                <outlet property="lblDiscountPrice" destination="0GK-Ej-djF" id="56B-c3-n6i"/>
                <outlet property="lblPrice" destination="8Qn-mN-xPa" id="sCD-FU-FHk"/>
                <outlet property="viewBookmark" destination="9fm-qs-M3i" id="5LT-DS-g6B"/>
                <outlet property="viewOutOfStock" destination="hBg-C5-R4u" id="2K4-VS-a3H"/>
            </connections>
            <point key="canvasLocation" x="-154" y="-12"/>
        </collectionViewCell>
    </objects>
    <resources>
        <image name="placeholder_product" width="80" height="73.666664123535156"/>
        <namedColor name="AppTheme_DiffBlackColor_#101113">
            <color red="0.062745098039215685" green="0.066666666666666666" blue="0.074509803921568626" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="AppTheme_LightGrayColor_#A0A0A0">
            <color red="0.62745098039215685" green="0.62745098039215685" blue="0.62745098039215685" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="AppTheme_LightGrayColor_#CECECE">
            <color red="0.80784313725490198" green="0.80784313725490198" blue="0.80784313725490198" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="AppTheme_RedColor_#D82828">
            <color red="0.84705882352941175" green="0.15686274509803921" blue="0.15686274509803921" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="AppTheme_StrikeThroughColor_#707070">
            <color red="0.4392156862745098" green="0.4392156862745098" blue="0.4392156862745098" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
    </resources>
</document>
