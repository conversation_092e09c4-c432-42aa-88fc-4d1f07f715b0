//
//  ProductsOfBundleTVCell.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 23/05/2024.
//  Copyright © 2024 SOTSYS203. All rights reserved.
//

import UIKit
import Kingfisher

class ProductsOfBundleTVCell: UITableViewCell {

    // MARK: - IBOutlets
    @IBOutlet weak var viewMain: UIView!
    @IBOutlet weak var lblProductCount: UILabel!
    @IBOutlet weak var lblProductName: UILabel!
    @IBOutlet weak var lblProductUnit: UILabel!
    @IBOutlet weak var imgProduct: UIImageView!
    
    // MARK: Func
    override func awakeFromNib() {
        super.awakeFromNib()
        // Initialization code
        self.viewMain.addShadow()
    }
    
    override func setSelected(_ selected: Bool, animated: Bool) {
        super.setSelected(selected, animated: animated)
        // Configure the view for the selected state
    }
    
    func setupCell(productCount: String, productName: String, productUnit: String, productImageUrl: String) {
        [lblProductName, lblProductUnit].forEach { label in
            label?.numberOfLines = 0
            label?.sizeToFit()
            label?.alignmentText()
        }
        if UserDefaults.standard.isCurrentLanguageArabic() {
            self.lblProductCount.text = "x\(productCount)"
        } else {
            self.lblProductCount.text = "\(productCount)x"
        }
        self.lblProductName.text = productName
        self.lblProductUnit.text = productUnit
        
        self.imgProduct.kf.indicatorType = .activity
        guard let url = URL(string: productImageUrl) else {
            return
        }
        self.imgProduct.kf.setImage(with: url, placeholder: UIImage(named: "placeholder_product"))
    }
    
}
