<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="22505" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22504"/>
        <capability name="Named colors" minToolsVersion="9.0"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="LoewNextArabic-Bold.ttf">
            <string>LoewNextArabic-Bold</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" reuseIdentifier="ProductsOfBundleTVCell" rowHeight="70" id="KGk-i7-Jjw" customClass="ProductsOfBundleTVCell" customModule="TopCustomer" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="375" height="70"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="375" height="70"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <view opaque="NO" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ZHT-E4-u69">
                        <rect key="frame" x="5" y="5" width="365" height="60"/>
                        <subviews>
                            <stackView opaque="NO" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="qAa-k6-NmR">
                                <rect key="frame" x="5" y="20" width="355" height="20"/>
                                <subviews>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="LxV-rD-wkb">
                                        <rect key="frame" x="0.0" y="0.0" width="25" height="20"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="25" id="fFM-52-29O"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="12"/>
                                        <color key="textColor" name="AppTheme_BorderColor_#1F1F1F"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="FXK-0Q-qNt">
                                        <rect key="frame" x="25" y="0.0" width="30" height="20"/>
                                        <subviews>
                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="logo_login" translatesAutoresizingMaskIntoConstraints="NO" id="dTm-Fw-YKG">
                                                <rect key="frame" x="2.6666666666666643" y="-2.3333333333333321" width="25" height="25"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="25" id="JIY-2Y-MOU"/>
                                                    <constraint firstAttribute="height" constant="25" id="aMp-MV-rAW"/>
                                                </constraints>
                                            </imageView>
                                        </subviews>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="30" id="955-Jg-4Zz"/>
                                            <constraint firstItem="dTm-Fw-YKG" firstAttribute="centerX" secondItem="FXK-0Q-qNt" secondAttribute="centerX" id="aeo-jg-k4V"/>
                                            <constraint firstItem="dTm-Fw-YKG" firstAttribute="centerY" secondItem="FXK-0Q-qNt" secondAttribute="centerY" id="nS0-0O-kO9"/>
                                        </constraints>
                                    </view>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" ambiguous="YES" text="" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="h5a-D7-Ogd">
                                        <rect key="frame" x="54.999999999999986" y="0.0" width="177.66666666666663" height="20"/>
                                        <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="12"/>
                                        <color key="textColor" name="AppTheme_BorderColor_#1F1F1F"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" ambiguous="YES" text="" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Kh2-hf-mHn">
                                        <rect key="frame" x="232.66666666666666" y="0.0" width="122.33333333333334" height="20"/>
                                        <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="12"/>
                                        <color key="textColor" name="AppTheme_BorderColor_#1F1F1F"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            </stackView>
                        </subviews>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="qAa-k6-NmR" firstAttribute="top" secondItem="ZHT-E4-u69" secondAttribute="top" constant="20" id="0JD-34-kiu"/>
                            <constraint firstAttribute="trailing" secondItem="qAa-k6-NmR" secondAttribute="trailing" constant="5" id="FKi-xo-DN2"/>
                            <constraint firstItem="qAa-k6-NmR" firstAttribute="leading" secondItem="ZHT-E4-u69" secondAttribute="leading" constant="5" id="Yvm-CY-eiU"/>
                            <constraint firstAttribute="bottom" secondItem="qAa-k6-NmR" secondAttribute="bottom" constant="20" id="i2a-Bq-lcA"/>
                        </constraints>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                                <color key="value" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            </userDefinedRuntimeAttribute>
                            <userDefinedRuntimeAttribute type="color" keyPath="shadowColor">
                                <color key="value" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </view>
                </subviews>
                <constraints>
                    <constraint firstItem="ZHT-E4-u69" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="5" id="1dF-s4-EVL"/>
                    <constraint firstItem="ZHT-E4-u69" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" constant="5" id="AGG-nG-bXj"/>
                    <constraint firstAttribute="trailing" secondItem="ZHT-E4-u69" secondAttribute="trailing" constant="5" id="LH5-61-wg6"/>
                    <constraint firstAttribute="bottom" secondItem="ZHT-E4-u69" secondAttribute="bottom" constant="5" id="yn2-ny-L4H"/>
                </constraints>
            </tableViewCellContentView>
            <viewLayoutGuide key="safeArea" id="njF-e1-oar"/>
            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <connections>
                <outlet property="imgProduct" destination="dTm-Fw-YKG" id="xFV-Lf-J81"/>
                <outlet property="lblProductCount" destination="LxV-rD-wkb" id="KGe-RX-N7a"/>
                <outlet property="lblProductName" destination="h5a-D7-Ogd" id="Ql3-Zp-byv"/>
                <outlet property="lblProductUnit" destination="Kh2-hf-mHn" id="ExP-7u-p6A"/>
                <outlet property="viewMain" destination="ZHT-E4-u69" id="n4q-Bj-fhe"/>
            </connections>
            <point key="canvasLocation" x="14" y="20"/>
        </tableViewCell>
    </objects>
    <resources>
        <image name="logo_login" width="100.66666412353516" height="33"/>
        <namedColor name="AppTheme_BorderColor_#1F1F1F">
            <color red="0.12156862745098039" green="0.12156862745098039" blue="0.12156862745098039" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
    </resources>
</document>
