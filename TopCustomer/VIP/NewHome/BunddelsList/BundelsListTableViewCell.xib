<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="22505" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22504"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="collection view cell content view" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="LoewNextArabic-Bold.ttf">
            <string>LoewNextArabic-Bold</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" reuseIdentifier="BundelsListTableViewCell" rowHeight="260" id="KGk-i7-Jjw" customClass="BundelsListTableViewCell" customModule="TopCustomer" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="414" height="260"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="414" height="260"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="bc2-Jg-9hy">
                        <rect key="frame" x="5" y="5" width="404" height="250"/>
                        <subviews>
                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="fillProportionally" translatesAutoresizingMaskIntoConstraints="NO" id="Dsu-eJ-aPu">
                                <rect key="frame" x="0.0" y="0.0" width="404" height="250"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Dqf-7X-pdb">
                                        <rect key="frame" x="0.0" y="0.0" width="404" height="25"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Title" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Dce-pX-0qG">
                                                <rect key="frame" x="20" y="0.0" width="364" height="18"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="18" id="iya-CH-RSY"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="20"/>
                                                <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="right" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" hasAttributedTitle="YES" translatesAutoresizingMaskIntoConstraints="NO" id="fvF-gk-QKg">
                                                <rect key="frame" x="345" y="0.0" width="39" height="18"/>
                                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                <state key="normal">
                                                    <attributedString key="attributedTitle">
                                                        <fragment content="More">
                                                            <attributes>
                                                                <color key="NSColor" name="textColor" catalog="System" colorSpace="catalog"/>
                                                                <font key="NSFont" size="14" name="LoewNextArabic-Medium"/>
                                                                <paragraphStyle key="NSParagraphStyle" alignment="natural" lineBreakMode="wordWrapping" baseWritingDirection="natural" tighteningFactorForTruncation="0.0"/>
                                                                <integer key="NSUnderline" value="1"/>
                                                            </attributes>
                                                        </fragment>
                                                    </attributedString>
                                                </state>
                                            </button>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="hvq-02-SIg">
                                                <rect key="frame" x="0.0" y="0.0" width="404" height="25"/>
                                                <state key="normal" title="Button"/>
                                                <buttonConfiguration key="configuration" style="plain" title=" "/>
                                            </button>
                                        </subviews>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="bottom" secondItem="Dce-pX-0qG" secondAttribute="bottom" constant="7" id="54n-dg-WuY"/>
                                            <constraint firstAttribute="height" constant="25" id="JNa-wP-dc0"/>
                                            <constraint firstAttribute="trailing" secondItem="Dce-pX-0qG" secondAttribute="trailing" constant="20" id="W2l-Jy-UVr"/>
                                            <constraint firstItem="fvF-gk-QKg" firstAttribute="top" secondItem="Dqf-7X-pdb" secondAttribute="top" id="WOe-nO-OJs"/>
                                            <constraint firstItem="Dce-pX-0qG" firstAttribute="top" secondItem="Dqf-7X-pdb" secondAttribute="top" id="cFr-WY-raK"/>
                                            <constraint firstAttribute="trailing" secondItem="fvF-gk-QKg" secondAttribute="trailing" constant="20" id="f2s-12-ZAf"/>
                                            <constraint firstAttribute="trailing" secondItem="hvq-02-SIg" secondAttribute="trailing" id="fmT-G0-hop"/>
                                            <constraint firstItem="Dce-pX-0qG" firstAttribute="leading" secondItem="Dqf-7X-pdb" secondAttribute="leading" constant="20" id="fw6-uS-wZ7"/>
                                            <constraint firstItem="hvq-02-SIg" firstAttribute="top" secondItem="Dqf-7X-pdb" secondAttribute="top" id="gP0-Pi-cod"/>
                                            <constraint firstItem="hvq-02-SIg" firstAttribute="leading" secondItem="Dqf-7X-pdb" secondAttribute="leading" id="i43-FG-SWV"/>
                                            <constraint firstAttribute="bottom" secondItem="fvF-gk-QKg" secondAttribute="bottom" constant="7" id="iaj-Fg-jro"/>
                                            <constraint firstAttribute="bottom" secondItem="hvq-02-SIg" secondAttribute="bottom" id="pvf-XM-aAc"/>
                                        </constraints>
                                    </view>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Z32-YX-yet">
                                        <rect key="frame" x="0.0" y="25" width="404" height="225"/>
                                        <subviews>
                                            <collectionView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" dataMode="prototypes" translatesAutoresizingMaskIntoConstraints="NO" id="p7g-2q-i2S">
                                                <rect key="frame" x="0.0" y="0.0" width="404" height="225"/>
                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <collectionViewFlowLayout key="collectionViewLayout" scrollDirection="horizontal" automaticEstimatedItemSize="YES" minimumLineSpacing="0.0" minimumInteritemSpacing="0.0" id="y2V-qX-ptI">
                                                    <size key="itemSize" width="128" height="193"/>
                                                    <size key="headerReferenceSize" width="0.0" height="0.0"/>
                                                    <size key="footerReferenceSize" width="0.0" height="0.0"/>
                                                    <inset key="sectionInset" minX="0.0" minY="0.0" maxX="0.0" maxY="0.0"/>
                                                </collectionViewFlowLayout>
                                                <cells>
                                                    <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" reuseIdentifier="LastOfferCollectionViewCell" id="szd-Si-ka3">
                                                        <rect key="frame" x="0.0" y="16" width="128" height="193"/>
                                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                                        <collectionViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" insetsLayoutMarginsFromSafeArea="NO" id="1Db-I9-nBB">
                                                            <rect key="frame" x="0.0" y="0.0" width="128" height="193"/>
                                                            <autoresizingMask key="autoresizingMask"/>
                                                        </collectionViewCellContentView>
                                                    </collectionViewCell>
                                                </cells>
                                            </collectionView>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstAttribute="trailing" secondItem="p7g-2q-i2S" secondAttribute="trailing" id="Dt6-1m-5KE"/>
                                            <constraint firstItem="p7g-2q-i2S" firstAttribute="top" secondItem="Z32-YX-yet" secondAttribute="top" id="Lbd-xE-Ahu"/>
                                            <constraint firstAttribute="height" relation="greaterThanOrEqual" constant="200" id="NtS-Gy-rnW"/>
                                            <constraint firstAttribute="bottom" secondItem="p7g-2q-i2S" secondAttribute="bottom" id="ZO0-ab-ugx"/>
                                            <constraint firstItem="p7g-2q-i2S" firstAttribute="leading" secondItem="Z32-YX-yet" secondAttribute="leading" id="dTr-EA-qOL"/>
                                        </constraints>
                                    </view>
                                </subviews>
                            </stackView>
                        </subviews>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstAttribute="trailing" secondItem="Dsu-eJ-aPu" secondAttribute="trailing" id="Olm-8k-VlX"/>
                            <constraint firstAttribute="height" constant="250" id="QMm-6z-qis"/>
                            <constraint firstItem="Dsu-eJ-aPu" firstAttribute="top" secondItem="bc2-Jg-9hy" secondAttribute="top" id="TZz-9j-AQv"/>
                            <constraint firstAttribute="bottom" secondItem="Dsu-eJ-aPu" secondAttribute="bottom" id="lpH-Ou-Zeu"/>
                            <constraint firstItem="Dsu-eJ-aPu" firstAttribute="leading" secondItem="bc2-Jg-9hy" secondAttribute="leading" id="x6u-LH-wXz"/>
                        </constraints>
                    </view>
                </subviews>
                <constraints>
                    <constraint firstItem="bc2-Jg-9hy" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" constant="5" id="85g-t1-xjC"/>
                    <constraint firstItem="bc2-Jg-9hy" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="5" id="Iz9-VA-oeY"/>
                    <constraint firstAttribute="bottom" secondItem="bc2-Jg-9hy" secondAttribute="bottom" constant="5" id="Zzj-zq-len"/>
                    <constraint firstAttribute="trailing" secondItem="bc2-Jg-9hy" secondAttribute="trailing" constant="5" id="mRJ-Di-tgM"/>
                </constraints>
            </tableViewCellContentView>
            <viewLayoutGuide key="safeArea" id="njF-e1-oar"/>
            <connections>
                <outlet property="btnMore" destination="fvF-gk-QKg" id="8mw-sR-IR5"/>
                <outlet property="btnMoreOpenOffers" destination="hvq-02-SIg" id="sVT-Cv-HZI"/>
                <outlet property="bundelsCollectionView" destination="p7g-2q-i2S" id="gdk-VB-ora"/>
                <outlet property="lblTitle" destination="Dce-pX-0qG" id="NvG-Nv-vvM"/>
            </connections>
            <point key="canvasLocation" x="52" y="21"/>
        </tableViewCell>
    </objects>
    <resources>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
