//
//  BunddelsListTableViewCell.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 21/02/2024.
//  Copyright © 2024 SOTSYS203. All rights reserved.
//

import UIKit
import Kingfisher

protocol BundelsListTableViewCellDelegate: AnyObject {
    func openBundelsListDetails(bundleID: Int, index: Int)
    func addBundleToCart(bundle: Int)
}

class BundelsListTableViewCell: UITableViewCell {

    // MARK: - IBOutlets
    @IBOutlet weak var lblTitle: UILabel!
    @IBOutlet weak var bundelsCollectionView: UICollectionView!
    @IBOutlet weak var btnMore: UIButton!
    @IBOutlet weak var btnMoreOpenOffers: UIButton!
    // MARK: - Variables
    var bundelsList: [BundelsListResponseFields] = []
    weak var delegate: BundelsListTableViewCellDelegate?
    
    // MARK: - Func
    override func awakeFromNib() {
        super.awakeFromNib()
        // Initialization code
        self.setText()
        self.bundelsCollectionView.delegate = self
        self.bundelsCollectionView.dataSource = self
        self.bundelsCollectionView.registerCell(cell: BundelsListCollectionViewCell.self)
    }
    
    override func setSelected(_ selected: Bool, animated: Bool) {
        super.setSelected(selected, animated: animated)
        // Configure the view for the selected state
    }
    
    func setupCell(bundels: [BundelsListResponseFields]) {
        self.lblTitle.text = "bundles".localized
        self.bundelsList = bundels
    }
    
    private func setText() {
        let string = ObjKeymessages.kLABEL_MORE
        let range = (string as NSString).range(of: ObjKeymessages.kLABEL_MORE)
        let attributedString = NSMutableAttributedString(string: string)
        attributedString.addAttribute(NSAttributedString.Key.underlineStyle, value: NSNumber(value: 1), range: range)
        attributedString.addAttribute(NSAttributedString.Key.underlineColor, value: UIColor.black, range: range)
        attributedString.addAttribute(NSAttributedString.Key.font, value: UIFont(name: Fonts.LoewNextArabicMedium, size: 14)!, range: range)
        attributedString.addAttribute(NSAttributedString.Key.foregroundColor, value: UIColor.black, range: range)
        self.btnMore.setAttributedTitle(attributedString, for: .normal)
        self.lblTitle.text = "bundles".localized
        self.lblTitle.alignmentText()
    }
    
}

extension BundelsListTableViewCell: UICollectionViewDelegate, UICollectionViewDataSource, UICollectionViewDelegateFlowLayout {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return self.bundelsList.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeue(with: BundelsListCollectionViewCell.self, for: indexPath)
        cell.isAppadvertisement(value: true)
        cell.setupCell(bundle: self.bundelsList[indexPath.row])
        cell.btnAddToCart.tag =  indexPath.row //self.bundelsList[indexPath.row].bunddelId ?? 0
        cell.btnAddToCart.addTarget(self, action: #selector(self.addBundleToCart(_:)), for: .touchUpInside)
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        self.delegate?.openBundelsListDetails(bundleID: self.bundelsList[indexPath.row].bunddelId ?? 0, index: indexPath.row)
    }
    
    @objc func addBundleToCart(_ sender: UIButton) {
        self.delegate?.addBundleToCart(bundle: sender.tag)
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
            return CGSize(width: (collectionView.frame.width - 20) / 3, height: 193)
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, insetForSectionAt section: Int) -> UIEdgeInsets {
        return UIEdgeInsets(top: 0, left: 0, bottom: 0, right: 0)
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, minimumLineSpacingForSectionAt section: Int) -> CGFloat {
        return 0
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, minimumInteritemSpacingForSectionAt section: Int) -> CGFloat {
        return 0
    }
    
}
