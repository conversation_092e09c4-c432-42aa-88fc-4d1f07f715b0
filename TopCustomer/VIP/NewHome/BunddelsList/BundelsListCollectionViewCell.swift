//
//  BunddelsListCollectionViewCell.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 21/02/2024.
//  Copyright © 2024 SOTSYS203. All rights reserved.
//

import UIKit
import Kingfisher

class BundelsListCollectionViewCell: UICollectionViewCell {
    
    // MARK: - IBOutlets
    @IBOutlet weak var imageBundle: UIImageView!
    @IBOutlet weak var lblAppadvertisementTitle: MaterialLocalizeLable!
    @IBOutlet weak var viewBookmark: UIView!
    @IBOutlet weak var lblBundleName: MaterialLocalizeLable!
    @IBOutlet weak var lblBundleDescription: MaterialLocalizeLable!
    @IBOutlet weak var lblPrice: MaterialLocalizeLable!
    @IBOutlet weak var lblDiscountPrice: MaterialLocalizeLable!
    @IBOutlet weak var btnAddToCart: CustomRoundedButtton!
    @IBOutlet weak var lblDiscountOff: MaterialLocalizeLable!
    @IBOutlet weak var discountStackView: UIStackView!
    @IBOutlet weak var viewOutOfStock: UIView!
    
    // MARK: - Func
    override func awakeFromNib() {
        super.awakeFromNib()
        // Initialization code
        self.lblAppadvertisementTitle.text = "Ad".localized
        self.btnAddToCart.setTitle("Add to cart".localized, for: .normal)
        self.setDefaultShadow()
        let attributeString: NSMutableAttributedString = NSMutableAttributedString(string: lblDiscountPrice.text ?? "")
        attributeString.addAttribute(NSAttributedString.Key.strikethroughStyle, value: NSUnderlineStyle.single.rawValue, range: NSRange(location: 0, length: lblDiscountPrice.text?.length ?? 0))
        attributeString.addAttribute(NSAttributedString.Key.strikethroughColor, value: UIColor.AppTheme_StrikeThroughColor_707070, range: NSRange(location: 0, length: lblDiscountPrice.text?.length ?? 0))
        self.lblDiscountPrice.attributedText = attributeString

    }
    
    func setupCell(bundle: BundelsListResponseFields?) {
        if bundle?.iHowMuchLeftInStock ?? 0 == 0 {
            viewOutOfStock.isHidden = false
        } else {
            viewOutOfStock.isHidden = true
        }
        self.lblBundleName.text = bundle?.vBunddelName ?? ""
        self.lblBundleDescription.text = bundle?.txBunddelDescription ?? ""
        lblPrice.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: bundle?.price ?? 0.0))")
        if bundle?.discountPrice ?? 0 > 0 {
            self.discountStackView.isHidden = false
            let originalPrice = Double(bundle?.price ?? 0.0)
            let discountedPrice = Double(bundle?.discountPrice ?? 0.0)
            let a = originalPrice - discountedPrice
            let b = a / originalPrice
            let percent = b * 100
            lblDiscountOff.text = "(\(forTrailingZero(temp: percent.rounded(.up)))% \(ObjKeymessages.kLABEL_OFF))"
            lblDiscountPrice.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: bundle?.price ?? 0.0))")
            lblPrice.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: bundle?.discountPrice ?? 0.0))")
        } else {
            self.discountStackView.isHidden = true
            lblDiscountOff.text = ""
            lblDiscountPrice.text = ""
        }
        self.imageBundle.kf.indicatorType = .activity
        guard let url = URL(string: bundle?.vBunddelImage ?? "") else {
            return
        }
        self.imageBundle.kf.setImage(with: url, placeholder: UIImage(named: "placeholder_product"))
    }
    
    func isAppadvertisement(value: Bool) {
        self.viewBookmark.isHidden = value
    }
    
}
