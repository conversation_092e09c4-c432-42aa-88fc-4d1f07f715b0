
import UIKit

protocol LoginPresentationProtocol {
    func checkValidation(dictData:[String:Any]) -> Bool
    func apiCallForLogin(dictData:[String:Any])
    func apiResponseLogin(response:UserResponse?,error:Error?)

    func apiCallForGetCountryList(flagNoNetwork: Bool)
    func apiResponseGetCountryList(response:CountryListResponse?,error:Error?, flagNoNetwork: Bool)

}

class LoginPresenter: LoginPresentationProtocol {
    
    // MARK: Objects & Variables
    weak var viewControllerLogin: LoginProtocol?
    var interactorLogin: LoginInteractorProtocol?
    
    func checkValidation(dictData:[String:Any]) -> Bool {
        if setDataInString(dictData["vName"] as AnyObject).isBlank {
            AppSingletonObj.showAlert(strMessage: ObjKeymessages.kALERT_ENTER_NAME)
            return false
        }
        else if setDataInString(dictData["vISDCode"] as AnyObject).isBlank {
            AppSingletonObj.showAlert(strMessage: ObjKeymessages.kALERT_ISD_CODE)
            return false
        }
        else if setDataInString(dictData["vMobileNumber"] as AnyObject).isBlank {
            AppSingletonObj.showAlert(strMessage: ObjKeymessages.kALERT_MOBILE_NUMBER)
            return false
        }
        else if setDataInString(dictData["vMobileNumber"] as AnyObject).count < MinMoNoLength || setDataInString(dictData["vMobileNumber"] as AnyObject).count > MaxMoNoLength {
            AppSingletonObj.showAlert(strMessage: ObjKeymessages.kALERT_MOBILE_NUMBER_TEN_DIGITS)
            return false
        }
        else if setDataInString(dictData["selectedPrivacy"] as AnyObject) == "0" {
            AppSingletonObj.showAlert(strMessage: ObjKeymessages.kALERT_SELECT_PRIVACY)
            return false
        }
        return true
    }

    func apiCallForLogin(dictData:[String:Any]) {
        self.interactorLogin?.apiCallForLogin(dictData: dictData)
    }

    func apiResponseLogin(response:UserResponse?,error:Error?) {
        if let error = error  {
//            viewControllerLogin?.displayAlert(string: error.localizedDescription)
            AppSingletonObj.showAlert(strMsg: error.localizedDescription)
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            //            viewController?.displayAlert(strTitle: "", strMessage: KeyMessages.kInvalidateResponse)
            return
        }
        
        if code == APICODE400 {
//            viewControllerLogin?.displayAlert(string: response.responseMessage ?? "")
            AppSingletonObj.showAlert(strMsg: response.responseMessage ?? "")
            return
        }
        
        guard let model = response.responseData,code == APISUCCESSCODE200  else {
            return
        }
        self.viewControllerLogin?.navigateToOtp(model: model)
    }

    func apiCallForGetCountryList(flagNoNetwork: Bool) {
        interactorLogin?.apiCallForGetCountryList(flagNoNetwork: flagNoNetwork)
    }

    func apiResponseGetCountryList(response: CountryListResponse?, error: Error?, flagNoNetwork: Bool) {
//        if let vc = self.viewControllerLogin as? BaseViewController {
//            DispatchQueue.main.async {
//                vc.endRefresing()
//            }
//        }
        if let error = error  {
            AppSingletonObj.showAlert(strMsg: error.localizedDescription)
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            //            viewController?.displayAlert(strTitle: "", strMessage: KeyMessages.kInvalidateResponse)
            return
        }
        
        if code == APICODE400 {
            AppSingletonObj.showAlert(strMsg: response.responseMessage ?? "")
            return
        }
        
        guard let model = response.responseData,code == APISUCCESSCODE200  else {
            return
        }
        self.viewControllerLogin?.refreshCountryData(arrCountryList: model, flagNoNetwork: flagNoNetwork)
    }

}
