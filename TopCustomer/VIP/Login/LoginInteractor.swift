
import UIKit

protocol LoginInteractorProtocol {
    func apiCallForLogin(dictData:[String:Any])
    func apiCallForGetCountryList(flagNoNetwork: Bool)

}

protocol LoginDataStore {
    //{ get set }
}

class LoginInteractor: LoginInteractorProtocol, LoginDataStore {

    // MARK: Objects & Variables
    var presenterLogin: LoginPresentationProtocol?
    
    func apiCallForLogin(dictData:[String:Any]) {
        ActivityIndicator.shared.showCentralSpinner()
        
        var strJson = ""
        if let strValue = UserDefaults.standard.object(forKey: USERDEFAULTS_INFO_KEY.DEEPLINKING_JSON) as? String {
            strJson = strValue
        }

        var strDeviceToken = ""
        if vDeviceToken == "" {
//            AppDelegate.refreshToken()
            strDeviceToken = AppDelegate.getDeviceToken()
        }
        else {
            strDeviceToken = vDeviceToken
        }
        
        print("New FCM Token: \(strDeviceToken)")
        
        AuthenticationAPI.login(accept: AcceptParamForHeader, lang: CurrentAppLang, iRoleId: RoleCustomer_2, vName: setDataInString(dictData["vName"] as AnyObject), vLanguage: CurrentAppLang, vISDCode: setDataInString(dictData["vISDCode"] as AnyObject), vMobileNumber: setDataInString(dictData["vMobileNumber"] as AnyObject), vDeviceToken: strDeviceToken, tiDeviceType: AuthenticationAPI.TiDeviceType_login._2, vDeviceName: vDeviceName, vDeviceKey: vDeviceUniqueId, vReferalPlatform: strJson) { data, error in
            ActivityIndicator.shared.hideCentralSpinner()
            self.presenterLogin?.apiResponseLogin(response: data, error: error)
        }
        
    }

    func apiCallForGetCountryList(flagNoNetwork: Bool) {
        
        ActivityIndicator.shared.showCentralSpinner()
        
        CountryAPI.countryListing(accept: AcceptParamForHeader, lang: CurrentAppLang) { data, error in
            ActivityIndicator.shared.hideCentralSpinner()
            self.presenterLogin?.apiResponseGetCountryList(response: data, error: error, flagNoNetwork: flagNoNetwork)

        }
        
    }

}
