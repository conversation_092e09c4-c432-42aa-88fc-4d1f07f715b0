
import UIKit
import ActionSheetPicker_3_0
import TikTokBusinessSDK
import FirebaseAnalytics
import SwiftUI

protocol LoginProtocol: AnyObject {
    func navigateToOtp(model : UserResponseFields)
    func refreshCountryData(arrCountryList : [CountryListResponseFields], flagNoNetwork: Bool)
    
}

class LoginViewController: BaseViewController {
    
    // MARK: Objects & Variables
    var presenterLogin: LoginPresentationProtocol?
    
    // MARK: IBOutlets
    @IBOutlet weak var btnCheckUncheck: UIButton!
    //@IBOutlet weak var lblName: UILabel!
    @IBOutlet weak var btnCountryCode: UIButton!
    @IBOutlet weak var txtName: CustomTextfieldWithFontStyle!
    //    @IBOutlet weak var lblPhoneNo: UILabel!
    @IBOutlet weak var txtPhoneNo: CustomTextfieldWithFontStyle!
    @IBOutlet weak var btnSignIn: CustomRoundedButtton!
    @IBOutlet weak var lblIAgree: UILabel!
    @IBOutlet weak var btnSkipNow: UIButton!
    @IBOutlet weak var lblVersion: UILabel!
    @IBOutlet weak var txtSelectCity: CustomTextfieldWithFontStyle!
    @IBOutlet weak var btnSelectCity: UIButton!
    
    // MARK: - Variables
    var arrCountry: [CountryListResponseFields] = []
    var cites = CityModel.getData()
    var citySelected: CityModel?
    var actionSheetCitesPicker = ActionSheetStringPicker()
    
    // MARK: Object lifecycle
    /*
     override init(nibName nibNameOrNil: String?, bundle nibBundleOrNil: Bundle?) {
     super.init(nibName: nibNameOrNil, bundle: nibBundleOrNil)
     setup()
     }
     */
    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
        setup()
    }
    
    // MARK: Setup
    
    private func setup() {
        let viewController = self
        let interactor = LoginInteractor()
        let presenter = LoginPresenter()
        
        //View Controller will communicate with only presenter
        viewController.presenterLogin = presenter
        
        //Presenter will communicate with Interector and Viewcontroller
        presenter.viewControllerLogin = viewController
        presenter.interactorLogin = interactor
        
        //Interactor will communucate with only presenter.
        interactor.presenterLogin = presenter
    }
    
    
    // MARK: View lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        self.setTexts()
        self.setVersionText()
        
        // Mixpanel
        MixpanelEvents.sharedInstance.initializeMixpanel()
        
        self.getCountryList(flagNoNetwork: false)
    }
    
    private func setVersionText() {
#if DEBUG
        lblVersion.text = "\(ObjKeymessages.kLABEL_VERSION): \(Bundle.main.releaseVersionNumber ?? "") (\(Bundle.main.buildVersionNumber ?? "").QA)"
        
#elseif STAGING
        lblVersion.text = "\(ObjKeymessages.kLABEL_VERSION): \(Bundle.main.releaseVersionNumber ?? "") (\(Bundle.main.buildVersionNumber ?? "").STAGING)"
        
#elseif DEVRELEASE
        lblVersion.text = "\(ObjKeymessages.kLABEL_VERSION): \(Bundle.main.releaseVersionNumber ?? "") (\(Bundle.main.buildVersionNumber ?? "").LIVE)"
        
#elseif RELEASESTAGING
        lblVersion.text = "\(ObjKeymessages.kLABEL_VERSION): \(Bundle.main.releaseVersionNumber ?? "") (\(Bundle.main.buildVersionNumber ?? "").STAGING-RELEASE)"
        
#else
        lblVersion.text = "\(ObjKeymessages.kLABEL_VERSION): \(Bundle.main.releaseVersionNumber ?? "") (\(Bundle.main.buildVersionNumber ?? ""))"
        
#endif
    }
    
    private func getCountryList(flagNoNetwork: Bool) {
        if AppSingletonObj.isConnectedToNetworkForCheckoutScreen(){
            self.presenterLogin?.apiCallForGetCountryList(flagNoNetwork: flagNoNetwork)
        }
    }
    
    private func setTexts() {
        //        lblName.text = ObjKeymessages.kLABEL_NAME
        txtName.placeholder = ObjKeymessages.kLABEL_NAME
        //        lblPhoneNo.text = ObjKeymessages.kLABEL_PHONE_NO
        txtPhoneNo.placeholder = ObjKeymessages.kLABEL_PHONE_NO
        btnSignIn.setTitle(ObjKeymessages.kLABEL_SIGN_IN, for: .normal)
        btnSkipNow.setTitle(ObjKeymessages.kLABEL_SKIP, for: .normal)
        btnCountryCode.setTitle(SACountryCode, for: .normal)
        
        // set privacy policy attributed label
        let string = ObjKeymessages.kLABEL_I_AGREE_WITH
        let range = (string as NSString).range(of: ObjKeymessages.kLABEL_PRIVACY_POLICY)
        let attributedString = NSMutableAttributedString(string: string)
        attributedString.addAttribute(NSAttributedString.Key.underlineStyle, value: NSNumber(value: 1), range: range)
        attributedString.addAttribute(NSAttributedString.Key.underlineColor, value: UIColor.AppTheme_BlueColor_012CDA, range: range)
        attributedString.addAttribute(NSAttributedString.Key.foregroundColor, value: UIColor.AppTheme_BlueColor_012CDA, range: range)
        attributedString.addAttribute(NSAttributedString.Key.font, value: UIFont(name: Fonts.LoewNextArabicBold, size: 12)!, range: range)
        lblIAgree.attributedText = attributedString
    }
    
    @IBAction func btnCheckUncheckClicked(_ sender: Any) {
        self.view.endEditing(true)
        btnCheckUncheck.isSelected = !btnCheckUncheck.isSelected
    }
    
    
    @IBAction func btnSelectCityAction(_ sender: UIButton) {
        setCitesData()
    }
    
    private func setCitesData() {
        ActionSheetStringPicker.show(withTitle: "select_city".localized ,
                                     rows: self.cites.map{ $0.cityName.localized } ,
                                     initialSelection: 0,
                                     doneBlock: {[weak self] picker, index, value in
            guard let self = self else {return}
            picker?.title = "Done".localized
            if self.cites.count == 0 { return }
            UserDefaults.standard.selectedCityID = self.cites[index].cityId
            self.citySelected = self.cites[index]
            self.txtSelectCity.text = self.cites[index].cityName
            return
        },
                                     cancel: { picker in
            picker?.title = "Cancel".localized
            return
        },
                                     origin: self.view)
    }
    
    @IBAction func loginClicked(_ sender: Any) {
        // hidden for now
        //        if citySelected?.cityId == nil {
        //            self.showAlert(message: "please_select_city".localized)
        //            return
        //        }
        self.view.endEditing(true)
        if AppSingletonObj.isConnectedToNetwork(){
            var dictParam : [String:Any] = [:]
            dictParam["vName"] = txtName.text
            dictParam["vISDCode"] = btnCountryCode.titleLabel?.text
            dictParam["vMobileNumber"] = txtPhoneNo.text
            dictParam["selectedPrivacy"] = btnCheckUncheck.isSelected
            
            if self.presenterLogin?.checkValidation(dictData: dictParam) ?? false {
                TikTokBusiness.trackEvent("Login")
                Analytics.logEvent(TTEventName.login.rawValue, parameters: dictParam)
                self.presenterLogin?.apiCallForLogin(dictData: dictParam)
            }
        }
    }
    
    @IBAction func privacyPolicyClicked(_ sender: Any) {
        let controller : StaticPageViewController = UIStoryboard.storyboard(.Settings).instantiateViewController()
        controller.strFromWhichScreen = "PrivacyPolicy"
        self.pushVC(controller)
    }
    
    @IBAction func btnCountryCodePressed(_ sender: Any) {
        self.view.endEditing(true)
        
        if AppSingletonObj.isConnectedToNetworkForCheckoutScreen() {
            if arrCountry.count <= 0 {  // Do not have country list, call API
                self.getCountryList(flagNoNetwork: true)
            }
            else {  // Having country list, open country picker
                let countryView = CountrySelectView()     //.shared
                //        countryView.barTintColor = .red
                countryView.show()
                countryView.selectedCountryCallBack = {[weak self] (countryDic) -> Void in
                    self?.countryDictSetUp(countryDic: countryDic)
                }
            }
        }
    }
    
    @IBAction func skipNowAction(_ sender: Any) {
        selectDeliveryType()
//        let vc = homeStoryboard.instantiate(MainTabbarViewController.self)
//        let navigationVc = UINavigationController(rootViewController: vc)
//        AppDel?.window?.rootViewController = navigationVc
    }
    
    func selectDeliveryType() {
        AppDel?.window?.rootViewController = UIHostingController(rootView: SelectDeliveryTypeView())
        AppDel?.window?.makeKeyAndVisible()
    }
    
    func countryDictSetUp(countryDic: [String: Any]?){
        if countryDic == nil {
            self.btnCountryCode.setTitle(SACountryCode, for: .normal)
            if let regCode = Locale.current.regionCode {
                if let dict = CountryCodeJson.filter({$0["locale"] as? String == regCode}).first {
                    self.btnCountryCode.setTitle("+\(dict["code"] as! NSNumber)", for: .normal)
                }
            }
        }
        else {
            self.btnCountryCode.setTitle("+\(countryDic!["code"] as! NSNumber)", for: .normal)
        }
    }
    
    func switchBasedNextTextField(_ textField: UITextField) {
        switch textField  {
        case txtName:
            txtPhoneNo.becomeFirstResponder()
        default:
            txtPhoneNo.resignFirstResponder()
        }
    }
    
}

extension UserDefaults {
    func setLanguage(language:String = UserAPI.VLanguage_userLanguage.en.rawValue)
    {
        let path = Bundle.main.path(forResource: language, ofType: "lproj")
        _ = Bundle.init(path: path!)!
        UserDefaults.standard.set(language, forKey: "AppLanguage")
        UserDefaults.standard.synchronize()
    }
    
    func getLanguage() -> String? {
        return UserDefaults.standard.string(forKey: "AppLanguage") ?? UserAPI.VLanguage_userLanguage.en.rawValue
    }
    
    func isCurrentLanguageArabic() -> Bool {
        return UserDefaults.standard.getLanguage() == UserAPI.VLanguage_userLanguage.ar.rawValue ? true : false
    }
    
}

extension String {
    var localized: String {
        let language : String = UserDefaults.standard.getLanguage()!
        return NSLocalizedString(self, tableName: nil, bundle:  Bundle(path: Bundle.main.path(forResource: language , ofType: "lproj")!)!, value: "", comment: "")
    }
}

// MARK: - UITextFieldDelegate
extension LoginViewController : UITextFieldDelegate {
    
    func textField(_ textField: UITextField, shouldChangeCharactersIn range: NSRange, replacementString string: String) -> Bool {
        
        if textField == txtPhoneNo {
            let newLength: Int = (textField.text?.length)! + string.length - range.length
            if newLength > MaxMoNoLength {
                return false
            }
            return true
        }
        else if textField == txtName {
            let newLength: Int = (textField.text?.length)! + string.length - range.length
            if newLength > MaxNameLength {
                return false
            }
            return true
        }
        else {
            return true
        }
    }
    
    func textFieldShouldReturn(_ textField: UITextField) -> Bool {
        switchBasedNextTextField(textField)
        return true
    }
}

extension LoginViewController : LoginProtocol {
    func navigateToOtp(model : UserResponseFields) {
        // Do you have a friend invitation gift?
        Constant.shared.IS_FRIEND_INVITATION_GIFT = model.inviteFriend ?? false
        let storyboard = UIStoryboard(name: "Main", bundle: Bundle.main)
        let objVC = storyboard.instantiateViewController(withIdentifier: "OTPVerificationViewController") as! OTPVerificationViewController
        objVC.strCountryCode = model.vISDCode ?? ""
        objVC.strMobNo = model.vMobileNumber ?? ""
        objVC.strName = model.vName ?? ""
        objVC.strDeviceToken = ""
        //model.vDeviceToken ?? ""
        objVC.isFromEditProfile = false
        self.pushVC(objVC)
    }
    
    func refreshCountryData(arrCountryList : [CountryListResponseFields], flagNoNetwork: Bool) {
        
        arrCountry = arrCountryList
        
        var countryCodeJson = [] as [[String:Any]]
        var dictAnswer: [String: Any] = [:]
        
        for obj in arrCountryList {
            dictAnswer["en"] = obj.vCountryName
            dictAnswer["es"] = obj.vCountryName
            dictAnswer["zh"] = obj.vCountryName
            dictAnswer["locale"] = obj.vImage
            dictAnswer["code"] = obj.vDialingCode
            
            countryCodeJson.append(dictAnswer)
        }
        
        CountryCodeJson = countryCodeJson
        
        if flagNoNetwork == true {  // Open country picker
            if arrCountry.count > 0 {  // Do not have country list, call API
                let countryView = CountrySelectView()     //.shared
                //        countryView.barTintColor = .red
                countryView.show()
                countryView.selectedCountryCallBack = {[weak self] (countryDic) -> Void in
                    self?.countryDictSetUp(countryDic: countryDic)
                }
            }
        }
    }
    
}

extension Dictionary {
    
    var json : String {
        get {
            let jsonData = try? JSONSerialization.data(withJSONObject: self, options: .prettyPrinted)
            
            if let jsonData = jsonData {
                let jsonString = String(decoding: jsonData, as: UTF8.self)
                return jsonString
            }
            return ""
        }
    }
    
    var data : Data? {
        get {
            let jsonData = try? JSONSerialization.data(withJSONObject: self, options: .prettyPrinted)
            if let jsonData = jsonData {
                return jsonData
            }
            return nil
        }
    }
}

extension Data {
    var json : [String:Any]  {
        get {
            let dictData = try? JSONSerialization.jsonObject(with: self, options: []) as? [String:Any]
            if let dict = dictData {
                return dict
            }
            return [:]
        }
        
    }
    
    var jsonDictionary : [String:Any]?  {
        get {
            let dictData = try? JSONSerialization.jsonObject(with: self, options: []) as? [String:Any]
            if let dict = dictData {
                return dict
            }
            return nil
        }
        
    }
    
    var stringValue : String {
        get {
            
            let jsonString = String(decoding: self, as: UTF8.self)
            return jsonString
            
        }
    }
    
    
}
