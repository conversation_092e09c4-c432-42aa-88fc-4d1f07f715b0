
import UIKit
import AACarousel

protocol FullScreenImageProtocol: AnyObject {
 
}

class FullScreenImageViewController: UIViewController, FullScreenImageProtocol, AACarouselDelegate {
    // MARK: Objects & Variables
    var presenterFullScreenImage: FullScreenImagePresentationProtocol?

    // MARK: IBOutlets
    @IBOutlet weak var imageScrollView: ImageScrollView!
    @IBOutlet weak var carouselView: AACarousel!
    
    // MARK: - Variables
    var mediaImage: UIImage?
    var images = [String]()
    // MARK: Object lifecycle
    /*
    override init(nibName nibNameOrNil: String?, bundle nibBundleOrNil: Bundle?) {
        super.init(nibName: nibNameOrNil, bundle: nibBundleOrNil)
        setup()
    }
    */
    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
        setup()
    }
    
    // MARK: Setup
    
    private func setup() {
        let viewController = self
        let interactor = FullScreenImageInteractor()
        let presenter = FullScreenImagePresenter()
        
        //View Controller will communicate with only presenter
        viewController.presenterFullScreenImage = presenter
        
        //Presenter will communicate with Interector and Viewcontroller
        presenter.viewControllerFullScreenImage = viewController
        presenter.interactorFullScreenImage = interactor
        
        //Interactor will communucate with only presenter.
        interactor.presenterFullScreenImage = presenter
    }
    
    // MARK: View lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        imageScrollView.setup()
        imageScrollView.imageContentMode = .aspectFit
        imageScrollView.initialOffset = .center
        guard let image = self.mediaImage else {
            return
        }
        if self.images.count >= 1 {
            imageScrollView.isHidden = true
            imageScrollView.zoomingImageDelegate = self
            self.setImagesData()
        } else {
            imageScrollView.isHidden = true
            imageScrollView.display(image: image)
            imageScrollView.isHidden = false
        }
        
    }
    
    @IBAction func closeAction(_ sender: Any) {
        self.dismiss(animated: true, completion: nil)
    }
    
    //require method
    func downloadImages(_ url: String, _ index:Int) {
        var imageView = UIImageView()
        imageView.image = UIImage(named: "logo_login")
        imageView.kf.indicatorType = .activity
        imageView.kf.setImage(
            with: URL(string: url),
            placeholder: UIImage(named: "logo_login"),
            options: [
                .scaleFactor(UIScreen.main.scale),
                .transition(.fade(1)),
                .cacheOriginalImage
            ])
        {
            result in
            switch result {
            case .success(let value):
                print("Task done for: \(value.source.url?.absoluteString ?? "")")
                self.carouselView.images[index] = value.image
            case .failure(let error):
                print("Job failed: \(error.localizedDescription)")
            }
        }
    }
    
    func setImagesData() {
        carouselView.isHidden = false
        carouselView.delegate = self
        carouselView.setCarouselData(paths: self.images, describedTitle: [], isAutoScroll: true, timer: 2.5, defaultImage: "logo_login")
        //optional method
        carouselView.setCarouselOpaque(layer: true, describedTitle: true, pageIndicator: false)
        carouselView.setCarouselLayout(displayStyle: 0, pageIndicatorPositon: 3, pageIndicatorColor: .gray, describedTitleColor: .clear, layerColor: .clear)
    }
    
    func startAutoScroll() {
        //optional method
        carouselView.startScrollImageView()
    }
    
    func stopAutoScroll() {
        //optional method
        carouselView.stopScrollImageView()
    }
    
    //optional method (show first image faster during downloading of all images)
    func callBackFirstDisplayView(_ imageView: UIImageView, _ url: [String], _ index: Int) {
        imageView.kf.setImage(with: URL(string: url[index]), placeholder: UIImage.init(named: "logo_login"), options: [.transition(.fade(1))], progressBlock: nil, completionHandler: nil)
    }
    
    //optional method (interaction for touch image)
    func didSelectCarouselView(_ view: AACarousel ,_ index: Int) {
        imageScrollView.isHidden = false
        imageScrollView.display(image: carouselView.images[index])
        self.carouselView.isHidden = true
    }
    
}
// MARK: - Extension ZoomingImageDelegate
extension FullScreenImageViewController: ZoomingImageDelegate {
    func startZooming() {
        debugPrint("startZooming")
        imageScrollView.isHidden = false
        self.carouselView.isHidden = true
    }
    
    func finishZooming() {
        debugPrint("finishZooming")
        imageScrollView.isHidden = true
        self.carouselView.isHidden = false
    }
    
}
