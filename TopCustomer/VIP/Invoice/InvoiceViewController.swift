
import UIKit

protocol InvoiceProtocol: AnyObject {
   
}

class InvoiceViewController: UIViewController, InvoiceProtocol {

    // MARK: Objects & Variables
    var presenterInvoice: InvoicePresentationProtocol?

    // MARK: IBOutlets
    @IBOutlet weak var btnCancel: UIButton!
    @IBOutlet weak var lblTitle: UILabel!
    
    
    // MARK: Object lifecycle
    /*
    override init(nibName nibNameOrNil: String?, bundle nibBundleOrNil: Bundle?) {
        super.init(nibName: nibNameOrNil, bundle: nibBundleOrNil)
        setup()
    }
    */
    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
        setup()
    }
    
    // MARK: Setup
    
    private func setup() {
        let viewController = self
        let interactor = InvoiceInteractor()
        let presenter = InvoicePresenter()
        
        //View Controller will communicate with only presenter
        viewController.presenterInvoice = presenter
        
        //Presenter will communicate with <PERSON><PERSON> and Viewcontroller
        presenter.viewControllerInvoice = viewController
        presenter.interactorInvoice = interactor
        
        //Interactor will communucate with only presenter.
        interactor.presenterInvoice = presenter
    }
    
    
    // MARK: View lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        lblTitle.text = ObjKeymessages.kLABEL_TAX_INVOICE
    }
    
    @IBAction func btnBackAction(_ sender: Any) {
        self.dismissVC {
            
        }
    }

}
