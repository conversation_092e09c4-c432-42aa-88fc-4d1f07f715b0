
import UIKit

protocol VersionUpdateProtocol: AnyObject {
  
}

class VersionUpdateViewController: UIViewController, VersionUpdateProtocol {

    // MARK: Objects & Variables
    var presenterVersionUpdate: VersionUpdatePresentationProtocol?

    // MARK: IBOutlets
    @IBOutlet weak var lblTitle: UILabel!
    @IBOutlet weak var lblMessage: UILabel!
    @IBOutlet weak var btnNoThanks: UIButton!
    @IBOutlet weak var btnUpdate: UIButton!
    
    var navigateForUpdateApp: (()->())?
    var strMsg = ""
    var flagRegular = true
    
    // MARK: Object lifecycle
    /*
    override init(nibName nibNameOrNil: String?, bundle nibBundleOrNil: Bundle?) {
        super.init(nibName: nibNameOrNil, bundle: nibBundleOrNil)
        setup()
    }
    */
    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
        setup()
    }
    
    // MARK: Setup
    
    private func setup() {
        let viewController = self
        let interactor = VersionUpdateInteractor()
        let presenter = VersionUpdatePresenter()
        
        //View Controller will communicate with only presenter
        viewController.presenterVersionUpdate = presenter
        
        //Presenter will communicate with Interector and Viewcontroller
        presenter.viewControllerVersionUpdate = viewController
        presenter.interactorVersionUpdate = interactor
        
        //Interactor will communucate with only presenter.
        interactor.presenterVersionUpdate = presenter
    }
    
    // MARK: View lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        self.setTexts()
    }
    
    private func setTexts() {
        lblTitle.text = ObjKeymessages.kLABEL_UPDATE_AVAILABLE
        lblMessage.text = strMsg
        btnUpdate.setTitle(ObjKeymessages.kLABEL_UPDATE, for: .normal)
        
        let string = ObjKeymessages.kLABEL_NOT_NOW
        let range = (string as NSString).range(of: ObjKeymessages.kLABEL_NOT_NOW)
        let attributedString = NSMutableAttributedString(string: string)
        attributedString.addAttribute(NSAttributedString.Key.underlineStyle, value: NSNumber(value: 1), range: range)
        attributedString.addAttribute(NSAttributedString.Key.underlineColor, value: UIColor.black, range: range)
        attributedString.addAttribute(NSAttributedString.Key.font, value: UIFont(name: Fonts.LoewNextArabicBold, size: 14)!, range: range)
        attributedString.addAttribute(NSAttributedString.Key.foregroundColor, value: UIColor.black, range: range)  
        btnNoThanks.setAttributedTitle(attributedString, for: .normal)

        if flagRegular == true {  // Regular update, show two buttons
        }
        else { // Force update, show one button
            btnNoThanks.isHidden = true
        }
    }
    
    @IBAction func noThanksAction(_ sender: Any) {
        self.dismiss(animated: true, completion: nil)
    }

    @IBAction func updateAction(_ sender: Any) {
        self.navigateForUpdateApp?()
        if flagRegular == true {  // Regular update, show two buttons
            self.dismiss(animated: true, completion: nil)
        }
    }

}
