<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="21701" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21678"/>
        <capability name="Named colors" minToolsVersion="9.0"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="LoewNextArabic-Medium.ttf">
            <string>LoewNextArabic-Medium</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" reuseIdentifier="RewardDetailsCell" id="KGk-i7-Jjw" customClass="RewardDetailsCell" customModule="TopCustomer" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="320" height="60"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="320" height="60"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <stackView opaque="NO" contentMode="scaleToFill" alignment="top" translatesAutoresizingMaskIntoConstraints="NO" id="zfU-y0-ZGq">
                        <rect key="frame" x="10" y="5" width="300" height="50"/>
                        <subviews>
                            <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="1." textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="IZK-9l-Y5c">
                                <rect key="frame" x="-20" y="0.0" width="20" height="20"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="20" id="rJs-gW-9Rr"/>
                                </constraints>
                                <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="20"/>
                                <color key="textColor" name="AppTheme_BlueColor_#012CDA"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text=" " textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="f83-PN-DyY" userLabel="Collect material bottles each time you order.">
                                <rect key="frame" x="0.0" y="0.0" width="300" height="20"/>
                                <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="20"/>
                                <color key="textColor" name="AppTheme_DarkGrayColor_#5D5D5D"/>
                                <nil key="highlightedColor"/>
                            </label>
                        </subviews>
                    </stackView>
                </subviews>
                <constraints>
                    <constraint firstAttribute="bottom" secondItem="zfU-y0-ZGq" secondAttribute="bottom" constant="5" id="6AW-px-JbN"/>
                    <constraint firstItem="zfU-y0-ZGq" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="10" id="DzY-nM-Kha"/>
                    <constraint firstAttribute="trailing" secondItem="zfU-y0-ZGq" secondAttribute="trailing" constant="10" id="QR9-0Z-hMg"/>
                    <constraint firstItem="zfU-y0-ZGq" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" constant="5" id="m4y-De-QLH"/>
                </constraints>
            </tableViewCellContentView>
            <viewLayoutGuide key="safeArea" id="njF-e1-oar"/>
            <connections>
                <outlet property="lblItemNo" destination="IZK-9l-Y5c" id="3ta-r0-mt6"/>
                <outlet property="lblRewardDesc" destination="f83-PN-DyY" id="gc3-jM-Cyt"/>
            </connections>
            <point key="canvasLocation" x="60" y="20"/>
        </tableViewCell>
    </objects>
    <resources>
        <namedColor name="AppTheme_BlueColor_#012CDA">
            <color red="0.0039215686274509803" green="0.17254901960784313" blue="0.85490196078431369" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="AppTheme_DarkGrayColor_#5D5D5D">
            <color red="0.36470588235294116" green="0.36470588235294116" blue="0.36470588235294116" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
    </resources>
</document>
