//
//  RewardDetailsCell.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 08/11/2023.
//  Copyright © 2023 SOTSYS203. All rights reserved.
//

import UIKit

class RewardDetailsCell: UITableViewCell {
    // MARK: - IBOutlets
    @IBOutlet weak var lblItemNo: UILabel!
    @IBOutlet weak var lblRewardDesc: UILabel!
    
    // MARK: - Func
    override func awakeFromNib() {
        super.awakeFromNib()
        // Initialization code
    }
    
    override func setSelected(_ selected: <PERSON><PERSON>, animated: Bool) {
        super.setSelected(selected, animated: animated)
        // Configure the view for the selected state
    }
    
    func setupCell(itemNo: Int, desc: String, isUsingSmallFont: Bool = false) {
        if isUsingSmallFont {
            self.lblItemNo.font = .LoewNextArabic.medium(size: 14).font
            self.lblRewardDesc.font = .LoewNextArabic.medium(size: 14).font
        } else {
            self.lblItemNo.font = .LoewNextArabic.medium(size: 20).font
            self.lblRewardDesc.font = .LoewNextArabic.medium(size: 20).font
        }
        self.lblItemNo.text = "\(itemNo)"
        self.lblRewardDesc.text = desc
        if UserDefaults.standard.getLanguage()! == UserAPI.VLanguage_userLanguage.ar.rawValue {
            self.lblRewardDesc.setLineSpacing(lineSpacing: 15)
        } else {
            self.lblRewardDesc.setLineSpacing(lineSpacing: 5)
        }
        self.lblRewardDesc.alignmentText()
    }
    
}
