//
//  RewardDetailsTVCell.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 14/10/2023.
//  Copyright © 2023 SOTSYS203. All rights reserved.
//

import UIKit

class PointsHostoryTVCell: UITableViewCell {
    // MARK: - IBOutlets
    @IBOutlet weak var imageHistory: UIImageView!
    @IBOutlet weak var lblOrderNo: MaterialLocalizeLable!
    @IBOutlet weak var lblOrderNoValue: MaterialLocalizeLable!
    @IBOutlet weak var lblAmount: MaterialLocalizeLable!
    @IBOutlet weak var lblAmountValue: MaterialLocalizeLable!
    @IBOutlet weak var lblPoints: MaterialLocalizeLable!
    @IBOutlet weak var lblPointsValue: MaterialLocalizeLable!
    @IBOutlet weak var stackViewOrder: UIStackView!
    
    // MARK: - Func
    override func awakeFromNib() {
        super.awakeFromNib()
        // Initialization code
    }
    
    override func setSelected(_ selected: <PERSON><PERSON>, animated: <PERSON><PERSON>) {
        super.setSelected(selected, animated: animated)
        // Configure the view for the selected state
    }
    
    func setupCell(pointsHistory: PointsHistoryData) {
        if pointsHistory.vOrderNumber ?? "" == "" {
            self.stackViewOrder.isHidden = true
            self.lblAmountValue.text = ""
            self.lblAmount.text = "Gift".localized
            self.lblPoints.text = "Coins:".localized
            self.lblPointsValue.text = "\(pointsHistory.points ?? 0)"
        } else {
            self.stackViewOrder.isHidden = false
            self.lblOrderNo.text = "Order No:".localized
            self.lblOrderNoValue.text = "\(pointsHistory.vOrderNumber ?? "")"
            
            self.lblAmount.text = "Amount:".localized
            self.lblAmountValue.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: pointsHistory.amount ?? 0.0))")
            self.lblPoints.text = "Coins:".localized
            self.lblPointsValue.text = "\(pointsHistory.points ?? 0)"
        }
        if pointsHistory.type ?? "" == "Income" {
            self.imageHistory.image = UIImage(named: "transaction-in")
        } else {
            self.imageHistory.image = UIImage(named: "transaction-out")
        }
    }
    
}
