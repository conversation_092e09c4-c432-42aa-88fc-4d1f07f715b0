<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="21701" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21678"/>
        <capability name="Named colors" minToolsVersion="9.0"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="LoewNextArabic-Bold.ttf">
            <string>LoewNextArabic-Bold</string>
        </array>
        <array key="LoewNextArabic-Medium.ttf">
            <string>LoewNextArabic-Medium</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" reuseIdentifier="PointsHostoryTVCell" id="KGk-i7-Jjw" customClass="PointsHostoryTVCell" customModule="TopCustomer" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="320" height="110"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="320" height="110"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="PrS-5Q-JM7">
                        <rect key="frame" x="20" y="10" width="280" height="90"/>
                        <subviews>
                            <stackView opaque="NO" contentMode="scaleToFill" spacing="20" translatesAutoresizingMaskIntoConstraints="NO" id="eWG-ot-Fdh">
                                <rect key="frame" x="0.0" y="0.0" width="280" height="90"/>
                                <subviews>
                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="Grs-Bj-ToI">
                                        <rect key="frame" x="0.0" y="0.0" width="38" height="90"/>
                                        <subviews>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="gef-Pz-ltS">
                                                <rect key="frame" x="0.0" y="0.0" width="38" height="90"/>
                                                <subviews>
                                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="transaction-in" translatesAutoresizingMaskIntoConstraints="NO" id="oyo-Xo-bF2">
                                                        <rect key="frame" x="0.0" y="27" width="38" height="36"/>
                                                        <constraints>
                                                            <constraint firstAttribute="width" constant="38" id="jih-B0-lk4"/>
                                                            <constraint firstAttribute="height" constant="36" id="ymt-cV-to4"/>
                                                        </constraints>
                                                    </imageView>
                                                </subviews>
                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstItem="oyo-Xo-bF2" firstAttribute="centerX" secondItem="gef-Pz-ltS" secondAttribute="centerX" id="Ae2-9B-NTA"/>
                                                    <constraint firstItem="oyo-Xo-bF2" firstAttribute="centerY" secondItem="gef-Pz-ltS" secondAttribute="centerY" id="G8g-jp-kK9"/>
                                                    <constraint firstAttribute="width" constant="38" id="rtS-jD-BKb"/>
                                                </constraints>
                                            </view>
                                        </subviews>
                                    </stackView>
                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="fillEqually" alignment="top" spacing="5" translatesAutoresizingMaskIntoConstraints="NO" id="Ie9-La-X7Q">
                                        <rect key="frame" x="58" y="0.0" width="222" height="90"/>
                                        <subviews>
                                            <stackView opaque="NO" contentMode="scaleToFill" spacing="5" translatesAutoresizingMaskIntoConstraints="NO" id="OYm-le-GbC">
                                                <rect key="frame" x="0.0" y="0.0" width="143" height="26.666666666666668"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Order No:" lineBreakMode="wordWrap" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="JiR-LE-nCc" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="0.0" width="69" height="26.666666666666668"/>
                                                        <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="14"/>
                                                        <color key="textColor" name="AppTheme_DiffBlackColor_#101113"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="0000000" lineBreakMode="wordWrap" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="GRc-L7-5hO" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                        <rect key="frame" x="74" y="0.0" width="69" height="26.666666666666668"/>
                                                        <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="12"/>
                                                        <color key="textColor" name="AppTheme_DiffBlackColor_#101113"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                </subviews>
                                            </stackView>
                                            <stackView opaque="NO" contentMode="scaleToFill" spacing="5" translatesAutoresizingMaskIntoConstraints="NO" id="pIC-G8-e9z">
                                                <rect key="frame" x="0.0" y="31.666666666666664" width="129.66666666666666" height="26.666666666666664"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Amount:" lineBreakMode="wordWrap" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="rKz-Eq-vkO" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="0.0" width="62.333333333333336" height="26.666666666666668"/>
                                                        <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="14"/>
                                                        <color key="textColor" name="AppTheme_DiffBlackColor_#101113"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="0.0" lineBreakMode="wordWrap" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="LNG-W5-vKt" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                        <rect key="frame" x="67.333333333333343" y="0.0" width="62.333333333333343" height="26.666666666666668"/>
                                                        <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="12"/>
                                                        <color key="textColor" name="AppTheme_DiffBlackColor_#101113"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                </subviews>
                                            </stackView>
                                            <stackView opaque="NO" contentMode="scaleToFill" spacing="5" translatesAutoresizingMaskIntoConstraints="NO" id="kK8-rQ-z17">
                                                <rect key="frame" x="0.0" y="63.333333333333321" width="103" height="26.666666666666664"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Points:" lineBreakMode="wordWrap" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="GJY-yw-Pgj" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="0.0" width="49" height="26.666666666666668"/>
                                                        <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="14"/>
                                                        <color key="textColor" name="AppTheme_DiffBlackColor_#101113"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="0" lineBreakMode="wordWrap" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="leL-HE-5yO" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                        <rect key="frame" x="54" y="0.0" width="49" height="26.666666666666668"/>
                                                        <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="12"/>
                                                        <color key="textColor" name="AppTheme_DiffBlackColor_#101113"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                </subviews>
                                            </stackView>
                                        </subviews>
                                    </stackView>
                                </subviews>
                            </stackView>
                        </subviews>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="eWG-ot-Fdh" firstAttribute="top" secondItem="PrS-5Q-JM7" secondAttribute="top" id="A9t-8n-Mgu"/>
                            <constraint firstItem="eWG-ot-Fdh" firstAttribute="leading" secondItem="PrS-5Q-JM7" secondAttribute="leading" id="eXB-em-UKK"/>
                            <constraint firstAttribute="trailing" secondItem="eWG-ot-Fdh" secondAttribute="trailing" id="myi-xc-s5j"/>
                            <constraint firstAttribute="bottom" secondItem="eWG-ot-Fdh" secondAttribute="bottom" id="uBY-nS-8Wv"/>
                        </constraints>
                    </view>
                </subviews>
                <constraints>
                    <constraint firstItem="PrS-5Q-JM7" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" constant="10" id="6oE-Jk-RNr"/>
                    <constraint firstAttribute="trailing" secondItem="PrS-5Q-JM7" secondAttribute="trailing" constant="20" id="7RJ-dD-P7W"/>
                    <constraint firstAttribute="bottom" secondItem="PrS-5Q-JM7" secondAttribute="bottom" constant="10" id="UzT-ot-e5K"/>
                    <constraint firstItem="PrS-5Q-JM7" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="20" id="aw6-GA-Y5a"/>
                </constraints>
            </tableViewCellContentView>
            <viewLayoutGuide key="safeArea" id="njF-e1-oar"/>
            <connections>
                <outlet property="imageHistory" destination="oyo-Xo-bF2" id="RcC-5P-yTJ"/>
                <outlet property="lblAmount" destination="rKz-Eq-vkO" id="FOc-TZ-zdz"/>
                <outlet property="lblAmountValue" destination="LNG-W5-vKt" id="jJJ-Od-gSu"/>
                <outlet property="lblOrderNo" destination="JiR-LE-nCc" id="h6b-cK-h7L"/>
                <outlet property="lblOrderNoValue" destination="GRc-L7-5hO" id="UtP-PM-dpd"/>
                <outlet property="lblPoints" destination="GJY-yw-Pgj" id="SaQ-sj-kn1"/>
                <outlet property="lblPointsValue" destination="leL-HE-5yO" id="7Tm-Qv-krZ"/>
                <outlet property="stackViewOrder" destination="OYm-le-GbC" id="Mww-cb-vjw"/>
            </connections>
            <point key="canvasLocation" x="-53.435114503816791" y="19.718309859154932"/>
        </tableViewCell>
    </objects>
    <resources>
        <image name="transaction-in" width="43.666667938232422" height="43.666667938232422"/>
        <namedColor name="AppTheme_DiffBlackColor_#101113">
            <color red="0.062745098039215685" green="0.066666666666666666" blue="0.074509803921568626" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
