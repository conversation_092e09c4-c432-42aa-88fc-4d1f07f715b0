//
//  YourRewardsVC.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 12/10/2023.
//  Copyright © 2023 SOTSYS203. All rights reserved.
//

import UIKit
import Lottie

class YourRewardsVC: UIViewController {
    
    // MARK: - IBOutlets
    @IBOutlet weak var viewMyPointsShape: AnimationView!
    @IBOutlet weak var lblYourRewards: UILabel!
    @IBOutlet weak var lblPoints: UILabel!
    @IBOutlet weak var lblBottelsGiftDesc: UILabel!
    @IBOutlet weak var lblCollectMaterialDesc: UILabel!
    @IBOutlet weak var lblBuyProductsDesc: UILabel!
    @IBOutlet weak var btnContinueShopping: UIButton!
    @IBOutlet weak var lblCollect: UILabel!
    @IBOutlet weak var lblCoins: UILabel!
    @IBOutlet weak var tblRewardDetails: UITableView!
    @IBOutlet weak var stackCollectCoins: UIStackView!
    
    // MARK: - Func
    override func viewDidLoad() {
        super.viewDidLoad()
        // Do any additional setup after loading the view.
        self.viewMyPointsShape.stop()
        self.viewMyPointsShape.contentMode = .scaleAspectFill
        self.viewMyPointsShape.loopMode = .playOnce
        tblRewardDetails.delegate = self
        tblRewardDetails.dataSource = self
        let rewardDetailsCell = UINib(nibName: "RewardDetailsCell", bundle: Bundle.main)
        tblRewardDetails.register(rewardDetailsCell, forCellReuseIdentifier: "RewardDetailsCell")
        self.tblRewardDetails.separatorStyle = .none
        self.tblRewardDetails.reloadData()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        self.setlocalized()
        CoinsLevelsShape.shared.getPointsLevels() { level, isGetNextLevel in
            self.viewMyPointsShape.stop()
            let path = Bundle.main.path(forResource: level,
                                        ofType: "json") ?? ""
            self.viewMyPointsShape.animation = Animation.filepath(path)
            self.viewMyPointsShape.play()
            self.setlocalized()
        }
    }
    
    private func setlocalized() {
        self.lblPoints.text = "\(Constant.shared.CURRENT_POINTS_VALUE)"
        if Constant.shared.POINTS_LEVEL >= Constant.shared.POINTS_MAX_LEVELS {
            self.lblBottelsGiftDesc.isHidden = true
            self.lblCoins.isHidden = true
            self.lblCollect.text = "You have achieved the highest level".localized
        } else {
            self.lblCollect.text = "Collect".localized
        }
        self.lblYourRewards.text = "Your rewards".localized
        let coinGift = "To get coin as a gift".localized.replacingOccurrences(of: "%2d", with: "\(Constant.shared.REWARD_POINT_LEVEL)")
        self.lblBottelsGiftDesc.text = coinGift
        self.lblCollectMaterialDesc.text = "collect_material_desc".localized
        self.lblBuyProductsDesc.text = "buy_products_desc".localized
        self.btnContinueShopping.setTitle("continue shopping".localized, for: .normal)
        self.lblCoins.text = "\(Constant.shared.REMAINING_POINT_LEVEL) \("Coins".localized)"
    }
    
    private func updateAnimationFile() {
        self.viewMyPointsShape.stop()
        let path = Bundle.main.path(forResource: Constant.shared.FILE_NAME,
                                    ofType: "json") ?? ""
        self.viewMyPointsShape.animation = Animation.filepath(path)
        
        viewMyPointsShape.contentMode = .scaleAspectFill
        viewMyPointsShape.loopMode = .playOnce
        viewMyPointsShape.play()
    }
    
    @IBAction func closeBtnTapped(_ sender: Any) {
        self.dismiss(animated: true)
    }
    
    @IBAction func continueShoppingBtnTapped(_ sender: Any) {
        self.dismiss(animated: true)
    }
}

// MARK: - Extension
extension YourRewardsVC: UITableViewDataSource, UITableViewDelegate {
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return Constant.shared.POINTS_DESCRIPTION.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "RewardDetailsCell") as! RewardDetailsCell
        cell.setupCell(itemNo: indexPath.row + 1, desc: Constant.shared.POINTS_DESCRIPTION[indexPath.row])
        return cell
    }
    
}
