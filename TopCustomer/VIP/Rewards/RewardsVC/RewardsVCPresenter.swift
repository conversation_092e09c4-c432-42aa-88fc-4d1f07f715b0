//
//  RewardsVCPresenter.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 14/10/2023.
//  Copyright © 2023 SOTSYS203. All rights reserved.
//

import Foundation

protocol RewardsVCPresenterProtocol {
    func apiCallForGetPointsHistory()
    func apiResponseGetPointsHistory(response: PointsHostoryResponse?, error: Error?)
}


class RewardsVCPresenter: RewardsVCPresenterProtocol {
    
    // MARK: Objects & Variables
    weak var viewControllerReward: RewardVCProtocol?
    var interactorReward: RewardsVCInteractorProtocol?
    
    func apiCallForGetPointsHistory() {
        interactorReward?.apiCallForGetPointsHistory()
    }
    
    func apiResponseGetPointsHistory(response: PointsHostoryResponse?, error: Error?) {
        if let error = error  {
            viewControllerReward?.displayAlert(string: error.localizedDescription)
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            return
        }
        
        if code == APICODE400 {
            viewControllerReward?.displayAlert(string: response.responseMessage ?? "")
            return
        }
        
        if code == APIINVALIDAUTHORIZATIONCODE401 {
            AppSingletonObj.displaySessionExpiredAlert(strMsg: response.responseMessage ?? "")
            return
        }
        
        if code == APISUCCESSCODE200 {
            viewControllerReward?.getPointsHistory(data: response.data ?? [])
        }
    }
    
}

