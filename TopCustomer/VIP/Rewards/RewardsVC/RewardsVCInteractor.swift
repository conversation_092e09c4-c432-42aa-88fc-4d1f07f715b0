//
//  RewardsVCInteractor.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 14/10/2023.
//  Copyright © 2023 SOTSYS203. All rights reserved.
//

import UIKit

protocol RewardsVCInteractorProtocol {
    func apiCallForGetPointsHistory()
}

class RewardsVCInteractor: RewardsVCInteractorProtocol {
    
    // MARK: Objects & Variables
    var presenterRewards: RewardsVCPresenterProtocol?
    
    func apiCallForGetPointsHistory() {
        ActivityIndicator.shared.showCentralSpinner()
        UserAPI.getPointsHistory { data, error in
            ActivityIndicator.shared.hideCentralSpinner()
            self.presenterRewards?.apiResponseGetPointsHistory(response: data, error: error)
        }
    }
    
}
