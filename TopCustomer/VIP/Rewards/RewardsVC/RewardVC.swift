//
//  RewardVC.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 14/10/2023.
//  Copyright © 2023 SOTSYS203. All rights reserved.
//

import UIKit

protocol RewardVCProtocol: AnyObject {
    func displayAlert(string:String)
    func navigateToLogin(string:String)
    func getPointsHistory(data: [PointsHistoryData]?)
}

class RewardVC: BaseViewController, RewardVCProtocol {
    
    // MARK: Objects & Variables
    var presenterRewardVC: RewardsVCPresenterProtocol?
    
    // MARK: IBOutlets
    @IBOutlet weak var lblYourRewards: UILabel!
    @IBOutlet weak var lblPoints: UILabel!
    @IBOutlet weak var btnPointsHistory: UIButton!
    @IBOutlet weak var btnRewardDetails: UIButton!
    @IBOutlet weak var lblTabUnderline: UILabel!
    @IBOutlet weak var rootContainerView: UIView!
    @IBOutlet weak var lblTitle: UILabel!
    @IBOutlet weak var topView: UIView!
    @IBOutlet weak var lineView: UIView!
    @IBOutlet weak var lblCount: UILabel!
    @IBOutlet weak var stackViewRewardDetails: UIStackView!
    @IBOutlet weak var tblPointsHistory: UITableView!
    @IBOutlet weak var lblCollectMaterialDesc: UILabel!
    @IBOutlet weak var lblBuyProductsDesc: UILabel!
    @IBOutlet weak var tblRewardDetails: UITableView!
    
    // MARK: - Variables
    var pointsHistory = [PointsHistoryData]()
    
    // MARK: - Init
    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
        setup()
    }
    
    
    // MARK: Setup
    private func setup() {
        let viewController = self
        let interactor = RewardsVCInteractor()
        let presenter = RewardsVCPresenter()
        
        //View Controller will communicate with only presenter
        viewController.presenterRewardVC = presenter
        
        //Presenter will communicate with Interector and Viewcontroller
        presenter.viewControllerReward = viewController
        presenter.interactorReward = interactor
        
        //Interactor will communucate with only presenter.
        interactor.presenterRewards = presenter
    }
    
    
    // MARK: View lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        // Do any additional setup after loading the view.
        tblPointsHistory.delegate = self
        tblPointsHistory.dataSource = self
        let cell = UINib(nibName: "PointsHostoryTVCell", bundle: Bundle.main)
        tblPointsHistory.register(cell, forCellReuseIdentifier: "PointsHostoryTVCell")
        
        tblRewardDetails.delegate = self
        tblRewardDetails.dataSource = self
        let rewardDetailsCell = UINib(nibName: "RewardDetailsCell", bundle: Bundle.main)
        tblRewardDetails.register(rewardDetailsCell, forCellReuseIdentifier: "RewardDetailsCell")
        self.tblRewardDetails.separatorStyle = .none
        self.tblRewardDetails.reloadData()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        self.lblPoints.text = "\(Constant.shared.CURRENT_POINTS_VALUE)"
        setLocalized()
        self.presenterRewardVC?.apiCallForGetPointsHistory()
    }
    
    private func setLocalized() {
        lblTitle.text = "reward".localized
        lblYourRewards.text = "Your rewards".localized
        btnPointsHistory.setTitle("Points History".localized, for: .normal)
        btnRewardDetails.setTitle("Reward Details".localized, for: .normal)
        lblBuyProductsDesc.text = "buy_products_desc".localized
        lblCollectMaterialDesc.text = "collect_material_desc".localized
    }
    
    @IBAction func actionBack(_ sender: UIButton) {
        self.popVC()
    }
    
    @IBAction func actionSelectTab(_ sender: UIButton) {
        self.selectedTab(index: sender.tag)
    }
    
    private func selectedTab(index: Int) {
        switch index {
        case 0:
            var frm = lblTabUnderline.frame
            UIView.animate(withDuration: 0.3) {
                if UserDefaults.standard.getLanguage()! == UserAPI.VLanguage_userLanguage.en.rawValue {       // english
                    frm.origin.x = 0
                }
                else {
                    frm.origin.x = (self.view.frame.size.width / 2)
                }
                self.lblTabUnderline.frame = frm
            }
            
            stackViewRewardDetails.isHidden = true
            tblPointsHistory.isHidden = false
            btnRewardDetails.setTitleColor(.AppTheme_DarkGrayColor_5D5D5D, for: .normal)
            btnPointsHistory.setTitleColor(.AppTheme_DiffBlackColor_101113, for: .normal)
            break
        case 1:
            var frm = lblTabUnderline.frame
            UIView.animate(withDuration: 0.3) {
                if UserDefaults.standard.getLanguage()! == UserAPI.VLanguage_userLanguage.en.rawValue {       // english
                    frm.origin.x = (self.view.frame.size.width / 2)
                }
                else {
                    frm.origin.x = 0
                }
                self.lblTabUnderline.frame = frm
            }
            stackViewRewardDetails.isHidden = false
            tblPointsHistory.isHidden = true
            btnRewardDetails.setTitleColor(.AppTheme_DiffBlackColor_101113, for: .normal)
            btnPointsHistory.setTitleColor(.AppTheme_DarkGrayColor_5D5D5D, for: .normal)
            break
        default:
            print(index)
        }
    }
    
    func getPointsHistory(data: [PointsHistoryData]?) {
        self.pointsHistory = data ?? []
        self.tblPointsHistory.reloadData()
    }
    
    func displayAlert(string: String) {
        AppSingleton.shared.showCustomPopUpWithOkButton(strButtonTitle: ObjKeymessages.kLABEL_OK, strMessage: string, showOnTopVC: false) { (isOk) in
        }
    }
    
    func navigateToLogin(string:String) {
        SocketIOManager.shared.disConnectSocket()
        AppDel?.restartApp()
        AppSingletonObj.showAlert(strMessage: string)
    }
    
}

// MARK: - Extension
extension RewardVC: UITableViewDataSource, UITableViewDelegate {
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        if tableView == tblRewardDetails {
            return Constant.shared.POINTS_DESCRIPTION.count
        } else {
            return self.pointsHistory.count
        }
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        if tableView == tblRewardDetails {
            let cell = tableView.dequeueReusableCell(withIdentifier: "RewardDetailsCell") as! RewardDetailsCell
            cell.setupCell(itemNo: indexPath.row + 1, desc: Constant.shared.POINTS_DESCRIPTION[indexPath.row])
            return cell
        } else {
            let cell = tableView.dequeueReusableCell(withIdentifier: "PointsHostoryTVCell") as! PointsHostoryTVCell
            cell.setupCell(pointsHistory: self.pointsHistory[indexPath.row])
            return cell
        }
    }
    
}
