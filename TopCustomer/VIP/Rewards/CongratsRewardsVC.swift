//
//  CongratsRewardsVC.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 12/10/2023.
//  Copyright © 2023 SOTSYS203. All rights reserved.
//

import UIKit
import Lottie

class CongratsRewardsVC: UIViewController {
    // MARK: - IBOutlets
    @IBOutlet weak var viewMyPointsShape: AnimationView!
    @IBOutlet weak var lblCongrats: UILabel!
    @IBOutlet weak var lblCongratsDesc: UILabel!
    @IBOutlet weak var lblCoins: UILabel!
    @IBOutlet weak var lblCongratsTitle: UILabel!
    
    // MARK: - Variables
    var completionBlock: ((String) -> Void)?
    var timer: Timer?
    var SHAPE_SECONDS_TIMER_LOADING = 3
    
    // MARK: - Func
    override func viewDidLoad() {
        super.viewDidLoad()
        // Do any additional setup after loading the view.
        self.viewMyPointsShape.stop()
        self.viewMyPointsShape.contentMode = .scaleAspectFill
        self.viewMyPointsShape.loopMode = .playOnce
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        setLocalized()
        Constant.shared.POINTS_LEVEL += Constant.shared.CONGRATS_COINS
        lblCoins.text = "\(Constant.shared.CONGRATS_COINS)"
        getTotalUserPoints()
        CoinsLevelsShape.shared.getPointsLevels(isCongratsRewardsVC: true) { level, isGetNextLevel in
            self.viewMyPointsShape.stop()
            let path = Bundle.main.path(forResource: level,
                                        ofType: "json") ?? ""
            self.viewMyPointsShape.animation = Animation.filepath(path)
            self.viewMyPointsShape.play()
            if isGetNextLevel == true {
                self.lblCongrats.isHidden = false
                self.lblCongrats.text = "+\(Constant.shared.REWARD_POINT_LEVEL)"
                self.lblCongrats.alpha = 0
                UIView.animate(withDuration: 1.0) {
                    self.lblCongrats.alpha = 1
                } completion: { value in
                    UIView.animate(withDuration: 3.0) {
                        self.lblCongrats.alpha = 0
                    }
                }
            }
        }
    }
    
    private func getTotalUserPoints() {
        self.lblCoins.text = "\(Constant.shared.CURRENT_POINTS_VALUE)"
        CoinsLevelsShape.shared.getTotalUserPoints { totalCoins in
            self.lblCoins.text = "\(totalCoins)"
        }
    }
    
    private func setLocalized() {
        if Constant.shared.IS_PAYMENT_WITH_COINS {
            let congratsCoins = "You paid with coins.".localized.replacingOccurrences(of: "%2d", with: "\(forTrailingZero(temp: Constant.shared.USED_COINS))")
            self.lblCongratsDesc.text = congratsCoins
        } else {
            self.lblCongratsTitle.isHidden = false
            self.lblCongratsTitle.text = "Congrats !".localized
            let congratsCoins = "You have earned coin by completing your order".localized.replacingOccurrences(of: "%2d", with: "\(forTrailingZero(temp: Constant.shared.CONGRATS_COINS))")
            self.lblCongratsDesc.text = congratsCoins
        }
    }
    
    func stopTimer() {
        self.timer?.invalidate()
        self.timer = nil
    }
    
    @IBAction func closeBtnTapped(_ sender: Any) {
        Constant.shared.IS_PAYMENT_WITH_COINS = false
        self.dismiss(animated: true, completion: nil)
        self.completionBlock?("Complete")
    }
    
}
