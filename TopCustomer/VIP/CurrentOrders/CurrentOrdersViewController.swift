
import UIKit
import StoreKit

protocol GetCurrentOrdersListTotalDelegate: AnyObject {
    func passCurrentOrdersTotal(strTotal: Int)
}

protocol CurrentOrdersProtocol: AnyObject {
    func displayAlert(string:String)
    func loadOrderList(model : OrderListResponseFields)
    func showAlertAndReload(strMessage :  String)
//    func displaySessionExpiredAlert(strMsg: String)

}

class CurrentOrdersViewController: BaseViewController {

    // MARK: Objects & Variables
    var presenterCurrentOrders: CurrentOrdersPresentationProtocol?

    // MARK: IBOutlets
    @IBOutlet weak var tblCurrent: UITableView!
    @IBOutlet weak var lblNoOrderFound: UILabel!
    @IBOutlet weak var lblLooksLike: UILabel!
    @IBOutlet weak var btnGoShopping: CustomRoundedButtton!
    @IBOutlet weak var viewNoData: UIView!
    
    
    var arrCurrentOrders: [OrderResponseFields] = []

    private var totalCount: Int = 0
    private var offset: Int = 1
    weak var delegate: GetCurrentOrdersListTotalDelegate?
    let objKeymessages: KeyMessages = ObjKeymessages

    // MARK: Object lifecycle
    /*
    override init(nibName nibNameOrNil: String?, bundle nibBundleOrNil: Bundle?) {
        super.init(nibName: nibNameOrNil, bundle: nibBundleOrNil)
        setup()
    }
    */
    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
        setup()
    }
    
    // MARK: Setup
    
    private func setup() {
        let viewController = self
        let interactor = CurrentOrdersInteractor()
        let presenter = CurrentOrdersPresenter()
        
        //View Controller will communicate with only presenter
        viewController.presenterCurrentOrders = presenter
        
        //Presenter will communicate with Interector and Viewcontroller
        presenter.viewControllerCurrentOrders = viewController
        presenter.interactorCurrentOrders = interactor
        
        //Interactor will communucate with only presenter.
        interactor.presenterCurrentOrders = presenter
    }
    
    
    // MARK: View lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        self.setupRefreshControlInTableView(tableView: tblCurrent)

        self.setTexts()
    }
    
    override func viewWillAppear(_ animated: Bool) {
//        self.viewWillAppear(animated)
        
        // Request a review
        SKStoreReviewController.requestReview()

        addObservers()

        offset = 1
        self.getCurrentOrders(flagLoader: true)
        NotificationCenter.default.addObserver(self, selector: #selector(openThanksAlert), name: NSNotification.Name(rawValue: "OpenThanksAlert"), object: nil)
    }
    
    override func viewDidDisappear(_ animated: Bool) {
        super.viewDidDisappear(animated)
        NotificationCenter.default.removeObserver(self)
    }

    func addObservers() {
        removeObservers()
        NotificationCenter.default.addObserver(self, selector: #selector(refreshOrdersForIPN), name: NSNotification.Name(rawValue: "PushRefreshCurrentOrdersForIPN"), object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(openDetailScreen), name: NSNotification.Name(rawValue: "OpenCurrentOrderDetail"), object: nil)

    }

    func removeObservers() {
        NotificationCenter.default.removeObserver(self)
    }

    @objc func refreshOrdersForIPN() {
        offset = 1
        self.getCurrentOrders(flagLoader: true)
    }

    @objc func openThanksAlert(_ notification: NSNotification) {
        let vc = ProductPopupStoryboard.instantiate(ThankYouViewController.self)
        vc.modalPresentationStyle = .overFullScreen
        vc.modalTransitionStyle = .crossDissolve
        vc.message = "payment_changed".localized
        
        vc.completionBlock = { [weak self] (strAction) in
            guard let self = self else { return }
            self.dismiss(animated: false, completion: nil)
            if strAction == "Complete" {
                self.dismiss(animated: true, completion: nil)
            }
        }
        self.present(vc, animated: true)
    }
    
    @objc func openDetailScreen(_ notification: NSNotification) {
        if let strOrderId = (notification.object as? [String:Any]) {
            if let id = strOrderId["iOrderId"] as? String {
                let vc = ProductPopupStoryboard.instantiate(OrderDetailViewController.self)
                
                vc.orderId = Int(id) ?? 0
                vc.showCancelOrderPopup = { [weak self] () in
                    DispatchQueue.main.async {
                        /*let alert = UIAlertController(title: AppName, message: ObjKeymessages.kMSG_CANCEL_ORDER_CONFIRMATION, preferredStyle: .alert)
                        let noButton = UIAlertAction(title: ObjKeymessages.kLABEL_NO, style: .default, handler: {(_ action: UIAlertAction) -> Void in
                        })
                        let yesButton = UIAlertAction(title: ObjKeymessages.kLABEL_YES, style: .default, handler: {(_ action: UIAlertAction) -> Void in
                            // show alert to get reason for cancelling order
                            let vc = ProductPopupStoryboard.instantiate(CancelReasonViewController.self)
                            vc.modalPresentationStyle = .overFullScreen
                            vc.modalTransitionStyle = .crossDissolve
                            vc.delegate = self
                            vc.orderId = Int(id) ?? 0
                            self?.presentVC(vc)
                        })
                        alert.addAction(noButton)
                        alert.addAction(yesButton)
                        self?.present(alert, animated: true) {() -> Void in }*/
                        
                        /*let vc = settingsStoryboard.instantiate(CustomPopupAlertViewController.self)
                        vc.modalPresentationStyle = .overFullScreen
                        vc.modalTransitionStyle = .crossDissolve
                        vc.strDescription = ObjKeymessages.kMSG_CANCEL_ORDER_CONFIRMATION
                        vc.strLeftButtonTitle = ObjKeymessages.kLABEL_NO
                        vc.isNeedToSwitchButton = true
                        vc.strRightButtonTitle = ObjKeymessages.kLABEL_YES
                        vc.isCenterButton = false
                        vc.completion = { (index) in
                            if index == 0 {
                            }else{
                                // show alert to get reason for cancelling order
                                let vc = ProductPopupStoryboard.instantiate(CancelReasonViewController.self)
                                vc.modalPresentationStyle = .overFullScreen
                                vc.modalTransitionStyle = .crossDissolve
                                vc.delegate = self
                                vc.orderId = Int(id) ?? 0
                                self?.presentVC(vc)
                            }
                        }
                        self?.presentVC(vc)*/
                        
                        AppSingleton.shared.showCustomPopUpWithYesNoButton(strButton1Title: ObjKeymessages.kLABEL_YES, strButton2Title: ObjKeymessages.kLABEL_NO, strMessage: ObjKeymessages.kMSG_CANCEL_ORDER_CONFIRMATION, showOnTopVC: false) { (isOk) in
                            if isOk == true {
                                // show alert to get reason for cancelling order
                                let vc = ProductPopupStoryboard.instantiate(CancelReasonViewController.self)
                                vc.modalPresentationStyle = .overFullScreen
                                vc.modalTransitionStyle = .crossDissolve
                                vc.delegate = self
                                vc.orderId = Int(id) ?? 0
                                self?.presentVC(vc)
                            }
                        }

                    }
                }
                
                vc.refreshOrderData = { [weak self] () in
                    self?.getCurrentOrders(flagLoader: true)
                }
                
                vc.showUpdateTransactionAlert = { [weak self] (strMsg) in
                    DispatchQueue.main.async {
                        /*let alert = UIAlertController(title: AppName, message: strMsg, preferredStyle: .alert)
                        let okButton = UIAlertAction(title: ObjKeymessages.kLABEL_OK, style: .default, handler: {(_ action: UIAlertAction) -> Void in
                        })
                        alert.addAction(okButton)
                        self?.present(alert, animated: true) {() -> Void in }*/
                        
                        AppSingleton.shared.showCustomPopUpWithOkButton(strButtonTitle: ObjKeymessages.kLABEL_OK, strMessage: strMsg, showOnTopVC: false) { (isOk) in
                        }
                    }
                }
                
                vc.popupDelegate = self
                self.presentVC(vc)
            }
        }
    }

    private func getCurrentOrders(flagLoader: Bool) {
        var dictParam : [String:Any] = [:]
        dictParam["offset"] = offset

        if AppSingletonObj.isConnectedToNetwork(){
            self.presenterCurrentOrders?.apiCallForGetCurrentOrders(dictData: dictParam, flagLoader: flagLoader)
        }
    }

    private func setTexts() {
        lblNoOrderFound.text = ObjKeymessages.kLABEL_NO_ORDER_FOUND
        lblLooksLike.text = ObjKeymessages.kLABEL_NO_ORDER_MADE
        btnGoShopping.setTitle(ObjKeymessages.kLABEL_GO_SHOPPING, for: .normal)
    }
    
    @IBAction func btnGoShoppingAction(_ sender: Any) {
        self.tabBarController?.selectedIndex = 0
    }

    override func didPullToRefresh(_ sender: UIRefreshControl) {
        if AppSingletonObj.isConnectedToNetwork(){
            self.tblCurrent.refreshControl?.beginRefreshing()
            offset = 1
            self.getCurrentOrders(flagLoader: false)
        }
        else {
            ez.runThisAfterDelay(seconds: 1.0) {
                self.endRefresing()
            }
        }
    }

}

extension CurrentOrdersViewController : UITableViewDelegate, UITableViewDataSource{
    func numberOfSections(in tableView: UITableView) -> Int {
        return 1
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return arrCurrentOrders.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        guard let cell = tblCurrent.dequeueReusableCell(withIdentifier: CellIdentifiers.kCURRENT_ORDERS_CELL) as? CurrentOrdersTableViewCell else {
            return UITableViewCell()
        }

        cell.lblOrderNoValue.text = arrCurrentOrders[indexPath.row].vOrderNumber

        // convert date to desired format
        /*let strDate = arrCurrentOrders[indexPath.row].tsOrderedAt
        let strFormattedDate = strDate?.utcToLocal(dateStr: strDate ?? "")
        cell.lblDateValue.text = strFormattedDate*/

        if arrCurrentOrders[indexPath.row].tsExpectedStartTime != "" && arrCurrentOrders[indexPath.row].tsExpectedEndTime != "" {
            // convert date to desired format
            let strStartTime = arrCurrentOrders[indexPath.row].tsExpectedStartTime
            let strEndTime = arrCurrentOrders[indexPath.row].tsExpectedEndTime
            let strFormattedStartTime = "\(strEndTime?.checkDateIsTodayOrTomorrow() ?? "") \(strEndTime?.getTimeOnlyWithDate(dateStr: strStartTime ?? "") ?? "")" //.utcToLocalTimeOnlyWithDate(dateStr: strStartTime ?? "")
            let strFormattedEndTime = strEndTime?.utcToLocalTimeOnly(dateStr: strEndTime ?? "")
            cell.lblDateValue.text = "\(objKeymessages.kLABEL_EXPECTED_DELIVERY_TIME): \(strFormattedStartTime ?? "") \(objKeymessages.kLABEL_TO) \(strFormattedEndTime ?? "")"
            
            let string = cell.lblDateValue.text ?? ""
            let range = (string as NSString).range(of: "\(objKeymessages.kLABEL_EXPECTED_DELIVERY_TIME):")
            let attributedString = NSMutableAttributedString(string: string)
            attributedString.addAttribute(NSAttributedString.Key.font, value: UIFont(name: Fonts.LoewNextArabicExtraBold, size: 14)!, range: range)
            attributedString.addAttribute(NSAttributedString.Key.foregroundColor, value: UIColor.black, range: range)
            cell.lblDateValue.attributedText = attributedString
        }
        else { // hide expected delivery time label
            let string = "\(objKeymessages.kLABEL_EXPECTED_DELIVERY_TIME):"
            let range = (string as NSString).range(of: string)
            let attributedString = NSMutableAttributedString(string: string)
            attributedString.addAttribute(NSAttributedString.Key.font, value: UIFont(name: Fonts.LoewNextArabicExtraBold, size: 14)!, range: range)
            attributedString.addAttribute(NSAttributedString.Key.foregroundColor, value: UIColor.black, range: range)
            cell.lblDateValue.attributedText = attributedString
        }

        if arrCurrentOrders[indexPath.row].tiOrderStatus == OrderStatus.OrderPlaced.rawValue || arrCurrentOrders[indexPath.row].tiOrderStatus == OrderStatus.OrderAssigned.rawValue || arrCurrentOrders[indexPath.row].tiOrderStatus == OrderStatus.OrderInProgress.rawValue {
            cell.lblOrderStatus.text = objKeymessages.kLABEL_ORDER_RECEIVED
        }
        else if arrCurrentOrders[indexPath.row].tiOrderStatus == OrderStatus.OrderReady.rawValue {
            cell.lblOrderStatus.text = objKeymessages.kLABEL_READY

        }
        else if arrCurrentOrders[indexPath.row].tiOrderStatus == OrderStatus.OrderOnTheWay.rawValue {
            cell.lblOrderStatus.text = objKeymessages.kLABEL_ON_THE_WAY

        }
        else if arrCurrentOrders[indexPath.row].tiOrderStatus == OrderStatus.OrderDelivered.rawValue {
            cell.lblOrderStatus.text = objKeymessages.kLABEL_DELIVERED
        }

        // 0- pending
        // 1- success
        // 2- fail
        if arrCurrentOrders[indexPath.row].tiPaymentStatus != 1 && arrCurrentOrders[indexPath.row].tiTransactionType != TransactionType.COD.rawValue { // not success
            //Pending
            cell.lblOrderStatus.text = ObjKeymessages.kLABEL_ORDER_PENDING
        }
        return cell
    }
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        let vc = ProductPopupStoryboard.instantiate(OrderDetailViewController.self)
//            vc.modalPresentationStyle = .fullScreen
//        vc.modalPresentationStyle = .overFullScreen
//        vc.modalTransitionStyle = .crossDissolve
        
        vc.orderId = arrCurrentOrders[indexPath.row].iOrderId ?? 0
        vc.showCancelOrderPopup = { [weak self] () in
            DispatchQueue.main.async {
                /*let alert = UIAlertController(title: AppName, message: ObjKeymessages.kMSG_CANCEL_ORDER_CONFIRMATION, preferredStyle: .alert)
                let noButton = UIAlertAction(title: ObjKeymessages.kLABEL_NO, style: .default, handler: {(_ action: UIAlertAction) -> Void in
                })
                let yesButton = UIAlertAction(title: ObjKeymessages.kLABEL_YES, style: .default, handler: {(_ action: UIAlertAction) -> Void in
                    
                    /*var dictParam : [String:Any] = [:]
                    dictParam["iOrderId"] = self?.arrCurrentOrders[indexPath.row].iOrderId ?? 0
                    
                    if AppSingletonObj.isConnectedToNetwork(){
                        self?.presenterCurrentOrders?.apiCallForCancelOrder(dictData: dictParam)
                    }*/
                    
                    // show alert to get reason for cancelling order
                    let vc = ProductPopupStoryboard.instantiate(CancelReasonViewController.self)
                    vc.modalPresentationStyle = .overFullScreen
                    vc.modalTransitionStyle = .crossDissolve
                    vc.delegate = self
                    vc.orderId = self?.arrCurrentOrders[indexPath.row].iOrderId ?? 0
                    self?.presentVC(vc)


                })
                alert.addAction(noButton)
                alert.addAction(yesButton)
                self?.present(alert, animated: true) {() -> Void in }*/

                /*let vc = settingsStoryboard.instantiate(CustomPopupAlertViewController.self)
                vc.modalPresentationStyle = .overFullScreen
                vc.modalTransitionStyle = .crossDissolve
                vc.strDescription = ObjKeymessages.kMSG_CANCEL_ORDER_CONFIRMATION
                vc.strLeftButtonTitle = ObjKeymessages.kLABEL_NO
                vc.isNeedToSwitchButton = true
                vc.strRightButtonTitle = ObjKeymessages.kLABEL_YES
                vc.isCenterButton = false
                vc.completion = { (index) in
                    if index == 0 {
                    }else{
                        // show alert to get reason for cancelling order
                        let vc = ProductPopupStoryboard.instantiate(CancelReasonViewController.self)
                        vc.modalPresentationStyle = .overFullScreen
                        vc.modalTransitionStyle = .crossDissolve
                        vc.delegate = self
                        vc.orderId = self?.arrCurrentOrders[indexPath.row].iOrderId ?? 0
                        self?.presentVC(vc)
                    }
                }
                self?.presentVC(vc)*/
                
                AppSingleton.shared.showCustomPopUpWithYesNoButton(strButton1Title: ObjKeymessages.kLABEL_YES, strButton2Title: ObjKeymessages.kLABEL_NO, strMessage: ObjKeymessages.kMSG_CANCEL_ORDER_CONFIRMATION, showOnTopVC: false) { (isOk) in
                    if isOk == true {
                        // show alert to get reason for cancelling order
                        let vc = ProductPopupStoryboard.instantiate(CancelReasonViewController.self)
                        vc.modalPresentationStyle = .overFullScreen
                        vc.modalTransitionStyle = .crossDissolve
                        vc.delegate = self
                        vc.orderId = self?.arrCurrentOrders[indexPath.row].iOrderId ?? 0
                        self?.presentVC(vc)
                    }
                }

            }
            

        }
        
        vc.refreshOrderData = { [weak self] () in
            self?.getCurrentOrders(flagLoader: true)
        }
        
        vc.showUpdateTransactionAlert = { [weak self] (strMsg) in
            DispatchQueue.main.async {
                /*let alert = UIAlertController(title: AppName, message: strMsg, preferredStyle: .alert)
                let okButton = UIAlertAction(title: ObjKeymessages.kLABEL_OK, style: .default, handler: {(_ action: UIAlertAction) -> Void in
                })
                alert.addAction(okButton)
                self?.present(alert, animated: true) {() -> Void in }*/
                
                AppSingleton.shared.showCustomPopUpWithOkButton(strButtonTitle: ObjKeymessages.kLABEL_OK, strMessage: strMsg, showOnTopVC: false) { (isOk) in
                }
            }
        }
        
        vc.popupDelegate = self
        
        self.presentVC(vc)

    }
    
    func tableView(_ tableView: UITableView, willDisplay cell: UITableViewCell, forRowAt indexPath: IndexPath) {
        if indexPath.row == (self.arrCurrentOrders.count ) - 1  {
            if (self.arrCurrentOrders.count ) < totalCount {
                offset = offset + 1
                
                var dictParam : [String:Any] = [:]
                dictParam["offset"] = offset

                if AppSingletonObj.isConnectedToNetwork(){
                    self.presenterCurrentOrders?.apiCallForGetCurrentOrders(dictData: dictParam, flagLoader: false)
                }
            }
        }
    }

}

extension CurrentOrdersViewController: CurrentOrdersProtocol {
    func displayAlert(string: String) {
//        self.showAlert(title: AppName, message: string)
        AppSingleton.shared.showCustomPopUpWithOkButton(strButtonTitle: ObjKeymessages.kLABEL_OK, strMessage: string, showOnTopVC: false) { (isOk) in
        }
    }

    func loadOrderList(model : OrderListResponseFields) {
        self.endRefresing()

        self.totalCount = model.totalRecord ?? 0

        // emtpy the array if it is initial call
        if self.offset == 1 {
            self.arrCurrentOrders.removeAll()
        }
        self.arrCurrentOrders.append(contentsOf: model.orders ?? [])

        if self.arrCurrentOrders.count <= 0 {  // show no data view
            viewNoData.isHidden = false
            tblCurrent.isHidden = true
            self.tabBarController?.tabBar.items?[3].badgeValue = nil
        }
        else {
            viewNoData.isHidden = true
            tblCurrent.isHidden = false
//            self.tabBarController?.tabBar.items?[3].badgeValue = ""
            self.tabBarController?.tabBar.items?[3].badgeValue = "●"
            self.tabBarController?.tabBar.items?[3].badgeColor = .clear
            self.tabBarController?.tabBar.items?[3].setBadgeTextAttributes([NSAttributedString.Key.foregroundColor: UIColor.AppTheme_SelectedTabColor_F4BA45], for: .normal)
            self.tabBarController?.tabBar.items?[3].setBadgeTextAttributes([NSAttributedString.Key.foregroundColor: UIColor.AppTheme_SelectedTabColor_F4BA45], for: .selected)

        }
        
        // get total and update UI
        if self.delegate != nil{
            self.delegate?.passCurrentOrdersTotal(strTotal: model.totalRecord ?? 0)
        }

        tblCurrent.reloadData()

    }

    func showAlertAndReload(strMessage :  String) {
//        showAlert(title: AppName, message: strMessage, buttonTitles: [ObjKeymessages.kLABEL_OK], highlightedButtonIndex: 0) { void in
            AppSingletonObj.showAlert(strMessage: strMessage)
            self.offset = 1
            self.getCurrentOrders(flagLoader: true)
//        }
    }

    /*func displaySessionExpiredAlert(strMsg: String) {
        let alert = UIAlertController(title: AppName, message: strMsg, preferredStyle: .alert)
        let okButton = UIAlertAction(title: ObjKeymessages.kLABEL_OK, style: .default, handler: {(_ action: UIAlertAction) -> Void in
            AppDel?.restartApp()
            self.dismiss(animated: true, completion: nil)
        })
        alert.addAction(okButton)
        self.present(alert, animated: true) {() -> Void in }
    }*/

}

extension String {
    func getDateWithFormat(strFormat: String) -> Date {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = strFormat //"yyyy-MM-dd HH:mm:ss"
        let date = dateFormatter.date(from: self)
        return date ?? Date()
    }

    func utcToLocal(dateStr: String) -> String? {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
        dateFormatter.timeZone = TimeZone(abbreviation: "UTC")
        dateFormatter.locale = Locale(identifier: "EN")
        if let date = dateFormatter.date(from: dateStr) {
            dateFormatter.timeZone = TimeZone.current
            dateFormatter.dateFormat = "dd-MM-yyyy hh:mm a"
            if UserDefaults.standard.getLanguage() == "en" {
                return dateFormatter.string(from: date)
            } else {
                return dateFormatter.string(from: date).getTimingAccordingToCurrentLanguage()
            }
        }
        return nil
    }
    
    func utcToLocalTimeOnly(dateStr: String) -> String? {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
        dateFormatter.locale = Locale(identifier: "EN")
        
        if let date = dateFormatter.date(from: dateStr) {
            dateFormatter.timeZone = TimeZone.current
            dateFormatter.dateFormat = "hh:mm a"
        
            if UserDefaults.standard.getLanguage() == "en" {
                return dateFormatter.string(from: date)
            } else {
                return dateFormatter.string(from: date).getTimingAccordingToCurrentLanguage()
            }
        }
        return nil
    }
    
    func utcToLocalTimeOnlyDate() -> Date? {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "hh:mm"
        if let date = dateFormatter.date(from: self) {
            dateFormatter.dateFormat = "hh:mm"
            return dateFormatter.date(from: dateFormatter.string(from: date))
        }
        return nil
    }

    func utcToLocalTimeOnlyWithDate(dateStr: String) -> String? {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
        dateFormatter.timeZone = TimeZone(abbreviation: "UTC")
        dateFormatter.locale = Locale(identifier: "EN")
        
        if let date = dateFormatter.date(from: dateStr) {
            dateFormatter.timeZone = TimeZone.current
            dateFormatter.dateFormat = "hh:mm a"
        
            let today = Calendar.current.isDateInToday(date)
            print(today)
            
            if today == true {  // Date is of Today
                
                if UserDefaults.standard.getLanguage() == "en" {
                    return "\(ObjKeymessages.kLABEL_TODAY) \(ObjKeymessages.kLABEL_FROM.lowercased()) \(dateFormatter.string(from: date))"
                } else {
                    return "\(ObjKeymessages.kLABEL_TODAY) \(ObjKeymessages.kLABEL_FROM.lowercased()) \(dateFormatter.string(from: date).getTimingAccordingToCurrentLanguage())"
                }
            }
            else {
                if UserDefaults.standard.getLanguage() == "en" {
                    return "\(dateStr.dateTime?.convertDateToStringByLocale(dateFormat: "MM-dd") ?? "") \(ObjKeymessages.kLABEL_FROM.lowercased()) \(dateFormatter.string(from: date))"
                } else {
                    return "\(dateStr.dateTime?.convertDateToStringByLocale(dateFormat: "MM-dd") ?? "") \(ObjKeymessages.kLABEL_FROM.lowercased()) \(dateFormatter.string(from: date).getTimingAccordingToCurrentLanguage())"
                }
            }
        }
        return nil
    }
    
    func getTimeOnlyWithDate(dateStr: String) -> String? {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
        dateFormatter.locale = Locale(identifier: "EN")
        
        if let date = dateFormatter.date(from: dateStr) {
            dateFormatter.timeZone = TimeZone.current
            dateFormatter.dateFormat = "hh:mm a"
        
            let today = Calendar.current.isDateInToday(date)
            print(today)
            
            if today == true {  // Date is of Today
                
                if UserDefaults.standard.getLanguage() == "en" {
                    return "\(ObjKeymessages.kLABEL_FROM.lowercased()) \(dateFormatter.string(from: date))"
                } else {
                    return "\(ObjKeymessages.kLABEL_FROM.lowercased()) \(dateFormatter.string(from: date).getTimingAccordingToCurrentLanguage())"
                }
            }
            else {
                if UserDefaults.standard.getLanguage() == "en" {
                    return "\(ObjKeymessages.kLABEL_FROM.lowercased()) \(dateFormatter.string(from: date))"
                } else {
                    return "\(ObjKeymessages.kLABEL_FROM.lowercased()) \(dateFormatter.string(from: date).getTimingAccordingToCurrentLanguage())"
                }
            }
        }
        return nil
    }

    func getDate(dateFormat: String) -> String {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = dateFormat
        return dateFormatter.string(from: self.getDateMonthAndDayByLocale ?? Date())
    }
}

extension CurrentOrdersViewController: BottomPopupDelegate {
    
    func bottomPopupDidDismiss() {
        print("Dismissed")
    }
    
}

extension CurrentOrdersViewController : ReloadOrderListDelegate {
    func reloadScreen() {
        offset = 1
        self.getCurrentOrders(flagLoader: true)
    }
    
}
