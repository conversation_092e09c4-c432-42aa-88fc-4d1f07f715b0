
import UIKit

protocol CurrentOrdersInteractorProtocol {
    func apiCallForGetCurrentOrders(dictData:[String:Any], flagLoader: Bool)
    func apiCallForCancelOrder(dictData:[String:Any])

}

protocol CurrentOrdersDataStore {
    //{ get set }
}

class CurrentOrdersInteractor: CurrentOrdersInteractorProtocol, CurrentOrdersDataStore {

    // MARK: Objects & Variables
    var presenterCurrentOrders: CurrentOrdersPresentationProtocol?
    
    func apiCallForGetCurrentOrders(dictData:[String:Any], flagLoader: Bool) {
        if flagLoader == true {
            ActivityIndicator.shared.showCentralSpinner()
        }
        
        let authorization = getAuthorizationText()
        
        OrderAPI.getOrderList(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, iOrderType: OrderAPI.IOrderType_getOrderList._1, offset: Int(setDataInString(dictData["offset"] as AnyObject)) ?? 0) { data, error in
                ActivityIndicator.shared.hideCentralSpinner()
                self.presenterCurrentOrders?.apiResponseGetCurrentOrders(response: data, error: error)

        }
        
    }
    
    func apiCallForCancelOrder(dictData:[String:Any]) {
        
        ActivityIndicator.shared.showCentralSpinner()
        
        let authorization = getAuthorizationText()
        
        OrderAPI.cancelOrder(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, iOrderId: Int(setDataInString(dictData["iOrderId"] as AnyObject)) ?? 0) { data, error in
                ActivityIndicator.shared.hideCentralSpinner()
                self.presenterCurrentOrders?.apiResponseCancelOrder(response: data, error: error)

        }
        
        
    }

}
