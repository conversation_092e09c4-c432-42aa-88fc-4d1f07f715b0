
import UIKit

protocol CurrentOrdersPresentationProtocol {
    func apiCallForGetCurrentOrders(dictData:[String:Any], flagLoader: Bool)
    func apiResponseGetCurrentOrders(response:OrderListResponse?,error:Error?)

    func apiCallForCancelOrder(dictData:[String:Any])
    func apiResponseCancelOrder(response:CommonFields?,error:Error?)

}

class CurrentOrdersPresenter: CurrentOrdersPresentationProtocol {
    
    // MARK: Objects & Variables
    weak var viewControllerCurrentOrders: CurrentOrdersProtocol?
    var interactorCurrentOrders: CurrentOrdersInteractorProtocol?
    
    func apiCallForGetCurrentOrders(dictData:[String:Any], flagLoader: Bool) {
        interactorCurrentOrders?.apiCallForGetCurrentOrders(dictData: dictData, flagLoader: flagLoader)
    }

    func apiResponseGetCurrentOrders(response: OrderListResponse?, error: Error?) {
        if let vc = self.viewControllerCurrentOrders as? BaseViewController {
            DispatchQueue.main.async {
                vc.endRefresing()
            }
        }
        if let error = error  {
            viewControllerCurrentOrders?.displayAlert(string: error.localizedDescription)
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            //            viewController?.displayAlert(strTitle: "", strMessage: KeyMessages.kInvalidateResponse)
            return
        }
        
        if code == APIINVALIDAUTHORIZATIONCODE401 {
//            AppSingletonObj.goToLoginScreen(message: response.responseMessage ?? "")
//            self.viewControllerCurrentOrders?.displaySessionExpiredAlert(strMsg: response.responseMessage ?? "")
            AppSingletonObj.displaySessionExpiredAlert(strMsg: response.responseMessage ?? "")
            return
        }

        if code == APICODE400 {
            viewControllerCurrentOrders?.displayAlert(string: response.responseMessage ?? "")
            return
        }
        
        guard let model = response.responseData,code == APISUCCESSCODE200  else {
            return
        }
        self.viewControllerCurrentOrders?.loadOrderList(model: model)
    }

    func apiCallForCancelOrder(dictData:[String:Any]) {
        interactorCurrentOrders?.apiCallForCancelOrder(dictData: dictData)
    }

    func apiResponseCancelOrder(response:CommonFields?,error:Error?) {
//        if let vc = self.viewControllerCurrentOrders as? BaseViewController {
//            DispatchQueue.main.async {
//                vc.endRefresing()
//            }
//        }
        if let error = error  {
            viewControllerCurrentOrders?.displayAlert(string: error.localizedDescription)
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            //            viewController?.displayAlert(strTitle: "", strMessage: KeyMessages.kInvalidateResponse)
            return
        }
        
        if code == APIINVALIDAUTHORIZATIONCODE401 {
//            AppSingletonObj.goToLoginScreen(message: response.responseMessage ?? "")
//            self.viewControllerCurrentOrders?.displaySessionExpiredAlert(strMsg: response.responseMessage ?? "")
            AppSingletonObj.displaySessionExpiredAlert(strMsg: response.responseMessage ?? "")
            return
        }

        if code == APICODE400 {
            viewControllerCurrentOrders?.displayAlert(string: response.responseMessage ?? "")
            return
        }
        
//        guard let model = response.responseData,code == APISUCCESSCODE200  else {
//            return
//        }
        self.viewControllerCurrentOrders?.showAlertAndReload(strMessage: response.responseMessage ?? "")
    }

}
