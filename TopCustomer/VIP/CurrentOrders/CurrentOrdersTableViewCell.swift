
import UIKit

class CurrentOrdersTableViewCell: UITableViewCell {

    @IBOutlet weak var lblOrderNoTitle: UILabel!
    @IBOutlet weak var lblOrderNoValue: UILabel!
//    @IBOutlet weak var lblDateTitle: UILabel!
    @IBOutlet weak var lblDateValue: UILabel!
    @IBOutlet weak var lblOrderStatus: UILabel!
    @IBOutlet weak var viewOuter: UIView!
    
    
    override func awakeFromNib() {
        super.awakeFromNib()
        
        lblOrderNoTitle.text = "\(ObjKeymessages.kLABEL_ORDER_NO):"
//        lblDateTitle.text = "\(ObjKeymessages.kLABEL_DATE_ONLY):"
//        lblOrderStatus.text = ObjKeymessages.kLABEL_DELIVERED
//        lblDateTitle.text = "\(ObjKeymessages.kLABEL_EXPECTED_DELIVERY_TIME):"
//        lblDateValue.text = "\(ObjKeymessages.kLABEL_EXPECTED_DELIVERY_TIME):"
    }

    override func setSelected(_ selected: Bool, animated: Bool) {
        super.setSelected(selected, animated: animated)

        // Configure the view for the selected state
    }

}
