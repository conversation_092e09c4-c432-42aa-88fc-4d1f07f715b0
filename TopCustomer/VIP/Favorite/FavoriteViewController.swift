
import UIKit

protocol FavoriteProtocol: AnyObject {
    func displayAlert(string:String)
    func loadOrderList(model : ProductListResponseFields)
    func refreshFavList(string:String)

}

protocol UpdateCountFromFavoriteProtocol {
    func updateCartCountFromFavorite(cartCount:Int?)
}

class FavoriteViewController: BaseViewController, FavoriteProtocol {

    // MARK: Objects & Variables
    var presenterFavorite: FavoritePresentationProtocol?

    // MARK: IBOutlets
    @IBOutlet weak var collectionView: UICollectionView!
    @IBOutlet weak var lblTitle: UILabel!
    @IBOutlet weak var lblNoData: UILabel!
    @IBOutlet weak var activityIndicatorView: UIActivityIndicatorView!
    @IBOutlet weak var cons_activityIndicatorView_height: NSLayoutConstraint!
    @IBOutlet weak var stackViewActivityIndicatorView: UIStackView!
    @IBOutlet weak var btnDeleteAllFavProducts: MaterialLocalizeButton!
    
    private var offset: Int = 1
    private var totalCount: Int = 0
    var arrFavoriteProducts: [ProductResponseFields] = []
    var delegate : UpdateCountFromFavoriteProtocol?

    // MARK: Object lifecycle
    /*
    override init(nibName nibNameOrNil: String?, bundle nibBundleOrNil: Bundle?) {
        super.init(nibName: nibNameOrNil, bundle: nibBundleOrNil)
        setup()
    }
    */
    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
        setup()
    }
    
    // MARK: Setup
    
    private func setup() {
        let viewController = self
        let interactor = FavoriteInteractor()
        let presenter = FavoritePresenter()
        
        //View Controller will communicate with only presenter
        viewController.presenterFavorite = presenter
        
        //Presenter will communicate with Interector and Viewcontroller
        presenter.viewControllerFavorite = viewController
        presenter.interactorFavorite = interactor
        
        //Interactor will communucate with only presenter.
        interactor.presenterFavorite = presenter
    }
    
    // MARK: View lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        lblTitle.text = ObjKeymessages.kLABEL_FAVORITE
        lblNoData.text = ObjKeymessages.kMSG_EMPTY_FAV_PRODUCT
        self.collectionView.registerCell(cell: ProductsCollectionViewCellSmall.self)
        
        offset = 1
        var dictParam : [String:Any] = [:]
        dictParam["offset"] = offset
        self.getFavoriteOrders(dictData: dictParam, flagLoader: true)

    }
    
    private func getFavoriteOrders(dictData:[String:Any], flagLoader: Bool) {
        if AppSingletonObj.isConnectedToNetwork(){
            self.presenterFavorite?.apiCallForGetFavoriteProducts(dictData: dictData, flagLoader: flagLoader)
        }
    }

    @IBAction func actionBack(_ sender: UIButton) {
        self.popVC()
    }

    @IBAction func actionDeleteAll(_ sender: UIButton) {
        AppSingleton.shared.showCustomPopUpWithYesNoButton(strButton1Title: ObjKeymessages.kLABEL_YES, strButton2Title: ObjKeymessages.kLABEL_NO, strMessage: ObjKeymessages.kMSG_DELETE_ALL_FAVORITE, showOnTopVC: false) { (isOk) in
            if isOk == true {
                if AppSingletonObj.isConnectedToNetwork(){
                    self.presenterFavorite?.apiCallForDeleteFavoriteProducts()
                }
            }
        }
    }

    func displayAlert(string: String) {
        AppSingleton.shared.showCustomPopUpWithOkButton(strButtonTitle: ObjKeymessages.kLABEL_OK, strMessage: string, showOnTopVC: false) { (isOk) in
        }
    }

    func loadOrderList(model : ProductListResponseFields) {

        cons_activityIndicatorView_height.constant = 0
        stackViewActivityIndicatorView.isHidden = true
        activityIndicatorView.isHidden = true
        activityIndicatorView.stopAnimating()

        self.totalCount = model.totalRecord ?? 0

        // emtpy the array if it is initial call
        if self.offset == 1 {
            self.arrFavoriteProducts.removeAll()
        }
        self.arrFavoriteProducts.append(contentsOf: model.productList ?? [])

        if arrFavoriteProducts.count == 0 {
            lblNoData.isHidden = false
            btnDeleteAllFavProducts.isHidden = true
        }
        else {
            lblNoData.isHidden = true
            btnDeleteAllFavProducts.isHidden = false
        }

        collectionView.reloadData()
    }

    func refreshFavList(string:String) {
        AppSingletonObj.showAlert(strMessage: string)
        self.totalCount = 0
        self.arrFavoriteProducts.removeAll()
        lblNoData.isHidden = false
        btnDeleteAllFavProducts.isHidden = true
        collectionView.reloadData()
    }

}

extension FavoriteViewController: UICollectionViewDelegate, UICollectionViewDataSource, UICollectionViewDelegateFlowLayout {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return arrFavoriteProducts.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeue(with: ProductsCollectionViewCellSmall.self, for: indexPath)
        cell.isAppadvertisement(value: arrFavoriteProducts[indexPath.row].isAppAdvertisement == 1 ? false : true)
        cell.isQuantitiesDiscountHidden(value: arrFavoriteProducts[indexPath.row].quantityDiscount ?? 1 == 1 ? false : true)
        cell.lblProductName.text = "\(arrFavoriteProducts[indexPath.row].vProductName ?? "")"
        cell.lblPriceTitle.text = "\(arrFavoriteProducts[indexPath.row].vProductUnit ?? "")"
        cell.lblPrice.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(arrFavoriteProducts[indexPath.row].dbOriginalProductPrice?.toDouble() ?? 0.0)")
        if arrFavoriteProducts[indexPath.row].offertitle != nil && arrFavoriteProducts[indexPath.row].offertitle != "" {
            cell.viewOfferTitle.isHidden = false
            cell.lblOfferTitle.text = "\("Offer".localized)"
        } else {
            cell.viewOfferTitle.isHidden = true
        }
        if arrFavoriteProducts[indexPath.row].isLowQuantity == 0 {  // 0 - hide low quantity label
            cell.viewLowQuantity.isHidden = true
            
            cell.imgProduct.kf.indicatorType = .activity
            let url = URL(string: arrFavoriteProducts[indexPath.row].vProductImage ?? "")
            cell.imgProduct.kf.setImage(with: url , placeholder: UIImage(named: "placeholder_product"))
        }
        else {  // 1 - show low quantity label
            if arrFavoriteProducts[indexPath.row].iHowMuchLeftInStock ?? 0 <= 0 {  // out of stock
                cell.viewLowQuantity.isHidden = true
                
                cell.imgProduct.kf.indicatorType = .activity
                let url = URL(string: arrFavoriteProducts[indexPath.row].vProductImage ?? "")
                
                cell.imgProduct.kf.setImage(with: url , placeholder: UIImage(named: "placeholder_product")) { result in
                   switch result {
                   case .success(let value):
                       print("Image: \(value.image). Got from: \(value.cacheType)")
                       cell.imgProduct.image = value.image.grayed
                   case .failure(let error):
                       print("Error: \(error)")
                   }
                 }
            }
            else {
                cell.viewLowQuantity.isHidden = false
                
                cell.imgProduct.kf.indicatorType = .activity
                let url = URL(string: arrFavoriteProducts[indexPath.row].vProductImage ?? "")
                cell.imgProduct.kf.setImage(with: url , placeholder: UIImage(named: "placeholder_product"))
            }
        }

        // discount logic
        if arrFavoriteProducts[indexPath.row].tiDiscountType != 1 {  // no discount
            cell.lblPrice.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(arrFavoriteProducts[indexPath.row].dbOriginalProductPrice?.toDouble() ?? 0.0)")
            cell.lblOldPrice.isHidden = true
            cell.lblOffer.isHidden = true
        }
        else {  // discount is there
            cell.lblPrice.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(arrFavoriteProducts[indexPath.row].dDiscountedProductPrice?.toDouble() ?? 0.0)")
            cell.lblOldPrice.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(arrFavoriteProducts[indexPath.row].dbOriginalProductPrice?.toDouble() ?? 0.0)")
            cell.lblOldPrice.isHidden = false
            
            cell.lblOffer.isHidden = false
            cell.lblOffer.text = "( \(arrFavoriteProducts[indexPath.row].dDiscountAmount?.toDouble() ?? 0.0)% \(ObjKeymessages.kLABEL_OFF) )"
            let originalPrice = Double(arrFavoriteProducts[indexPath.row].dbOriginalProductPrice?.toDouble() ?? 0.0)
            let discountedPrice = Double(arrFavoriteProducts[indexPath.row].dDiscountedProductPrice?.toDouble() ?? 0.0)
            let a = originalPrice - discountedPrice
            let b = a / originalPrice
            let percent = b * 100
            cell.lblOffer.text = "(\(forTrailingZero(temp: percent.rounded(.up)))% \(ObjKeymessages.kLABEL_OFF))"

        }
        // discount logic ends here

        if arrFavoriteProducts[indexPath.row].iHowMuchLeftInStock ?? 0 <= 0 {  // out of stock
            cell.viewOutOfStock.isHidden = false
        }
        else {
            cell.viewOutOfStock.isHidden = true
        }
        if arrFavoriteProducts[indexPath.row].quantityDiscount ?? 1 == 0 {
            cell.stackViewDiscountPrice.isHidden = false
        } else {
            let priceMin = self.arrFavoriteProducts[indexPath.row].prices?.map({$0.price?.toDouble() ?? 0.0}).min() ?? 0.0
            let priceMax = self.arrFavoriteProducts[indexPath.row].prices?.map({$0.price?.toDouble() ?? 0.0}).max() ?? 0.0
            cell.lblPrice.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: priceMin)) - \(forTrailingZero(temp: priceMax))")
            cell.stackViewDiscountPrice.isHidden = true
        }
        
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        return CGSize(width: (self.collectionView.frame.width - 20) / 3, height: 200)
    }
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        self.goToProductDetailScreen(obj: arrFavoriteProducts[indexPath.row])
    }
    
    private func goToProductDetailScreen(obj: ProductResponseFields) {
        let vc = ProductPopupStoryboard.instantiate(ProductPopupViewController.self)
        vc.objProductDetails = obj
        vc.arrMayLikeProducts = obj.youMayAlsoLikeProducts ?? []
        vc.productId = obj.biProductId ?? 0
        
        vc.delegate = self
        
        vc.showYouMayLikePopup = { [weak self] (obj) in
            DispatchQueue.main.async {
                self?.goToProductDetailScreen(obj: obj)
            }
        }

        vc.refreshList = { [weak self] () in
            DispatchQueue.main.async {
                self?.callFavAPI()
            }
        }

        vc.popupDelegate = self

        self.presentVC(vc)
    }
    
    private func callFavAPI() {
        offset = 1
        var dictParam : [String:Any] = [:]
        dictParam["offset"] = offset
        self.getFavoriteOrders(dictData: dictParam, flagLoader: true)
    }
    
    func collectionView(_ collectionView: UICollectionView, willDisplay cell: UICollectionViewCell, forItemAt indexPath: IndexPath) {
        if indexPath.row == (self.arrFavoriteProducts.count ) - 1  {
            if (self.arrFavoriteProducts.count ) < totalCount {
                offset = offset + 1
                
                var dictParam : [String:Any] = [:]
                dictParam["offset"] = offset

                cons_activityIndicatorView_height.constant = 40
                stackViewActivityIndicatorView.isHidden = false
                activityIndicatorView.isHidden = false
                activityIndicatorView.startAnimating()
                
                self.getFavoriteOrders(dictData: dictParam, flagLoader: false)
            }
        }
    }
    
}

extension FavoriteViewController : UpdateCountProtocol {
    
    func updateCartCount(cartCount:Int?) {
        // update my cart button count
        delegate?.updateCartCountFromFavorite(cartCount: cartCount)
    }
}

extension FavoriteViewController: BottomPopupDelegate {
    func bottomPopupDidDismiss() {
        DispatchQueue.main.async {
            self.callFavAPI()
        }
    }
    
}
