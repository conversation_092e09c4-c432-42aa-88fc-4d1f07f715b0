
import UIKit

protocol FavoritePresentationProtocol {
    func apiCallForGetFavoriteProducts(dictData:[String:Any], flagLoader: Bool)
    func apiResponseGetFavoriteProducts(response:FavouriteListResponse?,error:Error?)

    func apiCallForDeleteFavoriteProducts()
    func apiResponseDeleteFavoriteProducts(response:CommonFields?,error:Error?)

}

class FavoritePresenter: FavoritePresentationProtocol {
    
    // MARK: Objects & Variables
    weak var viewControllerFavorite: FavoriteProtocol?
    var interactorFavorite: FavoriteInteractorProtocol?
    
    func apiCallForGetFavoriteProducts(dictData:[String:Any], flagLoader: Bool) {
        interactorFavorite?.apiCallForGetFavoriteProducts(dictData: dictData, flagLoader: flagLoader)
    }

    func apiResponseGetFavoriteProducts(response: FavouriteListResponse?, error: Error?) {
        if let vc = self.viewControllerFavorite as? BaseViewController {
            DispatchQueue.main.async {
                vc.endRefresing()
            }
        }
        if let error = error  {
            viewControllerFavorite?.displayAlert(string: error.localizedDescription)
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            return
        }
        
        if code == APIINVALIDAUTHORIZATIONCODE401 {
            AppSingletonObj.displaySessionExpiredAlert(strMsg: response.responseMessage ?? "")
            return
        }

        if code == APICODE400 {
            viewControllerFavorite?.displayAlert(string: response.responseMessage ?? "")
            return
        }
        
        guard let model = response.responseData,code == APISUCCESSCODE200  else {
            return
        }
        self.viewControllerFavorite?.loadOrderList(model: model)
    }

    func apiCallForDeleteFavoriteProducts() {
        self.interactorFavorite?.apiCallForDeleteFavoriteProducts()
    }

    func apiResponseDeleteFavoriteProducts(response:CommonFields?,error:Error?) {
        if let error = error  {
            viewControllerFavorite?.displayAlert(string: error.localizedDescription)
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            return
        }
        
        if code == APICODE400 {
            viewControllerFavorite?.displayAlert(string: response.responseMessage ?? "")
            return
        }
        
        if code == APIINVALIDAUTHORIZATIONCODE401 {
            AppSingletonObj.displaySessionExpiredAlert(strMsg: response.responseMessage ?? "")
            return
        }

        if code == APISUCCESSCODE200 {
            self.viewControllerFavorite?.refreshFavList(string: response.responseMessage ?? "")
        }
    }

}
