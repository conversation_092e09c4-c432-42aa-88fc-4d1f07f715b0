
import UIKit

protocol FavoriteInteractorProtocol {
    func apiCallForGetFavoriteProducts(dictData:[String:Any], flagLoader: Bool)
    func apiCallForDeleteFavoriteProducts()

}

protocol FavoriteDataStore {
    //{ get set }
}

class FavoriteInteractor: FavoriteInteractorProtocol, FavoriteDataStore {

    // MARK: Objects & Variables
    var presenterFavorite: FavoritePresentationProtocol?
    
    func apiCallForGetFavoriteProducts(dictData:[String:Any], flagLoader: Bool) {
        if flagLoader == true {
            ActivityIndicator.shared.showCentralSpinner()
        }
        
        let authorization = getAuthorizationText()
        
        FavouriteAPI.favouriteListing(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, offset: Int(setDataInString(dictData["offset"] as AnyObject)) ?? 0) { data, error in
            ActivityIndicator.shared.hideCentralSpinner()
            self.presenterFavorite?.apiResponseGetFavoriteProducts(response: data, error: error)
        }
        
    }

    func apiCallForDeleteFavoriteProducts() {
        ActivityIndicator.shared.showCentralSpinner()
        
        let authorization = getAuthorizationText()

        FavouriteAPI.removeFavourite(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang) { data, error in
            ActivityIndicator.shared.hideCentralSpinner()
            self.presenterFavorite?.apiResponseDeleteFavoriteProducts(response: data, error: error)
        }
        
    }

}
