
import UIKit
import GoogleMaps

protocol TrackOrderProtocol: AnyObject {
   
}

class TrackOrderViewController: BaseViewController, TrackOrderProtocol {

    // MARK: IBOutlets
    @IBOutlet weak var gmsMapView: GMSMapView!
    @IBOutlet weak var lblETATime: UILabel!
    @IBOutlet weak var viewMapOuter: UIView!
    
    // MARK: Objects & Variables
    var presenterTrackOrder: TrackOrderPresentationProtocol?

    var mapPolyLine : GMSPolyline?
    var originMarker : GMSMarker = GMSMarker()
    var destinationMarker : GMSMarker = GMSMarker()
    
    var customerLat = 0.0
    var customerLong = 0.0
    var driverLat = 0.0
    var driverLong = 0.0

    var orderID = 0
    var strCarAdditionalCar = ""
    
    // MARK: Object lifecycle
    /*
    override init(nibName nibNameOrNil: String?, bundle nibBundleOrNil: Bundle?) {
        super.init(nibName: nibNameOrNil, bundle: nibBundleOrNil)
        setup()
    }
    */
    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
        setup()
    }
    
    // MARK: Setup
    
    private func setup() {
        let viewController = self
        let interactor = TrackOrderInteractor()
        let presenter = TrackOrderPresenter()
        
        //View Controller will communicate with only presenter
        viewController.presenterTrackOrder = presenter
        
        //Presenter will communicate with Interector and Viewcontroller
        presenter.viewControllerTrackOrder = viewController
        presenter.interactorTrackOrder = interactor
        
        //Interactor will communucate with only presenter.
        interactor.presenterTrackOrder = presenter
    }
    
    
    // MARK: View lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        gmsMapView.isMyLocationEnabled = false
        gmsMapView.settings.myLocationButton = false
        gmsMapView.translatesAutoresizingMaskIntoConstraints = true
    }
    
    func getDriverLocation() {
        SocketIOManager.shared.socket.on("receive_driver_location") { (data, ack) in
            let dict = data.first as? [String:Any]
            let orderId = Int(setDataInString(dict?["iOrderId"] as AnyObject)) ?? 0

            if self.orderID == orderId {
                self.driverLat = Double(setDataInString(dict?["dLatitude"] as AnyObject)) ?? 0.0
                self.driverLong = Double(setDataInString(dict?["dLongitude"] as AnyObject)) ?? 0.0

                self.originMarker.groundAnchor = CGPoint(x: 0.5, y: 0.5)
                self.originMarker.icon = UIImage(named: "car")
                
                let driverLocation = CLLocation(latitude: self.driverLat, longitude: self.driverLong)

                let angle = getBearingBetweenTwoPoints1(point1: CLLocation(latitude: self.originMarker.position.latitude, longitude: self.originMarker.position.longitude), point2: driverLocation)

                if self.originMarker.map != nil {
                    CATransaction.begin()
                    CATransaction.setAnimationDuration(2.0)
                    self.originMarker.position = driverLocation.coordinate
                    self.originMarker.rotation = angle
                    CATransaction.commit()
                }else{
                    self.originMarker.position = driverLocation.coordinate
                    self.originMarker.rotation = angle
                    self.originMarker.map = self.gmsMapView
                }
                self.gmsMapView.animate(toLocation: driverLocation.coordinate)
            }
        }
    }

    @IBAction func btnDimViewAction(_ sender: Any) {
        self.dismiss(animated: true, completion: nil)
    }

    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        SocketIOManager.shared.socket.off("receive_driver_location")
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        self.gmsMapView.frame = self.viewMapOuter.bounds
        self.getDriverLocation()
        self.manageDestination(sourceLocation: CLLocationCoordinate2D(latitude: self.driverLat, longitude: self.driverLong))
    }

    func manageDestination(sourceLocation : CLLocationCoordinate2D) {
        // source lat long - driver's location
        // destination lat long - user's location
        
        let fromCord = sourceLocation
        let toCord = CLLocationCoordinate2D(latitude: customerLat, longitude: customerLong)

        self.showDestinationPathOnMap(fromLocation: fromCord, toLocation: toCord, wayPoint: nil)
       
    }

}

extension TrackOrderViewController {
    
    func showDestinationPathOnMap(fromLocation: CLLocationCoordinate2D,toLocation:CLLocationCoordinate2D,wayPoint:CLLocationCoordinate2D?) {
        if !AppSingletonObj.isConnectedToNetwork(){
            return
        }
        
        GoogleAPIHelper.shared.getPolylineRoute(from: fromLocation, to: toLocation, waypointsString: wayPoint) { (success, points, duration, distance,model, strSeconds) in
            if success
            {
                guard let strPath = points else {
                    return
                }
                ez.runThisInMainThread {

                    self.lblETATime.text = "\(duration ?? "")"
                    self.showPath(polyStr: strPath,
                                  fromCord: fromLocation,
                                  toCord: toLocation,wayPoint: wayPoint,goolgeModel: model)
                }
                
            }
        }
        
    }

    func showPath(polyStr: String? = nil,
                  fromCord: CLLocationCoordinate2D,
                  toCord: CLLocationCoordinate2D,wayPoint:CLLocationCoordinate2D?,goolgeModel:GoogleDirectionDetailsRootClass?){
        
        
        let path = polyStr.convert(if: { (string) -> GMSMutablePath in
            return GMSMutablePath(fromEncodedPath: string)!
        }) { () -> GMSMutablePath in
            let path = GMSMutablePath()
            path.add(fromCord)
            path.add(toCord)
            return path
        }
        var isFirstTime = false
        if mapPolyLine == nil {
            isFirstTime = true
        }
        self.mapPolyLine?.map = nil
        self.mapPolyLine = nil
        self.mapPolyLine = GMSPolyline(path: path)
        self.mapPolyLine?.strokeWidth = 3
        self.mapPolyLine?.strokeColor = UIColor.AppTheme_BlueColor_012CDA
        self.mapPolyLine?.map = self.gmsMapView
        
        
        originMarker.groundAnchor = CGPoint(x: 0.5, y: 0.5)
        originMarker.icon = UIImage(named: "car")
        
        if originMarker.map != nil {
            CATransaction.begin()
            CATransaction.setAnimationDuration(1.0)
            let (location,_,angle) = self.getRotationMarkerAngle(goolgeModel: goolgeModel)
            originMarker.position = location?.coordinate ?? fromCord
            originMarker.rotation = angle
            CATransaction.commit()
        }else{
            let (location,_,angle) = self.getRotationMarkerAngle(goolgeModel: goolgeModel)
            originMarker.position = location?.coordinate ?? fromCord
            originMarker.rotation = angle
            originMarker.map = self.gmsMapView
        }
        
        destinationMarker.position = toCord
        destinationMarker.groundAnchor = CGPoint(x: 0.5, y: 0.5)
        destinationMarker.icon = UIImage(named: "home")
        destinationMarker.map = self.gmsMapView
        
        
        if isFirstTime {
            let bounds = GMSCoordinateBounds(path: path)
            self.gmsMapView.animate(with: GMSCameraUpdate.fit(bounds, with: UIEdgeInsets(top: 20, left: 20, bottom: 20, right: 20)))
        }else {
            self.gmsMapView.animate(toLocation: fromCord)
        }
        
    }

    func getRotationMarkerAngle(goolgeModel: GoogleDirectionDetailsRootClass?) -> (CLLocation?,CLLocation?,Double) {
        if let model = goolgeModel,let directionModel = model.routes.first?.legs.first?.steps.first,let endModel = model.routes.first?.legs.first?.steps.last {
            let startLat = Double(directionModel.startLocation?.lat ?? 0)
            let startlang = Double(directionModel.startLocation?.lng ?? 0)
            let endLat = Double(directionModel.endLocation?.lat ?? 0)
            let endLang = Double(directionModel.endLocation?.lng ?? 0)
            let startLocation = CLLocation(latitude: startLat, longitude: startlang)
            let endLocation = CLLocation(latitude: endLat, longitude: endLang)
            let angle = getBearingBetweenTwoPoints1(point1: startLocation, point2: endLocation)
            
            let endRouteLat = Double(endModel.endLocation?.lat ?? 0)
            let endRouteLang = Double(endModel.endLocation?.lng ?? 0)
            let endRouteLocation = CLLocation(latitude: endRouteLat, longitude: endRouteLang)
            print("startLocation  = ",startLocation)
            print("endLocation  = ",endLocation)
            
            return (startLocation,endRouteLocation,angle)
        }else{
            return (nil,nil,0.0)
        }
    }

}

extension Optional {
    /// Tries to unwrap `self` and if that succeeds continues to unwrap the parameter `optional`
    /// and returns the result of that.
    func and<B>(_ optional: B?) -> B? {
        guard self != nil else { return nil }
        return optional
    }
    
    /// Executes a closure with the unwrapped result of an optional.
    /// This allows chaining optionals together.
    func convert<T>(block: (Wrapped) -> T?) -> T? {
        guard let unwrapped = self else { return nil }
        return block(unwrapped)
    }
    
    func convert<T>(`if`: (Wrapped) -> T, `else`: ()-> T) -> T {
        guard let unwrapped = self else { return `else`() }
        return `if`(unwrapped)
    }
    
    /// Only perform `block` if the optional has a non-empty value
    func perform(block: (Wrapped) -> Void) {
        guard let unwrapped = self else { return }
        block(unwrapped)
    }
    
    /// Zips the content of this optional with the content of another
    /// optional `other` only if both optionals are not empty
    func zip2<A>(with other: Optional<A>) -> (Wrapped, A)? {
        guard let first = self, let second = other else { return nil }
        return (first, second)
    }
    
    /// Zips the content of this optional with the content of another
    /// optional `other` only if both optionals are not empty
    func zip3<A, B>(with other: Optional<A>, another: Optional<B>) -> (Wrapped, A, B)? {
        guard let first = self,
            let second = other,
            let third = another else { return nil }
        return (first, second, third)
    }
}

func degreesToRadians(degrees: Double) -> Double { return degrees * .pi / 180.0 }
func radiansToDegrees(radians: Double) -> Double { return radians * 180.0 / .pi }

func getBearingBetweenTwoPoints1(point1 : CLLocation, point2 : CLLocation) -> Double {

    let lat1 = degreesToRadians(degrees: point1.coordinate.latitude)
    let lon1 = degreesToRadians(degrees: point1.coordinate.longitude)

    let lat2 = degreesToRadians(degrees: point2.coordinate.latitude)
    let lon2 = degreesToRadians(degrees: point2.coordinate.longitude)

    let dLon = lon2 - lon1

    let y = sin(dLon) * cos(lat2)
    let x = cos(lat1) * sin(lat2) - sin(lat1) * cos(lat2) * cos(dLon)
    let radiansBearing = atan2(y, x)

    return radiansToDegrees(radians: radiansBearing)
}

