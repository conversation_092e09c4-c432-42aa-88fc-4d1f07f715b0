
import UIKit

protocol ScheduledOrderDetailPresentationProtocol {
    func apiCallForGetOrderDetail(dictData:[String:Any])
    func apiResponseGetOrderDetail(response:OrderDetailResponse?,error:Error?)

    func apiCallForUpdateTransaction(dictData:[String:Any], paymentStatus: Int, strCardNumber: String, strCardType: String, strCardScheme: String, strCardToken: String, walletUsedOrNot: Int)
    func apiResponseUpdateTransaction(response:TransactionResponse?,error:Error?, paymentStatus: Int, strCardNumber: String, strCardType: String, strCardScheme: String, strCardToken: String, strTransactionId: String)

    func apiCallForAddCard(dictData:[String:Any])
    func apiResponseAddCard(response:CommonFields?,error:Error?)

}

class ScheduledOrderDetailPresenter: ScheduledOrderDetailPresentationProtocol {
    
    // MARK: Objects & Variables
    weak var viewControllerScheduledOrderDetail: ScheduledOrderDetailProtocol?
    var interactorScheduledOrderDetail: ScheduledOrderDetailInteractorProtocol?
 
    func apiCallForGetOrderDetail(dictData:[String:Any]) {
        interactorScheduledOrderDetail?.apiCallForGetOrderDetail(dictData: dictData)
    }

    func apiResponseGetOrderDetail(response: OrderDetailResponse?, error: Error?) {
        if let error = error  {
            viewControllerScheduledOrderDetail?.displayAlert(string: error.localizedDescription)
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            return
        }
        
        if code == APIINVALIDAUTHORIZATIONCODE401 {
            AppSingletonObj.goToLoginScreen(message: response.responseMessage ?? "")
            return
        }

        if code == APICODE400 {
            viewControllerScheduledOrderDetail?.displayAlertAndDismissScreen(string: response.responseMessage ?? "")
            return
        }
        
        guard let model = response.responseData,code == APISUCCESSCODE200  else {
            return
        }
        self.viewControllerScheduledOrderDetail?.getOrderDetails(model: model)
    }

    func apiCallForUpdateTransaction(dictData:[String:Any], paymentStatus: Int, strCardNumber: String, strCardType: String, strCardScheme: String, strCardToken: String, walletUsedOrNot: Int) {
        self.interactorScheduledOrderDetail?.apiCallForUpdateTransaction(dictData: dictData, paymentStatus: paymentStatus, strCardNumber: strCardNumber, strCardType: strCardType, strCardScheme: strCardScheme, strCardToken: strCardToken, walletUsedOrNot: walletUsedOrNot)
    }

    func apiResponseUpdateTransaction(response:TransactionResponse?,error:Error?, paymentStatus: Int, strCardNumber: String, strCardType: String, strCardScheme: String, strCardToken: String, strTransactionId: String) {
        if let error = error  {
            viewControllerScheduledOrderDetail?.displayAlertForFailedCaseAndGoToCurrentOrders(strMsg: error.localizedDescription)

            return
        }
        
        guard let response = response,let code = response.responseCode else {
            return
        }
        
        if code == APIINVALIDAUTHORIZATIONCODE401 {
            AppSingletonObj.goToLoginScreen(message: response.responseMessage ?? "")
            return
        }

        if code == APICODE400 {
            viewControllerScheduledOrderDetail?.displayAlert(string: response.responseMessage ?? "")
            return
        }
        
        if code == APICODE203 {
            if paymentStatus == 1 {  // success
                self.viewControllerScheduledOrderDetail?.displayErrorAlert(strMsg: response.responseMessage ?? "")
            }
            else {
                self.viewControllerScheduledOrderDetail?.displayErrorAlertAndGoToHome(strMsg: response.responseMessage ?? "")
            }
            return
        }

        guard let model = response.responseData,code == APISUCCESSCODE200  else {
            return
        }
        
        if paymentStatus == 1 || paymentStatus == 0 {  // success
            self.viewControllerScheduledOrderDetail?.showThankyouAlert(strCardNumber: strCardNumber, strCardType: strCardType, strCardScheme: strCardScheme, strCardToken: strCardToken, strTransactionId: strTransactionId, strLatestWalletBalance: model.latestWalletBalance ?? "")
        }

    }

    func apiCallForAddCard(dictData:[String:Any]) {
        self.interactorScheduledOrderDetail?.apiCallForAddCard(dictData: dictData)
    }

    func apiResponseAddCard(response:CommonFields?,error:Error?) {
        if let error = error  {
            viewControllerScheduledOrderDetail?.displayAlert(string: error.localizedDescription)
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            return
        }
        
        if code == APIINVALIDAUTHORIZATIONCODE401 {
            AppSingletonObj.goToLoginScreen(message: response.responseMessage ?? "")
            return
        }

        if code == APICODE400 {
            viewControllerScheduledOrderDetail?.displayAlert(string: response.responseMessage ?? "")
            return
        }
        
    }

}
