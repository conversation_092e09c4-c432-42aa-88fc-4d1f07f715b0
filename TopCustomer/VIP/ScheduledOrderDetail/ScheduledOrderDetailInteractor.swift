
import UIKit

protocol ScheduledOrderDetailInteractorProtocol {
    func apiCallForGetOrderDetail(dictData:[String:Any])
    func apiCallForUpdateTransaction(dictData:[String:Any], paymentStatus: Int, strCardNumber: String, strCardType: String, strCardScheme: String, strCardToken: String, walletUsedOrNot: Int)
    func apiCallForAddCard(dictData:[String:Any])

}

protocol ScheduledOrderDetailDataStore {
    //{ get set }
}

class ScheduledOrderDetailInteractor: ScheduledOrderDetailInteractorProtocol, ScheduledOrderDetailDataStore {

    // MARK: Objects & Variables
    var presenterScheduledOrderDetail: ScheduledOrderDetailPresentationProtocol?
    
    func apiCallForGetOrderDetail(dictData:[String:Any]) {
        
        ActivityIndicator.shared.showCentralSpinner()
        
        let authorization = getAuthorizationText()
        
        OrderAPI.getOrder(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, iOrderId: Int(setDataInString(dictData["iOrderId"] as AnyObject)) ?? 0) { data, error in
                ActivityIndicator.shared.hideCentralSpinner()
                self.presenterScheduledOrderDetail?.apiResponseGetOrderDetail(response: data, error: error)

        }
        
    }

    func apiCallForUpdateTransaction(dictData:[String:Any], paymentStatus: Int, strCardNumber: String, strCardType: String, strCardScheme: String, strCardToken: String, walletUsedOrNot: Int) {

        ActivityIndicator.shared.showCentralSpinner()
        
        let authorization = getAuthorizationText()
        
        OrderAPI.orderTransactionUpdate(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, iOrderId: Int(setDataInString(dictData["iOrderId"] as AnyObject)) ?? 0, tiTransactionType: Int(setDataInString(dictData["tiTransactionType"] as AnyObject)) ?? 0, vTransactionRef: setDataInString(dictData["vTransactionRef"] as AnyObject), iPaymentStatus: Int(setDataInString(dictData["iPaymentStatus"] as AnyObject)) ?? 0, iUseWallet: walletUsedOrNot, vCardName: setDataInString(dictData["vCardName"] as AnyObject)) { data, error in
            ActivityIndicator.shared.hideCentralSpinner()
            self.presenterScheduledOrderDetail?.apiResponseUpdateTransaction(response: data, error: error, paymentStatus: paymentStatus, strCardNumber: strCardNumber, strCardType: strCardType, strCardScheme: strCardScheme, strCardToken: strCardToken, strTransactionId: setDataInString(dictData["vTransactionRef"] as AnyObject))
        }
        
    }

    func apiCallForAddCard(dictData:[String:Any]) {
        let authorization = getAuthorizationText()

        UserCardsAPI.addUserCards(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, vToken: setDataInString(dictData["vToken"] as AnyObject), vTransactionId: setDataInString(dictData["vTransactionId"] as AnyObject), vPaymentDescription: setDataInString(dictData["vPaymentDescription"] as AnyObject), vCardType: setDataInString(dictData["vCardType"] as AnyObject), vCardScheme: setDataInString(dictData["vCardScheme"] as AnyObject)) { data, error in
            
            self.presenterScheduledOrderDetail?.apiResponseAddCard(response: data, error: error)

        }
    }

}
