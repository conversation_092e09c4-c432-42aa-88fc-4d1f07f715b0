
import UIKit
import PaymentSDK
import Mixpanel

protocol ScheduledOrderDetailProtocol: AnyObject {
    func displayAlert(string:String)
    func getOrderDetails(model : OrderDetailResponseFields)
    func displayErrorAlert(strMsg:String)
    func displayErrorAlertAndGoToHome(strMsg:String)
    func showThankyouAlert(strCardNumber: String, strCardType: String, strCardScheme: String, strCardToken: String, strTransactionId: String, strLatestWalletBalance: String)
    func displayAlertForFailedCaseAndGoToCurrentOrders(strMsg: String)
    func displayAlertAndDismissScreen(string: String)

}

class ScheduledOrderDetailViewController: BaseViewController {

    // MARK: Objects & Variables
    var presenterScheduledOrderDetail: ScheduledOrderDetailPresentationProtocol?

    // MARK: IBOutlets
    @IBOutlet weak var tblOrders: UITableView!
    @IBOutlet weak var tblOrdersHeightConstraints: NSLayoutConstraint!
    @IBOutlet weak var btnCancelOrder: UIButton!
    @IBOutlet weak var lblTitle: UILabel!
    @IBOutlet weak var lblOrderNo: UILabel!
    @IBOutlet weak var lblOrderDate: UILabel!
    @IBOutlet weak var lblDetails: UILabel!
    @IBOutlet weak var lblDeliveryTitle: UILabel!
    @IBOutlet weak var lblTotalTitle: UILabel!
    @IBOutlet weak var lblOrderStatus: UILabel!
    @IBOutlet weak var lblPaymentMethodTitle: UILabel!
    @IBOutlet weak var btnChangePaymentMethod: UIButton!
    @IBOutlet weak var btnPayNow: CustomRoundedButtton!
    @IBOutlet weak var lblOrderNoValue: UILabel!
    @IBOutlet weak var lblOrderDateValue: UILabel!
    @IBOutlet weak var lblDeliveryValue: UILabel!
    @IBOutlet weak var lblTotalValue: UILabel!
    @IBOutlet weak var lblPaymentMethod: UILabel!
    @IBOutlet weak var lblOrderStatusValue: UILabel!
    @IBOutlet weak var lblVatValue: UILabel!
    @IBOutlet weak var lblSubTotalValue: UILabel!
    @IBOutlet weak var lblSubtotalTitle: UILabel!
    @IBOutlet weak var lblVatTitle: UILabel!
    @IBOutlet weak var lblDeliveryAddressTitle: UILabel!
    @IBOutlet weak var lblType: UILabel!
    @IBOutlet weak var lblAddress: UILabel!
    @IBOutlet weak var lblDeliveryShiftTitle: UILabel!
    @IBOutlet weak var lblShift: UILabel!
    @IBOutlet weak var lblOrderTypeTitle: MaterialLocalizeLable!
    @IBOutlet weak var lblOrderTypeValue: MaterialLocalizeLable!
    @IBOutlet weak var lblNotesTitle: UILabel!
    @IBOutlet weak var viewNote: UIView!
    @IBOutlet weak var lblNote: UILabel!
    @IBOutlet weak var cons_bottom_viewNotes: NSLayoutConstraint!

    
    var showCancelScheduledOrderPopup: (()->())?
    var orderId = 0
    var objOrderDetails: OrderDetailResponseFields?
    var isAddNewCard = false
    var splitPaymentOrNot = 0
    var orderAmount = 0.0
    var showUpdateTransactionAlert: ((String)->())?

    // MARK: Object lifecycle
    /*
    override init(nibName nibNameOrNil: String?, bundle nibBundleOrNil: Bundle?) {
        super.init(nibName: nibNameOrNil, bundle: nibBundleOrNil)
        setup()
    }
    */
    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
        setup()
    }
    
    // MARK: Setup
    
    private func setup() {
        let viewController = self
        let interactor = ScheduledOrderDetailInteractor()
        let presenter = ScheduledOrderDetailPresenter()
        
        //View Controller will communicate with only presenter
        viewController.presenterScheduledOrderDetail = presenter
        
        //Presenter will communicate with Interector and Viewcontroller
        presenter.viewControllerScheduledOrderDetail = viewController
        presenter.interactorScheduledOrderDetail = interactor
        
        //Interactor will communucate with only presenter.
        interactor.presenterScheduledOrderDetail = presenter
    }
    
    
    // MARK: View lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        self.setTexts()
        initialSetUp()
        self.getOrderDetails()

    }
    
    private func getOrderDetails() {
        var dictParam : [String:Any] = [:]
        dictParam["iOrderId"] = orderId

        if AppSingletonObj.isConnectedToNetwork(){
            self.presenterScheduledOrderDetail?.apiCallForGetOrderDetail(dictData: dictParam)
        }
    }

    private func setTexts() {
        lblTitle.text = ObjKeymessages.kLABEL_MY_ORDER
        lblDetails.text = ObjKeymessages.kLABEL_DETAILS
        lblDeliveryTitle.text = ObjKeymessages.kLABEL_DELIVERY
        lblTotalTitle.text = ObjKeymessages.kLABEL_TOTAL
        lblOrderStatus.text = ObjKeymessages.kLABEL_ORDER_STATUS
        lblPaymentMethodTitle.text = ObjKeymessages.kLABEL_PAYMENT_METHOD
        btnChangePaymentMethod.setTitle(ObjKeymessages.kLABEL_CHANGE, for: .normal)
        btnPayNow.setTitle(ObjKeymessages.kLABEL_PAY_NOW, for: .normal)
        btnCancelOrder.setTitle(ObjKeymessages.kLABEL_CANCEL_SCHEDULE, for: .normal)
        lblOrderNo.text = "\(ObjKeymessages.kLABEL_ORDER_NO):"
        lblOrderDate.text = "\(ObjKeymessages.kLABEL_DATE):"

        lblSubtotalTitle.text = "\(ObjKeymessages.kLABEL_SUBTOTAL) (\(ObjKeymessages.kLABEL_WITHOUT_VAT))"
        lblVatTitle.text = "\(ObjKeymessages.kLABEL_VAT)"

        lblDeliveryAddressTitle.text = ObjKeymessages.kLABEL_DELIVERY_ADDRESS
        lblDeliveryShiftTitle.text = ObjKeymessages.kLABEL_DELIVERY_SHIFT
        lblOrderTypeTitle.text = ObjKeymessages.kLABEL_ORDER_TYPE
        lblNotesTitle.text = ObjKeymessages.kLABEL_NOTES

    }
    
    private func initialSetUp() {
        self.tblOrders.registerCell(cell: HistoryOrderDetailsTableViewCell.self)
        self.tblOrders.delegate = self
        self.tblOrders.dataSource = self
    }

    @IBAction func btnDimViewAction(_ sender: Any) {
        self.dismiss(animated: true, completion: nil)
    }

    @IBAction func actionChangePaymentMethod(_ sender: Any) {
        let vc = PaymentsStoryboard.instantiate(ChoosePaymentViewController.self)
        vc.modalPresentationStyle = .overFullScreen
        vc.modalTransitionStyle = .crossDissolve
        self.presentVC(vc)
        
    }

    @IBAction func actionPayNow(_ sender: Any) {
        let vc = PaymentsStoryboard.instantiate(ChoosePaymentViewController.self)
        vc.modalPresentationStyle = .overFullScreen
        vc.modalTransitionStyle = .crossDissolve
        
        vc.grandTotal = objOrderDetails?.dTotalAmount ?? ""

        vc.passSelectedCardData = { [weak self] (cardToken, cardTransactionReference, flag) in
            DispatchQueue.main.async {
                
                self?.splitPaymentOrNot = 0

                self?.isAddNewCard = flag
                self?.payWithCard(flagCardOrNot: flag, strCardToken: cardToken, strCardTransactionReference:
                                    cardTransactionReference, flagSplitWallet: false, strRemainingAmt: "")
            }
        }
        
        vc.applePaySelected = { [weak self] () in
            DispatchQueue.main.async {
                
                self?.splitPaymentOrNot = 0

                self?.payWithApplePay(flagSplitWallet: false, strRemainingAmt: "")
            }
        }

        vc.codSelected = { [weak self] () in
            DispatchQueue.main.async {
                self?.updateTransaction(strTransactionReference: TransactionTypeCOD, paymentStatus: 0, strCardNumber: "", strCardType: "", strCardScheme: "", strCardToken: "", transactionType: TransactionType.COD.rawValue, walletUsedOrNot: UsedWalletOrNot.NotUsed.rawValue)
            }
        }

        vc.walletSelected = { [weak self] () in
            DispatchQueue.main.async {
                self?.updateTransaction(strTransactionReference: TransactionTypeWallet, paymentStatus: 1, strCardNumber: "", strCardType: "", strCardScheme: "", strCardToken: "", transactionType: TransactionType.Wallet.rawValue, walletUsedOrNot: UsedWalletOrNot.NotUsed.rawValue)
            }
        }

        vc.splitWalletSelected = { [weak self] (cardToken, cardTransactionReference, flag, strPaymentType, strRemainingAmountAfterWallet) in
            DispatchQueue.main.async {
                
                self?.splitPaymentOrNot = 1
                
                if strPaymentType == "Card" {
                    self?.isAddNewCard = flag
                    self?.payWithCard(flagCardOrNot: flag, strCardToken: cardToken, strCardTransactionReference:
                                        cardTransactionReference, flagSplitWallet: true, strRemainingAmt: strRemainingAmountAfterWallet)
                }
                else if strPaymentType == "COD" {
                    self?.updateTransaction(strTransactionReference: TransactionTypeCOD, paymentStatus: 0, strCardNumber: "", strCardType: "", strCardScheme: "", strCardToken: "", transactionType: TransactionType.COD.rawValue, walletUsedOrNot: UsedWalletOrNot.Used.rawValue)
                }
                else if strPaymentType == "ApplePay" {
                    self?.payWithApplePay(flagSplitWallet: true, strRemainingAmt: strRemainingAmountAfterWallet)
                }

            }
        }

        self.presentVC(vc)

    }
    
    private func closeScreen() {
        let vc = homeStoryboard.instantiate(MainTabbarViewController.self)
        let navigationVc = UINavigationController(rootViewController: vc)
        AppDel?.window?.rootViewController = navigationVc
    }
    
    @IBAction func cancelScheduledOrderAction(_ sender: Any) {
        self.showCancelScheduledOrderPopup?()
        self.dismiss(animated: true, completion: nil)

    }

}

extension ScheduledOrderDetailViewController: UITableViewDelegate, UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return objOrderDetails?.productDetails?.count ?? 0
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeue(with: HistoryOrderDetailsTableViewCell.self, for: indexPath)
        
        cell.lblQuantity.text = "X\(objOrderDetails?.productDetails?[indexPath.row].iProductQuantity ?? 0)"
        cell.lblName.text = "\(objOrderDetails?.productDetails?[indexPath.row].vProductName ?? "") \(objOrderDetails?.productDetails?[indexPath.row].vProductUnit ?? "")"
        cell.lblPrice.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(objOrderDetails?.productDetails?[indexPath.row].dbPrice ?? "")")

        return cell
    }
    
    func tableView(_ tableView: UITableView, willDisplay cell: UITableViewCell, forRowAt indexPath: IndexPath) {
        self.tblOrders.layoutIfNeeded()
        self.tblOrdersHeightConstraints.constant = self.tblOrders.contentSize.height
    }
}

extension ScheduledOrderDetailViewController: ScheduledOrderDetailProtocol {
    func displayAlert(string: String) {
        AppSingleton.shared.showCustomPopUpWithOkButton(strButtonTitle: ObjKeymessages.kLABEL_OK, strMessage: string, showOnTopVC: false) { (isOk) in
        }
    }

    func displayErrorAlert(strMsg:String) {
        AppSingleton.shared.showCustomPopUpWithOkButton(strButtonTitle: ObjKeymessages.kLABEL_OK, strMessage: strMsg, showOnTopVC: false) { (isOk) in
            if isOk == true {
                self.popVC()
            }
        }
    }

    func displayErrorAlertAndGoToHome(strMsg:String) {
        AppSingleton.shared.showCustomPopUpWithOkButton(strButtonTitle: ObjKeymessages.kLABEL_OK, strMessage: strMsg, showOnTopVC: false) { (isOk) in
        }
    }

    func displayAlertForFailedCaseAndGoToCurrentOrders(strMsg: String) {
        self.showUpdateTransactionAlert?(strMsg)
        self.dismiss(animated: true, completion: nil)
    }

    func displayAlertAndDismissScreen(string: String) {
        AppSingleton.shared.showCustomPopUpWithOkButton(strButtonTitle: ObjKeymessages.kLABEL_OK, strMessage: string, showOnTopVC: false) { (isOk) in
            if isOk == true {
                self.dismiss(animated: true, completion: nil)
            }
        }
    }

    func showThankyouAlert(strCardNumber: String, strCardType: String, strCardScheme: String, strCardToken: String, strTransactionId: String, strLatestWalletBalance: String) {
        
        // Mixpanel
        Mixpanel.mainInstance().track(event: ScreenNames.CreateOrder.rawValue, properties: [
            "user_id": "\(User.shared.iUserId ?? 0)",
            "order_number": objOrderDetails?.vOrderNumber,
            "promocode": "",
            "order_total": orderAmount
        ])

        // product vise mixpanel event
        for objProduct in objOrderDetails?.productDetails ?? [] {
            Mixpanel.mainInstance().track(event: ScreenNames.ProductPurchase.rawValue, properties: [
                "user_id": "\(User.shared.iUserId ?? 0)",
                "order_number": objOrderDetails?.vOrderNumber,
                "product_name": objProduct.vProductName,
                "product_quantity": objProduct.iProductQuantity,
                "product_price": objProduct.dbPrice,
                "order_total": orderAmount
            ])
        }

        // save wallet balance
        let WalletBalance = Double(strLatestWalletBalance) ?? 0
        let strWalletBalance = "\(forTrailingZero(temp: WalletBalance))"
        UserDefaults.standard.set(strWalletBalance, forKey: USERDEFAULTS_INFO_KEY.WALLET_BALANCE)
        // save wallet balance with english numbers
        let strWalletBalanceEnNumbers = "\(forTrailingZeroEnglishNumbersOnly(temp: WalletBalance))"
        UserDefaults.standard.set(strWalletBalanceEnNumbers, forKey: USERDEFAULTS_INFO_KEY.WALLET_BALANCE_EN_NUMBER)
        
        UserDefaults.standard.synchronize()

        // call add card api in background
        if strCardToken != "" {
            self.addUserCard(strCardNumber: strCardNumber, strCardType: strCardType, strCardScheme: strCardScheme, strCardToken: strCardToken, strTransactionId: strTransactionId)
        }

        let vc = ProductPopupStoryboard.instantiate(ThankYouViewController.self)
        vc.modalPresentationStyle = .overFullScreen
        vc.modalTransitionStyle = .crossDissolve
        
        vc.isAddNewCard = self.isAddNewCard

        vc.completionPaymentCardBlock = { [weak self] (strAction, isAddNewCard) in
            guard let self = self else { return }
            self.dismiss(animated: false, completion: nil)
            if strAction == "Complete" {
                if isAddNewCard {
                    self.dismiss(animated: false)
                }
            }
        }

        vc.completionBlock = { [weak self] (strAction) in
            guard let self = self else { return }
            self.dismiss(animated: false, completion: nil)
            if strAction == "Complete" {
                self.dismiss(animated: true, completion: nil)
            }
        }
        delay(0.3) {
            UIApplication.topViewController()?.presentVC(vc)
        }
    }

    private func addUserCard(strCardNumber: String, strCardType: String, strCardScheme: String, strCardToken: String, strTransactionId: String) {
        var dictParam : [String:Any] = [:]
        dictParam["vToken"] = strCardToken
        dictParam["vTransactionId"] = strTransactionId
        dictParam["vPaymentDescription"] = strCardNumber
        dictParam["vCardType"] = strCardType
        dictParam["vCardScheme"] = strCardScheme

        if AppSingletonObj.isConnectedToNetwork(){
            self.presenterScheduledOrderDetail?.apiCallForAddCard(dictData: dictParam)
        }
    }

    func getOrderDetails(model : OrderDetailResponseFields) {
        objOrderDetails = model
        self.setData()
    }
    
    private func setData() {
        lblOrderNoValue.text = objOrderDetails?.vOrderNumber
        // convert date to desired format
        let strDate = objOrderDetails?.tsOrderedAt
        let strFormattedDate = strDate?.utcToLocal(dateStr: strDate ?? "")
        lblOrderDateValue.text = strFormattedDate

        tblOrders.reloadData()

        lblDeliveryValue.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(objOrderDetails?.dDeliveryCharge ?? "")")

        lblTotalValue.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(objOrderDetails?.dTotalAmount ?? "")")
        
        // convert date to desired format
        let strDeliveryDate = objOrderDetails?.tsOrderDeliveredAt
        let strFormattedDeliveryDate = strDeliveryDate?.utcToLocal(dateStr: strDeliveryDate ?? "")
        lblOrderStatusValue.text = "\(ObjKeymessages.kLABEL_WILL_BE_DELIVERED) \(strFormattedDeliveryDate ?? "")"

        lblVatValue.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(objOrderDetails?.dVatCharge ?? "")")
        lblSubTotalValue.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(objOrderDetails?.dOrderSubTotal ?? "")")

        if objOrderDetails?.tiIsPaybtnEnabled == 0 {  // show Pay Now button
            btnPayNow.isHidden = false
        }
        else {
            btnPayNow.isHidden = true
        }
        
        lblType.text = objOrderDetails?.vType
        lblAddress.text = objOrderDetails?.txAddress
        
        if objOrderDetails?.vShiftDisplayName == nil || objOrderDetails?.vShiftDisplayName == "" {  // shift name is empty. This is old user.
            if objOrderDetails?.tiShiftType == ShiftType.Morning.rawValue { // Morning
                lblShift.text = ObjKeymessages.kLABEL_MORNING
            }
            else { // Evening
                lblShift.text = ObjKeymessages.kLABEL_EVENING
            }
        }
        else {
            lblShift.text = objOrderDetails?.vShiftDisplayName
        }

        // Order Type
        if objOrderDetails?.iReccuringType == ReccuringType.OnlyOnce.rawValue {
            lblOrderTypeValue.text = ObjKeymessages.kLABEL_ONLY_ONCE
        }
        else if objOrderDetails?.iReccuringType == ReccuringType.EveryWeek.rawValue {
            lblOrderTypeValue.text = ObjKeymessages.kLABEL_EVERY_WEEK
        }
        else if objOrderDetails?.iReccuringType == ReccuringType.Every2Weeks.rawValue {
            lblOrderTypeValue.text = ObjKeymessages.kLABEL_EVERY_TWO_WEEKS
        }
        else if objOrderDetails?.iReccuringType == ReccuringType.EveryMonth.rawValue {
            lblOrderTypeValue.text = ObjKeymessages.kLABEL_EVERY_MONTH
        }

        if objOrderDetails?.tAdditionalNote == "" {  // hide notes view
            lblNotesTitle.isHidden = true
            viewNote.isHidden = true
            cons_bottom_viewNotes.constant = 0
        }
        else { // show notes view
            lblNotesTitle.isHidden = false
            viewNote.isHidden = false
            cons_bottom_viewNotes.constant = 30
        }
        lblNote.text = objOrderDetails?.tAdditionalNote

    }

}

extension ScheduledOrderDetailViewController {
    
    func payWithApplePay(flagSplitWallet: Bool, strRemainingAmt: String) {
        
        var strAddress = ""
        var strCity = ""
        var strZipcode = ""
        var strState = ""
        var strCountryCode = ""

        if let address = UserDefaults.standard.object(forKey: USERDEFAULTS_INFO_KEY.CURRENT_ADDRESS) as? [String:Any],let data = address.data {
            let decodeResult: (decodableObj: AddressResponseFields?, error: Error?) = CodableHelper.decode(AddressResponseFields.self, from: data)
            if let decodeObj = decodeResult.decodableObj {
                strAddress = decodeObj.txAddress ?? ""
                strCity = decodeObj.vCity ?? ""
                strZipcode = decodeObj.vZipCode ?? ""
                strState = decodeObj.vState ?? ""
                strCountryCode = decodeObj.vCountryCode ?? ""

            }
        }

        var strEmail = ""
        if User.shared.vEmailId == "" {  // use default email id
            strEmail = DefaultEmailForApplePay
        }
        else {
            strEmail = User.shared.vEmailId ?? ""
        }

        var billingDetails: PaymentSDKBillingDetails! {
            return PaymentSDKBillingDetails(name: User.shared.vName,
                                         email: strEmail,
                                            phone: "\(User.shared.vISDCode ?? "")\(User.shared.vMobileNumber ?? "")",
                                         addressLine: strAddress,
                                         city: strCity,
                                         state: strState,
                                         countryCode: strCountryCode,
                                         zip: strZipcode)
        }

        var shippingDetails: PaymentSDKShippingDetails! {
            return PaymentSDKShippingDetails(name: User.shared.vName,
                                             email: strEmail,
                                             phone: "\(User.shared.vISDCode ?? "")\(User.shared.vMobileNumber ?? "")",
                                             addressLine: strAddress,
                                             city: strCity,
                                             state: strState,
                                             countryCode: strCountryCode,
                                             zip: strZipcode)
        }
        
        // check for wallet utilisation
        orderAmount = Double(objOrderDetails?.dTotalAmount ?? "") ?? 0.0
        if flagSplitWallet == true {  // split payment
            orderAmount = Double(strRemainingAmt) ?? 0.0
        }

        var applePayConfiguration: PaymentSDKConfiguration! {
            return PaymentSDKConfiguration(profileID: profileID,
                                           serverKey: serverKey,
                                           clientKey: clientKey,
                                           currency: SACurrencyCode,
                                           amount: orderAmount,
                                           merchantCountryCode: MerchantCountryCode)
                .cartDescription("cart description ApplePay")
                .cartID(objOrderDetails?.vOrderNumber ?? "")
                .screenTitle(AppName)
                .languageCode(UserDefaults.standard.getLanguage() ?? "")
                .merchantName("Material")
                .merchantAppleBundleID(PAYTABS_MERCHANT_IDENTIFIER)
                .simplifyApplePayValidation(true)
                .billingDetails(billingDetails)
                .shippingDetails(shippingDetails)

        }

        PaymentManager.startApplePayPayment(on: self,
                                     configuration: applePayConfiguration,
                                     delegate: self)

    }

    func payWithCard(flagCardOrNot: Bool, strCardToken: String, strCardTransactionReference: String, flagSplitWallet: Bool, strRemainingAmt: String) {
        var strAddress = ""
        var strCity = ""
        var strZipcode = ""
        var strState = ""
        var strCountryCode = ""

        if let address = UserDefaults.standard.object(forKey: USERDEFAULTS_INFO_KEY.CURRENT_ADDRESS) as? [String:Any],let data = address.data {
            let decodeResult: (decodableObj: AddressResponseFields?, error: Error?) = CodableHelper.decode(AddressResponseFields.self, from: data)
            if let decodeObj = decodeResult.decodableObj {
                strAddress = decodeObj.txAddress ?? ""
                strCity = decodeObj.vCity ?? ""
                strZipcode = decodeObj.vZipCode ?? ""
                strState = decodeObj.vState ?? ""
                strCountryCode = decodeObj.vCountryCode ?? ""

            }
        }

        var billingDetails: PaymentSDKBillingDetails! {
            return PaymentSDKBillingDetails(name: User.shared.vName,
                                         email: User.shared.vEmailId,
                                            phone: "\(User.shared.vISDCode ?? "")\(User.shared.vMobileNumber ?? "")",
                                         addressLine: strAddress,
                                         city: strCity,
                                         state: strState,
                                         countryCode: strCountryCode,
                                         zip: strZipcode)
        }

        var configuration: PaymentSDKConfiguration! {
            let theme = PaymentSDKTheme.default
            theme.logoImage = UIImage(named: "logo_login_white")
            theme.secondaryColor = UIColor.AppTheme_BlueColor_012CDA
            theme.secondaryFontColor = UIColor.AppTheme_BlueColor_012CDA
            theme.primaryFontColor = .black
            theme.strokeColor = UIColor.AppTheme_BlueColor_012CDA
            theme.buttonColor = UIColor.AppTheme_BlueColor_012CDA
            theme.titleFontColor = .black
            theme.buttonFontColor = .white

            var strToken = ""
            var strTransactionReference = ""
            if flagCardOrNot == false {
                strToken = ""
                strTransactionReference = ""
            }
            else {
                strToken = strCardToken
                strTransactionReference = strCardTransactionReference
            }

            // check for wallet utilisation
            orderAmount = Double(objOrderDetails?.dTotalAmount ?? "") ?? 0.0
            if flagSplitWallet == true {  // split payment
                orderAmount = Double(strRemainingAmt) ?? 0.0
            }
            
            return PaymentSDKConfiguration(profileID: profileID,
                                           serverKey: serverKey,
                                           clientKey: clientKey,
                                           currency: SACurrencyCode,
                                           amount: orderAmount,
                                           merchantCountryCode: MerchantCountryCode)
                .cartDescription("cart description")
                .cartID(objOrderDetails?.vOrderNumber ?? "")
                .screenTitle(AppName)
                .theme(theme)
                .showBillingInfo(true)
                .hideCardScanner(true)
                .languageCode(UserDefaults.standard.getLanguage() ?? "")
                .tokeniseType(.userOptinoal)
                .tokenFormat(.hex32)
                .token(strToken)
                .transactionReference(strTransactionReference)
                .billingDetails(billingDetails)
        }

        PaymentManager.startCardPayment(on: self, configuration: configuration,
                                 delegate: self)

    }

}

extension ScheduledOrderDetailViewController: PaymentManagerDelegate {
    
    func paymentManager(didFinishTransaction transactionDetails: PaymentSDKTransactionDetails?, error: Error?) {
        if let transactionDetails = transactionDetails {
            print("Response Code: " + (transactionDetails.paymentResult?.responseCode ?? ""))
            print("Result: " + (transactionDetails.paymentResult?.responseMessage ?? ""))
            print("Token: " + (transactionDetails.token ?? ""))
            print("Transaction Reference: " + (transactionDetails.transactionReference ?? ""))
            print("Transaction Time: " + (transactionDetails.paymentResult?.transactionTime ?? "" ))
            
            var trans_type = 0
            var strCardScheme = ""

            if transactionDetails.cartDescription == "cart description ApplePay" {  // apple pay
                trans_type = 2
                strCardScheme = transactionDetails.paymentInfo?.paymentDescription ?? ""
            }
            else {  // card
                trans_type = 1
                strCardScheme = transactionDetails.paymentInfo?.cardScheme ?? ""
            }

            if transactionDetails.isSuccess() {
                print("Successful transaction")
                
                // call update transaction api for successful transaction
                self.updateTransaction(strTransactionReference: (transactionDetails.transactionReference ?? ""), paymentStatus: 1, strCardNumber: (transactionDetails.paymentInfo?.paymentDescription ?? ""), strCardType: (transactionDetails.paymentInfo?.cardType ?? ""), strCardScheme: strCardScheme, strCardToken: (transactionDetails.token ?? ""), transactionType: trans_type, walletUsedOrNot: splitPaymentOrNot)

            }
            else {
                print("Transaction failed")
                
                // call update transaction api for failed transaction
                self.updateTransaction(strTransactionReference: (transactionDetails.transactionReference ?? ""), paymentStatus: 2, strCardNumber: (transactionDetails.paymentInfo?.paymentDescription ?? ""), strCardType: (transactionDetails.paymentInfo?.cardType ?? ""), strCardScheme: strCardScheme, strCardToken: (transactionDetails.token ?? ""), transactionType: trans_type, walletUsedOrNot: 0)
            }
        } else if let error = error {
            // call update transaction api for failed transaction
            self.updateTransaction(strTransactionReference: (""), paymentStatus: 2, strCardNumber: (""), strCardType: (""), strCardScheme: "", strCardToken: "", transactionType: 0, walletUsedOrNot: 0)
        }
    }
    
    func showError(message: String) {
        DispatchQueue.main.async {
            AppSingleton.shared.showCustomPopUpWithOkButton(strButtonTitle: ObjKeymessages.kLABEL_OK, strMessage: message, showOnTopVC: false) { (isOk) in
                if isOk == true {
                    self.dismiss(animated: true, completion: nil)
                }
            }
        }
    }

    func updateTransaction(strTransactionReference: String, paymentStatus: Int, strCardNumber: String, strCardType: String, strCardScheme: String, strCardToken: String, transactionType: Int, walletUsedOrNot: Int) {
        if AppSingletonObj.isConnectedToNetworkForCheckoutScreen() {
            var dictParam : [String:Any] = [:]
            dictParam["iOrderId"] = orderId
            dictParam["tiTransactionType"] = transactionType
            dictParam["vTransactionRef"] = strTransactionReference
            dictParam["iPaymentStatus"] = paymentStatus
            dictParam["vCardName"] = strCardScheme

            print(dictParam)
            
            self.presenterScheduledOrderDetail?.apiCallForUpdateTransaction(dictData: dictParam, paymentStatus: paymentStatus, strCardNumber: strCardNumber, strCardType: strCardType, strCardScheme: strCardScheme, strCardToken: strCardToken, walletUsedOrNot: walletUsedOrNot)
        }
        else {
            self.displayAlertForFailedCaseAndGoToCurrentOrders(strMsg: ObjKeymessages.kMSG_NO_INTERNET)
        }
    }
}
