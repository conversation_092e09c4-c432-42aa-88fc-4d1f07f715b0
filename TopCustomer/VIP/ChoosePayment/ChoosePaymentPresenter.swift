
import UIKit

protocol ChoosePaymentPresentationProtocol {
    func apiCallForGetCardList()
    func apiResponseGetCardList(response:CardResponse?,error:Error?)

}

class ChoosePaymentPresenter: ChoosePaymentPresentationProtocol {
    
    // MARK: Objects & Variables
    weak var viewControllerChoosePayment: ChoosePaymentProtocol?
    var interactorChoosePayment: ChoosePaymentInteractorProtocol?
    
    func apiCallForGetCardList() {
        interactorChoosePayment?.apiCallForGetCardList()
    }

    func apiResponseGetCardList(response: CardResponse?, error: Error?) {
        if let error = error  {
            AppSingletonObj.showAlert(strMsg: error.localizedDescription)
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            return
        }
        
        if code == APIINVALIDAUTHORIZATIONCODE401 {
            AppSingletonObj.displaySessionExpiredAlert(strMsg: response.responseMessage ?? "")
            return
        }

        if code == APICODE400 {
            AppSingletonObj.showAlert(strMsg: response.responseMessage ?? "")
            return
        }
        
        guard let objCard = response.responseData,code == APISUCCESSCODE200  else {
            return
        }
        self.viewControllerChoosePayment?.loadCardList(objCard: objCard)
    }

}
