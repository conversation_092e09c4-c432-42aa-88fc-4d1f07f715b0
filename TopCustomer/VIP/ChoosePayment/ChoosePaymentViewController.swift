
import UIKit
import SwiftUI
import Tabby

protocol ChoosePaymentDelegate: AnyObject {
    func startPaymentWithTabby()
}
protocol ChoosePaymentProtocol: AnyObject {
    func loadCardList(objCard : CardResponseFields)

}

class ChoosePaymentViewController: <PERSON><PERSON>iew<PERSON>ontroller, ChoosePaymentProtocol {

    // MARK: Objects & Variables
    var presenterChoosePayment: ChoosePaymentPresentationProtocol?

    // MARK: IBOutlets
    @IBOutlet weak var tblCards: UITableView!
    @IBOutlet weak var btnPayWithNewCard: UIButton!
    @IBOutlet weak var lblChoosePaymentMethod: UILabel!
    @IBOutlet weak var lblCardOnDelivery: UILabel!
    @IBOutlet weak var lblApplePay: UILabel!
    @IBOutlet weak var btnSave: UIButton!
    @IBOutlet weak var btnCardDuring: UIButton!
    @IBOutlet weak var cons_tblCards_height: NSLayoutConstraint!
    @IBOutlet weak var btnApplePay: UIButton!
    @IBOutlet weak var lblGrandTotal: MaterialLocalizeLable!
    @IBOutlet weak var btnWallet: UIButton!
    @IBOutlet weak var lblWalletTitle: UILabel!
    @IBOutlet weak var lblWalletBalance: UILabel!
    @IBOutlet weak var viewDisableWallet: UIView!
    @IBOutlet weak var btnTabby: UIButton!
    @IBOutlet weak var lblTabby: MaterialLocalizeLable!
    @IBOutlet weak var lblTabbyDesc: MaterialLocalizeLable!
    
    var selectedPaymentMethodIndex = -1
    var arrCards: [GetUserCardDetailResponseFields] = []
    var passSelectedCardData: ((String, String, Bool) -> Void)?
    var applePaySelected: (() -> Void)?
    var codSelected: (() -> Void)?
    var walletSelected: (() -> Void)?
    var splitWalletSelected: ((String, String, Bool, String, String) -> Void)?
    var tabbySelected: (() -> Void)?

    var grandTotal = ""
    
    var WalletBalanceCase = 0
    var remainingAmountAfterWallet = 0.0
    weak var delegate: ChoosePaymentDelegate?
    
    // MARK: Object lifecycle
    /*
    override init(nibName nibNameOrNil: String?, bundle nibBundleOrNil: Bundle?) {
        super.init(nibName: nibNameOrNil, bundle: nibBundleOrNil)
        setup()
    }
    */
    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
        setup()
    }
    
    // MARK: Setup
    
    private func setup() {
        let viewController = self
        let interactor = ChoosePaymentInteractor()
        let presenter = ChoosePaymentPresenter()
        
        //View Controller will communicate with only presenter
        viewController.presenterChoosePayment = presenter
        
        //Presenter will communicate with Interector and Viewcontroller
        presenter.viewControllerChoosePayment = viewController
        presenter.interactorChoosePayment = interactor
        
        //Interactor will communucate with only presenter.
        interactor.presenterChoosePayment = presenter
    }
    
    
    // MARK: View lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        self.setTexts()

        if AppSingletonObj.isConnectedToNetwork(){
            self.presenterChoosePayment?.apiCallForGetCardList()
        }

    }
    
    private func setTexts() {
        btnPayWithNewCard.setTitle(ObjKeymessages.kLABEL_PAY_WITH_OTHER_CARD, for: .normal)
        lblChoosePaymentMethod.text = ObjKeymessages.kLABEL_CHOOSE_PAYMENT_METHOD
        lblCardOnDelivery.text = ObjKeymessages.kLABEL_CARD_ON_DELIVERY
        lblApplePay.text = ObjKeymessages.kLABEL_APPLE_PAY
        btnSave.setTitle(ObjKeymessages.kLABEL_SELECT, for: .normal)
        lblWalletTitle.text = ObjKeymessages.kLABEL_WALLET

        lblGrandTotal.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: grandTotal)
        
        lblWalletBalance.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "0")
        lblTabby.text = "tabby".localized
        lblTabbyDesc.text = "divide_your_invoice_on_4_months".localized
        self.setWalletBalance()
        
        if UserDefaults.standard.getLanguage()! == UserAPI.VLanguage_userLanguage.en.rawValue {       // english
            btnPayWithNewCard.imageEdgeInsets = UIEdgeInsets(top: 0, left: 15, bottom: 0, right: 0)
            btnPayWithNewCard.titleEdgeInsets = UIEdgeInsets(top: 0, left: 30, bottom: 0, right: 0)
        }
        else {   // arabic
            btnPayWithNewCard.imageEdgeInsets = UIEdgeInsets(top: 0, left: 0, bottom: 0, right: 15)
            btnPayWithNewCard.titleEdgeInsets = UIEdgeInsets(top: 0, left: 0, bottom: 0, right: 30)
        }

    }
    
    private func setWalletBalance() {
        if var walletBalance = UserDefaults.standard.object(forKey: USERDEFAULTS_INFO_KEY.WALLET_BALANCE) as? String,
        var walletBalanceEnNumber = UserDefaults.standard.object(forKey: USERDEFAULTS_INFO_KEY.WALLET_BALANCE_EN_NUMBER) as? String {
            lblWalletBalance.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(walletBalance)")
            walletBalanceEnNumber = walletBalanceEnNumber.replacingOccurrences(of: ",", with: "")
            if (Double(walletBalanceEnNumber) ?? 0) <= 0 { // Wallet balance is 0. Disable wallet view
                viewDisableWallet.isHidden = false
                WalletBalanceCase = 0
            }
            else if (Double(walletBalanceEnNumber) ?? 0) >= (Double(grandTotal) ?? 0) { // full amt can be paid from wallet
                viewDisableWallet.isHidden = true
                WalletBalanceCase = 1
            }
            else {  // utilise wallet balance and pay remaining amt from other payment method
                viewDisableWallet.isHidden = true
                WalletBalanceCase = 2
                remainingAmountAfterWallet = (Double(grandTotal) ?? 0) - (Double(walletBalanceEnNumber) ?? 0)
            }
        }
    }
    
    @IBAction func btnDimViewAction(_ sender: Any) {
        self.dismiss(animated: true, completion: nil)
    }

    @IBAction func payWithNewCardAction(_ sender: Any) {
        isApplePayPayment = false
        if btnWallet.isSelected == true {  // wallet is selected
            if WalletBalanceCase == 2 {  // wallet part utilisation
                
                // pass data of other payment method along with Wallet
                self.splitWalletSelected?("", "", false, "Card", "\(forTrailingZero(temp: remainingAmountAfterWallet))")
            }
            else {
                self.passSelectedCardData?("", "", false)
            }
        }
        else {
            self.passSelectedCardData?("", "", false)
        }
        
        self.dismiss(animated: true, completion: nil)
    }
    
    @IBAction func cardOnDeliveryAction(_ sender: Any) {
        isApplePayPayment = false
        if WalletBalanceCase == 2 {  // wallet part utilisation
            
        }
        else {
            btnWallet.isSelected = false
        }
        selectedPaymentMethodIndex = -1
        btnCardDuring.isSelected = true
        btnApplePay.isSelected = false
        btnTabby.isSelected = false
        tblCards.reloadData()
    }
    
    @IBAction func applePayAction(_ sender: Any) {
        isApplePayPayment = true
        if WalletBalanceCase == 2 {  // wallet part utilisation
            
        }
        else {
            btnWallet.isSelected = false
        }
        selectedPaymentMethodIndex = -1
        btnCardDuring.isSelected = false
        btnTabby.isSelected = false
        btnApplePay.isSelected = true
        tblCards.reloadData()
    }
    
    @IBAction func tabbyPaymentAction(_ sender: Any) {
        isApplePayPayment = false
        if WalletBalanceCase == 2 {  // wallet part utilisation
            
        }
        else {
            btnWallet.isSelected = false
        }
        selectedPaymentMethodIndex = -1
        btnCardDuring.isSelected = false
        btnApplePay.isSelected = false
        btnTabby.isSelected = true
        tblCards.reloadData()
    }
    
    @IBAction func walletAction(_ sender: Any) {
        isApplePayPayment = false
        if viewDisableWallet.isHidden == false { // show Insufficient wallet balance alert
            AppSingletonObj.showAlert(strMessage: ObjKeymessages.kMSG_LOW_WALLET_BALANCE)
            return
        }

        
        if WalletBalanceCase == 2 {  // wallet part utilisation

            btnWallet.isSelected = !btnWallet.isSelected
            if btnWallet.isSelected == true {
                lblGrandTotal.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: remainingAmountAfterWallet))")
            }
            else {
                lblGrandTotal.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(grandTotal)")
            }
            
        }
        else {
            selectedPaymentMethodIndex = -1
            btnCardDuring.isSelected = false
            btnApplePay.isSelected = false
            btnTabby.isSelected = false
            btnWallet.isSelected = true

        }
        
        tblCards.reloadData()

    }

    @IBAction func saveAction(_ sender: Any) {
        if selectedPaymentMethodIndex == -1 && btnCardDuring.isSelected == false && btnApplePay.isSelected == false && btnWallet.isSelected == false && btnTabby.isSelected == false {  // no method selected
            AppSingletonObj.showAlert(strMessage: ObjKeymessages.kLABEL_NO_CARD_SELECTED)
            return
        }
        else {
            if btnWallet.isSelected == true {  // wallet is selected
                if WalletBalanceCase == 2 {  // wallet part utilisation
                    if selectedPaymentMethodIndex == -1 && btnCardDuring.isSelected == false && btnApplePay.isSelected == false {  // no method selected
                        AppSingletonObj.showAlert(strMessage: ObjKeymessages.kLABEL_NO_CARD_SELECTED)
                        return
                    }
                    
                    // pass data of other payment method along with Wallet
                    if selectedPaymentMethodIndex != -1 {  // card selected from list
                        self.splitWalletSelected?(arrCards[selectedPaymentMethodIndex].vToken ?? "", arrCards[selectedPaymentMethodIndex].vTransactionId ?? "", true, "Card", "\(forTrailingZero(temp: remainingAmountAfterWallet))")
                    }
                    else if btnCardDuring.isSelected == true {  // card during delivery is selected
                        self.splitWalletSelected?("", "", false, "COD", "\(forTrailingZero(temp: remainingAmountAfterWallet))")
                    }
                    else if btnApplePay.isSelected == true {  // apple pay is selected
                        self.splitWalletSelected?("", "", false, "ApplePay", "\(forTrailingZero(temp: remainingAmountAfterWallet))")
                    }
                }
                else {
                    self.walletSelected?()
                }
            }
            else if selectedPaymentMethodIndex != -1 {  // card selected from list
                self.passSelectedCardData?(arrCards[selectedPaymentMethodIndex].vToken ?? "", arrCards[selectedPaymentMethodIndex].vTransactionId ?? "", true)
            }
            else if btnCardDuring.isSelected == true {  // card during delivery is selected
                self.codSelected?()
            }
            else if btnApplePay.isSelected == true {  // apple pay is selected
                self.applePaySelected?()
            } else if btnTabby.isSelected == true {
                // pay with Tabby
                self.tabbySelected?()
                self.delegate?.startPaymentWithTabby()
            }
        }
        self.dismiss(animated: true, completion: nil)
    }
    
    func loadCardList(objCard : CardResponseFields) {

        self.arrCards = objCard.userCard ?? []
        
        // save wallet balance
        let WalletBalance = Double(objCard.latestWalletBalance ?? "") ?? 0
        let strWalletBalance = "\(forTrailingZero(temp: WalletBalance))"
        UserDefaults.standard.set(strWalletBalance, forKey: USERDEFAULTS_INFO_KEY.WALLET_BALANCE)
        // save wallet balance with english numbers
        let strWalletBalanceEnNumbers = "\(forTrailingZeroEnglishNumbersOnly(temp: Double(objCard.latestWalletBalance ?? "") ?? 0))"
        UserDefaults.standard.set(strWalletBalanceEnNumbers, forKey: USERDEFAULTS_INFO_KEY.WALLET_BALANCE_EN_NUMBER)
        
        UserDefaults.standard.synchronize()
        self.setWalletBalance()

        tblCards.reloadData()
        
        if self.arrCards.count <= 0 {
            self.cons_tblCards_height.constant = 0
        }
        else {
            if self.tblCards.contentSize.height <= 200 {
                self.cons_tblCards_height.constant = self.tblCards.contentSize.height
            }
            else {
                self.cons_tblCards_height.constant = 200
            }
        }
    }

}

extension ChoosePaymentViewController : UITableViewDelegate, UITableViewDataSource{
    func numberOfSections(in tableView: UITableView) -> Int {
        return 1
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return arrCards.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tblCards.dequeueReusableCell(withIdentifier: "PaymentCardListTableViewCell") as! PaymentCardListTableViewCell
        
        if indexPath.row == selectedPaymentMethodIndex {
            cell.btnSelection.isSelected = true
        }
        else {
            cell.btnSelection.isSelected = false
        }
        
        cell.lblCardNumber.text = arrCards[indexPath.row].vPaymentDescription
        
        return cell
    }
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        if WalletBalanceCase == 2 {  // wallet part utilisation
            
        }
        else {
            btnWallet.isSelected = false
        }

        btnCardDuring.isSelected = false
        btnApplePay.isSelected = false
        selectedPaymentMethodIndex = indexPath.row
        tblCards.reloadData()
    }
}
