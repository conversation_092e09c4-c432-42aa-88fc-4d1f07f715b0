
import UIKit

protocol ChoosePaymentInteractorProtocol {
    func apiCallForGetCardList()

}

protocol ChoosePaymentDataStore {
    //{ get set }
}

class ChoosePaymentInteractor: ChoosePaymentInteractorProtocol, ChoosePaymentDataStore {

    // MARK: Objects & Variables
    var presenterChoosePayment: ChoosePaymentPresentationProtocol?
    
    func apiCallForGetCardList() {
        ActivityIndicator.shared.showCentralSpinner()
        
        let authorization = getAuthorizationText()
        
        UserCardsAPI.viewUserCards(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang) { data, error in
            ActivityIndicator.shared.hideCentralSpinner()
            self.presenterChoosePayment?.apiResponseGetCardList(response: data, error: error)
        }
        
    }

}
