
import UIKit

protocol CancelReasonProtocol: AnyObject {
    func refreshReasonsList(arrReasons : [CancelOrderReasonResponseFields])
    func displayAlert(string: String)
    func showAlertAndReload(strMessage :  String)

}

protocol ReloadOrderListDelegate {
    func reloadScreen()
}

class CancelReasonViewController: BaseViewController, CancelReasonProtocol {

    // MARK: Objects & Variables
    var presenterCancelReason: CancelReasonPresentationProtocol?

    // MARK: IBOutlets
    @IBOutlet weak var viewOuter: UIView!
    @IBOutlet weak var cons_viewOuter_height: NSLayoutConstraint!
    @IBOutlet weak var lblChooseAReason: MaterialLocalizeLable!
    @IBOutlet weak var tblReason: UITableView!
    @IBOutlet weak var btnSubmit: UIButton!
    @IBOutlet weak var btnCancel: UIButton!
    @IBOutlet weak var cons_tblReason_height: NSLayoutConstraint!

    var selectedIndex = -1
    var arrReason: [CancelOrderReasonResponseFields] = []
    var orderId = 0
    var delegate : ReloadOrderListDelegate?
    var tableHeightObserver: NSKeyValueObservation?

    // MARK: Object lifecycle
    /*
    override init(nibName nibNameOrNil: String?, bundle nibBundleOrNil: Bundle?) {
        super.init(nibName: nibNameOrNil, bundle: nibBundleOrNil)
        setup()
    }
    */
    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
        setup()
    }
    
    // MARK: Setup
    
    private func setup() {
        let viewController = self
        let interactor = CancelReasonInteractor()
        let presenter = CancelReasonPresenter()
        
        //View Controller will communicate with only presenter
        viewController.presenterCancelReason = presenter
        
        //Presenter will communicate with Interector and Viewcontroller
        presenter.viewControllerCancelReason = viewController
        presenter.interactorCancelReason = interactor
        
        //Interactor will communucate with only presenter.
        interactor.presenterCancelReason = presenter
    }
    
    
    // MARK: View lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        self.setTexts()
        self.viewOuter.isHidden = true
        self.getReasons()

        tableHeightObserver = tblReason.observe(\.contentSize, changeHandler: { _, _ in
            self.cons_tblReason_height.constant = self.tblReason.contentSize.height
        })

    }
    
    private func getReasons() {
        if AppSingletonObj.isConnectedToNetwork(){
            self.presenterCancelReason?.apiCallForGetReasons()
        }
    }

    private func setTexts() {
        lblChooseAReason.text = ObjKeymessages.kLABEL_CANCEL_REASON_TITLE
        btnSubmit.setTitle(ObjKeymessages.kLABEL_SUBMIT, for: .normal)
        btnCancel.setTitle(ObjKeymessages.kLABEL_CANCEL, for: .normal)
    }
    
    @IBAction func btnDimViewAction(_ sender: Any) {
        self.dismiss(animated: true, completion: nil)
    }

    @IBAction func actionSubmit(_ sender: Any) {
        
        if selectedIndex == -1 {
            AppSingletonObj.showAlert(strMessage: ObjKeymessages.kMSG_SELECT_ORDER_CANCEL_REASON)
            return
        }
        
        var dictParam : [String:Any] = [:]
        dictParam["iOrderId"] = orderId
        dictParam["iOrderCancelReasonId"] = arrReason[selectedIndex].iOrderCancelReasonId

        if AppSingletonObj.isConnectedToNetwork(){
            self.presenterCancelReason?.apiCallForCancelOrder(dictData: dictParam)
        }
    }
    
    @IBAction func actionCancel(_ sender: Any) {
        self.dismiss(animated: true, completion: nil)
    }

    func refreshReasonsList(arrReasons : [CancelOrderReasonResponseFields]) {
        arrReason = arrReasons
        tblReason.reloadData()
        self.viewOuter.isHidden = false

    }

    func displayAlert(string: String) {
        AppSingleton.shared.showCustomPopUpWithOkButton(strButtonTitle: ObjKeymessages.kLABEL_OK, strMessage: string, showOnTopVC: false) { (isOk) in
        }
    }

    func showAlertAndReload(strMessage :  String) {
        delegate?.reloadScreen()
        self.dismiss(animated: true, completion: nil)
        AppSingletonObj.showAlert(strMessage: strMessage)
    }

}

extension CancelReasonViewController : UITableViewDelegate, UITableViewDataSource{
    func numberOfSections(in tableView: UITableView) -> Int {
        return 1
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return arrReason.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tblReason.dequeueReusableCell(withIdentifier: "CancelReasonTableViewCell") as! CancelReasonTableViewCell
        
        if indexPath.row == selectedIndex {
            cell.btnSelection.isSelected = true
        }
        else {
            cell.btnSelection.isSelected = false
        }

        cell.lblReason.text = arrReason[indexPath.row].cancelReason
        
        return cell
    }
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        selectedIndex = indexPath.row
        tblReason.reloadData()

    }
}
