
import UIKit

protocol CancelReasonInteractorProtocol {
    func apiCallForGetReasons()
    func apiCallForCancelOrder(dictData:[String:Any])

}

protocol CancelReasonDataStore {
    //{ get set }
}

class CancelReasonInteractor: CancelReasonInteractorProtocol, CancelReasonDataStore {

    // MARK: Objects & Variables
    var presenterCancelReason: CancelReasonPresentationProtocol?
    
    func apiCallForGetReasons() {
        
        ActivityIndicator.shared.showCentralSpinner()
        
        let authorization = getAuthorizationText()
        
        OrderAPI.orderCancelReason(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang) { data, error in
            ActivityIndicator.shared.hideCentralSpinner()
            self.presenterCancelReason?.apiResponseGetReasons(response: data, error: error)

        }
        
    }

    func apiCallForCancelOrder(dictData:[String:Any]) {
        
        ActivityIndicator.shared.showCentralSpinner()
        
        let authorization = getAuthorizationText()
        
        OrderAPI.cancelOrder(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, iOrderId: Int(setDataInString(dictData["iOrderId"] as AnyObject)) ?? 0, iOrderCancelReasonId: Int(setDataInString(dictData["iOrderCancelReasonId"] as AnyObject)) ?? 0) { data, error in
            ActivityIndicator.shared.hideCentralSpinner()
            self.presenterCancelReason?.apiResponseCancelOrder(response: data, error: error)
            
        }
    }
}
