
import UIKit

protocol CancelReasonPresentationProtocol {
    func apiCallForGetReasons()
    func apiResponseGetReasons(response:CancelOrderReasonResponse?,error:Error?)

    func apiCallForCancelOrder(dictData:[String:Any])
    func apiResponseCancelOrder(response:CommonFields?,error:Error?)

}

class CancelReasonPresenter: CancelReasonPresentationProtocol {
    
    // MARK: Objects & Variables
    weak var viewControllerCancelReason: CancelReasonProtocol?
    var interactorCancelReason: CancelReasonInteractorProtocol?
    
    func apiCallForGetReasons() {
        interactorCancelReason?.apiCallForGetReasons()
    }

    func apiResponseGetReasons(response:CancelOrderReasonResponse?,error:Error?) {
        if let error = error  {
            viewControllerCancelReason?.displayAlert(string: error.localizedDescription)
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            return
        }
        
        if code == APIINVALIDAUTHORIZATIONCODE401 {
            AppSingletonObj.displaySessionExpiredAlert(strMsg: response.responseMessage ?? "")
            return
        }

        if code == APICODE400 {
            viewControllerCancelReason?.displayAlert(string: response.responseMessage ?? "")
            return
        }
        
        guard let model = response.responseData,code == APISUCCESSCODE200  else {
            return
        }
        self.viewControllerCancelReason?.refreshReasonsList(arrReasons: model)
    }

    func apiCallForCancelOrder(dictData:[String:Any]) {
        interactorCancelReason?.apiCallForCancelOrder(dictData: dictData)
    }

    func apiResponseCancelOrder(response:CommonFields?,error:Error?) {
        if let error = error  {
            viewControllerCancelReason?.displayAlert(string: error.localizedDescription)
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            return
        }
        
        if code == APIINVALIDAUTHORIZATIONCODE401 {
            AppSingletonObj.displaySessionExpiredAlert(strMsg: response.responseMessage ?? "")
            return
        }

        if code == APICODE400 {
            viewControllerCancelReason?.displayAlert(string: response.responseMessage ?? "")
            return
        }
        
        self.viewControllerCancelReason?.showAlertAndReload(strMessage: response.responseMessage ?? "")
    }

}
