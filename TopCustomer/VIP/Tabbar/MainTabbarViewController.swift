
import UIKit
import <PERSON><PERSON><PERSON>bar
import AppsFlyerLib
import SwiftUI

class MainTabbarViewController: UITabBarController,UITabBarControllerDelegate {
    var deepLinkData: DeepLink? = nil
    
    override func viewWillAppear(_ animated: <PERSON><PERSON>) {
        super.viewWillAppear(animated)
        UITabBar.appearance().tintColor = .white //UIColor.AppTheme_SelectedTabColor_F4BA45
        UITabBar.appearance().barTintColor = .white //UIColor.AppTheme_SelectedTabColor_F4BA45

        tabBar.items![0].title = ObjKeymessages.kLABEL_HOME
        tabBar.items![1].title = ObjKeymessages.kLABEL_OFFERS
        tabBar.items![1].badgeValue = "New".localized
        tabBar.items![2].title = ""
        tabBar.items![3].title = ObjKeymessages.kLABEL_MY_ORDERS
        tabBar.items![4].title = ObjKeymessages.kLABEL_SETTINGS

        tabBar.items?[2].isEnabled = false
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        addObservers()

        self.navigationController?.interactivePopGestureRecognizer?.delegate = self as? UIGestureRecognizerDelegate
        self.navigationController?.interactivePopGestureRecognizer?.isEnabled = true
        self.delegate = self
        UITabBarItem.appearance().setTitleTextAttributes([NSAttributedString.Key.foregroundColor: UIColor.white, NSAttributedString.Key.font: UIFont(name: Fonts.LoewNextArabicBold, size: 10)!], for: .selected)
        
        UITabBarItem.appearance().setTitleTextAttributes([NSAttributedString.Key.foregroundColor: UIColor.AppTheme_LightGrayColor_A0A0A0, NSAttributedString.Key.font: UIFont(name: Fonts.LoewNextArabicBold, size: 10)!], for: .normal)
        self.navigationController?.isNavigationBarHidden = true
        UITabBar.appearance().tintAdjustmentMode = .normal
        
        if let myTabbar = tabBar as? STTabbar {
            myTabbar.buttonImage = UIImage(named: "icn_cart")
//            myTabbar.unselectedItemColor = UIColor.AppTheme_SelectedTabColor
            myTabbar.centerButtonActionHandler = {
                print("Center Button Tapped")
                if !User.shared.checkUserLoginStatus() {
                    self.showNoLoginUserAlert(complation: nil)
                    return
                }
                /*let vc = MyCartStoryboard.instantiate(MyCartViewController.self)
                vc.delegate = self
                self.pushVC(vc)*/
                
                /*if self.selectedIndex != 2 {
                    let vc = homeStoryboard.instantiate(MainTabbarViewController.self)
                    vc.selectedIndex = 2
                    let navigationVc = UINavigationController(rootViewController: vc)
                    navigationVc.setNavigationBarHidden(true, animated: false)
                    AppDel?.window?.rootViewController = navigationVc
                }*/
                
//                if let tabbarC = self.tabBarController {
                        self.selectedIndex = 2
                        let setting = self.viewControllers![2]
                        self.tabBarController(self, didSelect: setting)

//                }
                
            }
        }
    }
    
    func addObservers() {
        removeObservers()
        NotificationCenter.default.addObserver(self, selector: #selector(goToHomeScreen), name: NSNotification.Name(rawValue: "ProductFromDeepLink"), object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(goToOfferScreen), name: NSNotification.Name(rawValue: "OfferFromDeepLink"), object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(goToHomeScreenForRating), name: NSNotification.Name(rawValue: "OpenRatingPopup"), object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(goToProductListScreen), name: NSNotification.Name(rawValue: "GoToProductList"), object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(goToCurrentOrderDetailScreen), name: NSNotification.Name(rawValue: "OpenCurrentOrderDetailPopup"), object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(goToHistoryOrderDetailScreen), name: NSNotification.Name(rawValue: "OpenHistoryOrderDetailPopup"), object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(goToCartListScreen), name: NSNotification.Name(rawValue: "OpenCartScreen"), object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(goToScheduledOrderDetailScreen), name: NSNotification.Name(rawValue: "OpenScheduledOrderDetailPopup"), object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(goToCurrentOrdersListScreen), name: NSNotification.Name(rawValue: "GoToCurrentOrdersForIPN"), object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(openBannerInfoPopup), name: NSNotification.Name(rawValue: "BannerInfoFromDeepLink"), object: nil)

    }

    func removeObservers() {
        NotificationCenter.default.removeObserver(self)
    }

    @objc func goToHistoryOrderDetailScreen(_ notification: NSNotification) {
        self.selectedIndex = 4

        if let strOrderId = (notification.object as? [String:Any]) {
            if let id = strOrderId["iOrderId"] as? String {
                delay(0.5) {
                    NotificationCenter.default.post(name: NSNotification.Name("OpenHistoryOrder"), object: ["iOrderId": id], userInfo: nil)
                }
            }
        }
    }

    @objc func goToCartListScreen(_ notification: NSNotification) {
        self.selectedIndex = 2

//        if let strOrderId = (notification.object as? [String:Any]) {
//            if let id = strOrderId["iOrderId"] as? String {
//                delay(0.5) {
//                    NotificationCenter.default.post(name: NSNotification.Name("OpenHistoryOrder"), object: ["iOrderId": id], userInfo: nil)
//                }
//            }
//        }
    }

    @objc func goToCurrentOrderDetailScreen(_ notification: NSNotification) {
        self.selectedIndex = 3

        if let strOrderId = (notification.object as? [String:Any]) {
            if let id = strOrderId["iOrderId"] as? String {
                delay(0.5) {
                    NotificationCenter.default.post(name: NSNotification.Name("OpenCurrentOrderDetail"), object: ["iOrderId": id], userInfo: nil)
                }
            }
        }
    }

    @objc func goToScheduledOrderDetailScreen(_ notification: NSNotification) {
        self.selectedIndex = 3

        if let strOrderId = (notification.object as? [String:Any]) {
            if let id = strOrderId["iOrderId"] as? String {
                delay(0.5) {
                    NotificationCenter.default.post(name: NSNotification.Name("OpenScheduledOrderList"), object: ["iOrderId": id], userInfo: nil)
                }
            }
        }
    }

    @objc func goToHomeScreen(_ notification: NSNotification) {
        self.selectedIndex = 0

        if let strProductId = (notification.object as? [String:Any]) {
            if let id = strProductId["productId"] as? String {
                print(id)
                NotificationCenter.default.post(name: NSNotification.Name("OpenProductDetailFromDeepLink"), object: ["productId": id], userInfo: nil)
            }
        }
    }

    @objc func goToOfferScreen(_ notification: NSNotification) {
        self.selectedIndex = 1

        if let strOfferId = (notification.object as? [String:Any]) {
            if let id = strOfferId["offerId"] as? String {
                print(id)
                delay(0.5) {
//                DispatchQueue.main.async {
                    NotificationCenter.default.post(name: NSNotification.Name("OpenOfferDetailFromDeepLink"), object: ["offerId": id], userInfo: nil)
                }
            }
        }
    }

    @objc func goToHomeScreenForRating(_ notification: NSNotification) {
        self.selectedIndex = 0

        if let strOrderId = (notification.object as? [String:Any]) {
            if let id = strOrderId["iOrderId"] as? String {
                NotificationCenter.default.post(name: NSNotification.Name("OpenRatingPopupOnHome"), object: ["iOrderId": id], userInfo: nil)
            }
        }
    }

    @objc func goToProductListScreen(_ notification: NSNotification) {
        self.selectedIndex = 0

        if let strCategoryId = (notification.object as? [String:Any]) {
            if let id = strCategoryId["categoryId"] as? String {
                print(id)
                var strName = ""
                if let name = strCategoryId["categoryName"] as? String {
                    strName = name
                }
                NotificationCenter.default.post(name: NSNotification.Name("OpenProductSearchScreen"), object: ["categoryId": id, "categoryName": strName], userInfo: nil)
            }
        }
    }

    @objc func openBannerInfoPopup(_ notification: NSNotification) {
        self.selectedIndex = 0

        if let strBannerId = (notification.object as? [String:Any]) {
            if let id = strBannerId["bannerId"] as? String {
                NotificationCenter.default.post(name: NSNotification.Name("OpenBannerInfoPopupOnHome"), object: ["bannerId": id], userInfo: nil)
            }
        }
    }

    @objc func goToCurrentOrdersListScreen(_ notification: NSNotification) {
        self.selectedIndex = 3

//        if let strOrderId = (notification.object as? [String:Any]) {
//            if let id = strOrderId["iOrderId"] as? String {
//                delay(0.5) {
//                    NotificationCenter.default.post(name: NSNotification.Name("OpenCurrentOrderDetail"), object: ["iOrderId": id], userInfo: nil)
//                }
//            }
//        }
    }

    override func viewDidDisappear(_ animated: Bool) {
        super.viewDidDisappear(animated)
        NotificationCenter.default.removeObserver(self)
    }
     
    func tabBarController(_ tabBarController: UITabBarController, shouldSelect viewController: UIViewController) -> Bool {
        if let index = self.viewControllers?.firstIndex(of: viewController) {
            if index == 1 || index == 3 || index == 4 {
                if !User.shared.checkUserLoginStatus() {
                    self.showNoLoginUserAlert(complation: nil)
                    return false
                }
            }
            else if index == 0 {
                if self.selectedIndex == 0 {
                    if self.topMostViewController is NewHomeViewController {
                        NotificationCenter.default.post(name: NSNotification.Name("RefreshHomeListing"), object: nil, userInfo: nil)
                    }
                }
            }
        }
        return true
    }

    func tabBarController(_ tabBarController: UITabBarController, didSelect viewController: UIViewController) {
//        print("called")
        
        let navigationController = viewController as? UINavigationController
        navigationController?.popToRootViewController(animated: true)
    }
    
    func attributionDataToString() -> NSMutableAttributedString {
        let newString = NSMutableAttributedString()
        let boldAttribute = [
            NSAttributedString.Key.font: UIFont(name: "HelveticaNeue-Bold", size: 18.0)!
        ]
        let regularAttribute = [
            NSAttributedString.Key.font: UIFont(name: "HelveticaNeue-Light", size: 18.0)!
        ]
        for (key, value) in deepLinkData!.clickEvent {
            print("ViewController", key, ":",value)
            let keyStr = key
            let boldKeyStr = NSAttributedString(string: keyStr, attributes: boldAttribute)
            newString.append(boldKeyStr)
            let valueStr = value as? String ?? "null"
            let normalValueStr = NSAttributedString(string: ": \(valueStr)\n", attributes: regularAttribute)
            newString.append(normalValueStr)
        }
        return newString
    }
}

/*extension MainTabbarViewController : UpdateCountFromMyCartProtocol {
    
    func updateCartFromMyCart(cartCount:Int?) {
        // update my cart button count
        if let myTabbar = tabBar as? STTabbar {
            if cartCount ?? 0 <= 0 {
                myTabbar.label?.isHidden = true
            }
            else {
                myTabbar.label?.isHidden = false
                myTabbar.label?.text = "\(cartCount ?? 0)"
            }
        }

    }
}*/

extension UIViewController {
    func showNoLoginUserAlert(complation : (()->())?) {
        AppSingleton.shared.showCustomPopUpWithYesNoButton(strButton1Title: ObjKeymessages.kLABEL_YES, strButton2Title: ObjKeymessages.kLABEL_NO, strMessage: ObjKeymessages.kMSG_USER_IS_NOT_LOGIN_INTO_APPLICATION, showOnTopVC: true) { (isOk) in
            if isOk == true {
                let objVC : LoginViewController = UIStoryboard.storyboard(.Main).instantiateViewController()
                let navigationVc = UINavigationController(rootViewController: objVC)
                navigationVc.setNavigationBarHidden(true, animated: false)
                AppDel?.window?.rootViewController = navigationVc
            }
        }
    }
    
    func showNoLoginUserAlertToHome(complation: (()->())?) {
        AppSingleton.shared.showCustomPopUpWithYesNoButton(
            strButton1Title: ObjKeymessages.kLABEL_YES,
            strButton2Title: ObjKeymessages.kLABEL_NO,
            strMessage: ObjKeymessages.kMSG_USER_IS_NOT_LOGIN_INTO_APPLICATION,
            showOnTopVC: true
        ) { (isOk) in
            if isOk {
                // لو اختار YES -> Login
                let objVC: LoginViewController = UIStoryboard.storyboard(.Main).instantiateViewController()
                let navigationVc = UINavigationController(rootViewController: objVC)
                navigationVc.setNavigationBarHidden(true, animated: false)
                AppDel?.window?.rootViewController = navigationVc
            } else {
                // لو اختار NO -> SwiftUI Home
                let homeView = MainTabBarView()
                let hostingController = UIHostingController(rootView: homeView)
                AppDel?.window?.rootViewController = hostingController
            }
            
            complation?()
        }
    }
}

extension UIApplication {
    func topViewController(base: UIViewController? = UIApplication.shared.connectedScenes
        .compactMap { ($0 as? UIWindowScene)?.keyWindow }
        .first?.rootViewController) -> UIViewController? {
        
        if let nav = base as? UINavigationController {
            return topViewController(base: nav.visibleViewController)
        }
        if let tab = base as? UITabBarController {
            if let selected = tab.selectedViewController {
                return topViewController(base: selected)
            }
        }
        if let presented = base?.presentedViewController {
            return topViewController(base: presented)
        }
        return base
    }
}

extension UITabBarController: CAAnimationDelegate {

    func animateTabBarBadgeView() {
        loopThrowViews(view: self.tabBar)
    }
    
    func loopThrowViews(view: UIView) {
        for subview in (view.subviews) {
            let type = String(describing: type(of: subview))
            print(type)
            if type == "_UIBadgeView" {
                print("this is BadgeView")
                print("index = \(String(describing: subview.superview?.tag))")
                animateView(view: subview)
            }
            else {
                loopThrowViews(view:subview)
            }
        }
    }
    
    func animateView(view: UIView) {
        let animation = CABasicAnimation(keyPath: "opacity")
        animation.duration = 1.0
        animation.repeatCount = 100
        animation.autoreverses = true
        animation.fillMode = .removed
        animation.fromValue = 0
        animation.toValue = 1
        animation.delegate = self
        view.layer.add(animation, forKey: "opacity")
    }
    
    public func animationDidStop(_ anim: CAAnimation, finished flag: Bool) {
        debugPrint("stop Animation")
    }
    
}
