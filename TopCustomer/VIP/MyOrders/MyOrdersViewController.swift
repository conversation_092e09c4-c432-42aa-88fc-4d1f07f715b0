
import UIKit

protocol MyOrdersProtocol: AnyObject {
  
}

class MyOrdersViewController: BaseViewController, MyOrdersProtocol {

    // MARK: Objects & Variables
    var presenterMyOrders: MyOrdersPresentationProtocol?

    // MARK: IBOutlets
    @IBOutlet weak var btnCurrentOrders: UIButton!
    @IBOutlet weak var btnScheduledOrders: UIButton!
    @IBOutlet weak var lblTabUnderline: UILabel!
    @IBOutlet weak var rootContainerView: UIView!
    @IBOutlet weak var lblTitle: UILabel!
    @IBOutlet weak var topView: UIView!
    @IBOutlet weak var lineView: UIView!
    @IBOutlet weak var lblCount: UILabel!
    
    
    
    private var selectedMenu = 0
    private let maxPage = 1 //total no of page 2 -> (2 - 1) = 1

    lazy private var objCurrentOrders : CurrentOrdersViewController = {
        let vc : CurrentOrdersViewController = UIStoryboard.storyboard(.Home).instantiateViewController()
        vc.delegate = self
        return vc
    }()

    lazy private var objScheduledOrders : ScheduledOrdersViewController = {
        let vc : ScheduledOrdersViewController = UIStoryboard.storyboard(.Home).instantiateViewController()
        return vc
    }()

    
    // MARK: Object lifecycle
    /*
    override init(nibName nibNameOrNil: String?, bundle nibBundleOrNil: Bundle?) {
        super.init(nibName: nibNameOrNil, bundle: nibBundleOrNil)
        setup()
    }
    */
    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
        setup()
    }
    
    // MARK: Setup
    
    private func setup() {
        let viewController = self
        let interactor = MyOrdersInteractor()
        let presenter = MyOrdersPresenter()
        
        //View Controller will communicate with only presenter
        viewController.presenterMyOrders = presenter
        
        //Presenter will communicate with Interector and Viewcontroller
        presenter.viewControllerMyOrders = viewController
        presenter.interactorMyOrders = interactor
        
        //Interactor will communucate with only presenter.
        interactor.presenterMyOrders = presenter
    }
    
    
    // MARK: View lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        self.setTexts()
        self.addGestures()
//        self.selectCurrentOrdersTab()

//        topView.dropShadow(color: UIColor.AppTheme_DropShadowColor_00000046, offSet: CGSize(width: 2.0, height: 1.0))
//        topView.addBottomShadow()
//        lineView.addBottomShadow()

    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        addObservers()

        self.selectCurrentOrdersTab()
    }
    
    func addObservers() {
        removeObservers()
        NotificationCenter.default.addObserver(self, selector: #selector(openScheduledOrderList), name: NSNotification.Name(rawValue: "OpenScheduledOrderList"), object: nil)

    }

    func removeObservers() {
        NotificationCenter.default.removeObserver(self)
    }

    override func viewDidDisappear(_ animated: Bool) {
        super.viewDidDisappear(animated)
        NotificationCenter.default.removeObserver(self)
    }

    @objc func openScheduledOrderList(_ notification: NSNotification) {
        if let strOrderId = (notification.object as? [String:Any]) {
            if let id = strOrderId["iOrderId"] as? String {
                self.selectedTab(index: 1)
                self.selectMenu(selectedIndex: 1)
                NotificationCenter.default.post(name: NSNotification.Name("OpenScheduledOrderDetail"), object: ["iOrderId": id], userInfo: nil)
            }
        }
    }
    
    private func addGestures() {
        let swipeLeft = UISwipeGestureRecognizer(target: self, action: #selector(viewSwipped(gesture:)))
        swipeLeft.direction = UISwipeGestureRecognizer.Direction.left
        rootContainerView.addGestureRecognizer(swipeLeft)
        
        let swipeRight = UISwipeGestureRecognizer(target: self, action: #selector(viewSwipped(gesture:)))
        swipeRight.direction = UISwipeGestureRecognizer.Direction.right
        rootContainerView.addGestureRecognizer(swipeRight)
    }

    func selectCurrentOrdersTab() {
        self.selectedMenu = 0
        self.selectedTab(index: self.selectedMenu)
        ViewEmbedder.embed(
            withViewController: objCurrentOrders,
            parent: self,
            container: self.rootContainerView){ vc in
        }
    }

    @objc func viewSwipped(gesture: UIGestureRecognizer) {
        if let swipeGesture = gesture as? UISwipeGestureRecognizer {
            switch swipeGesture.direction {
            case UISwipeGestureRecognizer.Direction.right:
                print("Swiped right")
                let pageLimit = 0
                if self.selectedMenu == pageLimit {
                    return
                }
                changeMenu(isSwipeLeft: false)
                
            case UISwipeGestureRecognizer.Direction.left:
                print("Swiped left")
                let pageLimit = maxPage
                if self.selectedMenu == pageLimit {
                    return
                }
                changeMenu(isSwipeLeft: true)
            default:
                break
            }
        }
    }

    private func changeMenu(isSwipeLeft:Bool = true) {
        if isSwipeLeft {
            self.selectedMenu = min(maxPage, (self.selectedMenu + 1))
        } else {
            self.selectedMenu = max(0, (self.selectedMenu - 1))
        }
        
        print("index : \(self.selectedMenu)")
        switch selectedMenu {
        case 0:
            selectedMenu = 0
            animationOnView(vcWithIdentifier: "CurrentOrdersViewController", isSwipeLeft: isSwipeLeft)
        case 1:
            selectedMenu = 1
            animationOnView(vcWithIdentifier: "ScheduledOrdersViewController", isSwipeLeft: isSwipeLeft)
        default:
            break
        }
    }

    private func setTexts() {
        lblTitle.text = ObjKeymessages.kLABEL_MY_ORDERS
        btnCurrentOrders.setTitle(ObjKeymessages.kLABEL_CURRENT_ORDERS, for: .normal)
        btnScheduledOrders.setTitle(ObjKeymessages.kLABEL_SCHEDULED_ORDERS, for: .normal)

    }

    /*@IBAction func currentOrdersClicked(_ sender: Any) {
        btnCurrentOrders.setTitleColor(.black, for: .normal)
        btnScheduledOrders.setTitleColor(UIColor.AppTheme_LightGrayOrderColor_AAAAAA, for: .normal)

    }
    
    @IBAction func scheduledOrdersClicked(_ sender: Any) {
        btnScheduledOrders.setTitleColor(.black, for: .normal)
        btnCurrentOrders.setTitleColor(UIColor.AppTheme_LightGrayOrderColor_AAAAAA, for: .normal)

    }*/

    @IBAction func actionSelectTab(_ sender: UIButton) {
        if self.selectedMenu == sender.tag {
            return
        }
        self.selectedTab(index: sender.tag)
        self.selectMenu(selectedIndex: sender.tag)
    }

    private func selectedTab(index : Int) {
        switch index {
        case 0:
            var frm = lblTabUnderline.frame
            UIView.animate(withDuration: 0.3) {
                if UserDefaults.standard.getLanguage()! == UserAPI.VLanguage_userLanguage.en.rawValue {       // english
                    frm.origin.x = 0
                }
                else {
                    frm.origin.x = (self.view.frame.size.width / 2)
                }
                self.lblTabUnderline.frame = frm
            }

            self.changeSelection(btnSelected: self.btnCurrentOrders, btnDeSelected: self.btnScheduledOrders)
            break
        case 1:
            var frm = lblTabUnderline.frame
            UIView.animate(withDuration: 0.3) {
                if UserDefaults.standard.getLanguage()! == UserAPI.VLanguage_userLanguage.en.rawValue {       // english
                    frm.origin.x = (self.view.frame.size.width / 2)
                }
                else {
                    frm.origin.x = 0
                }
                self.lblTabUnderline.frame = frm
            }

            self.changeSelection(btnSelected: self.btnScheduledOrders, btnDeSelected: self.btnCurrentOrders)
            break
        default:
            print(index)
        }
    }

    func changeSelection(btnSelected: UIButton, btnDeSelected: UIButton) {
        btnSelected.setTitleColor(.black, for: .normal)
        btnDeSelected.setTitleColor(UIColor.AppTheme_LightGrayOrderColor_AAAAAA, for: .normal)
        
        /*btnDeSelected.layer.borderColor = UIColor.AppTheme_BlueColor.cgColor
        btnDeSelected.layer.borderWidth = 1
        btnDeSelected.layer.masksToBounds = true
        
        btnSelected.layer.borderColor = UIColor.clear.cgColor*/
        
    }

    private func selectMenu(selectedIndex : Int) {
        //set animation direction
        let isSwipeLeft = selectedIndex > self.selectedMenu
        
        self.selectedMenu = selectedIndex
        if self.selectedMenu == 0 {
            animationOnView(vcWithIdentifier: "CurrentOrdersViewController", isSwipeLeft: isSwipeLeft)
        } else if self.selectedMenu == 1 {
            animationOnView(vcWithIdentifier: "ScheduledOrdersViewController", isSwipeLeft: isSwipeLeft)
        }
    }

    private func animationOnView(vcWithIdentifier id:String, isSwipeLeft : Bool = false) {
        self.selectedTab(index: self.selectedMenu)
        switch id {
        case "CurrentOrdersViewController":
            ViewEmbedder.embed(
                withViewController: objCurrentOrders,
                parent: self,
                container: self.rootContainerView){ vc in
                // do things when embed complete
            }
        case "ScheduledOrdersViewController":
            ViewEmbedder.embed(
                withViewController: objScheduledOrders,
                parent: self,
                container: self.rootContainerView) { vc in
                // do things when embed complete
            }
        default:
            break
        }
        
        self.rootContainerView.swipeAnimation(direction: (isSwipeLeft ? .rightToLeft : .leftToRight), duration: 0.3)
    }

}

extension UIView {
    
    func swipeAnimation(direction: AnimationDirection, duration: TimeInterval = 0.5, completionDelegate: AnyObject? = nil) {
        // Create a CATransition object
        let leftToRightTransition = CATransition()
        
        // Set its callback delegate to the completionDelegate that was provided
        if let delegate: AnyObject = completionDelegate {
            leftToRightTransition.delegate = delegate as? CAAnimationDelegate
        }
        
        switch direction {
        case .topToBottom:
            leftToRightTransition.subtype =  CATransitionSubtype.fromTop
        case .bottomToTop:
            leftToRightTransition.subtype =  CATransitionSubtype.fromBottom
        case .rightToLeft:
            leftToRightTransition.subtype =  CATransitionSubtype.fromRight
        case .leftToRight:
            leftToRightTransition.subtype =  CATransitionSubtype.fromLeft
        }
        leftToRightTransition.type = CATransitionType.push
        leftToRightTransition.duration = duration
        leftToRightTransition.timingFunction = CAMediaTimingFunction(name: CAMediaTimingFunctionName.easeInEaseOut)
        leftToRightTransition.fillMode = CAMediaTimingFillMode.removed
        
        // Add the animation to the View's layer
        self.layer.add(leftToRightTransition, forKey: "leftToRightTransition")
    }
}

enum AnimationDirection: Int {
    case topToBottom = 0, bottomToTop, rightToLeft, leftToRight
}

// MARK: - GetInProgressListTotalDelegate
extension MyOrdersViewController : GetCurrentOrdersListTotalDelegate
{
    func passCurrentOrdersTotal(strTotal: Int) {
        if strTotal <= 0 {
            //btnCurrentOrders.setTitle(ObjKeymessages.kLABEL_CURRENT_ORDERS, for: .normal)
            lblCount.isHidden = true
            lblCount.text = ""
        }
        else {
            //btnCurrentOrders.setTitle("\(ObjKeymessages.kLABEL_CURRENT_ORDERS)(\(strTotal))", for: .normal)
            lblCount.isHidden = false
            lblCount.text = "\(strTotal)"
        }
    }
    
}
