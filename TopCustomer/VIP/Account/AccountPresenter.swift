
import UIKit

protocol AccountPresentationProtocol : AnyObject {
    func checkValidation(dictData:[String:Any]) -> Bool
    func apiCallEditProfile(dictData:[String:Any])
    func apiResponseEditProfile(response:UserResponse?,error:Error?)

    func apiCallForGetCountryList(flagNoNetwork: Bool)
    func apiResponseGetCountryList(response:CountryListResponse?,error:Error?, flagNoNetwork: Bool)

    func apiCallForDeleteAccount()
    func apiResponseDeleteAccount(response:CommonFields?,error:Error?)

}

class AccountPresenter: AccountPresentationProtocol {
    
    //MARK: - Objects & Variables -
    weak var viewControllerAccount: AccountProtocol?
    var interactorAccount: AccountInteractorProtocol?
    
    func checkValidation(dictData:[String:Any]) -> Bool {
        if setDataInString(dictData["vName"] as AnyObject).isBlank {
//            AppSingletonObj.showAlert(strMsg: ObjKeymessages.kALERT_ENTER_NAME)
            AppSingletonObj.showAlert(strMessage: ObjKeymessages.kALERT_ENTER_NAME)

            return false
        }
        else if setDataInString(dictData["vISDCode"] as AnyObject).isBlank {
//            AppSingletonObj.showAlert(strMsg: ObjKeymessages.kALERT_ISD_CODE)
            AppSingletonObj.showAlert(strMessage: ObjKeymessages.kALERT_ISD_CODE)

            return false
        }
        else if setDataInString(dictData["vMobileNumber"] as AnyObject).isBlank {
//            AppSingletonObj.showAlert(strMsg: ObjKeymessages.kALERT_MOBILE_NUMBER)
            AppSingletonObj.showAlert(strMessage: ObjKeymessages.kALERT_MOBILE_NUMBER)

            return false
        }
        else if setDataInString(dictData["vMobileNumber"] as AnyObject).count < MinMoNoLength || setDataInString(dictData["vMobileNumber"] as AnyObject).count > MaxMoNoLength {
            AppSingletonObj.showAlert(strMessage: ObjKeymessages.kALERT_MOBILE_NUMBER_TEN_DIGITS)

            return false
        }
        else if setDataInString(dictData["vEmailId"] as AnyObject).isBlank {
//            AppSingletonObj.showAlert(strMsg: ObjKeymessages.kALERT_VALIDATE_EMAIL)
            AppSingletonObj.showAlert(strMessage: ObjKeymessages.kALERT_VALIDATE_EMAIL)

            return false
        }
        else if setDataInString(dictData["vEmailId"] as AnyObject).isEmail == false {
//            AppSingletonObj.showAlert(strMsg: ObjKeymessages.kALERT_VALIDATE_EMAIL_VALID)
            AppSingletonObj.showAlert(strMessage: ObjKeymessages.kALERT_VALIDATE_EMAIL_VALID)

            return false
        }
        /*else if setDataInString(dictData["iDob"] as AnyObject).isBlank {
            AppSingletonObj.showAlert(strMsg: ObjKeymessages.kMSG_EMPTY_DOB)
            return false
        }*/
        return true
    }

    func apiCallEditProfile(dictData:[String:Any]) {
        self.interactorAccount?.apiCallEditProfile(dictData: dictData)
    }

    func apiResponseEditProfile(response:UserResponse?,error:Error?) {
        if let error = error  {
//            viewControllerLogin?.displayAlert(string: error.localizedDescription)
            AppSingletonObj.showAlert(strMsg: error.localizedDescription)
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            //            viewController?.displayAlert(strTitle: "", strMessage: KeyMessages.kInvalidateResponse)
            return
        }
        
        if code == APIINVALIDAUTHORIZATIONCODE401 {
//            AppSingletonObj.goToLoginScreen(message: response.responseMessage ?? "")
            AppSingletonObj.displaySessionExpiredAlert(strMsg: response.responseMessage ?? "")
            return
        }

        if code == APICODE400 {
//            viewControllerLogin?.displayAlert(string: response.responseMessage ?? "")
            AppSingletonObj.showAlert(strMessage: response.responseMessage ?? "")
            return
        }
        
        guard let model = response.responseData,code == APISUCCESSCODE200  else {
            return
        }
        
        if let dictData = model.dictionary {
            User.shared.setUserLoginStatus(isLogin: true)
            User.shared.setDataFromDictionary(dict: dictData)
        }

        self.viewControllerAccount?.reloadScreen(strMessage: response.responseMessage ?? "")
    }

    func apiCallForGetCountryList(flagNoNetwork: Bool) {
        interactorAccount?.apiCallForGetCountryList(flagNoNetwork: flagNoNetwork)
    }

    func apiResponseGetCountryList(response: CountryListResponse?, error: Error?, flagNoNetwork: Bool) {
//        if let vc = self.viewControllerAccount as? BaseViewController {
//            DispatchQueue.main.async {
//                vc.endRefresing()
//            }
//        }
        if let error = error  {
            AppSingletonObj.showAlert(strMsg: error.localizedDescription)
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            //            viewController?.displayAlert(strTitle: "", strMessage: KeyMessages.kInvalidateResponse)
            return
        }
        
        if code == APICODE400 {
            AppSingletonObj.showAlert(strMsg: response.responseMessage ?? "")
            return
        }
        
        guard let model = response.responseData,code == APISUCCESSCODE200  else {
            return
        }
        self.viewControllerAccount?.refreshCountryData(arrCountryData: model, flagNoNetwork: flagNoNetwork)
    }

    func apiCallForDeleteAccount() {
        self.interactorAccount?.apiCallForDeleteAccount()
    }

    func apiResponseDeleteAccount(response:CommonFields?,error:Error?) {
        if let error = error  {
            viewControllerAccount?.displayAlert(string: error.localizedDescription)
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            //            viewController?.displayAlert(strTitle: "", strMessage: KeyMessages.kInvalidateResponse)
            return
        }
        
        if code == APICODE400 {
            viewControllerAccount?.displayAlert(string: response.responseMessage ?? "")
            return
        }
        
        if code == APIINVALIDAUTHORIZATIONCODE401 {
//            AppSingletonObj.goToLoginScreen(message: response.responseMessage ?? "")
            AppSingletonObj.displaySessionExpiredAlert(strMsg: response.responseMessage ?? "")
            return
        }

        if code == APISUCCESSCODE200 {
            self.viewControllerAccount?.navigateToLogin(string: response.responseMessage ?? "")
        }
    }

}
