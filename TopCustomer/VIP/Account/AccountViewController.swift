
import UIKit
import Mixpanel

protocol AccountProtocol: AnyObject {
//    func displayAlert(strMsg: String)
    func reloadScreen(strMessage :  String)
//    func displaySessionExpiredAlert(strMsg: String)
    func refreshCountryData(arrCountryData : [CountryListResponseFields], flagNoNetwork: Bool)
    func displayAlert(string:String)
    func navigateToLogin(string:String)

}

class AccountViewController: BaseViewController, AccountProtocol {

    //MARK: - Properties -
    var presenterAccount: AccountPresentationProtocol?

    //MARK: - IBOutlets -
    @IBOutlet weak var txtName: CustomTextfieldWithFontStyle!
    @IBOutlet weak var btnCountryCode: UIButton!
    @IBOutlet weak var txtPhoneNumber: CustomTextfieldWithFontStyle!
    @IBOutlet weak var txtEmailAddress: CustomTextfieldWithFontStyle!
    @IBOutlet weak var txtDateOfBirth: CustomTextfieldWithFontStyle!
    @IBOutlet weak var btnFemale: UIButton!
    @IBOutlet weak var btnMale: UIButton!
    @IBOutlet weak var lblTitle: UILabel!
    @IBOutlet weak var lblNameTitle: UILabel!
    @IBOutlet weak var lblMobNoTitle: UILabel!
    @IBOutlet weak var lblEmailTitle: UILabel!
    @IBOutlet weak var lblDOBTitle: UILabel!
    @IBOutlet weak var lblGender: UILabel!
    @IBOutlet weak var btnUpdate: CustomRoundedButtton!
    @IBOutlet weak var btnDeleteMyAccount: UIButton!

    
    var arrCountry: [CountryListResponseFields] = []
    
    //MARK: - Object lifecycle
    var datePicker = UIDatePicker()
    
    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
        setup()
    }
     
    
    //MARK: - View Life Cycle -
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        self.setTexts()
        self.setData()
        self.getCountryList(flagNoNetwork: false)

    }
    
    private func getCountryList(flagNoNetwork: Bool) {
        if AppSingletonObj.isConnectedToNetworkForCheckoutScreen(){
            self.presenterAccount?.apiCallForGetCountryList(flagNoNetwork: flagNoNetwork)
        }
    }

    private func setData() {
        txtName.text = User.shared.vName
        btnCountryCode.setTitle(User.shared.vISDCode, for: .normal)
        txtPhoneNumber.text = User.shared.vMobileNumber
        txtEmailAddress.text = User.shared.vEmailId
        txtDateOfBirth.text = User.shared.iDob

//        1 = Male, 2 = Female, 3 = Other
        if User.shared.tiGender == UserAPI.TiGender_editProfile._1.rawValue {
            btnMale.isSelected = true
            btnFemale.isSelected = false
        }
        else if User.shared.tiGender == UserAPI.TiGender_editProfile._2.rawValue {
            btnMale.isSelected = false
            btnFemale.isSelected = true
        }
        else {
            btnMale.isSelected = false
            btnFemale.isSelected = false
        }

    }
    
    private func setTexts() {
        lblTitle.text = ObjKeymessages.kLABEL_ACCOUNT
        lblNameTitle.text = ObjKeymessages.kLABEL_NAME
        txtName.placeholder = ObjKeymessages.kLABEL_ACCOUNT_NAME_PLACEHOLDER
        lblMobNoTitle.text = ObjKeymessages.kLABEL_MOBILE_NO
        lblEmailTitle.text = ObjKeymessages.kLABEL_EMAIL_ADDRESS
        txtEmailAddress.placeholder = ObjKeymessages.kLABEL_EMAIL_PLACEHOLDER
        lblDOBTitle.text = ObjKeymessages.kLABEL_DATE_OF_BIRTH
        lblGender.text = ObjKeymessages.kLABEL_GENDER
        btnFemale.setTitle(ObjKeymessages.kLABEL_FEMALE, for: .normal)
        btnMale.setTitle(ObjKeymessages.kLABEL_MALE, for: .normal)
        btnUpdate.setTitle(ObjKeymessages.kLABEL_UPDATE, for: .normal)
        txtDateOfBirth.placeholder = ObjKeymessages.kLABEL_DATE_OF_BIRTH
        txtPhoneNumber.placeholder = ObjKeymessages.kLABEL_PHONE_NO

        if UserDefaults.standard.getLanguage()! == UserAPI.VLanguage_userLanguage.en.rawValue {       // english
            btnFemale.imageEdgeInsets = UIEdgeInsets(top: 0, left: -20, bottom: 0, right: 0)
            btnFemale.contentEdgeInsets = UIEdgeInsets(top: 0, left: 20, bottom: 0, right: 0)
            btnMale.imageEdgeInsets = UIEdgeInsets(top: 0, left: -20, bottom: 0, right: 0)
            btnMale.contentEdgeInsets = UIEdgeInsets(top: 0, left: 20, bottom: 0, right: 0)
        }
        else {   // arabic
            btnFemale.imageEdgeInsets = UIEdgeInsets(top: 0, left: 0, bottom: 0, right: -20)
            btnFemale.contentEdgeInsets = UIEdgeInsets(top: 0, left: 0, bottom: 0, right: 20)
            btnMale.imageEdgeInsets = UIEdgeInsets(top: 0, left: 0, bottom: 0, right: -20)
            btnMale.contentEdgeInsets = UIEdgeInsets(top: 0, left: 0, bottom: 0, right: 20)
        }
        
        let string = ObjKeymessages.kLABEL_DELETE_MY_ACCOUNT
        let range = (string as NSString).range(of: ObjKeymessages.kLABEL_DELETE_MY_ACCOUNT)
        let attributedString = NSMutableAttributedString(string: string)
        attributedString.addAttribute(NSAttributedString.Key.underlineStyle, value: NSNumber(value: 1), range: range)
        attributedString.addAttribute(NSAttributedString.Key.underlineColor, value: UIColor.darkGray, range: range)
        attributedString.addAttribute(NSAttributedString.Key.font, value: UIFont(name: Fonts.LoewNextArabicMedium, size: 17)!, range: range)
        attributedString.addAttribute(NSAttributedString.Key.foregroundColor, value: UIColor.darkGray, range: range)
        btnDeleteMyAccount.setAttributedTitle(attributedString, for: .normal)

    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
//        self.tabBarController?.tabBar.isHidden = true
    }
    
    //MARK: - Memory Management -
    override func didReceiveMemoryWarning() {
        super.didReceiveMemoryWarning()
        print("didReceiveMemoryWarning : \(self.classForCoder)")
    }
    
    //MARK: - DeInit -
    deinit {
        print("DeAllocated :  \(self.classForCoder)")
    }
    
    //MARK: Our Own Functions
    private func setupUI() {
        if #available(iOS 13.4, *) {
            self.datePicker.preferredDatePickerStyle = .wheels
        } else {
            // Fallback on earlier versions
        }
        self.datePicker.datePickerMode = .date
        self.datePicker.maximumDate = Date()
        self.txtDateOfBirth.inputView = self.datePicker
        
        self.datePicker.addTarget(self, action: #selector(dateValueChanged), for: .valueChanged)
        
//        self.btnFemale.setImage(UIImage(named: "icn_radioSelected"), for: .selected)
//        self.btnFemale.setImage(UIImage(named: "icn_radioUnselected"), for: .normal)
//        self.btnMale.setImage(UIImage(named: "icn_radioSelected"), for: .selected)
//        self.btnMale.setImage(UIImage(named: "icn_radioUnselected"), for: .normal)
        
//        self.btnDeleteMyAccount.setBtnImage()

        
    }
    
    func countryDictSetUp(countryDic: [String: Any]?){
        if countryDic == nil {
            self.btnCountryCode.setTitle(SACountryCode, for: .normal)
            if let regCode = Locale.current.regionCode {
                if let dict = CountryCodeJson.filter({$0["locale"] as? String == regCode}).first {
                    self.btnCountryCode.setTitle("+\(dict["code"] as! NSNumber)", for: .normal)
                }
            }
        }
        else {
            self.btnCountryCode.setTitle("+\(countryDic!["code"] as! NSNumber)", for: .normal)
        }
    }
    
    @objc func dateValueChanged(sender: UIDatePicker) {
        let dateFormatter = DateFormatter()
        dateFormatter.dateStyle = .medium
        dateFormatter.timeStyle = .none
        dateFormatter.dateFormat = "yyyy-MM-dd"
        self.txtDateOfBirth.text = dateFormatter.string(from: sender.date)
//        self.view.endEditing(true)
    }
    
    //MARK: - Actions
    @IBAction func actionBack(_ sender: UIButton) {
        self.popVC()
    }
    
    @IBAction func actionDeleteMyAccount(_ sender: UIButton) {
        /*let alert = UIAlertController(title: AppName, message: ObjKeymessages.kMSG_DELETE_MY_ACCOUNT, preferredStyle: .alert)
        let cancelButton = UIAlertAction(title: ObjKeymessages.kLABEL_NO, style: .default, handler: {(_ action: UIAlertAction) -> Void in
        })
        let logOutButton = UIAlertAction(title: ObjKeymessages.kLABEL_YES, style: .default, handler: {(_ action: UIAlertAction) -> Void in
            
            // Mixpanel
            Mixpanel.mainInstance().track(event: ScreenNames.DeleteMyAccount.rawValue)
            Mixpanel.mainInstance().reset()

            // call delete my account api
            if AppSingletonObj.isConnectedToNetwork(){
                self.presenterAccount?.apiCallForDeleteAccount()
            }
        })
        alert.addAction(cancelButton)
        alert.addAction(logOutButton)
        self.present(alert, animated: true) {() -> Void in }*/
        
        
        
        /*let vc = settingsStoryboard.instantiate(CustomPopupAlertViewController.self)
        vc.modalPresentationStyle = .overFullScreen
        vc.modalTransitionStyle = .crossDissolve
        vc.strDescription = ObjKeymessages.kMSG_DELETE_MY_ACCOUNT
        vc.strLeftButtonTitle = ObjKeymessages.kLABEL_NO
        vc.isNeedToSwitchButton = true
        vc.strRightButtonTitle = ObjKeymessages.kLABEL_YES
        vc.isCenterButton = false
        vc.completion = { (index) in
            if index == 0 {
            }else{
                // Mixpanel
                Mixpanel.mainInstance().track(event: ScreenNames.DeleteMyAccount.rawValue)
                Mixpanel.mainInstance().reset()

                // call delete my account api
                if AppSingletonObj.isConnectedToNetwork(){
                    self.presenterAccount?.apiCallForDeleteAccount()
                }
            }
        }
        self.presentVC(vc)*/
        
        AppSingleton.shared.showCustomPopUpWithYesNoButton(strButton1Title: ObjKeymessages.kLABEL_YES, strButton2Title: ObjKeymessages.kLABEL_NO, strMessage: ObjKeymessages.kMSG_DELETE_MY_ACCOUNT, showOnTopVC: false) { (isOk) in
            if isOk == true {
                // Mixpanel
                Mixpanel.mainInstance().track(event: ScreenNames.DeleteMyAccount.rawValue)
                Mixpanel.mainInstance().reset()

                // call delete my account api
                if AppSingletonObj.isConnectedToNetwork(){
                    self.presenterAccount?.apiCallForDeleteAccount()
                }
            }
        }

    }

    @IBAction func actionUpdate(_ sender: UIButton) {
        self.view.endEditing(true)
        if AppSingletonObj.isConnectedToNetwork(){
            var dictParam : [String:Any] = [:]
            dictParam["vName"] = txtName.text
            dictParam["vISDCode"] = btnCountryCode.titleLabel?.text
            dictParam["vMobileNumber"] = txtPhoneNumber.text
            dictParam["vEmailId"] = txtEmailAddress.text
            dictParam["iDob"] = txtDateOfBirth.text
            
    //        1 = Male, 2 = Female, 3 = Other
            dictParam["tiGender"] = btnMale.isSelected == true ? UserAPI.TiGender_editProfile._1.rawValue : (btnFemale.isSelected == true ? UserAPI.TiGender_editProfile._2.rawValue : UserAPI.TiGender_editProfile._3.rawValue)

            if self.presenterAccount?.checkValidation(dictData: dictParam) ?? false {
                self.presenterAccount?.apiCallEditProfile(dictData: dictParam)
            }
        }
    }
    
    @IBAction func btnFemale(_ sender: Any) {
        if !self.btnFemale.isSelected {
            self.btnFemale.isSelected = true
            self.btnMale.isSelected = false
        }
    }
    
    @IBAction func actionMale(_ sender: Any) {
        if !self.btnMale.isSelected {
            self.btnMale.isSelected = true
            self.btnFemale.isSelected = false
        }
    }
    
    @IBAction func actionCountryCode(_ sender: Any) {
        self.view.endEditing(true)
        
        if AppSingletonObj.isConnectedToNetworkForCheckoutScreen() {
            if arrCountry.count <= 0 {  // Do not have country list, call API
                self.getCountryList(flagNoNetwork: true)
            }
            else {  // Having country list, open country picker
                let countryView = CountrySelectView()     //.shared
        //        countryView.barTintColor = .red
                countryView.show()
                countryView.selectedCountryCallBack = {[weak self] (countryDic) -> Void in
                    self?.countryDictSetUp(countryDic: countryDic)
                }
            }
        }
    }
    
    /*func displayAlert(strMsg: String) {
        let alert = UIAlertController(title: AppName, message: strMsg, preferredStyle: .alert)
        let okButton = UIAlertAction(title: ObjKeymessages.kLABEL_OK, style: .default, handler: {(_ action: UIAlertAction) -> Void in
            self.popVC()
        })
        alert.addAction(okButton)
        self.present(alert, animated: true) {() -> Void in }
    }*/
    
    func reloadScreen(strMessage: String) {
//        showAlert(title: AppName, message: strMessage, buttonTitles: [ObjKeymessages.kLABEL_OK], highlightedButtonIndex: 0) { void in
        
        // Mixpanel
        MixpanelEvents.sharedInstance.initializeMixpanel()

        AppSingletonObj.showAlert(strMessage: strMessage)

            // check if user has changed mobile no
            if User.shared.vNewMobileNumber != "" {
                let storyboard = UIStoryboard(name: "Main", bundle: Bundle.main)
                let objVC = storyboard.instantiateViewController(withIdentifier: "OTPVerificationViewController") as! OTPVerificationViewController
                objVC.strCountryCode = User.shared.vNewISDCode ?? ""
                objVC.strMobNo = User.shared.vNewMobileNumber ?? ""
                objVC.strName = User.shared.vName ?? ""
                objVC.isFromEditProfile = true
                self.pushVC(objVC)
            }
            else {
                self.popVC()
            }
//        }
    }

    /*func displaySessionExpiredAlert(strMsg: String) {
        let alert = UIAlertController(title: AppName, message: strMsg, preferredStyle: .alert)
        let okButton = UIAlertAction(title: ObjKeymessages.kLABEL_OK, style: .default, handler: {(_ action: UIAlertAction) -> Void in
            AppDel?.restartApp()
            self.dismiss(animated: true, completion: nil)
        })
        alert.addAction(okButton)
        self.present(alert, animated: true) {() -> Void in }
    }*/

    func displayAlert(string: String) {
//        self.showAlert(title: AppName, message: string)
        AppSingleton.shared.showCustomPopUpWithOkButton(strButtonTitle: ObjKeymessages.kLABEL_OK, strMessage: string, showOnTopVC: false) { (isOk) in
        }
    }

    func navigateToLogin(string:String) {
        SocketIOManager.shared.disConnectSocket()
        AppDel?.restartApp()
        AppSingletonObj.showAlert(strMessage: string)
    }

    func refreshCountryData(arrCountryData : [CountryListResponseFields], flagNoNetwork: Bool) {
        
        arrCountry = arrCountryData

        var countryCodeJson = [] as [[String:Any]]
        var dictAnswer: [String: Any] = [:]

        for obj in arrCountryData {
            dictAnswer["en"] = obj.vCountryName
            dictAnswer["es"] = obj.vCountryName
            dictAnswer["zh"] = obj.vCountryName
            dictAnswer["locale"] = obj.vImage
            dictAnswer["code"] = obj.vDialingCode
           
            countryCodeJson.append(dictAnswer)
        }
        
        CountryCodeJson = countryCodeJson

        if flagNoNetwork == true {  // Open country picker
            if arrCountry.count > 0 {  // Do not have country list, call API
                let countryView = CountrySelectView()     //.shared
        //        countryView.barTintColor = .red
                countryView.show()
                countryView.selectedCountryCallBack = {[weak self] (countryDic) -> Void in
                    self?.countryDictSetUp(countryDic: countryDic)
                }
            }
        }
    }

}

//MARK: - Extensions

extension AccountViewController {
    //MARK: - VIP Setup -
    /// VIP Setup for AccountViewController
    private func setup() {
        let viewController = self
        let interactor = AccountInteractor()
        let presenter = AccountPresenter()
        
        //View Controller will communicate with only presenter
        viewController.presenterAccount = presenter
        
        //Presenter will communicate with Interector and Viewcontroller
        presenter.viewControllerAccount = viewController
        presenter.interactorAccount = interactor
        
        //Interactor will communucate with only presenter.
        interactor.presenterAccount = presenter
    }
}

extension AccountViewController : UITextFieldDelegate {
    func textField(_ textField: UITextField, shouldChangeCharactersIn range: NSRange, replacementString string: String) -> Bool {
        
        if textField == txtPhoneNumber {
            let newLength: Int = (textField.text?.length)! + string.length - range.length
            if newLength > MaxMoNoLength {
                return false
            }
            return true
        }
        else if textField == txtName {
            let newLength: Int = (textField.text?.length)! + string.length - range.length
            if newLength > MaxNameLength {
                return false
            }
            return true
        }
        else {
            return true
        }
    }

}

/*extension UIButton {
    func setBtnImage() {
        let attribute =  UIView.appearance().semanticContentAttribute
        let layoutDirection = UIView.userInterfaceLayoutDirection(for: attribute)
        if layoutDirection == .rightToLeft {
//            self.semanticContentAttribute = .forceLeftToRight
            self.contentHorizontalAlignment = .trailing
            self.titleEdgeInsets = UIEdgeInsets(top: 0, left: 0, bottom: 0, right: 15)
            self.imageEdgeInsets = UIEdgeInsets(top: 0, left: 0, bottom: 0, right: 10)

        }
        else{
//            self.semanticContentAttribute = .forceRightToLeft
            self.contentHorizontalAlignment = .leading
            self.titleEdgeInsets = UIEdgeInsets(top: 0, left: 15, bottom: 0, right: 0)
            self.imageEdgeInsets = UIEdgeInsets(top: 0, left: 10, bottom: 0, right: 0)

        }
    }

}*/
