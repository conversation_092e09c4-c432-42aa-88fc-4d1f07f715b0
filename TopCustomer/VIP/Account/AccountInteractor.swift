
import UIKit

protocol AccountInteractorProtocol : AnyObject {
    func apiCallEditProfile(dictData:[String:Any])
    func apiCallForGetCountryList(flagNoNetwork: Bool)
    func apiCallForDeleteAccount()

}

protocol AccountDataStore {
    //{ get set }
}

class AccountInteractor: AccountInteractorProtocol, AccountDataStore {

    //MARK: - Objects & Variables -
    weak var presenterAccount: AccountPresentationProtocol?
    
    func apiCallEditProfile(dictData:[String:Any]) {
        ActivityIndicator.shared.showCentralSpinner()
        
        let authorization = getAuthorizationText()

        var genderSelected = UserAPI.TiGender_editProfile._3
        if setDataInString(dictData["tiGender"] as AnyObject) == UserAPI.TiGender_editProfile._1.rawValue {
            genderSelected = UserAPI.TiGender_editProfile._1
        }
        else if setDataInString(dictData["tiGender"] as AnyObject) == UserAPI.TiGender_editProfile._2.rawValue {
            genderSelected = UserAPI.TiGender_editProfile._2
        }
        else if setDataInString(dictData["tiGender"] as AnyObject) == UserAPI.TiGender_editProfile._3.rawValue {
            genderSelected = UserAPI.TiGender_editProfile._3
        }

        if genderSelected == UserAPI.TiGender_editProfile._3 {
            UserAPI.editProfile(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, vName: setDataInString(dictData["vName"] as AnyObject), vISDCode: setDataInString(dictData["vISDCode"] as AnyObject), vMobileNumber: setDataInString(dictData["vMobileNumber"] as AnyObject), vEmailId: setDataInString(dictData["vEmailId"] as AnyObject), iDob: setDataInString(dictData["iDob"] as AnyObject), tiGender: nil) { data, error in
                
                ActivityIndicator.shared.hideCentralSpinner()
                self.presenterAccount?.apiResponseEditProfile(response: data, error: error)
            }
        }
        else {
            UserAPI.editProfile(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, vName: setDataInString(dictData["vName"] as AnyObject), vISDCode: setDataInString(dictData["vISDCode"] as AnyObject), vMobileNumber: setDataInString(dictData["vMobileNumber"] as AnyObject), vEmailId: setDataInString(dictData["vEmailId"] as AnyObject), iDob: setDataInString(dictData["iDob"] as AnyObject), tiGender: genderSelected) { data, error in
                
                ActivityIndicator.shared.hideCentralSpinner()
                self.presenterAccount?.apiResponseEditProfile(response: data, error: error)
            }
        }
        
    }

    func apiCallForGetCountryList(flagNoNetwork: Bool) {

        ActivityIndicator.shared.showCentralSpinner()
        
//        let authorization = getAuthorizationText()
        
        CountryAPI.countryListing(accept: AcceptParamForHeader, lang: CurrentAppLang) { data, error in
            ActivityIndicator.shared.hideCentralSpinner()
            self.presenterAccount?.apiResponseGetCountryList(response: data, error: error, flagNoNetwork: flagNoNetwork)

        }
        
    }

    func apiCallForDeleteAccount() {
        ActivityIndicator.shared.showCentralSpinner()
        
        let authorization = getAuthorizationText()

        UserAPI.deleteAccount(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang) { data, error in
            ActivityIndicator.shared.hideCentralSpinner()
            self.presenterAccount?.apiResponseDeleteAccount(response: data, error: error)

        }
        
    }

}
