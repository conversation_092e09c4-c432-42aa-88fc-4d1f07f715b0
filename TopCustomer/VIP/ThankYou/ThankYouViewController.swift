
import UIKit
import Lottie

protocol ThankYouProtocol: AnyObject {
   
}

class ThankYouViewController: UIViewController, ThankYouProtocol {

    // MARK: Objects & Variables
    var presenterThankYou: ThankYouPresentationProtocol?

    // MARK: IBOutlets
    
    @IBOutlet weak var lblThankYou: UILabel!
    @IBOutlet weak var lblMessage: UILabel!
    @IBOutlet weak var animationView: AnimationView!    
    
    
    var completionBlock: ((String) -> Void)?
    var completionPaymentCardBlock: ((String, Bool) -> Void)?
    var isAddNewCard = false
    var message: String?
    var timer: Timer?
    var runCount = 0
    // MARK: Object lifecycle
    /*
    override init(nibName nibNameOrNil: String?, bundle nibBundleOrNil: Bundle?) {
        super.init(nibName: nibNameOrNil, bundle: nibBundleOrNil)
        setup()
    }
    */
    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
        setup()
    }
    
    // MARK: Setup
    
    private func setup() {
        let viewController = self
        let interactor = ThankYouInteractor()
        let presenter = ThankYouPresenter()
        
        //View Controller will communicate with only presenter
        viewController.presenterThankYou = presenter
        
        //Presenter will communicate with Interector and Viewcontroller
        presenter.viewControllerThankYou = viewController
        presenter.interactorThankYou = interactor
        
        //Interactor will communucate with only presenter.
        interactor.presenterThankYou = presenter
    }
    
    
    // MARK: View lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        lblThankYou.text = ObjKeymessages.kLABEL_THANK_YOU
        lblMessage.text = ObjKeymessages.kMSG_ORDER_SENT

        animationView.loopMode = .playOnce
        animationView.play()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        if message != nil {
            self.lblMessage.text = self.message
        }
        runTimer()
    }
    
    func runTimer() {
        timer = Timer.scheduledTimer(timeInterval: 1.0, target: self,   selector: (#selector(updateTimer)), userInfo: nil, repeats: true)
    }
    
    @objc func updateTimer() {
        
        if runCount == 2 {
            self.closeVC()
        }
        runCount += 1
    }
    @IBAction func btnDimViewAction(_ sender: Any) {
        self.stopTimer()
        self.dismiss(animated: true, completion: nil)
    }

    @IBAction func actionClose(_ sender: Any) {
        self.closeVC()
    }
    
    private func closeVC() {
        self.stopTimer()
        self.dismiss(animated: true, completion: nil)
        if isAddNewCard == true {  // card is selected
            self.completionPaymentCardBlock?("Complete", true)
        }
        else {
            self.completionBlock?("Complete")
        }
    }
    
    func stopTimer() {
        self.timer?.invalidate()
        self.timer = nil
    }
}
