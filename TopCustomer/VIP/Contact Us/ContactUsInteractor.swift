
import UIKit

protocol ContactUsInteractorProtocol : AnyObject {
    func apiCallForContactUs(dictData:[String:Any])

}

protocol ContactUsDataStore {
    //{ get set }
}

class ContactUsInteractor: ContactUsInteractorProtocol, ContactUsDataStore {

    //MARK: - Objects & Variables -
    weak var presenterContactUs: ContactUsPresentationProtocol?
    
    func apiCallForContactUs(dictData:[String:Any]) {
        ActivityIndicator.shared.showCentralSpinner()
        
        let authorization = getAuthorizationText()

        ContactUsAPI.contactHelpCreate(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, vName: setDataInString(dictData["vName"] as AnyObject), vEmailId: setDataInString(dictData["vEmailId"] as AnyObject), txDescription: setDataInString(dictData["txDescription"] as AnyObject)) { data, error in
            
            ActivityIndicator.shared.hideCentralSpinner()
            self.presenterContactUs?.apiResponseContactUs(response: data, error: error)
        }
        
    }

}
