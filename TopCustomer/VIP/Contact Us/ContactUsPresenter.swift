
import UIKit

protocol ContactUsPresentationProtocol : AnyObject {
    func checkValidation(dictData:[String:Any]) -> Bool
    func apiCallContactUs(dictData:[String:Any])
    func apiResponseContactUs(response:CommonFields?,error:Error?)

}

class ContactUsPresenter: ContactUsPresentationProtocol {
    
    //MARK: - Objects & Variables -
    weak var viewControllerContactUs: ContactUsProtocol?
    var interactorContactUs: ContactUsInteractorProtocol?
    
    func checkValidation(dictData:[String:Any]) -> Bool {
        if setDataInString(dictData["vName"] as AnyObject).isBlank {
//            AppSingletonObj.showAlert(strMsg: ObjKeymessages.kALERT_ENTER_NAME)
            AppSingletonObj.showAlert(strMessage: ObjKeymessages.kALERT_ENTER_NAME)

            return false
        }
        else if setDataInString(dictData["vEmailId"] as AnyObject).isBlank {
//            AppSingletonObj.showAlert(strMsg: ObjKeymessages.kALERT_VALIDATE_EMAIL)
            AppSingletonObj.showAlert(strMessage: ObjKeymessages.kALERT_VALIDATE_EMAIL)

            return false
        }
        else if setDataInString(dictData["vEmailId"] as AnyObject).isEmail == false {
//            AppSingletonObj.showAlert(strMsg: ObjKeymessages.kALERT_VALIDATE_EMAIL_VALID)
            AppSingletonObj.showAlert(strMessage: ObjKeymessages.kALERT_VALIDATE_EMAIL_VALID)

            return false
        }
        else if setDataInString(dictData["txDescription"] as AnyObject).isBlank {
//            AppSingletonObj.showAlert(strMsg: ObjKeymessages.kALERT_DESC_BLANK)
            AppSingletonObj.showAlert(strMessage: ObjKeymessages.kALERT_DESC_BLANK)

            return false
        }
        return true
    }

    func apiCallContactUs(dictData:[String:Any]) {
        self.interactorContactUs?.apiCallForContactUs(dictData: dictData)
    }

    func apiResponseContactUs(response:CommonFields?,error:Error?) {
        if let error = error  {
//            viewControllerLogin?.displayAlert(string: error.localizedDescription)
            AppSingletonObj.showAlert(strMsg: error.localizedDescription)
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            //            viewController?.displayAlert(strTitle: "", strMessage: KeyMessages.kInvalidateResponse)
            return
        }
        
        if code == APIINVALIDAUTHORIZATIONCODE401 {
//            AppSingletonObj.goToLoginScreen(message: response.responseMessage ?? "")
            AppSingletonObj.displaySessionExpiredAlert(strMsg: response.responseMessage ?? "")
            return
        }

        if code == APICODE400 {
//            viewControllerLogin?.displayAlert(string: response.responseMessage ?? "")
            AppSingletonObj.showAlert(strMsg: response.responseMessage ?? "")
            return
        }
        
//        guard let model = response.responseData,code == APISUCCESSCODE200  else {
//            return
//        }
        self.viewControllerContactUs?.displayAlert(strMsg: response.responseMessage ?? "")
    }

}
