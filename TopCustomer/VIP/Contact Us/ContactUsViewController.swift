
import UIKit
import IQKeyboardManagerSwift

protocol ContactUsProtocol: AnyObject {
    func displayAlert(strMsg: String)
//    func displaySessionExpiredAlert(strMsg: String)

}

class ContactUsViewController: BaseViewController, ContactUsProtocol {

    //MARK: - Properties -
    var presenterContactUs: ContactUsPresentationProtocol?

    //MARK: - IBOutlets -
    @IBOutlet weak var txtName: CustomTextfieldWithFontStyle!
    @IBOutlet weak var txtEmail: CustomTextfieldWithFontStyle!
    @IBOutlet weak var txtMessage: IQTextView!
    @IBOutlet weak var lblTitle: UILabel!
    @IBOutlet weak var lblContactInfo: UILabel!
    @IBOutlet weak var lblHowCan: UILabel!
    @IBOutlet weak var btnSend: CustomRoundedButtton!
    @IBOutlet weak var btnFollowUs: UIButton!
    @IBOutlet weak var lblVersion: UILabel!

    
    //MARK: - Object lifecycle
    
    
    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
        setup()
    }
     
    
    //MARK: - View Life Cycle -
    override func viewDidLoad() {
        super.viewDidLoad()
        print("Allocated : \(self.classForCoder)")
        initialSetUp()
        self.setTexts()
        
        txtName.text = User.shared.vName
        txtEmail.text = User.shared.vEmailId

        self.setVersionText()
    }
    
    private func setVersionText() {
        #if DEBUG
        lblVersion.text = "\(ObjKeymessages.kLABEL_VERSION): \(Bundle.main.releaseVersionNumber ?? "") (\(Bundle.main.buildVersionNumber ?? "").QA)"

        #elseif STAGING
        lblVersion.text = "\(ObjKeymessages.kLABEL_VERSION): \(Bundle.main.releaseVersionNumber ?? "") (\(Bundle.main.buildVersionNumber ?? "").STAGING)"

        #elseif DEVRELEASE
        lblVersion.text = "\(ObjKeymessages.kLABEL_VERSION): \(Bundle.main.releaseVersionNumber ?? "") (\(Bundle.main.buildVersionNumber ?? "").LIVE)"

        #elseif RELEASESTAGING
        lblVersion.text = "\(ObjKeymessages.kLABEL_VERSION): \(Bundle.main.releaseVersionNumber ?? "") (\(Bundle.main.buildVersionNumber ?? "").STAGING-RELEASE)"

        #else
        lblVersion.text = "\(ObjKeymessages.kLABEL_VERSION): \(Bundle.main.releaseVersionNumber ?? "") (\(Bundle.main.buildVersionNumber ?? ""))"

        #endif
    }
    private func setTexts() {
        lblTitle.text = ObjKeymessages.kLABEL_CONTACT_US
        lblContactInfo.text = ObjKeymessages.kLABEL_CONTACT_INFO
        txtName.placeholder = ObjKeymessages.kLABEL_NAME
        txtEmail.placeholder = ObjKeymessages.kLABEL_EMAIL_ADDRESS
        lblHowCan.text = ObjKeymessages.kLABEL_HOW_CAN
        txtMessage.placeholder = ObjKeymessages.kLABEL_WRITE_YOUR_MSG
        btnSend.setTitle(ObjKeymessages.kLABEL_SEND, for: .normal)
        btnFollowUs.setTitle(ObjKeymessages.kLABEL_CONTACT_US, for: .normal)
        
        if UserDefaults.standard.getLanguage()! == UserAPI.VLanguage_userLanguage.en.rawValue {       // english
        }
        else {   // arabic
            if txtMessage.textAlignment == NSTextAlignment.center {
            }
            else if txtMessage.textAlignment == NSTextAlignment.right || txtMessage.textAlignment == NSTextAlignment.left{
                txtMessage.textAlignment = txtMessage.textAlignment == NSTextAlignment.right ? NSTextAlignment.left : NSTextAlignment.right
            }
        }

    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
//        self.tabBarController?.tabBar.isHidden = true
    }
    
    //MARK: - Memory Management -
    override func didReceiveMemoryWarning() {
        super.didReceiveMemoryWarning()
        print("didReceiveMemoryWarning : \(self.classForCoder)")
    }
    
    //MARK: - DeInit -
    deinit {
        print("DeAllocated :  \(self.classForCoder)")
    }
    
    //MARK: Our Own Functions
    
    //MARK: - Actions
    @IBAction func actionBack(_ sender: Any) {
        self.popVC()
    }
    
    @IBAction func actionSend(_ sender: Any) {
        self.view.endEditing(true)
        if AppSingletonObj.isConnectedToNetwork(){
            var dictParam : [String:Any] = [:]
            dictParam["vName"] = txtName.text
            dictParam["vEmailId"] = txtEmail.text
            dictParam["txDescription"] = txtMessage.text

            if self.presenterContactUs?.checkValidation(dictData: dictParam) ?? false {
                self.presenterContactUs?.apiCallContactUs(dictData: dictParam)
            }
        }
    }
    
    @IBAction func actionInstagram(_ sender: Any) {
        self.gotoSocialPages(strUrl: InstagramURL)
    }
    
    @IBAction func actionTwitter(_ sender: Any) {
        self.gotoSocialPages(strUrl: TwitterURL)
    }
    
    @IBAction func actionPhone(_ sender: Any) {
        let strPhoneNo = ContactUSNumber //objOrderDetails?.vDriverMobile
        let strTrimmedPhoneNo = strPhoneNo.replacingOccurrences(of: " ", with: "")
        if let url = URL(string: "tel://\(strTrimmedPhoneNo)"), UIApplication.shared.canOpenURL(url) {
            UIApplication.shared.open(url)
        }
    }

    private func gotoSocialPages(strUrl: String) {
        
        guard let socialUrl = URL(string: strUrl) else {
            return
        }
        
        if UIApplication.shared.canOpenURL(socialUrl) {
            if #available(iOS 10.0, *) {
                UIApplication.shared.open(socialUrl, options: [:], completionHandler: nil)
            } else {
                UIApplication.shared.openURL(socialUrl)
                // Fallback on earlier versions
            }
        }
    }

    func displayAlert(strMsg: String) {
        /*let alert = UIAlertController(title: AppName, message: strMsg, preferredStyle: .alert)
        let okButton = UIAlertAction(title: ObjKeymessages.kLABEL_OK, style: .default, handler: {(_ action: UIAlertAction) -> Void in
            self.popVC()
        })
        alert.addAction(okButton)
        self.present(alert, animated: true) {() -> Void in }*/
        AppSingletonObj.showAlert(strMessage: strMsg)
        self.popVC()
    }

    /*func displaySessionExpiredAlert(strMsg: String) {
        let alert = UIAlertController(title: AppName, message: strMsg, preferredStyle: .alert)
        let okButton = UIAlertAction(title: ObjKeymessages.kLABEL_OK, style: .default, handler: {(_ action: UIAlertAction) -> Void in
            AppDel?.restartApp()
            self.dismiss(animated: true, completion: nil)
        })
        alert.addAction(okButton)
        self.present(alert, animated: true) {() -> Void in }
    }*/

    //MARK: - Protocol Functions -
    private func initialSetUp() { }
    
}

//MARK: - Extensions

extension ContactUsViewController {
    //MARK: - VIP Setup -
    /// VIP Setup for ContactUsViewController
    private func setup() {
        let viewController = self
        let interactor = ContactUsInteractor()
        let presenter = ContactUsPresenter()
        
        //View Controller will communicate with only presenter
        viewController.presenterContactUs = presenter
        
        //Presenter will communicate with Interector and Viewcontroller
        presenter.viewControllerContactUs = viewController
        presenter.interactorContactUs = interactor
        
        //Interactor will communucate with only presenter.
        interactor.presenterContactUs = presenter
    }
}

extension ContactUsViewController : UITextFieldDelegate {
    
    func textField(_ textField: UITextField, shouldChangeCharactersIn range: NSRange, replacementString string: String) -> Bool {
        if textField == txtName {
            let newLength: Int = (textField.text?.length)! + string.length - range.length
            if newLength > MaxNameLength {
                return false
            }
            return true
        }
        else {
            return true
        }
    }
    
}
