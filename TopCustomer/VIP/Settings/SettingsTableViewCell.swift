
import UIKit

class SettingsTableViewCell: UITableViewCell {

    @IBOutlet weak var profileView: UIView!
    @IBOutlet weak var lblSettingName: UILabel!
    @IBOutlet weak var imgSetting: UIImageView!
    @IBOutlet weak var btnDiaclosure: UIButton!
    @IBOutlet weak var btnArrow: UIButton!
//    @IBOutlet weak var lblSeperator: UILabel!    
    
    
    override func awakeFromNib() {
        super.awakeFromNib()
        // Initialization code
//        profileView.layer.borderColor = UIColor.AppThemeView_LightGreyBorderColor.cgColor
//        profileView.layer.borderWidth = 1.0
//        profileView.layer.cornerRadius = 10
    }

    override func setSelected(_ selected: <PERSON><PERSON>, animated: Bool) {
        super.setSelected(selected, animated: animated)

        // Configure the view for the selected state
    }
   
}
