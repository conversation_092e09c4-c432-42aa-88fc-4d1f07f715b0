
import UIKit

protocol HistoryOrdersInteractorProtocol : AnyObject {
    func apiCallForGetHistoryOrders(dictData:[String:Any], flagLoader: Bool)

}

protocol HistoryOrdersDataStore {
    //{ get set }
}

class HistoryOrdersInteractor: HistoryOrdersInteractorProtocol, HistoryOrdersDataStore {

    //MARK: - Objects & Variables -
    weak var presenterHistoryOrders: HistoryOrdersPresentationProtocol?
    
    //MARK: - API Calls
    func apiCallForGetHistoryOrders(dictData:[String:Any], flagLoader: Bool) {
        
        if flagLoader == true {
            ActivityIndicator.shared.showCentralSpinner()
        }

        let authorization = getAuthorizationText()
        
        OrderAPI.getOrderList(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, iOrderType: OrderAPI.IOrderType_getOrderList._3, offset: Int(setDataInString(dictData["offset"] as AnyObject)) ?? 0) { data, error in
                ActivityIndicator.shared.hideCentralSpinner()
                self.presenterHistoryOrders?.apiResponseGetHistoryOrders(response: data, error: error)

        }
        
    }

}
