
import UIKit

protocol HistoryOrdersPresentationProtocol : AnyObject {
    func navigateToHistoryOrderDetails()
    
    func apiCallForGetHistoryOrders(dictData:[String:Any], flagLoader: Bool)
    func apiResponseGetHistoryOrders(response:OrderListResponse?,error:Error?)

}

class HistoryOrdersPresenter: HistoryOrdersPresentationProtocol {
    
    //MARK: - Objects & Variables -
    weak var viewControllerHistoryOrders: HistoryOrdersProtocol?
    var interactorHistoryOrders: HistoryOrdersInteractorProtocol?
    
    //MARK: - API Request
    
    
    //MARK: - API Response
    
    
    //MARK: - View Calls
    func navigateToHistoryOrderDetails() {
//        let vc = settingsStoryboard.instantiate(HistoryOrderDetailsViewController.self)
//        (self.viewControllerHistoryOrders as! UIViewController).presentVC(vc)
        
        let vc = settingsStoryboard.instantiate(HistoryOrderDetailsViewController.self)
        vc.modalPresentationStyle = .overFullScreen
        vc.modalTransitionStyle = .crossDissolve
        (self.viewControllerHistoryOrders as! UIViewController).presentVC(vc)
//        self.presentVC(vc)
    }
    
    func apiCallForGetHistoryOrders(dictData:[String:Any], flagLoader: Bool) {
        interactorHistoryOrders?.apiCallForGetHistoryOrders(dictData: dictData, flagLoader: flagLoader)
    }

    func apiResponseGetHistoryOrders(response: OrderListResponse?, error: Error?) {
//        if let vc = self.viewControllerHistoryOrders as? BaseViewController {
//            DispatchQueue.main.async {
//                vc.endRefresing()
//            }
//        }
        if let error = error  {
            viewControllerHistoryOrders?.displayAlert(string: error.localizedDescription)
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            //            viewController?.displayAlert(strTitle: "", strMessage: KeyMessages.kInvalidateResponse)
            return
        }
        
        if code == APIINVALIDAUTHORIZATIONCODE401 {
//            AppSingletonObj.goToLoginScreen(message: response.responseMessage ?? "")
            AppSingletonObj.displaySessionExpiredAlert(strMsg: response.responseMessage ?? "")
            return
        }

        if code == APICODE400 {
            viewControllerHistoryOrders?.displayAlert(string: response.responseMessage ?? "")
            return
        }
        
        guard let model = response.responseData,code == APISUCCESSCODE200  else {
            return
        }
        self.viewControllerHistoryOrders?.loadOrderList(model: model)
    }

}
