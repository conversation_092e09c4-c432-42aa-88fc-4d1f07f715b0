
import UIKit

protocol HistoryOrdersProtocol: AnyObject {
    func displayAlert(string:String)
//    func displaySessionExpiredAlert(strMsg: String)
    func loadOrderList(model : OrderListResponseFields)

}

class HistoryOrdersViewController: BaseViewController {

    //MARK: - Properties -
    var presenterHistoryOrders: HistoryOrdersPresentationProtocol?

    //MARK: - IBOutlets -
    @IBOutlet weak var tblHistoryOrders: UITableView!
    @IBOutlet weak var lblTitle: UILabel!
    @IBOutlet weak var viewNoData: UIView!
    @IBOutlet weak var lblNoOrderFound: UILabel!
    @IBOutlet weak var lblLooksLike: UILabel!
    @IBOutlet weak var btnGoShopping: CustomRoundedButtton!

    
    var arrHistoryOrders: [OrderResponseFields] = []

    private var totalCount: Int = 0
    private var offset: Int = 1
    var strFromWhichScreen = ""
    var orderId = 0
    
    //MARK: - Object lifecycle
    
    
    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
        setup()
    }
     
    
    //MARK: - View Life Cycle -
    override func viewDidLoad() {
        super.viewDidLoad()
        self.setupRefreshControlInTableView(tableView: tblHistoryOrders)

        initialSetUp()
        self.setTexts()
        self.getHistoryOrders(flagLoader: true)

        if self.strFromWhichScreen == "OrderCancelledByAdmin" {
            self.openDetailScreen()
        }
    }
    
    private func openDetailScreen() {
        let vc = settingsStoryboard.instantiate(HistoryOrderDetailsViewController.self)
        vc.modalPresentationStyle = .overFullScreen
        vc.modalTransitionStyle = .crossDissolve
        
        AppSingletonObj.showAlert(strMessage: ObjKeymessages.kALERT_MOBILE_NUMBER)
        
        vc.dismissScreen = { [weak self] () in
            DispatchQueue.main.async {
                self?.popVC()
                self?.tabBarController?.selectedIndex = 3
            }
        }
        vc.orderId = self.orderId
        self.presentVC(vc)
    }
    
    private func getHistoryOrders(flagLoader: Bool) {
        var dictParam : [String:Any] = [:]
        dictParam["offset"] = offset

        if AppSingletonObj.isConnectedToNetwork(){
            self.presenterHistoryOrders?.apiCallForGetHistoryOrders(dictData: dictParam, flagLoader: flagLoader)
        }
    }

    private func setTexts() {
        lblTitle.text = ObjKeymessages.kLABEL_HISTORY_ORDERS
        lblNoOrderFound.text = ObjKeymessages.kLABEL_NO_ORDER_FOUND
        lblLooksLike.text = ObjKeymessages.kLABEL_NO_ORDER_MADE
        btnGoShopping.setTitle(ObjKeymessages.kLABEL_GO_SHOPPING, for: .normal)
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        self.navigationController?.navigationBar.isHidden = true
    }
    
    //MARK: - Memory Management -
    override func didReceiveMemoryWarning() {
        super.didReceiveMemoryWarning()
        print("didReceiveMemoryWarning : \(self.classForCoder)")
    }
    
    //MARK: - DeInit -
    deinit {
        print("DeAllocated :  \(self.classForCoder)")
    }
    
    //MARK: Our Own Functions
    private func initialSetUp() {
        self.tblHistoryOrders.registerCell(cell: HistoryOrdersTableViewCell.self)
        self.tblHistoryOrders.delegate = self
        self.tblHistoryOrders.dataSource = self
    }
    
    //MARK: - Actions
    @IBAction func actionBack(_ sender: UIButton) {
        self.popVC()
    }
    
    @IBAction func btnGoShoppingAction(_ sender: Any) {
        self.tabBarController?.selectedIndex = 0
        self.popVC()
    }

    override func didPullToRefresh(_ sender: UIRefreshControl) {
        if AppSingletonObj.isConnectedToNetwork(){
            self.tblHistoryOrders.refreshControl?.beginRefreshing()
            offset = 1
            self.getHistoryOrders(flagLoader: false)
        }
        else {
            ez.runThisAfterDelay(seconds: 1.0) {
                self.endRefresing()
            }
        }
    }

    
}

//MARK: - Extensions
extension HistoryOrdersViewController: UITableViewDelegate, UITableViewDataSource {
    func numberOfSections(in tableView: UITableView) -> Int {
        return 1
    }

    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return arrHistoryOrders.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        guard let cell = tblHistoryOrders.dequeueReusableCell(withIdentifier: CellIdentifiers.kHISTORY_ORDERS_CELL) as? HistoryOrdersTableViewCell else {
            return UITableViewCell()
        }

        cell.lblOrderNo.text = arrHistoryOrders[indexPath.row].vOrderNumber
//        cell.lblDate.text = arrHistoryOrders[indexPath.row].tsOrderedAt
        // convert date to desired format
        let strDate = arrHistoryOrders[indexPath.row].tsOrderedAt
        let strFormattedDate = strDate?.utcToLocal(dateStr: strDate ?? "")
        cell.lblDate.text = strFormattedDate

        // 6 --> Order Delivered
        // 7 -->  Order Cancelled
        if arrHistoryOrders[indexPath.row].tiOrderStatus == OrderStatus.OrderCancelled.rawValue {
            cell.lblStatus.text = ObjKeymessages.kLABEL_CANCELLED
            cell.lblStatus.textColor = UIColor.AppTheme_SelectedTabColor_F4BA45
//            cell.btnRateNow.isHidden = true
//            cell.btnRating.isHidden = true
        }
        else {  // Delivered
            cell.lblStatus.text = ObjKeymessages.kLABEL_DELIVERED
            cell.lblStatus.textColor = UIColor.AppTheme_DiscountGreenColor_05B13E
            
            // Ratings
            /*if arrHistoryOrders[indexPath.row].tiIsRated == 0 {  // Ratings remaining
                cell.btnRateNow.isHidden = false
                cell.btnRating.isHidden = true
            }
            else {  // Ratings given
                cell.btnRateNow.isHidden = true
                cell.btnRating.isHidden = false
                
                cell.btnRating.setTitle(arrHistoryOrders[indexPath.row].dbRating, for: .normal)
            }*/
        }
        
        /*cell.btnRateNow.tag = indexPath.row
        cell.btnRateNow.addTarget(self, action: #selector(self.rateNowAction(sender:)), for: .touchUpInside)*/
                
        return cell
    }
    
    /*@objc func rateNowAction(sender:UIButton){
        let vc = settingsStoryboard.instantiate(RatingViewController.self)
        vc.modalPresentationStyle = .overFullScreen
        vc.modalTransitionStyle = .crossDissolve
        vc.delegate = self
        vc.orderId = arrHistoryOrders[sender.tag].iOrderId ?? 0
        self.presentVC(vc)
    }*/

    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
//        self.presenterHistoryOrders?.navigateToHistoryOrderDetails()
        
        let vc = settingsStoryboard.instantiate(HistoryOrderDetailsViewController.self)
        vc.modalPresentationStyle = .overFullScreen
        vc.modalTransitionStyle = .crossDissolve
        
        vc.dismissScreen = { [weak self] () in
            
            DispatchQueue.main.async {
                self?.popVC()
                self?.tabBarController?.selectedIndex = 3
            }
            
        }
        vc.orderId = arrHistoryOrders[indexPath.row].iOrderId ?? 0
        self.presentVC(vc)

    }
    
    func tableView(_ tableView: UITableView, willDisplay cell: UITableViewCell, forRowAt indexPath: IndexPath) {
        if indexPath.row == (self.arrHistoryOrders.count ) - 1  {
            if (self.arrHistoryOrders.count ) < totalCount {
                offset = offset + 1
                
                var dictParam : [String:Any] = [:]
                dictParam["offset"] = offset

                if AppSingletonObj.isConnectedToNetwork(){
                    self.presenterHistoryOrders?.apiCallForGetHistoryOrders(dictData: dictParam, flagLoader: false)
                }
            }
        }
    }

}


extension HistoryOrdersViewController {
    //MARK: - VIP Setup -
    /// VIP Setup for HistoryOrdersViewController
    private func setup() {
        let viewController = self
        let interactor = HistoryOrdersInteractor()
        let presenter = HistoryOrdersPresenter()
        
        //View Controller will communicate with only presenter
        viewController.presenterHistoryOrders = presenter
        
        //Presenter will communicate with Interector and Viewcontroller
        presenter.viewControllerHistoryOrders = viewController
        presenter.interactorHistoryOrders = interactor
        
        //Interactor will communucate with only presenter.
        interactor.presenterHistoryOrders = presenter
    }
}

extension HistoryOrdersViewController: HistoryOrdersProtocol {
    func displayAlert(string: String) {
//        self.showAlert(title: AppName, message: string)
        AppSingleton.shared.showCustomPopUpWithOkButton(strButtonTitle: ObjKeymessages.kLABEL_OK, strMessage: string, showOnTopVC: false) { (isOk) in
        }
    }

    /*func displaySessionExpiredAlert(strMsg: String) {
        let alert = UIAlertController(title: AppName, message: strMsg, preferredStyle: .alert)
        let okButton = UIAlertAction(title: ObjKeymessages.kLABEL_OK, style: .default, handler: {(_ action: UIAlertAction) -> Void in
            AppDel?.restartApp()
            self.dismiss(animated: true, completion: nil)
        })
        alert.addAction(okButton)
        self.present(alert, animated: true) {() -> Void in }
    }*/

    func loadOrderList(model : OrderListResponseFields) {
        self.endRefresing()

        self.totalCount = model.totalRecord ?? 0

        // emtpy the array if it is initial call
        if self.offset == 1 {
            self.arrHistoryOrders.removeAll()
        }
        self.arrHistoryOrders.append(contentsOf: model.orders ?? [])

        if self.arrHistoryOrders.count <= 0 {  // show no data view
            viewNoData.isHidden = false
            tblHistoryOrders.isHidden = true
        }
        else {
            viewNoData.isHidden = true
            tblHistoryOrders.isHidden = false
        }
        
        tblHistoryOrders.reloadData()

    }

}

extension HistoryOrdersViewController: ReloadHistoryOrderListDelegate {
    func reloadScreen() {
        offset = 1
        self.getHistoryOrders(flagLoader: true)
    }
    
}
