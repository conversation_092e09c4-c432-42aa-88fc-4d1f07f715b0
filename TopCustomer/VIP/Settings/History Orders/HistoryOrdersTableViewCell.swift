
import UIKit

class HistoryOrdersTableViewCell: UITableViewCell {

    //MARK: - Outlets
    
    @IBOutlet weak var lblOrderNo: UILabel!
    @IBOutlet weak var lblDate: UILabel!
    @IBOutlet weak var lblStatus: UILabel!
    @IBOutlet weak var lblOrderNoTitle: UILabel!
    @IBOutlet weak var lblDateTitle: UILabel!
    @IBOutlet weak var imgArrow: UIImageView!
    @IBOutlet weak var btnRateNow: UIButton!
    @IBOutlet weak var btnRating: UIButton!
    
    override func awakeFromNib() {
        super.awakeFromNib()
        // Initialization code
        
        lblOrderNoTitle.text = "\(ObjKeymessages.kLABEL_ORDER_NO):"
        lblDateTitle.text = "\(ObjKeymessages.kLABEL_DATE_ONLY):"
//        lblStatus.text = ObjKeymessages.kLABEL_DELIVERED

//        self.btnRateNow.setTitle(ObjKeymessages.kLABEL_RATE_NOW, for: .normal)
        
//        self.btnRating.makeButtonRightToLeftIfRequiredWhileCenterAndOppsiteDirection()
        
        let strLangCode = UserDefaults.standard.getLanguage()!
        if strLangCode == UserAPI.VLanguage_userLanguage.en.rawValue {
        }
        else {
            imgArrow.image = imgArrow.image?.withHorizontallyFlippedOrientation()
        }
    }

    /*override func layoutSubviews() {
        super.layoutSubviews()
        
        let strLangCode = UserDefaults.standard.getLanguage()!
        if strLangCode == UserAPI.VLanguage_userLanguage.en.rawValue {
        }
        else {
            imgArrow.image = imgArrow.image?.withHorizontallyFlippedOrientation()
        }
    }*/

    override func setSelected(_ selected: Bool, animated: Bool) {
        super.setSelected(selected, animated: animated)

        // Configure the view for the selected state
    }
    
}
