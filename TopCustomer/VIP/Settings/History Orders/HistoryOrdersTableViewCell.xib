<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="21507" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21505"/>
        <capability name="Named colors" minToolsVersion="9.0"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="LoewNextArabic-Bold.ttf">
            <string>LoewNextArabic-Bold</string>
        </array>
        <array key="LoewNextArabic-ExtraBold.ttf">
            <string>LoewNextArabic-ExtraBold</string>
        </array>
        <array key="LoewNextArabic-Medium.ttf">
            <string>LoewNextArabic-Medium</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" reuseIdentifier="HistoryOrdersTableViewCell" rowHeight="153" id="KGk-i7-Jjw" customClass="HistoryOrdersTableViewCell" customModule="TopCustomer" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="320" height="153"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="320" height="153"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="y9p-w6-d7r" customClass="CustomViewForShadow" customModule="TopCustomer" customModuleProvider="target">
                        <rect key="frame" x="0.0" y="0.0" width="320" height="153"/>
                        <subviews>
                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="20" translatesAutoresizingMaskIntoConstraints="NO" id="9UN-ye-1aG">
                                <rect key="frame" x="25" y="35" width="252" height="100"/>
                                <subviews>
                                    <stackView opaque="NO" contentMode="scaleToFill" spacing="3" translatesAutoresizingMaskIntoConstraints="NO" id="NC0-Nr-Ufa">
                                        <rect key="frame" x="0.0" y="0.0" width="252" height="14"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="252" verticalHuggingPriority="251" text="Order No.:" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="x8F-59-wCm">
                                                <rect key="frame" x="0.0" y="0.0" width="72" height="14"/>
                                                <fontDescription key="fontDescription" name="LoewNextArabic-ExtraBold" family="Loew Next Arabic" pointSize="14"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="77829022" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Vl2-bf-VSs">
                                                <rect key="frame" x="75" y="0.0" width="177" height="14"/>
                                                <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="14"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                        </subviews>
                                    </stackView>
                                    <stackView opaque="NO" contentMode="scaleToFill" spacing="3" translatesAutoresizingMaskIntoConstraints="NO" id="gAg-ap-aOG">
                                        <rect key="frame" x="0.0" y="34" width="252" height="14"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="252" verticalHuggingPriority="251" text="Date:" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="DLP-Qe-8yv">
                                                <rect key="frame" x="0.0" y="0.0" width="37.5" height="14"/>
                                                <fontDescription key="fontDescription" name="LoewNextArabic-ExtraBold" family="Loew Next Arabic" pointSize="14"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="18-02-2022 3:05 P.M." textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="c9I-T4-eLD">
                                                <rect key="frame" x="40.5" y="0.0" width="211.5" height="14"/>
                                                <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="14"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                        </subviews>
                                    </stackView>
                                    <stackView opaque="NO" contentMode="scaleToFill" spacing="3" translatesAutoresizingMaskIntoConstraints="NO" id="9LO-JG-M39">
                                        <rect key="frame" x="0.0" y="68" width="252" height="32"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Delivered" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="bnN-7i-Bp6">
                                                <rect key="frame" x="0.0" y="0.0" width="252" height="32"/>
                                                <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="13"/>
                                                <color key="textColor" name="AppTheme_SelectedTabColor_#F4BA45"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                        </subviews>
                                    </stackView>
                                </subviews>
                            </stackView>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="icn_arrowRight" translatesAutoresizingMaskIntoConstraints="NO" id="lJ8-fA-vYK">
                                <rect key="frame" x="287" y="70" width="8" height="13.5"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="8" id="EHO-x9-zOe"/>
                                    <constraint firstAttribute="height" constant="13.5" id="znI-Bl-qxd"/>
                                </constraints>
                            </imageView>
                            <stackView hidden="YES" opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="fillEqually" translatesAutoresizingMaskIntoConstraints="NO" id="EYY-Zg-7tn">
                                <rect key="frame" x="295" y="119" width="0.0" height="0.0"/>
                                <subviews>
                                    <button hidden="YES" opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" hasAttributedTitle="YES" translatesAutoresizingMaskIntoConstraints="NO" id="Nxa-Jd-xZl">
                                        <rect key="frame" x="0.0" y="0.0" width="58" height="35"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="35" id="6RE-5S-67y"/>
                                        </constraints>
                                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                        <state key="normal">
                                            <attributedString key="attributedTitle">
                                                <fragment content="Rate now">
                                                    <attributes>
                                                        <color key="NSColor" name="AppTheme_BlueColor_#012CDA"/>
                                                        <font key="NSFont" size="12" name="LoewNextArabic-Medium"/>
                                                        <integer key="NSUnderline" value="1"/>
                                                    </attributes>
                                                </fragment>
                                            </attributedString>
                                        </state>
                                    </button>
                                    <button hidden="YES" opaque="NO" contentMode="scaleToFill" semanticContentAttribute="forceRightToLeft" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Vit-bs-Axo">
                                        <rect key="frame" x="0.0" y="0.0" width="58" height="0.0"/>
                                        <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="14"/>
                                        <inset key="imageEdgeInsets" minX="20" minY="0.0" maxX="0.0" maxY="0.0"/>
                                        <state key="normal" title="5" image="star_small">
                                            <color key="titleColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        </state>
                                    </button>
                                </subviews>
                            </stackView>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="QtM-P1-1ll">
                                <rect key="frame" x="0.0" y="152" width="320" height="1"/>
                                <color key="backgroundColor" name="AppTheme_LightGrayColor_#CECECE"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="1" id="klp-Wx-gX1"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                <color key="textColor" name="AppTheme_LightGrayColor_#CECECE"/>
                                <nil key="highlightedColor"/>
                            </label>
                        </subviews>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="EYY-Zg-7tn" firstAttribute="trailing" secondItem="lJ8-fA-vYK" secondAttribute="trailing" id="7HK-RL-rpB"/>
                            <constraint firstItem="lJ8-fA-vYK" firstAttribute="centerY" secondItem="y9p-w6-d7r" secondAttribute="centerY" id="HoT-i7-ih1"/>
                            <constraint firstAttribute="bottom" secondItem="QtM-P1-1ll" secondAttribute="bottom" id="Igu-Iw-eO2"/>
                            <constraint firstItem="QtM-P1-1ll" firstAttribute="leading" secondItem="y9p-w6-d7r" secondAttribute="leading" id="J7W-w3-uVS"/>
                            <constraint firstAttribute="bottom" secondItem="9UN-ye-1aG" secondAttribute="bottom" constant="18" id="SE6-Ab-RY9"/>
                            <constraint firstItem="EYY-Zg-7tn" firstAttribute="centerY" secondItem="9LO-JG-M39" secondAttribute="centerY" id="UOg-JG-WUp"/>
                            <constraint firstAttribute="trailing" secondItem="lJ8-fA-vYK" secondAttribute="trailing" constant="25" id="VJ4-Qa-2Jc"/>
                            <constraint firstItem="9UN-ye-1aG" firstAttribute="top" secondItem="y9p-w6-d7r" secondAttribute="top" constant="35" id="hkl-yi-GRt"/>
                            <constraint firstItem="9UN-ye-1aG" firstAttribute="leading" secondItem="y9p-w6-d7r" secondAttribute="leading" constant="25" id="qVy-wb-WWX"/>
                            <constraint firstAttribute="trailing" secondItem="QtM-P1-1ll" secondAttribute="trailing" id="uHA-TC-pLX"/>
                            <constraint firstItem="lJ8-fA-vYK" firstAttribute="leading" secondItem="9UN-ye-1aG" secondAttribute="trailing" constant="10" id="v1S-XM-88a"/>
                        </constraints>
                    </view>
                </subviews>
                <constraints>
                    <constraint firstItem="y9p-w6-d7r" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" id="D2Y-WX-sYJ"/>
                    <constraint firstAttribute="trailing" secondItem="y9p-w6-d7r" secondAttribute="trailing" id="bcH-Vu-8tb"/>
                    <constraint firstAttribute="bottom" secondItem="y9p-w6-d7r" secondAttribute="bottom" id="thp-Cv-0B6"/>
                    <constraint firstItem="y9p-w6-d7r" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" id="z8Y-2T-UYh"/>
                </constraints>
            </tableViewCellContentView>
            <viewLayoutGuide key="safeArea" id="njF-e1-oar"/>
            <connections>
                <outlet property="btnRateNow" destination="Nxa-Jd-xZl" id="JBb-Lv-TIG"/>
                <outlet property="btnRating" destination="Vit-bs-Axo" id="qul-7R-YkK"/>
                <outlet property="imgArrow" destination="lJ8-fA-vYK" id="MZD-gs-Hpz"/>
                <outlet property="lblDate" destination="c9I-T4-eLD" id="a1m-N4-ntT"/>
                <outlet property="lblDateTitle" destination="DLP-Qe-8yv" id="GVj-nc-tEN"/>
                <outlet property="lblOrderNo" destination="Vl2-bf-VSs" id="k5O-5l-jHl"/>
                <outlet property="lblOrderNoTitle" destination="x8F-59-wCm" id="PBG-ol-U4h"/>
                <outlet property="lblStatus" destination="bnN-7i-Bp6" id="Doq-S9-b28"/>
            </connections>
            <point key="canvasLocation" x="131.8840579710145" y="120.20089285714285"/>
        </tableViewCell>
    </objects>
    <resources>
        <image name="icn_arrowRight" width="8" height="13.5"/>
        <image name="star_small" width="16" height="16"/>
        <namedColor name="AppTheme_BlueColor_#012CDA">
            <color red="0.0039215686274509803" green="0.17254901960784313" blue="0.85490196078431369" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="AppTheme_LightGrayColor_#CECECE">
            <color red="0.80784313725490198" green="0.80784313725490198" blue="0.80784313725490198" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="AppTheme_SelectedTabColor_#F4BA45">
            <color red="0.95686274509803926" green="0.72941176470588232" blue="0.27058823529411763" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
