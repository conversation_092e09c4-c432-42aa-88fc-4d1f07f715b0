
import UIKit

protocol SettingsPresentationProtocol {
    func apiCallForLogout()
    func apiResponseLogout(response:CommonFields?,error:Error?)

    func apiCallForDeleteAccount()
    func apiResponseDeleteAccount(response:CommonFields?,error:Error?)

}

class SettingsPresenter: SettingsPresentationProtocol {
    
    // MARK: Objects & Variables
    weak var viewControllerSettings: SettingsProtocol?
    var interactorSettings: SettingsInteractorProtocol?
    
    
    func apiCallForLogout() {
        self.interactorSettings?.apiCallForLogout()
    }

    func apiResponseLogout(response:CommonFields?,error:Error?) {
        if let error = error  {
            viewControllerSettings?.displayAlert(string: error.localizedDescription)
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            //            viewController?.displayAlert(strTitle: "", strMessage: KeyMessages.kInvalidateResponse)
            return
        }
        
        if code == APICODE400 {
            viewControllerSettings?.displayAlert(string: response.responseMessage ?? "")
            return
        }
        
        if code == APIINVALIDAUTHORIZATIONCODE401 {
//            AppSingletonObj.goToLoginScreen(message: response.responseMessage ?? "")
            AppSingletonObj.displaySessionExpiredAlert(strMsg: response.responseMessage ?? "")
            return
        }

        if code == APISUCCESSCODE200 {
            self.viewControllerSettings?.navigateToLogin(string: response.responseMessage ?? "")
        }
    }

    func apiCallForDeleteAccount() {
        self.interactorSettings?.apiCallForDeleteAccount()
    }

    func apiResponseDeleteAccount(response:CommonFields?,error:Error?) {
        if let error = error  {
            viewControllerSettings?.displayAlert(string: error.localizedDescription)
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            //            viewController?.displayAlert(strTitle: "", strMessage: KeyMessages.kInvalidateResponse)
            return
        }
        
        if code == APICODE400 {
            viewControllerSettings?.displayAlert(string: response.responseMessage ?? "")
            return
        }
        
        if code == APIINVALIDAUTHORIZATIONCODE401 {
//            AppSingletonObj.goToLoginScreen(message: response.responseMessage ?? "")
            AppSingletonObj.displaySessionExpiredAlert(strMsg: response.responseMessage ?? "")
            return
        }

        if code == APISUCCESSCODE200 {
            self.viewControllerSettings?.navigateToLogin(string: response.responseMessage ?? "")
        }
    }

}
