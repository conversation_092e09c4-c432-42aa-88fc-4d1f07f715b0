
import UIKit

protocol SettingsInteractorProtocol {
    func apiCallForLogout()
    func apiCallForDeleteAccount()

}

protocol SettingsDataStore {
    //{ get set }
}

class SettingsInteractor: SettingsInteractorProtocol, SettingsDataStore {

    // MARK: Objects & Variables
    var presenterSettings: SettingsPresentationProtocol?
    
    func apiCallForLogout() {
        ActivityIndicator.shared.showCentralSpinner()
        
        let authorization = getAuthorizationText()

        var strDeviceToken = ""
        if vDeviceToken == "" {
//            AppDelegate.refreshToken()
            strDeviceToken = AppDelegate.getDeviceToken()
        }
        else {
            strDeviceToken = vDeviceToken
        }

        UserAPI.logout(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, vDeviceToken: strDeviceToken) { data, error in
            ActivityIndicator.shared.hideCentralSpinner()
            self.presenterSettings?.apiResponseLogout(response: data, error: error)
        }
        
    }

    func apiCallForDeleteAccount() {
        ActivityIndicator.shared.showCentralSpinner()
        
        let authorization = getAuthorizationText()

        UserAPI.deleteAccount(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang) { data, error in
            ActivityIndicator.shared.hideCentralSpinner()
            self.presenterSettings?.apiResponseDeleteAccount(response: data, error: error)

        }
        
    }

}
