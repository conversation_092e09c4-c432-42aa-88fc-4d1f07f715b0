
import UIKit
import Mixpanel
import STTabbar

protocol SettingsProtocol: AnyObject {
    func displayAlert(string:String)
    func navigateToLogin(string:String)
//    func displaySessionExpiredAlert(strMsg: String)

}

class SettingsViewController: BaseViewController, SettingsProtocol {

    // MARK: Objects & Variables
    var presenterSettings: SettingsPresentationProtocol?

    // MARK: IBOutlets
    @IBOutlet weak var tblSettings: UITableView!
    @IBOutlet weak var lblTitle: UILabel!
    @IBOutlet weak var lblName: UILabel!
//    @IBOutlet weak var lblAllYourWishes: UILabel!
    @IBOutlet weak var lblBalance: UILabel!
        
    @IBOutlet weak var lblYourAccount: UILabel!
    @IBOutlet weak var lblPayments: UILabel!
    @IBOutlet weak var lblYourFavorite: UILabel!
    @IBOutlet weak var lblHistoryOrders: UILabel!
    @IBOutlet weak var imageRewardsArrow: UIImageView!
    @IBOutlet weak var lblBottlesValue: MaterialLocalizeLable!
    @IBOutlet weak var lblBottles: MaterialLocalizeLable!
    
    private var arrEngSettingsText = SettingsEng.allCases
    private var arrArbSettingsText = SettingsArb.allCases
    private var arrSettingsImages = SettingsImages.allCases

    // MARK: Object lifecycle
    /*
    override init(nibName nibNameOrNil: String?, bundle nibBundleOrNil: Bundle?) {
        super.init(nibName: nibNameOrNil, bundle: nibBundleOrNil)
        setup()
    }
    */
    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
        setup()
    }
    
    // MARK: Setup
    
    private func setup() {
        let viewController = self
        let interactor = SettingsInteractor()
        let presenter = SettingsPresenter()
        
        //View Controller will communicate with only presenter
        viewController.presenterSettings = presenter
        
        //Presenter will communicate with Interector and Viewcontroller
        presenter.viewControllerSettings = viewController
        presenter.interactorSettings = interactor
        
        //Interactor will communucate with only presenter.
        interactor.presenterSettings = presenter
    }
    
    
    // MARK: View lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        addObservers()
        self.tabBarController?.tabBar.isHidden = false
        self.setLocalized()
        CoinsLevelsShape.shared.getPointsLevels { level, isGetNextLevel in
            self.lblBottles.text = "Bottles".localized
            self.lblBottlesValue.text = "\(Constant.shared.CURRENT_POINTS_VALUE)"
        }
    }

    func addObservers() {
        removeObservers()
        NotificationCenter.default.addObserver(self, selector: #selector(openHistoryOrder), name: NSNotification.Name(rawValue: "OpenHistoryOrder"), object: nil)

    }

    func removeObservers() {
        NotificationCenter.default.removeObserver(self)
    }

    @objc func openHistoryOrder(_ notification: NSNotification) {
        if let strOrderId = (notification.object as? [String:Any]) {
            if let id = strOrderId["iOrderId"] as? String {
                let vc = settingsStoryboard.instantiate(HistoryOrdersViewController.self)
                vc.strFromWhichScreen = "OrderCancelledByAdmin"
                vc.orderId = Int(id) ?? 0
                vc.hidesBottomBarWhenPushed = true
                self.pushVC(vc)
            }
        }
    }

    override func viewDidDisappear(_ animated: Bool) {
        super.viewDidDisappear(animated)
        NotificationCenter.default.removeObserver(self)
    }

    private func setLocalized() {
        lblTitle.text = ObjKeymessages.kLABEL_SETTINGS
        lblName.text = "\(ObjKeymessages.kLABEL_HELLO) \(User.shared.vName?.uppercased() ?? "")!"
//        lblAllYourWishes.text = ObjKeymessages.kLABEL_ALL_YOUR_WISHES
        lblBalance.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(ObjKeymessages.kLABEL_BALANCE) 0")
        if let balance = UserDefaults.standard.object(forKey: USERDEFAULTS_INFO_KEY.WALLET_BALANCE) as? String {
            lblBalance.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(ObjKeymessages.kLABEL_BALANCE) \(balance)")
        }
        
        lblYourAccount.text = ObjKeymessages.kLABEL_MY_ACCOUNT
        lblPayments.text = ObjKeymessages.kLABEL_WALLET
        lblYourFavorite.text = ObjKeymessages.kLABEL_MY_FAVORITE
        lblHistoryOrders.text = ObjKeymessages.kLABEL_HISTORY_ORDERS
        if UserDefaults.standard.getLanguage() == "en" {
            self.imageRewardsArrow.image = UIImage(named: "arrow-navigate-icon-en")
        } else {
            self.imageRewardsArrow.image = UIImage(named: "arrow-navigate-icon-ar")
        }
    }

    func displayAlert(string: String) {
//        self.showAlert(title: AppName, message: string)
        AppSingleton.shared.showCustomPopUpWithOkButton(strButtonTitle: ObjKeymessages.kLABEL_OK, strMessage: string, showOnTopVC: false) { (isOk) in
        }
    }

    func navigateToLogin(string:String) {
        SocketIOManager.shared.disConnectSocket()
        UserDefaults.standard.inviteFriendsShareLink = ""
        UserDefaults.standard.inviteFriendsShareQRImage = UIImage()
        AppDel?.restartApp()
        AppSingletonObj.showAlert(strMessage: string)
    }

    /*func displaySessionExpiredAlert(strMsg: String) {
        let alert = UIAlertController(title: AppName, message: strMsg, preferredStyle: .alert)
        let okButton = UIAlertAction(title: ObjKeymessages.kLABEL_OK, style: .default, handler: {(_ action: UIAlertAction) -> Void in
            AppDel?.restartApp()
            self.dismiss(animated: true, completion: nil)
        })
        alert.addAction(okButton)
        self.present(alert, animated: true) {() -> Void in }
    }*/

    @IBAction func actionYourAccount(_ sender: Any) {
        let vc = settingsStoryboard.instantiate(AccountViewController.self)
        vc.hidesBottomBarWhenPushed = true
        self.pushVC(vc)
    }
    
    @IBAction func actionYourFavorites(_ sender: Any) {
        let vc = homeStoryboard.instantiate(FavoriteViewController.self)
        vc.hidesBottomBarWhenPushed = true
        vc.delegate = self
        self.pushVC(vc)
    }

    @IBAction func actionHistoryOrders(_ sender: Any) {
        let vc = settingsStoryboard.instantiate(HistoryOrdersViewController.self)
        vc.hidesBottomBarWhenPushed = true
        self.pushVC(vc)
    }

    @IBAction func actionPayments(_ sender: Any) {
        let vc = PaymentsStoryboard.instantiate(PaymentsViewController.self)
        vc.hidesBottomBarWhenPushed = true
        self.pushVC(vc)
    }
    
    @IBAction func actionRewardsVC(_ sender: Any) {
        let vc = homeStoryboard.instantiate(RewardVC.self)
        vc.hidesBottomBarWhenPushed = true
        self.pushVC(vc)
    }
    
}


extension SettingsViewController : UITableViewDelegate, UITableViewDataSource{
    func numberOfSections(in tableView: UITableView) -> Int {
        return 1
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return arrEngSettingsText.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tblSettings.dequeueReusableCell(withIdentifier: "SettingsTableViewCell") as! SettingsTableViewCell
        
        if UserDefaults.standard.getLanguage() == UserAPI.VLanguage_userLanguage.en.rawValue {
            cell.lblSettingName.text = arrEngSettingsText[indexPath.row].getTitle()
        }
        else {
            cell.lblSettingName.text = arrArbSettingsText[indexPath.row].getTitle()
        }
        
            
        cell.imgSetting.image = UIImage(named: arrSettingsImages[indexPath.row].rawValue)

        /*if indexPath.row == 7 || indexPath.row == 8 {
            cell.btnArrow.isHidden = true

        }
        else {
            cell.btnArrow.isHidden = false
        }
           
        if indexPath.row == 8 {
            cell.lblSeperator.isHidden = true
            cell.lblSettingName.textColor = .darkGray
        }
        else {
            cell.lblSeperator.isHidden = false
            cell.lblSettingName.textColor = .black
        }*/

        
        return cell
    }
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        
        let index = indexPath.row
        
        switch index {
        case 0:
            self.openWhatsapp()
//            let vc = settingsStoryboard.instantiate(ContactUsViewController.self)
//            vc.hidesBottomBarWhenPushed = true
//            self.pushVC(vc)
        case 1:
            let controller : StaticPageViewController = UIStoryboard.storyboard(.Settings).instantiateViewController()
            controller.strFromWhichScreen = "PrivacyPolicy"
            controller.hidesBottomBarWhenPushed = true
            self.pushVC(controller)
            break
        case 2:
            let vc = settingsStoryboard.instantiate(SelectLanguageViewController.self)
            self.presentVC(vc)
        case 3:
            self.rateThisApp()
        case 4:
            /*let alert = UIAlertController(title: AppName, message: ObjKeymessages.kMSG_LOG_OUT, preferredStyle: .alert)
            let cancelButton = UIAlertAction(title: ObjKeymessages.kLABEL_NO, style: .default, handler: {(_ action: UIAlertAction) -> Void in
            })
            let logOutButton = UIAlertAction(title: ObjKeymessages.kLABEL_YES, style: .default, handler: {(_ action: UIAlertAction) -> Void in
                
                // Mixpanel
                Mixpanel.mainInstance().track(event: ScreenNames.Logout.rawValue)
                Mixpanel.mainInstance().reset()
                
                if AppSingletonObj.isConnectedToNetwork(){
                    self.presenterSettings?.apiCallForLogout()
                }
            })
            alert.addAction(cancelButton)
            alert.addAction(logOutButton)
            self.present(alert, animated: true) {() -> Void in }*/
            
            
            
            
            /*let vc = settingsStoryboard.instantiate(CustomPopupAlertViewController.self)
            vc.modalPresentationStyle = .overFullScreen
            vc.modalTransitionStyle = .crossDissolve
            vc.strDescription = ObjKeymessages.kMSG_LOG_OUT
            vc.strLeftButtonTitle = ObjKeymessages.kLABEL_NO
            vc.isNeedToSwitchButton = true
            vc.strRightButtonTitle = ObjKeymessages.kLABEL_YES
            vc.isCenterButton = false
            vc.completion = { (index) in
                if index == 0 {
                }else{
                    // Mixpanel
                    Mixpanel.mainInstance().track(event: ScreenNames.Logout.rawValue)
                    Mixpanel.mainInstance().reset()
                    
                    if AppSingletonObj.isConnectedToNetwork(){
                        self.presenterSettings?.apiCallForLogout()
                    }
                }
            }
            self.presentVC(vc)*/
            
            AppSingleton.shared.showCustomPopUpWithYesNoButton(strButton1Title: ObjKeymessages.kLABEL_YES, strButton2Title: ObjKeymessages.kLABEL_NO, strMessage: ObjKeymessages.kMSG_LOG_OUT, showOnTopVC: false) { (isOk) in
                if isOk == true {
                    // Mixpanel
                    Mixpanel.mainInstance().track(event: ScreenNames.Logout.rawValue)
                    Mixpanel.mainInstance().reset()
                    
                    if AppSingletonObj.isConnectedToNetwork(){
                        self.presenterSettings?.apiCallForLogout()
                    }
                }
            }
            break
        default:
            print("Default")
        }
    }
    
    private func rateThisApp() {
        guard let socialUrl = URL(string: AppStoreURL) else {
            return
        }
        if UIApplication.shared.canOpenURL(socialUrl) {
            if #available(iOS 10.0, *) {
                UIApplication.shared.open(socialUrl, options: [:], completionHandler: nil)
            } else {
                UIApplication.shared.openURL(socialUrl)
                // Fallback on earlier versions
            }
        }
    }

    private func openWhatsapp() {
        guard let whatsappURL = URL(string: "https://wa.me/+966554020703") else {return}
        if UIApplication.shared.canOpenURL(whatsappURL) {
            UIApplication.shared.open(whatsappURL, options: [:], completionHandler: nil)
        } else {
            AppSingleton.shared.showCustomPopUpWithOkButton(strButtonTitle: ObjKeymessages.kLABEL_OK, strMessage: "please_install_whatsapp".localized, showOnTopVC: false) { (isOk) in
            }
        }
    }
    
}

extension SettingsViewController : UpdateCountFromFavoriteProtocol {
    
    func updateCartCountFromFavorite(cartCount:Int?) {
        // update my cart button count
        if let myTabbar = self.tabBarController?.tabBar as? STTabbar {
            if cartCount ?? 0 <= 0 {
                myTabbar.label?.isHidden = true
            }
            else {
                myTabbar.label?.isHidden = false
                myTabbar.label?.text = "\(cartCount ?? 0)"
            }
        }

    }
}
