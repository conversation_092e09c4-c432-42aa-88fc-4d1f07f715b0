
import UIKit

protocol HistoryOrderDetailsPresentationProtocol : AnyObject {
    func apiCallForGetOrderDetail(dictData:[String:Any])
    func apiResponseGetOrderDetail(response:OrderDetailResponse?,error:Error?)

    func apiCallForReOrder(dictData:[String:Any], transactionType: Int, flagCardOrNot: Bool, strCardToken: String, strCardTransactionReference: String)
    func apiResponseReOrderDetail(response:CreateOrderResponse?,error:Error?, transactionType: Int, flagCardOrNot: Bool, strCardToken: String, strCardTransactionReference: String)

    func apiCallForUpdateTransaction(dictData:[String:Any], paymentStatus: Int, strCardNumber: String, strCardType: String, strCardScheme: String, strCardToken: String)
    func apiResponseUpdateTransaction(response:TransactionResponse?,error:Error?, paymentStatus: Int, strCardNumber: String, strCardType: String, strCardScheme: String, strCardToken: String, strTransactionId: String)

    func apiCallForAddCard(dictData:[String:Any])
    func apiResponseAddCard(response:CommonFields?,error:Error?)

}

class HistoryOrderDetailsPresenter: HistoryOrderDetailsPresentationProtocol {
    
    //MARK: - Objects & Variables -
    weak var viewControllerHistoryOrderDetails: HistoryOrderDetailsProtocol?
    var interactorHistoryOrderDetails: HistoryOrderDetailsInteractorProtocol?

    func apiCallForGetOrderDetail(dictData:[String:Any]) {
        interactorHistoryOrderDetails?.apiCallForGetOrderDetail(dictData: dictData)
    }

    func apiResponseGetOrderDetail(response: OrderDetailResponse?, error: Error?) {
//        if let vc = self.viewControllerHistoryOrderDetails as? BaseViewController {
//            DispatchQueue.main.async {
//                vc.endRefresing()
//            }
//        }
        if let error = error  {
            viewControllerHistoryOrderDetails?.displayAlert(string: error.localizedDescription)
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            //            viewController?.displayAlert(strTitle: "", strMessage: KeyMessages.kInvalidateResponse)
            return
        }
        
        if code == APIINVALIDAUTHORIZATIONCODE401 {
            AppSingletonObj.goToLoginScreen(message: response.responseMessage ?? "")
            return
        }

        if code == APICODE400 {
            viewControllerHistoryOrderDetails?.displayAlertAndDismissScreen(string: response.responseMessage ?? "")
            return
        }
        
        guard let model = response.responseData,code == APISUCCESSCODE200  else {
            return
        }
        self.viewControllerHistoryOrderDetails?.getOrderDetails(model: model)
    }

    func apiCallForReOrder(dictData:[String:Any], transactionType: Int, flagCardOrNot: Bool, strCardToken: String, strCardTransactionReference: String) {
        interactorHistoryOrderDetails?.apiCallForReOrderDetail(dictData: dictData, transactionType: transactionType, flagCardOrNot: flagCardOrNot, strCardToken: strCardToken, strCardTransactionReference: strCardTransactionReference)
    }

    func apiResponseReOrderDetail(response:CreateOrderResponse?,error:Error?, transactionType: Int, flagCardOrNot: Bool, strCardToken: String, strCardTransactionReference: String) {
//        if let vc = self.viewControllerHistoryOrderDetails as? BaseViewController {
//            DispatchQueue.main.async {
//                vc.endRefresing()
//            }
//        }
        if let error = error  {
            viewControllerHistoryOrderDetails?.displayAlert(string: error.localizedDescription)
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            //            viewController?.displayAlert(strTitle: "", strMessage: KeyMessages.kInvalidateResponse)
            return
        }
        
        if code == APIINVALIDAUTHORIZATIONCODE401 {
            AppSingletonObj.goToLoginScreen(message: response.responseMessage ?? "")
            return
        }

        if code == APICODE400 {
            viewControllerHistoryOrderDetails?.displayAlert(string: response.responseMessage ?? "")
            return
        }
        
        if code == APICODE203 {
            self.viewControllerHistoryOrderDetails?.displayErrorAlertAndGoToHome(strMsg: response.responseMessage ?? "")
            return
        }

        guard let model = response.responseData,code == APISUCCESSCODE200  else {
            return
        }
        self.viewControllerHistoryOrderDetails?.getReOrderData(model: model, transactionType: transactionType, flagCardOrNot: flagCardOrNot, strCardToken: strCardToken, strCardTransactionReference: strCardTransactionReference)
    }

    func apiCallForUpdateTransaction(dictData:[String:Any], paymentStatus: Int, strCardNumber: String, strCardType: String, strCardScheme: String, strCardToken: String) {
        self.interactorHistoryOrderDetails?.apiCallForUpdateTransaction(dictData: dictData, paymentStatus: paymentStatus, strCardNumber: strCardNumber, strCardType: strCardType, strCardScheme: strCardScheme, strCardToken: strCardToken)
    }

    func apiResponseUpdateTransaction(response:TransactionResponse?,error:Error?, paymentStatus: Int, strCardNumber: String, strCardType: String, strCardScheme: String, strCardToken: String, strTransactionId: String) {
        if let error = error  {
//            viewControllerLogin?.displayAlert(string: error.localizedDescription)
            AppSingletonObj.showAlert(strMsg: error.localizedDescription)
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            //            viewController?.displayAlert(strTitle: "", strMessage: KeyMessages.kInvalidateResponse)
            return
        }
        
        if code == APIINVALIDAUTHORIZATIONCODE401 {
            AppSingletonObj.goToLoginScreen(message: response.responseMessage ?? "")
            return
        }

        if code == APICODE400 {
            viewControllerHistoryOrderDetails?.displayAlert(string: response.responseMessage ?? "")
            return
        }
        
        if code == APICODE203 {
            if paymentStatus == 1 {  // success
                self.viewControllerHistoryOrderDetails?.displayErrorAlert(strMsg: response.responseMessage ?? "")
            }
            else {
                self.viewControllerHistoryOrderDetails?.displayErrorAlertAndGoToHome(strMsg: response.responseMessage ?? "")
            }
            return
        }

//        guard let model = response.responseData,code == APISUCCESSCODE200  else {
//            return
//        }
        
        if paymentStatus == 1 || paymentStatus == 0 {  // success
            self.viewControllerHistoryOrderDetails?.showThankyouAlert(strCardNumber: strCardNumber, strCardType: strCardType, strCardScheme: strCardScheme, strCardToken: strCardToken, strTransactionId: strTransactionId)
        }

    }

    func apiCallForAddCard(dictData:[String:Any]) {
        self.interactorHistoryOrderDetails?.apiCallForAddCard(dictData: dictData)
    }

    func apiResponseAddCard(response:CommonFields?,error:Error?) {
//        if let vc = self.viewControllerHistoryOrderDetails as? BaseViewController {
//            DispatchQueue.main.async {
//                vc.endRefresing()
//            }
//        }
        if let error = error  {
            viewControllerHistoryOrderDetails?.displayAlert(string: error.localizedDescription)
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            //            viewController?.displayAlert(strTitle: "", strMessage: KeyMessages.kInvalidateResponse)
            return
        }
        
        if code == APIINVALIDAUTHORIZATIONCODE401 {
            AppSingletonObj.goToLoginScreen(message: response.responseMessage ?? "")
            return
        }

        if code == APICODE400 {
            viewControllerHistoryOrderDetails?.displayAlert(string: response.responseMessage ?? "")
            return
        }
        
//        guard let model = response.responseData,code == APISUCCESSCODE200  else {
//            return
//        }
        

    }

}
