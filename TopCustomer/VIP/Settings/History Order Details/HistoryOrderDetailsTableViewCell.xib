<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="20037" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="20020"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="LoewNextArabic-Bold.ttf">
            <string>LoewNextArabic-Bold</string>
        </array>
        <array key="LoewNextArabic-Medium.ttf">
            <string>LoewNextArabic-Medium</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" rowHeight="30" id="KGk-i7-Jjw" customClass="HistoryOrderDetailsTableViewCell" customModule="TopCustomer" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="320" height="32"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="320" height="32"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <stackView opaque="NO" contentMode="scaleToFill" spacing="5" translatesAutoresizingMaskIntoConstraints="NO" id="cnN-Ye-VUi">
                        <rect key="frame" x="27" y="10" width="266" height="12"/>
                        <subviews>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="X6" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Mcb-m8-aO4" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                <rect key="frame" x="0.0" y="0.0" width="26" height="12"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="26" id="Qmu-5M-WmP"/>
                                </constraints>
                                <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="12"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" verticalHuggingPriority="251" text="Arwa water 330 ml X48" lineBreakMode="wordWrap" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Owv-jf-Xgb" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                <rect key="frame" x="31" y="0.0" width="167" height="12"/>
                                <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="12"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" horizontalCompressionResistancePriority="752" text="18.00 SAR" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="nba-Yb-0ry" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                <rect key="frame" x="203" y="0.0" width="63" height="12"/>
                                <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="12"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                        </subviews>
                    </stackView>
                </subviews>
                <constraints>
                    <constraint firstItem="cnN-Ye-VUi" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" constant="10" id="LIg-WK-Ume"/>
                    <constraint firstItem="cnN-Ye-VUi" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="27" id="Tpp-3i-gn5"/>
                    <constraint firstAttribute="trailing" secondItem="cnN-Ye-VUi" secondAttribute="trailing" constant="27" id="c8d-df-CWx"/>
                    <constraint firstAttribute="bottom" secondItem="cnN-Ye-VUi" secondAttribute="bottom" constant="10" id="jRD-el-Y3t"/>
                </constraints>
            </tableViewCellContentView>
            <viewLayoutGuide key="safeArea" id="njF-e1-oar"/>
            <connections>
                <outlet property="lblName" destination="Owv-jf-Xgb" id="DLJ-M6-dOR"/>
                <outlet property="lblPrice" destination="nba-Yb-0ry" id="5Ui-9F-3Cz"/>
                <outlet property="lblQuantity" destination="Mcb-m8-aO4" id="kby-my-36L"/>
            </connections>
            <point key="canvasLocation" x="131.8840579710145" y="97.767857142857139"/>
        </tableViewCell>
    </objects>
</document>
