
import UIKit

protocol HistoryOrderDetailsInteractorProtocol : AnyObject {
    func apiCallForGetOrderDetail(dictData:[String:Any])
    func apiCallForReOrderDetail(dictData:[String:Any], transactionType: Int, flagCardOrNot: Bool, strCardToken: String, strCardTransactionReference: String)
    func apiCallForUpdateTransaction(dictData:[String:Any], paymentStatus: Int, strCardNumber: String, strCardType: String, strCardScheme: String, strCardToken: String)
    func apiCallForAddCard(dictData:[String:Any])

}

protocol HistoryOrderDetailsDataStore {
    //{ get set }
}

class HistoryOrderDetailsInteractor: HistoryOrderDetailsInteractorProtocol, HistoryOrderDetailsDataStore {

    //MARK: - Objects & Variables -
    weak var presenterHistoryOrderDetails: HistoryOrderDetailsPresentationProtocol?
    
    func apiCallForGetOrderDetail(dictData:[String:Any]) {
        
        ActivityIndicator.shared.showCentralSpinner()
        
        let authorization = getAuthorizationText()
        
        OrderAPI.getOrder(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, iOrderId: Int(setDataInString(dictData["iOrderId"] as AnyObject)) ?? 0) { data, error in
                ActivityIndicator.shared.hideCentralSpinner()
                self.presenterHistoryOrderDetails?.apiResponseGetOrderDetail(response: data, error: error)

        }
        
    }

    func apiCallForReOrderDetail(dictData:[String:Any], transactionType: Int, flagCardOrNot: Bool, strCardToken: String, strCardTransactionReference: String) {
        
        ActivityIndicator.shared.showCentralSpinner()
        
        let authorization = getAuthorizationText()
        
        /*OrderAPI.getOrder(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, iOrderId: Int(setDataInString(dictData["iOrderId"] as AnyObject)) ?? 0) { data, error in
                ActivityIndicator.shared.hideCentralSpinner()
                self.presenterHistoryOrderDetails?.apiResponseGetOrderDetail(response: data, error: error)

        }*/
        
        OrderAPI.reOrder(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, iOrderId: Int(setDataInString(dictData["iOrderId"] as AnyObject)) ?? 0) { data, error in
            ActivityIndicator.shared.hideCentralSpinner()
            self.presenterHistoryOrderDetails?.apiResponseReOrderDetail(response: data, error: error, transactionType: transactionType, flagCardOrNot: flagCardOrNot, strCardToken: strCardToken, strCardTransactionReference: strCardTransactionReference)
        }
        
    }

    func apiCallForUpdateTransaction(dictData:[String:Any], paymentStatus: Int, strCardNumber: String, strCardType: String, strCardScheme: String, strCardToken: String) {

        ActivityIndicator.shared.showCentralSpinner()
        
        let authorization = getAuthorizationText()
        
        OrderAPI.orderTransactionUpdate(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, iOrderId: Int(setDataInString(dictData["iOrderId"] as AnyObject)) ?? 0, tiTransactionType: Int(setDataInString(dictData["tiTransactionType"] as AnyObject)) ?? 0, vTransactionRef: setDataInString(dictData["vTransactionRef"] as AnyObject), iPaymentStatus: Int(setDataInString(dictData["iPaymentStatus"] as AnyObject)) ?? 0) { data, error in
            ActivityIndicator.shared.hideCentralSpinner()
            self.presenterHistoryOrderDetails?.apiResponseUpdateTransaction(response: data, error: error, paymentStatus: paymentStatus, strCardNumber: strCardNumber, strCardType: strCardType, strCardScheme: strCardScheme, strCardToken: strCardToken, strTransactionId: setDataInString(dictData["vTransactionRef"] as AnyObject))
        }
        
    }

    func apiCallForAddCard(dictData:[String:Any]) {
        let authorization = getAuthorizationText()

        UserCardsAPI.addUserCards(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, vToken: setDataInString(dictData["vToken"] as AnyObject), vTransactionId: setDataInString(dictData["vTransactionId"] as AnyObject), vPaymentDescription: setDataInString(dictData["vPaymentDescription"] as AnyObject), vCardType: setDataInString(dictData["vCardType"] as AnyObject), vCardScheme: setDataInString(dictData["vCardScheme"] as AnyObject)) { data, error in
            
            self.presenterHistoryOrderDetails?.apiResponseAddCard(response: data, error: error)

        }
    }

}
