
import UIKit
import PaymentSDK

protocol HistoryOrderDetailsProtocol: AnyObject {
    func displayAlert(string:String)
    func getOrderDetails(model : OrderDetailResponseFields)
    func getReOrderData(model : CreateOrderResponseFields, transactionType: Int, flagCardOrNot: Bool, strCardToken: String, strCardTransactionReference: String)
    func showThankyouAlert(strCardNumber: String, strCardType: String, strCardScheme: String, strCardToken: String, strTransactionId: String)
    func displayErrorAlert(strMsg:String)
    func displayErrorAlertAndGoToHome(strMsg:String)
    func displayAlertAndDismissScreen(string: String)

}

class HistoryOrderDetailsViewController: BaseViewController {

    //MARK: - Properties -
    var presenterHistoryOrderDetails: HistoryOrderDetailsPresentationProtocol?

    //MARK: - IBOutlets -
    @IBOutlet weak var tblOrders: UITableView!
    @IBOutlet weak var tblOrdersHeightConstraints: NSLayoutConstraint!
    @IBOutlet weak var lblOrderNo: UILabel!
    @IBOutlet weak var lblOrderDate: UILabel!
    @IBOutlet weak var lblSubTotal: UILabel!
    @IBOutlet weak var lblDelivery: UILabel!
    @IBOutlet weak var lblTotal: UILabel!
    @IBOutlet weak var lblVat: UILabel!
    @IBOutlet weak var btnReOrder: UIButton!
    @IBOutlet weak var btnHelp: UIButton!
    @IBOutlet weak var btnInvoice: UIButton!
    @IBOutlet weak var lblTitle: UILabel!
    @IBOutlet weak var lblOrderNoTitle: UILabel!
    @IBOutlet weak var lblOrderDateTitle: UILabel!
    @IBOutlet weak var lblStatus: UILabel!
    @IBOutlet weak var lblSubTotalTitle: UILabel!
    @IBOutlet weak var lblDeliveryTitle: UILabel!
    @IBOutlet weak var lblTotalTitle: UILabel!
    @IBOutlet weak var lblAllPrices: UILabel!
    @IBOutlet weak var lblDiscountTitle: UILabel!
    @IBOutlet weak var lblDiscountValue: UILabel!
    @IBOutlet weak var stackViewDiscount: UIStackView!
    @IBOutlet weak var lblLineDiscount: UILabel!
    
    @IBOutlet weak var lblDeliveryDiscountTitle: MaterialLocalizeLable!
    @IBOutlet weak var lblDeliveryDiscountValue: MaterialLocalizeLable!
    @IBOutlet weak var stackViewDeliveryDiscount: UIStackView!
    @IBOutlet weak var lblLineDeliveryDiscount: UILabel!

    @IBOutlet weak var lblPaymentMethodTitle: UILabel!
    @IBOutlet weak var lblPaymentMethodValue: UILabel!
    @IBOutlet weak var lblType: UILabel!
    @IBOutlet weak var lblAddress: UILabel!
    @IBOutlet weak var lblShift: UILabel!
    @IBOutlet weak var lblNote: UILabel!
    @IBOutlet weak var lblDeliveryAddressTitle: UILabel!
    @IBOutlet weak var lblDeliveryShiftTitle: UILabel!
    @IBOutlet weak var lblNotesTitle: UILabel!
    @IBOutlet weak var cons_bottom_viewNotes: NSLayoutConstraint!
    @IBOutlet weak var viewNote: UIView!

    @IBOutlet weak var lblOrderTypeTitle: MaterialLocalizeLable!
    @IBOutlet weak var lblOrderTypeValue: MaterialLocalizeLable!

    @IBOutlet weak var viewPaymentMethod: UIView!
    @IBOutlet weak var cons_height_viewPaymentMethod: NSLayoutConstraint!

    var orderId = 0
    var objOrderDetails: OrderDetailResponseFields?
    var dismissScreen: (()->())?
    var reOrderId = 0
    var reOrderAmount = ""

    var isAddNewCard = false
    var dictParam : [String:Any] = [:]

    //MARK: - Object lifecycle
//    override var popupHeight: CGFloat { return self.view.bounds.height }
    
    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
        setup()
    }
     
    
    //MARK: - View Life Cycle -
    override func viewDidLoad() {
        super.viewDidLoad()
        print("Allocated : \(self.classForCoder)")
        initialSetUp()
        self.setTexts()
        self.getOrderDetails()

    }
    
    private func getOrderDetails() {
        var dictParam : [String:Any] = [:]
        dictParam["iOrderId"] = orderId

        if AppSingletonObj.isConnectedToNetwork(){
            self.presenterHistoryOrderDetails?.apiCallForGetOrderDetail(dictData: dictParam)
        }
    }

    private func setTexts() {
        lblTitle.text = ObjKeymessages.kLABEL_HISTORY_ORDER
        lblOrderNoTitle.text = "\(ObjKeymessages.kLABEL_ORDER_NO):"
        lblOrderDateTitle.text = "\(ObjKeymessages.kLABEL_DATE):"
//        lblStatus.text = ObjKeymessages.kLABEL_DELIVERED

        btnReOrder.setTitle(ObjKeymessages.kLABEL_REORDER, for: .normal)
        lblSubTotalTitle.text = "\(ObjKeymessages.kLABEL_SUBTOTAL) (\(ObjKeymessages.kLABEL_WITHOUT_VAT))"
        lblDeliveryTitle.text = "\(ObjKeymessages.kLABEL_DELIVERY)"
        lblTotalTitle.text = "\(ObjKeymessages.kLABEL_TOTAL)"

        lblVat.text = "*(\(ObjKeymessages.kLABEL_VAT_INCLUDED))"
        lblAllPrices.text = ObjKeymessages.kLABEL_ALL_PRICES
        lblDiscountTitle.text = "\(ObjKeymessages.kLABEL_DISCOUNT)"
        lblDeliveryDiscountTitle.text = ObjKeymessages.kLABEL_DELIVERY_DISCOUNT

        lblPaymentMethodTitle.text = ObjKeymessages.kLABEL_PAYMENT_METHOD

//        btnInvoice.setTitle(ObjKeymessages.kLABEL_INVOICE, for: .normal)
        let stringInvoice = ObjKeymessages.kLABEL_INVOICE
        let rangeInvoice = (stringInvoice as NSString).range(of: ObjKeymessages.kLABEL_INVOICE)
        let attributedStringInvoice = NSMutableAttributedString(string: stringInvoice)
        attributedStringInvoice.addAttribute(NSAttributedString.Key.underlineStyle, value: NSNumber(value: 1), range: rangeInvoice)
        attributedStringInvoice.addAttribute(NSAttributedString.Key.underlineColor, value: UIColor.AppTheme_BlueColor_012CDA, range: rangeInvoice)
        attributedStringInvoice.addAttribute(NSAttributedString.Key.font, value: UIFont(name: Fonts.LoewNextArabicMedium, size: 12)!, range: rangeInvoice)
        attributedStringInvoice.addAttribute(NSAttributedString.Key.foregroundColor, value: UIColor.AppTheme_BlueColor_012CDA, range: rangeInvoice)
        btnInvoice.setAttributedTitle(attributedStringInvoice, for: .normal)

//        btnHelp.setTitle(ObjKeymessages.kLABEL_HELP, for: .normal)
        let stringHelp = ObjKeymessages.kLABEL_HELP
        let rangeHelp = (stringHelp as NSString).range(of: ObjKeymessages.kLABEL_HELP)
        let attributedStringHelp = NSMutableAttributedString(string: stringHelp)
        attributedStringHelp.addAttribute(NSAttributedString.Key.underlineStyle, value: NSNumber(value: 1), range: rangeHelp)
        attributedStringHelp.addAttribute(NSAttributedString.Key.underlineColor, value: UIColor.AppTheme_BlueColor_012CDA, range: rangeHelp)
        attributedStringHelp.addAttribute(NSAttributedString.Key.font, value: UIFont(name: Fonts.LoewNextArabicMedium, size: 12)!, range: rangeHelp)
        attributedStringHelp.addAttribute(NSAttributedString.Key.foregroundColor, value: UIColor.AppTheme_BlueColor_012CDA, range: rangeHelp)
        btnHelp.setAttributedTitle(attributedStringHelp, for: .normal)

        lblDeliveryAddressTitle.text = ObjKeymessages.kLABEL_DELIVERY_ADDRESS
        lblDeliveryShiftTitle.text = ObjKeymessages.kLABEL_DELIVERY_SHIFT
        lblOrderTypeTitle.text = ObjKeymessages.kLABEL_ORDER_TYPE
        lblNotesTitle.text = ObjKeymessages.kLABEL_NOTES

    }
    
    //MARK: - Memory Management -
    override func didReceiveMemoryWarning() {
        super.didReceiveMemoryWarning()
        print("didReceiveMemoryWarning : \(self.classForCoder)")
    }
    
    //MARK: - DeInit -
    deinit {
        print("DeAllocated :  \(self.classForCoder)")
    }
    
    //MARK: Our Own Functions
    
    //MARK: - Actions
    @IBAction func actionReOrder(_ sender: UIButton) {
//        var dictParam : [String:Any] = [:]
        dictParam["iOrderId"] = orderId

        if AppSingletonObj.isConnectedToNetwork(){
//            self.presenterHistoryOrderDetails?.apiCallForReOrder(dictData: dictParam)
            self.showPaymentPopup()
        }
    }
    
    @IBAction func actionHelp(_ sender: UIButton) {
        let vc = ProductPopupStoryboard.instantiate(HelpViewController.self)
//            vc.modalPresentationStyle = .fullScreen
        vc.modalPresentationStyle = .overFullScreen
        vc.modalTransitionStyle = .crossDissolve
        vc.orderId = self.orderId
        self.presentVC(vc)
    }
    
    @IBAction func actionInvoice(_ sender: UIButton) {
        let vc = ViewInvoiceVC()
        vc.invoiceUrl = objOrderDetails?.vInvoicePdfName ?? ""
        self.presentVC(vc)
    }
    
    //MARK: - Protocol Functions -
    private func initialSetUp() {
        self.tblOrders.registerCell(cell: HistoryOrderDetailsTableViewCell.self)
        self.tblOrders.delegate = self
        self.tblOrders.dataSource = self
    }
 
    @IBAction func btnDimViewAction(_ sender: Any) {
        self.dismiss(animated: true, completion: nil)
    }

}

//MARK: - Extensions
extension HistoryOrderDetailsViewController: UITableViewDelegate, UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return objOrderDetails?.productDetails?.count ?? 0
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeue(with: HistoryOrderDetailsTableViewCell.self, for: indexPath)
        
        cell.lblQuantity.text = "X\(objOrderDetails?.productDetails?[indexPath.row].iProductQuantity ?? 0)"
//        cell.lblName.text = objOrderDetails?.productDetails?[indexPath.row].vProductName
        cell.lblName.text = "\(objOrderDetails?.productDetails?[indexPath.row].vProductName ?? "") \(objOrderDetails?.productDetails?[indexPath.row].vProductUnit ?? "")"
        cell.lblPrice.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(objOrderDetails?.productDetails?[indexPath.row].dbPrice ?? "")")

        return cell
    }
    
    func tableView(_ tableView: UITableView, willDisplay cell: UITableViewCell, forRowAt indexPath: IndexPath) {
        self.tblOrders.layoutIfNeeded()
        self.tblOrdersHeightConstraints.constant = self.tblOrders.contentSize.height
    }
}


extension HistoryOrderDetailsViewController {
    //MARK: - VIP Setup -
    /// VIP Setup for HistoryOrderDetailsViewController
    private func setup() {
        let viewController = self
        let interactor = HistoryOrderDetailsInteractor()
        let presenter = HistoryOrderDetailsPresenter()
        
        //View Controller will communicate with only presenter
        viewController.presenterHistoryOrderDetails = presenter
        
        //Presenter will communicate with Interector and Viewcontroller
        presenter.viewControllerHistoryOrderDetails = viewController
        presenter.interactorHistoryOrderDetails = interactor
        
        //Interactor will communucate with only presenter.
        interactor.presenterHistoryOrderDetails = presenter
    }
}

extension HistoryOrderDetailsViewController: HistoryOrderDetailsProtocol {
    func displayAlert(string: String) {
//        self.showAlert(title: AppName, message: string)
        AppSingleton.shared.showCustomPopUpWithOkButton(strButtonTitle: ObjKeymessages.kLABEL_OK, strMessage: string, showOnTopVC: false) { (isOk) in
        }
    }

    func getOrderDetails(model : OrderDetailResponseFields) {
        objOrderDetails = model
        self.setData()
    }

    func getReOrderData(model : CreateOrderResponseFields, transactionType: Int, flagCardOrNot: Bool, strCardToken: String, strCardTransactionReference: String) {
        reOrderId = model.iOrderId ?? 0
        reOrderAmount = model.dOrderTotal ?? ""
//        self.showPaymentPopup()

        if transactionType == TransactionType.Card.rawValue {
            self.payWithCard(flagCardOrNot: flagCardOrNot, strCardToken: strCardToken, strCardTransactionReference: strCardTransactionReference)
        }
        else if transactionType == TransactionType.ApplePay.rawValue {
            self.payWithApplePay()
        }
        else if transactionType == TransactionType.COD.rawValue {
            self.updateTransaction(strTransactionReference: TransactionTypeCOD, paymentStatus: 0, strCardNumber: "", strCardType: "", strCardScheme: "", strCardToken: "", transactionType: TransactionType.COD.rawValue)
        }

    }
    
    func showPaymentPopup() {
        let vc = PaymentsStoryboard.instantiate(ChoosePaymentViewController.self)
        vc.modalPresentationStyle = .overFullScreen
        vc.modalTransitionStyle = .crossDissolve
        
        vc.passSelectedCardData = { [weak self] (cardToken, cardTransactionReference, flag) in
            DispatchQueue.main.async {
                self?.isAddNewCard = flag
                
                self?.presenterHistoryOrderDetails?.apiCallForReOrder(dictData: self?.dictParam ?? [:], transactionType: TransactionType.Card.rawValue, flagCardOrNot: flag, strCardToken: cardToken, strCardTransactionReference: cardTransactionReference)
//                self?.payWithCard(flagCardOrNot: flag, strCardToken: cardToken, strCardTransactionReference:
//                                    cardTransactionReference)
            }
        }
        
        vc.applePaySelected = { [weak self] () in
            DispatchQueue.main.async {
                self?.presenterHistoryOrderDetails?.apiCallForReOrder(dictData: self?.dictParam ?? [:], transactionType: TransactionType.ApplePay.rawValue, flagCardOrNot: false, strCardToken: "", strCardTransactionReference: "")
//                self?.payWithApplePay()
            }
        }

        vc.codSelected = { [weak self] () in
            DispatchQueue.main.async {
                self?.presenterHistoryOrderDetails?.apiCallForReOrder(dictData: self?.dictParam ?? [:], transactionType: TransactionType.COD.rawValue, flagCardOrNot: false, strCardToken: "", strCardTransactionReference: "")
//                self?.updateTransaction(strTransactionReference: TransactionTypeCOD, paymentStatus: 0, strCardNumber: "", strCardType: "", strCardScheme: "", strCardToken: "", transactionType: TransactionType.COD.rawValue)
            }
        }

        self.presentVC(vc)
    }
    
    func displayErrorAlert(strMsg:String) {
        /*let alert = UIAlertController(title: AppName, message: strMsg, preferredStyle: .alert)
        let okButton = UIAlertAction(title: ObjKeymessages.kLABEL_OK, style: .default, handler: {(_ action: UIAlertAction) -> Void in
            self.popVC()
        })
        alert.addAction(okButton)
        self.present(alert, animated: true) {() -> Void in }*/
        
        AppSingleton.shared.showCustomPopUpWithOkButton(strButtonTitle: ObjKeymessages.kLABEL_OK, strMessage: strMsg, showOnTopVC: false) { (isOk) in
            if isOk == true {
                self.popVC()
            }
        }
    }

    func displayErrorAlertAndGoToHome(strMsg:String) {
        /*let alert = UIAlertController(title: AppName, message: strMsg, preferredStyle: .alert)
        let okButton = UIAlertAction(title: ObjKeymessages.kLABEL_OK, style: .default, handler: {(_ action: UIAlertAction) -> Void in
//            for controller in self.navigationController!.viewControllers {
//                if (controller is MainTabbarViewController) {
//                    let tmpController = controller as? MainTabbarViewController
//                    tmpController?.selectedIndex = 0
//                    self.navigationController?.popToViewController(controller, animated: false)
//                    break
//                }
//            }
        })
        alert.addAction(okButton)
        self.present(alert, animated: true) {() -> Void in }*/
        
        AppSingleton.shared.showCustomPopUpWithOkButton(strButtonTitle: ObjKeymessages.kLABEL_OK, strMessage: strMsg, showOnTopVC: false) { (isOk) in
        }
    }

    func displayAlertAndDismissScreen(string: String) {
        /*let alert = UIAlertController(title: AppName, message: string, preferredStyle: .alert)
        let okButton = UIAlertAction(title: ObjKeymessages.kLABEL_OK, style: .default, handler: {(_ action: UIAlertAction) -> Void in
            self.dismiss(animated: true, completion: nil)
        })
        alert.addAction(okButton)
        self.present(alert, animated: true) {() -> Void in }*/
        
        AppSingleton.shared.showCustomPopUpWithOkButton(strButtonTitle: ObjKeymessages.kLABEL_OK, strMessage: string, showOnTopVC: false) { (isOk) in
            if isOk == true {
                self.dismiss(animated: true, completion: nil)
            }
        }
    }

    func showThankyouAlert(strCardNumber: String, strCardType: String, strCardScheme: String, strCardToken: String, strTransactionId: String) {
        
        // call add card api in background
        if strCardToken != "" {
            self.addUserCard(strCardNumber: strCardNumber, strCardType: strCardType, strCardScheme: strCardScheme, strCardToken: strCardToken, strTransactionId: strTransactionId)
        }

        let vc = ProductPopupStoryboard.instantiate(ThankYouViewController.self)
        vc.modalPresentationStyle = .overFullScreen
        vc.modalTransitionStyle = .crossDissolve
        
        vc.isAddNewCard = self.isAddNewCard

        vc.completionPaymentCardBlock = { [weak self] (strAction, isAddNewCard) in
            guard let self = self else { return }
            self.dismiss(animated: false, completion: nil)
            if strAction == "Complete" {
                /*for controller in self.navigationController!.viewControllers {
                    if (controller is MainTabbarViewController) {
                        let tmpController = controller as? MainTabbarViewController
                        tmpController?.selectedIndex = 0
                        self.navigationController?.popToViewController(controller, animated: false)
                        break
                    }
                }*/
//                self.dismiss(animated: true, completion: nil)
                
                if isAddNewCard {
                    self.dismissScreen?()
                    self.dismiss(animated: false)
                }
            }
        }

        vc.completionBlock = { [weak self] (strAction) in
            guard let self = self else { return }
            self.dismiss(animated: false, completion: nil)
            if strAction == "Complete" {
                /*for controller in self.navigationController!.viewControllers {
                    if (controller is MainTabbarViewController) {
                        let tmpController = controller as? MainTabbarViewController
                        tmpController?.selectedIndex = 0
                        self.navigationController?.popToViewController(controller, animated: false)
                        break
                    }
                }*/
                self.dismiss(animated: true, completion: nil)
                self.dismissScreen?()
            }
        }
        delay(0.3) {
            UIApplication.topViewController()?.presentVC(vc)
        }
    }

    private func addUserCard(strCardNumber: String, strCardType: String, strCardScheme: String, strCardToken: String, strTransactionId: String) {
        var dictParam : [String:Any] = [:]
        dictParam["vToken"] = strCardToken
        dictParam["vTransactionId"] = strTransactionId
        dictParam["vPaymentDescription"] = strCardNumber
        dictParam["vCardType"] = strCardType
        dictParam["vCardScheme"] = strCardScheme

        if AppSingletonObj.isConnectedToNetwork(){
            self.presenterHistoryOrderDetails?.apiCallForAddCard(dictData: dictParam)
        }
    }

    private func setData() {
        lblOrderNo.text = objOrderDetails?.vOrderNumber
//        lblOrderDate.text = objOrderDetails?.tsOrderedAt
        // convert date to desired format
        let strDate = objOrderDetails?.tsOrderedAt
        let strFormattedDate = strDate?.utcToLocal(dateStr: strDate ?? "")
        lblOrderDate.text = strFormattedDate

        // 5 --> Order Delivered
        // 6 -->  Order Cancelled
        if objOrderDetails?.tiOrderStatus == OrderStatus.OrderCancelled.rawValue {
            lblStatus.text = ObjKeymessages.kLABEL_CANCELLED
            lblStatus.textColor = UIColor.AppTheme_SelectedTabColor_F4BA45
        }
        else {
            lblStatus.text = ObjKeymessages.kLABEL_DELIVERED
            lblStatus.textColor = UIColor.AppTheme_DiscountGreenColor_05B13E
        }

        tblOrders.reloadData()

//        lblSubTotal.text = "\(objOrderDetails?.dOrderSubTotal ?? "") \(ObjKeymessages.kLABEL_SAR)"
        let subTotalWithoutVat = Double(objOrderDetails?.dOrderSubTotal ?? "") ?? 0
        lblSubTotal.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: subTotalWithoutVat))")

        lblDelivery.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(objOrderDetails?.dDeliveryCharge ?? "")")
        lblTotal.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(objOrderDetails?.dTotalAmount ?? "")")

        let vatCost = Double(objOrderDetails?.dVatCharge ?? "") ?? 0
        lblVat.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "*(\(ObjKeymessages.kLABEL_VAT_INCLUDED) \(objOrderDetails?.dVatPercentage ?? "")%: \(forTrailingZero(temp: vatCost)))")

        // hide/show Reorder button
        /*if objOrderDetails?.iReccuringType == ReccuringType.OnlyOnce.rawValue {
            btnReOrder.isHidden = false
        }
        else {
            btnReOrder.isHidden = true
        }*/
        
        lblDiscountValue.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "-\(objOrderDetails?.dOrderDiscount ?? "")")
        if objOrderDetails?.iOrderDiscountType != 3 && objOrderDetails?.iOrderDiscountType != 0 {
            stackViewDiscount.isHidden = false
            lblLineDiscount.isHidden = false
        }
        else {
            stackViewDiscount.isHidden = true
            lblLineDiscount.isHidden = true
        }

        if objOrderDetails?.tiOrderStatus == OrderStatus.OrderDelivered.rawValue {  // Delivery is done, show the invoice button
            btnInvoice.isHidden = false
        }
        else {
            btnInvoice.isHidden = true
        }
        
        if objOrderDetails?.iOrderDiscountType == 3 || objOrderDetails?.dMinOrderFreeDelivery == 1 || objOrderDetails?.dHasFreeOrder == 1 {
            stackViewDeliveryDiscount.isHidden = false
            lblLineDeliveryDiscount.isHidden = false
            lblDeliveryDiscountValue.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value:  "-\(objOrderDetails?.dDistanceCostSetInAdmin ?? "")")
            lblDelivery.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(objOrderDetails?.dDistanceCostSetInAdmin ?? "")")
        }
        else {
            stackViewDeliveryDiscount.isHidden = true
            lblLineDeliveryDiscount.isHidden = true
            lblDeliveryDiscountValue.text = ""
        }

        lblDiscountTitle.text = "\(ObjKeymessages.kLABEL_DISCOUNT) - (\(objOrderDetails?.vPromocode ?? ""))"
        if objOrderDetails?.dMinOrderFreeDelivery == 1 || objOrderDetails?.dHasFreeOrder == 1 {
            lblDeliveryDiscountTitle.text = "\(ObjKeymessages.kLABEL_DELIVERY_DISCOUNT)"
        }
        else {
            lblDeliveryDiscountTitle.text = "\(ObjKeymessages.kLABEL_DELIVERY_DISCOUNT) - (\(objOrderDetails?.vPromocode ?? ""))"
        }

        lblType.text = objOrderDetails?.vType
        lblAddress.text = objOrderDetails?.txAddress
        
        if objOrderDetails?.vShiftDisplayName == nil || objOrderDetails?.vShiftDisplayName == "" {  // shift name is empty. This is old user.
            if objOrderDetails?.tiShiftType == ShiftType.Morning.rawValue { // Morning
                lblShift.text = ObjKeymessages.kLABEL_MORNING
            }
            else { // Evening
                lblShift.text = ObjKeymessages.kLABEL_EVENING
            }
        }
        else {
            lblShift.text = objOrderDetails?.vShiftDisplayName
        }

        if objOrderDetails?.tAdditionalNote == "" {  // hide notes view
            lblNotesTitle.isHidden = true
            viewNote.isHidden = true
            cons_bottom_viewNotes.constant = 0
        }
        else { // show notes view
            lblNotesTitle.isHidden = false
            viewNote.isHidden = false
            cons_bottom_viewNotes.constant = 40
        }
        lblNote.text = objOrderDetails?.tAdditionalNote

        if objOrderDetails?.tiTransactionType == 0 {  // No payment method, hide view
            viewPaymentMethod.isHidden = true
            cons_height_viewPaymentMethod.constant = 0
            lblPaymentMethodValue.text = ""
        }
        else {
            viewPaymentMethod.isHidden = false
            cons_height_viewPaymentMethod.constant = 94
            
            if objOrderDetails?.tiIsWalletUsed == 1 {  // partial payment from wallet
                if objOrderDetails?.tiTransactionType == TransactionType.Card.rawValue {  // Card
                    lblPaymentMethodValue.text = "\(ObjKeymessages.kLABEL_WALLET) + \(ObjKeymessages.kLABEL_CARD)"
                }
                else if objOrderDetails?.tiTransactionType == TransactionType.ApplePay.rawValue {  // Apple Pay
                    lblPaymentMethodValue.text = "\(ObjKeymessages.kLABEL_WALLET) + \(ObjKeymessages.kLABEL_APPLE_PAY)"
                }
                else if objOrderDetails?.tiTransactionType == TransactionType.COD.rawValue {  // Card on delivery
                    lblPaymentMethodValue.text = "\(ObjKeymessages.kLABEL_WALLET) + \(ObjKeymessages.kLABEL_CARD_ON_DELIVERY)"
                }
                else if objOrderDetails?.tiTransactionType == TransactionType.Wallet.rawValue {  // Wallet
                    lblPaymentMethodValue.text = ObjKeymessages.kLABEL_WALLET
                }
                else if objOrderDetails?.tiTransactionType == TransactionType.Coins.rawValue {  // Wallet
                    lblPaymentMethodValue.text = ObjKeymessages.kLABEL_Coins
                }
            }
            else {
                if objOrderDetails?.tiTransactionType == TransactionType.Card.rawValue {  // Card
                    lblPaymentMethodValue.text = ObjKeymessages.kLABEL_CARD
                }
                else if objOrderDetails?.tiTransactionType == TransactionType.ApplePay.rawValue {  // Apple Pay
                    lblPaymentMethodValue.text = ObjKeymessages.kLABEL_APPLE_PAY
                }
                else if objOrderDetails?.tiTransactionType == TransactionType.COD.rawValue {  // Card on delivery
                    lblPaymentMethodValue.text = ObjKeymessages.kLABEL_CARD_ON_DELIVERY
                }
                else if objOrderDetails?.tiTransactionType == TransactionType.Wallet.rawValue {  // Wallet
                    lblPaymentMethodValue.text = ObjKeymessages.kLABEL_WALLET
                }
                else if objOrderDetails?.tiTransactionType == TransactionType.Coins.rawValue {  // Wallet
                    lblPaymentMethodValue.text = ObjKeymessages.kLABEL_Coins
                }
            }
        }

        // Order Type
        if objOrderDetails?.iReccuringType == ReccuringType.OnlyOnce.rawValue {
            lblOrderTypeValue.text = ObjKeymessages.kLABEL_ONLY_ONCE
        }
        else if objOrderDetails?.iReccuringType == ReccuringType.EveryWeek.rawValue {
            lblOrderTypeValue.text = ObjKeymessages.kLABEL_EVERY_WEEK
        }
        else if objOrderDetails?.iReccuringType == ReccuringType.Every2Weeks.rawValue {
            lblOrderTypeValue.text = ObjKeymessages.kLABEL_EVERY_TWO_WEEKS
        }
        else if objOrderDetails?.iReccuringType == ReccuringType.EveryMonth.rawValue {
            lblOrderTypeValue.text = ObjKeymessages.kLABEL_EVERY_MONTH
        }

    }

}

// PayTabs
extension HistoryOrderDetailsViewController {
    
    func payWithCard(flagCardOrNot: Bool, strCardToken: String, strCardTransactionReference: String) {
        var strAddress = ""
        var strCity = ""
        var strZipcode = ""
        var strState = ""
        var strCountryCode = ""

        if let address = UserDefaults.standard.object(forKey: USERDEFAULTS_INFO_KEY.CURRENT_ADDRESS) as? [String:Any],let data = address.data {
            let decodeResult: (decodableObj: AddressResponseFields?, error: Error?) = CodableHelper.decode(AddressResponseFields.self, from: data)
            if let decodeObj = decodeResult.decodableObj {
                strAddress = decodeObj.txAddress ?? ""
                strCity = decodeObj.vCity ?? ""
                strZipcode = decodeObj.vZipCode ?? ""
                strState = decodeObj.vState ?? ""
                strCountryCode = decodeObj.vCountryCode ?? ""

            }
        }

        var billingDetails: PaymentSDKBillingDetails! {
            return PaymentSDKBillingDetails(name: User.shared.vName,
                                         email: User.shared.vEmailId,
                                            phone: "\(User.shared.vISDCode ?? "")\(User.shared.vMobileNumber ?? "")",
                                         addressLine: strAddress,
                                         city: strCity,
                                         state: strState,
                                         countryCode: strCountryCode,
                                         zip: strZipcode)
        }

        var configuration: PaymentSDKConfiguration! {
            let theme = PaymentSDKTheme.default
            theme.logoImage = UIImage(named: "logo_login_white")   // icn_home
            theme.secondaryColor = UIColor.AppTheme_BlueColor_012CDA
            theme.secondaryFontColor = UIColor.AppTheme_BlueColor_012CDA
            theme.primaryFontColor = .black
            theme.strokeColor = UIColor.AppTheme_BlueColor_012CDA
            theme.buttonColor = UIColor.AppTheme_BlueColor_012CDA
            theme.titleFontColor = .black
            theme.buttonFontColor = .white

            var strToken = ""
            var strTransactionReference = ""
            if flagCardOrNot == false {
                strToken = ""
                strTransactionReference = ""
            }
            else {
                strToken = strCardToken
                strTransactionReference = strCardTransactionReference
            }

            
            
            return PaymentSDKConfiguration(profileID: profileID,
                                           serverKey: serverKey,
                                           clientKey: clientKey,
                                           currency: SACurrencyCode,
                                           amount: Double(reOrderAmount) ?? 0.0,
                                           merchantCountryCode: MerchantCountryCode)
                .cartDescription("cart description")
            .cartID("\(orderId)")
                .screenTitle(AppName)
                .theme(theme)
                .showBillingInfo(true)
                .hideCardScanner(true)
                .languageCode(UserDefaults.standard.getLanguage() ?? "")
                .tokeniseType(.userOptinoal)
                .tokenFormat(.hex32)
                .token(strToken)
                .transactionReference(strTransactionReference)
                .billingDetails(billingDetails)
        }

//        configuration.showBillingInfo = true
//        configuration.showShippingInfo = true
        
        PaymentManager.startCardPayment(on: self, configuration: configuration,
                                 delegate: self)

    }
    
    func payWithApplePay() {
        
        var strAddress = ""
        var strCity = ""
        var strZipcode = ""
        var strState = ""
        var strCountryCode = ""

        if let address = UserDefaults.standard.object(forKey: USERDEFAULTS_INFO_KEY.CURRENT_ADDRESS) as? [String:Any],let data = address.data {
            let decodeResult: (decodableObj: AddressResponseFields?, error: Error?) = CodableHelper.decode(AddressResponseFields.self, from: data)
            if let decodeObj = decodeResult.decodableObj {
                strAddress = decodeObj.txAddress ?? ""
                strCity = decodeObj.vCity ?? ""
                strZipcode = decodeObj.vZipCode ?? ""
                strState = decodeObj.vState ?? ""
                strCountryCode = decodeObj.vCountryCode ?? ""

            }
        }

        var strEmail = ""
        if User.shared.vEmailId == "" {  // use default email id
            strEmail = DefaultEmailForApplePay
        }
        else {
            strEmail = User.shared.vEmailId ?? ""
        }

        var billingDetails: PaymentSDKBillingDetails! {
            return PaymentSDKBillingDetails(name: User.shared.vName,
                                         email: strEmail,
                                            phone: "\(User.shared.vISDCode ?? "")\(User.shared.vMobileNumber ?? "")",
                                         addressLine: strAddress,
                                         city: strCity,
                                         state: strState,
                                         countryCode: strCountryCode,
                                         zip: strZipcode)
        }

        var shippingDetails: PaymentSDKShippingDetails! {
            return PaymentSDKShippingDetails(name: User.shared.vName,
                                             email: strEmail,
                                             phone: "\(User.shared.vISDCode ?? "")\(User.shared.vMobileNumber ?? "")",
                                             addressLine: strAddress,
                                             city: strCity,
                                             state: strState,
                                             countryCode: strCountryCode,
                                             zip: strZipcode)
        }
        
        var applePayConfiguration: PaymentSDKConfiguration! {
            return PaymentSDKConfiguration(profileID: profileID,
                                           serverKey: serverKey,
                                           clientKey: clientKey,
                                           currency: SACurrencyCode,
                                           amount: Double(reOrderAmount) ?? 0.0,
                                           merchantCountryCode: MerchantCountryCode)
                .cartDescription("cart description ApplePay")
                .cartID("\(orderId)")
                .screenTitle(AppName)
                .languageCode(UserDefaults.standard.getLanguage() ?? "")
                .merchantName("Material")
                .merchantAppleBundleID(PAYTABS_MERCHANT_IDENTIFIER)
                .simplifyApplePayValidation(true)
                .billingDetails(billingDetails)
                .shippingDetails(shippingDetails)

        }

        PaymentManager.startApplePayPayment(on: self,
                                     configuration: applePayConfiguration,
                                     delegate: self)

    }

}

extension HistoryOrderDetailsViewController: PaymentManagerDelegate {
    
    func paymentManager(didFinishTransaction transactionDetails: PaymentSDKTransactionDetails?, error: Error?) {
        if let transactionDetails = transactionDetails {
            print("Response Code: " + (transactionDetails.paymentResult?.responseCode ?? ""))
            print("Result: " + (transactionDetails.paymentResult?.responseMessage ?? ""))
            print("Token: " + (transactionDetails.token ?? ""))
            print("Transaction Reference: " + (transactionDetails.transactionReference ?? ""))
            print("Transaction Time: " + (transactionDetails.paymentResult?.transactionTime ?? "" ))
            
            var trans_type = 0
            if transactionDetails.cartDescription == "cart description ApplePay" {  // apple pay
                trans_type = 2
            }
            else {  // card
                trans_type = 1
            }

            if transactionDetails.isSuccess() {
                print("Successful transaction")
                
                // call update transaction api for successful transaction
                self.updateTransaction(strTransactionReference: (transactionDetails.transactionReference ?? ""), paymentStatus: 1, strCardNumber: (transactionDetails.paymentInfo?.paymentDescription ?? ""), strCardType: (transactionDetails.paymentInfo?.cardType ?? ""), strCardScheme: (transactionDetails.paymentInfo?.cardScheme ?? ""), strCardToken: (transactionDetails.token ?? ""), transactionType: trans_type)

                
            }
            else {
                print("Transaction failed")
                
                // call update transaction api for failed transaction
                self.updateTransaction(strTransactionReference: (transactionDetails.transactionReference ?? ""), paymentStatus: 2, strCardNumber: (transactionDetails.paymentInfo?.paymentDescription ?? ""), strCardType: (transactionDetails.paymentInfo?.cardType ?? ""), strCardScheme: (transactionDetails.paymentInfo?.cardScheme ?? ""), strCardToken: (transactionDetails.token ?? ""), transactionType: trans_type)
            }
        } else if let error = error {
//            showError(message: error.localizedDescription)
            
            // call update transaction api for failed transaction
            self.updateTransaction(strTransactionReference: (""), paymentStatus: 2, strCardNumber: (""), strCardType: (""), strCardScheme: "", strCardToken: "", transactionType: 0)
        }
    }
    
    func showError(message: String) {
        DispatchQueue.main.async {
            /*let alertController = UIAlertController.init(title: self.title, message: message, preferredStyle: .alert)
            alertController.addAction(UIAlertAction.init(title: "Ok", style: .cancel, handler: nil))
            self.present(alertController, animated: true, completion: nil)*/
            
            AppSingleton.shared.showCustomPopUpWithOkButton(strButtonTitle: ObjKeymessages.kLABEL_OK, strMessage: message, showOnTopVC: false) { (isOk) in
            }
        }
    }

    func updateTransaction(strTransactionReference: String, paymentStatus: Int, strCardNumber: String, strCardType: String, strCardScheme: String, strCardToken: String, transactionType: Int) {
        if AppSingletonObj.isConnectedToNetwork(){
            var dictParam : [String:Any] = [:]
            dictParam["iOrderId"] = reOrderId
            dictParam["tiTransactionType"] = transactionType
            dictParam["vTransactionRef"] = strTransactionReference
            dictParam["iPaymentStatus"] = paymentStatus

            print(dictParam)
            
            self.presenterHistoryOrderDetails?.apiCallForUpdateTransaction(dictData: dictParam, paymentStatus: paymentStatus, strCardNumber: strCardNumber, strCardType: strCardType, strCardScheme: strCardScheme, strCardToken: strCardToken)
        }

    }
}
