
import UIKit

protocol SelectLanguageInteractorProtocol : AnyObject {
    func apiCallForChangeLang(dictData:[String:Any])

}

protocol SelectLanguageDataStore {
    //{ get set }
}

class SelectLanguageInteractor: SelectLanguageInteractorProtocol, SelectLanguageDataStore {

    //MARK: - Objects & Variables -
    weak var presenterSelectLanguage: SelectLanguagePresentationProtocol?
    
    func apiCallForChangeLang(dictData:[String:Any]) {
        
        ActivityIndicator.shared.showCentralSpinner()

        let authorization = User.shared.checkUserLoginStatus() ? getAuthorizationText() : GuestAccessToken
        
        var strLanguage = UserAPI.VLanguage_userLanguage.en
        if setDataInString(dictData["vLanguage"] as AnyObject) == UserAPI.VLanguage_userLanguage.en.rawValue {
            strLanguage = UserAPI.VLanguage_userLanguage.en
        }
        else if setDataInString(dictData["vLanguage"] as AnyObject) == UserAPI.VLanguage_userLanguage.ar.rawValue {
            strLanguage = UserAPI.VLanguage_userLanguage.ar
        }

        var strDeviceToken = ""
        if vDeviceToken == "" {
//            AppDelegate.refreshToken()
            strDeviceToken = AppDelegate.getDeviceToken()
        }
        else {
            strDeviceToken = vDeviceToken
        }

        UserAPI.userLanguage(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, vLanguage: strLanguage, vDeviceToken: strDeviceToken) { data, error in
            ActivityIndicator.shared.hideCentralSpinner()
            self.presenterSelectLanguage?.apiResponseChangeLang(response: data, error: error, strLang: setDataInString(dictData["vLanguage"] as AnyObject))

        }
        
    }

}
