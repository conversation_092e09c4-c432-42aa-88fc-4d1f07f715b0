
import UIKit

protocol SelectLanguagePresentationProtocol : AnyObject {
    func apiCallForChangeLang(dictData:[String:Any])
    func apiResponseChangeLang(response:UpdateLanguageResponse?,error:Error?, strLang: String)

}

class SelectLanguagePresenter: SelectLanguagePresentationProtocol {
    
    //MARK: - Objects & Variables -
    weak var viewControllerSelectLanguage: SelectLanguageProtocol?
    var interactorSelectLanguage: SelectLanguageInteractorProtocol?
    
    func apiCallForChangeLang(dictData:[String:Any]) {
        interactorSelectLanguage?.apiCallForChangeLang(dictData: dictData)
    }

    func apiResponseChangeLang(response:UpdateLanguageResponse?,error:Error?, strLang: String) {
//        if let vc = self.viewControllerSelectLanguage as? BaseViewController {
//            DispatchQueue.main.async {
//                vc.endRefresing()
//            }
//        }
        if let error = error  {
            viewControllerSelectLanguage?.displayAlert(string: error.localizedDescription)
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            //            viewController?.displayAlert(strTitle: "", strMessage: KeyMessages.kInvalidateResponse)
            return
        }
        
        if code == APIINVALIDAUTHORIZATIONCODE401 {
//            AppSingletonObj.goToLoginScreen(message: response.responseMessage ?? "")
            AppSingletonObj.displaySessionExpiredAlert(strMsg: response.responseMessage ?? "")
            return
        }

        if code == APICODE400 {
            viewControllerSelectLanguage?.displayAlert(string: response.responseMessage ?? "")
            return
        }
        
//        guard let model = response.responseData,code == APISUCCESSCODE200  else {
//            return
//        }
        self.viewControllerSelectLanguage?.goToHomeScreen(strMessage: response.responseMessage ?? "", strLang: strLang)
    }

}
