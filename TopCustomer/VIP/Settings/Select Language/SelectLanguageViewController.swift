
import UIKit

protocol SelectLanguageProtocol: AnyObject {
    func displayAlert(string:String)
//    func displaySessionExpiredAlert(strMsg: String)
    func goToHomeScreen(strMessage :  String, strLang: String)

}

class SelectLanguageViewController: BottomPopupViewController, SelectLanguageProtocol {

    //MARK: - Properties -
    var presenterSelectLanguage: SelectLanguagePresentationProtocol?

    //MARK: - IBOutlets -
    @IBOutlet weak var lblSelectLang: UILabel!
    @IBOutlet weak var btnCancel: UIButton!
    
    
    //MARK: - Object lifecycle
    override var popupHeight: CGFloat { return 260 }
    
    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
        setup()
    }
     
    
    //MARK: - View Life Cycle -
    override func viewDidLoad() {
        super.viewDidLoad()
        
        // Mixpanel
        MixpanelEvents.sharedInstance.logTimeEvent(strScreenName: ScreenNames.LanguageSelectionScreen.rawValue)

        lblSelectLang.text = ObjKeymessages.kLABEL_SELECT_LANGUAGE
        btnCancel.setTitle(ObjKeymessages.kLABEL_CANCEL, for: .normal)
        
        initialSetUp()
    }
    
    override func viewDidDisappear(_ animated: Bool) {
        MixpanelEvents.sharedInstance.trackTimeEvent(strScreenName: ScreenNames.LanguageSelectionScreen.rawValue)
    }

    //MARK: - Memory Management -
    override func didReceiveMemoryWarning() {
        super.didReceiveMemoryWarning()
        print("didReceiveMemoryWarning : \(self.classForCoder)")
    }
    
    //MARK: - DeInit -
    deinit {
        print("DeAllocated :  \(self.classForCoder)")
    }
    
    //MARK: Our Own Functions
    
    //MARK: - Actions
    @IBAction func actionEnglish(_ sender: UIButton) {
        if UserDefaults.standard.getLanguage() != UserAPI.VLanguage_userLanguage.en.rawValue {
//            self.setLang(strLangCode: "en")
            self.changeLangAPI(strLanguage: UserAPI.VLanguage_userLanguage.en.rawValue)
        }
//        self.dismissVC {
//        }
    }
    
    @IBAction func actionArabic(_ sender: UIButton) {
        if UserDefaults.standard.getLanguage() != UserAPI.VLanguage_userLanguage.ar.rawValue {
//            self.setLang(strLangCode: "ar")
            self.changeLangAPI(strLanguage: UserAPI.VLanguage_userLanguage.ar.rawValue)
        }
//        self.dismissVC {
//        }
    }
    
    private func changeLangAPI(strLanguage: String) {
        var dictParam : [String:Any] = [:]
        dictParam["vLanguage"] = strLanguage

        if AppSingletonObj.isConnectedToNetwork(){
            self.presenterSelectLanguage?.apiCallForChangeLang(dictData: dictParam)
        }
    }

    private func setLang(strLangCode: String) {
        UserDefaults.standard.setLanguage(language: strLangCode)
        let objKeymessages: KeyMessages = KeyMessages()
        ObjKeymessages = objKeymessages
        CurrentAppLang = strLangCode
        if strLangCode == UserAPI.VLanguage_userLanguage.ar.rawValue {
            UIView.appearance().semanticContentAttribute = .forceRightToLeft
            UIWindow.appearance().semanticContentAttribute = .forceRightToLeft
            CountrySelectView.appearance().semanticContentAttribute = .forceRightToLeft
            UICollectionView.appearance().semanticContentAttribute = .forceRightToLeft
            UICollectionViewCell.appearance().semanticContentAttribute = .forceRightToLeft
        }
        else {
            UIView.appearance().semanticContentAttribute = .forceLeftToRight
            UIWindow.appearance().semanticContentAttribute = .forceLeftToRight
            CountrySelectView.appearance().semanticContentAttribute = .forceLeftToRight
            UICollectionView.appearance().semanticContentAttribute = .forceLeftToRight
            UICollectionViewCell.appearance().semanticContentAttribute = .forceLeftToRight
        }

        AppDel?.restartAppAfterChangingLang()
    }
    
    @IBAction func actionCancel(_ sender: UIButton) {
        self.dismissVC {
        }
    }
        
    //MARK: - Protocol Functions -
    private func initialSetUp() {
        self.view.dropShadow()
    }
    
    func displayAlert(string: String) {
//        self.showAlert(title: AppName, message: string)
        AppSingleton.shared.showCustomPopUpWithOkButton(strButtonTitle: ObjKeymessages.kLABEL_OK, strMessage: string, showOnTopVC: false) { (isOk) in
        }
    }

    /*func displaySessionExpiredAlert(strMsg: String) {
        let alert = UIAlertController(title: AppName, message: strMsg, preferredStyle: .alert)
        let okButton = UIAlertAction(title: ObjKeymessages.kLABEL_OK, style: .default, handler: {(_ action: UIAlertAction) -> Void in
            AppDel?.restartApp()
            self.dismiss(animated: true, completion: nil)
        })
        alert.addAction(okButton)
        self.present(alert, animated: true) {() -> Void in }
    }*/

    func goToHomeScreen(strMessage :  String, strLang: String) {
        self.setLang(strLangCode: strLang)
        self.dismissVC {
        }

    }

}

//MARK: - Extensions

extension SelectLanguageViewController {
    //MARK: - VIP Setup -
    /// VIP Setup for SelectLanguageViewController
    private func setup() {
        let viewController = self
        let interactor = SelectLanguageInteractor()
        let presenter = SelectLanguagePresenter()
        
        //View Controller will communicate with only presenter
        viewController.presenterSelectLanguage = presenter
        
        //Presenter will communicate with Interector and Viewcontroller
        presenter.viewControllerSelectLanguage = viewController
        presenter.interactorSelectLanguage = interactor
        
        //Interactor will communucate with only presenter.
        interactor.presenterSelectLanguage = presenter
    }
}

extension UIView {

  func dropShadow(scale: Bool = true) {
    layer.masksToBounds = false
    layer.shadowColor = UIColor.black.cgColor
    layer.shadowOpacity = 0.5
    layer.shadowOffset = CGSize(width: -1, height: 1)
    layer.shadowRadius = 5.0

    layer.shadowPath = UIBezierPath(rect: bounds).cgPath
    layer.shouldRasterize = true
    layer.rasterizationScale = scale ? UIScreen.main.scale : 1
  }

}
