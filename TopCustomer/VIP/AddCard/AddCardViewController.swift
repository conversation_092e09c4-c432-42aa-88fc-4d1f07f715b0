
import UIKit

protocol AddCardProtocol: AnyObject {
   
}

class AddCardViewController: UIViewController, AddCardProtocol {

    // MARK: Objects & Variables
    var presenterAddCard: AddCardPresentationProtocol?

    // MARK: IBOutlets
    @IBOutlet weak var btnCancel: UIButton!
    @IBOutlet weak var lblTitle: UILabel!
    @IBOutlet weak var lblCardNo: UILabel!
    @IBOutlet weak var lblName: UILabel!
    @IBOutlet weak var lblExpiryDate: UILabel!
    @IBOutlet weak var lblCVVCode: UILabel!
    @IBOutlet weak var btnAdd: CustomRoundedButtton!
    @IBOutlet weak var txtName: CustomTextfieldWithFontStyle!
    
    
    // MARK: Object lifecycle
    /*
    override init(nibName nibNameOrNil: String?, bundle nibBundleOrNil: Bundle?) {
        super.init(nibName: nibNameOrNil, bundle: nibBundleOrNil)
        setup()
    }
    */
    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
        setup()
    }
    
    // MARK: Setup
    
    private func setup() {
        let viewController = self
        let interactor = AddCardInteractor()
        let presenter = AddCardPresenter()
        
        //View Controller will communicate with only presenter
        viewController.presenterAddCard = presenter
        
        //Presenter will communicate with Interector and Viewcontroller
        presenter.viewControllerAddCard = viewController
        presenter.interactorAddCard = interactor
        
        //Interactor will communucate with only presenter.
        interactor.presenterAddCard = presenter
    }
    
    
    // MARK: View lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        self.setTexts()
    }
    
    private func setTexts() {
        lblTitle.text = ObjKeymessages.kLABEL_ADD_NEW_CARD
        lblCardNo.text = ObjKeymessages.kLABEL_CARD_NUMBER
        lblName.text = ObjKeymessages.kLABEL_NAME
        lblExpiryDate.text = ObjKeymessages.kLABEL_EXPIRY_DATE
        lblCVVCode.text = ObjKeymessages.kLABEL_CVV_CODE
        btnAdd.setTitle(ObjKeymessages.kLABEL_ADD, for: .normal)
        btnCancel.setTitle(ObjKeymessages.kLABEL_CANCEL, for: .normal)

    }
    
    @IBAction func btnBackAction(_ sender: Any) {
        self.dismissVC {
            
        }
    }

}

extension AddCardViewController : UITextFieldDelegate {
    
    func textField(_ textField: UITextField, shouldChangeCharactersIn range: NSRange, replacementString string: String) -> Bool {
        if textField == txtName {
            let newLength: Int = (textField.text?.length)! + string.length - range.length
            if newLength > MaxNameLength {
                return false
            }
            return true
        }
        else {
            return true
        }
    }
    
}
