
import UIKit

class AnimationTest: UIViewController {

    @IBOutlet weak var imgOrderReceived: UIImageView!
    @IBOutlet weak var imgOrderReady: UIImageView!
    @IBOutlet weak var imgOrderOnTheWay: UIImageView!
    @IBOutlet weak var imgOrderDelivered: UIImageView!
    @IBOutlet weak var viewLineFromReceived: UIView!
    @IBOutlet weak var viewLineFromReady: UIView!
    @IBOutlet weak var viewLineFromOnTheWay: UIView!

    @IBOutlet weak var stackViewMain: UIStackView!
    
    @IBOutlet weak var cons_viewLine_height: NSLayoutConstraint!
    
    
    /*
case OrderPlaced = 1
case OrderAssigned = 2
case OrderInProgress = 3
case OrderReady = 4
case OrderOnTheWay = 5
case OrderDelivered = 6
case OrderCancelled = 7
*/
    
    var orderStatus = 4
    let duration = 1.0

    override func viewDidLoad() {
        super.viewDidLoad()

        animateView(start: 0, end: orderStatus)
        
        
        /*if orderStatus == 1 || orderStatus == 2 || orderStatus == 3 {
            imgOrderReceived.image = UIImage(named: "status_selected")
            imgOrderReady.image = UIImage(named: "status_deselected")
            imgOrderOnTheWay.image = UIImage(named: "status_deselected")
            imgOrderDelivered.image = UIImage(named: "status_deselected")

            self.animateToCurrentOrderStatus(index: 0)

        }
        else if orderStatus == 4 {
            imgOrderReceived.image = UIImage(named: "status_selected")
            imgOrderReady.image = UIImage(named: "status_selected")
            imgOrderOnTheWay.image = UIImage(named: "status_deselected")
            imgOrderDelivered.image = UIImage(named: "status_deselected")

            self.animateToCurrentOrderStatus(index: 1)

        }
        else if orderStatus == 5 {
            imgOrderReceived.image = UIImage(named: "status_selected")
            imgOrderReady.image = UIImage(named: "status_selected")
            imgOrderOnTheWay.image = UIImage(named: "status_selected")
            imgOrderDelivered.image = UIImage(named: "status_deselected")

            self.animateToCurrentOrderStatus(index: 2)

        }
        else if orderStatus == 6 {
            imgOrderReceived.image = UIImage(named: "status_selected")
            imgOrderReady.image = UIImage(named: "status_selected")
            imgOrderOnTheWay.image = UIImage(named: "status_selected")
            imgOrderDelivered.image = UIImage(named: "status_selected")

            viewLineFromOnTheWay.backgroundColor = UIColor.AppTheme_BlueColor_012CDA
            self.animateToCurrentOrderStatus(index: 3)

        }*/
        
    }
    
    func animateToCurrentOrderStatus(index: Int) {
        
        if index == 0 {  // order received
            let subStackView = stackViewMain.subviews[index]
            let arrSubViews = subStackView.subviews
            let imageView = arrSubViews[0] as? UIImageView
            imageView?.image = UIImage(named: "status_selected")
            imageView?.blink()
        }
        else {
    //        var count = 0
            let subStackView = stackViewMain.subviews[index]
            print(subStackView)
            let arrSubViews = subStackView.subviews
            print(arrSubViews)
            let imageView = arrSubViews[0] as? UIImageView
            imageView?.image = UIImage(named: "status_selected")
            let arrInnerSubView = arrSubViews[1].subviews
            let viewLine = arrInnerSubView[1]
            
            viewLine.layoutIfNeeded()
            viewLine.frame.size.height = 0
            UIView.animate(withDuration: 1.0, delay: 0.0, options: .curveEaseInOut, animations: {
                viewLine.size = CGSize(width: viewLine.frame.width, height: 70)
                viewLine.layoutIfNeeded()
            }, completion: {_ in
    //            count += 1
    //            if count < index {
    //                self.animateToCurrentOrderStatus(index: count)
    //            }
    //            imageView?.blink()
            })

        }
        

        
    }
    
    func animateView(start: Int, end: Int) {
                        
            guard start < stackViewMain.subviews.count else { return }
            guard start < end else { return }
            
            let segmentView = stackViewMain.subviews[start]
            segmentView.subviews.forEach { segment in
                
                if let imageView = segment as? UIImageView {
                    imageView.image = UIImage(named: "status_selected")
                    
                    if start < end - 1 {
                        debugPrint("No Animation")
                        imageView.layer.removeAllAnimations()
                        
                    } else {
                        imageView.alpha = 0
                        UIView.animate(withDuration: duration, delay: 0, options: [.curveEaseInOut, .repeat, .autoreverse]) {
                            imageView.alpha = 1
                        }
                    }
                    
                } else {
                    guard start < end - 1 else {
                        return
                    }
                    guard let bgView = segment.viewWithTag(-1), let frontView = segment.viewWithTag(1) else {
                        debugPrint("Not matched..!!")
                        return
                    }
                    
                    bgView.backgroundColor = .lightGray
                    frontView.backgroundColor = .lightGray
                    
                    frontView.frame = bgView.frame
                    frontView.frame.size.height = 0
                    
                    DispatchQueue.main.asyncAfter(deadline: .now() + (start < end - 1 ? 0 : (duration / 2))) { [weak self] in
                        
                        UIView.animate(withDuration: self?.duration ?? 0.0, delay: 0, options: .curveEaseInOut) {
                            frontView.backgroundColor = UIColor.AppTheme_BlueColor_012CDA
                            frontView.frame.size.height = bgView.frame.height
                            
                        } completion: { [weak self] _ in
                            self?.animateView(start: start + 1, end: end)
                        }
                    }
                }
            }
        }
}
