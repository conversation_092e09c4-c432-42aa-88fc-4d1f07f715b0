
import UIKit

protocol AddAddressInteractorProtocol {
    func apiCallForAddAddress(dictData:[String:Any])

}

protocol AddAddressDataStore {
    //{ get set }
}

class AddAddressInteractor: AddAddressInteractorProtocol, AddAddressDataStore {

    // MARK: Objects & Variables
    var presenterAddAddress: AddAddressPresentationProtocol?
    
    func apiCallForAddAddress(dictData:[String:Any]) {
        ActivityIndicator.shared.showCentralSpinner()
        
        let authorization = getAuthorizationText()
        
        print(dictData["dLatitude"] as? Double ?? 0.0 , dictData["dLongitude"] as? Double ?? 0.0)
        
        if authorization == "Bearer " {
            // Save Lat , Lng in User Def.
            saveGuestLocation(GuestLocation(lat: dictData["dLatitude"] as? String ?? "", lng: dictData["dLongitude"] as? String ?? "", addressName: dictData["vTypeTitle"] as? String ?? ""))
            self.presenterAddAddress?.ReturnAddressFromGuest()
        }else{
            UserAPI.addAddress(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, iTypeId: nil, txAddress: setDataInString(dictData["txAddress"] as AnyObject), vTypeTitle: setDataInString(dictData["vTypeTitle"] as AnyObject), vZipCode: setDataInString(dictData["vZipCode"] as AnyObject), txAptSuite: setDataInString(dictData["txAptSuite"] as AnyObject), dLatitude: setDataInString(dictData["dLatitude"] as AnyObject), dLongitude: setDataInString(dictData["dLongitude"] as AnyObject), vCity: setDataInString(dictData["vCity"] as AnyObject), vState: setDataInString(dictData["vState"] as AnyObject), vCountryCode: setDataInString(dictData["vCountryCode"] as AnyObject), vIsMosques: setDataInString(dictData["isMosques"] as AnyObject)) { data, error in
                
                    ActivityIndicator.shared.hideCentralSpinner()
                    self.presenterAddAddress?.apiResponseAddAddress(response: data, error: error)

            }
        }
        
    }

}
