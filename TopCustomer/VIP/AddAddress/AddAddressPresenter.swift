
import UIKit

protocol AddAddressPresentationProtocol {
    func checkValidation(dictData:[String:Any]) -> Bool
    func apiCallForAddAddress(dictData:[String:Any])
    func apiResponseAddAddress(response:AddressResponse?,error:Error?)
    func ReturnAddressFromGuest()

}

class AddAddressPresenter: AddAddressPresentationProtocol {
    
    // MARK: Objects & Variables
    weak var viewControllerAddAddress: AddAddressProtocol?
    var interactorAddAddress: AddAddressInteractorProtocol?
    
    func checkValidation(dictData:[String:Any]) -> Bool {
        if setDataInString(dictData["txAddress"] as AnyObject).isBlank {
            AppSingletonObj.showAlert(strMessage: ObjKeymessages.kMSG_EMPTY_DETECTED_ADDRESS)
            return false
        }
        else if setDataInString(dictData["vZipCode"] as AnyObject).isBlank {
            AppSingletonObj.showAlert(strMessage: ObjKeymessages.kMSG_EMPTY_ZIPCODE)
            return false
        }
        else if setDataInString(dictData["vTypeTitle"] as AnyObject).isBlank {
            AppSingletonObj.showAlert(strMessage: ObjKeymessages.kMSG_EMPTY_LOCATION_NAME)
            return false
        }
        return true
    }

    func apiCallForAddAddress(dictData:[String:Any]) {
        self.interactorAddAddress?.apiCallForAddAddress(dictData: dictData)
    }

    func apiResponseAddAddress(response:AddressResponse?,error:Error?) {
        if let error = error  {
            AppSingletonObj.showAlert(strMsg: error.localizedDescription)
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            return
        }
        
        if code == APIINVALIDAUTHORIZATIONCODE401 {
            if Constant.shared.SELECTED_ADDRESS_NAME != "" {
                Constant.shared.SELECTED_ADDRESS_ID = response.responseData?.iAddressId ?? 0
                self.viewControllerAddAddress?.selectDeliveryType()
            } else {
                AppSingletonObj.displaySessionExpiredAlert(strMsg: response.responseMessage ?? "")
            }
            return
        }

        if code == APICODE400 {
            self.viewControllerAddAddress?.displayAlertWithoutDismiss(strMsg:response.responseMessage ?? "")
            return
        }

        guard let model = response.responseData,code == APISUCCESSCODE200  else {
            return
        }
        if Constant.shared.SELECTED_ADDRESS_NAME != "" {
            Constant.shared.SELECTED_ADDRESS_ID = response.responseData?.iAddressId ?? 0
            self.viewControllerAddAddress?.selectDeliveryType()
        } else {
            self.viewControllerAddAddress?.displayAlert(strMsg: response.responseMessage ?? "")
        }
    }
    
    func ReturnAddressFromGuest() {
        if isGuestLocationSaved() {
            self.viewControllerAddAddress?.selectDeliveryType()
        } else {
            AppSingletonObj.displaySessionExpiredAlert(strMsg: "Please Choose Address Correctly")
        }
    }

}
