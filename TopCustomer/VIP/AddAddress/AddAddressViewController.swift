
import UIKit
import GoogleMaps
import GooglePlaces
import ActionSheetPicker_3_0
import TikTokBusinessSDK
import FirebaseAnalytics
import SwiftUI

protocol AddAddressProtocol: AnyObject {
    func displayAlert(strMsg: String)
    func displayAlertWithoutDismiss(strMsg: String)
    func selectDeliveryType()
}

class AddAddressViewController: BaseViewController {

    // MARK: Objects & Variables
    var presenterAddAddress: AddAddressPresentationProtocol?

    // MARK: IBOutlets
    var gmsMapView : GMSMapView = {
        let view = GMSMapView(x: 0, y: 0, w: 370, h: 317)
        view.backgroundColor = .white
        view.translatesAutoresizingMaskIntoConstraints = false
        return view
    }()
    @IBOutlet weak var mapContainerView: UIView!
    @IBOutlet weak var txtLocationName: UITextField!
    @IBOutlet weak var lblDeliveryLocationTitle: UILabel!
    @IBOutlet weak var lblLocationNameTitle: UILabel!
    @IBOutlet weak var btnSave: CustomRoundedButtton!
    @IBOutlet weak var lblAddressDetected: UILabel!
    @IBOutlet weak var lblCity: UILabel!
    @IBOutlet weak var lblMosqueTitle: MaterialLocalizeLable!
    @IBOutlet weak var lblMosqueName: UILabel!
    
    // MARK: - Variables
    var refreshAddressList: (()->())?
    fileprivate var strZipcode = ""
    fileprivate var strState = ""
    fileprivate var strCountryCode = ""
    let rect = GMSMutablePath()
    var mosquesArr:[Dictionary<String, AnyObject>] = Array()
    var mosquesList = [MosquesResponseFields]()
    var mosqueSelected = MosquesResponseFields()
    var nextPageTokenGoogleApi = ""
    @IBOutlet weak var stackViewSelectMosque: UIStackView!
    var isMustSelectMosque = false
    var isLaunch = false

    // MARK: Object lifecycle
    /*
    override init(nibName nibNameOrNil: String?, bundle nibBundleOrNil: Bundle?) {
        super.init(nibName: nibNameOrNil, bundle: nibBundleOrNil)
        setup()
    }
    */
    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
        setup()
    }
    
    // MARK: Setup
    
    private func setup() {
        let viewController = self
        let interactor = AddAddressInteractor()
        let presenter = AddAddressPresenter()
        
        //View Controller will communicate with only presenter
        viewController.presenterAddAddress = presenter
        
        //Presenter will communicate with Interector and Viewcontroller
        presenter.viewControllerAddAddress = viewController
        presenter.interactorAddAddress = interactor
        
        //Interactor will communucate with only presenter.
        interactor.presenterAddAddress = presenter
    }
    
    
    // MARK: View lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        self.mapContainerView.insertSubview(self.gmsMapView, at: 0)
        gmsMapView.leadingAnchor.constraint(equalTo: mapContainerView.leadingAnchor).isActive = true
        gmsMapView.trailingAnchor.constraint(equalTo: mapContainerView.trailingAnchor).isActive = true
        gmsMapView.topAnchor.constraint(equalTo: mapContainerView.topAnchor).isActive = true
        gmsMapView.bottomAnchor.constraint(equalTo: mapContainerView.bottomAnchor).isActive = true
        gmsMapView.updateConstraints()
        
        self.setTexts()
        txtLocationName.setLeftPaddingPoints(10)
        
        LocationManager.sharedInstance.startUpdatingLocation()
        LocationManager.sharedInstance.actionDinied = { [weak self] in
            
            if UserDefaults.standard.bool(forKey: "DontAllow") == false{
                self?.dismiss(animated: true)
                UserDefaults.standard.set(true, forKey: "DontAllow")
            }
        }
        gmsMapView.delegate = self
        gmsMapView.isMyLocationEnabled = true
        gmsMapView.settings.myLocationButton = true
        
        // check for 0 0 lat long
        if CLLocationManager.authorizationStatus() == .denied || CLLocationManager.authorizationStatus() == .notDetermined {
            delay(0.1) {   // show static lat long of Riyadh
                let vancouver = CLLocationCoordinate2D(latitude: RiyadhCenterLatitude, longitude: RiyadhCenterLongitude)
                let vancouverCam = GMSCameraUpdate.setTarget(vancouver, zoom: 18.0)
                self.gmsMapView.animate(with: vancouverCam)
            }
        }
        else {
            let position = GMSCameraPosition.init(latitude: LocationManager.sharedInstance.latitude, longitude: LocationManager.sharedInstance.longitude, zoom: 18.0)
            gmsMapView.animate(to: position)
        }
        
        gmsMapView.addObserver(self, forKeyPath: "myLocation", options: NSKeyValueObservingOptions.new, context: nil)

        // Polygon
        self.addPolygonForRiyadh()
        
        delay(0.1) {
            self.locationPermissionAccess()
        }
        if self.isMustSelectMosque {
            self.stackViewSelectMosque.isHidden = false
            self.getCurrentMosquesPlace(latitude: RiyadhCenterLatitude, longitude: RiyadhCenterLongitude)
        } else {
            self.stackViewSelectMosque.isHidden = true
        }
    }
    
    func locationPermissionAccess(isValid:Bool = false) {
        if CLLocationManager.authorizationStatus() == .denied /*|| !CLLocationManager.locationServicesEnabled()*/ {
            self.showNoLocationAlert()
        }
    }

    private func getCurrentMosquesPlace(latitude: Double, longitude: Double) {
        #if DEVRELEASE
        var strGoogleApi = "https://maps.googleapis.com/maps/api/place/nearbysearch/json?location=\(latitude),\(longitude)&radius=2000&type=mosque&language=\(CurrentAppLang)&key=\(Constant.GOOGLE_API_KEY)"
        #else
        var strGoogleApi = "https://maps.googleapis.com/maps/api/place/nearbysearch/json?location=\(latitude),\(longitude)&radius=2000&type=mosque&language=\(CurrentAppLang)&key=\(Constant.GOOGLE_API_KEY)"
        #endif
        debugPrint(strGoogleApi)
        strGoogleApi = strGoogleApi.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed)!
        var urlRequest = URLRequest(url: URL(string: strGoogleApi)!)
        urlRequest.setValue(Bundle.main.bundleIdentifier ?? "", forHTTPHeaderField: "X-Ios-Bundle-Identifier")
        urlRequest.httpMethod = "GET"
        let task = URLSession.shared.dataTask(with: urlRequest) { (data, resopnse, error) in
            if error == nil {
                if let responseData = data {
                    let jsonDict = try? JSONSerialization.jsonObject(with: responseData, options: .mutableContainers)
                    if let dict = jsonDict as? Dictionary<String, AnyObject> {
                        if let nextPage = dict["next_page_token"] as? String {
                            self.nextPageTokenGoogleApi = nextPage
                        } else {
                            self.nextPageTokenGoogleApi = ""
                        }
                        
                        if let results = dict["results"] as? [Dictionary<String, AnyObject>] {
                            print("json == \(results)")
                            if self.nextPageTokenGoogleApi == "" {
                                self.mosquesArr.removeAll()
                            }
                            
                            for dct in results {
                                self.mosquesArr.append(dct)
                            }
                            self.getListOfAllMosque()
                        }
                    }
                }
            } else {
                //we have error connection google api
            }
        }
        task.resume()
    }

    private func getListOfAllMosque() {
        self.mosquesList.removeAll()
        DispatchQueue.main.async {
            self.gmsMapView.clear()
        }
        self.mosquesArr.forEach { mosque in
            if let locationGeometry = mosque["geometry"] as? Dictionary<String, AnyObject>, let name = mosque["name"] as? String {
                if let location = locationGeometry["location"] as? Dictionary<String, AnyObject> {
                    if let latitude = location["lat"] as? Double {
                        if let longitude = location["lng"] as? Double {
                            debugPrint(latitude)
                            debugPrint(longitude)
                            if name.lowercased() != "masjid" || name.lowercased() != "mosque" || name != "مسجد" {
                                self.mosquesList.append(MosquesResponseFields(name: name,location: MosqueLocationsFields(lat: latitude,lng: longitude)))
                                
                                DispatchQueue.main.async {
                                    let marker = GMSMarker()
                                    marker.position = CLLocationCoordinate2D(latitude: latitude, longitude: longitude)
                                    marker.title = name
                                    marker.icon = UIImage(named: "mosque-pin-icon")
                                    marker.map = self.gmsMapView
                                }
                                
                                
                            }
                        }
                    }
                }
            }
        }
    }
    
    @IBAction func selectMosqueBtnTapped(_ sender: UIButton) {
        if self.mosquesArr.count == 0 {
            self.getCurrentMosquesPlace(latitude: RiyadhCenterLatitude, longitude: RiyadhCenterLongitude)
        }
        self.selectMosque(sender)
    }
    
    func selectMosque(_ sender: UIButton) {
        // select mosque from list
        ActionSheetStringPicker.show(withTitle: "select_mosque".localized ,
                                     rows: self.mosquesList.map{ $0.name } ,
                                     initialSelection: 0,
                                     doneBlock: {[weak self] picker, index, value in
            guard let self = self else {return}
            if self.mosquesList.count == 0 {return}
            self.setSelectedMosqueLocation(self.mosquesList[index])
            debugPrint(self.mosquesList[index].name)
            debugPrint(self.mosquesList[index].location.lat)
            debugPrint(self.mosquesList[index].location.lng)
            return
        },
                                     cancel: { picker in
            return
        },
                                     origin: sender)
    }
    
    private func setSelectedMosqueLocation(_ location: MosquesResponseFields) {
        self.lblMosqueName.text = location.name
        self.txtLocationName.text = location.name
        self.mosqueSelected = location
    }
    
    func showNoLocationAlert() {
        AppSingleton.shared.showCustomPopUpWithYesNoButton(strButton1Title: ObjKeymessages.kLABEL_ALLOW, strButton2Title: ObjKeymessages.kLABEL_CANCEL, strMessage: ObjKeymessages.kMSG_LOCATION_PERMISSION, showOnTopVC: true) { (isOk) in
            if isOk == true {
                self.dismiss(animated: true, completion: nil)

                guard let settingsUrl = URL(string: UIApplication.openSettingsURLString) else {
                    return
                }
                if UIApplication.shared.canOpenURL(settingsUrl) {
                    UIApplication.shared.open(settingsUrl, completionHandler: { (success) in
                    })
                }
            }
            else {
                self.dismiss(animated: true, completion: nil)
            }
        }
    }

    func addPolygonForRiyadh() {
        
        // Create a rectangular path
        rect.add(CLLocationCoordinate2D(latitude: 25.1468470, longitude: 46.3652220))
        rect.add(CLLocationCoordinate2D(latitude: 25.1468470, longitude: 46.3652220))
        rect.add(CLLocationCoordinate2D(latitude: 25.2147217, longitude: 46.8038196))
        rect.add(CLLocationCoordinate2D(latitude: 25.1572820, longitude: 47.2445780))
        rect.add(CLLocationCoordinate2D(latitude: 24.9294250, longitude: 47.3425660))
        rect.add(CLLocationCoordinate2D(latitude: 24.6752930, longitude: 47.2166067))
        rect.add(CLLocationCoordinate2D(latitude: 24.4501490, longitude: 47.2168150))
        rect.add(CLLocationCoordinate2D(latitude: 24.3311600, longitude: 47.1090290))
        rect.add(CLLocationCoordinate2D(latitude: 24.2939520, longitude: 46.9734790))
        rect.add(CLLocationCoordinate2D(latitude: 24.4025670, longitude: 46.7268770))
        rect.add(CLLocationCoordinate2D(latitude: 24.5066300, longitude: 46.4476120))
        rect.add(CLLocationCoordinate2D(latitude: 24.5526880, longitude: 46.3708550))
        rect.add(CLLocationCoordinate2D(latitude: 24.6417840, longitude: 46.2989970))
        rect.add(CLLocationCoordinate2D(latitude: 24.7280272, longitude: 46.2284777))
        rect.add(CLLocationCoordinate2D(latitude: 24.9774170, longitude: 46.2149755))
        rect.add(CLLocationCoordinate2D(latitude: 25.1469340, longitude: 46.3659560))

        let polygon = GMSPolygon(path: rect)
        polygon.strokeColor = UIColor.AppTheme_BlueColor_012CDA
        polygon.fillColor = UIColor.clear
        polygon.strokeWidth = 1.0
        polygon.map = gmsMapView
        
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        LocationManager.sharedInstance.stopUpdatingLocation()
    }
    
    private func setTexts() {
        txtLocationName.placeholder = ObjKeymessages.kLABEL_LOCATION_PLACEHOLDER
        lblDeliveryLocationTitle.text = ObjKeymessages.kLABEL_DELIVERY_LOCATION
        lblLocationNameTitle.text = ObjKeymessages.kLABEL_LOCATION_NAME
        btnSave.setTitle(ObjKeymessages.kLABEL_SAVE, for: .normal)
        self.lblMosqueName.text = "select_mosque".localized
        self.lblMosqueTitle.text = "select_mosque".localized
    }
    
    override func observeValue(forKeyPath keyPath: String?, of object: Any?, change: [NSKeyValueChangeKey : Any]?, context: UnsafeMutableRawPointer?) {

        if keyPath == "myLocation",let myLocation: CLLocation = change?[NSKeyValueChangeKey.newKey] as? CLLocation {
            gmsMapView.camera = GMSCameraPosition.camera(withTarget: myLocation.coordinate, zoom: 18.0)
            gmsMapView.removeObserver(self, forKeyPath: "myLocation", context: nil)
        }
    }

    @IBAction func btnDimViewAction(_ sender: Any) {
        if isLaunch == false {
            self.dismiss(animated: true, completion: nil)
        }
    }

    @IBAction func saveAction(_ sender: Any) {
        self.view.endEditing(true)
        if self.isMustSelectMosque && self.mosqueSelected.name == "" {
            self.showAlert(message: "select_mosque".localized)
            return
        }
        
        if AppSingletonObj.isConnectedToNetwork(){
            var dictParam : [String:Any] = [:]
            dictParam["iTypeId"] = 0
            dictParam["txAddress"] = self.lblAddressDetected.text
            dictParam["vTypeTitle"] = txtLocationName.text
            dictParam["txAptSuite"] = ""
            if self.isMustSelectMosque {
                dictParam["vZipCode"] = "mosque"
                dictParam["dLatitude"] = "\(self.mosqueSelected.location.lat)"
                dictParam["dLongitude"] = "\(self.mosqueSelected.location.lng)"
            } else {
                dictParam["dLatitude"] = "\(gmsMapView.camera.target.latitude)"
                dictParam["dLongitude"] = "\(gmsMapView.camera.target.longitude)"
                dictParam["vZipCode"] = strZipcode
            }
            dictParam["vCity"] = lblCity.text
            dictParam["vState"] = strState
            dictParam["vCountryCode"] = strCountryCode
            dictParam["isMosques"] = "\(self.isMustSelectMosque == true ? "1" : "0")"
            debugPrint(dictParam)
            if self.presenterAddAddress?.checkValidation(dictData: dictParam) ?? false {
                Constant.shared.SELECTED_ADDRESS_NAME = self.txtLocationName.text ?? ""
                Constant.shared.SELECTED_LATITUDE = gmsMapView.camera.target.latitude
                Constant.shared.SELECTED_LONGITUDE = gmsMapView.camera.target.longitude
                TikTokBusiness.trackEvent("add_new_address", withProperties: dictParam)
                Analytics.logEvent("add_new_address", parameters: dictParam)
                self.presenterAddAddress?.apiCallForAddAddress(dictData: dictParam)
            }
            if isLaunch == false {
                self.dismiss(animated: true, completion: nil)
            }
        }
    }
    
}

extension UITextField {
    func setLeftPaddingPoints(_ amount:CGFloat){
        let paddingView = UIView(frame: CGRect(x: 0, y: 0, width: amount, height: self.frame.size.height))
        self.leftView = paddingView
        self.leftViewMode = .always
    }
    func setRightPaddingPoints(_ amount:CGFloat) {
        let paddingView = UIView(frame: CGRect(x: 0, y: 0, width: amount, height: self.frame.size.height))
        self.rightView = paddingView
        self.rightViewMode = .always
    }
}

extension AddAddressViewController: AddAddressProtocol {
    func displayAlert(strMsg: String) {
        AppSingletonObj.showAlert(strMessage: strMsg)
        self.refreshAddressList?()
        self.dismiss(animated: true, completion: nil)
    }

    func displayAlertWithoutDismiss(strMsg: String) {
        AppSingleton.shared.showCustomPopUpWithOkButton(strButtonTitle: ObjKeymessages.kLABEL_OK, strMessage: strMsg, showOnTopVC: true) { (isOk) in
            if isOk == true {
                self.dismiss(animated: true, completion: nil)
            }
        }
    }
    
    func selectDeliveryType() {
//            if let scene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
//               let window = scene.windows.first {
//
//                // نحذف أي view controllers معروضة
//                window.rootViewController?.dismiss(animated: false)
//
//                // نحاول نجيب SplashViewController من Main.storyboard
//                let storyboard = UIStoryboard(name: "Main", bundle: nil)
//                guard let splashVC = storyboard.instantiateViewController(withIdentifier: "SplashViewController") as? SplashViewController else {
//                    print("❌ Couldn't instantiate SplashViewController")
//                    return
//                }
//
//                // نعينه كـ root
//                window.rootViewController = splashVC
//                window.makeKeyAndVisible()
//
//                // نأكد إن window قابلة للتفاعل
//                window.isUserInteractionEnabled = true
//            }
        
//        self.dismiss(animated: true)
//        NotificationCenter.default.post(name: NSNotification.Name("OpenDeliveryType"), object: nil)
        
        
//        self.dismiss(animated: false)
        AppDel?.window?.rootViewController = UIHostingController(rootView: SelectDeliveryTypeView())
        AppDel?.window?.isUserInteractionEnabled = true
        AppDel?.window?.makeKeyAndVisible()
        
//        self.dismiss(animated: false) {
//            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
//                if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
//                   let window = windowScene.windows.first {
//                    
//                    let hostingController = UIHostingController(rootView: SelectDeliveryTypeView())
//                    window.rootViewController = hostingController
//                    window.makeKeyAndVisible()
//
//                    UIView.transition(with: window, duration: 0.4, options: .transitionCrossDissolve, animations: nil)
//                }
//            }
//        }
    }

}

extension AddAddressViewController : GMSMapViewDelegate {
    func mapView(_ mapView: GMSMapView, didTap marker: GMSMarker) -> Bool {
        debugPrint(marker.title)
        debugPrint(marker.position.latitude)
        debugPrint(marker.position.longitude)
        self.setSelectedMosqueLocation(MosquesResponseFields(name: marker.title ?? "",location: MosqueLocationsFields(lat: marker.position.latitude, lng: marker.position.longitude)))
        return true
    }
 
    func mapView(_ mapView: GMSMapView, idleAt position: GMSCameraPosition) {
        
        // check for valid lat long
        if (GMSGeometryContainsLocation(CLLocationCoordinate2D(latitude: position.target.latitude, longitude: position.target.longitude), rect, true)) {
            print("YES: you are in this polygon.")
            
            LocationManager.sharedInstance.reverseGeocodeLocationUsingGoogleWithLatLon(latitude: position.target.latitude, longitude: position.target.longitude) { (dict, model, strError) in
                if let strFormattedAddress : String = dict?["formattedAddress"] as? String {
                    ez.runThisInMainThread {
                        self.lblAddressDetected.text = strFormattedAddress
                        self.lblCity.text = dict?["locality"] as? String
                        self.strZipcode = dict?["postalCode"] as? String ?? ""
                        self.strState = dict?["administrativeArea"] as? String ?? ""
                        self.strCountryCode = dict?["countryCode"] as? String ?? ""
                        if self.isMustSelectMosque {
                            self.getCurrentMosquesPlace(latitude: position.target.latitude, longitude: position.target.longitude)
                        }
                    }
                }else if let strError = strError {
                }
            }
        }
        else {
            self.lblAddressDetected.text = ""
            self.lblCity.text = ""
            self.strZipcode = ""
            self.strState = ""
            self.strCountryCode = ""
            
            if CLLocationManager.authorizationStatus() == .denied || CLLocationManager.authorizationStatus() == .notDetermined {
            }
            else {
                AppSingletonObj.showAlert(strMessage: ObjKeymessages.kLABEL_INVALID_LOCATION)
            }
        }
        
    }
}

extension GMSMapView {
    
    var centerCoordinates : CLLocationCoordinate2D {
        let coordinate = self.projection.coordinate(for: self.center)
        return coordinate
    }
    
}

// MARK: - UITextFieldDelegate
extension AddAddressViewController : UITextFieldDelegate {
    
    func textField(_ textField: UITextField, shouldChangeCharactersIn range: NSRange, replacementString string: String) -> Bool {
        
        if textField == txtLocationName {
            let newLength: Int = (textField.text?.length)! + string.length - range.length
            if newLength > MaxLocationNameLength {
                return false
            }
            return true
        }
        else {
            return true
        }
    }
    
}
