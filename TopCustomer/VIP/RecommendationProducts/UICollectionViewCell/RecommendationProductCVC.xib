<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="23504" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="23506"/>
        <capability name="Named colors" minToolsVersion="9.0"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="LoewNextArabic-Bold.ttf">
            <string>LoewNextArabic-Bold</string>
        </array>
        <array key="LoewNextArabic-Medium.ttf">
            <string>LoewNextArabic-Medium</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" reuseIdentifier="RecommendationProductCVC" id="gTV-IL-0wX" customClass="RecommendationProductCVC" customModule="TopCustomer" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="150" height="220"/>
            <autoresizingMask key="autoresizingMask"/>
            <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center">
                <rect key="frame" x="0.0" y="0.0" width="150" height="220"/>
                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="3ei-YH-4qC">
                        <rect key="frame" x="0.0" y="0.0" width="150" height="220"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ZVk-oe-bXo">
                                <rect key="frame" x="5" y="15" width="140" height="190"/>
                                <subviews>
                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="7aQ-6P-7vj">
                                        <rect key="frame" x="15" y="12" width="110" height="95"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="95" id="eBH-6u-CKV"/>
                                            <constraint firstAttribute="width" constant="110" id="uy9-ce-sl0"/>
                                        </constraints>
                                    </imageView>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="WX1-0B-5ej">
                                        <rect key="frame" x="0.0" y="113" width="140" height="1"/>
                                        <color key="backgroundColor" name="AppTheme_LightGrayColor_#B4B2B2"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="1" id="cp9-Mg-rWo"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                        <nil key="textColor"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="4" translatesAutoresizingMaskIntoConstraints="NO" id="ISS-QU-Sij">
                                        <rect key="frame" x="5" y="118" width="130" height="48"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Ceed Ginger Ale" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="rB5-nM-GbK">
                                                <rect key="frame" x="0.0" y="0.0" width="130" height="10"/>
                                                <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="10"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="200 x4" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Hw5-l1-X0m">
                                                <rect key="frame" x="0.0" y="14" width="130" height="8"/>
                                                <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="8"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="39.00 SAR" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="3cf-GJ-SBW">
                                                <rect key="frame" x="0.0" y="26" width="130" height="10"/>
                                                <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="10"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <stackView opaque="NO" contentMode="scaleToFill" spacing="4" translatesAutoresizingMaskIntoConstraints="NO" id="UyF-uE-RWx">
                                                <rect key="frame" x="0.0" y="40" width="130" height="8"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" ambiguous="YES" text="200 x4" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="AhE-12-X9m">
                                                        <rect key="frame" x="0.0" y="0.0" width="28.333333333333332" height="8"/>
                                                        <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="8"/>
                                                        <color key="textColor" red="0.74509803919999995" green="0.15686274510000001" blue="0.10980392160000001" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" ambiguous="YES" text="off" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="eL0-30-cfp">
                                                        <rect key="frame" x="32.333333333333336" y="0.0" width="12" height="8"/>
                                                        <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="8"/>
                                                        <color key="textColor" red="0.74509803919999995" green="0.15686274510000001" blue="0.10980392160000001" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" ambiguous="YES" text=" " textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="qew-So-TfS">
                                                        <rect key="frame" x="48.333333333333336" y="0.0" width="81.666666666666657" height="8"/>
                                                        <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="8"/>
                                                        <color key="textColor" red="0.74509803919999995" green="0.15686274510000001" blue="0.10980392160000001" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                </subviews>
                                            </stackView>
                                        </subviews>
                                    </stackView>
                                </subviews>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="140" id="9ud-zR-PUI"/>
                                    <constraint firstItem="WX1-0B-5ej" firstAttribute="leading" secondItem="ZVk-oe-bXo" secondAttribute="leading" id="HtL-dY-9Wn"/>
                                    <constraint firstItem="ISS-QU-Sij" firstAttribute="top" secondItem="WX1-0B-5ej" secondAttribute="bottom" constant="4" id="KTR-py-S61"/>
                                    <constraint firstAttribute="trailing" secondItem="WX1-0B-5ej" secondAttribute="trailing" id="Rkk-RO-z08"/>
                                    <constraint firstAttribute="trailing" secondItem="ISS-QU-Sij" secondAttribute="trailing" constant="5" id="cTv-kT-kAf"/>
                                    <constraint firstItem="7aQ-6P-7vj" firstAttribute="centerX" secondItem="ZVk-oe-bXo" secondAttribute="centerX" id="ftk-If-Mtj"/>
                                    <constraint firstAttribute="height" constant="190" id="fwA-U9-mCn"/>
                                    <constraint firstItem="7aQ-6P-7vj" firstAttribute="top" secondItem="ZVk-oe-bXo" secondAttribute="top" constant="12" id="ifr-m2-XAZ"/>
                                    <constraint firstItem="WX1-0B-5ej" firstAttribute="top" secondItem="7aQ-6P-7vj" secondAttribute="bottom" constant="6" id="knT-Qg-Mwv"/>
                                    <constraint firstItem="ISS-QU-Sij" firstAttribute="leading" secondItem="ZVk-oe-bXo" secondAttribute="leading" constant="5" id="o8d-Vq-gtz"/>
                                </constraints>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="boolean" keyPath="isViewShadow" value="YES"/>
                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                        <real key="value" value="10"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </view>
                            <button opaque="NO" contentMode="scaleToFill" preservesSuperviewLayoutMargins="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="4kb-Dk-896">
                                <rect key="frame" x="121" y="0.0" width="29" height="29"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="29" id="irP-ki-ijf"/>
                                    <constraint firstAttribute="height" constant="29" id="qb2-7k-nsb"/>
                                </constraints>
                                <state key="normal" title="Button"/>
                                <buttonConfiguration key="configuration" style="plain" image="add-icon-new"/>
                            </button>
                        </subviews>
                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="4kb-Dk-896" firstAttribute="top" secondItem="3ei-YH-4qC" secondAttribute="top" id="8Ug-QX-Bcr"/>
                            <constraint firstItem="ZVk-oe-bXo" firstAttribute="centerY" secondItem="3ei-YH-4qC" secondAttribute="centerY" id="E8o-MS-BbH"/>
                            <constraint firstAttribute="trailing" secondItem="4kb-Dk-896" secondAttribute="trailing" id="iWB-i9-jgX"/>
                            <constraint firstItem="ZVk-oe-bXo" firstAttribute="centerX" secondItem="3ei-YH-4qC" secondAttribute="centerX" id="vZN-3M-HUB"/>
                        </constraints>
                    </view>
                </subviews>
            </view>
            <viewLayoutGuide key="safeArea" id="ZTg-uK-7eu"/>
            <constraints>
                <constraint firstItem="3ei-YH-4qC" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" id="5PQ-kX-vQk"/>
                <constraint firstItem="3ei-YH-4qC" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" id="9vS-K2-rae"/>
                <constraint firstAttribute="bottom" secondItem="3ei-YH-4qC" secondAttribute="bottom" id="Aky-pC-vEy"/>
                <constraint firstAttribute="trailing" secondItem="3ei-YH-4qC" secondAttribute="trailing" id="UFg-dy-2Mr"/>
            </constraints>
            <connections>
                <outlet property="btnAddToCart" destination="4kb-Dk-896" id="zHX-28-2i4"/>
                <outlet property="imgProduct" destination="7aQ-6P-7vj" id="yN1-WI-7T6"/>
                <outlet property="lblDiscountPrice" destination="AhE-12-X9m" id="KvO-Ya-Qsk"/>
                <outlet property="lblDiscountValue" destination="eL0-30-cfp" id="cjg-Xa-dMy"/>
                <outlet property="lblPrice" destination="3cf-GJ-SBW" id="mM3-e1-r9T"/>
                <outlet property="lblProductCategory" destination="Hw5-l1-X0m" id="sSx-kB-WKx"/>
                <outlet property="lblProductName" destination="rB5-nM-GbK" id="3c0-Bd-t5V"/>
                <outlet property="stackDiscount" destination="UyF-uE-RWx" id="2qw-Ac-8dG"/>
            </connections>
            <point key="canvasLocation" x="-76" y="20"/>
        </collectionViewCell>
    </objects>
    <resources>
        <image name="add-icon-new" width="29" height="29"/>
        <namedColor name="AppTheme_LightGrayColor_#B4B2B2">
            <color red="0.70588235294117652" green="0.69803921568627447" blue="0.69803921568627447" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
    </resources>
</document>
