//
//  RecommendationProductCVC.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 15/08/2023.
//  Copyright © 2023 SOTSYS203. All rights reserved.
//

import UIKit

class RecommendationProductCVC: UICollectionViewCell {
    // MARK: - IBOutlets
    @IBOutlet weak var btnAddToCart: UIButton!
    @IBOutlet weak var imgProduct: UIImageView!
    @IBOutlet weak var lblProductName: UILabel!
    @IBOutlet weak var lblPrice: UILabel!
    @IBOutlet weak var lblProductCategory: UILabel!
    @IBOutlet weak var lblDiscountPrice: UILabel!
    @IBOutlet weak var lblDiscountValue: UILabel!
    @IBOutlet weak var stackDiscount: UIStackView!
    
    // MARK: - Func
    override func awakeFromNib() {
        super.awakeFromNib()
        // Initialization code
        self.btnAddToCart.setTitle("", for: .normal)
        
        // border radius
        self.layer.cornerRadius = 7.0

        // border
        self.layer.borderColor = UIColor.clear.cgColor
        self.layer.borderWidth = 0.0

        //drop shadow
        self.layer.shadowColor = UIColor(red: 0.0, green: 0.0, blue: 0.0, alpha: 0.15).cgColor
        self.layer.shadowOpacity = 0.8
        self.layer.shadowRadius = 3.0
        self.layer.shadowOffset = CGSize(width: 1.0, height: 1.0)
        self.clipsToBounds = true
        self.layer.masksToBounds = false

    }

}
