//
//  AlertType.swift
//  BeqalaApp
//
//  Created by <PERSON><PERSON> on 11/03/2025.
//


enum AlertType {
    case success
    case error
    case warning

    var title: String {
        switch self {
        case .success: return "Success"
        case .error: return "Error"
        case .warning: return "Warning"
        }
    }

    var message: String {
        switch self {
        case .success: return "Operation completed successfully."
        case .error: return "An error occurred. Please try again."
        case .warning: return "This is a warning message."
        }
    }
}