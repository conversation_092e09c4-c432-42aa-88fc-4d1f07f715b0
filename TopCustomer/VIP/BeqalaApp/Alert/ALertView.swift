//
//  ALertView.swift
//  BeqalaApp
//
//  Created by <PERSON><PERSON> on 11/03/2025.
//


import SwiftUI

struct ALertView: View {
    @State private var showAlert = false
    @State private var alertType: AlertType = .success

    var body: some View {
        VStack {
            <PERSON><PERSON>("Show Success Alert") {
                alertType = .success
                showAlert = true
            }
            .padding()

            <PERSON><PERSON>("Show Error Alert") {
                alertType = .error
                showAlert = true
            }
            .padding()

            <PERSON><PERSON>("Show Warning Alert") {
                alertType = .warning
                showAlert = true
            }
            .padding()
        }
        .alert(alertType.title, isPresented: $showAlert) {
            <PERSON><PERSON>("OK", role: .cancel) { }
        } message: {
            Text(alertType.message)
        }
    }
}
