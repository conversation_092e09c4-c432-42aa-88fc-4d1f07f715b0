//
//  AccountView.swift
//  BeqalaApp
//
//  Created by <PERSON><PERSON> on 10/12/2024.
//

import SwiftUI

struct AccountView: View {
    var body: some View {
        ScrollView {
            VStack {
                // User Profile Section
                HStack {
                    Circle()
                        .overlay {
                            Image("raouf")
                                .resizable()
                                .frame(width: 60, height: 60)
                                .clipShape(.circle)
                        }
                        .frame(width: 60, height: 60)
                    VStack(alignment: .leading) {
                        Text("<PERSON>")
                            .font(.title3)
                            .fontWeight(.bold)
                            .lineLimit(1)
                        Text("+************")
                            .foregroundColor(.secondary)
                    }
                    Spacer()
                    <PERSON><PERSON>(action: {
                        // Action for edit profile
                    }) {
                        Text("Edit")
                            .font(.callout)
                            .foregroundColor(.blue)
                    }
                }
                .padding()
                
                // Complete Profile Section
                HStack {
                    Image(systemName: "person.fill")
                        .foregroundColor(.blue)
                    VStack(alignment: .leading) {
                        Text("Complete your profile")
                            .font(.callout)
                            .fontWeight(.semibold)
                        Text("This helps you receive personal discounts and promotions")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    Spacer()
                    <PERSON><PERSON>(action: {
                        // Action to complete profile
                    }) {
                        Image(systemName: "chevron.right")
                            .foregroundColor(.gray)
                    }
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(10)
                .padding(.horizontal)
                
                // Premium Subscription Section
                VStack(alignment: .center, spacing: 8) {
                    HStack {
                        Image(systemName: "crown.fill")
                            .foregroundColor(.yellow)
                        Text("Unlock your Material Premium experience!")
                            .font(.headline)
                            .fontWeight(.bold)
                    }
                    Text("With Material Premium, enjoy exclusive perks like free delivery, up to 30% cashback, and special deals curated just for you.")
                        .font(.footnote)
                        .foregroundColor(.secondary)
                    HStack(alignment: .center, spacing: 16) {
                        FeatureBadge(text: "Free Delivery", icon: "shippingbox.fill")
                        FeatureBadge(text: "+30% Cashback", icon: "creditcard.fill")
                        FeatureBadge(text: "Exclusive Deals", icon: "star.fill")
                    }
                    Button(action: {
                        // Action to view subscriptions
                    }) {
                        Text("View Subscriptions")
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(Color.blue)
                            .cornerRadius(10)
                    }
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(10)
                .padding(.horizontal)
                
                // Account Actions
                VStack(spacing: 16) {
                    AccountRow(icon: "wallet.pass.fill", title: "Wallet", detail: "SAR 0.00")
                    AccountRow(icon: "map.fill", title: "My Addresses", detail: "")
                    AccountRow(icon: "heart.fill", title: "My Favourite", detail: "")
                    AccountRow(icon: "wallet.bifold.fill", title: "Payment Methods", detail: "")
                    AccountRow(icon: "globe", title: "English", detail: "")
                    HStack{
                        Text("Rewards")
                            .font(.headline)
                            .fontWeight(.bold)
                            .frame(maxWidth: .infinity, alignment: .leading)
                    }
                    AccountRow(icon: "globe", title: "My Vouchers", detail: "")
                    AccountRow(icon: "face.smiling.inverse", title: "Refer a Friend", detail: "")
                    
                    HStack{
                        Text("Help")
                            .font(.headline)
                            .fontWeight(.bold)
                            .frame(maxWidth: .infinity, alignment: .leading)
                    }
                    
                    AccountRow(icon: "phone.bubble.fill", title: "Contact Us", detail: "")
                    AccountRow(icon: "square.and.pencil.circle.fill", title: "Feedback", detail: "")
                    
                    
                    
                }
                .padding(.horizontal)
                
                Spacer()
            }
            .padding(.top)
            .navigationTitle("Account")
            .navigationBarTitleDisplayMode(.automatic)
        }
        
    }
}

#Preview {
    AccountView()
}

struct FeatureBadge: View {
    var text: String
    var icon: String
    
    var body: some View {
        VStack {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(.blue)
            Text(text)
                .font(.caption)
                .fontWeight(.semibold)
                .multilineTextAlignment(.center)
        }
        .frame(width: 80)
    }
}

struct AccountRow: View {
    var icon: String
    var title: String
    var detail: String
    
    var body: some View {
        HStack {
            Image(systemName: icon)
                .frame(width: 30, height: 30)
                .foregroundColor(.blue)
            Text(title)
                .font(.body)
                .fontWeight(.semibold)
            Spacer()
            if !detail.isEmpty {
                Text(detail)
                    .foregroundColor(.secondary)
            }
            Image(systemName: "chevron.right")
                .foregroundColor(.gray)
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(10)
    }
}

// Placeholder views for navigation links
//struct WalletView: View { var body: some View { Text("Wallet") } }
//struct MyAddressesView: View { var body: some View { Text("My Addresses") } }
//struct MyFavoriteView: View { var body: some View { Text("My Favourite") } }
struct PaymentMethodsView: View { var body: some View { Text("Payment Methods") } }
struct LanguageView: View { var body: some View { Text("Language") } }
struct MyVouchersView: View { var body: some View { Text("My Vouchers") } }
struct ReferAFriendView: View { var body: some View { Text("Refer a Friend") } }
struct ContactUsView: View { var body: some View { Text("Contact Us") } }
struct FeedbackView: View { var body: some View { Text("Feedback") } }
struct FAQView: View { var body: some View { Text("FAQ") } }
struct TermsAndConditionsView: View { var body: some View { Text("Terms & Conditions") } }
struct LangMethodsView: View { var body: some View { Text("English") } }
