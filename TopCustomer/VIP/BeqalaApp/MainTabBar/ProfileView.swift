//
//  ProfileView.swift
//  BeqalaApp
//
//  Created by <PERSON><PERSON> on 04/02/2025.
//

import SwiftUI

struct ProfileView: View {
    
    private var arrEngSettingsText = SettingsEng.allCases
    private var arrArbSettingsText = SettingsArb.allCases
    private var arrSettingsImages = SettingsImages.allCases
    
    
    var body: some View {
        NavigationView {
            VStack(alignment: .leading) {
                ScrollView {
                    // Profile Header
                    HStack {
                        ZStack {
                            Circle()
                                .fill(Color.gray.opacity(0.3))
                                .frame(width: 50, height: 50)
                            if !User.shared.checkUserLoginStatus() {
                                Text("GU")
                                    .fontWeight(.bold)
                            }else{
                                Text("TS")
                                    .fontWeight(.bold)
                            }
                        }
                        
                        VStack(alignment: .leading) {
                            if !User.shared.checkUserLoginStatus() {
                                Text("Guest User")
                                    .font(.headline)
                                    .foregroundColor(.gray)
                            } else {
                                Text(User.shared.vName ?? "")
                                    .font(.headline)
                                    .fontWeight(.bold)
                                
                                Text(User.shared.vMobileNumber ?? "")
                                    .font(.subheadline)
                                    .foregroundColor(.gray)
                            }
                        }
                        
                        Spacer()
                        
                        Button(action: {
                            // Edit profile action
                        }) {
                            Text("Edit")
                                .foregroundColor(.blue)
                        }
                    }
                    .padding()
                    
                    // Complete Profile Card
                    HStack {
                        Image(systemName: "shield.fill")
                            .foregroundColor(.blue)
                            .font(.title)
                        
                        VStack(alignment: .leading) {
                            Text("Complete Your Profile")
                                .fontWeight(.bold)
                            
                            Text("This helps you receive personal discounts and promotions")
                                .font(.caption)
                                .foregroundColor(.gray)
                        }
                        
                        Spacer()
                        
                        Image(systemName: "chevron.right")
                            .foregroundColor(.gray)
                    }
                    .padding()
                    .background(Color.blue.opacity(0.1))
                    .cornerRadius(10)
                    .padding(.horizontal)
                    
                    // Account Section
                    SectionHeader(title: "Account")
                    
                    NavigationLink(destination: WalletView()) {
                        ProfileRow(icon: "house.fill", title: "Wallet", active: true) {}
                    }
                    NavigationLink(destination: MyAddressesView()) {
                        ProfileRow(icon: "house.fill", title: "My Addresses", active: true) {}
                    }
                    NavigationLink(destination: MyFavouritesView()) {
                        ProfileRow(icon: "house.fill", title: "My Favourite", active: true) {}
                    }
                    
//                    ProfileRow(icon: "house.fill", title: "Payment Methods", active: false) {}
                    if UserDefaults.standard.getLanguage() == UserAPI.VLanguage_userLanguage.ar.rawValue {
//                        cell.lblSettingName.text = arrEngSettingsText[indexPath.row].getTitle()
                        ProfileRow(icon: "house.fill", title: arrEngSettingsText[2].getTitle(), active: true) {
                            if let topVC = UIApplication.shared.topViewController() {
                                let vc = settingsStoryboard.instantiate(SelectLanguageViewController.self)
                                topVC.present(vc, animated: true)
                            }
                        }
                    }
                    else {
//                        cell.lblSettingName.text = arrArbSettingsText[indexPath.row].getTitle()
                        ProfileRow(icon: "house.fill", title: arrArbSettingsText[2].getTitle(), active: true) {
                            if let topVC = UIApplication.shared.topViewController() {
                                let vc = settingsStoryboard.instantiate(SelectLanguageViewController.self)
                                topVC.present(vc, animated: true)
                            }
                        }
                    }
                    
                    
                    // Rewards Section
                    SectionHeader(title: "Rewards")
                    
//                    ProfileRow(icon: "house.fill", title: "My Vouchers", active: false) {}
                    NavigationLink(destination: ReferView()) {
                        ProfileRow(icon: "house.fill", title: "Refer a Friend", active: true) {}
                    }
                    
                    // Help Section
                    SectionHeader(title: "Help")
                    
                    ProfileRow(icon: "house.fill", title: "Contact Us", active: true) {
                        guard let whatsappURL = URL(string: "https://wa.me/+966554020703") else {return}
                        if UIApplication.shared.canOpenURL(whatsappURL) {
                            UIApplication.shared.open(whatsappURL, options: [:], completionHandler: nil)
                        } else {
                            AppSingleton.shared.showCustomPopUpWithOkButton(strButtonTitle: ObjKeymessages.kLABEL_OK, strMessage: "please_install_whatsapp".localized, showOnTopVC: false) { (isOk) in
                            }
                        }
                    }
//                    ProfileRow(icon: "house.fill", title: "Feedback", active: false) {}
                }
            }
            .navigationTitle("Profile")
            .navigationBarTitleDisplayMode(.inline)
            .onAppear {
                checkLoginStatus()
            }
        }
    }
    
    private func checkLoginStatus() {
            if !User.shared.checkUserLoginStatus() {
                if let topVC = UIApplication.shared.topViewController() {
                    topVC.showNoLoginUserAlertToHome(complation: nil)
                }
            }
        }
}

// Section Header Component
struct SectionHeader: View {
    let title: String
    
    var body: some View {
        HStack {
            Text(title)
                .font(.headline)
                .fontWeight(.bold)
            Spacer()
        }
        .padding(.top)
        .padding(.horizontal)
    }
}

// Profile Row Component
struct ProfileRow: View {
    let icon: String
    let title: String
    let active : Bool
    let onTap: () -> Void
    
    var body: some View {
        HStack {
            Image(systemName: icon)
            
            Text(title)
                .font(.body)
            
            Spacer()
            
            Image(systemName: "chevron.right")
                .foregroundColor(.gray)
        }
        .padding(.top, 1)
        .padding(.horizontal)
        .opacity(active ? 1.0 : 0.3)
        .onTapGesture {
            if active {
                onTap()
            }
        }
    }
}



#Preview {
    ProfileView()
}
