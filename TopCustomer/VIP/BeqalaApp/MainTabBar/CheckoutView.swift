//
//  CheckoutView.swift
//  BeqalaApp
//
//  Created by <PERSON><PERSON> on 22/12/2024.
//

import SwiftUI

struct CheckoutView: View {
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // Delivery Address & Time
                    HStack {
                        Text("Delivery Address & Time")
                            .font(.headline)
                        Spacer()
                    }
                    
                    HStack {
                        Image(systemName: "location.fill")
                            .foregroundColor(.gray)
                        Text("Delivering to:")
                            .foregroundColor(.gray)
                        Text("Work")
                            .fontWeight(.bold)
                        Spacer()
                    }
                    
                    Divider()
                    
                    // Payment Methods
                    VStack(alignment: .leading, spacing: 10) {
                        Text("Payment Methods")
                            .font(.headline)
                        
                        HStack {
                            Image(systemName: "applelogo")
                            Text("Apple Pay")
                            Spacer()
                        }
                        .padding()
                        .background(Color.gray.opacity(0.1))
                        .cornerRadius(8)
                        
                        HStack {
                            Image(systemName: "creditcard.fill")
                            Text("Wallet")
                            Spacer()
                            Text("SAR 0.00")
                                .foregroundColor(.gray)
                        }
                        .padding()
                        .background(Color.gray.opacity(0.1))
                        .cornerRadius(8)
                    }
                    
                    Divider()
                    
                    // Ninja Rewards
                    VStack(alignment: .leading, spacing: 10) {
                        Text("Material Rewards")
                            .font(.headline)
                        
                        HStack {
                            Text("9 SAR")
                                .fontWeight(.bold)
                                .foregroundColor(.blue)
                            Text("Free Delivery")
                                .foregroundColor(.gray)
                            Spacer()
                            Text("Valid until: 31 Dec, 2025")
                                .font(.caption)
                                .foregroundColor(.gray)
                        }
                        .padding()
                        .background(Color.blue.opacity(0.1))
                        .cornerRadius(8)
                    }
                    
                    Divider()
                    
                    // Summary
                    VStack(alignment: .leading, spacing: 10) {
                        Text("Summary")
                            .font(.headline)
                        
                        HStack {
                            Text("Items Price")
                            Spacer()
                            Text("70.95 SAR")
                        }
                        
                        HStack {
                            Text("Delivery Fee")
                            Spacer()
                            Text("Free Delivery")
                                .strikethrough()
                        }
                        
                        HStack {
                            Text("Discount")
                            Spacer()
                            Text("-9 SAR")
                                .foregroundColor(.green)
                        }
                        
                        Divider()
                        
                        HStack {
                            Text("Total")
                                .font(.headline)
                            Spacer()
                            Text("70.95 SAR")
                                .font(.headline)
                        }
                    }
                    
                    Spacer()
                    
                    // Apple Pay Button
                    Button(action: {
                        // Handle payment action
                    }) {
                        HStack {
                            Image(systemName: "applelogo")
                            Text("Set Up Pay")
                                .fontWeight(.bold)
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.black)
                        .foregroundColor(.white)
                        .cornerRadius(8)
                    }
                }
                .padding()
                .navigationTitle("Pay")
            }
        }
    }
}


#Preview {
    CheckoutView()
}
