//
//  FoodHomeView.swift
//  BeqalaApp
//
//  Created by <PERSON><PERSON> on 10/12/2024.
//

import SwiftUI

struct FoodHomeView: View {
    @State private var showingCategory = false
    var body: some View {
        ScrollView {
            VStack {
                // Top Header
                HStack {
                    Image("logo_login")
                        .resizable()
                        .fixedSize()
                    Spacer()
                    HStack {
                        Text("Deliver to:")
                            .font(.callout)
                        Button(action: {
                            // Action for changing address
                        }) {
                            HStack {
                                Text("home")
                                Image(systemName: "chevron.down")
                                    .font(.subheadline)
                            }
                        }
                    }
                    .foregroundColor(.primary)
                }
                .padding(.horizontal)
                
                // Search Bar
                HStack {
                    TextField("Search burger, al baik...", text: .constant(""))
                        .padding(10)
                        .background(Color(.systemGray6))
                        .cornerRadius(8)
                        .padding(.horizontal)
                }
                Spacer()
                // Offers Carousel
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 16) {
                        OfferCardImage(imageName: "category04")
                            .onTapGesture {
                                showingCategory.toggle()
                            }
                        OfferCardImage(imageName: "category02")
                        OfferCardImage(imageName: "category03")
                        OfferCardImage(imageName: "category01")
                        OfferCardImage(imageName: "category05")
                        OfferCardImage(imageName: "category02")
                        
                    }
                    .padding(.horizontal)
                }
                .frame(height: 200)
                Spacer()
                // Categories
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 16) {
                        CategoryBadge(text: "1 SR Offers")
                            .onTapGesture {
                                showingCategory.toggle()
                            }
                        CategoryBadge(text: "Hot Deals")
                        CategoryBadge(text: "Spin & Win!")
                        CategoryBadge(text: "Free Delivery")
                    }
                    .padding(.horizontal)
                }
                
                // Restaurants Section
                VStack(alignment: .leading) {
                    Text("Restaurants")
                        .font(.title2)
                        .fontWeight(.bold)
                        .padding(.horizontal)
                    
                    ScrollView {
                        VStack(spacing: 16) {
                            RestaurantCard(
                                name: "Pizza Hut",
                                description: "Fast Food · Pizza · Beverages",
                                deliveryTime: "30 minutes",
                                distance: "3.2 km",
                                price: "SAR 7"
                            ).onTapGesture {
                                showingCategory.toggle()
                            }
                            RestaurantCard(
                                name: "KFC",
                                description: "Fast Food · American · Beverages",
                                deliveryTime: "30 minutes",
                                distance: "0.5 km",
                                price: "SAR 7"
                            )
                            RestaurantCard(
                                name: "KFC",
                                description: "Fast Food · American · Beverages",
                                deliveryTime: "30 minutes",
                                distance: "0.5 km",
                                price: "SAR 7"
                            )
                            RestaurantCard(
                                name: "KFC",
                                description: "Fast Food · American · Beverages",
                                deliveryTime: "30 minutes",
                                distance: "0.5 km",
                                price: "SAR 7"
                            )
                            RestaurantCard(
                                name: "KFC",
                                description: "Fast Food · American · Beverages",
                                deliveryTime: "30 minutes",
                                distance: "0.5 km",
                                price: "SAR 7"
                            )
                            RestaurantCard(
                                name: "KFC",
                                description: "Fast Food · American · Beverages",
                                deliveryTime: "30 minutes",
                                distance: "0.5 km",
                                price: "SAR 7"
                            )
                            RestaurantCard(
                                name: "KFC",
                                description: "Fast Food · American · Beverages",
                                deliveryTime: "30 minutes",
                                distance: "0.5 km",
                                price: "SAR 7"
                            )
                        }
                        .padding(.horizontal)
                    }
                }
                .padding(.top)
                
                Spacer()
            }
        }.sheet(isPresented: $showingCategory) {
            ProductDetailView()
        }
    }
}

#Preview {
    FoodHomeView()
}

struct OfferCardNew: View {
    var title: String
    var image: String
    var price: String
    
    var body: some View {
        VStack {
            Image(image) // Replace with actual image assets
                .resizable()
                .aspectRatio(contentMode: .fill)
                .frame(width: 120, height: 100)
                .cornerRadius(10)
                .background(Color.random.opacity(0.7))
            Text(title)
                .font(.caption)
                .fontWeight(.bold)
            Text(price)
                .font(.footnote)
                .foregroundColor(.secondary)
        }
        .frame(width: 140)
    }
}

struct CategoryBadge: View {
    var text: String
    
    var body: some View {
        Text(text)
            .font(.caption)
            .fontWeight(.bold)
            .padding(8)
            .background(Color.random.opacity(0.1))
            .cornerRadius(20)
    }
}

struct RestaurantCard: View {
    var name: String
    var description: String
    var deliveryTime: String
    var distance: String
    var price: String
    
    var body: some View {
        HStack {
            Image("KFC") // Replace with actual restaurant logo
                .resizable()
                .frame(width: 60, height: 60)
                .cornerRadius(10)
            VStack(alignment: .leading, spacing: 4) {
                Text(name)
                    .font(.headline)
                Text(description)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                
                HStack {
                    Text(deliveryTime)
                    Text("· \(distance)")
                    Text("· \(price)")
                }
                .font(.footnote)
                .foregroundColor(.secondary)
            }
            
            Spacer()
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(10)
    }
}
