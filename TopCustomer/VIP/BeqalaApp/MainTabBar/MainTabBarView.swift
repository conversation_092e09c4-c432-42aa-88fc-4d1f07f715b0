//
//  TabView.swift
//  BeqalaApp
//
//  Created by <PERSON><PERSON> on 09/12/2024.
//

import SwiftUI

struct MainTabBarView: View {
    @State private var selectedTab = 0
    
    var body: some View {
        TabView(selection: $selectedTab) {
            
            // مرر الـ binding لمين هوم فيو
            NewMainHomeView(selectedTab: $selectedTab)
                .tabItem {
                    Label("Grocery", systemImage: "house")
                }
                .tag(0)
            
            OffersView()
                .tabItem {
                    Label("Offers", systemImage: "bag.circle")
                }
                .tag(1)
            
            SearchView(selectedTab: $selectedTab)
                .tabItem {
                    Label("Search", systemImage: "magnifyingglass")
                }
                .tag(2)
            
            OrdersListView(selectedTab: $selectedTab)
                .tabItem {
                    Label("Orders", systemImage: "cube.box")
                }
                .tag(3)
            
            ProfileView()
                .tabItem {
                    Label("Account", systemImage: "gearshape")
                }
                .tag(4)
        }
    }
}

#Preview {
    MainTabBarView()
}

