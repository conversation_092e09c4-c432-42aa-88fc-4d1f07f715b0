//
//  MainCategoriesView.swift
//  BeqalaApp
//
//  Created by <PERSON><PERSON> on 15/12/2024.
//

import SwiftUI

struct MainCategoriesView: View {
    @Environment(\.dismiss) var dismiss
    @State private var quantity: Int = 1
    
    var body: some View {
        ScrollView() {
            VStack {
                // Navigation Title
                Text("New Coffee")
                    .font(.title)
                    .bold()
                    .padding(.top, 20)
                
                Spacer()
                
                // Product Card
                HStack {
                    // Product Image
                    Image("coffe") // Replace with actual image
                        .resizable()
                        .scaledToFit()
                        .frame(width: 80, height: 80)
                        .background(Color.gray.opacity(0.2))
                        .cornerRadius(10)
                    
                    // Product Details
                    VStack(alignment: .leading) {
                        Text("coffee")
                            .font(.headline)
                        
                        Text("Carbonated Drink\nWith Date Extract")
                            .font(.subheadline)
                            .foregroundColor(.gray)
                            .lineLimit(2)
                        
                        Spacer()
                        
                        // Price
                        Text("SAR 3.50")
                            .font(.title3)
                            .bold()
                            .foregroundColor(.green)
                    }
                    .padding(.leading, 10)
                    
                    Spacer()
                    
                    // Quantity Buttons
                    VStack {
                        Button(action: {
                            if quantity > 1 { quantity -= 1 }
                        }) {
                            Image(systemName: "minus.circle.fill")
                                .foregroundColor(.red)
                                .font(.title2)
                        }
                        .padding(.bottom, 5)
                        
                        Text("\(quantity)")
                            .font(.title2)
                            .frame(width: 30, alignment: .center)
                        
                        Button(action: {
                            quantity += 1
                        }) {
                            Image(systemName: "plus.circle.fill")
                                .foregroundColor(.green)
                                .font(.title2)
                        }
                        .padding(.top, 5)
                    }
                }
                .padding()
                .background(RoundedRectangle(cornerRadius: 15)
                    .fill(Color.white)
                    .shadow(color: Color.gray.opacity(0.3), radius: 5))
                .padding(.horizontal)
                
                Spacer()
                
                // Bottom Bar
                HStack {
                    Text("SAR \(String(format: "%.2f", 3.5 * Double(quantity)))")
                        .font(.title3)
                        .bold()
                    
                    Spacer()
                    
                    Button(action: {
                        // Action for viewing the cart
                        dismiss()
                    }) {
                        Text("View Cart")
                            .bold()
                            .foregroundColor(.white)
                            .padding()
                            .background(Capsule().fill(Color.blue))
                    }
                }
                .padding()
                .background(Color(UIColor.systemGray6))
            }
            .edgesIgnoringSafeArea(.bottom)
        }
    }
}

#Preview {
    MainCategoriesView()
}
