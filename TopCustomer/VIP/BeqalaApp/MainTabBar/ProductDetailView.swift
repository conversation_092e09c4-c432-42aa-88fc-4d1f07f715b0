//
//  ProductDetailView.swift
//  BeqalaApp
//
//  Created by <PERSON><PERSON> on 15/12/2024.
//

import SwiftUI

struct ProductDetailView: View {
    @Environment(\.dismiss) var dismiss
    @State private var quantity: Int = 3 // Example initial quantity
    @State private var isHeartSelected: Bool = false
    
    var body: some View {
        ScrollView() {
            VStack(spacing: 20) {
                // Close Button and Favorite
                HStack {
                    Button(action: {
                        // Close action
                        dismiss()
                    }) {
                        Image(systemName: "xmark")
                            .foregroundColor(.black)
                            .font(.title2)
                            .padding()
                    }
                    
                    Spacer()
                    
                    Button(action: {
                        // Favorite action
                        isHeartSelected.toggle()
                    }) {
                        Image(systemName: "heart.fill")
                            .foregroundColor(isHeartSelected ? .red : .gray)
                            .font(.title2)
                            .padding()
                        
                    }
                }
                
                // Product Image
                Image("coffe") // Replace with actual image
                    .resizable()
                    .scaledToFit()
                    .padding()
                
                // Product Title and Price
                VStack{
                    Text("Coffee Now’s")
                        .font(.headline)
                        .fontWeight(.bold)
                        .frame(maxWidth: .infinity, alignment: .leading)
                    //                            .padding()
                    Text("SAR 14")
                        .font(.headline)
                        .fontWeight(.bold)
                        .frame(maxWidth: .infinity, alignment: .leading)
                    //                            .padding()
                }.padding()
                // Description Section
                VStack(alignment: .leading, spacing: 10) {
                    Text("Overview")
                        .font(.headline)
                    
                    Text("""
                    Savor every sip of ‘Coffee Now’s Flat White,’ a harmony of robust espresso fused with velvety milk. This coffee blend promises a creamy texture and rich flavor, ideal for kick-starting your morning or boosting your afternoon. Perfect for those who appreciate a sophisticated, full-bodied coffee experience. Enjoy the luxury of cafe-quality coffee from the comfort of your home.
                    """)
                    .font(.body)
                    .foregroundColor(.gray)
                }
                .padding(.horizontal)
                
                Spacer()
                
                // Bottom Bar
                HStack {
                    // Cart Total
                    HStack {
                        Image(systemName: "dollarsign.gauge.chart.leftthird.topthird.rightthird")
                            .foregroundColor(.black)
                            .font(.title2)
                        
                        Text("SAR 3.5")
                            .font(.title3)
                            .bold()
                    }
                    
                    Spacer()
                    
                    // Add to Cart Button
                    Button(action: {
                        // Add to cart action
                        dismiss()
                    }) {
                        Text("Add to cart")
                            .bold()
                            .frame(maxWidth: .infinity)
                            .padding()
                            .foregroundColor(.white)
                            .background(Capsule().fill(Color.blue))
                    }
                    .frame(maxWidth: 200)
                }
                .padding()
                .background(Color(UIColor.systemGray6))
            }
            .padding(.top, 10)
            .background(Color.white)
            .edgesIgnoringSafeArea(.bottom)
            
        }
    }
}

#Preview {
    ProductDetailView()
}
