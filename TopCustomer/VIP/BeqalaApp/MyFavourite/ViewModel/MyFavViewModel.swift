//
//  MyFavViewModel.swift
//  TopCustomer
//
//  Created by macintosh on 25/06/2025.
//  Copyright © 2025 SOTSYS203. All rights reserved.
//

import Foundation
import Combine


@MainActor
class MyFavViewModel: ObservableObject {
    
    @Published var favData : FavouriteListResponse?
    
    func apiCallForGetFavoriteProducts(dictData:[String:Any], flagLoader: Bool) {
        if flagLoader == true {
            ActivityIndicator.shared.showCentralSpinner()
        }
    
        let authorization = getAuthorizationText()
    
        FavouriteAPI.favouriteListing(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, offset: Int(setDataInString(dictData["offset"] as AnyObject)) ?? 0) { data, error in
            ActivityIndicator.shared.hideCentralSpinner()
            print(data)
            self.favData = data
        }
    
    }
    
}
