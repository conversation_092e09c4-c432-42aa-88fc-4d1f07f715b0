//
//  MyFavouritesView.swift
//  TopCustomer
//
//  Created by macintosh on 25/06/2025.
//  Copyright © 2025 SOTSYS203. All rights reserved.
//

import SwiftUI

struct MyFavouritesView: View {
    @StateObject var viewModel = MyFavViewModel()
    
    var body: some View {
        NavigationStack {
            VStack(alignment: .leading) {
                ScrollView {
                    LazyVGrid(columns: [GridItem(.adaptive(minimum: 120))], spacing: 5) {
                        
                        let products = viewModel.favData?.responseData?.productList ?? []
                        
                        ForEach(products.indices, id: \.self) { index in
                            let product = products[index]
                            LazyVStack {
                                ProductItemView(
                                    id: product.biProductId ?? 0,
                                    name: product.vProductName ?? "",
                                    amount: product.price ?? 0.0,
                                    imageUrl: product.vProductImage ?? ""
                                ) { addedItem in
                                    debugPrint(addedItem)
                                }
                            }
                        }
                    }
                    .padding(.horizontal)
                }
            }
            .onAppear {
                let dictParam: [String: Any] = ["offset": 1]
                viewModel.apiCallForGetFavoriteProducts(dictData: dictParam, flagLoader: true)
            }
            .navigationTitle("My Favourite")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarHidden(true)
            .background(Color(UIColor.systemGroupedBackground))
        }
    }
}

#Preview {
    MyFavouritesView()
}
