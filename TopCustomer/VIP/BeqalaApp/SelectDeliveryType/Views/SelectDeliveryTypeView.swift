//
//  SelectDeliveryTypeView.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 02/03/2025.
//  Copyright © 2025 SOTSYS203. All rights reserved.
//

import SwiftUI

struct SelectDeliveryTypeView: View {
    @StateObject private var viewModel = SelectDeliveryTypeViewModel()
    @Environment(\.dismiss) var dismiss
    @StateObject private var tabRouter = TabRouter()
    let columns = [
        GridItem(.flexible()),
        GridItem(.flexible()),
        GridItem(.flexible())
    ]

    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // Title Bar
                Group {
                    Rectangle()
                        .fill(Color.black)
                        .frame(width: 50, height: 3)
                        .cornerRadius(5)
                }

                Text("Where would you like to order today?")
                    .font(.headline)
                    .padding(.top, 20)
                    .frame(maxWidth: .infinity, alignment: .leading)

                // Delivery Options
                HStack(spacing: 16) {
                    
                    /* --------- Hyper Option --------- */
                    VStack {
                        Button {
                            Constant.shared.SELECTED_WAREHOUSE_ID = 0
                            navigateToHomeScreen()
                        } label: {
                            Image("logo")
                                .resizable()
                                .scaledToFit()
                                .frame(height: 150)               // ← ارتفاع ثابت
                                .background(Color("AppTheme_BlueColor_#012CDA"))
                                .cornerRadius(15)
                        }

                        Text("Material App")
                            .font(.headline)
                            .frame(maxWidth: .infinity, alignment: .center)
                            .padding(.top, 20)
                    }
                    .frame(maxWidth: .infinity)                   // ↔︎ يأخذ نصف العرض
                    
                    
                    /* --------- Market Option --------- */
                    VStack {
                        Button {
                            goToMainTabBar(selectedWarehouseId: 1)
                        } label: {
                            Image("hypermarket")
                                .resizable()
                                .scaledToFit()
                                .frame(height: 150)               // ← نفس الارتفاع بالضبط
                                .background(Color.white)          // عشان تشبه الأولى
                                .cornerRadius(15)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 15)
                                        .stroke(Color.black, lineWidth: 2)
                                )
                        }

                        Text("Market App")
                            .font(.headline)
                            .frame(maxWidth: .infinity, alignment: .center)
                            .padding(.top, 20)
                    }
                    .frame(maxWidth: .infinity)                   // ↔︎ نفس العرض
                }
                .padding()

                // More Options
                Text("More stores in your area")
                    .font(.headline)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(.top, 20)

                if viewModel.warehouses.count >= 1 {
                    LazyVGrid(columns: columns, spacing: 16) {
                        ForEach(viewModel.warehouses) { item in
                            Button {
                                goToMainTabBar(selectedWarehouseId: item.id ?? 1)
                            } label: {
                                VStack(spacing: 10) {
                                    AsyncImage(url: URL(string: item.image ?? "")) { image in
                                        image.resizable()
                                            .scaledToFill()
                                            .frame(width: 80, height: 80)
                                            .clipped()
                                            .cornerRadius(8)
                                    } placeholder: {
                                        Image("logo")
                                            .resizable()
                                            .scaledToFill()
                                            .frame(width: 80, height: 80)
                                            .clipped()
                                            .cornerRadius(8)
                                    }

                                    Text(item.name ?? "")
                                        .font(.caption)
                                        .multilineTextAlignment(.center)
                                        .foregroundColor(.black)
                                }
                                .padding()
                                .background(Color.white)
                                .cornerRadius(10)
                                .shadow(radius: 1)
                            }
                        }
                    }
                    .padding(.top, 10)
                } else {
                    Text("Your location is not supported yet")
                        .font(.callout)
                        .foregroundColor(.black)
//                        .onAppear {
//                            Constant.shared.SELECTED_WAREHOUSE_ID = 0
//                            navigateToHomeScreen()
//                        }
                }

                Spacer()
            }
            .padding()
            .background(Color.white)
            .task {
                print("Start loading")
                   await viewModel.getData()
                   print("Done loading")
                if viewModel.warehouses.isEmpty {
                        Constant.shared.SELECTED_WAREHOUSE_ID = 0
                        navigateToHomeScreen()
                    }
            }
            .onReceive(viewModel.$warehouses) { list in
                print(list)
//                if list.isEmpty {
//                    Constant.shared.SELECTED_WAREHOUSE_ID = 0
//                    navigateToHomeScreen()
//                }
            }
        }
    }
}

#Preview {
    SelectDeliveryTypeView()
}

// Navigation Functions
func goToMainTabBar(selectedWarehouseId: Int) {
    if let window = UIApplication.shared.windows.first {
        Constant.shared.SELECTED_WAREHOUSE_ID = selectedWarehouseId
        window.isUserInteractionEnabled = true
        window.rootViewController = UIHostingController(rootView: MainTabBarView())
        window.makeKeyAndVisible()
    }
}

func navigateToHomeScreen() {
    let vc = homeStoryboard.instantiate(MainTabbarViewController.self)
    let navigationVc = UINavigationController(rootViewController: vc)
    AppDel?.window?.rootViewController = navigationVc
}
