//
//  SelectDeliveryTypeViewModel.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 03/03/2025.
//  Copyright © 2025 SOTSYS203. All rights reserved.
//

import SwiftUI
import Combine

@MainActor
class SelectDeliveryTypeViewModel: ObservableObject {
    
    @Published var warehouses: [WarehouseData] = []
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    func getImage(item: WarehouseData) -> String? {
        if let url = URL(string: item.image ?? "") {
            return url.absoluteString
        } else {
            return nil
        }
    }
    
    func getData() async {
        guard let url = URL(string: "https://app.materiel.sa/api-market/warehouses") else { return }
        
        
        isLoading = true
        
        do {
            let fetchedUsers = try await APIClient.fetch(url: url, model: WarehouseModel.self)
            warehouses = fetchedUsers.data ?? []
        } catch {
            errorMessage = error.localizedDescription
        }
        isLoading = true
        
    }
    
}

struct APIClient {
    static func fetch<T: Decodable>(
        url: URL,
        model: T.Type,
        additionalHeaders: [String: String]? = nil
    ) async throws -> T {
        
        var request = URLRequest(url: url)
        request.httpMethod = "GET"
        
        // Location headers
        let loc = getGuestLocation()
        if Constant.shared.SELECTED_LATITUDE == 0.0 {
            request.setValue(loc?.lat, forHTTPHeaderField: "lat")
            request.setValue(loc?.lng, forHTTPHeaderField: "long")
        } else {
            request.setValue("\(Constant.shared.SELECTED_LATITUDE)", forHTTPHeaderField: "lat")
            request.setValue("\(Constant.shared.SELECTED_LONGITUDE)", forHTTPHeaderField: "long")
        }
        
        // Default headers
        request.setValue("application/json", forHTTPHeaderField: "Accept")
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        // Additional headers like warehouse
        additionalHeaders?.forEach { key, value in
            request.setValue(value, forHTTPHeaderField: key)
        }
        
        let (data, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse,
              (200...299).contains(httpResponse.statusCode) else {
            throw URLError(.badServerResponse)
        }
        
        let decodedData = try JSONDecoder().decode(T.self, from: data)
        return decodedData
    }
}
