//
//  WarehouseModel.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 03/03/2025.
//  Copyright © 2025 SOTSYS203. All rights reserved.
//

import Foundation

struct WarehouseModel : Codable, Identifiable {
    var id = UUID()
    let status : Int?
    let data : [WarehouseData]?
    
    enum CodingKeys: String, CodingKey {
        case status = "status"
        case data = "data"
    }
    
    init(from decoder: Decoder) throws {
        let values = try decoder.container(keyedBy: CodingKeys.self)
        status = try values.decodeIfPresent(Int.self, forKey: .status)
        data = try values.decodeIfPresent([WarehouseData].self, forKey: .data)
    }
    
}

struct WarehouseData : Codable, Identifiable, Hashable {
    var id : Int?
    var name : String?
    var image : String?
    var distance : Double?
    
    enum CodingKeys: String, CodingKey {
        
        case id = "id"
        case name = "name"
        case image = "image"
        case distance = "distance"
    }
    
    init(from decoder: Decoder) throws {
        let values = try decoder.container(keyedBy: CodingKeys.self)
        id = try values.decodeIfPresent(Int.self, forKey: .id)
        name = try values.decodeIfPresent(String.self, forKey: .name)
        image = try values.decodeIfPresent(String.self, forKey: .image)
        distance = try values.decodeIfPresent(Double.self, forKey: .distance)
    }
    
}
