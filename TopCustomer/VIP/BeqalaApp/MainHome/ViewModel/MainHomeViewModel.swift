//
//  MainHomeViewModel.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 03/03/2025.
//  Copyright © 2025 SOTSYS203. All rights reserved.
//

import Combine

@MainActor
class MainHomeViewModel: ObservableObject, Identifiable {
    @Published var banners: [ListResponseFields] = []
    @Published var categories: [CategoryListResponseFields] = []
    @Published var lastOffers: [OfferResponseFields] = []
    @Published var superCategoryList:[SuperCategoryListResponseFields] = []
    @Published var sections: [DynamicSectionResponseFields] = []
    @Published var products: [CategoryProductsResponseFields] = []
    
    func getData() async {
        ActivityIndicator.shared.showCentralSpinner()
        let authorization = User.shared.checkUserLoginStatus() ? getAuthorizationText() : GuestAccessToken
        
        print(authorization)
        
        ProductAPI.dynamicMarketHomeListingBanner(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, iCategoryId: 0) { data, error in
            debugPrint("Home Data => ",data)
            ActivityIndicator.shared.hideCentralSpinner()
            self.banners = data?.responseData?.banners ?? []
            self.categories = data?.responseData?.categories ?? []
            self.superCategoryList = data?.responseData?.superCategoryList ?? []
            self.sections = data?.responseData?.sections?
                .filter { !( $0.banners?.isEmpty ?? true )
                       || !( $0.products?.isEmpty ?? true )
                       || !( $0.lastOffers?.isEmpty ?? true )
                       || !( $0.bundelsList?.isEmpty ?? true ) }
                ?? []
            print("SSS" , self.sections)
            ActivityIndicator.shared.hideCentralSpinner()
        }
    }
    
}
