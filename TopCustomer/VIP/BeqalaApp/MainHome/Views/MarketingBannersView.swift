//
//  MarketingBannersView.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 04/03/2025.
//  Copyright © 2025 SOTSYS203. All rights reserved.
//

import SwiftUI

struct MarketingBannersView: View {
    var imageUrl: String
    var onShareTapped: (() -> Void)?
    
    var body: some View {
        ZStack() {
            // Background
            LinearGradient(gradient: Gradient(colors: [Color.red, Color.black.opacity(0.8)]),
                           startPoint: .top, endPoint: .bottom)
            .edgesIgnoringSafeArea(.all)
            LoadingImageWithUrlView(imageUrl: imageUrl, height: 300)
            HStack() {
                Button {
                    onShareTapped?()
                } label: {
                    Image("share_round")
                        .resizable()
                        .frame(width: 50, height: 50)
                }
                
            }
        }
    }
}

#Preview {
    MarketingBannersView(imageUrl: "https://d3unfb4bm0djwt.cloudfront.net/production/marketing_banner_images/17358099662666.png") {
        debugPrint("")
    }
}
