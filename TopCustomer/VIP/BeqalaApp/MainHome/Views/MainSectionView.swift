//
//  MainSectionView.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 04/03/2025.
//  Copyright © 2025 SOTSYS203. All rights reserved.
//

import SwiftUI

// MARK: - Generic Section View
struct MainSectionView<Content: View>: View {
    let title: String
    let content: Content
    
    init(title: String, @ViewBuilder content: () -> Content) {
        self.title = title
        self.content = content()
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 10) {
            Text(title)
                .font(.title2)
                .bold()
                .padding(.leading)
            
            content
                .padding(.horizontal)
        }
        .padding(.vertical, 10)
        .background(Color(.systemGray6))
        .cornerRadius(10)
        .padding(.horizontal)
    }
}


//#Preview {
//    MainSectionView()
//}
