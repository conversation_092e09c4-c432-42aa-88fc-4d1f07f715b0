//
//  MainHomeView.swift
//  BeqalaApp
//
//  Created by <PERSON><PERSON> on 09/12/2024.
//

import SwiftUI

struct MainHomeView: View {
    @StateObject private var viewModel = MainHomeViewModel()
    @Binding var selectedTab: Int 
    @State private var showingCategory = false
    @State private var showingSubCategory = false
    @State private var showingCartView = false
    @State private var showingProductCategory = false
    @State var isLinkActive = false
    @State var selectedCategoryId: Int = 1
    @State var selectedCategoryName: String = ""
    
    private var subCategoriesDestination: some View {
        SubCategoriesView(selectedTab: $selectedTab, categoryId: $selectedCategoryId)
    }
    
    var body: some View {
        NavigationStack {
            ZStack(alignment: .bottom) {
                ScrollView {
                    VStack(spacing: 0) {
                        // Top bar
                        HStack {
                            Image("logo_login")
                                .resizable()
                                .fixedSize()
                            Spacer()
                            HStack {
                                Text("Deliver to: ")
                                    .font(.subheadline)
                                Constant.shared.SELECTED_ADDRESS_NAME != "" ? Text(Constant.shared.SELECTED_ADDRESS_NAME)
                                    .font(.subheadline)
                                    .fontWeight(.bold)
                                : Text(getGuestLocation()?.addressName ?? "")
                                    .font(.subheadline)
                                    .fontWeight(.bold)
                                
                                Image(systemName: "chevron.down")
                            }
                            .foregroundColor(.black)
                            Spacer()
                            Button(action: {
                                print("Delivery time tapped")
                            }) {
                                Text("In 30 Mins")
                                    .font(.subheadline)
                                    .padding(.horizontal, 10)
                                    .padding(.vertical, 5)
                                    .foregroundColor(.black)
                                    .background(Color.gray.opacity(0.6))
                                    .cornerRadius(10)
                            }
                        }
                        .padding()
                        
                        // Search bar
                        Button(action: {
                            selectedTab = 2
                        }) {
                            HStack {
                                Image(systemName: "magnifyingglass")
                                    .foregroundColor(.gray)
                                Text("Need anything? Freshy, snacks, meat...")
                                    .foregroundColor(.gray)
                                Spacer()
                            }
                            .padding()
                            .background(Color(.systemGray6))
                            .cornerRadius(10)
                        }
                        .padding(.horizontal)
                        
                        // Banner Carousel
                        if !viewModel.banners.isEmpty {         
                            ScrollView(.horizontal, showsIndicators: false) {
                                LazyHStack(spacing: 10) {
                                    ForEach(viewModel.banners) { banner in
                                        Rectangle()
                                            .foregroundColor(.clear)          // عشان اللون ما يبانش
                                            .frame(width: 180, height: 130)
                                            .cornerRadius(10)
                                            .overlay(
                                                AsyncImage(url: URL(string: banner.vImage ?? "")) { img in
                                                    img.resizable()
                                                       .scaledToFill()
                                                       .frame(width: 180, height: 130)
                                                       .clipped()
                                                } placeholder: {
                                                    Image("logo")
                                                        .resizable()
                                                        .scaledToFill()
                                                        .frame(width: 180, height: 130)
                                                        .clipped()
                                                }
                                            )
                                            .cornerRadius(10)
                                            .onTapGesture {
                                                selectedCategoryId   = banner.iProductCategoryId ?? 1
                                                selectedCategoryName = banner.vCategoryName ?? ""
                                                showingCategory.toggle()
                                            }
                                    }
                                }
                                .padding(.horizontal)
                            }
                            .padding(.vertical)
                        }
//                        Spacer()
                        
                        ScrollView(.horizontal, showsIndicators: false) {
                            LazyHStack(spacing: 15) {
                                ForEach(viewModel.superCategoryList) { superCategory in
                                    SuperCategoryView(imageName: superCategory.image ?? "" , categoryTitle : superCategory.name ?? "")
                                        .onTapGesture {
                                            selectedCategoryId = superCategory._id ?? 1
                                            selectedCategoryName = superCategory.name ?? ""
                                            showingSubCategory.toggle()
                                        }
                                }
                            }
                            .padding(.horizontal)
                        }
                        .padding(.vertical)
                        
                        // Categories
                        ScrollView(.horizontal, showsIndicators: false) {
                            LazyHStack(spacing: 15) {
                                ForEach(viewModel.categories) { category in
                                    CategoryImage(imageName: category.vImage ?? "")
                                        .onTapGesture {
                                            selectedCategoryId = category._id ?? 1
                                            selectedCategoryName = category.vName ?? ""
                                            showingCategory.toggle()
                                        }
                                }
                            }
                            .padding(.horizontal)
                        }
                        .padding(.vertical)
                        
                        Spacer()
//                        Image("mainheader")
//                            .resizable()
//                            .padding()
//                            .frame(height: 150)
                        // Cashback Festival
//                        VStack(alignment: .center, spacing: 5) {
//                            HStack {
//                                Text("💰 Cashback Festival")
//                                    .font(.headline)
//                                    .padding(10)
//                                Text("From 1 Dec to 30 Dec")
//                                    .font(.footnote)
//                            }
//                            Text("50% cash back on your 10th order valid on restaurants")
//                                .font(.subheadline)
//                                .foregroundColor(.gray)
//                            
//                            ScrollView{
//                                HStack(spacing: 0) {
//                                    ForEach((1...4).reversed(), id: \.self) {
//                                        ContentCellView(isLast: $0 == 1) // isLast for not showing last line in last cell
//                                    }
//                                }
//                            }.padding()
//                            
//                        }
//                        .background(Color.gray.opacity(0.1))
//                        .cornerRadius(10)
//                        .padding()
                        
                        ScrollView {
                            ForEach(viewModel.sections.indices, id: \.self) { idx in
                                let section = viewModel.sections[idx]

                                VStack(alignment: .leading, spacing: 12) {

                                    // 1️⃣ BANNERS
                                    if let banners = section.banners, !banners.isEmpty {
                                        ForEach(banners.indices, id: \.self) { i in
                                            LoadingImageWithUrlView(
                                                imageUrl: banners[i].marketingBannerImage ?? "",
                                                height: 220
                                            )
                                            .cornerRadius(20)
                                            .onTapGesture {
                                                selectedCategoryId   = banners[i].iProductCategoryId ?? 0
                                                selectedCategoryName = banners[i].vTitle ?? ""
                                                showingCategory.toggle()
                                            }
                                        }
                                    }

                                    // 2️⃣ PRODUCTS
                                    if let productGroups = section.products, !productGroups.isEmpty {
                                        ForEach(productGroups.indices, id: \.self) { g in
                                            let group = productGroups[g]

                                            HStack {
                                                Text(group.categoryName ?? "")
                                                    .font(.headline)
                                                Spacer()
                                                Button("View All 〉") {
                                                    selectedCategoryId   = group.categoryId ?? 0
                                                    selectedCategoryName = group.categoryName ?? ""
                                                    showingCategory.toggle()
                                                }
                                                .foregroundColor(.blue)
                                            }
                                            .padding(.horizontal)

                                            LazyVGrid(columns: [GridItem(.adaptive(minimum: 120))], spacing: 6) {
                                                if let items = group.products {
                                                    ForEach(items.indices, id: \.self) { j in
                                                        ProductItemView(
                                                            id:       items[j]._id ?? 0,
                                                            name:     items[j].name ?? "",
                                                            amount:   items[j].price ?? 0.0,
                                                            imageUrl: items[j].vProductImage ?? ""
                                                        ) { val in
                                                            debugPrint(val)
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }

                                    // 3️⃣ LAST OFFERS
                                    if let offers = section.lastOffers, !offers.isEmpty {
                                        Text(section.typeName?.capitalized ?? "Latest Offers")
                                            .font(.headline)
                                            .padding(.horizontal)

                                        ScrollView(.horizontal, showsIndicators: false) {
                                            HStack(spacing: 10) {
                                                ForEach(offers.indices, id: \.self) { o in
                                                    VStack(alignment: .leading, spacing: 4) {
                                                        
                                                        AsyncImage(url: URL(string: offers[o].vOfferImage ?? "")) { phase in
                                                            if let img = phase.image {
                                                                img.resizable()
                                                                   .scaledToFill()
                                                                   .frame(width: 160, height: 160)
                                                                   .clipped()
                                                            } else if phase.error != nil {
                                                                Color.red.opacity(0.2)
                                                            } else {
                                                                Color.gray.opacity(0.2)
                                                            }
                                                        }
                                                        .cornerRadius(12)

                                                        Text(offers[o].vOfferName ?? "")
                                                            .font(.subheadline)
                                                            .lineLimit(1)
                                                            .truncationMode(.tail)
                                                            .frame(maxWidth: .infinity, alignment: .leading)

                                                        if let desc = offers[o].txOfferDescription,
                                                           !desc.isEmpty {
                                                            Text(desc)
                                                                .font(.caption)
                                                                .foregroundColor(.secondary)
                                                                .lineLimit(2)
                                                                .truncationMode(.tail)
                                                        }
                                                    }
                                                    .frame(width: 160)
                                                }
                                            }
                                            .padding(.horizontal)
                                        }
                                    }
                                }
                                .padding(.vertical)
                                .padding(.horizontal)
                            }
                        }
                        
                        Spacer()
                        Spacer()
                        Spacer()
                        Spacer()
                        Spacer()
                        Spacer()
                        Spacer()
                        Spacer()
                        Spacer()
                        Spacer()
                        Spacer()
                    }
                    
                }
                .navigationDestination(isPresented: $showingCategory) {
                    CategoriesView(categoryId: selectedCategoryId, catgoryName: $selectedCategoryName)
                }
                .navigationDestination(isPresented: $showingSubCategory) {
                    subCategoriesDestination
                }
                .navigationDestination(isPresented: $showingCartView) {
                    CartView(selectedTab: $selectedTab)
                }
                HStack {
                    CartViewButton(title: "View Cart")
                        .background(Color.clear.ignoresSafeArea())
                        .onTapGesture {
                            if !User.shared.checkUserLoginStatus() {
                                if let topVC = UIApplication.shared.topViewController() {
                                    topVC.showNoLoginUserAlert(complation: nil)
                                }
                                return
                            }
                            if CartManager.shared.cartItems?.products?.isEmpty == true {
                                AppSingleton.shared.showCustomPopUpWithOkButton(strButtonTitle: ObjKeymessages.kLABEL_OK, strMessage: "Cart is Empty", showOnTopVC: true) { (isOk) in
                                }
                                return
                            }
                            showingCartView.toggle()
                        }
                }
            }
        }.task {
            await viewModel.getData()
        }
        
        
    }
    
    
}


#Preview {
    MainHomeView(selectedTab: .constant(0))
}

struct CategoryButton: View {
    let title: String
    var body: some View {
        Circle()
            .fill(Color.random.opacity(0.7))
            .frame(width: 80)
            .overlay(
                Text(title)
                    .multilineTextAlignment(.center)
                    .font(.subheadline)
                    .padding(.horizontal, 15)
                    .padding(.vertical, 10)
                    .cornerRadius(20)
            )
        
    }
}

struct CategoryImage: View {
    let imageName: String

    var body: some View {
        RoundedRectangle(cornerRadius: 10)
            .fill(Color.white)
            .frame(width: 80, height: 80)
            .overlay(
                AsyncImage(url: URL(string: imageName), scale: 5) { image in
                    image
                        .resizable()
                        .scaledToFill()
                        .frame(width: 80, height: 80)
                        .cornerRadius(10)
                        .clipped()
                } placeholder: {
                    Image("logo")
                        .resizable()
                        .scaledToFill()
                        .frame(width: 80, height: 80)
                        .cornerRadius(10)
                        .clipped()
                }
            )
    }
}

struct SuperCategoryView: View {
    let imageName: String
    let categoryTitle: String
    
    var body: some View {
        VStack(spacing: 6) {
            ZStack {
                RoundedRectangle(cornerRadius: 10)
                    .fill(Color.white)
                    .frame(width: 80, height: 80)

                AsyncImage(url: URL(string: imageName), scale: 5) { image in
                    image
                        .resizable()
                        .scaledToFill()
                        .frame(width: 80, height: 80)
                        .cornerRadius(10) // ← الزاوية المطلوبة
                        .clipped()
                } placeholder: {
                    Image("logo")
                        .resizable()
                        .scaledToFill()
                        .frame(width: 80, height: 80)
                        .cornerRadius(10)
                        .clipped()
                }
            }

            Text(categoryTitle)
                .font(.caption)
                .foregroundColor(.primary)
                .multilineTextAlignment(.center)
                .lineLimit(2)
                .frame(width: 100)
        }
    }
}

struct OfferCard: View {
    let title: String
    let subtitle: String
    var body: some View {
        VStack(alignment: .leading, spacing: 5) {
            Text(title)
                .font(.headline)
            Text(subtitle)
                .font(.subheadline)
                .foregroundColor(.gray)
        }
        .frame(width: 150, height: 100)
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(10)
    }
}

struct OfferCardImage: View {
    let imageName: String
    var body: some View {
        Rectangle()
            .frame(width: 200, height: 200)
            .background(.white)
            .overlay(
                LoadingImageWithUrlView(imageUrl: imageName, height: 200, width: 200)
            )
    }
}

struct TabButton: View {
    let icon: String
    let label: String
    let isActive: Bool
    
    var body: some View {
        VStack {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(isActive ? .blue : .gray)
            Text(label)
                .font(.caption)
                .foregroundColor(isActive ? .blue : .gray)
        }
        .padding(.top, 5)
    }
}

struct ContentCellView: View {
    var isLast: Bool = false
    var body: some View {
        HStack(spacing: 0) {
            HStack {
                Circle()
                    .fill(Color.gray.opacity(0.3))
                    .frame(width: 40, height: 40)
                    .overlay {
                        Image(systemName: "lock.circle")
                    }
            }
            if !isLast {
                Rectangle().fill(Color.blue)
                    .frame(width: .infinity, height: 4) //, alignment: .leading).padding(.leading, 15.5)//.offset(y: -10)
            }
        }
    }
}

extension CGFloat {
    static func random() -> CGFloat {
        return CGFloat(arc4random()) / CGFloat(UInt32.max)
    }
}

extension Color {
    static var random: Color {
        return Color(red: .random(in: 0...1),
                     green: .random(in: 0...1),
                     blue: .random(in: 0...1))
    }
}

struct AnimatedImage: View {
    var image: Image?
    //  private let imageNames: [String] = ["house.circle","lock.circle","message.circle"]
    
    var body: some View {
        Group {
            image?
                .resizable()
                .scaledToFit()
                .frame(width: 50,height: 50)
        }.onAppear(perform: {
            //      self.animate()
        })
    }
    
    private func animate() {
        var imageIndex: Int = 0
        
        Timer.scheduledTimer(withTimeInterval: 0.025, repeats: true) { timer in
            imageIndex += 1
        }
    }
}

struct CartViewButton: View {
    @ObservedObject var cartManager = CartManager.shared
    var title: String
    
    var body: some View {
        HStack {
            // Item count badge
            ZStack {
                Circle()
                    .fill(Color.black)
                    .frame(width: 30, height: 30)
                    .overlay(
                        RoundedRectangle(cornerRadius: 15)
                            .stroke(.white, lineWidth: 2)
                    )
                Text("\(cartManager.itemCount)")
                    .foregroundColor(.white)
                    .fontWeight(.bold)
            }
            
            // Total price
            Text("\(String(format: "%.2f", cartManager.totalPrice))")
                .foregroundColor(.white)
                .fontWeight(.bold)
            
            Spacer()
            
            // "View Cart" button
            HStack {
                Text(title)
                    .foregroundColor(.white)
                    .fontWeight(.bold)
                Image(systemName: "arrowshape.right.circle.fill")
                    .foregroundColor(.white)
            }
        }
        .padding()
        .background(Color("AppTheme_BlueColor_#012CDA", bundle: .main))
        .cornerRadius(10)
        .shadow(radius: 5)
        .padding(.horizontal)
        .onAppear {
            cartManager.getCartItems()
        }
    }
}
//func goToCartView() {
//    if let window = UIApplication.shared.windows.first {
//        window.rootViewController = UIHostingController(rootView: CartView(selectedTab: $selectedTab))
//        window.makeKeyAndVisible()
//    }
//}
