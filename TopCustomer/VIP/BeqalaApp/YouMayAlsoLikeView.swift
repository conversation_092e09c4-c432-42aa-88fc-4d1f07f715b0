//
//  YouMayAlsoLikeView.swift
//  BeqalaApp
//
//  Created by <PERSON><PERSON> on 07/03/2025.
//

import SwiftUI

struct YouMayAlsoLikeView: View {
    var title: String
    var products: [CartYouMayAlsoLikeProductsResponseFields] = []
    var itemAdded: (Bool) -> Void
    
    var body: some View {
        VStack(alignment: .leading) {
            Text(title)
                .font(.title3)
                .fontWeight(.bold)
                .foregroundStyle(.black)
                .padding(.horizontal)
            
            ScrollView(.horizontal, showsIndicators: false) {
                LazyHStack {
                    ForEach(products.indices, id: \.self) { index in
                        ProductItemView(id: products[index].id ?? 1, name: products[index].name ?? "", amount: products[index].price ?? 0.0, imageUrl: products[index].image ?? "") { value in
                            itemAdded(value)
                        }
                    }
                }
                .padding(.horizontal)
            }
          
        }
        .padding(.vertical)
    }
}

#Preview {
    YouMayAlsoLikeView(title: "Suggested Products") { value in
        debugPrint(value)
    }
}
