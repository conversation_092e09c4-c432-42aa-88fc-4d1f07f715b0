//
//  CartViewModel.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 06/03/2025.
//  Copyright © 2025 SOTSYS203. All rights reserved.
//

import Combine

@MainActor
class CartViewModel: ObservableObject {
    @Published var cartItems: [GetCartResponseFields]? = []
    @Published var mayLikeItems: [CartYouMayAlsoLikeProductsResponseFields]? = []
    @Published var totalPrice: Double  = 0.0
    @Published var totalItemsCount: Int = 0
    @Published var iCartId: Int = 0
    @Published var itemsPrice: Double = 0.0
    @Published var deliveryfees: Double = 0.0
    @Published var discount: Double = 0.0
    @Published var vat: Double = 0.0
    @Published var totalAmount: Double = 0.0
    
    func getTotoalPrice() {
        var total = 0.0
        cartItems?.forEach({ item in
            total += Double((item.iProductQuantity ?? 1)) * (Double(item.productPriceWithoutVAT ?? "0.0") ?? 0.0)
        })
        totalPrice = total
    }
    
    func getCartItems(completion: CheckResultHandler? = nil)  {
        ActivityIndicator.shared.showCentralSpinner()
        
        let authorization = getAuthorizationText()
        
        CartAPI.getMarketCart(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, offset: 0) { data, error in
            debugPrint("Get Cart Items => ", data)
            ActivityIndicator.shared.hideCentralSpinner()
            ActivityIndicator.shared.hideCentralSpinner()
            self.iCartId = data?.responseData?.products?.first?.iCartId ?? 0
            self.itemsPrice = Double(data?.responseData?.dSubTotalWithoutVAT ?? "0.0") ?? 0.0
            self.deliveryfees = Double(data?.responseData?.dDistanceCost ?? "0.0") ?? 0.0
            self.vat = Double(data?.responseData?.dVatCost ?? "0.0") ?? 0.0
            self.totalAmount = Double(data?.responseData?.dSubTotal ?? "0.0") ?? 0.0
            self.cartItems = data?.responseData?.products ?? []
            self.getTotoalPrice()
            self.totalItemsCount = self.cartItems?.count ?? 0
            completion?(true)
            self.getYouMayLikeItems()
        }
    }
    
    
    func getYouMayLikeItems(completion: CheckResultHandler? = nil) {
        ActivityIndicator.shared.showCentralSpinner()
        
        var categoriesIds = ""
        var productIds = ""
        
        cartItems?.forEach({ item in
            productIds.append("\(item.biProductId ?? 0),")
            item.category_ids?.forEach({ val in
                categoriesIds.append("\(val),")
            })
        })
        
        CartAPI.getCartYouMayAlsoLikeProducts(categoryIDS: categoriesIds, productIDS: productIds) { data, error in
            ActivityIndicator.shared.hideCentralSpinner()
            self.mayLikeItems = data?.data ?? []
            ActivityIndicator.shared.hideCentralSpinner()
            completion?(true)
        }
    }
    
    func removeItemFromCart(index: Int) {
        if ((self.cartItems?.indices.contains(index)) != nil) {
            self.cartItems?.remove(at: index)
        }
    }
    
}
