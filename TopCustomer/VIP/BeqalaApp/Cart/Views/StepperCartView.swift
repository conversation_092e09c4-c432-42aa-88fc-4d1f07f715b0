//
//  StepperCartView.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 18/03/2025.
//  Copyright © 2025 SOTSYS203. All rights reserved.
//
import SwiftUI

struct StepperCartViewModel {
    let quantity: Int
    let isAddedItem: Bool
}

struct StepperCartView: View {
    var onQuantityChange: (StepperCartViewModel) -> Void
    @State var count: Int = 0
    
    var body: some View {
        HStack {
            Button(action: {
                if count > 0 {
                    count -= 1
                    onQuantityChange(StepperCartViewModel(quantity: count, isAddedItem: false))
                }
            }) {
                Image(systemName: "minus.circle.fill") // trash.fill
                    .foregroundColor(.white)
                    .padding()
                    .background(Color.red.opacity(0.8))
                    .clipShape(Circle())
                    .frame(width: 30, height: 30)
            }
            Text("\(count)")
                .font(.callout)
                .fontWeight(.bold)
                .frame(width: 30, height: 45)
            
            <PERSON><PERSON>(action: {
                count += 1
                onQuantityChange(StepperCartViewModel(quantity: count, isAddedItem: true))  // Callback when quantity changes
                
            }) {
                Image(systemName: "plus")
                    .foregroundColor(.white)
                    .padding()
                    .background(Color.blue.opacity(0.8))
                    .clipShape(Circle())
                    .frame(width: 30, height: 30)
            }
        }
        .frame(width: 120)
        .background(Color.white)
        .clipShape(Capsule())
        .shadow(radius: 2)
        
    }
}
