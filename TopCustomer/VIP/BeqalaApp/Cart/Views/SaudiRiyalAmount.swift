//
//  SaudiRiyalAmount.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 18/03/2025.
//  Copyright © 2025 SOTSYS203. All rights reserved.
//

import SwiftUI

struct SaudiRiyalAmount: View {
    var amount: Double
    var size: CGFloat? = 18
    var color: Color? = .black
    var fontWight: Font.Weight? = .regular
    var body: some View {
        HStack {
            Image("Saudi_Riyal_Symbol")
                .resizable()
                .frame(width: size, height: size, alignment: .center)
                .foregroundStyle(color ?? .black)
            Text("\(String(format: "%.2f", amount))")
                .font(.callout)
                .foregroundStyle(color ?? .black)
                .fontWeight(fontWight ?? .regular)
        }
    }
}
