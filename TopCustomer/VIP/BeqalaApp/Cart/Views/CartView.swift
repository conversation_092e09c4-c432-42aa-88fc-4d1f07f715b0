//
//  CartView.swift
//  BeqalaApp
//
//  Created by <PERSON><PERSON> on 15/12/2024.
//

import SwiftUI

struct CartView: View {
    @Binding var selectedTab: Int
    @StateObject var viewModel = CartViewModel()
    @State var showCheckoutView: Bool = false
    let minimumOrder: Double = 25.0
    @State private var productQuantity = 1
    
    // Group cart items by category
    private func groupedCartItems() -> [GetCartResponseFields?] {
        return viewModel.cartItems ?? []
    }
    
    // Update total price
    private func updateTotalPrice() {
        viewModel.getTotoalPrice()
    }
    
    // MARK: - Delete Item Function
    private func deleteItem(at index: Int) {
        viewModel.cartItems?.remove(at: index)
    }
    
    var body: some View {
        NavigationStack {
            ScrollView {
                VStack(spacing: 0) {
                    Divider()
                    Divider()
                    // Cart Items
                    ScrollView {
                        LazyVStack(alignment: .leading) {
                            if let items = viewModel.cartItems {
                                ForEach(items.indices, id: \.self) { index in
                                    HStack {
                                        HStack {
                                            LoadingImageWithUrlView(imageUrl: viewModel.cartItems?[index].vProductImage ?? "", height: 50)
                                                .frame(width: 50, height: 50)
                                                .cornerRadius(10)
                                            VStack(alignment: .leading) {
                                                Text(viewModel.cartItems?[index].vProductName ?? "")
                                                    .font(.callout)
                                                    .lineLimit(2)
                                                    .foregroundStyle(.black)
                                                    .frame(width: 200)
                                                SaudiRiyalAmount(amount: Double(viewModel.cartItems?[index].productPriceWithoutVAT ?? "0.0") ?? 0)
                                            }
                                            Spacer()
                                            StepperCartView(count: viewModel.cartItems?[index].iProductQuantity ?? 1) { item  in
                                                viewModel.cartItems?[index].iProductQuantity = item.quantity
                                                if item.quantity == 0 {
                                                    CartManager.shared.apiCallForRemoveFromMarketCart(productId: viewModel.cartItems?[index].biProductId ?? 0) { value in
                                                        viewModel.removeItemFromCart(index: index)
                                                        viewModel.getCartItems() { value in
                                                            self.updateTotalPrice()
                                                        }
                                                    }
                                                } else {
                                                    CartManager.shared.addItemToMarketCart(productId: viewModel.cartItems?[index].biProductId ?? 0, productQuantity: 1, price: viewModel.cartItems?[index].dbPrice ?? "0.0",isAddedItem: item.isAddedItem ? true : false) { value in
                                                        viewModel.getCartItems() { value in
                                                            self.updateTotalPrice()
                                                        }
                                                    }
                                                }
                                            }
                                            .padding()
                                            .frame(width: 90)
                                            Spacer()
                                        }
                                        .padding(.horizontal)
                                        .padding(.vertical, 5)
                                    }
                                }
                            }
                        }
                        
                        Divider()
                        // Suggested Products
                        YouMayAlsoLikeView(title: "Suggested Products", products: viewModel.mayLikeItems ?? [])  { value in
                            viewModel.getCartItems()
                        }
                        Spacer()
                        Spacer()
                        CartViewButton( title: "Checkout")
                            .background(Color.clear.ignoresSafeArea())
                            .onTapGesture {
                                showCheckoutView.toggle()
                            }
                        Spacer()
                        Spacer()
                        Spacer()
                    }.onAppear() {
                        updateTotalPrice()
                    }
                }
            }
            .background(Color.white)
            .navigationTitle("Cart")
            //            .edgesIgnoringSafeArea(.bottom)
            .navigationBarTitleDisplayMode(.inline)
            .navigationDestination(isPresented: $showCheckoutView) {
                CheckoutNewView(selectedTab: $selectedTab,iCartId: viewModel.iCartId, itemsPrice: viewModel.itemsPrice, deliveryfees: viewModel.deliveryfees, vat: viewModel.vat, discount: viewModel.discount, totalAmount: viewModel.totalAmount, iShiftId: 1, iAddressId: CheckoutManager.shared.addressID)
            }
            .task {
                await viewModel.getCartItems()
            }
        }
    }
    
}

// Cart Item Model
struct CartItem: Identifiable {
    var id = UUID()
    var category: String
    var name: String
    var price: Double
    var quantity: Int
}

#Preview {
    CartView(selectedTab: .constant(0))
}

let addressId  = CheckoutManager.shared.addressID
let addressTxt = CheckoutManager.shared.addressType


//func goToCheckoutView(iCartId: Int,
//                      itemsPrice: Double,
//                      deliveryfees: Double,
//                      discount: Double,
//                      vat: Double,
//                      totalAmount: Double,
//                      iShiftId: Int,
//                      iAddressId: Int) {
//
//    let checkoutView = CheckoutNewView(
//        selectedTab: $selectedId,
//        iCartId: iCartId,
//        itemsPrice: itemsPrice,
//        deliveryfees: deliveryfees,
//        vat: vat,
//        discount: discount,
//        totalAmount: totalAmount,
//        iShiftId: iShiftId,
//        iAddressId: addressId,
//        addressTxt: addressTxt
//    )
//
//    if let window = UIApplication.shared.windows.first {
//        window.rootViewController = UIHostingController(rootView: checkoutView)
//        window.makeKeyAndVisible()
//    }
//}
