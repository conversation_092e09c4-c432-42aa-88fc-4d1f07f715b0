//
//  CartManager.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 06/03/2025.
//  Copyright © 2025 SOTSYS203. All rights reserved.
//

import Combine
import UIKit

@MainActor
class CartManager: ObservableObject {
    static var shared = CartManager()
    @Published var itemCount: Int = 0
    @Published var totalPrice: Double = 0.0
    
    @Published var cartItems: GetCartListResponseFields? = nil
    
    private init() {}
       
       func addItemToCart() {
           itemCount += 1
       }
       
       func removeItemFromCart() {
           itemCount = max(0, itemCount - 1)
       }
    
    
    func addItemToCart(productId: Int, productQuantity: Int, price: String, isAddedItem: Bool = true, completion: CheckResultHandler? = nil) {
        
        ActivityIndicator.shared.showCentralSpinner()
        
        let authorization = getAuthorizationText()
        
        CartAPI.addToCart(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, biProductId: productId,
                          tiIsCheck: isAddedItem ? AddToCartIsCheck.Add.rawValue : AddToCartIsCheck.Remove.rawValue, iProductQuantity: 1,
                          dbPrice: price) { data, error in
            debugPrint("add Item To Cart => ", data)
            ActivityIndicator.shared.hideCentralSpinner()
            
            if data?.responseCode != 200  {
                self.displayAlert(string: data?.responseMessage ?? "")
                return
            }
            
            
            self.getCartItems()
            completion?(true)
        }
        
    }
    
    func addItemToMarketCart(productId: Int, productQuantity: Int, price: String, isAddedItem: Bool = true, completion: CheckResultHandler? = nil) {
            
            ActivityIndicator.shared.showCentralSpinner()
            
            let authorization = getAuthorizationText()
            
            CartAPI.addToMarketCart(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, biProductId: productId,
                              tiIsCheck: isAddedItem ? AddToCartIsCheck.Add.rawValue : AddToCartIsCheck.Remove.rawValue, iProductQuantity: 1,
                              dbPrice: price) { [weak self] data, error in
                ActivityIndicator.shared.hideCentralSpinner()
                
                guard let self = self else { return }
                
                if data?.responseCode != 200 {
                    self.displayAlert(string: data?.responseMessage ?? "")
                    completion?(false)
                    return
                }
                
                self.getCartItems()
                completion?(true)
            }
        }
    
    func displayAlert(string: String) {
        AppSingleton.shared.showCustomPopUpWithOkButton(strButtonTitle: ObjKeymessages.kLABEL_OK, strMessage: string, showOnTopVC: true) { (isOk) in
        }
    }
    
    
    func getCartItems() {
        
        ActivityIndicator.shared.showCentralSpinner()
        
        let authorization = getAuthorizationText()
        
        CartAPI.getCart(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, offset: 0) { data, error in
            debugPrint("Get Cart Items => ", data)
            ActivityIndicator.shared.hideCentralSpinner()
            ActivityIndicator.shared.hideCentralSpinner()
            self.cartItems = data?.responseData
            let products = data?.responseData?.products ?? []
            self.itemCount = products.count
            self.totalPrice = Double(data?.responseData?.dSubTotalWithoutVAT ?? "") ?? 0.0
//            self.totalPrice = products.reduce(into: 0.0) { $0 + (Double($1.totalProductPriceWithVAT ?? 0.0)) }
        }
    }
    
    func apiCallForRemoveFromCart(productId: Int, completion: CheckResultHandler? = nil) {
        
        ActivityIndicator.shared.showCentralSpinner()
        
        let authorization = getAuthorizationText()
        
        CartAPI.removeFromCart(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, biProductId: productId) { data, error in
            ActivityIndicator.shared.hideCentralSpinner()
            debugPrint("apiCallForRemoveFromCart => ", data ?? "")
            ActivityIndicator.shared.hideCentralSpinner()
            completion?(true)
        }
        
    }
    
    func apiCallForRemoveFromMarketCart(productId: Int, completion: CheckResultHandler? = nil) {
        
        ActivityIndicator.shared.showCentralSpinner()
        
        let authorization = getAuthorizationText()
        
        CartAPI.removeFromMarketCart(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, biProductId: productId) { data, error in
            ActivityIndicator.shared.hideCentralSpinner()
            debugPrint("apiCallForRemoveFromCart => ", data ?? "")
            ActivityIndicator.shared.hideCentralSpinner()
            completion?(true)
        }
        
    }
    
    func apiCallForCartYouMayAlsoLikeProducts(categoryIDS: String, productIDS: String) {
        ActivityIndicator.shared.showCentralSpinner()
        CartAPI.getCartYouMayAlsoLikeProducts(categoryIDS: categoryIDS, productIDS: productIDS) { data, error in
            ActivityIndicator.shared.hideCentralSpinner()
            debugPrint("apiCallForCartYouMayAlsoLikeProducts => ", data ?? "")
        }
    }
    
}
