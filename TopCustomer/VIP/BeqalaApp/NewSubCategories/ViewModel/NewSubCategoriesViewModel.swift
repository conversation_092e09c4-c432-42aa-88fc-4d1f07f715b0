//
//  NewSubCategoriesViewModel.swift
//  TopCustomer
//
//  Created by macintosh on 31/07/2025.
//  Copyright © 2025 SOTSYS203. All rights reserved.
//

import Foundation
import Combine

@MainActor
class NewSubCategoriesViewModel: ObservableObject {
    
    @Published var productsList: [NewProduct] = []
    @Published var subCategoriesList: [SubCategory] = []
    
    func getCategoriesById(_ category_id: Int? = nil,_ sub_category_id : Int? = nil) {
        
        ActivityIndicator.shared.showCentralSpinner()
        
        let authorization = User.shared.checkUserLoginStatus() ? getAuthorizationText() : GuestAccessToken
        
        ProductAPI.NewSubCategoryProductsListing(authorization: authorization ,accept: AcceptParamForHeader, lang: CurrentAppLang ,category_id: category_id , sub_category_id: sub_category_id ) { data, error in
            self.productsList = data?.data?.products ?? []
            self.subCategoriesList = data?.data?.category?.subCategories ?? []
            ActivityIndicator.shared.hideCentralSpinner()
        }
    }
}
