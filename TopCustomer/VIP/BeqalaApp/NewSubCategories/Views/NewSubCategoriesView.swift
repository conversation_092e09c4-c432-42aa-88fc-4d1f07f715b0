//
//  NewSubCategoriesView.swift
//  TopCustomer
//
//  Created by macintosh on 31/07/2025.
//  Copyright © 2025 SOTSYS203. All rights reserved.
//

import SwiftUI

struct NewSubCategoriesView: View {
    @Binding var selectedTab: Int
    @Binding var categoryId: Int
    @Binding var categoryName: String
    @StateObject private var viewModel = NewSubCategoriesViewModel()
    @ObservedObject private var cartManager = CartManager.shared
    @State private var showingCartView = false
    
    var body: some View {
        NavigationStack {
            mainContentView
                .navigationTitle(categoryName)
                .onAppear {
                    Task { viewModel.getCategoriesById(categoryId, nil) }
                }
                .toolbar {
                    if cartManager.itemCount > 0 {
                        ToolbarItem(placement: .bottomBar) {
                            CartViewButton(
                                title: "View Cart"
                            )
                            .onTapGesture { showingCartView = true }
                        }
                    }
                    
                    ToolbarItem(placement: .navigationBarTrailing) {
                        Button(action: {
                            selectedTab = 2
                        }) {
                            Image(systemName: "magnifyingglass")
                                .foregroundColor(.black)
                        }
                    }
                }
                .navigationDestination(isPresented: $showingCartView) {
                    CartView(selectedTab: $selectedTab)
                }
        }
    }
    
    private var mainContentView: some View {
        VStack(spacing: 8) {
            NewSubCategoriesSelectionView(
                subCategories: viewModel.subCategoriesList,
                onSelectionItem: { selectedSub in
                    viewModel.getCategoriesById(categoryId, selectedSub.id)
                }
            )
            
            productsGrid
        }
    }
        
    private var productsGrid: some View {
        GeometryReader { geometry in
            let horizontalPadding: CGFloat = 16
            let spacing: CGFloat = 10
            let totalSpacing = spacing * 2
            let availableWidth = geometry.size.width - (horizontalPadding * 2) - totalSpacing
            let itemSize = availableWidth / 3
            
            ScrollView {
                LazyVGrid(columns: [
                    GridItem(.fixed(itemSize), spacing: spacing),
                    GridItem(.fixed(itemSize), spacing: spacing),
                    GridItem(.fixed(itemSize), spacing: spacing)
                ], spacing: spacing) {
                    ForEach(viewModel.productsList.indices, id: \.self) { i in
                        productItemView(for: i, size: itemSize)
                            .frame(width: itemSize) // تحديد عرض العنصر
                            .contentShape(Rectangle()) // تحديد منطقة الضغط بالضبط
                    }
                }
                .padding(.horizontal, horizontalPadding)
            }
        }
    }
    
    @ViewBuilder
    private func productItemView(for index: Int, size: CGFloat) -> some View {
        let product = viewModel.productsList[index]
        ProductSquareItemView(
            id: product.id ?? 0,
            name: product.name ?? "",
            amount: product.price ?? 0.0,
            imageUrl: product.image ?? "",
            itemSize: size
        ) { tapped in
            debugPrint(tapped)
        }
    }
}
