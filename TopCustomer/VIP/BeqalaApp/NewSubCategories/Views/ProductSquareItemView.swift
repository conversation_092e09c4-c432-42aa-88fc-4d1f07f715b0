//
//  ProductSquareItemView.swift
//  TopCustomer
//
//  Created by macintosh on 10/08/2025.
//  Copyright © 2025 SOTSYS203. All rights reserved.
//

import SwiftUI

struct ProductSquareItemView: View {
    var id: Int
    var name: String
    var amount: Double
    var imageUrl: String
    var itemSize: CGFloat // حجم العنصر المربع
    @State var showItemDetails: Bool = false
    var itemAdded: (Bool) -> Void
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            ZStack(alignment: .bottomTrailing) {
                LoadingSquareImageWithUrlView(imageUrl: imageUrl)
                    .frame(width: itemSize, height: itemSize)
                    .cornerRadius(12)
                
                But<PERSON>(action: {
                    if !User.shared.checkUserLoginStatus() {
                        if let topVC = UIApplication.shared.topViewController() {
                            topVC.showNoLoginUserAlert(complation: nil)
                        }
                        return
                    }
                    CartManager.shared.addItemToMarketCart(
                        productId: id,
                        productQuantity: 1,
                        price: "\(amount)"
                    ) { value in
                        AppSingletonObj.showAlert(strMessage: "Added to Cart Successfully")
                        itemAdded(value)
                        CartManager.shared.getCartItems()
                    }
                }) {
                    Image(systemName: "plus")
                        .foregroundColor(.black)
                        .padding(6)
                        .background(Color.white)
                        .clipShape(Circle())
                        .shadow(radius: 2)
                }
                .padding(8)
            }
            .frame(width: itemSize, height: itemSize)
            .shadow(radius: 3)
            
            // السعر والاسم
            VStack(alignment: .leading, spacing: 4) {
                SaudiRiyalAmount(amount: amount, size: 12)
                
                Text(name)
                    .font(.system(size: 11))
                    .fontWeight(.bold)
                    .foregroundColor(.black)
                    .lineLimit(2)
            }
            .padding(.horizontal, 4)
        }
        .frame(width: itemSize)
        .padding(8)
        .background(Color.white)
        .cornerRadius(12)
        .onTapGesture {
            showItemDetails.toggle()
        }
        .sheet(isPresented: $showItemDetails) {
            ProductDetailsView(biProductId: id)
        }
    }
}

#Preview {
    ProductSquareItemView(id: 0, name: "name", amount: 34.90, imageUrl: "category01", itemSize: 0.0) { value in
        
    }
}
