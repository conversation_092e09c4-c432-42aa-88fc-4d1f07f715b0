//
//  NewSubCategoriesTapSelectionView.swift
//  TopCustomer
//
//  Created by macintosh on 31/07/2025.
//  Copyright © 2025 SOTSYS203. All rights reserved.
//

import Foundation
import SwiftUI

struct NewSubCategoriesSelectionView: View {
    let subCategories: [SubCategory]
    var onSelectionItem: (SubCategory) -> Void
    
    @State private var selectedSubCategoryId: Int?
    
    var body: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 15) {
                ForEach(subCategories.indices, id: \.self) { idx in
                    subCategoryButton(for: idx)
                }
            }
            .padding(.horizontal)
        }
        .padding(.vertical, 8)
        .onChange(of: subCategories) { newValue in
            guard selectedSubCategoryId == nil, let first = newValue.first else { return }
            selectedSubCategoryId = first.id
            onSelectionItem(first)
        }
    }
    
    private func subCategoryButton(for index: Int) -> some View {
        let sub = subCategories[index]
        return Text(sub.name ?? "")
            .lineLimit(1)
            .padding(.horizontal, 14)
            .padding(.vertical, 8)
            .background(
                selectedSubCategoryId == sub.id
                ? Color.blue.opacity(0.2)
                : Color.gray.opacity(0.15)
            )
            .clipShape(Capsule())
            .id(index)
            .onTapGesture {
                selectedSubCategoryId = sub.id
                onSelectionItem(sub)
            }
    }
}
