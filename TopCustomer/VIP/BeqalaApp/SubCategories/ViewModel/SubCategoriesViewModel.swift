//
//  SubCategoriesViewModel.swift
//  TopCustomer
//
//  Created by macintosh on 16/07/2025.
//  Copyright © 2025 SOTSYS203. All rights reserved.
//

import Foundation
import Combine

@MainActor
class SubCategoriesViewModel: ObservableObject {
    private var cancellables = Set<AnyCancellable>()
    @Published var searchResults: [NewProductResponse] = []
    @Published var categoriesData: [MainCategoriesData] = []
    @Published var isLoading = false
    @Published var errorMessage = ""

    var categoryId = 0
    var currentMainIndex = 0
    var currentSubIndex = 0

    func getCategoriesBySearch(_ value: String, _ categoryId: Int, pageNo: Int, append: Bool = false) {
        ActivityIndicator.shared.showCentralSpinner()
        let authorization = User.shared.checkUserLoginStatus() ? getAuthorizationText() : GuestAccessToken

        ProductAPI.NewProductListing(
            authorization: authorization,
            accept: AcceptParamForHeader,
            lang: CurrentAppLang,
            iCategoryId: categoryId,
            vSerchString: value,
            offset: 1,
            pageNo: pageNo,
            isOnlyBundle: 0
        ) { data, error in
            if let newProducts = data?.data?.products {
                if append {
                    self.searchResults += newProducts
                } else {
                    self.searchResults = newProducts
                }
            }
            ActivityIndicator.shared.hideCentralSpinner()
        }
    }

    func getCategoriesData() async {
        guard let url = URL(string: "https://app.materiel.sa/api-market/categories") else { return }
        isLoading = true
        do {
            let warehouse = Constant.shared.SELECTED_WAREHOUSE_ID > 0 ? "\(Constant.shared.SELECTED_WAREHOUSE_ID)" : nil
            let headers = ["warehouse": "\(warehouse ?? "")"]
            let fetchedUsers = try await APIClient.fetch(url: url, model: CategoriesModel.self, additionalHeaders: headers)
            print(fetchedUsers)
            categoriesData = fetchedUsers.data?.categories ?? []
        } catch {
            errorMessage = error.localizedDescription
        }
        isLoading = false
    }

    func handleScrollDown() {
        guard let currentMain = categoriesData[safe: currentMainIndex] else { return }
        let subCats = currentMain.subCategories ?? []

        if currentSubIndex + 1 < subCats.count {
            currentSubIndex += 1
            let nextSub = subCats[currentSubIndex]
            getCategoriesBySearch("", nextSub._id ?? 0, pageNo: 1, append: true)
        }
    }

    func handleScrollUp() {
        guard let currentMain = categoriesData[safe: currentMainIndex] else { return }
        let subCats = currentMain.subCategories ?? []

        if currentSubIndex - 1 >= 0 {
            currentSubIndex -= 1
            let prevSub = subCats[currentSubIndex]
            getCategoriesBySearch("", prevSub._id ?? 0, pageNo: 1, append: true)
        }
    }

    func getMainIndex(for categoryId: Int) -> Int {
        categoriesData.firstIndex(where: { $0._id == categoryId }) ?? 0
    }

    func getSubIndex(for categoryId: Int, subId: Int) -> Int {
        guard let mainIndex = categoriesData.firstIndex(where: { $0._id == categoryId }),
              let subs = categoriesData[mainIndex].subCategories else { return 0 }
        return subs.firstIndex(where: { $0._id == subId }) ?? 0
    }
}

extension Array {
    subscript(safe index: Int) -> Element? {
        return indices.contains(index) ? self[index] : nil
    }
}
