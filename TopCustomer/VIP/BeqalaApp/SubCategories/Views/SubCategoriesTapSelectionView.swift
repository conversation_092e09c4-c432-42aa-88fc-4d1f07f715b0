//
//  SubCategoriesTapSelectionView.swift
//  TopCustomer
//
//  Created by macintosh on 17/07/2025.
//  Copyright © 2025 SOTSYS203. All rights reserved.
//

import SwiftUI

struct SubCategoriesTapSelectionView: View {
    let categories: [MainCategoriesData]
    let selectedCategoryId: Int
    
    @Binding var currentMainIndex: Int
    @Binding var currentSubIndex: Int

    @State private var categorySelectedIndex = 0
    @State private var selectedCategory = ""
    @State private var selectedSubCategory: String? = nil

    var onSelectionItem: (TabCategoriesSelection) -> Void

    var body: some View {
        VStack(alignment: .leading, spacing: 10) {
            
            // 🔹 MAIN CATEGORIES
            ScrollView(.horizontal, showsIndicators: false) {
                ScrollViewReader { proxy in
                    HStack(spacing: 20) {
                        ForEach(categories.indices, id: \.self) { idx in
                            let isSel = categorySelectedIndex == idx
                            VStack {
                                Text(categories[idx].vName ?? "")
                                    .fontWeight(isSel ? .bold : .regular)
                                    .foregroundColor(isSel ? .black : .gray)
                                    .onTapGesture {
                                        categorySelectedIndex = idx
                                        currentMainIndex = idx
                                        selectedCategory = categories[idx].vName ?? ""
                                        
                                        if let firstSub = categories[idx].subCategories?.first {
                                            selectedSubCategory = firstSub.vName
                                            currentSubIndex = 0
                                            onSelectionItem(.init(
                                                categoryId: categories[idx]._id ?? 0,
                                                subCategoryId: firstSub._id ?? 0
                                            ))
                                        } else {
                                            selectedSubCategory = nil
                                            currentSubIndex = 0
                                            onSelectionItem(.init(
                                                categoryId: categories[idx]._id ?? 0,
                                                subCategoryId: 0
                                            ))
                                        }
                                    }
                                
                                Rectangle()
                                    .frame(height: 2)
                                    .foregroundColor(isSel ? .blue : .clear)
                            }
                            .id(idx) // Add ID for scrolling
                        }
                    }
                    .padding(.horizontal)
                    .onChange(of: categorySelectedIndex) { newValue in
                        withAnimation {
                            proxy.scrollTo(newValue, anchor: .center)
                        }
                    }
                }
            }
            
            // 🔹 SUB CATEGORIES
            if categories.indices.contains(categorySelectedIndex),
               let subs = categories[categorySelectedIndex].subCategories,
               !subs.isEmpty {
                ScrollView(.horizontal, showsIndicators: false) {
                    ScrollViewReader { proxy in
                        HStack(spacing: 15) {
                            ForEach(subs.indices, id: \.self) { subIdx in
                                let sub = subs[subIdx]
                                Text(sub.vName ?? "")
                                    .lineLimit(1)
                                    .padding(.horizontal, 14)
                                    .padding(.vertical, 8)
                                    .background(
                                        selectedSubCategory == sub.vName
                                        ? Color.blue.opacity(0.2)
                                        : Color.gray.opacity(0.15)
                                    )
                                    .clipShape(Capsule())
                                    .id(subIdx) // Add ID for scrolling
                                    .onTapGesture {
                                        selectedSubCategory = sub.vName
                                        currentSubIndex = subIdx
                                        onSelectionItem(.init(
                                            categoryId: categories[categorySelectedIndex]._id ?? 0,
                                            subCategoryId: sub._id ?? 0
                                        ))
                                    }
                            }
                        }
                        .padding(.horizontal)
                        .onChange(of: currentSubIndex) { newValue in
                            withAnimation {
                                proxy.scrollTo(newValue, anchor: .center)
                            }
                        }
                    }
                }
            }
        }
        .padding(.vertical, 8)
        .onChange(of: categories) { newCategories in
            if let idx = newCategories.firstIndex(where: { $0._id == selectedCategoryId }) {
                categorySelectedIndex = idx
                currentMainIndex = idx
                selectedCategory = newCategories[idx].vName ?? ""

                if let firstSub = newCategories[idx].subCategories?.first {
                    selectedSubCategory = firstSub.vName
                    currentSubIndex = 0
                    onSelectionItem(.init(
                        categoryId: newCategories[idx]._id ?? 0,
                        subCategoryId: firstSub._id ?? 0
                    ))
                } else {
                    currentSubIndex = 0
                    onSelectionItem(.init(
                        categoryId: newCategories[idx]._id ?? 0,
                        subCategoryId: 0
                    ))
                }
            }
        }
    }
}
