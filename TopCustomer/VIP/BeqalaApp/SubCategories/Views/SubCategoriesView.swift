//
//  SubCategoriesView.swift
//  TopCustomer
//
//  Created by macintosh on 16/07/2025.
//  Copyright © 2025 SOTSYS203. All rights reserved.
//

import SwiftUI

struct SubCategoriesView: View {
    @Binding var selectedTab: Int
    @Binding var categoryId: Int
    @StateObject private var viewModel = SubCategoriesViewModel()
    @State private var showingCartView = false

    var body: some View {
        NavigationStack {
            VStack(spacing: 8) {

                SubCategoriesTapSelectionView(
                    categories: viewModel.categoriesData,
                    selectedCategoryId: categoryId,
                    currentMainIndex: $viewModel.currentMainIndex,
                    currentSubIndex: $viewModel.currentSubIndex
                ) { cat in
                    let subId = cat.subCategoryId == 0 ? cat.categoryId : cat.subCategoryId
                    viewModel.categoryId = cat.categoryId
                    viewModel.currentMainIndex = viewModel.getMainIndex(for: cat.categoryId)
                    viewModel.currentSubIndex = viewModel.getSubIndex(for: cat.categoryId, subId: cat.subCategoryId)
                    viewModel.getCategoriesBySearch("", subId, pageNo: 1)
                }

                ScrollView {
                    LazyVGrid(columns: [GridItem(.adaptive(minimum: 120))], spacing: 5) {
                        ForEach(viewModel.searchResults.indices, id: \.self) { i in
                            ProductItemView(
                                id: viewModel.searchResults[i].biProductId ?? 0,
                                name: viewModel.searchResults[i].vProductName ?? "",
                                amount: viewModel.searchResults[i].price ?? 0.0,
                                imageUrl: viewModel.searchResults[i].vProductImage ?? ""
                            ) { tapped in
                                debugPrint(tapped)
                            }
                            .onAppear {
                                if i == viewModel.searchResults.count - 1 {
                                    viewModel.handleScrollDown()
                                } else if i == 0 {
                                    viewModel.handleScrollUp()
                                }
                            }
                        }
                    }
                    .padding(.horizontal)
                }
            }
            .navigationTitle("Categories")
            .onAppear {
                Task { await viewModel.getCategoriesData() }
            }
            .toolbar {
                ToolbarItem(placement: .bottomBar) {
                    CartViewButton(
//                        itemCount: CartManager.shared.itemCount,
//                        totalPrice: CartManager.shared.totalPrice,
                        title: "View Cart"
                    )
                    .onTapGesture { showingCartView = true }
                }
            }
            .navigationDestination(isPresented: $showingCartView) {
                CartView(selectedTab: $selectedTab)
            }
        }
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Button(action: {
                    selectedTab = 2
                }) {
                    Image(systemName: "magnifyingglass")
                        .foregroundColor(.black)
                }
            }
        }
    }
}
