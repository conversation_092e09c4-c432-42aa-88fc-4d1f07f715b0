//
//  WalletViewModel.swift
//  TopCustomer
//
//  Created by macintosh on 25/06/2025.
//  Copyright © 2025 SOTSYS203. All rights reserved.
//

import Foundation
import Combine


@MainActor
class WalletViewModel: ObservableObject {
    
    var paymentCardData : CardResponse?
    
    func apiCallForGetCardList() {
        ActivityIndicator.shared.showCentralSpinner()
        
        let authorization = getAuthorizationText()
        
        UserCardsAPI.viewUserCards(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang) { data, error in
            ActivityIndicator.shared.hideCentralSpinner()
            self.paymentCardData = data
        }
    }
    
}
