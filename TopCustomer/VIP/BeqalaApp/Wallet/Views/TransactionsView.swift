//
//  TransactionsView.swift
//  TopCustomer
//
//  Created by macintosh on 25/06/2025.
//  Copyright © 2025 SOTSYS203. All rights reserved.
//

import SwiftUI

struct TransactionsView: View {
    let cards: [GetUserCardDetailResponseFields]
    var body: some View {
        VStack(alignment: .leading) {
            Text("Wallet Transactions")
                .font(.headline)
                .padding(.horizontal)
            
            List(cards.indices, id: \.self) { index in
                let card = cards[index]
                HStack {
                    Image(systemName: "creditcard.fill")
                        .foregroundColor(.gray)
                    
                    VStack(alignment: .leading) {
                        Text(card.vCardType ?? "")
                            .fontWeight(.medium)
                        Text("23 Jun, 2025")
                            .font(.caption)
                            .foregroundColor(.gray)
                    }
                    
                    Spacer()
                    
                    SaudiRiyalAmount(amount: Double("0.0") ?? 0)
                }
                .padding(.vertical, 4)
            }
        }
        .listStyle(PlainListStyle())
    }
}



//struct TransactionsView: View {
//    let cards: [GetUserCardDetailResponseFields]
//    
//    var body: some View {
//        VStack(alignment: .leading) {
//            Text("Wallet Transactions")
//                .font(.headline)
//                .padding(.horizontal)
//            
//            List(cards.indices, id: \.self) { index in
//                let card = cards[index]
//                
//                HStack {
//                    Image(systemName: "creditcard.fill")
//                        .foregroundColor(.gray)
//
//                    VStack(alignment: .leading) {
//                        Text(card.vPaymentDescription ?? "No Description")
//                            .fontWeight(.medium)
//                        
//                        if let txnID = card.vTransactionId {
//                            Text("ID: \(txnID)")
//                                .font(.caption)
//                                .foregroundColor(.gray)
//                        }
//                        
//                        Text("\(card.vCardType ?? "") - \(card.vCardScheme ?? "")")
//                            .font(.caption2)
//                            .foregroundColor(.gray)
//                    }
//
//                    Spacer()
//                }
//                .padding(.vertical, 4)
//            }
//            .listStyle(.plain)
//        }
//    }
//}
