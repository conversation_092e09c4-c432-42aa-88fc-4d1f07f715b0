//
//  WalletView.swift
//  TopCustomer
//
//  Created by macintosh on 23/06/2025.
//  Copyright © 2025 SOTSYS203. All rights reserved.
//

import SwiftUI

struct WalletView: View {
    @StateObject var viewModel = WalletViewModel()
    var body: some View {
        VStack(spacing: 20) {
            // Balance Card
            VStack(alignment: .leading, spacing: 10) {
                Text("Current Balance")
                    .foregroundColor(.gray)
                    .font(.subheadline)
                
                HStack {
                    if let balance = UserDefaults.standard.object(forKey: USERDEFAULTS_INFO_KEY.WALLET_BALANCE) as? String {
                        SaudiRiyalAmount(amount: Double("\(balance)") ?? 0.0 )
                    }
                    Spacer()
                }
                
                Text("Created At: 23 Jun 2025")
                    .font(.footnote)
                    .foregroundColor(.gray)
            }
            .padding()
            .background(Color.white)
            .cornerRadius(12)
            .shadow(radius: 5)
            .padding(.horizontal)
            
            if let cards = viewModel.paymentCardData?.responseData?.userCard {
                TransactionsView(cards: cards)
            } else {
                ZStack {
                    Color.clear 
                    Text("No transactions available")
                        .foregroundColor(.gray)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
            }
            
            Spacer()
        }
        .onAppear {
            viewModel.apiCallForGetCardList()
        }
        .navigationTitle("Wallet")
        .navigationBarTitleDisplayMode(.inline)
        .background(Color(UIColor.systemGroupedBackground))
    }
}

#Preview {
    NavigationView {
        WalletView()
    }
}
