//
//  NewMainHomeView.swift
//  TopCustomer
//
//  Created by mac<PERSON>osh on 24/07/2025.
//  Copyright © 2025 SOTSYS203. All rights reserved.
//

import SwiftUI

struct NewMainHomeView: View {
    @StateObject private var viewModel = NewMainHomeViewModel()
    @ObservedObject private var cartManager = CartManager.shared
    @Binding var selectedTab: Int
    @State private var showingCategory = false
    @State private var showingCartView = false
    @State private var showingBanner = false
    @State var selectedCategoryId: Int = 0
    @State var selectedBannerId: Int = 0
    @State var selectedCategoryName: String = ""
    
    var body: some View {
        NavigationStack {
            VStack(spacing : 10 ) {
                ZStack(alignment: .bottom) {
                    ScrollView {
                        VStack(spacing: 0) {
                            Rectangle()
                                .fill(Color("AppTheme_BlueColor_#012CDA").opacity(0.2))
                                .frame(height: UIApplication.shared.windows.first?.safeAreaInsets.top)
                                .edgesIgnoringSafeArea(.top)
                            
                            topBar
                            searchBar
                            
                            if !viewModel.banners.isEmpty {
                                bannerSection
                            }
                            
                            if !$viewModel.sections.isEmpty {
                                ForEach(viewModel.sections.indices, id: \.self) { index in
                                    sectionView(section: viewModel.sections[index])
                                }
                            }

                            if !viewModel.lastOffers.isEmpty {
                                latestOffersView(offers: viewModel.lastOffers)
                            }
                            
                        }
                    }
                    .navigationDestination(isPresented: $showingBanner) {
                        NewProductsView(bannerId: selectedBannerId , msiId: selectedCategoryId , catgoryName: $selectedCategoryName)
                    }
                    .navigationDestination(isPresented: $showingCategory) {
//                        NewProductsView(bannerId: selectedBannerId , msiId: selectedCategoryId , catgoryName: $selectedCategoryName)
//                        SubCategoriesView(selectedTab: $selectedTab, categoryId: $selectedCategoryId)
                        NewSubCategoriesView(selectedTab: $selectedTab, categoryId: $selectedCategoryId , categoryName: $selectedCategoryName)
                    }
                    .navigationDestination(isPresented: $showingCartView) {
                        CartView(selectedTab: $selectedTab)
                    }
                    .edgesIgnoringSafeArea(.top)
                }
                
                if CartManager.shared.cartItems?.products?.isEmpty != true {
                    HStack {
                        CartViewButton(title: "View Cart")
                            .background(Color.clear.ignoresSafeArea())
                            .onTapGesture {
                                if !User.shared.checkUserLoginStatus() {
                                    if let topVC = UIApplication.shared.topViewController() {
                                        topVC.showNoLoginUserAlert(complation: nil)
                                    }
                                    return
                                }
                                if CartManager.shared.cartItems?.products?.isEmpty == true {
                                    AppSingleton.shared.showCustomPopUpWithOkButton(strButtonTitle: ObjKeymessages.kLABEL_OK, strMessage: "Cart is Empty", showOnTopVC: true) { (isOk) in
                                    }
                                    return
                                }
                                showingCartView.toggle()
                            }
                    }
                }
            }
        }
        .task {
            await viewModel.getData()
            print(cartManager.itemCount)
        }
    }

    private var topBar: some View {
        HStack {
            Image("logo_login")
                .resizable()
                .frame(width: 80, height: 30)

            Spacer()

            HStack {
                Text("Deliver to: ")
                    .font(.subheadline)

                Group {
                    if Constant.shared.SELECTED_ADDRESS_NAME != "" {
                        Text(Constant.shared.SELECTED_ADDRESS_NAME)
                    } else {
                        Text(getGuestLocation()?.addressName ?? "")
                    }
                }
                .font(.subheadline)
                .fontWeight(.bold)

                Image(systemName: "chevron.down")
            }
            .foregroundColor(.black)

            Spacer()

            Button(action: {
                print("Delivery time tapped")
            }) {
                Text("In 30 Mins")
                    .font(.subheadline)
                    .padding(.horizontal, 10)
                    .padding(.vertical, 5)
                    .foregroundColor(.black)
                    .background(Color.gray.opacity(0.6))
                    .cornerRadius(10)
            }
        }
        .padding()
        .background(
            Color("AppTheme_BlueColor_#012CDA", bundle: .main)
                .opacity(0.2)
        )
    }

    private var searchBar: some View {
        Button(action: {
            selectedTab = 2
        }) {
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(.gray)
                Text("Need anything? Freshy, snacks, meat...")
                    .foregroundColor(.gray)
                Spacer()
            }
            .padding()
            .background(Color(.systemGray6))
            .cornerRadius(10)
        }
        .padding(.horizontal)
        .padding(.bottom, 12)
        .background(Color("AppTheme_BlueColor_#012CDA", bundle: .main).opacity(0.2))
    }

    private var bannerSection: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            LazyVGrid(columns: [GridItem(.adaptive(minimum: 120))], spacing: 5) {
                ForEach(viewModel.banners, id: \.uid) { banner in
                    BannerItemView(banner: banner) {
                        selectedBannerId = banner.id ?? 1
                        selectedCategoryName = banner.name ?? ""
                        showingBanner.toggle()
                    }
                }
            }
            .padding(.horizontal)
        }
        .padding(.vertical)
    }
    
    
    private func sectionView(section: SectionCategories) -> some View {
        let columns = Array(repeating: GridItem(.flexible(), spacing: 6), count: 3)

        return VStack(alignment: .leading, spacing: 6) {
            // Section Title
            Text(section.name ?? "")
                .font(.headline)
                .fontWeight(.bold)
                .padding(.horizontal, 6)
                .padding(.top, 16)

            // Section Items
            LazyVGrid(columns: columns, spacing: 6) {
                ForEach(section.categories ?? [], id: \.id) { item in
                    SectionItemView(item: item) {
                        selectedCategoryId = item.id ?? 1
                        selectedCategoryName = item.name ?? ""
                        showingCategory.toggle()
                    }
                }
            }
            .padding(.horizontal, 6)
        }
        .background(Color.white)
        .padding(.bottom, 8)
    }
    
}

struct BannerItemView: View {
    let banner: Banners
    let onTap: () -> Void

    var body: some View {
        ZStack {
            if let imageUrl = banner.image, let url = URL(string: imageUrl) {
                AsyncImage(url: url) { image in
                    image
                        .resizable()
                        .scaledToFill()
                    
                } placeholder: {
                    Image("logo")
                        .resizable()
                        .scaledToFill()
                    
                }
            } else {
                Image("logo")
                    .resizable()
                    .scaledToFill()
                
            }
        }
        .frame(width: 130, height: 150)
        .background(Color.gray.opacity(0.1))
        .cornerRadius(10)
        .onTapGesture {
            onTap()
        }
    }
}

struct SectionItemView: View {
    let item: SectionCategoriesItems
    let onTap: () -> Void

    var body: some View {
        let screenWidth = UIScreen.main.bounds.width
        let spacing: CGFloat = 6
        let itemCountPerRow: CGFloat = 3
        let totalSpacing = spacing * (itemCountPerRow + 1)
        let itemWidth = (screenWidth - totalSpacing) / itemCountPerRow

        ZStack(alignment: .top) {
            ZStack {
//                Color("AppTheme_BlueColor_#012CDA").opacity(0.2)

                if let imageUrl = item.image, let url = URL(string: imageUrl) {
                    AsyncImage(url: url) { image in
                        image
                            .resizable()
                            .scaledToFill()
                            .clipped()
                    } placeholder: {
                        ProgressView()
                            .frame(width: itemWidth, height: itemWidth + 20)
                            .background(Color.gray.opacity(0.2))
                    }
                } else {
                    Image("logo")
                        .resizable()
                        .scaledToFill()
                        .clipped()
                }
            }
            .frame(width: itemWidth, height: itemWidth + 20)
            .cornerRadius(10)
            .clipped()

            Text(item.name ?? "")
                .font(.system(size: 14, weight: .semibold))
                .multilineTextAlignment(.center)
                .lineLimit(2)
                .padding(.top, 10)
                .frame(width: itemWidth * 0.9, alignment: .top)
                .foregroundColor(.primary)
        }
        .onTapGesture {
            onTap()
        }
    }
}
    
    // Latest Offers View
private func latestOffersView(offers: [Latest_offers]) -> some View {
    let columns = Array(repeating: GridItem(.flexible(), spacing: 6), count: 3)
    
    return VStack(alignment: .leading, spacing: 6) {
        Text("Latest Offers")
            .font(.headline)
            .fontWeight(.bold)
            .padding(.horizontal, 6)
            .padding(.top, 16)
        
        LazyVGrid(columns: columns, spacing: 6) {
            ForEach(offers, id: \.iOfferId) { offer in
                LatestOfferItemView(offer: offer)
            }
        }
        .padding(.horizontal, 6)
    }
    .background(Color.white)
    .padding(.bottom, 8)
}

struct LatestOfferItemView: View {
    let offer: Latest_offers
    let screenWidth = UIScreen.main.bounds.width
    
    var body: some View {
        let spacing: CGFloat = 6
        let itemCountPerRow: CGFloat = 3
        let totalSpacing = spacing * (itemCountPerRow + 1) // 4 gaps
        let itemWidth = (screenWidth - totalSpacing) / itemCountPerRow
        
        VStack(alignment: .leading, spacing: 6) {
            if let imageUrl = offer.vOfferImage, let url = URL(string: imageUrl) {
                AsyncImage(url: url) { image in
                    image
                        .resizable()
                        .scaledToFill()
                        .frame(width: itemWidth, height: itemWidth + 20)
                        .clipped()
                        .cornerRadius(10)
                } placeholder: {
                    ProgressView()
                        .frame(width: itemWidth, height: itemWidth + 20)
                }
            } else {
                Image("logo")
                    .resizable()
                    .scaledToFill()
                    .frame(width: itemWidth, height: itemWidth + 20)
                    .clipped()
                    .cornerRadius(10)
            }
        }
    }
}

