//
//  NewMainHomeViewModel.swift
//  TopCustomer
//
//  Created by macintosh on 24/07/2025.
//  Copyright © 2025 SOTSYS203. All rights reserved.
//

import Foundation

@MainActor
class NewMainHomeViewModel: ObservableObject, Identifiable {
    @Published var banners: [Banners] = []
    @Published var lastOffers: [Latest_offers] = []
    @Published var sections: [SectionCategories] = []
    
    func getData() async {
        ActivityIndicator.shared.showCentralSpinner()
        let authorization = User.shared.checkUserLoginStatus() ? getAuthorizationText() : GuestAccessToken
                
        ProductAPI.dynamicNewMarketHomeListingBanner(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, iCategoryId: 0) { data, error in
            debugPrint("Home Data => ",data)
            ActivityIndicator.shared.hideCentralSpinner()
            self.banners = data?.responseData?.banners ?? []
            self.sections = data?.responseData?.sections ?? []
            self.lastOffers = data?.responseData?.latest_offers ?? []
            ActivityIndicator.shared.hideCentralSpinner()
        }
    }
}
