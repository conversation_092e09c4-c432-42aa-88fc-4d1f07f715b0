//
//  NewMainHomeModel.swift
//  TopCustomer
//
//  Created by macintosh on 24/07/2025.
//  Copyright © 2025 SOTSYS203. All rights reserved.
//

import Foundation

public struct NewMainHomeModel: Codable {
    public var responseCode: Int?
    public var responseMessage: String?
    public var responseData: NewMainHomeData?

    public init(responseCode: Int? = nil,
                responseMessage: String? = nil,
                responseData: NewMainHomeData? = nil) {
        self.responseCode = responseCode
        self.responseMessage = responseMessage
        self.responseData = responseData
    }

    public enum CodingKeys: String, CodingKey {
        case responseCode = "status"
        case responseMessage = "message"
        case responseData = "data"
    }
}

public struct NewMainHomeData : Codable {
    let banners : [Banners]?
    let sections : [SectionCategories]?
    let order_again : [String]?
    let latest_offers : [Latest_offers]?
}
public struct Banners : Codable {
    var uid: Int { id ?? UUID().hashValue }
    let id : Int?
    let name : String?
    let image : String?
}
public struct SectionCategories : Codable {
    let name : String?
    let categories : [SectionCategoriesItems]?
}
public struct SectionCategoriesItems : Codable {
    let id : Int?
    let image : String?
    let name : String?
}
public struct Latest_offers : Codable {
    let iOfferId : Int?
    let tiOfferType : Int?
    let biProductId : Int?
    let iBuyProductQuantity : Int?
    let biOfferProductId : Int?
    let iOfferProductQuantity : Int?
    let vOfferName : String?
    let dOfferAmount : String?
    let vOfferImage : String?
    let tiDiscountType : Int?
    let txOfferDescription : String?
    let tsStartDate : String?
    let tsEndDate : String?
    let isAppAdvertisement : Int?
}
