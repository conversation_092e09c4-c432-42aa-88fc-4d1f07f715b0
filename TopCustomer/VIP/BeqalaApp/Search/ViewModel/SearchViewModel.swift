//
//  SearchViewModel.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 09/03/2025.
//  Copyright © 2025 SOTSYS203. All rights reserved.
//

import Foundation
import Combine

@MainActor
class SearchViewModel: ObservableObject {
    
    private var cancellables = Set<AnyCancellable>()
    @Published var searchResults: [NewProductResponse] = []
    @Published var categoriesData: [MainCategoriesData] = []
    @Published var isLoading = false
    @Published var errorMessage = ""
    
    var categoryId = 0
    
    func getCategoriesBySearch(_ value: String,_ categoryId : Int , pageNo: Int) {
        
        ActivityIndicator.shared.showCentralSpinner()
        searchResults.removeAll()
        let authorization = User.shared.checkUserLoginStatus() ? getAuthorizationText() : GuestAccessToken
        
        ProductAPI.NewProductListing(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, iCategoryId: categoryId, vSerchString: value, offset: 1, pageNo: pageNo, isOnlyBundle: 0) { data, error in
            debugPrint(data)
            self.searchResults = data?.data?.products ?? []
            ActivityIndicator.shared.hideCentralSpinner()
        }
    }
    
    func getCategoriesData() async {
        guard let url = URL(string: "https://app.materiel.sa/api-market/categories") else { return }

        isLoading = true
        do {
            let warehouse = Constant.shared.SELECTED_WAREHOUSE_ID > 0 ? "\(Constant.shared.SELECTED_WAREHOUSE_ID)" : nil
            let headers = ["warehouse": "\(warehouse ?? "")"]
            let fetchedUsers = try await APIClient.fetch(url: url, model: CategoriesModel.self, additionalHeaders: headers)
            print(fetchedUsers)
            categoriesData = fetchedUsers.data?.categories ?? []
//            getCategoriesBySearch("", <#Int#>, pageNo: 1)
        } catch {
            errorMessage = error.localizedDescription
        }
        isLoading = false
    }
    
    func bindSearch(_ publisher: Published<String>.Publisher) {
            publisher
                .debounce(for: .milliseconds(300), scheduler: DispatchQueue.main)
                .removeDuplicates()
                .sink { [weak self] query in
                    self?.getCategoriesBySearch(query, 0, pageNo: 1)
                }
                .store(in: &cancellables)
        }
    
}
