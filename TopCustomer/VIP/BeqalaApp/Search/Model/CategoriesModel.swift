//
//  Json4Swift.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 13/03/2025.
//  Copyright © 2025 SOTSYS203. All rights reserved.
//

import Foundation

struct CategoriesModel : Codable {
    let status : Int?
    let data : CategoriesData?
    
    enum CodingKeys: String, CodingKey {
        
        case status = "status"
        case data = "data"
    }
    
    init(from decoder: Decoder) throws {
        let values = try decoder.container(keyedBy: CodingKeys.self)
        status = try values.decodeIfPresent(Int.self, forKey: .status)
        data = try values.decodeIfPresent(CategoriesData.self, forKey: .data)
    }
    
}

struct CategoriesData : Codable, Identifiable {
    let id = UUID()
    
    let categories : [MainCategoriesData]?
    
    enum CodingKeys: String, CodingKey {
        
        case categories = "categories"
    }
    
    init(from decoder: Decoder) throws {
        let values = try decoder.container(keyedBy: CodingKeys.self)
        categories = try values.decodeIfPresent([MainCategoriesData].self, forKey: .categories)
    }
    
}

struct MainCategoriesData : Codable, Identifiable, Equatable {
    static func == (lhs: MainCategoriesData, rhs: MainCategoriesData) -> Bool {
        return lhs._id == rhs._id
    }
    
    let id = UUID()
    let _id : Int?
    let vName : String?
    let vImage : String?
    let subCategories : [SubCategoriesData]?
    
    enum CodingKeys: String, CodingKey {
        
        case _id = "id"
        case vName = "vName"
        case vImage = "vImage"
        case subCategories = "sub_categories"
    }
    
    init(from decoder: Decoder) throws {
        let values = try decoder.container(keyedBy: CodingKeys.self)
        _id = try values.decodeIfPresent(Int.self, forKey: ._id)
        vName = try values.decodeIfPresent(String.self, forKey: .vName)
        vImage = try values.decodeIfPresent(String.self, forKey: .vImage)
        subCategories = try values.decodeIfPresent([SubCategoriesData].self, forKey: .subCategories)
    }
    
}

struct SubCategoriesData : Codable, Identifiable {
    let id = UUID()
    let _id : Int?
    let vName : String?
    let vImage : String?
    
    enum CodingKeys: String, CodingKey {
        
        case _id = "id"
        case vName = "vName"
        case vImage = "vImage"
    }
    
    init(from decoder: Decoder) throws {
        let values = try decoder.container(keyedBy: CodingKeys.self)
        _id = try values.decodeIfPresent(Int.self, forKey: ._id)
        vName = try values.decodeIfPresent(String.self, forKey: .vName)
        vImage = try values.decodeIfPresent(String.self, forKey: .vImage)
    }
    
}
