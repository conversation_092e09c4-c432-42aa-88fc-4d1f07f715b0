//
//  TabCategoriesSelectionView.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 13/03/2025.
//  Copyright © 2025 SOTSYS203. All rights reserved.
//

import SwiftUI

struct TabCategoriesSelection {
    let categoryId: Int
    let subCategoryId: Int
}

// MARK: - TabCategoriesSelectionView
struct TabCategoriesSelectionView: View {
    let categories: [MainCategoriesData]
    
    @State private var categorySelectedIndex = 0          // يبدأ بالأول
    @State private var selectedCategory      = ""
    @State private var selectedSubCategory: String? = nil
    
    var onSelectionItem: (TabCategoriesSelection) -> Void
    
    var body: some View {
        VStack(alignment: .leading, spacing: 10) {
            
            /* ---------- MAIN CATEGORIES ---------- */
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 20) {
                    ForEach(categories.indices, id: \.self) { idx in
                        let isSel = categorySelectedIndex == idx
                        VStack {
                            Text(categories[idx].vName ?? "")
                                .fontWeight(isSel ? .bold : .regular)
                                .foregroundColor(isSel ? .black : .gray)
                                .onTapGesture {
                                    categorySelectedIndex = idx
                                    selectedCategory = categories[idx].vName ?? ""
                                    
                                    if let firstSub = categories[idx].subCategories?.first {
                                        selectedSubCategory = firstSub.vName
                                        onSelectionItem(.init(
                                            categoryId: categories[idx]._id ?? 0,
                                            subCategoryId: firstSub._id ?? 0
                                        ))
                                    } else {
                                        selectedSubCategory = nil
                                        onSelectionItem(.init(
                                            categoryId: categories[idx]._id ?? 0,
                                            subCategoryId: 0
                                        ))
                                    }
                                }
                            
                            Rectangle()
                                .frame(height: 2)
                                .foregroundColor(isSel ? .blue : .clear)
                        }
                    }
                }
                .padding(.horizontal)
            }
            .frame(maxWidth: .infinity)        // ← يفرد للآخر
            
            /* ---------- SUB-CATEGORIES ---------- */
            if categories.indices.contains(categorySelectedIndex),       // ← سطر الأمان
               let subs = categories[categorySelectedIndex].subCategories,
               !subs.isEmpty {
                
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 15) {
                        ForEach(subs.indices, id: \.self) { subIdx in
                            let sub = subs[subIdx]
                            Text(sub.vName ?? "")
                                .lineLimit(1)
                                .padding(.horizontal, 14)
                                .padding(.vertical, 8)
                                .background(
                                    selectedSubCategory == sub.vName
                                    ? Color.blue.opacity(0.2)
                                    : Color.gray.opacity(0.15)
                                )
                                .clipShape(Capsule())
                                .onTapGesture {
                                    selectedSubCategory = sub.vName
                                    onSelectionItem(.init(
                                        categoryId: categories[categorySelectedIndex]._id ?? 0,
                                        subCategoryId: sub._id ?? 0
                                    ))
                                }
                        }
                    }
                    .padding(.horizontal)
                    .frame(maxWidth: .infinity, alignment: .leading)   // ← ياخد عرض الشاشة
                }
            }
        }
        .padding(.vertical, 8)
        .onAppear {
            // أوّل تحميل: حدّد أوّل Category تلقائيًا
            if !categories.isEmpty {
                selectedCategory = categories[0].vName ?? ""
                onSelectionItem(.init(
                    categoryId: categories[0]._id ?? 0,
                    subCategoryId: 0
                ))
            }
        }
    }
}
