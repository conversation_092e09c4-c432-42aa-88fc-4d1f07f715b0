//
//  SearchView.swift
//  BeqalaApp
//
//  Created by <PERSON><PERSON> on 10/12/2024.
//

import SwiftUI

// MARK: - SearchView
struct SearchView: View {
    @Binding var selectedTab: Int
    @StateObject private var viewModel = SearchViewModel()
    @ObservedObject private var cartManager = CartManager.shared
    @State private var searchText       = ""
    @State private var showingCartView  = false
    
    var body: some View {
        NavigationStack {
            VStack(spacing: 0) {
                // ⬅️ Tabs (Main + Sub)
                TabCategoriesSelectionView(categories: viewModel.categoriesData) { cat in
                    let subId = cat.subCategoryId == 0 ? cat.categoryId : cat.subCategoryId
                    viewModel.categoryId = cat.categoryId
                    viewModel.getCategoriesBySearch(searchText, subId, pageNo: 1)
                }
                
                // ⬅️ نتائج البحث (التصميم الجديد)
                searchResultsGrid
            }
            .navigationTitle("Search")
            .searchable(text: $searchText, prompt: "Search items")
            .task(id: searchText) {
                let query = searchText.trimmingCharacters(in: .whitespacesAndNewlines)
                guard !query.isEmpty else { return }
                try? await Task.sleep(for: .milliseconds(300))
                await viewModel.getCategoriesBySearch(query, 0, pageNo: 1)
            }
            .onSubmit(of: .search) {
                viewModel.getCategoriesBySearch(searchText, 0, pageNo: 1)
            }
            .onAppear {
                Task { await viewModel.getCategoriesData() }
            }
            .toolbar {
                if cartManager.itemCount > 0 {
                    ToolbarItem(placement: .bottomBar) {
                        CartViewButton(
                            title: "View Cart"
                        )
                        .onTapGesture { showingCartView = true }
                    }
                }
                
            }
            .navigationDestination(isPresented: $showingCartView) {
                CartView(selectedTab: $selectedTab)
            }
        }
    }
    
    private var searchResultsGrid: some View {
        GeometryReader { geometry in
            let horizontalPadding: CGFloat = 16
            let spacing: CGFloat = 10
            let totalSpacing = spacing * 2
            let availableWidth = geometry.size.width - (horizontalPadding * 2) - totalSpacing
            let itemSize = availableWidth / 3
            
            ScrollView {
                LazyVGrid(columns: [
                    GridItem(.flexible(), spacing: spacing),
                    GridItem(.flexible(), spacing: spacing),
                    GridItem(.flexible())
                ], spacing: spacing) {
                    ForEach(viewModel.searchResults.indices, id: \.self) { i in
                        ProductSquareItemView(
                            id: viewModel.searchResults[i].biProductId ?? 0,
                            name: viewModel.searchResults[i].vProductName ?? "",
                            amount: viewModel.searchResults[i].price ?? 0.0,
                            imageUrl: viewModel.searchResults[i].vProductImage ?? "",
                            itemSize: itemSize
                        ) { tapped in
                            debugPrint(tapped)
                        }
                    }
                }
                .padding(.horizontal, horizontalPadding)
            }
        }
    }
}

#Preview {
    SearchView(selectedTab: .constant(2))
}
