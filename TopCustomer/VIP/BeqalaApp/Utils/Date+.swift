//
//  Date+.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 08/03/2025.
//  Copyright © 2025 SOTSYS203. All rights reserved.
//

import Foundation

extension Date {
    // You can check if today is Friday in Swift using the Calendar and Date APIs. Here’s a simple way to do it:
    func todayisFriday() -> Bool {
        let today = Date()
        var calendar = Calendar(identifier: .gregorian)
        calendar.timeZone = TimeZone(identifier: "Asia/Riyadh")! // Set timezone to Saudi Arabia
        
        let weekday = calendar.component(.weekday, from: today)
        
        if weekday == 6 { // Friday in the Gregorian calendar
            debugPrint("Today is Friday in Saudi Arabia!")
            return true
        } else {
            debugPrint("Today is not Friday in Saudi Arabia.")
            return false
        }
    }
}
