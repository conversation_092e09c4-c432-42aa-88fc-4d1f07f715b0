//
//  LoadingImageWithUrlView.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 05/03/2025.
//  Copyright © 2025 SOTSYS203. All rights reserved.
//

import SwiftUI

struct LoadingImageWithUrlView: View {
    var imageUrl: String
    var height: Double
    var width: Double?

    var body: some View {
        AsyncImage(url: URL(string: imageUrl)) { phase in
            switch phase {
            case .empty:
                ProgressView()
                    .progressViewStyle(.circular)
                    .frame(width: width ?? .infinity, height: height)
                    .background(Color.white)

            case .success(let image):
                image
                    .resizable()
                    .frame(width: width ?? .infinity, height: height)
                    .cornerRadius(5)
                    .background(Color.white)

            case .failure(_):
                Image("logo") // اسم الصورة البديلة في الـ Assets
                    .resizable()
                    .frame(width: width ?? .infinity, height: height)
                    .cornerRadius(5)
                    .background(Color.white)

            @unknown default:
                Color.gray.frame(width: width ?? .infinity, height: height)
            }
        }
    }
}

#Preview {
    LoadingImageWithUrlView(imageUrl: "", height: 200)
}
