//
//  LoadingSquareImageWithUrlView.swift
//  TopCustomer
//
//  Created by macintosh on 10/08/2025.
//  Copyright © 2025 SOTSYS203. All rights reserved.
//

import SwiftUI

import SwiftUI

struct LoadingSquareImageWithUrlView: View {
    var imageUrl: String

    var body: some View {
        AsyncImage(url: URL(string: imageUrl)) { phase in
            switch phase {
            case .empty:
                ProgressView()
                    .progressViewStyle(.circular)
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                    .background(Color.gray.opacity(0.1))
            
            case .success(let image):
                image
                    .resizable()
                    .scaledToFill()
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
            
            case .failure:
                Image("logo")
                    .resizable()
                    .scaledToFit()
                    .padding()
                    .background(Color.gray.opacity(0.1))
            
            @unknown default:
                Color.gray
            }
        }
        .clipped() // قص الأجزاء الزائدة
    }
}


#Preview {
    LoadingSquareImageWithUrlView(imageUrl: "")
}
