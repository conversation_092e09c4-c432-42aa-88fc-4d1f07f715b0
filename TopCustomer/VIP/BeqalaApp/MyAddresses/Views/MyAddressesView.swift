//
//  MyAddressesView.swift
//  TopCustomer
//
//  Created by mac<PERSON>osh on 25/06/2025.
//  Copyright © 2025 SOTSYS203. All rights reserved.
//

import SwiftUI

struct MyAddressesView: View {
    @StateObject var viewModel = MyAddressesViewModel()
    
    var body: some View {
        VStack(spacing: 0) {
            if let addresses = viewModel.responseData?.responseData, !addresses.isEmpty {
                ScrollView {
                    VStack(spacing: 12) {
                        ForEach(addresses, id: \.iAddressId) { address in
                            AddressRow(address: address)
                        }
                    }
                    .padding(.top)
                }
            } else if viewModel.responseData != nil {
                VStack {
                    Spacer()
                    Text("No Addresses Added yet")
                        .font(.headline)
                        .foregroundColor(.gray)
                    Spacer()
                }
            } else {
                VStack {
                    Spacer()
                    Text("No Addresses Added yet")
                        .font(.headline)
                        .foregroundColor(.gray)
                    Spacer()
                }
            }
            Button(action: {
                print("Add new address tapped")
                if let topVC = UIApplication.shared.topViewController() {
                        let vc = ProductPopupStoryboard.instantiate(AddAddressViewController.self)
                        vc.modalPresentationStyle = .overFullScreen
                        vc.isMustSelectMosque = false
                        vc.refreshAddressList = {
                            // Refresh UI by calling the API again
                            viewModel.apiCallForGetAddress()
                        }
                        topVC.present(vc, animated: true)
                    }
            }) {
                Text("Add a new address")
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.cyan)
                    .foregroundColor(.white)
                    .cornerRadius(15)
                    .padding(.horizontal)
            }
            .padding(.vertical, 12)
            .background(Color(UIColor.systemGroupedBackground))
        }
        .onAppear {
            viewModel.apiCallForGetAddress()
        }
        .navigationTitle("My Addresses")
        .navigationBarTitleDisplayMode(.inline)
        .background(Color(UIColor.systemGroupedBackground).ignoresSafeArea())
    }
}

#Preview {
    NavigationStack {
        MyAddressesView()
    }
}
