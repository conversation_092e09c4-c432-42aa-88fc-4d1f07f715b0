//
//  AddressItem.swift
//  TopCustomer
//
//  Created by mac<PERSON>osh on 25/06/2025.
//  Copyright © 2025 SOTSYS203. All rights reserved.
//

import SwiftUI

struct AddressRow: View {
    let address: AddressResponseFields?

    var body: some View {
        HStack(alignment: .top) {
            Image(systemName: "checkmark.circle.fill")
                .foregroundColor(.cyan)
                .padding(.top, 5)

            VStack(alignment: .leading, spacing: 4) {
                Text(address?.vCity ?? "")
                    .font(.headline)

                Text(address?.txAddress ?? "")
                    .font(.subheadline)
                    .foregroundColor(.gray)
            }

            Spacer()

            HStack(spacing: 8) {
                But<PERSON>(action: {
                    print("Edit tapped")
                }) {
                    HStack(spacing: 4) {
                        Image(systemName: "pencil")
                        Text("Edit")
                    }
                    .foregroundColor(.gray)
                    .font(.subheadline)
                }

                Divider()
                    .frame(height: 15)

                But<PERSON>(action: {
                    print("Delete tapped")
                }) {
                    HStack(spacing: 4) {
                        Image(systemName: "trash")
                        Text("Delete")
                    }
                    .foregroundColor(.red)
                    .font(.subheadline)
                }
            }
        }
        .padding()
        .background(Color.white)
        .cornerRadius(10)
        .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
        .padding(.horizontal)
    }
}
