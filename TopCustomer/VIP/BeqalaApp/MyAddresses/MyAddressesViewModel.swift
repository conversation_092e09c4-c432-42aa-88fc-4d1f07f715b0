//
//  MyAddressesViewModel.swift
//  TopCustomer
//
//  Created by macintosh on 25/06/2025.
//  Copyright © 2025 SOTSYS203. All rights reserved.
//

import Foundation
import Combine

@MainActor
class MyAddressesViewModel: ObservableObject {
    
    @Published var responseData: AddressListResponse?
    
    func apiCallForGetAddress() {
        ActivityIndicator.shared.showCentralSpinner()
        let authorization = getAuthorizationText()
        
        UserAPI.userAddresses(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang) { data, error in
            ActivityIndicator.shared.hideCentralSpinner()
            print(data)
            self.responseData = data
        }
    }
}
