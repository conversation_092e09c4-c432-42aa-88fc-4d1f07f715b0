//
//  OrdersListViewModel.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 10/03/2025.
//  Copyright © 2025 SOTSYS203. All rights reserved.
//

import Foundation
import Combine

@MainActor
class OrdersListViewModel: ObservableObject {
    
    @Published var ordersList: [OrderResponseFields] = []
    @Published var orderDetail: OrderDetailResponseFields?
    
    //MARK: - API Calls
    func getHistoryOrders(pageNo: Int) {
        ActivityIndicator.shared.showCentralSpinner()
        let authorization = getAuthorizationText()
        
        OrderAPI.getMarketOrderList(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, iOrderType: OrderAPI.IOrderType_getOrderList._3, offset: pageNo) { data, error in
            ActivityIndicator.shared.hideCentralSpinner()
            
            self.ordersList = data?.responseData?.orders ?? []
            ActivityIndicator.shared.hideCentralSpinner()
        }
    }
    
    func getOrderDetail(iOrderId: Int) {
        ActivityIndicator.shared.showCentralSpinner()
        let authorization = getAuthorizationText()
        
        OrderAPI.getOrder(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, iOrderId: iOrderId) { data, error in
            ActivityIndicator.shared.hideCentralSpinner()
            //                  self.presenterHistoryOrderDetails?.apiResponseGetOrderDetail(response: data, error: error)
            self.orderDetail = data?.responseData ?? nil
            ActivityIndicator.shared.hideCentralSpinner()
        }
        
    }
}


