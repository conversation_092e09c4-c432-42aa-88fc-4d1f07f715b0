//
//  OrderDetailViewModel.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 18/03/2025.
//  Copyright © 2025 SOTSYS203. All rights reserved.
//

import Combine

@MainActor
class OrderDetailViewModel: ObservableObject {
    
    @Published var orderDetail: OrderDetailResponseFields?
    
    //MARK: - API Calls
    func getOrderDetail(iOrderId: Int) {
        ActivityIndicator.shared.showCentralSpinner()
        let authorization = getAuthorizationText()
        
        OrderAPI.getMarketOrder(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, iOrderId: iOrderId) { data, error in
            ActivityIndicator.shared.hideCentralSpinner()
            //                  self.presenterHistoryOrderDetails?.apiResponseGetOrderDetail(response: data, error: error)
            self.orderDetail = data?.responseData ?? nil
            ActivityIndicator.shared.hideCentralSpinner()
        }
        
    }
}
