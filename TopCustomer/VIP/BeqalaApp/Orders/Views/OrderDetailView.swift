//
//  OrderDetailView.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 10/03/2025.
//  Copyright © 2025 SOTSYS203. All rights reserved.
//

import SwiftUI

// MARK: - Order Detail View
import SwiftUI

struct OrderDetailView: View {
    @Environment(\.dismiss) var dismiss
    @State private var selectedTip: String?
    @StateObject var viewModel = OrderDetailViewModel()
    let orderId: Int
    @State private var rating = 5
    @State private var recommendRating = 10
    @State private var isShowingPopup = false
    
    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 20) {
                
                Text("#\(viewModel.orderDetail?.vOrderNumber ?? "N/A") Delivered")
                    .font(.title)
                    .fontWeight(.bold)
                
                // Header Section
                headerSection
                
                Button(action: {
                    print("Reorder tapped")
                }) {
                    Text("Reorder")
                        .font(.caption)
                        .fontWeight(.regular)
                        .foregroundColor(.white)
                        .frame(width: 160, height: 30)
                        .background(Color("AppTheme_BlueColor_#012CDA", bundle: .main))
                        .cornerRadius(8)
                }
                
                // Driver Information Section
                driverInfoSection
                
                // Delivery Address
                deliveryAddressSection
                
                // Rating Section
                ratingSection
                
                // Order Summary Card View
                OrderSummaryCardView(
                    restaurantName: viewModel.orderDetail?.vShiftDisplayName ?? "Restaurant",
                    productDetails: viewModel.orderDetail?.productDetails,
                    totalAmount: viewModel.orderDetail?.dTotalAmount,
                    viewModel: viewModel ,
                    onViewDetailsTapped: {
                        isShowingPopup = true
                    }
                )
                
                Image("share_ad")
                    .resizable()
                    .scaledToFill()
                    .frame(maxWidth: .infinity, minHeight: 100, maxHeight: 100)
                    .clipped()
                    .cornerRadius(8)
                
                
                // CustomerSupportSection
                CustomerSupportSection()
                
//                // Order Summary
//                summarySection
//                
//                // Price Breakdown
//                priceSection
//                
//                // Payment Information
//                paymentSection
//                
//                // Delivery Information
//                deliverySection
//                
//                // Products List
//                productsSection
//                
//                // Customer Information
//                customerSection
//                
//                // Additional Information
//                additionalInfoSection
                
            }
            .sheet(isPresented: $isShowingPopup) {
                OrderDetailsPopupView(viewModel: viewModel)
                    .presentationDetents([.medium, .large])
                    .presentationDragIndicator(.visible)
                    .background(Color(UIColor.systemGroupedBackground))
            }
            .padding()
        }
        .background(Color(UIColor.systemGroupedBackground))
        .navigationBarBackButtonHidden(true)
        .toolbar {
            ToolbarItem(placement: .navigationBarLeading) {
                HStack {
                    Image(systemName: "chevron.backward")
                        .foregroundColor(.black)
                }
                .onTapGesture {
                    dismiss()
                }
            }
        }
        .onAppear {
            viewModel.getOrderDetail(iOrderId: orderId)
        }
    }
    
    // MARK: - View Components
    
    private var headerSection: some View {
        VStack(alignment: .center , spacing: 10 ) {
//            Image(systemName: "shippingbox.fill")
//                .resizable()
//                .scaledToFit()
//                .frame(width: 60, height: 60)
//                .foregroundColor(.blue)
//                .padding(.bottom, 10)
            
            Text("WOW 🥳")
                .font(.title)
                .fontWeight(.bold)
            Text("Your request arrived sooner than expected!")
                .font(.caption)
                .fontWeight(.regular)
            
            
            
//            statusBadge
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(Color.orange.opacity(0.1))
        .cornerRadius(15)
        .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
    }
    
    private var statusBadge: some View {
        Text(statusText)
            .font(.subheadline)
            .fontWeight(.semibold)
            .padding(.vertical, 6)
            .padding(.horizontal, 12)
            .background(statusColor)
            .foregroundColor(.white)
            .clipShape(Capsule())
    }
    
    private var summarySection: some View {
        GroupBox(label:
            HStack {
                Text("Order Summary")
                    .font(.headline)
                    .foregroundColor(.primary)
                Spacer()
                Image(systemName: "doc.text")
            }
        ) {
            VStack(alignment: .leading, spacing: 8) {
                detailRow(title: "Order ID", value: viewModel.orderDetail?.vOrderNumber ?? "N/A")
                detailRow(title: "Order Date", value: formattedDate(viewModel.orderDetail?.tsOrderedAt))
                detailRow(title: "Delivered At", value: formattedDate(viewModel.orderDetail?.tsOrderDeliveredAt))
                detailRow(title: "Recurring Order", value: viewModel.orderDetail?.tiIsReccuring == 1 ? "Yes" : "No")
                detailRow(title: "Shift Type", value: viewModel.orderDetail?.vShiftDisplayName ?? "N/A")
                detailRow(title: "Additional Note", value: viewModel.orderDetail?.tAdditionalNote ?? "None")
            }
            .padding(.vertical, 8)
        }
        .padding(.horizontal, -5)
    }
    
    private var driverInfoSection: some View {
            VStack(spacing: 15) {
                HStack(spacing: 15) {
                    Image(systemName: "person.crop.circle.fill")
                        .resizable()
                        .scaledToFill()
                        .frame(width: 40, height: 40)
                        .foregroundColor(.blue)
                    
                    VStack(alignment: .leading, spacing: 5) {
                        Text("Qamar Abbas")
                            .font(.title)
                            .fontWeight(.regular)
                        
                        HStack {
//                            Image(systemName: "bicycle")
//                                .foregroundColor(.blue)
                            Text("Motorcycle 4504AG")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                    
                    Spacer()
                }
                
                VStack(alignment: .leading, spacing: 10) {
                    Text("Driver Tip")
                        .font(.headline)
                    
                    Text("100% of the tip goes directly to your driver")
                        .font(.caption)
                        .fontWeight(.light)
                        .foregroundColor(.secondary)
                    
                    // Tip options
                    LazyVGrid(columns: [
                        GridItem(.flexible(), spacing: 10),
                        GridItem(.flexible(), spacing: 10),
                        GridItem(.flexible()),
                        GridItem(.flexible())
                    ], spacing: 15) {
                        tipButton(amount: "5.00", isPopular: false)
                        tipButton(amount: "3.00", isPopular: true)
                        tipButton(amount: "1.00", isPopular: false)
                        tipButton(amount: "Other", isPopular: false)
                    }
                    .padding(.top, 10)
                }
            }
            .padding()
            .background(Color.white)
            .cornerRadius(12)
            .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
        }
    
    private func tipButton(amount: String, isPopular: Bool) -> some View {
            Button(action: {
                selectedTip = amount
            }) {
                VStack(spacing: 4) {
                    ZStack {
                        RoundedRectangle(cornerRadius: 10)
                            .stroke(selectedTip == amount ? Color.blue : (isPopular ? Color.green : Color.gray.opacity(0.3)),
                                      lineWidth: selectedTip == amount ? 2 : 1)
                            .background(RoundedRectangle(cornerRadius: 10).fill(Color.white))
                            .frame(height: 40)
                        
                        if amount == "Other" {
                            Text("Other")
                                .font(.subheadline)
                                .fontWeight(.medium)
                                .foregroundColor(.secondary)
                        } else {
                            VStack(spacing: 2) {
                                Text("\(amount) SAR")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
//                                Text(amount)
//                                    .font(.subheadline)
//                                    .fontWeight(.bold)
                            }
                        }
                    }
                    
                    if isPopular {
                        Text("Most Popular")
                            .font(.caption2)
                            .fontWeight(.medium)
                            .foregroundColor(.green)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(Color.green.opacity(0.1))
                            .cornerRadius(4)
                    }
                }
            }
        }

    private var deliveryAddressSection: some View {
            VStack(alignment: .leading, spacing: 15) {
                Text("Delivery Address")
                    .font(.headline)
                
                VStack(alignment: .leading, spacing: 8) {
                    HStack(alignment: .top) {
                        Image(systemName: "house.fill")
                            .foregroundColor(.blue)
                            .padding(.top, 2)
                        Text("Home: aia Ghimatah, Riyadh Saudi Arabia")
                            .font(.subheadline)
                    }
                    
                    HStack(alignment: .top) {
                        Image(systemName: "phone.fill")
                            .foregroundColor(.green)
                            .padding(.top, 2)
                        Text("+966 500622427")
                            .font(.subheadline)
                    }
                    
                    HStack(alignment: .top) {
                        Image(systemName: "person.fill")
                            .foregroundColor(.orange)
                            .padding(.top, 2)
                        Text("Islam Hassan - Deliver to me directly")
                            .font(.subheadline)
                    }
                }
            }
            .frame(maxWidth: .infinity, alignment: .leading)
            .padding()
            .background(Color.white)
            .cornerRadius(12)
            .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
        }
    
    private var ratingSection: some View {
            VStack(spacing: 20) {
//                Text("Rate Your Order")
//                    .font(.title2)
//                    .fontWeight(.bold)
//                
//                // Star rating
//                HStack(spacing: 5) {
//                    ForEach(1..<6) { star in
//                        Image(systemName: star <= rating ? "star.fill" : "star")
//                            .resizable()
//                            .scaledToFit()
//                            .frame(width: 35, height: 35)
//                            .foregroundColor(.orange)
//                            .onTapGesture {
//                                rating = star
//                            }
//                    }
//                }
//                
//                Text("How likely are you to recommend us to a friend?")
//                    .font(.headline)
//                    .multilineTextAlignment(.center)
        
        Text("How likely are you to recommend Kita to a friend?")
                    .font(.headline)
                
                // Recommendation scale
                VStack(spacing: 8) {
                    HStack {
                        Text("Not likely")
                            .font(.caption)
                        
                        Spacer()
                        
                        Text("Very likely")
                            .font(.caption)
                    }
                    
                    HStack(spacing: 0) {
                        ForEach(0..<11) { index in
                            Button(action: {
                                recommendRating = index
                            }) {
                                Text("\(index)")
                                    .font(.caption)
                                    .frame(width: 25)
                                    .padding(.vertical, 8)
                                    .background(recommendRating == index ? Color.blue.opacity(0.2) : Color.clear)
                                    .cornerRadius(4)
                            }
                        }
                    }
                }
                .padding(.horizontal)
            }
            .padding()
            .background(Color.white)
            .cornerRadius(12)
            .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
        }


    
    private var priceSection: some View {
        GroupBox(label:
            HStack {
                Text("Price Breakdown")
                    .font(.headline)
                    .foregroundColor(.primary)
                Spacer()
                Image(systemName: "dollarsign.circle")
            }
        ) {
            VStack(alignment: .leading, spacing: 8) {
                detailRow(title: "Subtotal", value: formatCurrency(viewModel.orderDetail?.dOrderSubTotal))
                detailRow(title: "Delivery Charge", value: formatCurrency(viewModel.orderDetail?.dDeliveryCharge))
                detailRow(title: "VAT (\(viewModel.orderDetail?.dVatPercentage ?? "0")%)",
                         value: formatCurrency(viewModel.orderDetail?.dVatCharge))
                detailRow(title: "Discount", value: formatCurrency(viewModel.orderDetail?.dOrderDiscount))
                detailRow(title: "Reward Amount", value: formatCurrency(viewModel.orderDetail?.RewardAmount))
                
                Divider().padding(.vertical, 8)
                
                detailRow(title: "Total Amount",
                         value: formatCurrency(viewModel.orderDetail?.dTotalAmount))
                    .font(.headline)
                    .foregroundColor(.green)
            }
            .padding(.vertical, 8)
        }
        .padding(.horizontal, -5)
    }
    
    private var paymentSection: some View {
        GroupBox(label:
            HStack {
                Text("Payment Information")
                    .font(.headline)
                    .foregroundColor(.primary)
                Spacer()
                Image(systemName: "creditcard")
            }
        ) {
            VStack(alignment: .leading, spacing: 8) {
                detailRow(title: "Payment ID", value: viewModel.orderDetail?.vPaymentId ?? "N/A")
                detailRow(title: "Payment Status", value: paymentStatusText)
                detailRow(title: "Transaction Type", value: transactionTypeText)
                detailRow(title: "Promo Code", value: viewModel.orderDetail?.vPromocode ?? "None")
                detailRow(title: "Wallet Used", value: viewModel.orderDetail?.tiIsWalletUsed == 1 ? "Yes" : "No")
            }
            .padding(.vertical, 8)
        }
        .padding(.horizontal, -5)
    }
    
    
    // MARK: - Order Summary Card View
    struct OrderSummaryCardView: View {
        let restaurantName: String
        let productDetails: [OrderedProductDetailResponseFields]?
        let totalAmount: String?
        let viewModel : OrderDetailViewModel?
        let onViewDetailsTapped: () -> Void
        
        var body: some View {
            VStack(alignment: .leading, spacing: 16) {
                // Restaurant header
                HStack {
                    
                    Image("logo")
                        .resizable()
                        .scaledToFit()
                        .frame(width: 40, height: 40)
                        .padding(5)
                        .background(Color.gray.opacity(0.2))
                    
                    Text(restaurantName)
                        .font(.headline)
                        .fontWeight(.bold)
                    
                    Spacer()
                    
                    Image(systemName: "chevron.right")
                        .foregroundColor(.gray)
                }
                
                // Payment method and item count
                HStack(spacing: 16) {
                    // Payment method icon
                    VStack(alignment: .leading) {
                        HStack(spacing: 4) {
                            Image(systemName: "creditcard")
                                .resizable()
                                .scaledToFit()
                                .frame(height: 20)
                            
                            Text("Cash On Delivery")
                                .font(.subheadline)
                                .fontWeight(.medium)
                        }
                        
                        Text("\(totalItemCount) items")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                    
                    Text(formatCurrency(totalAmount))
                        .font(.headline)
                        .fontWeight(.bold)
                    
                    
                }
                
                HStack(spacing: 16) {
                    // Product images
                    if let products = productDetails, !products.isEmpty {
                        HStack(spacing: 30) {
                            ForEach(products.prefix(3), id: \.biProductId) { product in
                                productImage(for: product)
                            }
                            
                            if products.count > 3 {
                                Text("+\(products.count - 3)")
                                    .font(.caption2)
                                    .padding(6)
                                    .background(Color.gray.opacity(0.2))
                            }
                        }
                    }
                }
                
                
                // Action buttons
                HStack(spacing: 16) {
                    Button(action: {
                        print("View Order Details tapped")
                        onViewDetailsTapped()
                    }) {
                        Text("View Order Details")
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(.black)
                            .frame(maxWidth: .infinity, minHeight: 40)
                            .background(Color.white)
                            .overlay(
                                RoundedRectangle(cornerRadius: 8)
                                    .stroke(Color.black, lineWidth: 0.5)
                            )
                            .clipShape(RoundedRectangle(cornerRadius: 8))
                    }
                    
                    Button(action: {
                        print("View Invoice tapped")
                    }) {
                        Text("View Invoice")
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(.black)
                            .frame(maxWidth: .infinity, minHeight: 40)
                            .background(Color.white)
                            .overlay(
                                RoundedRectangle(cornerRadius: 8)
                                    .stroke(Color.black, lineWidth: 0.5)
                            )
                            .clipShape(RoundedRectangle(cornerRadius: 8))
                    }
                }
            }
            .padding()
            .background(Color.white)
            .cornerRadius(12)
            .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
        }
        
        // MARK: - Helper Functions
        
        private func productImage(for product: OrderedProductDetailResponseFields) -> some View {
            ZStack {
                // Placeholder for product image
                if let imageName = product.vProductImage, !imageName.isEmpty {
                    Image(imageName)
                        .resizable()
                        .scaledToFill()
                        .frame(width: 40, height: 40)
//                        .clipShape(Circle())
                } else {
                    Image("logo")
                        .resizable()
                        .scaledToFit()
                        .frame(width: 40, height: 40)
                        .padding(5)
                        .background(Color.gray.opacity(0.2))
//                        .clipShape(Circle())
                }
                
                // Item quantity badge
                if let quantity = product.iProductQuantity, quantity > 1 {
                    Text("\(quantity)x")
                        .font(.system(size: 8, weight: .bold))
                        .foregroundColor(.white)
                        .padding(4)
                        .background(Color.blue)
//                        .clipShape(Circle())
                        .offset(x: 10, y: 10)
                }
            }
            .frame(width: 30, height: 30)
        }
        
        private func formatCurrency(_ value: String?) -> String {
            guard let value = value, let doubleValue = Double(value) else { return "SAR 0.00" }
            return String(format: "SAR %.2f", doubleValue)
        }
        
        // MARK: - Computed Properties
        
        private var totalItemCount: Int {
            productDetails?.reduce(0) { $0 + ($1.iProductQuantity ?? 0) } ?? 0
        }
    }
    
    struct CustomerSupportSection: View {
        var body: some View {
            VStack(alignment: .leading, spacing: 3) {
                deliveryGuaranteeSection
                customerServiceView
            }
            
            .padding()
            .background(Color.white)
            .cornerRadius(12)
            .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
        }

        private var deliveryGuaranteeSection: some View {
            VStack(alignment: .leading, spacing: 12) {
                Button(action: {}) {
                    VStack(alignment: .leading) {
                        HStack(spacing: 10) {
                            Image(systemName: "clock")
                            Text("On-time Delivery Guarantee")
                                .font(.subheadline)
                        }
                        
                        Text("Get compensation vouchers for late orders!")
                            .font(.caption)
                    }
                }
                .foregroundColor(.black)
            }
            .padding()
            .frame(maxWidth: .infinity, alignment: .leading)
            .cornerRadius(12)
        }
        
        
        private var customerServiceView: some View {
            VStack(alignment: .leading, spacing: 12) {
                Button(action: {}) {
                    VStack(alignment: .leading) {
                        HStack(spacing: 10) {
                            Image(systemName: "message")
                            Text("24/7 Customer Support")
                                .font(.subheadline)
                        }
                        
                        Text("Easy access, fast response.")
                            .font(.caption)
                    }
                }
                .foregroundColor(.black)
            }
            .padding()
            .frame(maxWidth: .infinity, alignment: .leading)
            .cornerRadius(12)
        }
    }


    
    private var deliverySection: some View {
        GroupBox(label:
            HStack {
                Text("Delivery Information")
                    .font(.headline)
                    .foregroundColor(.primary)
                Spacer()
                Image(systemName: "mappin.and.ellipse")
            }
        ) {
            VStack(alignment: .leading, spacing: 8) {
                detailRow(title: "Address", value: viewModel.orderDetail?.txAddress ?? "N/A")
                detailRow(title: "Driver Mobile", value: viewModel.orderDetail?.vDriverMobile ?? "N/A")
                detailRow(title: "Expected Delivery",
                         value: "\(formattedTime(viewModel.orderDetail?.tsExpectedStartTime)) - \(formattedTime(viewModel.orderDetail?.tsExpectedEndTime))")
                detailRow(title: "Distance Cost", value: formatCurrency(viewModel.orderDetail?.dDistanceCostSetInAdmin))
                detailRow(title: "Free Delivery", value: viewModel.orderDetail?.dHasFreeOrder == 1 ? "Yes" : "No")
            }
            .padding(.vertical, 8)
        }
        .padding(.horizontal, -5)
    }
    
    private var productsSection: some View {
        GroupBox(label:
            HStack {
                Text("Products")
                    .font(.headline)
                    .foregroundColor(.primary)
                Spacer()
                Image(systemName: "cart")
            }
        ) {
            let products = viewModel.orderDetail?.productDetails ?? []
            if products.isEmpty {
                Text("No products found")
                    .foregroundColor(.secondary)
                    .padding()
            } else {
                ForEach(products.indices, id: \.self) { index in
                    let product = products[index]
                    
                    HStack(alignment: .top, spacing: 12) {
                        Image(systemName: "shippingbox")
                            .resizable()
                            .scaledToFit()
                            .frame(width: 24, height: 24)
                            .foregroundColor(.blue)
                        
                        VStack(alignment: .leading, spacing: 6) {
                            Text(product.vProductName ?? "Unknown Product")
                                .font(.headline)
                                .foregroundColor(.primary)
                            
                            Text("Category: \(product.vCategoryName ?? "N/A")")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                            
                            HStack {
                                Text("Quantity: \(product.iProductQuantity ?? 0)")
                                Spacer()
                                Text("Price: \(formatCurrency(product.dbPrice))")
                            }
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                        }
                    }
                    .padding(.vertical, 8)
                    
                    if index < products.count - 1 {
                        Divider()
                    }
                }
            }
        }
        .padding(.horizontal, -5)
    }
    
    private var customerSection: some View {
        GroupBox(label:
            HStack {
                Text("Customer Information")
                    .font(.headline)
                    .foregroundColor(.primary)
                Spacer()
                Image(systemName: "person")
            }
        ) {
            VStack(alignment: .leading, spacing: 8) {
                detailRow(title: "Alternative Mobile",
                         value: "\(viewModel.orderDetail?.vAlternativeISDCode ?? "") \(viewModel.orderDetail?.vAlternativeMobileNumber ?? "")")
                detailRow(title: "Customer Type", value: viewModel.orderDetail?.vType ?? "N/A")
                detailRow(title: "Mosque Order", value: viewModel.orderDetail?.isMosques == 1 ? "Yes" : "No")
            }
            .padding(.vertical, 8)
        }
        .padding(.horizontal, -5)
    }
    
    private var additionalInfoSection: some View {
        GroupBox(label:
            HStack {
                Text("Additional Information")
                    .font(.headline)
                    .foregroundColor(.primary)
                Spacer()
                Image(systemName: "info.circle")
            }
        ) {
            VStack(alignment: .leading, spacing: 8) {
                detailRow(title: "Invoice PDF", value: viewModel.orderDetail?.vInvoicePdfName ?? "Not Available")
                detailRow(title: "Order Coordinates",
                         value: "\(viewModel.orderDetail?.dOrderLatitude ?? "0"), \(viewModel.orderDetail?.dOrderLongitude ?? "0")")
                detailRow(title: "Driver Coordinates",
                         value: "\(viewModel.orderDetail?.dDriverLatitude ?? "0"), \(viewModel.orderDetail?.dDriverLongitude ?? "0")")
            }
            .padding(.vertical, 8)
        }
        .padding(.horizontal, -5)
    }
    
    // MARK: - Helper Functions
    
    private func detailRow(title: String, value: String) -> some View {
        HStack {
            Text(title)
                .foregroundColor(.secondary)
            Spacer()
            Text(value)
                .multilineTextAlignment(.trailing)
                .foregroundColor(.primary)
        }
        .padding(.vertical, 4)
    }
    
    private func formatCurrency(_ value: String?) -> String {
        guard let value = value, let doubleValue = Double(value) else { return "$0.00" }
        return String(format: "$%.2f", doubleValue)
    }
    
    private func formatCurrency(_ value: Double?) -> String {
        guard let value = value else { return "$0.00" }
        return String(format: "$%.2f", value)
    }
    
    private func formattedDate(_ dateString: String?) -> String {
        guard let dateString = dateString else { return "N/A" }
        let formatter = ISO8601DateFormatter()
        formatter.formatOptions = [.withInternetDateTime, .withFractionalSeconds]
        
        if let date = formatter.date(from: dateString) {
            let outputFormatter = DateFormatter()
            outputFormatter.dateStyle = .medium
            outputFormatter.timeStyle = .short
            return outputFormatter.string(from: date)
        }
        return dateString
    }
    
    private func formattedTime(_ timeString: String?) -> String {
        guard let timeString = timeString else { return "N/A" }
        let formatter = ISO8601DateFormatter()
        formatter.formatOptions = [.withInternetDateTime, .withFractionalSeconds]
        
        if let date = formatter.date(from: timeString) {
            let outputFormatter = DateFormatter()
            outputFormatter.timeStyle = .short
            return outputFormatter.string(from: date)
        }
        return timeString
    }
    
    // MARK: - Computed Properties
    
    private var statusText: String {
        guard let status = viewModel.orderDetail?.tiOrderStatus else { return "UNKNOWN" }
        switch status {
        case 0: return "PENDING"
        case 1: return "CONFIRMED"
        case 2: return "PREPARING"
        case 3: return "ON THE WAY"
        case 4: return "DELIVERED"
        case 5: return "CANCELLED"
        default: return "UNKNOWN"
        }
    }
    
    private var statusColor: Color {
        guard let status = viewModel.orderDetail?.tiOrderStatus else { return .gray }
        switch status {
        case 0: return .orange
        case 1, 2: return .blue
        case 3: return .purple
        case 4: return .green
        case 5: return .red
        default: return .gray
        }
    }
    
    private var paymentStatusText: String {
        guard let status = viewModel.orderDetail?.tiPaymentStatus else { return "UNKNOWN" }
        switch status {
        case 0: return "PENDING"
        case 1: return "COMPLETED"
        case 2: return "FAILED"
        case 3: return "REFUNDED"
        default: return "UNKNOWN"
        }
    }
    
    private var transactionTypeText: String {
        guard let type = viewModel.orderDetail?.tiTransactionType else { return "N/A" }
        return type == 0 ? "Online" : "Cash on Delivery"
    }
}

struct OrderDetailView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            OrderDetailView(orderId: 12345)
        }
    }
}
