//
//  OrdersView.swift
//  BeqalaApp
//
//  Created by <PERSON><PERSON> on 10/12/2024.
//

import SwiftUI

struct OrdersView: View {
    @Binding var selectedTab: Int  // Get binding from parent
    
    
    var body: some View {
        VStack {
            // Header
            Text("Orders")
                .font(.title)
                .fontWeight(.bold)
                .padding(.top, 20)
            
            // No Orders Icon and Message
            VStack(spacing: 16) {
                Image(systemName: "doc.text.magnifyingglass") // Placeholder for the icon
                    .resizable()
                    .frame(width: 60, height: 60)
                    .foregroundColor(.blue)
                
                Text("No Orders")
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
                
                Text("No previous orders")
                    .font(.body)
                    .foregroundColor(.secondary)
            }
            
            // Explore Button
            Button(action: {
                // Action for the Explore button
                print("Go Shopping")
                selectedTab = 0
                
            }) {
                Text("Explore")
                    .font(.headline)
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.blue)
                    .cornerRadius(10)
                    .padding(.horizontal, 20)
            }
            
            Spacer()
        }
        .padding()
        
    }
}

#Preview {
    OrdersView(selectedTab: .constant(0))
}
