//
//  OrderDetailsPopupView.swift
//  TopCustomer
//
//  Created by macintosh on 14/07/2025.
//  Copyright © 2025 SOTSYS203. All rights reserved.
//

import SwiftUI

struct OrderDetailsPopupView: View {
    @ObservedObject var viewModel = OrderDetailViewModel()
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        VStack(alignment: .leading , spacing: 20){
            Text("Order Details")
                .font(.headline)
        }.padding()

        ScrollView{
            VStack(spacing: 10) {
                productsSection
                summarySection
                priceSection
                paymentSection
                deliverySection
            }.padding()
        }
        
        Divider()
        
        VStack {
            Button(action: {
                 dismiss()
            }) {
                Text("Done")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.black)
                    .frame(maxWidth: .infinity, minHeight: 40)
                    .background(Color.yellow)
                    .cornerRadius(20)
            }
        }
        .padding(.horizontal, 16)
        .padding(.bottom, 10)
    }
    
    private var summarySection: some View {
        
            VStack(alignment: .leading, spacing: 8) {
                detailRow(title: "Order ID", value: viewModel.orderDetail?.vOrderNumber ?? "N/A")
                detailRow(title: "Order Date", value: formattedDate(viewModel.orderDetail?.tsOrderedAt))
                detailRow(title: "Delivered At", value: formattedDate(viewModel.orderDetail?.tsOrderDeliveredAt))
                detailRow(title: "Recurring Order", value: viewModel.orderDetail?.tiIsReccuring == 1 ? "Yes" : "No")
                detailRow(title: "Shift Type", value: viewModel.orderDetail?.vShiftDisplayName ?? "N/A")
                detailRow(title: "Additional Note", value: viewModel.orderDetail?.tAdditionalNote ?? "None")
            }
            .padding(.vertical, 8)
        
        .padding(.horizontal, 16)
        .background(Color.white)
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.1), radius: 1, x: 0, y: 0)
    }
    
    private var priceSection: some View {
//        {
            VStack(alignment: .leading, spacing: 8) {
                detailRow(title: "Subtotal", value: formatCurrency(viewModel.orderDetail?.dOrderSubTotal))
                detailRow(title: "Delivery Charge", value: formatCurrency(viewModel.orderDetail?.dDeliveryCharge))
                detailRow(title: "VAT (\(viewModel.orderDetail?.dVatPercentage ?? "0")%)",
                         value: formatCurrency(viewModel.orderDetail?.dVatCharge))
                detailRow(title: "Discount", value: formatCurrency(viewModel.orderDetail?.dOrderDiscount))
                detailRow(title: "Reward Amount", value: formatCurrency(viewModel.orderDetail?.RewardAmount))
                
                Divider().padding(.vertical, 8)
                
                detailRow(title: "Total Amount",
                         value: formatCurrency(viewModel.orderDetail?.dTotalAmount))
                    .font(.headline)
                    .foregroundColor(.green)
            }
            .padding(.vertical, 8)
//        }
        .padding(.horizontal, 16)
        .background(Color.white)
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.1), radius: 1, x: 0, y: 0)
    }
    
    private var paymentSection: some View {
//        GroupBox{
            VStack(alignment: .leading, spacing: 8) {
                detailRow(title: "Payment ID", value: viewModel.orderDetail?.vPaymentId ?? "N/A")
                detailRow(title: "Payment Status", value: paymentStatusText)
                detailRow(title: "Transaction Type", value: transactionTypeText)
                detailRow(title: "Promo Code", value: viewModel.orderDetail?.vPromocode ?? "None")
                detailRow(title: "Wallet Used", value: viewModel.orderDetail?.tiIsWalletUsed == 1 ? "Yes" : "No")
            }
            .padding(.vertical, 8)
//        }
        .padding(.horizontal, 16)
        .background(Color.white)
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.1), radius: 1, x: 0, y: 0)
    }
    
    private var deliverySection: some View {
//        GroupBox {
            VStack(alignment: .leading, spacing: 8) {
                detailRow(title: "Address", value: viewModel.orderDetail?.txAddress ?? "N/A")
                detailRow(title: "Driver Mobile", value: viewModel.orderDetail?.vDriverMobile ?? "N/A")
                detailRow(title: "Expected Delivery",
                         value: "\(formattedTime(viewModel.orderDetail?.tsExpectedStartTime)) - \(formattedTime(viewModel.orderDetail?.tsExpectedEndTime))")
                detailRow(title: "Distance Cost", value: formatCurrency(viewModel.orderDetail?.dDistanceCostSetInAdmin))
                detailRow(title: "Free Delivery", value: viewModel.orderDetail?.dHasFreeOrder == 1 ? "Yes" : "No")
            }
            .padding(.vertical, 8)
//        }
        .padding(.horizontal, 16)
        .background(Color.white)
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.1), radius: 1, x: 0, y: 0)
    }
    
    private var productsSection: some View {
        VStack {
            let products = viewModel.orderDetail?.productDetails ?? []
            if products.isEmpty {
                Text("No products found")
                    .foregroundColor(.secondary)
                    .padding()
            } else {
                ForEach(products.indices, id: \.self) { index in
                    let product = products[index]
                    
                    HStack(alignment: .top, spacing: 12) {
                        
                        VStack(alignment: .leading, spacing: 6) {
                            Text(product.vProductName ?? "Unknown Product")
                                .font(.headline)
                                .foregroundColor(.primary)
                            
                            Text("Category: \(product.vCategoryName ?? "N/A")")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                            
                            HStack {
                                Text("Quantity: \(product.iProductQuantity ?? 0)")
                                Spacer()
//                                Text("Price: \()\(formatCurrency(product.dbPrice))")
                                SaudiRiyalAmount(amount: Double(product.dbPrice ?? "0.0") ?? 0.0, size: 20, color: .secondary , fontWight: .regular)
                            }
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                        }
                    }
                    .padding(.vertical, 8)
                    
                    if index < products.count - 1 {
                        Divider()
                    }
                }
            }
        }
        .padding(.horizontal, 16)
        .background(Color.white)
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.1), radius: 1, x: 0, y: 0)
    }
    
    private var paymentStatusText: String {
        guard let status = viewModel.orderDetail?.tiPaymentStatus else { return "UNKNOWN" }
        switch status {
        case 0: return "PENDING"
        case 1: return "COMPLETED"
        case 2: return "FAILED"
        case 3: return "REFUNDED"
        default: return "UNKNOWN"
        }
    }
    
    private var transactionTypeText: String {
        guard let type = viewModel.orderDetail?.tiTransactionType else { return "N/A" }
        return type == 0 ? "Online" : "Cash on Delivery"
    }

}

private func detailRow(title: String, value: String) -> some View {
    HStack {
        Text(title)
            .foregroundColor(.secondary)
        Spacer()
        Text(value)
            .multilineTextAlignment(.trailing)
            .foregroundColor(.primary)
    }
    .padding(.vertical, 4)
}

private func formatCurrency(_ value: String?) -> String {
    guard let value = value, let doubleValue = Double(value) else { return "0.00" }
    return String(format: "%.2f", doubleValue)
}

private func formatCurrency(_ value: Double?) -> String {
    guard let value = value else { return "$0.00" }
    return String(format: "$%.2f", value)
}

private func formattedDate(_ dateString: String?) -> String {
    guard let dateString = dateString else { return "N/A" }
    let formatter = ISO8601DateFormatter()
    formatter.formatOptions = [.withInternetDateTime, .withFractionalSeconds]
    
    if let date = formatter.date(from: dateString) {
        let outputFormatter = DateFormatter()
        outputFormatter.dateStyle = .medium
        outputFormatter.timeStyle = .short
        return outputFormatter.string(from: date)
    }
    return dateString
}

private func formattedTime(_ timeString: String?) -> String {
    guard let timeString = timeString else { return "N/A" }
    let formatter = ISO8601DateFormatter()
    formatter.formatOptions = [.withInternetDateTime, .withFractionalSeconds]
    
    if let date = formatter.date(from: timeString) {
        let outputFormatter = DateFormatter()
        outputFormatter.timeStyle = .short
        return outputFormatter.string(from: date)
    }
    return timeString
}

#Preview {
    OrderDetailsPopupView()
}
