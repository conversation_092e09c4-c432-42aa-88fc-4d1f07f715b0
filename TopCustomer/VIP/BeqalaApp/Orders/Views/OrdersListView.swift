//
//  OrdersView.swift
//  BeqalaApp
//
//  Created by <PERSON><PERSON> on 10/12/2024.
//

import SwiftUI

// MARK: - Orders List View
struct OrdersListView: View {
    @Binding var selectedTab: Int  // Get binding from parent
    
    @StateObject var viewModel = OrdersListViewModel()
    
    var body: some View {
        NavigationStack {
            if viewModel.ordersList.count >= 1 {
                List(viewModel.ordersList.indices, id: \.self) { index in
                    NavigationLink(destination: OrderDetailView(orderId: viewModel.ordersList[index].iOrderId ?? 0)) {
//                    NavigationLink(destination: OrderDeliveredView()) {
                        HStack {
                            Image("logo")
                                .resizable()
                                .scaledToFit()
                                .frame(width: 40, height: 40)
                            
                            VStack(alignment: .leading) {
                                Text(viewModel.ordersList[index].vOrderNumber ?? "")
                                    .font(.headline)
                                Text(viewModel.ordersList[index].tsExpectedEndTime ?? "")
                                    .font(.subheadline)
                                    .foregroundColor(.gray)
                                Text("Date: \(viewModel.ordersList[index].tsOrderedAt ?? "")")
                                    .font(.subheadline)
                                    .foregroundColor(.green)
                            }
                        }
                    }
                }
                .navigationTitle("Orders")
            } else {
                EmptyOrdersView(selectedTab: $selectedTab)
            }
        }.onAppear {
            viewModel.getHistoryOrders(pageNo: 1)
        }
    }
}

#Preview {
    OrdersListView(selectedTab: .constant(1))
}
