//
//  OrderDeliveredView.swift
//  TopCustomer
//
//  Created by macintosh on 13/07/2025.
//  Copyright © 2025 SOTSYS203. All rights reserved.
//

import SwiftUI

struct OrderDeliveredView: View {
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // Header
                    headerSection
                    
                    // Delivery Guarantee
                    deliveryGuaranteeSection
                    
                    // Reorder Button
                    reorderButton
                    
                    // Rating Section
                    ratingSection
                    
                    // Driver Info
                    driverSection
                    
                    // Driver Bonus
                    driverBonusSection
                    
                    // Cost Details
                    costDetailsSection
                    
                    // Payment Method
                    paymentMethodSection
                    
                    // Order Details Button
                    orderDetailsButton
                    
                    // Invoice Button
                    invoiceButton
                    
                    // Additional Guarantee
                    additionalGuaranteeSection
                    
                    // Customer Service
                    customerServiceSection
                }
                .padding()
                .background(Color(.systemGroupedBackground))
            }
            .navigationBarTitle("Order #1558", displayMode: .inline)
            .navigationBarItems(trailing: helpButton)
        }
    }
    
    // MARK: - View Components
    
    private var headerSection: some View {
        HStack {
            VStack(alignment: .leading) {
                Text("Order #1558 • Delivered")
                    .font(.title2)
                    .fontWeight(.bold)
                
                Text("July 13, 2025 at 12:12 PM")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
        .padding()
        .background(Color.white)
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
    }
    
    private var helpButton: some View {
        Button(action: {}) {
            Text("Help")
                .font(.headline)
                .foregroundColor(.blue)
        }
    }
    
    private var deliveryGuaranteeSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("On-Time Delivery Guarantee")
                .font(.headline)
                .foregroundColor(.green)
            
            HStack {
                Image(systemName: "checkmark.circle.fill")
                    .foregroundColor(.green)
                Text("Your order arrived on time in 25 minutes. Thank you for your order!")
                    .font(.subheadline)
            }
        }
        .padding()
        .frame(maxWidth: .infinity, alignment: .leading)
        .background(Color.white)
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
    }
    
    private var reorderButton: some View {
        Button(action: {}) {
            Text("Reorder")
                .font(.headline)
                .foregroundColor(.white)
                .padding(.vertical, 12)
                .frame(maxWidth: .infinity)
                .background(Color.blue)
                .cornerRadius(10)
        }
    }
    
    private var ratingSection: some View {
        VStack(spacing: 20) {
            Text("Rate Your Experience")
                .font(.title2)
                .fontWeight(.bold)
            
            Text("5")
                .font(.system(size: 50))
                .fontWeight(.bold)
                .foregroundColor(.orange)
            
            Text("How likely are you to recommend Kita to a friend?")
                .font(.headline)
            
            // Rating Scale
            VStack(spacing: 8) {
                HStack {
                    Text("Not at all likely")
                        .font(.caption)
                    
                    Spacer()
                    
                    Text("Extremely likely")
                        .font(.caption)
                }
                
                HStack(spacing: 0) {
                    ForEach(0..<11) { index in
                        Text("\(index)")
                            .font(.caption)
                            .frame(width: 30)
                    }
                }
            }
            .padding(.horizontal)
            
            Text("Rate Driver")
                .font(.headline)
                .padding(.top, 10)
        }
        .padding()
        .background(Color.white)
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
    }
    
    private var driverSection: some View {
        HStack(spacing: 15) {
            Image("driver_avatar")
                .resizable()
                .scaledToFill()
                .frame(width: 70, height: 70)
                .clipShape(Circle())
                .overlay(Circle().stroke(Color.blue, lineWidth: 2))
            
            VStack(alignment: .leading, spacing: 5) {
                Text("Ahssen Atta")
                    .font(.title3)
                    .fontWeight(.bold)
                
                HStack {
                    Image(systemName: "bicycle")
                        .foregroundColor(.blue)
                    Text("Motorcycle 4994BD")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
            }
            
            Spacer()
        }
        .padding()
        .background(Color.white)
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
    }
    
    private var driverBonusSection: some View {
        VStack(alignment: .leading, spacing: 10) {
            Text("Driver Bonus")
                .font(.headline)
            
            HStack {
                Text("100% paid to driver")
                    .font(.subheadline)
                Spacer()
                Image(systemName: "info.circle")
                    .foregroundColor(.blue)
            }
            
            // Tip options
            HStack {
                tipButton(amount: "5.00")
                tipButton(amount: "3.00")
                tipButton(amount: "1.00")
                tipButton(amount: "Other")
            }
            .padding(.top, 10)
        }
        .padding()
        .background(Color.white)
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
    }
    
    private func tipButton(amount: String) -> some View {
        Button(action: {}) {
            Text(amount == "Other" ? "Other" : "SAR \(amount)")
                .font(.subheadline)
                .foregroundColor(.primary)
                .padding(10)
                .background(Color.gray.opacity(0.1))
                .cornerRadius(8)
        }
    }
    
    private var costDetailsSection: some View {
        VStack(spacing: 12) {
            costRow(title: "Subtotal", value: "SAR 61.60")
            costRow(title: "Delivery Fee", value: "SAR 7.00")
            costRow(title: "Discount", value: "SAR 15.40")
            
            Divider()
                .padding(.vertical, 5)
            
            HStack {
                Text("You saved")
                    .foregroundColor(.green)
                Spacer()
                Text("SAR 32.40")
                    .fontWeight(.bold)
                    .foregroundColor(.green)
            }
            
            HStack {
                Text("VAT (15%)")
                Spacer()
                Text("Included")
                    .foregroundColor(.secondary)
            }
            
            Divider()
                .padding(.vertical, 5)
            
            HStack {
                Text("Total")
                    .font(.headline)
                Spacer()
                Text("SAR 61.60")
                    .font(.headline)
                    .fontWeight(.bold)
            }
            .padding(.top, 5)
        }
        .padding()
        .background(Color.white)
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
    }
    
    private func costRow(title: String, value: String) -> some View {
        HStack {
            Text(title)
            Spacer()
            Text(value)
        }
    }
    
    private var paymentMethodSection: some View {
        HStack {
            Image("apple_pay")
                .resizable()
                .scaledToFit()
                .frame(height: 30)
            
            VStack(alignment: .leading) {
                Text("Apple Pay")
                    .fontWeight(.medium)
                Text("2 items")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            Image(systemName: "chevron.right")
                .foregroundColor(.gray)
        }
        .padding()
        .background(Color.white)
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
    }
    
    private var orderDetailsButton: some View {
        Button(action: {}) {
            HStack {
                Text("View Order Details")
                    .font(.headline)
                    .foregroundColor(.primary)
                
                Spacer()
                
                Image(systemName: "chevron.right")
            }
            .padding()
            .background(Color.white)
            .cornerRadius(12)
            .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
        }
    }
    
    private var invoiceButton: some View {
        Button(action: {}) {
            HStack {
                Text("View Invoice")
                    .font(.headline)
                    .foregroundColor(.primary)
                
                Spacer()
                
                Image(systemName: "chevron.right")
            }
            .padding()
            .background(Color.white)
            .cornerRadius(12)
            .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
        }
    }
    
    private var additionalGuaranteeSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("On-Time Delivery Guarantee")
                .font(.headline)
            
            Text("Get compensation coupons for late orders!")
                .font(.subheadline)
            
            Button(action: {}) {
                Text("Learn more")
                    .font(.subheadline)
                    .foregroundColor(.blue)
            }
        }
        .padding()
        .frame(maxWidth: .infinity, alignment: .leading)
        .background(Color.blue.opacity(0.1))
        .cornerRadius(12)
    }
    
    private var customerServiceSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("24/7 Customer Support")
                .font(.headline)
            
            Text("Easy access, quick response.")
                .font(.subheadline)
            
            Button(action: {}) {
                Text("Contact Support")
                    .font(.subheadline)
                    .foregroundColor(.blue)
            }
        }
        .padding()
        .frame(maxWidth: .infinity, alignment: .leading)
        .background(Color.green.opacity(0.1))
        .cornerRadius(12)
    }
}

struct OrderDeliveredView_Previews: PreviewProvider {
    static var previews: some View {
        OrderDeliveredView()
            .previewDisplayName("Order Delivered Screen")
    }
}
