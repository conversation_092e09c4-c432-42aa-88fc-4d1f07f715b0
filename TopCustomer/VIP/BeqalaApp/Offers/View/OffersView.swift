//
//  OffersView.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 05/03/2025.
//  Copyright © 2025 SOTSYS203. All rights reserved.
//

import SwiftUI

struct OffersView: View {
    @StateObject var viewModel = OffersViewModel()
    @State var showingDetails: Bool = false
    @State var offerData: OfferResponseFields?
    
    var body: some View {
        // Products Grid
        NavigationView {
            ScrollView {
                Spacer()
                LazyVGrid(columns: [GridItem(.adaptive(minimum: 120))], spacing: 20) {
                    let offers = viewModel.offersData
                    ForEach(offers) { offer in
                        LazyVStack {
                            LoadingImageWithUrlView(imageUrl: offer.vOfferImage ?? "", height: 120)
                            HStack {
                                Text("\(offer.vOfferName ?? "")")
                                    .font(.subheadline)
                                    .bold()
                                VStack {
                                    Text("\(offer.productOfferedAmount ?? "0.0")")
                                        .font(.subheadline)
                                        .bold()
                                    Text("\(offer.productOriginalAmount ?? "0.0")")
                                        .font(.caption)
                                        .strikethrough()
                                        .foregroundColor(.red)
                                }
                            }.padding()
                        }
                        .background(Color.white)
                        .cornerRadius(12)
                        .shadow(radius: 2)
                        .onTapGesture {
                            self.offerData = offer
                            showingDetails.toggle()
                        }
                    }
                }
                .padding(.horizontal)
            }
        }
        .sheet(isPresented: $showingDetails) {
            OfferCardDetailsView(offerData: $offerData)
        }
        .task {
            if !User.shared.checkUserLoginStatus() {
                if let topVC = UIApplication.shared.topViewController() {
                    topVC.showNoLoginUserAlert(complation: nil)
                }
                return
            }
            await viewModel.getOffers()
        }.navigationTitle("Weekly Offers")
    }
}

//#Preview {
//    OffersView()
//}

