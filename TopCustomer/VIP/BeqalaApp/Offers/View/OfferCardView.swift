//
//  OfferCardView.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 05/03/2025.
//  Copyright © 2025 SOTSYS203. All rights reserved.
//

import SwiftUI

struct OfferCardView: View {
    var title: String
    var imageUrl: String
    var amountAfterDiscount: String
    var amountBeforDiscount: String
    var body: some View {
        Rectangle()
            .fill(Color.white)
            .cornerRadius(20)
            .frame(width: Double.infinity, height: 300)
            .padding(20)
            .shadow(radius: 12)
            .overlay {
                VStack {
                    LoadingImageWithUrlView(imageUrl: imageUrl, height: 200)
                    HStack {
                        // English Offer Text
                        Text(title)
                            .font(.footnote)
                            .bold()
                            .padding()
                        Spacer()
                        // Pricing and Discount
                        VStack {
                            Button(action: {}) {
                                Text(amountAfterDiscount)
                                    .font(.title3)
                                    .bold()
                                    .foregroundColor(.white)
                                    .padding(.horizontal, 20)
                                    .padding(.vertical, 8)
                                    .background(Color.blue)
                                    .cornerRadius(10)
                            }
                            
                            Text(amountBeforDiscount)
                                .font(.title3)
                                .strikethrough()
                                .foregroundColor(.red)
                        }
                        .padding()
                    }
                }.padding(20)
            }
    }
}


//#Preview {
//    OfferCardView()
//}
