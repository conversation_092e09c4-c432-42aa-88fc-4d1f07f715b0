//
//  OfferCardDetailsView.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 05/03/2025.
//  Copyright © 2025 SOTSYS203. All rights reserved.
//

import SwiftUI

struct OfferCardDetailsView: View {
    @Binding var offerData: OfferResponseFields?
    
    var body: some View {
        VStack(spacing: 16) {
            // Offer Title
            Text("Offer")
                .font(.title)
                .bold()
                .foregroundColor(.blue)
            
            // Product Image and Arabic Text
            ZStack {
                HStack {
                    LoadingImageWithUrlView(imageUrl: offerData?.vOfferImage ?? "", height: 200)
                }
                .padding()
            }
            
            // SKU Section
            HStack {
                Text("**Product sku**\n\(offerData?.vBuyProductSKU ?? "")")
                    .font(.subheadline)
                    .foregroundColor(.black)
                    .padding()
                
                Spacer()
                
                Image(systemName: "square.and.arrow.up") // Share Icon
                    .foregroundColor(.gray)
                    .padding()
            }
            .background(Color.gray.opacity(0.1))
            .cornerRadius(12)
            
            // Product Info & Pricing
            VStack(alignment: .leading, spacing: 8) {
                Text("\(offerData?.vOfferName ?? "")")
                    .font(.headline)
                    .bold()
                    .foregroundColor(.black)
                
                HStack {
                    Text("Total offer price: \(offerData?.productOfferedAmount ?? "")")
                        .font(.title3)
                        .foregroundColor(.black)
                }
                
                Text("\(offerData?.txOfferDescription ?? "")")
                    .font(.subheadline)
                    .foregroundColor(.gray)
            }
            
            // Add to Cart Button
            Button(action: {
                CartManager.shared.addItemToCart(productId: offerData?.biProductId ?? 0, productQuantity: 1, price: offerData?.productOfferedAmount ?? "0")
            }) {
                Text("Add to cart")
                    .font(.headline)
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.blue)
                    .cornerRadius(12)
            }
        }
        .padding()
        .background(Color.white)
        .cornerRadius(20)
        .shadow(radius: 4)
        .padding()
    }
}
