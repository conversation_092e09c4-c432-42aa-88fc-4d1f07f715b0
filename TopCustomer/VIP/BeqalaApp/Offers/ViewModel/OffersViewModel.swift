//
//  OffersViewModel.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 05/03/2025.
//  Copyright © 2025 SOTSYS203. All rights reserved.
//

import Combine

@MainActor
class OffersViewModel: ObservableObject {
    
    @Published var offersData: [OfferResponseFields] = []
    
    func getOffers() async {
        let authorization = getAuthorizationText()
        ActivityIndicator.shared.showCentralSpinner()
        OfferAPI.marketOfferListing(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, offset: 1) { data, error in
            debugPrint("Offers Data => ",data)
            self.offersData = data?.responseData?.offers ?? []
            ActivityIndicator.shared.hideCentralSpinner()
        }
    }
}
