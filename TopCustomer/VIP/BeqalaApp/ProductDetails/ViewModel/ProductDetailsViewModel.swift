//
//  ProductDetailsViewModel.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 08/03/2025.
//  Copyright © 2025 SOTSYS203. All rights reserved.
//

import Foundation

@MainActor
class ProductDetailsViewModel: ObservableObject {
    
    @Published var productInfo: ProductResponseFields?
    @Published var productImages: [String] = []
    
    
    func getProductInfoById(_ biProductId: Int) {
        
        ActivityIndicator.shared.showCentralSpinner()
        
        let authorization = User.shared.checkUserLoginStatus() ? getAuthorizationText() : GuestAccessToken
        
        ProductAPI.marketProductDetails(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, biProductId: biProductId) { [self] data, error in
            debugPrint("getProductInfoById", data)
            self.productInfo = data?.responseData?.productDetails
            
            if self.productInfo?.imagesApp?.count ?? 0 == 0 {
                self.productImages = [self.productInfo?.vProductImage ?? ""]
            } else {
                self.productImages = productInfo?.imagesApp ?? []
            }
            
            ActivityIndicator.shared.hideCentralSpinner()
        }
    }
    
    func apiCallForGetFavorite(dictData:[String:Any]) {
        
//        ActivityIndicator.shared.showCentralSpinner()
        
        let authorization = getAuthorizationText()
        
        FavouriteAPI.markFavourite(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, biProductId: Int(setDataInString(dictData["biProductId"] as AnyObject)) ?? 0, tiIsFavourite: Int(setDataInString(dictData["tiIsFavourite"] as AnyObject)) ?? 0) { data, error in
//                ActivityIndicator.shared.hideCentralSpinner()
//                self.presenterProductPopup?.apiResponseGetFavorite(response: data, error: error)
        }
        
    }
}
