//
//  ImageSliderView.swift
//  BeqalaApp
//
//  Created by <PERSON><PERSON> on 06/03/2025.
//

import SwiftUI

// Wrapper struct to make String identifiable
struct IdentifiableImage: Identifiable {
    let id = UUID()
    let name: String
}

struct ImageSliderView: View {
    @Binding var images: [String]  //= ["category01", "category02", "category03"] // Replace with your image names
    @State private var selectedImage: IdentifiableImage?
    @State private var currentPage: Int = 0  // Track current page index
    
    var body: some View {
        VStack {
            TabView(selection: $currentPage) {
                ForEach(images.indices, id: \.self) { index in
                    LoadingImageWithUrlView(imageUrl: images[index] ?? "", height: 500)
                        .cornerRadius(20)
                        .onTapGesture {
                            selectedImage = IdentifiableImage(name: images[index] ?? "")
                        }
                        .tag(index) // Set tag for tracking page index
                }
            }.frame(width: Double.infinity, height: 500)
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never)) // Hide default dots
            // Custom page control
            HStack {
                ForEach(images.indices, id: \.self) { index in
                    Circle()
                        .frame(width: 8, height: 8)
                        .foregroundColor(currentPage == index ? .white : .gray.opacity(0.5))
                }
            }
            .background(
                RoundedRectangle(cornerRadius: 10)
                    .fill(Color.black.opacity(0.2))
                    .frame(width: 110, height: 30) // Background behind dots
            )
        }
        .sheet(item: $selectedImage) { image in
            ZoomableImageView(imageName: image.name) {
                selectedImage = nil // Dismiss when swiped down
            }
        }.onAppear() {
            UIPageControl.appearance().currentPageIndicatorTintColor = .white  // Active dot color
            UIPageControl.appearance().pageIndicatorTintColor = UIColor.white.withAlphaComponent(0.5) // Inactive dot color
            UIPageControl.appearance().backgroundStyle = .prominent // Adds background color behind dots
        }
    }
}

#Preview {
    ImageSliderView(images: .constant([""]))
}

struct ZoomableImageView: View {
    let imageName: String
    var onDismiss: () -> Void?
    @State private var scale: CGFloat = 1.0
    @State private var lastScale: CGFloat = 1.0
    @State private var offset: CGSize = .zero
    @State private var lastOffset: CGSize = .zero
    
    var body: some View {
        ZStack {
            Color.white.ignoresSafeArea()
            LoadingImageWithUrlView(imageUrl: imageName, height: 500)
                .scaledToFit()
                .scaleEffect(scale)
                .offset(offset)
                .gesture(
                    MagnificationGesture()
                        .onChanged { value in
                            scale = max(1.0, lastScale * value)
                        }
                        .onEnded { _ in
                            lastScale = scale
                        }
                )
                .gesture(
                    DragGesture()
                        .onChanged { value in
                            if scale > 1.0 {
                                offset = CGSize(
                                    width: lastOffset.width + value.translation.width,
                                    height: lastOffset.height + value.translation.height
                                )
                            } else if abs(value.translation.height) > 100 {
                                onDismiss() // Swipe down to close
                            }
                        }
                        .onEnded { _ in
                            lastOffset = offset
                        }
                )
                .onTapGesture(count: 2) {
                    withAnimation {
                        scale = scale > 1.0 ? 1.0 : 2.5 // Toggle zoom on double-tap
                        lastScale = scale
                    }
                }
        }
    }
}
