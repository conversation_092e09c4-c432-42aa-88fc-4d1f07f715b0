//
//  ProductDetailsView.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 05/03/2025.
//  Copyright © 2025 SOTSYS203. All rights reserved.
//

import SwiftUI
import TikTokBusinessSDK
import FirebaseAnalytics

struct ProductDetailsView: View {
    
    @StateObject var viewModel = ProductDetailsViewModel()
    var biProductId: Int
    var ifFavOrNot = 0
    
    @Environment(\.dismiss) var dismiss
    @State private var quantity: Int = 1 // Example initial quantity
    @State private var isHeartSelected: Bool = false
    
    var body: some View {
        ScrollView() {
            VStack(spacing: 10) {
                // Close Button and Favorite
                HStack {
                    Button(action: {
                        // Close action
                        dismiss()
                    }) {
                        Image(systemName: "xmark")
                            .foregroundColor(.black)
                            .font(.title2)
                            .padding()
                    }
                    
                    Spacer()
                    
                    Button(action: {
                        // Favorite action
                        isHeartSelected.toggle()
                                                
                        if !User.shared.checkUserLoginStatus() {
                            if let topVC = UIApplication.shared.topViewController() {
                                topVC.showNoLoginUserAlert(complation: nil)
                            }
                            return
                        }

                        var fav = 0
                        if ifFavOrNot == 1 { // remove from favorite
                            fav = 0
                        }
                        else {  // add to favorite
                            fav = 1
                            // Apps Flyer Event
                            TikTokBusiness.trackEvent("ADD_WISHLIST", withProperties: [
                                "item_id": viewModel.productInfo?.biProductId ?? 0
                            ])
                            Analytics.logEvent("ADD_WISHLIST", parameters: [
                                "item_id": viewModel.productInfo?.biProductId ?? 0
                            ])
                            AppsFlyerEvents.shared.afAddToWishlist(price: viewModel.productInfo?.price ?? 0.0 == 0.0 ? viewModel.productInfo?.dbOriginalProductPrice?.toDouble() ?? 0.0 : viewModel.productInfo?.price ?? 0.0, content: viewModel.productInfo?.name == nil ? viewModel.productInfo?.vProductName ?? "" : viewModel.productInfo?.name ?? "", contentId: viewModel.productInfo?.biProductId ?? 0, contentType: viewModel.productInfo?.vProductUnit ?? "")

                        }

                        var dictParam : [String:Any] = [:]
                        dictParam["biProductId"] = viewModel.productInfo?._id == nil ? viewModel.productInfo?.biProductId : viewModel.productInfo?._id
                        dictParam["tiIsFavourite"] = fav

                        if AppSingletonObj.isConnectedToNetworkForCheckoutScreen() {
                            viewModel.apiCallForGetFavorite(dictData: dictParam)
                        }
                        else {
                            AppSingletonObj.showAlert(strMessage: ObjKeymessages.kMSG_NO_INTERNET)
                        }
                    }) {
                        Image(systemName: "heart.fill")
                            .foregroundColor(isHeartSelected ? .red : .gray)
                            .font(.title2)
                            .padding()
                    }
                }
                // Product Images
                ImageSliderView(images: $viewModel.productImages)
                // Product Title and Price
                VStack{
                    Text(viewModel.productInfo?.vProductName ?? "")
                        .font(.headline)
                        .fontWeight(.bold)
                        .frame(maxWidth: .infinity, alignment: .leading)
                    
                    SaudiRiyalAmount(amount: Double(viewModel.productInfo?.dbOriginalProductPrice ?? "0.0") ?? 0.0, size: 20, color: .black.opacity(0.8), fontWight: .bold)
                        .frame(maxWidth: .infinity, alignment: .leading)
                }.padding()
                // Description Section
                VStack(alignment: .leading, spacing: 10) {
                    Text("Overview")
                        .font(.headline)
                    
                    Text(viewModel.productInfo?.txProductDescription ?? "")
                        .font(.body)
                        .foregroundColor(.gray)
                }
                .padding(.horizontal)
                
                Spacer()
                
                // Bottom Bar
                HStack {
                    SaudiRiyalAmount(amount: Double(viewModel.productInfo?.dbOriginalProductPrice ?? "0.0") ?? 0.0, size: 30, color: .black, fontWight: .bold)
                    Spacer()
                    // Add to Cart Button
                    Button(action: {
                        // Add to cart action
                        dismiss()
                    }) {
                        Text("Add to cart")
                            .bold()
                            .frame(maxWidth: .infinity)
                            .padding()
                            .foregroundColor(.white)
                            .background(Capsule().fill(Color.blue))
                    }
                    
                    .frame(maxWidth: 200)
                }
                .padding()
                .background(Color(UIColor.systemGray6))
            }
            .padding(.top, 10)
            .background(Color.white)
            .edgesIgnoringSafeArea(.bottom)
            .onAppear() {
                viewModel.getProductInfoById(biProductId)
                if viewModel.productInfo?.tiIsFavourite == 1 {
                    isHeartSelected.toggle()
                }
            }
        }
    }
}

#Preview {
    ProductDetailsView(biProductId: 1)
}




