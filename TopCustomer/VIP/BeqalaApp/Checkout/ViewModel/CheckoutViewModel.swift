//
//  CheckoutViewModel.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 08/03/2025.
//  Copyright © 2025 SOTSYS203. All rights reserved.
//

import Foundation
import Combine
import SwiftUI
import PaymentSDK
import TikTokBusinessSDK
import FirebaseAnalytics

@MainActor
class CheckoutViewModel: ObservableObject {
    // shared
    static var shared = CheckoutViewModel()
    // address
    @Published var addressVtype: String = ""
    @Published var addressTxt: String = ""
    @Published var iAddressId: Int = 0
    @Published var promoCodeModel: PromocodeCheckResponseFields?
    // property for checkout
    @Published var isCheckoutSuccessfully: Bool = false
    var orderAmount: Double = 0.0
    var iOrderId = 0
    var tiOrderStatus = 0
    var lastOrderId : Int?
    
    
    
    func apiCallForApplyPromoCode(dictData:[String:Any]) {
        ActivityIndicator.shared.showCentralSpinner()
        
        let authorization = getAuthorizationText()

        PromocodeAPI.marketPromoCodeCheck(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, vPromocode: setDataInString(dictData["vPromocode"] as AnyObject), dSubTotal: setDataInString(dictData["dSubTotal"] as AnyObject), dDistanceCost: setDataInString(dictData["dDistanceCost"] as AnyObject)) { data, error in
                ActivityIndicator.shared.hideCentralSpinner()
            self.apiResponseApplyPromoCode(response: data, error: error)
        }
        
    }
    
    
    
    func apiCallForCheckCart(iCartId: Int) {
        ActivityIndicator.shared.showCentralSpinner()
        CartAPI.checkCart(iCartId: iCartId) { data, error in
            ActivityIndicator.shared.hideCentralSpinner()
            debugPrint("apiCallForCheckCart => ", data ?? "")
            self.isCheckoutSuccessfully = true
            ActivityIndicator.shared.hideCentralSpinner()
        }
    }
    
    func createOrder(iCartId: Int,
                     iAddressId: Int,
                     dOrderTotal: String,
                     dVatCost: String,
                     iShiftId: Int,
                     tiTransactionType: Int) {
        /** 1 - Card, 2 - Apple Pay, 3 - COD , 4- Wallet */
        ActivityIndicator.shared.showCentralSpinner()
        
        let authorization = getAuthorizationText()
        
        var reccuringType = OrderAPI.IReccuringType_createOrder(rawValue: ReccuringType.OnlyOnce.rawValue)
        let tiIsFriday = Date().todayisFriday() == true ? 1 : 0
        
        OrderAPI.createMarketOrder(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, iCartId: iCartId, iAddressId: iAddressId, iReccuringType: reccuringType, tAdditionalNote: "", iPromocodeUseId: 0, dOrderTotal: dOrderTotal, dVatCost: dVatCost, tiShiftType:  0, vAlternativeISDCode: "", vAlternativeMobileNumber: "", iShiftId: iShiftId, vShiftDisplayNameEn: "", vShiftDisplayNameAr: "", vStartAt: "11:00:00", vCloseAt: "11:00:00", tiIsFriday: tiIsFriday, tiIsRamadan: 0, tiIsRegularShift: 0, timezone: localTimeZoneIdentifier, isreward: false, rewardamount: 0, isMosques: "0", isCartIncludeBundle: 0, expectedTime: Date()) { data, error in
            print(data)
            ActivityIndicator.shared.hideCentralSpinner()
            if tiTransactionType != 3 {
                var configuration: PaymentSDKConfiguration! {
                    let theme = PaymentSDKTheme.default
                    theme.logoImage = UIImage(named: "logo_login_white")
                    theme.secondaryColor = UIColor.AppTheme_BlueColor_012CDA
                    theme.secondaryFontColor = UIColor.AppTheme_BlueColor_012CDA
                    theme.primaryFontColor = .black
                    theme.strokeColor = UIColor.AppTheme_BlueColor_012CDA
                    theme.buttonColor = UIColor.AppTheme_BlueColor_012CDA
                    theme.titleFontColor = .black
                    theme.buttonFontColor = .white
                    var strToken = ""
                    var strTransactionReference = ""
                    self.orderAmount = Double(data?.responseData?.dOrderTotal ?? "0.0") ?? 0.0
                    var strAddress = ""
                    var strCity = ""
                    var strZipcode = ""
                    var strState = ""
                    var strCountryCode = ""
                    
                    if let address = UserDefaults.standard.object(forKey: USERDEFAULTS_INFO_KEY.CURRENT_ADDRESS) as? [String:Any],let data = address.data {
                        let decodeResult: (decodableObj: AddressResponseFields?, error: Error?) = CodableHelper.decode(AddressResponseFields.self, from: data)
                        if let decodeObj = decodeResult.decodableObj {
                            strAddress = decodeObj.txAddress ?? ""
                            strCity = decodeObj.vCity ?? ""
                            strZipcode = decodeObj.vZipCode ?? ""
                            strState = decodeObj.vState ?? ""
                            strCountryCode = decodeObj.vCountryCode ?? ""
                            
                        }
                    }
                    
                    
                    var billingDetails: PaymentSDKBillingDetails! {
                        return PaymentSDKBillingDetails(name: User.shared.vName,
                                                        email: User.shared.vEmailId,
                                                        phone: "\(User.shared.vISDCode ?? "")\(User.shared.vMobileNumber ?? "")",
                                                        addressLine: strAddress,
                                                        city: strCity,
                                                        state: strState,
                                                        countryCode: strCountryCode,
                                                        zip: strZipcode)
                    }
                    
                    return PaymentSDKConfiguration(profileID: profileID,
                                                   serverKey: serverKey,
                                                   clientKey: clientKey,
                                                   currency: SACurrencyCode,
                                                   amount: self.orderAmount,
                                                   merchantCountryCode: MerchantCountryCode)
                    .cartDescription("cart description")
                    .cartID(data?.responseData?.vOrderNumber ?? "")
                    .screenTitle(AppName)
                    .theme(theme)
                    .showBillingInfo(true)
                    .hideCardScanner(true)
                    .languageCode(UserDefaults.standard.getLanguage() ?? "")
                    .tokeniseType(.userOptinoal)
                    .tokenFormat(.hex32)
                    .token(strToken)
                    .transactionReference(strTransactionReference)
                    .billingDetails(billingDetails)
                }
                
                guard let topVC = getTOPVC() else { return }
                PaymentManager.startCardPayment(on: topVC, configuration: configuration, delegate: PaymentDelegate())
            } else {
                self.lastOrderId = data?.responseData?.iOrderId ?? 0
                self.updateOrder(iOrderId: data?.responseData?.iOrderId ?? 1, tiTransactionType: tiTransactionType, vTransactionRef: "", iPaymentStatus:  0 , iUseWallet: 0, vCardName: "")
            }
        }
    }
    
    func updateOrder(iOrderId: Int, tiTransactionType: Int, vTransactionRef: String, iPaymentStatus: Int, iUseWallet: Int, vCardName: String) {
        // update transaction
        
        ActivityIndicator.shared.showCentralSpinner()
        
        let authorization = getAuthorizationText()
        
        OrderAPI.orderTransactionUpdate(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, iOrderId: iOrderId, tiTransactionType: tiTransactionType, vTransactionRef: vTransactionRef, iPaymentStatus: iPaymentStatus , iUseWallet: iUseWallet, vCardName: vCardName) { data, error in
            ActivityIndicator.shared.hideCentralSpinner()
            print(data)
            self.isCheckoutSuccessfully = true
            
        }
        
        
    }
    
    func cancelOrder(iOrderId: Int) {
        
        ActivityIndicator.shared.showCentralSpinner()
        
        let authorization = getAuthorizationText()
        
        OrderAPI.deleteOrder(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, iOrderId: iOrderId) { data, error in
            
            ActivityIndicator.shared.hideCentralSpinner()
        }
        
    }
    
    func setAddress(addressVtype: String, addressTxt: String, iAddressId: Int) {
        self.addressTxt = addressTxt
        self.addressVtype = addressVtype
        self.iAddressId = iAddressId
    }
    
    func apiResponseApplyPromoCode(response:PromocodeCheckResponse?,error:Error?) {
        if let error = error  {
            AppSingletonObj.showAlert(strMsg: error.localizedDescription)
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            return
        }
        
        if code == APIINVALIDAUTHORIZATIONCODE401 {
            AppSingletonObj.displaySessionExpiredAlert(strMsg: response.responseMessage ?? "")
            return
        }

        if code == APICODE400 {
            AppSingletonObj.showAlert(strMessage: response.responseMessage ?? "")
            return
        }
        
        guard let model = response.responseData,code == APISUCCESSCODE200  else {
            return
        }
//        self.viewControllerCheckout?.promoCodeSuccess(model: model, strMsg: response.responseMessage ?? "")
        self.promoCodeModel = model
    }
    
}



class CheckoutManager: AnyObject {
    static var shared = CheckoutManager()
    var addressID = 0
    var addressType = ""
    
    func setAddress(id: Int, type: String) {
        addressID = id
        addressType = type
    }
}

/// Returns the topmost view controller in the application's view hierarchy.
///
/// - Returns: The topmost view controller, or `nil` if not found.
func getTOPVC() -> UIViewController? {
    let keyWindow = UIApplication.shared.windows.filter {$0.isKeyWindow}.first
    
    if var topController = keyWindow?.rootViewController {
        while let presentedViewController = topController.presentedViewController {
            topController = presentedViewController
        }
        return topController
    }
    return nil
}


class PaymentDelegate: NSObject, PaymentManagerDelegate {
    func paymentManager(didFinishTransaction transactionDetails: PaymentSDK.PaymentSDKTransactionDetails?, error: Error?) {
        if let transactionDetails = transactionDetails {
            print("Response Code: " + (transactionDetails.paymentResult?.responseCode ?? ""))
            print("Result: " + (transactionDetails.paymentResult?.responseMessage ?? ""))
            print("Token: " + (transactionDetails.token ?? ""))
            print("Transaction Reference: " + (transactionDetails.transactionReference ?? ""))
            print("Transaction Time: " + (transactionDetails.paymentResult?.transactionTime ?? "" ))
            if transactionDetails.isSuccess() {
                print("Successful transaction")
            }
        } else if let error = error {
            print("Error: \(error.localizedDescription)")
        }
    }
}
