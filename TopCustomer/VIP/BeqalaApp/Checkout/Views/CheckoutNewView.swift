//
//  CheckoutNewView.swift
//  BeqalaApp
//
//  Created by <PERSON><PERSON> on 04/02/2025.
//

import SwiftUI
import PaymentSDK

struct CheckoutNewView: View {
    @Binding var selectedTab: Int
    @ObservedObject var viewModel = CheckoutViewModel()
    @State var choosePaymentVC: Bool = false
    @State var chooseLocationVC: Bool = false
    @State var paymentType: TransactionType = .NoType
    @State private var goToInvoice = false

    var iCartId: Int
    var itemsPrice: Double
    var deliveryfees: Double
    var vat: Double
    var discount: Double
    var totalAmount: Double
    var iShiftId: Int
    @State var iAddressId: Int = 1
    @State var addressTxt: String = ""

    @State private var useWallet = true
    @State private var orderAmount: Double = 0.0
    
    func createOrder() {
        viewModel.createOrder(iCartId: iCartId, iAddressId: iAddressId, dOrderTotal: "\(totalAmount)", dVatCost: "\(vat)", iShiftId: iShiftId, tiTransactionType: paymentType.rawValue)
    }

    func payWithApplePay(totalAmount: Double) {
        // Get address details
        var strAddress = ""
        var strCity = ""
        var strZipcode = ""
        var strState = ""
        var strCountryCode = ""

        if let address = UserDefaults.standard.object(forKey: USERDEFAULTS_INFO_KEY.CURRENT_ADDRESS) as? [String:Any],let data = address.data {
            let decodeResult: (decodableObj: AddressResponseFields?, error: Error?) = CodableHelper.decode(AddressResponseFields.self, from: data)
            if let decodeObj = decodeResult.decodableObj {
                strAddress = decodeObj.txAddress ?? ""
                strCity = decodeObj.vCity ?? ""
                strZipcode = decodeObj.vZipCode ?? ""
                strState = decodeObj.vState ?? ""
                strCountryCode = decodeObj.vCountryCode ?? ""
            }
        }

        // Get email
        var strEmail = ""
        if User.shared.vEmailId == "" {  // use default email id
            strEmail = DefaultEmailForApplePay
        }
        else {
            strEmail = User.shared.vEmailId ?? ""
        }

        // Setup billing details
        var billingDetails: PaymentSDKBillingDetails! {
            return PaymentSDKBillingDetails(name: User.shared.vName,
                                         email: strEmail,
                                            phone: "\(User.shared.vISDCode ?? "")\(User.shared.vMobileNumber ?? "")",
                                         addressLine: strAddress,
                                         city: strCity,
                                         state: strState,
                                         countryCode: strCountryCode,
                                         zip: strZipcode)
        }

        // Setup shipping details
        var shippingDetails: PaymentSDKShippingDetails! {
            return PaymentSDKShippingDetails(name: User.shared.vName,
                                             email: strEmail,
                                             phone: "\(User.shared.vISDCode ?? "")\(User.shared.vMobileNumber ?? "")",
                                             addressLine: strAddress,
                                             city: strCity,
                                             state: strState,
                                             countryCode: strCountryCode,
                                             zip: strZipcode)
        }

        // Set order amount
        orderAmount = totalAmount

        // Configure Apple Pay
        var applePayConfiguration: PaymentSDKConfiguration! {
            return PaymentSDKConfiguration(profileID: profileID,
                                           serverKey: serverKey,
                                           clientKey: clientKey,
                                           currency: SACurrencyCode,
                                           amount: orderAmount,
                                           merchantCountryCode: MerchantCountryCode)
                .cartDescription("cart description ApplePay")
                .cartID("TEMP_\(Int.random(in: 1000...9999))")
                .screenTitle(AppName)
                .languageCode(UserDefaults.standard.getLanguage() ?? "")
                .merchantName("Material")
                .merchantAppleBundleID(PAYTABS_MERCHANT_IDENTIFIER)
                .simplifyApplePayValidation(true)
                .billingDetails(billingDetails)
                .shippingDetails(shippingDetails)
        }

        // Start Apple Pay payment
        if let topVC = UIApplication.shared.topViewController() {
            PaymentManager.startApplePayPayment(on: topVC,
                                         configuration: applePayConfiguration,
                                         delegate: ApplePayDelegate(checkoutView: self))
        }
    }
    
    var body: some View {
        NavigationStack {
            VStack {
                ScrollView {
                    // Delivery Address & Time
                    SectionView(title: "Delivery Address & Time") {
                        HStack {
                            Image(systemName: "mappin.and.ellipse")
                                .foregroundColor(.black)
                            Text("Delivering to: ")
                                .fontWeight(.bold)
                            Text(addressTxt)
                                .foregroundColor(.gray)
                                .lineLimit(2)
                            Spacer()
                            Button(action: {
                                // Action for button
                                chooseLocationVC.toggle()
                            }) {
                                Text("Change 〉")
                                    .foregroundColor(.blue)
                            }
                        }
                    }
                    
                    // Payment Methods
                    SectionView(title: "Payment Methods") {
                        HStack {
                            Image(systemName: "creditcard")
                            Text("Change Payment Method")
                            Spacer()
                            Button(action: {
                                // Action for button
                                choosePaymentVC.toggle()
                            }) {
                                Text("Change 〉")
                                    .foregroundColor(.blue)
                            }
                        }
                        .padding(.vertical, 5)
                    }
                    Divider()
                    
                    PromoCodeView { code in
                        if AppSingletonObj.isConnectedToNetwork(){
                            var dictParam : [String:Any] = [:]
                            dictParam["vPromocode"] = code
                            dictParam["dSubTotal"] = "\(totalAmount)"
//                            dictParam["dDistanceCost"] = strDistanceCost

                            if code.trimmed == "" {
                                AppSingletonObj.showAlert(strMessage: ObjKeymessages.kMSG_EMPTY_PROMO_CODE)
                                return
                            }

                            viewModel.apiCallForApplyPromoCode(dictData: dictParam)
                        }
                    }
                    
                    Divider()
                    // Order Summary
                    SectionView(title: "Summary") {
                        SummaryRow(label: "Items price", value: "\(itemsPrice)", isHighlighted: true)
                        SummaryRow(label: "Delivery fee", value: "\(deliveryfees)", isHighlighted: true)
                        SummaryRow(label: "Vat", value: "\(vat)", isHighlighted: true)
                        if discount >= 1 {
                            SummaryRow(label: "Discount", value: "-\(discount)", isHighlighted: true, isDiscount: true, color: .red)
                        }
                    }
                }
                
                // Place Order Button
                Button(action: {
                    // Place order action
                    if paymentType == .NoType {
                        AppSingletonObj.showAlert(strMessage: ObjKeymessages.kMSG_EMPTY_PAYMENT_METHOD)
                    }else{
                        createOrder()
                    }
                    
                }) {
                    VStack {
                        // Total Price
                        HStack {
                            Text("Total")
                                .font(.headline)
                                .foregroundStyle(.black)
                            Spacer()
                            SaudiRiyalAmount(amount: deliveryfees + itemsPrice + vat - discount , fontWight: .bold)
                        }
                        .padding(.horizontal)
                        .padding(.vertical, 5)
                        
                        Text("Place Order")
                            .font(.headline)
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(Color("AppTheme_BlueColor_#012CDA", bundle: .main))
                            .cornerRadius(10)
                    }
                }.padding()
                    .background(.gray.opacity(0.2))
            }.sheet(isPresented: $choosePaymentVC) {
                MyViewChoosePaymentVC(total: "\(totalAmount)") { paymentType in
                    debugPrint(paymentType)
                    self.paymentType = paymentType
                    switch paymentType {
                    case .COD:
                        self.paymentType = paymentType
                    case .ApplePay:
                        self.paymentType = paymentType
                        // Calculate total amount for Apple Pay
                        let calculatedTotal = deliveryfees + itemsPrice + vat - discount
                        payWithApplePay(totalAmount: calculatedTotal)
                    case .Card: break

                    default:
                        debugPrint("unsupported payment")
                    }
                }
            }.sheet(isPresented: $chooseLocationVC) {
                //                PaymentsStoryboard.instantiate(ChoosePaymentViewController.self)
                MyViewSelectLocationVC() { address in
                    iAddressId = address.id
                    addressTxt = address.name
                }
            }
            .navigationTitle("Pay")
            .navigationBarTitleDisplayMode(.inline)
            .onReceive(viewModel.$isCheckoutSuccessfully) { isSuccess in
                if isSuccess {
                    print("Checkout was successful — يمكن هنا تنقل لـ View تانية أو تعرض Alert")
                    if let topVC = UIApplication.shared.topViewController() {
                        let vc = ProductPopupStoryboard.instantiate(ThankYouViewController.self)
                        vc.modalPresentationStyle = .overFullScreen
                        vc.modalTransitionStyle   = .crossDissolve
                        vc.completionBlock = { action in
                              guard isSuccess, let orderId = viewModel.lastOrderId else { return }
                              goToInvoice  = true
//                            self.selectedTab = 3
//                            goToMainTabBar(selectedWarehouseId: 1)
//                            print("fede")
                        }
                        topVC.present(vc, animated: true)
                    }
                }
            }
            .navigationDestination(isPresented: $goToInvoice) {
                OrderDetailView(orderId: viewModel.lastOrderId ?? 0)
            }.onAppear{
                if Constant.shared.SELECTED_ADDRESS_NAME != "" {
                    addressTxt = Constant.shared.SELECTED_ADDRESS_NAME
                    iAddressId =  Constant.shared.SELECTED_ADDRESS_ID
                } else {
                    addressTxt = getGuestLocation()?.addressName ?? ""
                }
            }
            
//            .alert("Order Placed successfully!", isPresented: $viewModel.isCheckoutSuccessfully) {
//                Button("OK", role: .cancel) {
//                    goToMainTabBar(selectedWarehouseId: 1)
//                }
//            }
        }
    }
    
}

// Section Header Component
struct SectionView<Content: View>: View {
    let title: String
    let trailingText: String?
    let content: Content
    
    init(title: String, trailingText: String? = nil, @ViewBuilder content: () -> Content) {
        self.title = title
        self.trailingText = trailingText
        self.content = content()
    }
    
    var body: some View {
        VStack(alignment: .leading) {
            HStack {
                Text(title)
                    .font(.headline)
                    .fontWeight(.bold)
                Spacer()
                if let trailingText = trailingText {
                    Text(trailingText)
                        .foregroundColor(.blue)
                }
            }
            content
        }
        .padding()
    }
}

// PromoCodeView
struct PromoCodeView: View {
    @State private var promoCode: String = ""
    var onApply: (String) -> Void
    @ObservedObject var viewModel = CheckoutViewModel()

    var body: some View {
        VStack(alignment: .leading, spacing: 10) {
            Text("Promo Code")
                .font(.headline)
                .padding(.horizontal, 4)

            HStack(spacing: 0) {
                TextField("Enter promo code", text: $promoCode)
                    .padding(.horizontal, 12)
                    .frame(height: 44)
                    .background(Color.white)
                    .overlay(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(Color.gray.opacity(0.5), lineWidth: 1)
                    )
                    .cornerRadius(8)

                Button(action: {
                    onApply(promoCode)
                }) {
                    Text("Apply")
                        .font(.subheadline)
                        .foregroundColor(.white)
                        .padding(.horizontal, 16)
                        .frame(height: 44)
                        .background(Color("AppTheme_BlueColor_#012CDA", bundle: .main))
                        .cornerRadius(8)
                }
                .padding(.leading, 8)
            }
        }.padding(.all , 16)
    }
}

// Order Summary Row Component
struct SummaryRow: View {
    let label: String
    let value: String
    var isHighlighted: Bool = false
    var isDiscount: Bool = false
    var color: Color = .black
    
    var body: some View {
        HStack {
            Text(label)
                .foregroundColor(isDiscount ? .red : .black)
            Spacer()
            SaudiRiyalAmount(amount: Double(value) ?? 0.0, color: color, fontWight: isHighlighted ? .bold : .regular)
        }
        .padding(.vertical, 2)
    }
}

#Preview {
    CheckoutNewView(selectedTab: .constant(0) , iCartId: 0, itemsPrice: 20, deliveryfees: 15 , vat: 0.14, discount: 1, totalAmount: 35, iShiftId: 1, iAddressId: 1)
}


import UIKit
// Step 1: Create a UIViewControllerRepresentable
struct MyViewChoosePaymentVC: UIViewControllerRepresentable {
    var total: String
    var onPaymentSelected: (TransactionType) -> Void?
    
    func makeUIViewController(context: Context) -> UIViewController {
        let vc = PaymentsStoryboard.instantiate(ChoosePaymentViewController.self)
        vc.modalPresentationStyle = .overFullScreen
        vc.modalTransitionStyle = .crossDissolve
        
        vc.grandTotal = total
        vc.applePaySelected = {
            onPaymentSelected(TransactionType.ApplePay)
        }
        
        vc.codSelected = {
            debugPrint("COD Payment Selected")
            onPaymentSelected(TransactionType.COD)
        }
        return vc
    }
    
    func updateUIViewController(_ uiViewController: UIViewController, context: Context) {
        // Update logic if needed
    }
}

struct GetAddres {
    var id: Int
    var name: String
}
// Step 1: Create a UIViewControllerRepresentable
struct MyViewSelectLocationVC: UIViewControllerRepresentable {
    
    var getSelectedAddress: (GetAddres) -> Void?
    
    func makeUIViewController(context: Context) -> UIViewController {
        let vc = settingsStoryboard.instantiate(SelectLocationViewController.self)
        vc.modalPresentationStyle = .overFullScreen
        vc.modalTransitionStyle = .crossDissolve
        vc.isMustSelectMosque = false
        vc.onSelectedLocationId = { value in
            getSelectedAddress(value)
        }
        return vc
    }
    
    func updateUIViewController(_ uiViewController: UIViewController, context: Context) {
        // Update logic if needed
    }
}


// MARK: - Apple Pay Delegate
class ApplePayDelegate: NSObject, PaymentManagerDelegate {
    weak var checkoutView: CheckoutNewView?

    init(checkoutView: CheckoutNewView) {
        self.checkoutView = checkoutView
    }

    func paymentManager(didFinishTransaction transactionDetails: PaymentSDKTransactionDetails?, error: Error?) {
        if let transactionDetails = transactionDetails {
            var trans_type = 0
            var strCardScheme = ""

            if transactionDetails.cartDescription == "cart description ApplePay" {  // apple pay
                trans_type = 2
                strCardScheme = transactionDetails.paymentInfo?.paymentDescription ?? ""
            }
            else {  // card
                trans_type = 1
                strCardScheme = transactionDetails.paymentInfo?.cardScheme ?? ""
            }

            if transactionDetails.isSuccess() {
                print("Apple Pay payment successful")
                // Create order after successful payment
                checkoutView?.createOrderAfterApplePaySuccess(
                    transactionReference: transactionDetails.transactionReference ?? "",
                    cardScheme: strCardScheme
                )
            } else {
                print("Apple Pay payment failed")
                checkoutView?.handlePaymentFailure(
                    transactionReference: transactionDetails.transactionReference ?? "",
                    cardScheme: strCardScheme
                )
            }
        } else if let error = error {
            print("Apple Pay error: \(error.localizedDescription)")
            // Handle payment cancellation or error
            checkoutView?.handlePaymentError(error: error)
        }
    }

    func paymentManager(didCancelPayment error: Error?) {
        print("Apple Pay payment cancelled")
        checkoutView?.handlePaymentCancellation()
    }
}

// MARK: - CheckoutNewView Extension for Payment Handling
extension CheckoutNewView {
    func createOrderAfterApplePaySuccess(transactionReference: String, cardScheme: String) {
        // Calculate total amount
        let calculatedTotal = deliveryfees + itemsPrice + vat - discount

        // Create order with Apple Pay transaction type
        viewModel.createOrder(
            iCartId: iCartId,
            iAddressId: iAddressId,
            dOrderTotal: "\(calculatedTotal)",
            dVatCost: "\(vat)",
            iShiftId: iShiftId,
            tiTransactionType: TransactionType.ApplePay.rawValue
        )

        // Wait a bit for order creation then update transaction
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            self.updateTransaction(
                strTransactionReference: transactionReference,
                paymentStatus: 1,
                strCardScheme: cardScheme,
                transactionType: TransactionType.ApplePay.rawValue
            )
        }
    }

    func handlePaymentFailure(transactionReference: String, cardScheme: String) {
        DispatchQueue.main.async {
            // Show error alert
            if let topVC = UIApplication.shared.topViewController() {
                let alert = UIAlertController(title: "Payment Failed", message: "Apple Pay payment failed. Please try again.", preferredStyle: .alert)
                alert.addAction(UIAlertAction(title: "OK", style: .default))
                topVC.present(alert, animated: true)
            }
        }
    }

    func updateTransaction(strTransactionReference: String, paymentStatus: Int, strCardScheme: String, transactionType: Int) {
        guard let orderId = viewModel.lastOrderId else {
            print("No order ID available for transaction update")
            return
        }

        viewModel.updateOrder(
            iOrderId: orderId,
            tiTransactionType: transactionType,
            vTransactionRef: strTransactionReference,
            iPaymentStatus: paymentStatus,
            iUseWallet: 0,
            vCardName: strCardScheme
        )
    }

    func handlePaymentError(error: Error) {
        DispatchQueue.main.async {
            // Show error alert
            if let topVC = UIApplication.shared.topViewController() {
                let alert = UIAlertController(title: "Payment Error", message: error.localizedDescription, preferredStyle: .alert)
                alert.addAction(UIAlertAction(title: "OK", style: .default))
                topVC.present(alert, animated: true)
            }
        }
    }

    func handlePaymentCancellation() {
        DispatchQueue.main.async {
            // Handle payment cancellation - maybe delete the order or show a message
            print("Payment was cancelled by user")
        }
    }
}
