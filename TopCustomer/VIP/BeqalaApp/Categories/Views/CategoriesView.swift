//
//  CategoriesView.swift
//  BeqalaApp
//
//  Created by <PERSON><PERSON> on 04/02/2025.
//

import SwiftUI

struct CategoriesView: View {
    @StateObject var viewModel = CategoriesViewModel()
    var categoryId: Int
    @Binding var catgoryName: String
    
    var body: some View {
        NavigationStack {
            VStack(alignment: .leading) {
                // Products Grid
                ScrollView {
                    LazyVGrid(columns: [GridItem(.adaptive(minimum: 120))], spacing: 5) {
                        ForEach(viewModel.categories.indices, id: \.self) { index in
                            LazyVStack {
                                ProductItemView(id: viewModel.categories[index].biProductId ?? 0, name: viewModel.categories[index].vProductName ?? "", amount: viewModel.categories[index].price ?? 0.0, imageUrl: viewModel.categories[index].vProductImage ?? "") { itemAddedd in
                                    debugPrint(itemAddedd)
                                }
                            }
                        }
                    }
                    .padding(.horizontal)
                }
            }.onAppear() {
                viewModel.getCategoriesById(categoryId, pageNo: 1)
            }
            .navigationTitle(catgoryName)
            .navigationBarTitleDisplayMode(.inline)
        }
    }
}


struct Product: Identifiable {
    let id = UUID()
    let name: String
    let price: String
    let image: String
}


#Preview {
    CategoriesView(categoryId: 1, catgoryName: .constant("Raouf"))
}
