//
//  CategoriesViewModel.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 08/03/2025.
//  Copyright © 2025 SOTSYS203. All rights reserved.
//

import Foundation
import Combine

@MainActor
class CategoriesViewModel: ObservableObject {
    
    @Published var categories: [NewProductResponse] = []
    
    func getCategoriesById(_ id: Int, pageNo: Int) {
        
        ActivityIndicator.shared.showCentralSpinner()
        
        let authorization = User.shared.checkUserLoginStatus() ? getAuthorizationText() : GuestAccessToken
        ProductAPI.NewProductListing(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, iCategoryId: id, vSerchString: nil, offset: 1, pageNo: pageNo, isOnlyBundle: 0) { data, error in
            self.categories = data?.data?.products ?? []
            ActivityIndicator.shared.hideCentralSpinner()
        }
    }
}
