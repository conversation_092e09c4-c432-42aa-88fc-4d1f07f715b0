//
//  NewProductsView.swift
//  TopCustomer
//
//  Created by mac<PERSON>osh on 24/07/2025.
//  Copyright © 2025 SOTSYS203. All rights reserved.
//

import SwiftUI

struct NewProductsView: View {
    @StateObject var viewModel = NewProductsViewModel()
    var bannerId: Int
    var msiId: Int
    @Binding var catgoryName: String
    
    var body: some View {
        NavigationStack {
            VStack(alignment: .leading) {
                productsGrid
            }
            .onAppear() {
                if bannerId == 0 {
                    viewModel.getCategoriesById(nil, msiId)
                }else{
                    viewModel.getCategoriesById(bannerId, nil)
                }
            }
            .navigationTitle(catgoryName)
            .navigationBarTitleDisplayMode(.inline)
        }
    }
    
    private var productsGrid: some View {
        GeometryReader { geometry in
            let horizontalPadding: CGFloat = 16
            let spacing: CGFloat = 10
            let totalSpacing = spacing * 2
            let availableWidth = geometry.size.width - (horizontalPadding * 2) - totalSpacing
            let itemSize = availableWidth / 3
            
            ScrollView {
                LazyVGrid(columns: [
                    GridItem(.flexible(), spacing: spacing),
                    GridItem(.flexible(), spacing: spacing),
                    GridItem(.flexible())
                ], spacing: spacing) {
                    ForEach(viewModel.productsList.indices, id: \.self) { i in
                        productItemView(for: i, size: itemSize)
                    }
                }
                .padding(.horizontal, horizontalPadding)
            }
        }
    }
    
    @ViewBuilder
    private func productItemView(for index: Int, size: CGFloat) -> some View {
        let product = viewModel.productsList[index]
        ProductSquareItemView(
            id: product.biProductId ?? 0,
            name: product.vProductName ?? "",
            amount: product.price ?? 0.0,
            imageUrl: product.vProductImage ?? "",
            itemSize: size
        ) { tapped in
            debugPrint(tapped)
        }
    }
}

#Preview {
    NewProductsView(bannerId: 0, msiId: 0, catgoryName: .constant("Category Name"))
}
