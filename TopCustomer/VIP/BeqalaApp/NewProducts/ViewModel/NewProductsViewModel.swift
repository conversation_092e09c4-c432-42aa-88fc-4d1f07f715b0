//
//  NewProductsViewModel.swift
//  TopCustomer
//
//  Created by macintosh on 24/07/2025.
//  Copyright © 2025 SOTSYS203. All rights reserved.
//

import Foundation
import Combine

@MainActor
class NewProductsViewModel: ObservableObject {
    
    @Published var productsList: [NewProductResponse] = []
    
    func getCategoriesById(_ banner_id: Int? = nil,_ msi_id : Int? = nil) {
        
        ActivityIndicator.shared.showCentralSpinner()
        
        let authorization = User.shared.checkUserLoginStatus() ? getAuthorizationText() : GuestAccessToken
        
        ProductAPI.NewViewProductListing(authorization: authorization ,accept: AcceptParamForHeader, lang: CurrentAppLang ,banner_id : banner_id, msi_id : msi_id) { data, error in
            self.productsList = data?.data?.products ?? []
            ActivityIndicator.shared.hideCentralSpinner()
        }
    }
}
