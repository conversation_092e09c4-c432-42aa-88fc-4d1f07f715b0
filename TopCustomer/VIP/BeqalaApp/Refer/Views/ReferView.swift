//
//  ReferView.swift
//  TopCustomer
//
//  Created by macintosh on 25/06/2025.
//  Copyright © 2025 SOTSYS203. All rights reserved.
//

import SwiftUI

struct ReferView: View {
    @State private var referralCode = ""
    @State private var isCopied = false
    
    var body: some View {
        VStack(spacing: 24) {
            Spacer().frame(height: 40)
            
            // Icon
            Image("logo")
                .resizable()
                .scaledToFit()
                .frame(width: 60, height: 60)
                .foregroundColor(.purple)
            
            // Title
            Text("Good friends give Material!")
                .font(.title2)
                .fontWeight(.bold)
                .multilineTextAlignment(.center)
            
            // Subtitle
            Text("Get 25 ﷼ voucher for every friend you invite when they place their first order using your code.")
                .font(.subheadline)
                .foregroundColor(.gray)
                .multilineTextAlignment(.center)
                .padding(.horizontal, 32)
            
            // Code + Copy
            HStack {
                Text(referralCode)
                    .fontWeight(.medium)
                    .padding(.leading)
                
                Spacer()
                
                Button(action: {
                    UIPasteboard.general.string = referralCode
                    isCopied = true
                }) {
                    HStack(spacing: 4) {
                        Text("Copy")
                        Image(systemName: "doc.on.doc")
                    }
                    .padding(.vertical, 6)
                    .padding(.horizontal, 12)
                    .background(Color(.systemGray6))
                    .cornerRadius(10)
                }
            }
            .padding()
            .background(Color(.systemGray5))
            .cornerRadius(12)
            .padding(.horizontal)
            
            // Share Button
            Button(action: {
                // Share code action
                let message = "Use my referral code \(referralCode) to get a discount on Material!"
                let activityVC = UIActivityViewController(activityItems: [message], applicationActivities: nil)
                UIApplication.shared.topViewController()?.present(activityVC, animated: true)
            }) {
                Text("Share Invite")
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.cyan)
                    .foregroundColor(.white)
                    .cornerRadius(15)
                    .padding(.horizontal)
            }
            
            Spacer()
        }
        .navigationTitle("Refer a Friend")
        .navigationBarTitleDisplayMode(.inline)
    }
}
