//
//  ProductItemView.swift
//  BeqalaApp
//
//  Created by <PERSON><PERSON> on 06/03/2025.
//

import SwiftUI

struct ProductItemView: View {
    var id: Int
    var name: String
    var amount: Double
    var imageUrl: String
    @State var showItemDetails: Bool = false
    var itemAdded: (Bool) -> Void
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            ZStack(alignment: .topLeading) {
                // Product Image
                LoadingImageWithUrlView(imageUrl: imageUrl, height: 160)
                    .cornerRadius(12)
            }
            .frame(height: 160)
            .shadow(radius: 3)
            .overlay(
                // Plus Button
                Button(action: {
                    print("Add to cart")
                    if !User.shared.checkUserLoginStatus() {
                        if let topVC = UIApplication.shared.topViewController() {
                            topVC.showNoLoginUserAlert(complation: nil)
                        }
                        return
                    }
                    CartManager.shared.addItemToMarketCart(productId: id, productQuantity: 1, price: "\(amount)") { value in
                        AppSingletonObj.showAlert(strMessage: "Added to Cart Successfully")
                        itemAdded(value)
                    }
                }) {
                    Image(systemName: "plus")
                        .foregroundColor(.black)
                        .padding(8)
                        .background(Color.white)
                        .clipShape(Circle())
                        .shadow(radius: 3)
                }
                    .offset(x: 10, y: 10), // Adjust position
                alignment: .bottomTrailing
            )
            
            // Price
            SaudiRiyalAmount(amount: amount, size: 15)
            
            // Product Name
            Text(name)
                .font(.caption)
                .fontWeight(.bold)
                .foregroundColor(.black)
                .lineLimit(2)
        }.sheet(isPresented: $showItemDetails) {
            ProductDetailsView(biProductId: id)
        }
        .padding()
        .background(Color.white)
        .cornerRadius(12)
        .frame(maxWidth: 190, maxHeight: 280)
        .onTapGesture {
            showItemDetails.toggle()
        }
    }
    
}

#Preview {
    ProductItemView(id: 0, name: "name", amount: 34.90, imageUrl: "category01") { value in
        
    }
}
