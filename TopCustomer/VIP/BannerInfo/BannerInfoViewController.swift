
import UIKit
import STTabbar

protocol BannerInfoProtocol: AnyObject {
    func displayAlert(string:String)
    func displayErrorAlert(strMsg:String)
    func getBannerDetailsInfo(model: ListResponseFields)

}

class BannerInfoViewController: BottomPopupViewController, BannerInfoProtocol {

    // MARK: Objects & Variables
    var presenterBannerInfo: BannerInfoPresentationProtocol?
    
    // MARK: IBOutlets
    
    @IBOutlet weak var lblDescription: UILabel!
    @IBOutlet weak var scrollView: UIScrollView!
    @IBOutlet weak var imgBanner: UIImageView!
    @IBOutlet weak var btnContinueShopping: CustomRoundedButtton!
    @IBOutlet weak var cons_viewOuter_height: NSLayoutConstraint!    
    @IBOutlet weak var viewOuter: UIView!
    @IBOutlet weak var btnShare: UIButton!
    @IBOutlet weak var viewAdvertising: UIView!
    @IBOutlet weak var lblAdvertisingTitle: MaterialLocalizeLable!
    var objBanner: ListResponseFields?
    var bannerId = 0
    var strFromWhichScreen = ""

    // MARK: Object lifecycle
    /*
    override init(nibName nibNameOrNil: String?, bundle nibBundleOrNil: Bundle?) {
        super.init(nibName: nibNameOrNil, bundle: nibBundleOrNil)
        setup()
    }
    */
    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
        setup()
    }
    
    // MARK: Setup
    
    private func setup() {
        let viewController = self
        let interactor = BannerInfoInteractor()
        let presenter = BannerInfoPresenter()
        
        //View Controller will communicate with only presenter
        viewController.presenterBannerInfo = presenter
        
        //Presenter will communicate with Interector and Viewcontroller
        presenter.viewControllerBannerInfo = viewController
        presenter.interactorBannerInfo = interactor
        
        //Interactor will communucate with only presenter.
        interactor.presenterBannerInfo = presenter
    }
    
    // MARK: View lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        self.viewOuter.isHidden = true

        btnContinueShopping.setTitle(ObjKeymessages.kLABEL_CONTINUE_SHOPPING, for: .normal)
        
        let image = UIImage(named: "share")?.withRenderingMode(.alwaysTemplate)
        btnShare.setImage(image, for: .normal)
        btnShare.tintColor = UIColor.AppTheme_BlueColor_012CDA
        
        if strFromWhichScreen == "FromDeepLink" {  // call API
            self.openBannerInfoPopup()
        }
        else {
            self.setData()
        }
        self.lblAdvertisingTitle.text = "Ad".localized
        delay(0.3) {
            self.updatePopupHeight(to: min(CGFloat((self.scrollView.contentSize.height) + 15 + (UIApplication.shared.windows.first?.safeAreaInsets.bottom ?? 0)), UIScreen.main.bounds.height * 0.95))
        }
    }
    
    func setData() {
        if objBanner?.isAppAdvertisement == 1 {
            self.viewAdvertising.isHidden = false
        } else {
            self.viewAdvertising.isHidden = true
        }
        imgBanner.kf.indicatorType = .activity
        let url = URL(string: objBanner?.vImage ?? "")
        imgBanner.kf.setImage(with: url , placeholder: UIImage(named: "placeholder_banner"))
        lblDescription.text = objBanner?.tBannerDescription
    }
    
    private func openBannerInfoPopup() {
        var dictParam : [String:Any] = [:]
        dictParam["iBannerId"] = bannerId

        if AppSingletonObj.isConnectedToNetwork(){
            self.presenterBannerInfo?.apiCallForGetBannerDetails(dictData: dictParam)
        }
    }

    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        if self.scrollView.contentSize.height > UIScreen.main.bounds.height * 0.7 {
            cons_viewOuter_height.constant = UIScreen.main.bounds.height * 0.7
        }
        else {
            cons_viewOuter_height.constant = scrollView.contentSize.height
        }
        self.viewOuter.isHidden = false
    }
    
    @IBAction func btnDimViewAction(_ sender: Any) {
        self.dismiss(animated: true, completion: nil)
    }

    @IBAction func ContinueShoppingAction(_ sender: Any) {
        self.dismiss(animated: true, completion: nil)
    }
    
    @IBAction func shareAction(_ sender: Any) {
        FirebaseDeeplinkingHelper.shared.generateFeedPlanLinkForBanner(strBannerId: "\(objBanner?._id ?? 0)", strTitle: objBanner?.vName ?? "", strDescription: objBanner?.tBannerDescription ?? "", strImageUrl: objBanner?.vImage ?? "") { url in
            let strMsg = "\(self.objBanner?.tBannerDescription ?? "") \(ObjKeymessages.kLABEL_SHARE_TEXT)\n\n\(url ?? "")"
            print(strMsg)
            self.shareTextInDefaultShareKit(array: [strMsg])
        }
    }

    func shareTextInDefaultShareKit(array:[Any],complation: (()->())? = nil) {
        let activityVC : UIActivityViewController = UIActivityViewController(activityItems: array, applicationActivities: nil)
        activityVC.completionWithItemsHandler = { (activityType,isCompleted,returnItems,error) in
            print("==>>> Contrll is dismiss")
            complation?()
        }
        self.presentVC(activityVC)
    }

    func displayAlert(string: String) {
        AppSingleton.shared.showCustomPopUpWithOkButton(strButtonTitle: ObjKeymessages.kLABEL_OK, strMessage: string, showOnTopVC: false) { (isOk) in
        }
    }

    func displayErrorAlert(strMsg:String) {
        AppSingleton.shared.showCustomPopUpWithOkButton(strButtonTitle: ObjKeymessages.kLABEL_OK, strMessage: strMsg, showOnTopVC: false) { (isOk) in
            if isOk == true {
                self.dismissVC(completion: nil)
            }
        }
    }

    func getBannerDetailsInfo(model: ListResponseFields) {
        objBanner = model
        self.setData()
    }

}
