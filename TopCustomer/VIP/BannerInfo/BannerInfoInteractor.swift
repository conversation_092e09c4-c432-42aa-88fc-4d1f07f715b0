
import UIKit

protocol BannerInfoInteractorProtocol {
    func apiCallForGetBannerDetails(dictData:[String:Any])

}

protocol BannerInfoDataStore {
    //{ get set }
}

class BannerInfoInteractor: BannerInfoInteractorProtocol, BannerInfoDataStore {

    // MARK: Objects & Variables
    var presenterBannerInfo: BannerInfoPresentationProtocol?
    
    func apiCallForGetBannerDetails(dictData:[String:Any]) {
        
        ActivityIndicator.shared.showCentralSpinner()
        
        let authorization = User.shared.checkUserLoginStatus() ? getAuthorizationText() : GuestAccessToken
        BannerAPI.bannerDetails(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, iBannerId: Int(setDataInString(dictData["iBannerId"] as AnyObject)) ?? 0) { data, error in
                ActivityIndicator.shared.hideCentralSpinner()
                self.presenterBannerInfo?.apiResponseGetBannerDetails(response: data, error: error)
        }
        
    }

}
