
import UIKit

protocol BannerInfoPresentationProtocol {
    func apiCallForGetBannerDetails(dictData:[String:Any])
    func apiResponseGetBannerDetails(response:BannerResponse?,error:Error?)

}

class BannerInfoPresenter: BannerInfoPresentationProtocol {
    
    // MARK: Objects & Variables
    weak var viewControllerBannerInfo: BannerInfoProtocol?
    var interactorBannerInfo: BannerInfoInteractorProtocol?
    
    func apiCallForGetBannerDetails(dictData:[String:Any]) {
        interactorBannerInfo?.apiCallForGetBannerDetails(dictData: dictData)
    }

    func apiResponseGetBannerDetails(response: BannerResponse?, error: Error?) {
        if let error = error  {
            viewControllerBannerInfo?.displayAlert(string: error.localizedDescription)
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            return
        }
        
        if code == APIINVALIDAUTHORIZATIONCODE401 {
            AppSingletonObj.displaySessionExpiredAlert(strMsg: response.responseMessage ?? "")
            return
        }

        if code == APICODE400 {
            viewControllerBannerInfo?.displayErrorAlert(strMsg: response.responseMessage ?? "")
            return
        }
        
        guard let model = response.responseData,code == APISUCCESSCODE200  else {
            return
        }
        self.viewControllerBannerInfo?.getBannerDetailsInfo(model: model)
    }

}
