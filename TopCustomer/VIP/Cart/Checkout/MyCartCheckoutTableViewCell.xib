<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="20037" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="20020"/>
        <capability name="Named colors" minToolsVersion="9.0"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="LoewNextArabic-Bold.ttf">
            <string>LoewNextArabic-Bold</string>
        </array>
        <array key="LoewNextArabic-Medium.ttf">
            <string>LoewNextArabic-Medium</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" rowHeight="170" id="KGk-i7-Jjw" customClass="MyCartCheckoutTableViewCell" customModule="TopCustomer" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="320" height="170"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="320" height="170"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="y9p-w6-d7r">
                        <rect key="frame" x="0.0" y="10" width="320" height="150"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="53u-SC-97W">
                                <rect key="frame" x="20" y="20" width="106" height="110"/>
                                <subviews>
                                    <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="XpM-89-2j3">
                                        <rect key="frame" x="0.0" y="110" width="106" height="0.0"/>
                                        <color key="backgroundColor" name="AppTheme_LightGrayOrderColor_#EFEFEF"/>
                                        <constraints>
                                            <constraint firstAttribute="height" id="4ad-v7-WtJ"/>
                                        </constraints>
                                    </view>
                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="Mpq-fQ-J6n">
                                        <rect key="frame" x="5" y="5" width="96" height="100"/>
                                    </imageView>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstItem="Mpq-fQ-J6n" firstAttribute="leading" secondItem="53u-SC-97W" secondAttribute="leading" constant="5" id="8mU-b1-FdV"/>
                                    <constraint firstAttribute="bottom" secondItem="XpM-89-2j3" secondAttribute="bottom" id="ENl-08-d3y"/>
                                    <constraint firstItem="Mpq-fQ-J6n" firstAttribute="top" secondItem="53u-SC-97W" secondAttribute="top" constant="5" id="OJG-9f-pee"/>
                                    <constraint firstItem="XpM-89-2j3" firstAttribute="leading" secondItem="53u-SC-97W" secondAttribute="leading" id="RCA-VV-vbl"/>
                                    <constraint firstItem="XpM-89-2j3" firstAttribute="top" secondItem="Mpq-fQ-J6n" secondAttribute="bottom" constant="5" id="UHz-1u-gpN"/>
                                    <constraint firstAttribute="trailing" secondItem="Mpq-fQ-J6n" secondAttribute="trailing" constant="5" id="XLb-J3-Fvu"/>
                                    <constraint firstAttribute="trailing" secondItem="XpM-89-2j3" secondAttribute="trailing" id="pP9-hS-rDS"/>
                                    <constraint firstAttribute="height" constant="110" id="qe0-X0-XRo"/>
                                    <constraint firstAttribute="width" constant="106" id="uNF-Lp-AK2"/>
                                </constraints>
                            </view>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Arwa Water" lineBreakMode="wordWrap" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="GdU-bR-jMq" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                <rect key="frame" x="141" y="30" width="87.5" height="12"/>
                                <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="12"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="330 ml X48" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Vl2-bf-VSs" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                <rect key="frame" x="141" y="47" width="80.5" height="12"/>
                                <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="12"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="252" verticalHuggingPriority="251" text="SKU19910145" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="c9I-T4-eLD" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                <rect key="frame" x="141" y="64" width="80.5" height="12"/>
                                <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="12"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Quantity : 1" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="YW6-Uc-a00" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                <rect key="frame" x="141" y="100" width="169" height="12"/>
                                <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="12"/>
                                <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="fillEqually" spacing="3" translatesAutoresizingMaskIntoConstraints="NO" id="cac-7A-msS">
                                <rect key="frame" x="236.5" y="22.5" width="73.5" height="27"/>
                                <subviews>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="18.00 SAR" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="0CS-P6-LsP" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                        <rect key="frame" x="0.0" y="0.0" width="73.5" height="12"/>
                                        <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="12"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="18.00 SAR" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="vFV-jJ-64v" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                        <rect key="frame" x="0.0" y="15" width="73.5" height="12"/>
                                        <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="12"/>
                                        <color key="textColor" name="AppTheme_SubLabelLightGrayColor_#B2B2B2"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                            </stackView>
                        </subviews>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstAttribute="trailing" secondItem="YW6-Uc-a00" secondAttribute="trailing" constant="10" id="1rS-cE-PUf"/>
                            <constraint firstItem="GdU-bR-jMq" firstAttribute="leading" secondItem="Vl2-bf-VSs" secondAttribute="leading" id="92Q-2r-0Rx"/>
                            <constraint firstItem="c9I-T4-eLD" firstAttribute="trailing" secondItem="Vl2-bf-VSs" secondAttribute="trailing" id="B1R-IR-dwu"/>
                            <constraint firstItem="Vl2-bf-VSs" firstAttribute="leading" secondItem="53u-SC-97W" secondAttribute="trailing" constant="15" id="BL6-PH-u9f"/>
                            <constraint firstAttribute="bottom" secondItem="53u-SC-97W" secondAttribute="bottom" constant="20" id="JyB-2S-XG1"/>
                            <constraint firstItem="GdU-bR-jMq" firstAttribute="top" secondItem="53u-SC-97W" secondAttribute="top" constant="10" id="LZ9-Oa-7zy"/>
                            <constraint firstItem="c9I-T4-eLD" firstAttribute="top" secondItem="Vl2-bf-VSs" secondAttribute="bottom" constant="5" id="MTs-bq-xOC"/>
                            <constraint firstItem="53u-SC-97W" firstAttribute="top" secondItem="y9p-w6-d7r" secondAttribute="top" constant="20" id="OtO-TZ-oJI"/>
                            <constraint firstItem="c9I-T4-eLD" firstAttribute="leading" secondItem="Vl2-bf-VSs" secondAttribute="leading" id="QMB-fn-Mld"/>
                            <constraint firstItem="cac-7A-msS" firstAttribute="leading" secondItem="GdU-bR-jMq" secondAttribute="trailing" constant="8" id="UvS-Qo-Hf6"/>
                            <constraint firstItem="cac-7A-msS" firstAttribute="leading" secondItem="Vl2-bf-VSs" secondAttribute="trailing" constant="15" id="YgT-Y9-Lmb"/>
                            <constraint firstItem="YW6-Uc-a00" firstAttribute="top" secondItem="c9I-T4-eLD" secondAttribute="bottom" constant="24" id="bu4-ul-tA5"/>
                            <constraint firstItem="cac-7A-msS" firstAttribute="centerY" secondItem="GdU-bR-jMq" secondAttribute="centerY" id="cvw-L6-EJx"/>
                            <constraint firstItem="YW6-Uc-a00" firstAttribute="leading" secondItem="Vl2-bf-VSs" secondAttribute="leading" id="dGX-am-fX7"/>
                            <constraint firstAttribute="trailing" secondItem="cac-7A-msS" secondAttribute="trailing" constant="10" id="ebc-fF-9eO"/>
                            <constraint firstItem="53u-SC-97W" firstAttribute="leading" secondItem="y9p-w6-d7r" secondAttribute="leading" constant="20" id="gwN-kd-gtj"/>
                            <constraint firstItem="Vl2-bf-VSs" firstAttribute="top" secondItem="GdU-bR-jMq" secondAttribute="bottom" constant="5" id="h9f-kj-V8M"/>
                        </constraints>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="boolean" keyPath="isViewShadow" value="YES"/>
                            <userDefinedRuntimeAttribute type="size" keyPath="shadowOffset">
                                <size key="value" width="0.0" height="3"/>
                            </userDefinedRuntimeAttribute>
                            <userDefinedRuntimeAttribute type="number" keyPath="shadowBlur">
                                <real key="value" value="10"/>
                            </userDefinedRuntimeAttribute>
                            <userDefinedRuntimeAttribute type="number" keyPath="shadowOpacity">
                                <real key="value" value="0.5"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </view>
                </subviews>
                <constraints>
                    <constraint firstItem="y9p-w6-d7r" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" constant="10" id="D2Y-WX-sYJ"/>
                    <constraint firstAttribute="trailing" secondItem="y9p-w6-d7r" secondAttribute="trailing" id="bcH-Vu-8tb"/>
                    <constraint firstAttribute="bottom" secondItem="y9p-w6-d7r" secondAttribute="bottom" constant="10" id="thp-Cv-0B6"/>
                    <constraint firstItem="y9p-w6-d7r" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" id="z8Y-2T-UYh"/>
                </constraints>
            </tableViewCellContentView>
            <viewLayoutGuide key="safeArea" id="njF-e1-oar"/>
            <connections>
                <outlet property="imgProduct" destination="Mpq-fQ-J6n" id="hOn-df-nPk"/>
                <outlet property="lblOldPrice" destination="vFV-jJ-64v" id="i0z-JC-Ukb"/>
                <outlet property="lblPrice" destination="0CS-P6-LsP" id="A89-XN-8O2"/>
                <outlet property="lblProductName" destination="GdU-bR-jMq" id="SCQ-MI-zHC"/>
                <outlet property="lblProductSkuValue" destination="c9I-T4-eLD" id="OyN-iC-yA8"/>
                <outlet property="lblQuantity" destination="YW6-Uc-a00" id="wSN-w9-WaQ"/>
                <outlet property="lblQuantityUnit" destination="Vl2-bf-VSs" id="lAy-9R-ag5"/>
            </connections>
            <point key="canvasLocation" x="118.84057971014494" y="124.55357142857142"/>
        </tableViewCell>
    </objects>
    <resources>
        <namedColor name="AppTheme_LightGrayOrderColor_#EFEFEF">
            <color red="0.93725490196078431" green="0.93725490196078431" blue="0.93725490196078431" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="AppTheme_SubLabelLightGrayColor_#B2B2B2">
            <color red="0.69803921568627447" green="0.69803921568627447" blue="0.69803921568627447" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
