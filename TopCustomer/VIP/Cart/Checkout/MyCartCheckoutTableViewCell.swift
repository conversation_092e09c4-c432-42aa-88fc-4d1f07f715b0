
import UIKit

class MyCartCheckoutTableViewCell: UITableViewCell {

    //MARK: - Outlets
    
    @IBOutlet weak var lblProductSkuTitle: UILabel!
    @IBOutlet weak var lblPrice: UILabel!
    @IBOutlet weak var lblProductName: UILabel!
    @IBOutlet weak var imgProduct: UIImageView!
    @IBOutlet weak var lblQuantityUnit: UILabel!
    @IBOutlet weak var lblProductSkuValue: UILabel!
    @IBOutlet weak var lblQuantity: UILabel!
    @IBOutlet weak var lblOldPrice: UILabel!    
    
    
    override func awakeFromNib() {
        super.awakeFromNib()
        // Initialization code
        
        let attributeString: NSMutableAttributedString = NSMutableAttributedString(string: lblPrice.text ?? "")
        attributeString.addAttribute(NSAttributedString.Key.strikethroughStyle, value: NSUnderlineStyle.single.rawValue, range: NSRange(location: 0, length: lblPrice.text?.length ?? 0))
        lblOldPrice.attributedText = attributeString

    }

    override func setSelected(_ selected: Bo<PERSON>, animated: Bool) {
        super.setSelected(selected, animated: animated)

        // Configure the view for the selected state
    }
    
}
