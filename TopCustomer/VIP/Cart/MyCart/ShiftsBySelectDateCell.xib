<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="23504" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="23506"/>
        <capability name="Named colors" minToolsVersion="9.0"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="TimesNewRoman.ttf">
            <string>.SFUI-Regular</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" reuseIdentifier="ShiftsBySelectDateCell" id="gTV-IL-0wX" customClass="ShiftsBySelectDateCell" customModule="TopCustomer" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="187" height="100"/>
            <autoresizingMask key="autoresizingMask"/>
            <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center">
                <rect key="frame" x="0.0" y="0.0" width="187" height="100"/>
                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="rNC-2v-Mmf">
                        <rect key="frame" x="0.0" y="0.0" width="187" height="100"/>
                        <subviews>
                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="center" spacing="5" translatesAutoresizingMaskIntoConstraints="NO" id="NJu-o1-UfP">
                                <rect key="frame" x="5" y="22.666666666666671" width="177" height="55"/>
                                <subviews>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Title" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="kPz-gO-2v4" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                        <rect key="frame" x="73.666666666666671" y="0.0" width="29.666666666666671" height="16.333333333333332"/>
                                        <fontDescription key="fontDescription" name="ArialRoundedMTBold" family="Arial Rounded MT Bold" pointSize="14"/>
                                        <color key="textColor" name="AppTheme_DiffBlackColor_#101113"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="From 9 AM to 5 PM." lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="WfC-J7-ham" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                        <rect key="frame" x="42" y="21.333333333333332" width="93" height="12.333333333333332"/>
                                        <fontDescription key="fontDescription" name=".SFUI-Regular" family=".AppleSystemUIFont" pointSize="11"/>
                                        <color key="textColor" name="AppTheme_DiffBlackColor_#101113"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="available" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="tSx-Jp-CBt" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                        <rect key="frame" x="58.333333333333329" y="38.666666666666671" width="60.333333333333329" height="16.333333333333329"/>
                                        <fontDescription key="fontDescription" name="ArialRoundedMTBold" family="Arial Rounded MT Bold" pointSize="14"/>
                                        <color key="textColor" name="AppTheme_DiscountGreenColor_#05B13E"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                            </stackView>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="daM-Un-xyS">
                                <rect key="frame" x="0.0" y="0.0" width="187" height="100"/>
                                <state key="normal" title="Button"/>
                                <buttonConfiguration key="configuration" style="plain" title=" "/>
                            </button>
                        </subviews>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstAttribute="trailing" secondItem="daM-Un-xyS" secondAttribute="trailing" id="E6e-gu-vAw"/>
                            <constraint firstItem="NJu-o1-UfP" firstAttribute="centerX" secondItem="rNC-2v-Mmf" secondAttribute="centerX" id="IO1-o4-YA1"/>
                            <constraint firstAttribute="trailing" secondItem="NJu-o1-UfP" secondAttribute="trailing" constant="5" id="UBk-Sk-WlQ"/>
                            <constraint firstItem="NJu-o1-UfP" firstAttribute="centerY" secondItem="rNC-2v-Mmf" secondAttribute="centerY" id="Vwt-pM-mEO"/>
                            <constraint firstItem="daM-Un-xyS" firstAttribute="top" secondItem="rNC-2v-Mmf" secondAttribute="top" id="Y5T-qX-eN0"/>
                            <constraint firstItem="daM-Un-xyS" firstAttribute="leading" secondItem="rNC-2v-Mmf" secondAttribute="leading" id="cIM-HB-XLf"/>
                            <constraint firstItem="NJu-o1-UfP" firstAttribute="leading" secondItem="rNC-2v-Mmf" secondAttribute="leading" constant="5" id="cR1-dj-Coa"/>
                            <constraint firstAttribute="bottom" secondItem="daM-Un-xyS" secondAttribute="bottom" id="yc3-Eh-ge3"/>
                        </constraints>
                    </view>
                </subviews>
            </view>
            <viewLayoutGuide key="safeArea" id="ZTg-uK-7eu"/>
            <constraints>
                <constraint firstItem="rNC-2v-Mmf" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" id="NjD-HF-acu"/>
                <constraint firstItem="rNC-2v-Mmf" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" id="fqr-2j-DGX"/>
                <constraint firstAttribute="bottom" secondItem="rNC-2v-Mmf" secondAttribute="bottom" id="qSi-XY-vCb"/>
                <constraint firstAttribute="trailing" secondItem="rNC-2v-Mmf" secondAttribute="trailing" id="spl-NE-1OG"/>
            </constraints>
            <connections>
                <outlet property="btnCheckoutShift" destination="daM-Un-xyS" id="rdP-Of-9DU"/>
                <outlet property="lblShiftAvailable" destination="tSx-Jp-CBt" id="3WL-ts-IUL"/>
                <outlet property="lblShiftTime" destination="WfC-J7-ham" id="XaU-g9-YJ8"/>
                <outlet property="lblShiftTitle" destination="kPz-gO-2v4" id="bzS-M3-wOZ"/>
            </connections>
            <point key="canvasLocation" x="-40" y="20"/>
        </collectionViewCell>
    </objects>
    <resources>
        <namedColor name="AppTheme_DiffBlackColor_#101113">
            <color red="0.062745098039215685" green="0.066666666666666666" blue="0.074509803921568626" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="AppTheme_DiscountGreenColor_#05B13E">
            <color red="0.019607843137254902" green="0.69411764705882351" blue="0.24313725490196078" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
