
import UIKit

protocol CheckoutInteractorProtocol {
    func apiCallForGetCart(dictData:[String:Any])
    func apiCallForCreateOrder(dictData:[String:Any], transactionType: Int, flagCardOrNot: Bool, strCardToken: String, strCardTransactionReference: String, flagSplitWallet: Bool, strRemainingAmt: String, isreward: Bool, rewardamount: Double, isCartIncludeBundle: Int, expectedTime: Date?)
    func apiCallForUpdateTransaction(dictData:[String:Any], paymentStatus: Int, strCardNumber: String, strCardType: String, strCardScheme: String, strCardToken: String, walletUsedOrNot: Int)
    func apiCallForAddCard(dictData:[String:Any])
    func apiCallForApplyPromoCode(dictData:[String:Any])
    func apiCallForRemovePromoCode(dictData:[String:Any])
    func apiCallForGetCountryList(flagNoNetwork: Bool)
    func apiCallForCancelOrder(dictData:[String:Any])
    func apiCallForAddToCart(dictData:[String:Any])
    func apiCallForCalculateUserPointsToSAR(points: Int)
    func apiCallForCalculateUserAmountToPoints(amount: Double, type: ConvertSARToPointsType)
    func apiCallForGetShiftsByDate(dictData:[String:Any])
}

protocol CheckoutDataStore {
    //{ get set }
}

class CheckoutInteractor: CheckoutInteractorProtocol, CheckoutDataStore {

    // MARK: Objects & Variables
    var presenterCheckout: CheckoutPresentationProtocol?
    
    func apiCallForGetCart(dictData:[String:Any]) {
        
        ActivityIndicator.shared.showCentralSpinner()
        
        let authorization = getAuthorizationText()
        
        CartAPI.getCart(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, offset: Int(setDataInString(dictData["offset"] as AnyObject)) ?? 0, shiftDate: String(setDataInString(dictData["shiftDate"] as AnyObject))) { data, error in
            ActivityIndicator.shared.hideCentralSpinner()
            self.presenterCheckout?.apiResponseGetCart(response: data, error: error)
        }
        
    }

    func apiCallForCreateOrder(dictData:[String:Any], transactionType: Int, flagCardOrNot: Bool, strCardToken: String, strCardTransactionReference: String, flagSplitWallet: Bool, strRemainingAmt: String, isreward: Bool, rewardamount: Double, isCartIncludeBundle: Int, expectedTime: Date? = nil) {
        ActivityIndicator.shared.showCentralSpinner()
        
        let authorization = getAuthorizationText()

        var reccuringType = OrderAPI.IReccuringType_createOrder(rawValue: ReccuringType.OnlyOnce.rawValue)
        if setDataInString(dictData["iReccuringType"] as AnyObject) == ReccuringType.OnlyOnce.rawValue {
            reccuringType = OrderAPI.IReccuringType_createOrder._1
        }
        else if setDataInString(dictData["iReccuringType"] as AnyObject) == ReccuringType.EveryWeek.rawValue {
            reccuringType = OrderAPI.IReccuringType_createOrder._2
        }
        else if setDataInString(dictData["iReccuringType"] as AnyObject) == ReccuringType.Every2Weeks.rawValue {
            reccuringType = OrderAPI.IReccuringType_createOrder._3
        }
        else if setDataInString(dictData["iReccuringType"] as AnyObject) == ReccuringType.EveryMonth.rawValue {
            reccuringType = OrderAPI.IReccuringType_createOrder._4
        }

        OrderAPI.createOrder(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, iCartId: Int(setDataInString(dictData["iCartId"] as AnyObject)) ?? 0, iAddressId: Int(setDataInString(dictData["iAddressId"] as AnyObject)) ?? 0, iReccuringType: reccuringType, tAdditionalNote: setDataInString(dictData["tAdditionalNote"] as AnyObject), iPromocodeUseId: Int(setDataInString(dictData["iPromocodeUseId"] as AnyObject)) ?? 0, dOrderTotal: setDataInString(dictData["dOrderTotal"] as AnyObject), dVatCost: setDataInString(dictData["dVatCost"] as AnyObject), tiShiftType: Int(setDataInString(dictData["tiShiftType"] as AnyObject)) ?? 0, vAlternativeISDCode: setDataInString(dictData["vAlternativeISDCode"] as AnyObject), vAlternativeMobileNumber: setDataInString(dictData["vAlternativeMobileNumber"] as AnyObject), iShiftId: Int(setDataInString(dictData["iShiftId"] as AnyObject)) ?? 0, vShiftDisplayNameEn: setDataInString(dictData["vShiftDisplayNameEn"] as AnyObject), vShiftDisplayNameAr: setDataInString(dictData["vShiftDisplayNameAr"] as AnyObject), vStartAt: setDataInString(dictData["vStartAt"] as AnyObject), vCloseAt: setDataInString(dictData["vCloseAt"] as AnyObject), tiIsFriday: Int(setDataInString(dictData["tiIsFriday"] as AnyObject)) ?? 0, tiIsRamadan: Int(setDataInString(dictData["tiIsRamadan"] as AnyObject)) ?? 0, tiIsRegularShift: Int(setDataInString(dictData["tiIsRegularShift"] as AnyObject)) ?? 0, timezone: localTimeZoneIdentifier, isreward: isreward, rewardamount: rewardamount, isMosques: setDataInString(dictData["isMosques"] as AnyObject), isCartIncludeBundle: isCartIncludeBundle, expectedTime: expectedTime) { data, error in
                ActivityIndicator.shared.hideCentralSpinner()
            self.presenterCheckout?.apiResponseCreateOrder(response: data, error: error, transactionType: transactionType, flagCardOrNot: flagCardOrNot, strCardToken: strCardToken, strCardTransactionReference: strCardTransactionReference, flagSplitWallet: flagSplitWallet, strRemainingAmt: strRemainingAmt)
        }
        
    }

    func apiCallForUpdateTransaction(dictData:[String:Any], paymentStatus: Int, strCardNumber: String, strCardType: String, strCardScheme: String, strCardToken: String, walletUsedOrNot: Int) {
        
        DispatchQueue.main.async {
            ActivityIndicator.shared.showCentralSpinner()
        }
        
        let authorization = getAuthorizationText()
        
        OrderAPI.orderTransactionUpdate(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, iOrderId: Int(setDataInString(dictData["iOrderId"] as AnyObject)) ?? 0, tiTransactionType: Int(setDataInString(dictData["tiTransactionType"] as AnyObject)) ?? 0, vTransactionRef: setDataInString(dictData["vTransactionRef"] as AnyObject), iPaymentStatus: Int(setDataInString(dictData["iPaymentStatus"] as AnyObject)) ?? 0, iUseWallet: walletUsedOrNot, vCardName: setDataInString(dictData["vCardName"] as AnyObject)) { data, error in
            ActivityIndicator.shared.hideCentralSpinner()
            self.presenterCheckout?.apiResponseUpdateTransaction(response: data, error: error, paymentStatus: paymentStatus, strCardNumber: strCardNumber, strCardType: strCardType, strCardScheme: strCardScheme, strCardToken: strCardToken, strTransactionId: setDataInString(dictData["vTransactionRef"] as AnyObject))
        }
                
    }

    func apiCallForAddCard(dictData:[String:Any]) {
        let authorization = getAuthorizationText()

        UserCardsAPI.addUserCards(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, vToken: setDataInString(dictData["vToken"] as AnyObject), vTransactionId: setDataInString(dictData["vTransactionId"] as AnyObject), vPaymentDescription: setDataInString(dictData["vPaymentDescription"] as AnyObject), vCardType: setDataInString(dictData["vCardType"] as AnyObject), vCardScheme: setDataInString(dictData["vCardScheme"] as AnyObject)) { data, error in
            
            self.presenterCheckout?.apiResponseAddCard(response: data, error: error)

        }
    }

    func apiCallForApplyPromoCode(dictData:[String:Any]) {
        ActivityIndicator.shared.showCentralSpinner()
        
        let authorization = getAuthorizationText()

        PromocodeAPI.promocodeCheck(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, vPromocode: setDataInString(dictData["vPromocode"] as AnyObject), dSubTotal: setDataInString(dictData["dSubTotal"] as AnyObject), dDistanceCost: setDataInString(dictData["dDistanceCost"] as AnyObject)) { data, error in
                ActivityIndicator.shared.hideCentralSpinner()
                self.presenterCheckout?.apiResponseApplyPromoCode(response: data, error: error)

        }
        
    }

    func apiCallForRemovePromoCode(dictData:[String:Any]) {
        ActivityIndicator.shared.showCentralSpinner()
        
        let authorization = getAuthorizationText()

        PromocodeAPI.removePromocode(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, iPromocodeUseId: Int(setDataInString(dictData["iPromocodeUseId"] as AnyObject)) ?? 0) { data, error in
            ActivityIndicator.shared.hideCentralSpinner()
            self.presenterCheckout?.apiResponseRemovePromoCode(response: data, error: error)
        }
        
    }

    func apiCallForGetCountryList(flagNoNetwork: Bool) {

        ActivityIndicator.shared.showCentralSpinner()
        
        CountryAPI.countryListing(accept: AcceptParamForHeader, lang: CurrentAppLang) { data, error in
            ActivityIndicator.shared.hideCentralSpinner()
            self.presenterCheckout?.apiResponseGetCountryList(response: data, error: error, flagNoNetwork: flagNoNetwork)

        }
        
    }

    func apiCallForCancelOrder(dictData:[String:Any]) {
        
        ActivityIndicator.shared.showCentralSpinner()
        
        let authorization = getAuthorizationText()
                
        OrderAPI.deleteOrder(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, iOrderId: Int(setDataInString(dictData["iOrderId"] as AnyObject)) ?? 0) { data, error in
            ActivityIndicator.shared.hideCentralSpinner()
            self.presenterCheckout?.apiResponseCancelOrder(response: data, error: error)
        }
        
    }

    func apiCallForAddToCart(dictData:[String:Any]) {
        
        let authorization = getAuthorizationText()
        
        CartAPI.addToCart(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, biProductId: Int(setDataInString(dictData["biProductId"] as AnyObject)) ?? 0, tiIsCheck: Int(setDataInString(dictData["tiIsCheck"] as AnyObject)) ?? 0, iProductQuantity: Int(setDataInString(dictData["iProductQuantity"] as AnyObject)) ?? 0, dbPrice: setDataInString(dictData["dbPrice"] as AnyObject), isMosques: setDataInString(dictData["isMosques"] as AnyObject), dbBundleID: Int(setDataInString(dictData["bunddelId"] as AnyObject)) ?? 0) { data, error in
            
            self.presenterCheckout?.apiResponseAddToCart(response: data, error: error)
        }
        
    }
    
    func apiCallForCalculateUserPointsToSAR(points: Int) {
        UserAPI.calculateUserPointsToSAR(points: points) { data, error in
            self.presenterCheckout?.apiResponseForCallForCalculateUserPointsToSAR(response: data, error: error)
        }
    }

    func apiCallForCalculateUserAmountToPoints(amount: Double, type: ConvertSARToPointsType) {
        UserAPI.calculateUserAmountToPoints(amount: amount, type: type) { data, error in
            self.presenterCheckout?.apiResponseForCallForCalculateUserAmountToPoints(response: data, error: error)
        }
    }
    
    func apiCallForGetShiftsByDate(dictData:[String:Any]) {
        
//        ActivityIndicator.shared.showCentralSpinner()
        
        let authorization = getAuthorizationText()
        
        CartAPI.getCart(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, offset: Int(setDataInString(dictData["offset"] as AnyObject)) ?? 0, shiftDate: String(setDataInString(dictData["shiftDate"] as AnyObject))) { data, error in
            ActivityIndicator.shared.hideCentralSpinner()
            self.presenterCheckout?.apiResponseGetShiftsByDate(response: data, error: error)
        }
        
    }
    
}
