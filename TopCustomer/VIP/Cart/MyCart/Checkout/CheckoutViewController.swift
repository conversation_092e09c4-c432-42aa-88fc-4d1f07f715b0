
import UIKit
import IQKeyboardManagerSwift
import PaymentSDK
import <PERSON>Tabbar
import Mixpanel
import SwiftUI
import <PERSON>bby
import T<PERSON>TokBusinessSDK
import FirebaseAnalytics

enum CalculateInvoiceType {
    case normal
    case fixedDiscountValue(value: Double)
    case freeDeliveryPromocode
    case percentPromocode(discountAmount: Double)
    case freeDeliveryWithPercentPromocode(discountAmount: Double)
    case coinsLessThanSubtotal
    case coinsMoreThanSubtotal
}

protocol CheckoutProtocol: AnyObject {
    func refreshCart(model : GetCartListResponseFields)
    func displayAlert(string:String)
    func makePaymentToPayTabs(model: CreateOrderResponseFields, transactionType: Int, flagCardOrNot: Bool, strCardToken: String, strCardTransactionReference: String, flagSplitWallet: Bool, strRemainingAmt: String)
    func displayErrorAlert(strMsg:String)
    func showThankyouAlert(strCardNumber: String, strCardType: String, strCardScheme: String, strCardToken: String, strTransactionId: String, strLatestWalletBalance: String)
    func displayErrorAlertAndGoToHome(strMsg:String)
    func promoCodeSuccess(model: PromocodeCheckResponseFields, strMsg: String)
    func removePromoCodeSuccess(strMsg: String)
    func refreshCountryData(arrCountryData : [CountryListResponseFields], flagNoNetwork: Bool)
    func displayAlertForFailedCaseAndGoToCurrentOrders(strMsg: String)
    func showAlertAndReload(strMessage :  String)
    func displayAlertAndDismiss(string:String, cartCount: Int)
    func notifyToLeaveDispatchGroup()
    func displayErrorToast(strMsg:String)
    func getCalculateUserPointsToSAR(model: CalculateUserPointsToSARResponseFields)
    func getCalculateUserAmountToPoints(model: convertSARToPointsResponseFields)
    func getShiftsBySelectDate(shifts: [GetCartShiftResponseFields]?)
    func startPaymentWithTabbyByURL(url: String)
}

class CheckoutViewController: BaseViewController {

    // MARK: Objects & Variables
    var presenterCheckout: CheckoutPresentationProtocol?

    // MARK: IBOutlets
    @IBOutlet weak var tblCheckout: ContentSizedTableView!
    @IBOutlet weak var lblTitle: UILabel!
    @IBOutlet weak var lblDeliveryAddressTitle: UILabel!
    @IBOutlet weak var btnChangeDeliveryAddress: UIButton!
    @IBOutlet weak var lblScheduleOrderTitle: UILabel!
    @IBOutlet weak var btnOnlyOnce: UIButton!
    @IBOutlet weak var btnEveryWeek: UIButton!
    @IBOutlet weak var btnEvery2Weeks: UIButton!
    @IBOutlet weak var btnEveryMonth: UIButton!
    @IBOutlet weak var lblNotesTitle: UILabel!
    @IBOutlet weak var textViewNotes: IQTextView!
    @IBOutlet weak var lblPromoCodeTitle: UILabel!
    @IBOutlet weak var btnApply: UIButton!
    @IBOutlet weak var lblSubTotalTitle: UILabel!
    @IBOutlet weak var lblSubTotalValue: UILabel!
    @IBOutlet weak var lblDeliveryTitle: UILabel!
    @IBOutlet weak var lblDeliveryValue: UILabel!
    @IBOutlet weak var lblVATTitle: UILabel!
    @IBOutlet weak var lblVATValue: UILabel!
    @IBOutlet weak var lblVATIncludes: UILabel!
    @IBOutlet weak var lblTotalTitle: UILabel!
    @IBOutlet weak var lblTotalValue: UILabel!
    @IBOutlet weak var lblVATRegNo: UILabel!
    @IBOutlet weak var btnPayNow: UIButton!
    @IBOutlet weak var lblType: UILabel!
    @IBOutlet weak var lblAddress: UILabel!    
    @IBOutlet weak var lblDeliveryShiftTitle: UILabel!
    @IBOutlet weak var txtPromoCode: UITextField!
    @IBOutlet weak var lblDiscountTitle: UILabel!
    @IBOutlet weak var lblDiscountValue: UILabel!
    @IBOutlet weak var stackViewDiscount: UIStackView!
    @IBOutlet weak var lblDeliveryDiscountTitle: MaterialLocalizeLable!
    @IBOutlet weak var lblDeliveryDiscountValue: MaterialLocalizeLable!
    @IBOutlet weak var stackViewDeliveryDiscount: UIStackView!
    @IBOutlet weak var btnCountryCode: UIButton!
    @IBOutlet weak var txtPhoneNumber: CustomTextfieldWithFontStyle!
    @IBOutlet weak var lblDriverWillCall: MaterialLocalizeLable!
    @IBOutlet weak var lblUponArrival: MaterialLocalizeLable!
    @IBOutlet weak var collectionViewShifts: UICollectionView!
    @IBOutlet weak var viewCongrats: UIView!
    @IBOutlet weak var lblCongrats: MaterialLocalizeLable!
    @IBOutlet weak var lblCongratsValue: MaterialLocalizeLable!
    @IBOutlet weak var stackViewDiscountUsingPoints: UIStackView!
    @IBOutlet weak var btnDiscountUsingPoints: UIButton!
    @IBOutlet weak var btnDiscountUsingPointsTwo: UIButton!
    @IBOutlet weak var stackViewPayWithPoints: UIStackView!
    @IBOutlet weak var btnPayWithPoints: UIButton!
    @IBOutlet weak var viewInfoPoints: UIView!
    @IBOutlet weak var viewMain: UIView!
    @IBOutlet weak var visualEffectViewInfoPoints: UIVisualEffectView!
    @IBOutlet weak var lblUsePoints: MaterialLocalizeLable!
    @IBOutlet weak var tblRewardDetails: UITableView!
    @IBOutlet weak var stackCoinsDiscount: UIStackView!
    @IBOutlet weak var lblCoinsDiscount: MaterialLocalizeLable!
    @IBOutlet weak var lblCoinsDiscountHint: MaterialLocalizeLable!
    @IBOutlet weak var lblCoinsDiscountValue: MaterialLocalizeLable!
    @IBOutlet weak var imgMosqueIcon: UIImageView!
    @IBOutlet weak var LblDeliveryShiftTitleSelected: MaterialLocalizeLable!
    @IBOutlet weak var BtnChangeDeliveryShift: UIButton!
    @IBOutlet weak var collectionViewShiftsByDate: UICollectionView!
    @IBOutlet weak var constraintShecduleShift: NSLayoutConstraint!
    
    // MARK: - Variables
    var arrCart: [GetCartResponseFields] = []
    var arrBundles: [BundelsListResponseFields] = []
    var subTotal = 0.0
    var addressId = 0
    var grandTotal = ""
    var coinsDiscountValue = 0.0
    var isreward = false
    var rewardAmount = 0.0
    var isFirstOpen = true
    var arrShifts: [GetCartShiftResponseFields] = []
    var selectedShiftIndex = -1
    private var totalCount: Int = 0
    private var offset: Int = 1
    var orderId = 0
    var dictParam : [String:Any] = [:]
    var strDistanceCost = ""
    var iPromocodeUseId = 0
    var objCartData: GetCartListResponseFields?
    var objPromoCodeData: PromocodeCheckResponseFields?
    var splitPaymentOrNot = 0
    var orderAmount = 0.0
    var orderNumber = ""
    var arrCountry: [CountryListResponseFields] = []
    var group: DispatchGroup?
    var bundleTotalValue = 0.0
    var isCartIncludeBundle = 0
    var vatPercentage = 0.0
    var vatCostValue = 0.0
    var vatCostFixedReturnValue = 0.0
    var deliveryCharageVatValue = 0.0
    var dSubTotalWithoutVAT = 0.0
    var dDistanceCost = 0.0
    var dDiscountAmount = 0.0
    var dVatPercentage = 0.0
    var dSubTotal = 0.0
    var dVatCost = 0.0
    var isCartIncludedItemsOfMosques = false
    var isMosqueAddress = false
    var newdVatCost = 0.0
    var isUsedCoins = false
    var deliveryShiftSelectedDate = ""
    var deliveryShiftSelectedDateIndex: Int?
    var deliverySelectedDate: Date?
    var arrShiftsBySelectDate: [GetCartShiftResponseFields] = []
    var selectedShiftBySelectedDateIndex = -1
    // tabby Payment
    let tabbyPay = TabyPaymentManager()
    var isTodayAvailable: Bool = false
    // MARK: Object lifecycle
    /*
    override init(nibName nibNameOrNil: String?, bundle nibBundleOrNil: Bundle?) {
        super.init(nibName: nibNameOrNil, bundle: nibBundleOrNil)
        setup()
    }
    */
    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
        setup()
    }
    
    // MARK: Setup
    
    private func setup() {
        let viewController = self
        let interactor = CheckoutInteractor()
        let presenter = CheckoutPresenter()
        
        //View Controller will communicate with only presenter
        viewController.presenterCheckout = presenter
        
        //Presenter will communicate with Interector and Viewcontroller
        presenter.viewControllerCheckout = viewController
        presenter.interactorCheckout = interactor
        
        //Interactor will communucate with only presenter.
        interactor.presenterCheckout = presenter
        self.tabbyPay.vc = self
        self.tabbyPay.delegate = self
    }
    
    
    // MARK: View lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        self.setTexts()
        self.setupUI()

        btnCountryCode.setTitle(User.shared.vISDCode, for: .normal)
        txtPhoneNumber.text = User.shared.vMobileNumber

        self.setCurrentAddress()

        self.tblCheckout.registerCell(cell: MyCartCheckoutTableViewCell.self)
        self.onlyOnceAction(UIButton())

        self.getNewProducts()
        self.getCountryList(flagNoNetwork: false)
        let tap = UITapGestureRecognizer(target: self, action: #selector(self.handleTap(_:)))
        self.view.isUserInteractionEnabled = true
        self.view.addGestureRecognizer(tap)
        self.isShowShiftsSelectedDate(isCollectionViewHidden: true)
    }
    
    func setupUI() {
        self.collectionViewShifts.registerCell(cell: CheckoutShiftsCell.self)
        self.collectionViewShifts.delegate = self
        self.collectionViewShifts.dataSource = self
        
        self.collectionViewShiftsByDate.registerCell(cell: ShiftsBySelectDateCell.self)
        self.collectionViewShiftsByDate.delegate = self
        self.collectionViewShiftsByDate.dataSource = self
        
        tblRewardDetails.delegate = self
        tblRewardDetails.dataSource = self
        let rewardDetailsCell = UINib(nibName: "RewardDetailsCell", bundle: Bundle.main)
        tblRewardDetails.register(rewardDetailsCell, forCellReuseIdentifier: "RewardDetailsCell")
        self.tblRewardDetails.separatorStyle = .none
        self.tblRewardDetails.reloadData()
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        self.presenterCheckout?.apiCalculateUserPointsToSAR(points: 0)
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            self.presenterCheckout?.apicalculateUserAmountToPoints(amount: forTrailingZero(temp: self.grandTotal.toDouble() ?? 0.0).toDouble() ?? 0.0, type: .getCoins)
        }
    }
    
    private func getCountryList(flagNoNetwork: Bool) {
        if AppSingletonObj.isConnectedToNetworkForCheckoutScreen(){
            self.presenterCheckout?.apiCallForGetCountryList(flagNoNetwork: flagNoNetwork)
        }
    }

    @objc func handleTap(_ sender: UITapGestureRecognizer? = nil) {
        self.viewInfoPoints.isHidden = true
        self.visualEffectViewInfoPoints.isHidden = true
    }
    
    private func setCurrentAddress() {
        if let address = UserDefaults.standard.object(forKey: USERDEFAULTS_INFO_KEY.CURRENT_ADDRESS) as? [String:Any],let data = address.data {
            let decodeResult: (decodableObj: AddressResponseFields?, error: Error?) = CodableHelper.decode(AddressResponseFields.self, from: data)
            if let decodeObj = decodeResult.decodableObj {
                lblType.text = decodeObj.vType
                lblAddress.text = decodeObj.txAddress
                addressId = decodeObj.iAddressId ?? 0
                self.isMosqueAddress = decodeObj.isMosques ?? 0 == 0 ? false : true
                if self.isMosqueAddress {
                    self.imgMosqueIcon.isHidden = false
                } else {
                    self.imgMosqueIcon.isHidden = true
                }
            }
            else {
                if self.isCartIncludedItemsOfMosques {
                    lblType.text = "select_address_of_mosque".localized
                    self.showAlert(message: "please_select_address_of_mosque_to_continue_your_order".localized)
                } else {
                    lblType.text = ObjKeymessages.kLABEL_SELECT_ADDRESS
                }
                lblAddress.text = ""
                addressId = 0
            }
        }
        else {
            if self.isCartIncludedItemsOfMosques {
                lblType.text = "select_address_of_mosque".localized
                self.showAlert(message: "please_select_address_of_mosque_to_continue_your_order".localized)
            } else {
                lblType.text = ObjKeymessages.kLABEL_SELECT_ADDRESS
            }
            lblAddress.text = ""
            addressId = 0
        }
    }

    private func getNewProducts() {
        var dictParam : [String:Any] = [:]
        dictParam["offset"] = offset
        dictParam["shiftDate"] = Date().convertDateToString()
        self.deliveryShiftSelectedDate = Date().convertDateToString()
        self.deliverySelectedDate = Date().getDate()
        
        if AppSingletonObj.isConnectedToNetwork() {
            self.presenterCheckout?.apiCallForGetCart(dictData: dictParam)
        }
    }

    private func setTexts() {
        lblTitle.text = ObjKeymessages.kLABEL_REVIEW_ORDER
        lblDeliveryAddressTitle.text = ObjKeymessages.kLABEL_DELIVERY_ADDRESS
        lblScheduleOrderTitle.text = ObjKeymessages.kLABEL_SCHEDULE_ORDER
        btnOnlyOnce.setTitle(ObjKeymessages.kLABEL_ONLY_ONCE, for: .normal)
        btnEveryWeek.setTitle(ObjKeymessages.kLABEL_EVERY_WEEK, for: .normal)
        btnEvery2Weeks.setTitle(ObjKeymessages.kLABEL_EVERY_TWO_WEEKS, for: .normal)
        btnEveryMonth.setTitle(ObjKeymessages.kLABEL_EVERY_MONTH, for: .normal)
        lblNotesTitle.text = ObjKeymessages.kLABEL_NOTES
        textViewNotes.placeholder = ObjKeymessages.kLABEL_WRITE_YOUR_NOTES
        lblPromoCodeTitle.text = ObjKeymessages.kLABEL_PROMO_CODE
        btnApply.setTitle(ObjKeymessages.kLABEL_APPLY, for: .normal)
        lblSubTotalTitle.text = "\(ObjKeymessages.kLABEL_SUBTOTAL) (\(ObjKeymessages.kLABEL_WITH_VAT))"
        lblDeliveryTitle.text = "\(ObjKeymessages.kLABEL_DELIVERY)"
        lblVATTitle.text = "\(ObjKeymessages.kLABEL_VAT)"
        lblTotalTitle.text = "\(ObjKeymessages.kLABEL_TOTAL)"
        lblVATRegNo.text = "\(ObjKeymessages.kLABEL_VAT_REG_NO): 311060632800003"
        btnPayNow.setTitle(ObjKeymessages.kLABEL_PAY_NOW, for: .normal)
        lblDiscountTitle.text = "\(ObjKeymessages.kLABEL_DISCOUNT)"
        txtPromoCode.placeholder = "\(ObjKeymessages.kLABEL_PROMO_CODE_PLACEHOLDER)"
        lblDeliveryShiftTitle.text = ObjKeymessages.kLABEL_DELIVERY_SHIFT
        lblDeliveryDiscountTitle.text = ObjKeymessages.kLABEL_DELIVERY_DISCOUNT
        lblDriverWillCall.text = ObjKeymessages.kLABEL_DRIVER_WILL_CALL
        lblUponArrival.text = ObjKeymessages.kLABEL_UPON_ARRIVAL
        txtPhoneNumber.placeholder = ObjKeymessages.kLABEL_PHONE_NO
        LblDeliveryShiftTitleSelected.text = ObjKeymessages.kLABEL_DELIVERY_SHIFT

        let string = ObjKeymessages.kLABEL_CHANGE
        let range = (string as NSString).range(of: ObjKeymessages.kLABEL_CHANGE)
        let attributedString = NSMutableAttributedString(string: string)
        attributedString.addAttribute(NSAttributedString.Key.underlineStyle, value: NSNumber(value: 1), range: range)
        attributedString.addAttribute(NSAttributedString.Key.underlineColor, value: UIColor.AppTheme_LightGrayColor_A0A0A0, range: range)
        attributedString.addAttribute(NSAttributedString.Key.font, value: UIFont(name: Fonts.LoewNextArabicBold, size: 12)!, range: range)
        attributedString.addAttribute(NSAttributedString.Key.foregroundColor, value: UIColor.AppTheme_LightGrayColor_B4B2B2, range: range)
        btnChangeDeliveryAddress.setAttributedTitle(attributedString, for: .normal)
        BtnChangeDeliveryShift.setAttributedTitle(attributedString, for: .normal)

        lblCongrats.text = "Congrats !".localized
        let congratsCoins = "You earn up to material coins.".localized.replacingOccurrences(of: "%2d", with: "")
        lblCongratsValue.text = congratsCoins
        btnPayWithPoints.setTitle("Pay with coins".localized, for: .normal)
        self.lblCoinsDiscount.text = "Discount".localized
        self.lblCoinsDiscountHint.text = "Coins discount".localized
        if UserDefaults.standard.getLanguage()! == UserAPI.VLanguage_userLanguage.en.rawValue {       // english
            btnOnlyOnce.titleEdgeInsets = UIEdgeInsets(top: 0, left: 10, bottom: 0, right: 0)
            btnEveryWeek.titleEdgeInsets = UIEdgeInsets(top: 0, left: 10, bottom: 0, right: 0)
            btnEvery2Weeks.titleEdgeInsets = UIEdgeInsets(top: 0, left: 10, bottom: 0, right: 0)
            btnEveryMonth.titleEdgeInsets = UIEdgeInsets(top: 0, left: 10, bottom: 0, right: 0)
        }
        else {   // arabic
            btnOnlyOnce.titleEdgeInsets = UIEdgeInsets(top: 0, left: 0, bottom: 0, right: 10)
            btnEveryWeek.titleEdgeInsets = UIEdgeInsets(top: 0, left: 0, bottom: 0, right: 10)
            btnEvery2Weeks.titleEdgeInsets = UIEdgeInsets(top: 0, left: 0, bottom: 0, right: 10)
            btnEveryMonth.titleEdgeInsets = UIEdgeInsets(top: 0, left: 0, bottom: 0, right: 10)

            if textViewNotes.textAlignment == NSTextAlignment.center {
            }
            else if textViewNotes.textAlignment == NSTextAlignment.right || textViewNotes.textAlignment == NSTextAlignment.left{
                textViewNotes.textAlignment = textViewNotes.textAlignment == NSTextAlignment.right ? NSTextAlignment.left : NSTextAlignment.right
            }
            
            if txtPhoneNumber.textAlignment == NSTextAlignment.center {
            }
            else if txtPhoneNumber.textAlignment == NSTextAlignment.right || txtPhoneNumber.textAlignment == NSTextAlignment.left{
                txtPhoneNumber.textAlignment = txtPhoneNumber.textAlignment == NSTextAlignment.right ? NSTextAlignment.left : NSTextAlignment.right
            }

        }
    }
    
    @IBAction func actionCountryCode(_ sender: Any) {
        self.view.endEditing(true)
        
        if AppSingletonObj.isConnectedToNetworkForCheckoutScreen() {
            if arrCountry.count <= 0 {  // Do not have country list, call API
                self.getCountryList(flagNoNetwork: true)
            }
            else {  // Having country list, open country picker
                let countryView = CountrySelectView()
                countryView.show()
                countryView.selectedCountryCallBack = {[weak self] (countryDic) -> Void in
                    self?.countryDictSetUp(countryDic: countryDic)
                }
            }
        }
    }
    
    
    @IBAction func actionDiscountUsingPoints(_ sender: UIButton) {
        self.isreward.toggle()
        self.setCoinsDiscountValues()
    }
    
    @IBAction func actionChangeDeliveryShift(_ sender: Any) {
        debugPrint("Delivery Shift Action")
        self.openChooseDeliveryDateVC()
    }
    
    private func openChooseDeliveryDateVC() {
        let vc = MyCartStoryboard.instantiate(ChooseDeliveryDateVC.self)
        vc.selectedIndex = self.deliveryShiftSelectedDateIndex
        vc.shiftAt = self.arrShifts[selectedShiftIndex].vStartAt ?? ""
        vc.modalPresentationStyle = .overFullScreen
        vc.delegate = self
        self.presentVC(vc)
    }
    
    private func setCoinsDiscountValues() {
        let final = lblTotalValue.text ?? "0"
        let total = final.replacingOccurrences(of: ObjKeymessages.kLABEL_SAR, with: "")
        self.coinsDiscountValue = forTrailingZero(temp: Constant.shared.USER_POINTS_BY_SAR).toDouble() ?? 0.0
        var netTotal = 0.0
        if isreward {
            self.isUsedCoins = true
            self.btnDiscountUsingPoints.setTitle("Discount is active".localized, for: .normal)
            self.lblCoinsDiscountValue.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "-\(forTrailingZero(temp: Constant.shared.USER_POINTS_BY_SAR).toDouble() ?? 0.0)")
            self.stackCoinsDiscount.isHidden = false
            if self.isCartIncludedItemsOfMosques {
                netTotal = (subTotal) - (self.coinsDiscountValue) - self.dDiscountAmount
            } else {
                netTotal = (subTotal + (strDistanceCost.toDouble() ?? 0.0)) - (self.coinsDiscountValue) - self.dDiscountAmount
            }
            lblVATValue.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: self.vatCostValue))")
            self.grandTotal = forTrailingZero(temp: netTotal)
            self.lblTotalValue.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: netTotal))")
            self.rewardAmount = forTrailingZero(temp: Constant.shared.USER_POINTS_BY_SAR).toDouble() ?? 0.0
            
            if self.coinsDiscountValue >= self.subTotal {
                self.calculateInvoiceDetails(type: .coinsMoreThanSubtotal)
            } else {
                self.calculateInvoiceDetails(type: .coinsLessThanSubtotal)
            }
            
        } else {
            self.isUsedCoins = false
            let strDiscount = "Get discount using your points".localized.replacingOccurrences(of: "%2d", with: "\(forTrailingZero(temp: Constant.shared.USER_POINTS_BY_SAR)) \(ObjKeymessages.kLABEL_SAR)")
            self.btnDiscountUsingPoints.setTitle(strDiscount, for: .normal)
            self.stackCoinsDiscount.isHidden = true
            self.coinsDiscountValue = 0.0
            netTotal = (subTotal + (strDistanceCost.toDouble() ?? 0.0)) - (self.coinsDiscountValue + self.deliveryCharageVatValue)
            lblVATValue.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: self.vatCostValue))")
            self.grandTotal = forTrailingZero(temp: netTotal)
            self.lblTotalValue.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: netTotal))")
            self.calculateInvoiceDetails(type: .normal)
        }
        self.presenterCheckout?.apicalculateUserAmountToPoints(amount: netTotal, type: .getCoins)
    }
    
    private func calculateInvoiceDetails(type: CalculateInvoiceType) {
        var vat = 0.0
        var total = 0.0
        switch type {
        case .normal:
            // Vat
            vat = self.calculateVat()
            if self.isCartIncludedItemsOfMosques {
                total = (self.subTotal) - (self.coinsDiscountValue + self.dDiscountAmount)
            } else {
                total = (self.subTotal + self.dDistanceCost) - (self.coinsDiscountValue + self.dDiscountAmount)
            }
            self.lblDeliveryValue.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: self.dDistanceCost))")
        case .fixedDiscountValue(let value):
            // Vat
            vat = self.calculateVat()
            if self.isCartIncludedItemsOfMosques {
                total = (self.subTotal) - (value + self.coinsDiscountValue + self.dDiscountAmount)
            } else {
                total = (self.subTotal + self.dDistanceCost) - (value + self.coinsDiscountValue)
            }
            self.lblDeliveryValue.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: self.dDistanceCost))")
            self.lblDiscountValue.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "-\(forTrailingZero(temp: value))")
        case .freeDeliveryPromocode:
            // Vat
            vat = self.calculateVat()
            total = self.subTotal - self.coinsDiscountValue
            self.lblDeliveryDiscountValue.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "-\(forTrailingZero(temp: self.dDistanceCost))")
        case .percentPromocode(let discountAmount):
            total = self.subTotal
            // Vat
            vat = self.calculateVat()
            if self.isCartIncludedItemsOfMosques {
                total = ((self.subTotal - discountAmount.rounded(toPlaces: 2))) - (self.coinsDiscountValue)
            } else {
                total = ((self.subTotal - discountAmount.rounded(toPlaces: 2)) + self.dDistanceCost) - (self.coinsDiscountValue)
            }
            self.lblDiscountValue.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value:  "-\(forTrailingZero(temp: discountAmount))")
        case .freeDeliveryWithPercentPromocode(let discountAmount):
            // Vat
            vat = self.calculateVat()
            total = (self.subTotal - discountAmount) - (self.coinsDiscountValue)
            self.lblDeliveryDiscountTitle.text = "\(ObjKeymessages.KLABEL_FREE_DELIVERY)"
            self.lblDiscountValue.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "-\(forTrailingZero(temp: discountAmount))")
        case .coinsMoreThanSubtotal:
            // Vat
            vat = self.calculateVat()
            self.lblCoinsDiscountValue.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "-\(forTrailingZero(temp: self.coinsDiscountValue))")
            if self.isCartIncludedItemsOfMosques {
                let total = (self.subTotal) - (self.dDiscountAmount + self.coinsDiscountValue)
            } else {
                let total = (self.subTotal + self.dDistanceCost) - (self.dDiscountAmount + self.coinsDiscountValue)
            }
        case .coinsLessThanSubtotal:
            let discountAmount = self.subTotal - self.coinsDiscountValue
            // Vat
            vat = self.calculateVat()
            if dDiscountAmount > 0 {
                total = (self.subTotal + self.dDistanceCost) - (self.coinsDiscountValue + self.dDiscountAmount)
            } else {
                if self.isCartIncludedItemsOfMosques {
                    total = (self.subTotal) - (self.dDiscountAmount + self.coinsDiscountValue)
                } else {
                    total = (self.subTotal + self.dDistanceCost) - (self.dDiscountAmount + self.coinsDiscountValue)
                }
            }
            self.lblCoinsDiscountValue.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "-\(forTrailingZero(temp: self.coinsDiscountValue))")
        }
        self.vatCostValue = vat
        self.lblSubTotalValue.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: self.subTotal))")
        self.lblVATValue.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: vat))")
        self.lblTotalValue.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: total))")
        self.grandTotal = forTrailingZero(temp: total)
    }
    
    private func calculateVat() -> Double {
        let subTotalAfterDiscount = self.subTotal - self.coinsDiscountValue - self.dDiscountAmount
        let priceAfterDiscountWithoutVat = subTotalAfterDiscount / 1.15
        let productVat = subTotalAfterDiscount - priceAfterDiscountWithoutVat
        // Vat
        return productVat + self.deliveryCharageVatValue
    }
    
    @IBAction func actionPayWithPoints(_ sender: Any) {
        Constant.shared.IS_PAYMENT_WITH_COINS = true
        self.payNow(isPayWithPoints: true)
    }
    
    
    @IBAction func actionInfoPoints(_ sender: Any) {
        self.viewInfoPoints.isHidden.toggle()
        self.visualEffectViewInfoPoints.isHidden.toggle()
    }
  
    func countryDictSetUp(countryDic: [String: Any]?) {
        if countryDic == nil {
            self.btnCountryCode.setTitle(SACountryCode, for: .normal)
            if let regCode = Locale.current.regionCode {
                if let dict = CountryCodeJson.filter({$0["locale"] as? String == regCode}).first {
                    self.btnCountryCode.setTitle("+\(dict["code"] as! NSNumber)", for: .normal)
                }
            }
        }
        else {
            self.btnCountryCode.setTitle("+\(countryDic!["code"] as! NSNumber)", for: .normal)
        }
    }

    @IBAction func btnBackAction(_ sender: Any) {
        self.view.endEditing(true)
        self.popVC()
    }

    @IBAction func payNowAction(_ sender: Any) {
        self.payNow()
    }
    
    private func payNow(isPayWithPoints: Bool = false, discountValue: Double? = 0.0) {
        // check delivery to mosque
        if isCartIncludedItemsOfMosques == true && self.isMosqueAddress == false {
            self.showError(message: "please_select_address_of_mosque_to_continue_your_order".localized)
            return
        }
        if isCartIncludedItemsOfMosques == true {
            self.grandTotal = "\(self.grandTotal.toDouble() ?? 0.0 - (objCartData?.dDistanceCostSetInAdmin?.toDouble() ?? 0.0))"
        }
        self.view.endEditing(true)
        if AppSingletonObj.isConnectedToNetwork(){
            
            if arrCart.count > 0 {
                dictParam["iCartId"] = arrCart[0].iCartId
            }
            else {
                dictParam["iCartId"] = arrBundles[0].iCartId
            }
            
            dictParam["iAddressId"] = addressId
            
            var reccuringType = ReccuringType.OnlyOnce.rawValue
            if btnOnlyOnce.isSelected == true {
                reccuringType = ReccuringType.OnlyOnce.rawValue
            }
            else if btnEveryWeek.isSelected == true {
                reccuringType = ReccuringType.EveryWeek.rawValue
            }
            else if btnEvery2Weeks.isSelected == true {
                reccuringType = ReccuringType.Every2Weeks.rawValue
            }
            else if btnEveryMonth.isSelected == true {
                reccuringType = ReccuringType.EveryMonth.rawValue
            }
            dictParam["iReccuringType"] = reccuringType
            dictParam["tAdditionalNote"] = textViewNotes.text
            dictParam["iPromocodeUseId"] = iPromocodeUseId
            dictParam["dOrderTotal"] = objCartData?.dSubTotalWithoutVAT == nil ? objCartData?.dSubTotal : objCartData?.dSubTotalWithoutVAT
            if isCartIncludedItemsOfMosques == true {
                dictParam["isMosques"] = "1"
            } else {
                dictParam["isMosques"] = "0"
            }
            dictParam["dOrderTotal"] = objCartData?.dSubTotalWithoutVAT
            dictParam["dVatCost"] = self.dVatCost.rounded(toPlaces: 2)
            if isPayWithPoints == true {
                self.rewardAmount = grandTotal.toDouble() ?? 0.0
                self.isreward = true
                dictParam["dVatCost"] = 0
                dictParam["dOrderTotal"] = 0
            }
            if selectedShiftBySelectedDateIndex == -1 {
                AppSingletonObj.showAlert(strMessage: ObjKeymessages.kMSG_EMPTY_DELIVERY_SHIFT)
                return
            }
            else {
                dictParam["tiShiftType"] = arrShiftsBySelectDate[selectedShiftBySelectedDateIndex].tiShiftType
            }

            dictParam["vAlternativeISDCode"] = btnCountryCode.titleLabel?.text
            dictParam["vAlternativeMobileNumber"] = txtPhoneNumber.text

            let objSelectedShift = arrShiftsBySelectDate[selectedShiftBySelectedDateIndex]
            dictParam["iShiftId"] = objSelectedShift.iShiftId
            dictParam["vShiftDisplayNameEn"] = objSelectedShift.vShiftDisplayNameEn
            dictParam["vShiftDisplayNameAr"] = objSelectedShift.vShiftDisplayNameAr
            dictParam["vStartAt"] = objSelectedShift.vStartAt
            dictParam["vCloseAt"] = objSelectedShift.vCloseAt
            dictParam["tiIsFriday"] = objSelectedShift.tiIsFriday
            dictParam["tiIsRamadan"] = objSelectedShift.tiIsRamadan
            dictParam["tiIsRegularShift"] = objSelectedShift.tiIsRegularShift

            if self.presenterCheckout?.checkValidation(dictData: dictParam) ?? false {
                if Int(setDataInString(dictParam["iAddressId"] as AnyObject)) ?? 0 == 0 {
                    self.actionChangeAddress((Any).self)
                    delay(0.5) {
                        AppSingletonObj.showAlert(strMessage: ObjKeymessages.kMSG_EMPTY_ADDRESS)
                    }
                    return
                }
                if isPayWithPoints {
                    self.payWithPoints()
                } else {
                    self.showPaymentPopup()
                }
            }
        }
    }
    
    private func payWithPoints(discountValue: Double = 0.0) {
        DispatchQueue.main.async {
            self.setLogOfInitiatedCheckout()
            self.presenterCheckout?.apiCallCreateOrder(dictData: self.dictParam , transactionType: TransactionType.Coins.rawValue, flagCardOrNot: false, strCardToken: "", strCardTransactionReference: "", flagSplitWallet: false, strRemainingAmt: discountValue == 0.0 ? "" : "\(discountValue)", isreward: self.isreward, rewardamount: self.rewardAmount, isCartIncludeBundle: self.isCartIncludeBundle, expectedTime: self.deliverySelectedDate)
        }
    }
    
    @IBAction func onlyOnceAction(_ sender: Any) {
        self.view.endEditing(true)
        btnOnlyOnce.isSelected = true
        btnEveryWeek.isSelected = false
        btnEvery2Weeks.isSelected = false
        btnEveryMonth.isSelected = false
    }
    
    @IBAction func everyWeekAction(_ sender: Any) {
        self.view.endEditing(true)
        btnOnlyOnce.isSelected = false
        btnEveryWeek.isSelected = true
        btnEvery2Weeks.isSelected = false
        btnEveryMonth.isSelected = false
    }

    @IBAction func everyTwoWeeksAction(_ sender: Any) {
        self.view.endEditing(true)
        btnOnlyOnce.isSelected = false
        btnEveryWeek.isSelected = false
        btnEvery2Weeks.isSelected = true
        btnEveryMonth.isSelected = false
    }

    @IBAction func everyMonthAction(_ sender: Any) {
        self.view.endEditing(true)
        btnOnlyOnce.isSelected = false
        btnEveryWeek.isSelected = false
        btnEvery2Weeks.isSelected = false
        btnEveryMonth.isSelected = true
    }

    @IBAction func actionChangeAddress(_ sender: Any) {
        self.view.endEditing(true)
        let vc = settingsStoryboard.instantiate(SelectLocationViewController.self)
        vc.delegate = self
        vc.isMustSelectMosque = self.isCartIncludedItemsOfMosques
        self.presentVC(vc)
    }
    
    @IBAction func applyPromoCodeAction(_ sender: Any) {
        self.view.endEditing(true)
        
        if txtPromoCode.isUserInteractionEnabled == true {  // Apply promo code
            if AppSingletonObj.isConnectedToNetwork(){
                var dictParam : [String:Any] = [:]
                dictParam["vPromocode"] = txtPromoCode.text
                dictParam["dSubTotal"] = "\(self.subTotal - self.coinsDiscountValue)"
                dictParam["dDistanceCost"] = strDistanceCost

                if txtPromoCode.text?.trimmed == "" {
                    AppSingletonObj.showAlert(strMessage: ObjKeymessages.kMSG_EMPTY_PROMO_CODE)
                    return
                }

                self.presenterCheckout?.apiCallApplyPromoCode(dictData: dictParam)
            }
        }
        else {  // Remove promo code
            if AppSingletonObj.isConnectedToNetwork(){
                var dictParam : [String:Any] = [:]
                dictParam["iPromocodeUseId"] = iPromocodeUseId

                self.presenterCheckout?.apiCallRemovePromoCode(dictData: dictParam)
            }
        }
    }
    
    func updateCostUI() {
        self.calculateCostingAfterApplyingPromoCode()
    }
    
    private func calculateCostingAfterApplyingPromoCode() {
        
        /*enum class PromoCodeDiscountType(val value: Int) {
            Flat(1),
            Percentage(2),
            FreeDelivery(3),
        }*/
        
        let subTotalTmp = Double("\(objPromoCodeData?.dOrderAmount ?? "")") ?? 0
        lblSubTotalValue.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: subTotalTmp))")
        strDistanceCost = objPromoCodeData?.dDistanceCost ?? ""
        lblDeliveryValue.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: objPromoCodeData?.dDistanceCost?.toDouble() ?? 0.0))")
        lblVATValue.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: objPromoCodeData?.dVatCost?.toDouble() ?? 0.0))")
        lblDiscountValue.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "-\(forTrailingZero(temp: objPromoCodeData?.dDiscountAmount?.toDouble() ?? 0.0))")
        let discountAmt = Double(objPromoCodeData?.dDiscountAmount ?? "")
        self.dDiscountAmount = Double(objPromoCodeData?.dDiscountAmount ?? "") ?? 0.0

        if objPromoCodeData?.tiDiscountType != 3 {
            stackViewDiscount.isHidden = false
            if objCartData?.dMinOrderFreeDelivery == 1 || objCartData?.dHasFreeOrder == 1 || self.isCartIncludedItemsOfMosques { // free delivery eligible
                stackViewDeliveryDiscount.isHidden = false
                let finalTotal = (subTotal + (strDistanceCost.toDouble() ?? 0.0)) - (self.coinsDiscountValue + self.deliveryCharageVatValue)
                grandTotal = forTrailingZero(temp: finalTotal)
                lblTotalValue.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: finalTotal))")
                lblDeliveryValue.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: objCartData?.dDistanceCostSetInAdmin?.toDouble() ?? 0.0))")
                
                if forTrailingZero(temp: Constant.shared.USER_POINTS_BY_SAR).toDouble() ?? 0.0 >= forTrailingZero(temp: finalTotal).toDouble() ?? 0.0 {
                    self.stackViewPayWithPoints.isHidden = false
                    self.stackViewDiscountUsingPoints.isHidden = true
                } else if forTrailingZero(temp: Constant.shared.USER_POINTS_BY_SAR).toDouble() ?? 0.0 == 0.0 {
                    self.stackViewPayWithPoints.isHidden = true
                    self.stackViewDiscountUsingPoints.isHidden = true
                } else {
                    self.stackViewPayWithPoints.isHidden = true
                    self.stackViewDiscountUsingPoints.isHidden = false
                }
                
            }
            else {
                stackViewDeliveryDiscount.isHidden = true
                lblDeliveryDiscountValue.text = ""
                let finalTotal = (subTotal + (strDistanceCost.toDouble() ?? 0.0)) - (self.coinsDiscountValue + self.deliveryCharageVatValue)
                grandTotal = forTrailingZero(temp: finalTotal)
                lblTotalValue.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: finalTotal))")
                if forTrailingZero(temp: Constant.shared.USER_POINTS_BY_SAR).toDouble() ?? 0.0 >= forTrailingZero(temp: finalTotal).toDouble() ?? 0.0 {
                    self.stackViewPayWithPoints.isHidden = false
                    self.stackViewDiscountUsingPoints.isHidden = true
                } else if forTrailingZero(temp: Constant.shared.USER_POINTS_BY_SAR).toDouble() ?? 0.0 == 0.0 {
                    self.stackViewPayWithPoints.isHidden = true
                    self.stackViewDiscountUsingPoints.isHidden = true
                } else {
                    self.stackViewPayWithPoints.isHidden = true
                    self.stackViewDiscountUsingPoints.isHidden = false
                }
            }
            
        }
        else {
            stackViewDiscount.isHidden = true
            stackViewDeliveryDiscount.isHidden = false
            lblDeliveryValue.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: objCartData?.dDistanceCostSetInAdmin?.toDouble() ?? 0.0))")
            lblDeliveryDiscountValue.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "-\(forTrailingZero(temp: objCartData?.dDistanceCostSetInAdmin?.toDouble() ?? 0.0))")

            if objCartData?.dMinOrderFreeDelivery == 1 || objCartData?.dHasFreeOrder == 1 { // free delivery eligible
                let finalTotal = (subTotal + (strDistanceCost.toDouble() ?? 0.0)) - (self.coinsDiscountValue + self.deliveryCharageVatValue)
                grandTotal = forTrailingZero(temp: finalTotal)
                lblTotalValue.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: finalTotal))")
                
                if forTrailingZero(temp: Constant.shared.USER_POINTS_BY_SAR).toDouble() ?? 0.0 >= forTrailingZero(temp: finalTotal).toDouble() ?? 0.0 {
                    self.stackViewPayWithPoints.isHidden = false
                    self.stackViewDiscountUsingPoints.isHidden = true
                } else if forTrailingZero(temp: Constant.shared.USER_POINTS_BY_SAR).toDouble() ?? 0.0 == 0.0 {
                    self.stackViewPayWithPoints.isHidden = true
                    self.stackViewDiscountUsingPoints.isHidden = true
                } else {
                    self.stackViewPayWithPoints.isHidden = true
                    self.stackViewDiscountUsingPoints.isHidden = false
                }
                
            }
            else {
                let finalTotal = (subTotal + (strDistanceCost.toDouble() ?? 0.0)) - (self.coinsDiscountValue + self.deliveryCharageVatValue)
                grandTotal = forTrailingZero(temp: finalTotal)
                lblTotalValue.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: finalTotal))")
                if forTrailingZero(temp: Constant.shared.USER_POINTS_BY_SAR).toDouble() ?? 0.0 >= forTrailingZero(temp: finalTotal).toDouble() ?? 0.0 {
                    self.stackViewPayWithPoints.isHidden = false
                    self.stackViewDiscountUsingPoints.isHidden = true
                } else if forTrailingZero(temp: Constant.shared.USER_POINTS_BY_SAR).toDouble() ?? 0.0 == 0.0 {
                    self.stackViewPayWithPoints.isHidden = true
                    self.stackViewDiscountUsingPoints.isHidden = true
                } else {
                    self.stackViewPayWithPoints.isHidden = true
                    self.stackViewDiscountUsingPoints.isHidden = false
                }
                
            }
        }
        if self.isreward {
            self.stackViewPayWithPoints.isHidden = true
        }
        self.updateCoinCalculateValues()
        if objCartData?.dMinOrderFreeDelivery == 1 || objCartData?.dHasFreeOrder == 1 { // free delivery eligible
            self.calculateInvoiceDetails(type: .freeDeliveryWithPercentPromocode(discountAmount: discountAmt ?? 0.0))
        } else if objPromoCodeData?.tiDiscountType == 3 {
            self.calculateInvoiceDetails(type: .freeDeliveryPromocode)
        } else if objPromoCodeData?.tiDiscountType == 2 {
            self.dDiscountAmount = discountAmt ?? 0.0
            self.calculateInvoiceDetails(type: .percentPromocode(discountAmount: discountAmt ?? 0.0))
        } else if objPromoCodeData?.tiDiscountType == 1 { // fixed amount
            self.calculateInvoiceDetails(type: .fixedDiscountValue(value: discountAmt ?? 0.0))
        }
    }

    private func updateCoinCalculateValues() {
        if self.grandTotal.toDouble() ?? 0.0 <= 0 {
            if forTrailingZero(temp: Constant.shared.USER_POINTS_BY_SAR).toDouble() ?? 0.0 >= self.grandTotal.toDouble() ?? 0.0 {
                let final = lblTotalValue.text ?? "0"
                let total = final.replacingOccurrences(of: ObjKeymessages.kLABEL_SAR, with: "")
                var netTotal = 0.0
                netTotal = (total.toDouble() ?? 0.0) + (self.coinsDiscountValue)
                self.grandTotal = forTrailingZero(temp: netTotal)
                self.lblTotalValue.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: netTotal))")
                self.stackViewPayWithPoints.isHidden = false
                self.stackViewDiscountUsingPoints.isHidden = true
                self.stackCoinsDiscount.isHidden = true
#warning("Get discount using your points")
                let strDiscount = "Get discount using your points".localized.replacingOccurrences(of: "%2d", with: "\(forTrailingZero(temp: Constant.shared.USER_POINTS_BY_SAR)) \(ObjKeymessages.kLABEL_SAR)")
                self.btnDiscountUsingPoints.setTitle(strDiscount, for: .normal)
                self.rewardAmount = 0.0
                self.coinsDiscountValue = 0.0
                self.isreward = false
            }
        }
        self.presenterCheckout?.apicalculateUserAmountToPoints(amount: self.grandTotal.toDouble() ?? 0.0, type: .getCoins)
    }
    
    private func setDeliveryShiftTitleSelected() {
        var vShiftDisplayName = ""
        if UserDefaults.standard.isCurrentLanguageArabic() {
            vShiftDisplayName = self.arrShifts[selectedShiftIndex].vShiftDisplayNameAr ?? ""
        }
        else {
            vShiftDisplayName = self.arrShifts[selectedShiftIndex].vShiftDisplayNameEn ?? ""
        }
        
        if self.deliveryShiftSelectedDate == "" {
            self.LblDeliveryShiftTitleSelected.text = "\(self.getDeliveryTiming()) \(vShiftDisplayName) \(ObjKeymessages.kLABEL_FROM) \(self.arrShifts[selectedShiftIndex].vStartAt ?? "") \(ObjKeymessages.kLABEL_TO) \(self.arrShifts[selectedShiftIndex].vCloseAt ?? "")."
        } else {
            self.LblDeliveryShiftTitleSelected.text = "\(self.deliveryShiftSelectedDate) \(vShiftDisplayName) \(ObjKeymessages.kLABEL_FROM) \(self.arrShifts[selectedShiftIndex].vStartAt ?? "") \(ObjKeymessages.kLABEL_TO) \(self.arrShifts[selectedShiftIndex].vCloseAt ?? "")."
        }
    }
    
    private func getDeliveryTiming() -> String {
        var dayName = ""
        if self.isTodayAvailable {
            dayName = "Today".localized
        } else {
            dayName = "tomorrow".localized
        }
        return dayName
    }
    
    private func isDeliveryToday(shiftAt: String) -> Bool {
        let originalTime = utcToLocalTimeOnlyDate(strTime: shiftAt) ?? Date() // "05:00 PM"
        let date = Date()// Aug 25, 2017, 11:55 AM
        let calendar = Calendar.current
        let hour = calendar.component(.hour, from: date)
        var fromHour = calendar.component(.hour, from: originalTime)
        if fromHour == 0 {
            fromHour = 24
        }
        if hour < fromHour && (hour - fromHour) != -1 {
            print("can delrivery")
            return true
        } else {
            print("can't delrivery")
            return false
        }
    }
    
    func utcToLocalTimeOnlyDate(strTime: String) -> Date? {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "hh:mm a"
        return dateFormatter.date(from: strTime)
    }
    
    private func checkFromTodayIsHasShiftAvailable() -> Bool {
        if self.isTodayAvailable == false {
            self.arrShifts[0].setIsShiftAvailable(value: true)
            self.selectedShiftIndex = 0
            self.collectionViewShifts.reloadData()
            self.getShiftsDetailsByDate(Date().addingTimeInterval(.days(1)))
            return false
        } else {
            self.selectedShiftIndex = 0
            return true
        }
    }
    
}

extension CheckoutViewController: UITableViewDelegate, UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        if tableView == tblRewardDetails {
            return Constant.shared.POINTS_DESCRIPTION.count
        } else {
            return arrCart.count + self.arrBundles.count
        }
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        
        if tableView == tblRewardDetails {
            let cell = tableView.dequeueReusableCell(withIdentifier: "RewardDetailsCell") as! RewardDetailsCell
            cell.setupCell(itemNo: indexPath.row + 1, desc: Constant.shared.POINTS_DESCRIPTION[indexPath.row], isUsingSmallFont: true)
            return cell
            // logic of bundles here..
        } else if indexPath.row < (self.arrBundles.count) && self.arrBundles.count != 0 {
            self.isCartIncludeBundle = 1
            let cell = tableView.dequeue(with: MyCartCheckoutTableViewCell.self, for: indexPath)
            let index = indexPath.row //- self.arrCart.count
            
            cell.imgProduct.kf.indicatorType = .activity
            let url = URL(string: arrBundles[index].vBunddelImage ?? "")
            cell.imgProduct.kf.setImage(with: url , placeholder: UIImage(named: "placeholder_category"))
            
            cell.lblProductName.text = arrBundles[index].vBunddelName
            cell.lblQuantityUnit.text = ""
            cell.lblProductSkuValue.text = arrBundles[index].vBunddelSKU
            cell.lblQuantity.text = "\(ObjKeymessages.kLABEL_QUANTITY) : \(forTrailingZero(temp: arrBundles[index].iBunddelQuantity ?? 1))"
            if arrBundles[index].discountPrice ?? 0 == 0 { // show price in green color
                cell.lblPrice.textColor = UIColor.AppTheme_DiscountGreenColor_05B13E
                cell.lblPrice.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: arrBundles[index].price ?? 0.0))")
                cell.lblOldPrice.isHidden = true
                cell.lblOldPrice.text = ""
            }
            else {
                cell.lblPrice.textColor = .black
                // discount logic
                if arrBundles[index].discountPrice ?? 0 == 0 {   // no discount
                    cell.lblPrice.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: arrBundles[index].price ?? 0.0))")
                    cell.lblOldPrice.isHidden = true
                    cell.lblOldPrice.text = ""
                    
                }
                else {  // discount is there
                    cell.lblPrice.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: arrBundles[index].discountPrice ?? 0.0))")
                    cell.lblOldPrice.isHidden = false
                    cell.lblOldPrice.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: arrBundles[index].price ?? 0.0))", isDiscounted: true)
                }
            }
            // discount logic ends here
            return cell
        } else {
            let cell = tableView.dequeue(with: MyCartCheckoutTableViewCell.self, for: indexPath)
            let index = indexPath.row - self.arrBundles.count
            cell.imgProduct.kf.indicatorType = .activity
            let url = URL(string: arrCart[index].vProductImage ?? "")
            cell.imgProduct.kf.setImage(with: url , placeholder: UIImage(named: "placeholder_category"))
            cell.lblProductName.text = arrCart[index].vProductName
            cell.lblQuantityUnit.text = arrCart[index].vProductUnit
            cell.lblProductSkuValue.text = arrCart[index].vProductSKU
            cell.lblQuantity.text = "\(ObjKeymessages.kLABEL_QUANTITY) : \(forTrailingZero(temp: arrCart[index].iProductQuantity ?? 0))"
            if arrCart[index].dDiscountedTotalProductPrice ?? "" == "0" { // show price in green color
                cell.lblPrice.textColor = UIColor.AppTheme_DiscountGreenColor_05B13E
                cell.lblPrice.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: arrCart[index].dDiscountedTotalProductPrice?.toDouble() ?? 0.0))")
                cell.lblOldPrice.isHidden = true
                cell.lblOldPrice.text = ""
            }
            else {
                cell.lblPrice.textColor = .black
                // discount logic
                if arrCart[index].dDiscountAmount == "" {   // no discount
                    cell.lblPrice.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: arrCart[index].totalProductPriceWithVAT?.toDouble() ?? 0.0))")
                    cell.lblOldPrice.isHidden = true
                    cell.lblOldPrice.text = ""
                    
                }
                else {  // discount is there
                    cell.lblPrice.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: arrCart[index].dDiscountedTotalProductPrice?.toDouble() ?? 0.0))")
                    cell.lblOldPrice.isHidden = false
                    cell.lblOldPrice.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: arrCart[index].totalProductPriceWithVAT?.toDouble() ?? 0.0))", isDiscounted: true)
                }
            }
            // discount logic ends here
            return cell
        }
    }
    
}

func delay(_ seconds: Double, completion: @escaping () -> ()) {
    DispatchQueue.main.asyncAfter(deadline: .now() + seconds) {
        completion()
    }
}

class ContentSizedTableView: UITableView {
    override var contentSize:CGSize {
        didSet {
            invalidateIntrinsicContentSize()
        }
    }
    
    override var intrinsicContentSize: CGSize {
        let height = min(contentSize.height, CGFloat.infinity)
        return CGSize(width: contentSize.width, height: height)
    }
}

extension CheckoutViewController: CheckoutProtocol {
    func displayAlert(string: String) {
        AppSingleton.shared.showCustomPopUpWithOkButton(strButtonTitle: ObjKeymessages.kLABEL_OK, strMessage: string, showOnTopVC: false) { (isOk) in
        }
    }

    func refreshCart(model : GetCartListResponseFields) {
        
        objCartData = model
        self.totalCount = model.totalRecord ?? 0

        // emtpy the array if it is initial call
        if self.offset == 1 {
            self.arrCart.removeAll()
            self.arrBundles.removeAll()
        }
        self.arrCart.append(contentsOf: model.products ?? [])
        self.arrBundles.append(contentsOf: model.bunddels ?? [])
        
        var shifts: [GetCartShiftResponseFields] = [GetCartShiftResponseFields()]
        self.arrShiftsBySelectDate = objCartData?.shift ?? []
        self.isTodayAvailable = model.isTodayAvailable ?? 0 == 1 ? true : false
        // add shifts related to isTodayAvailable
        if self.isTodayAvailable {
            shifts = [GetCartShiftResponseFields(iShiftId: 1,
                                                 vShiftDisplayNameEn: "Today".localized,
                                                 vShiftDisplayNameAr: "Today".localized
                                                ),
                      GetCartShiftResponseFields(iShiftId: 2,
                                                 vShiftDisplayNameEn: "Tomorrow".localized,
                                                 vShiftDisplayNameAr: "Tomorrow".localized
                                                ),
                      GetCartShiftResponseFields(iShiftId: 3,
                                                 vShiftDisplayNameEn: "select_date".localized,
                                                 vShiftDisplayNameAr: "select_date".localized
                                                )]
        } else {
            shifts = [GetCartShiftResponseFields(iShiftId: 2,
                                                 vShiftDisplayNameEn: "Tomorrow".localized,
                                                 vShiftDisplayNameAr: "Tomorrow".localized
                                                ),
                      GetCartShiftResponseFields(iShiftId: 3,
                                                 vShiftDisplayNameEn: "select_date".localized,
                                                 vShiftDisplayNameAr: "select_date".localized
                                                )]
        }
        
        self.arrShifts = shifts
        checkFromTodayIsHasShiftAvailable()
        tblCheckout.reloadData()
        
        if arrShifts.count > 0 {
            self.setDeliveryShiftTitleSelected()
            collectionViewShifts.reloadData()
        }
        
        if arrShiftsBySelectDate.count > 0 {
            self.isShowShiftsSelectedDate(isCollectionViewHidden: false)
            collectionViewShiftsByDate.reloadData()
        }
        self.calculateCosting(model: model)
    }

    private func calculateCosting(model : GetCartListResponseFields) {
        self.vatPercentage = model.dVatPercentage?.toDouble() ?? 0.0
        self.dVatCost = model.dVatCost?.toDouble()?.rounded(toPlaces: 2) ?? 0.0
        subTotal = 0
        self.dSubTotalWithoutVAT = model.dSubTotalWithoutVAT?.toDouble() ?? 0.0
        self.dDistanceCost = model.dDistanceCost?.toDouble() ?? 0.0
        self.dVatPercentage = model.dVatPercentage?.toDouble() ?? 0.0
        self.vatCostValue = model.dVatCost?.toDouble() ?? 0.0
        self.vatCostFixedReturnValue = model.dVatCost?.toDouble() ?? 0.0
        self.deliveryCharageVatValue = model.deliveryCharageVat ?? 0.0
        strDistanceCost = model.dDistanceCost ?? ""
        
        subTotal = Double(model.dSubTotal ?? "") ?? 0
        
        let subTotalWithoutVat = Double(model.dSubTotal ?? "") ?? 0
        lblSubTotalValue.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: subTotalWithoutVat))")

        let vatCost = Double(model.dVatCost ?? "") ?? 0
        lblVATValue.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: vatCost))")

        lblDeliveryValue.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: model.dDistanceCost?.toDouble() ?? 0.0))")
        lblVATIncludes.text = "(\(ObjKeymessages.kLABEL_VAT_INCLUDES) \(forTrailingZero(temp: model.dVatPercentage?.toDouble() ?? 0.0))%)"
        
        let deliveryCharge = Double(model.dDistanceCost ?? "")
        let finalTotal = (subTotal + (deliveryCharge ?? 0)) - (self.coinsDiscountValue)
        grandTotal = forTrailingZero(temp: finalTotal)
        lblTotalValue.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: finalTotal))")
        
        if forTrailingZero(temp: Constant.shared.USER_POINTS_BY_SAR).toDouble() ?? 0.0 >= forTrailingZero(temp: finalTotal).toDouble() ?? 0.0 {
            self.stackViewPayWithPoints.isHidden = false
            self.stackViewDiscountUsingPoints.isHidden = true
        } else if forTrailingZero(temp: Constant.shared.USER_POINTS_BY_SAR).toDouble() ?? 0.0 == 0.0 {
            self.stackViewPayWithPoints.isHidden = true
            self.stackViewDiscountUsingPoints.isHidden = true
        } else {
            self.stackViewPayWithPoints.isHidden = true
            self.stackViewDiscountUsingPoints.isHidden = false
            if self.isFirstOpen {
                self.isFirstOpen = false
                let strDiscount = "Get discount using your points".localized.replacingOccurrences(of: "%2d", with: "\(forTrailingZero(temp: Constant.shared.USER_POINTS_BY_SAR)) \(ObjKeymessages.kLABEL_SAR)")
                self.btnDiscountUsingPoints.setTitle(strDiscount, for: .normal)
            }
        }
        
        // logic for free delivery for particular amount. Eg. amount > 300
        if objCartData?.dMinOrderFreeDelivery == 1 || objCartData?.dHasFreeOrder == 1 || self.isCartIncludedItemsOfMosques { // free delivery eligible
            stackViewDeliveryDiscount.isHidden = false
            lblDeliveryValue.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: objCartData?.dDistanceCostSetInAdmin?.toDouble() ?? 0.0))")
            lblDeliveryDiscountValue.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "-\(forTrailingZero(temp: objCartData?.dDistanceCostSetInAdmin?.toDouble() ?? 0.0))")
            
            let subTotalWithoutVat = Double(model.dSubTotal ?? "") ?? 0
            let vatCost = Double(model.dVatCost ?? "") ?? 0
            let tmpTotal = (subTotalWithoutVat + (deliveryCharge ?? 0.0)) - (self.coinsDiscountValue + self.dDistanceCost)
            
            grandTotal = forTrailingZero(temp: tmpTotal)
            lblTotalValue.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: tmpTotal))")
            
            if forTrailingZero(temp: Constant.shared.USER_POINTS_BY_SAR).toDouble() ?? 0.0 >= forTrailingZero(temp: finalTotal).toDouble() ?? 0.0 {
                self.stackViewPayWithPoints.isHidden = false
                self.stackViewDiscountUsingPoints.isHidden = true
            } else if forTrailingZero(temp: Constant.shared.USER_POINTS_BY_SAR).toDouble() ?? 0.0 == 0.0 {
                self.stackViewPayWithPoints.isHidden = true
                self.stackViewDiscountUsingPoints.isHidden = true
            } else {
                self.stackViewPayWithPoints.isHidden = true
                self.stackViewDiscountUsingPoints.isHidden = false
            }
        }
        self.updateCoinCalculateValues()
        self.calculateInvoiceDetails(type: .normal)
    }
    
    func makePaymentToPayTabs(model: CreateOrderResponseFields, transactionType: Int, flagCardOrNot: Bool, strCardToken: String, strCardTransactionReference: String, flagSplitWallet: Bool, strRemainingAmt: String) {
        orderId = (model.iOrderId ?? 0)
        orderNumber = model.vOrderNumber ?? ""
        // Apps Flyer Event
        TikTokBusiness.trackEvent("PURCHASE", withProperties: ["price": model.dOrderTotal?.toDouble() ?? 0.0, "content": "", "contentId": 0, "contentType": "", "quantity": 1, "orderId": orderId, "receiptId": orderId])
        Analytics.logEvent("PURCHASE", parameters: ["price": model.dOrderTotal?.toDouble() ?? 0.0, "content": "", "contentId": 0, "contentType": "", "quantity": 1, "orderId": orderId, "receiptId": orderId])
        AppsFlyerEvents.shared.afPurchase(price: model.dOrderTotal?.toDouble() ?? 0.0, content: "", contentId: 0, contentType: "", quantity: 1, orderId: orderId, receiptId: orderId)

        var walletUsedOrnot = UsedWalletOrNot.NotUsed.rawValue
        if flagSplitWallet == true {
            walletUsedOrnot = UsedWalletOrNot.Used.rawValue
        }
        
        if transactionType == TransactionType.Card.rawValue {
            self.payWithCard(model: model, flagCardOrNot: flagCardOrNot, strCardToken: strCardToken, strCardTransactionReference: strCardTransactionReference, flagSplitWallet: flagSplitWallet, strRemainingAmt: strRemainingAmt)
        }
        else if transactionType == TransactionType.ApplePay.rawValue {
            self.payWithApplePay(model: model, flagSplitWallet: flagSplitWallet, strRemainingAmt: strRemainingAmt)
        }
        else if transactionType == TransactionType.COD.rawValue {
            self.updateTransaction(strTransactionReference: TransactionTypeCOD, paymentStatus: 0, strCardNumber: "", strCardType: "", strCardScheme: "", strCardToken: "", transactionType: TransactionType.COD.rawValue, walletUsedOrNot: walletUsedOrnot)
        }
        else if transactionType == TransactionType.Wallet.rawValue {
            self.updateTransaction(strTransactionReference: TransactionTypeWallet, paymentStatus: 1, strCardNumber: "", strCardType: "", strCardScheme: "", strCardToken: "", transactionType: TransactionType.Wallet.rawValue, walletUsedOrNot: walletUsedOrnot)
        }
        else if transactionType == TransactionType.Coins.rawValue {
            self.updateTransaction(strTransactionReference: TransactionTypeCoins, paymentStatus: 1, strCardNumber: "", strCardType: "", strCardScheme: "", strCardToken: "", transactionType: TransactionType.Coins.rawValue, walletUsedOrNot: walletUsedOrnot)
        } else if transactionType == TransactionType.Tabby.rawValue {
            self.updateTransaction(strTransactionReference: InitiateTabby, paymentStatus: 0, strCardNumber: "", strCardType: "", strCardScheme: "", strCardToken: "", transactionType: TransactionType.Tabby.rawValue, walletUsedOrNot: walletUsedOrnot)
        }
    }

    func showPaymentPopup() {
        let vc = PaymentsStoryboard.instantiate(ChoosePaymentViewController.self)
        vc.modalPresentationStyle = .overFullScreen
        vc.modalTransitionStyle = .crossDissolve
        
        vc.grandTotal = self.grandTotal
        self.setLogOfInitiatedCheckout()
        
        vc.passSelectedCardData = { [weak self] (cardToken, cardTransactionReference, flag) in
            DispatchQueue.main.async {
                self?.splitPaymentOrNot = 0
                self?.presenterCheckout?.apiCallCreateOrder(dictData: self?.dictParam ?? [:], transactionType: TransactionType.Card.rawValue, flagCardOrNot: flag, strCardToken: cardToken, strCardTransactionReference: cardTransactionReference, flagSplitWallet: false, strRemainingAmt: "", isreward: self?.isreward ?? false, rewardamount: self?.rewardAmount ?? 0.0, isCartIncludeBundle: self?.isCartIncludeBundle ?? 0, expectedTime: self?.deliverySelectedDate)
            }
        }
        
        vc.applePaySelected = { [weak self] () in
            DispatchQueue.main.async {
                self?.splitPaymentOrNot = 0
                self?.presenterCheckout?.apiCallCreateOrder(dictData: self?.dictParam ?? [:], transactionType: TransactionType.ApplePay.rawValue, flagCardOrNot: false, strCardToken: "", strCardTransactionReference: "", flagSplitWallet: false, strRemainingAmt: "", isreward: self?.isreward ?? false, rewardamount: self?.rewardAmount ?? 0.0, isCartIncludeBundle: self?.isCartIncludeBundle ?? 0, expectedTime: self?.deliverySelectedDate)
            }
        }

        vc.codSelected = { [weak self] () in
            DispatchQueue.main.async {
                self?.presenterCheckout?.apiCallCreateOrder(dictData: self?.dictParam ?? [:], transactionType: TransactionType.COD.rawValue, flagCardOrNot: false, strCardToken: "", strCardTransactionReference: "", flagSplitWallet: false, strRemainingAmt: "", isreward: self?.isreward ?? false, rewardamount: self?.rewardAmount ?? 0.0, isCartIncludeBundle: self?.isCartIncludeBundle ?? 0, expectedTime: self?.deliverySelectedDate)
            }
        }

        vc.walletSelected = { [weak self] () in
            DispatchQueue.main.async {
                self?.presenterCheckout?.apiCallCreateOrder(dictData: self?.dictParam ?? [:], transactionType: TransactionType.Wallet.rawValue, flagCardOrNot: false, strCardToken: "", strCardTransactionReference: "", flagSplitWallet: false, strRemainingAmt: "", isreward: self?.isreward ?? false, rewardamount: self?.rewardAmount ?? 0.0, isCartIncludeBundle: self?.isCartIncludeBundle ?? 0, expectedTime: self?.deliverySelectedDate)
            }
        }

        vc.splitWalletSelected = { [weak self] (cardToken, cardTransactionReference, flag, strPaymentType, strRemainingAmountAfterWallet) in
            DispatchQueue.main.async {
                
                self?.splitPaymentOrNot = 1
                
                if strPaymentType == "Card" {
                    self?.presenterCheckout?.apiCallCreateOrder(dictData: self?.dictParam ?? [:], transactionType: TransactionType.Card.rawValue, flagCardOrNot: flag, strCardToken: cardToken, strCardTransactionReference: cardTransactionReference, flagSplitWallet: true, strRemainingAmt: strRemainingAmountAfterWallet, isreward: self?.isreward ?? false, rewardamount: self?.rewardAmount ?? 0.0, isCartIncludeBundle: self?.isCartIncludeBundle ?? 0, expectedTime: self?.deliverySelectedDate)
                }
                else if strPaymentType == "COD" {
                    self?.presenterCheckout?.apiCallCreateOrder(dictData: self?.dictParam ?? [:], transactionType: TransactionType.COD.rawValue, flagCardOrNot: false, strCardToken: "", strCardTransactionReference: "", flagSplitWallet: true, strRemainingAmt: strRemainingAmountAfterWallet, isreward: self?.isreward ?? false, rewardamount: self?.rewardAmount ?? 0.0, isCartIncludeBundle: self?.isCartIncludeBundle ?? 0, expectedTime: self?.deliverySelectedDate)
                }
                else if strPaymentType == "ApplePay" {
                    self?.presenterCheckout?.apiCallCreateOrder(dictData: self?.dictParam ?? [:], transactionType: TransactionType.ApplePay.rawValue, flagCardOrNot: false, strCardToken: "", strCardTransactionReference: "", flagSplitWallet: true, strRemainingAmt: strRemainingAmountAfterWallet, isreward: self?.isreward ?? false, rewardamount: self?.rewardAmount ?? 0.0, isCartIncludeBundle: self?.isCartIncludeBundle ?? 0, expectedTime: self?.deliverySelectedDate)
                }
            }
        }
        
        vc.tabbySelected = { [weak self] () in
            DispatchQueue.main.async {
                // tabby payment
                self?.presenterCheckout?.apiCallCreateOrder(dictData: self?.dictParam ?? [:], transactionType: TransactionType.Tabby.rawValue, flagCardOrNot: false, strCardToken: "", strCardTransactionReference: "", flagSplitWallet: false, strRemainingAmt: "", isreward: self?.isreward ?? false, rewardamount: self?.rewardAmount ?? 0.0, isCartIncludeBundle: self?.isCartIncludeBundle ?? 0, expectedTime: self?.deliverySelectedDate)
            }
        }

        self.presentVC(vc)
    }
    
    private func setLogOfInitiatedCheckout() {
        // set log of afInitiatedCheckout
        TikTokBusiness.trackEvent("CHECKOUT", withProperties: ["price": grandTotal.toDouble() ?? 0.0, "quantity": 1, "orderId": orderId, "recieptId": orderId])
        Analytics.logEvent("CHECKOUT", parameters: ["price": grandTotal.toDouble() ?? 0.0, "quantity": 1, "orderId": orderId, "recieptId": orderId])
        AppsFlyerEvents.shared.afInitiatedCheckout(price: self.grandTotal.toDouble() ?? 0.0, content: "", contentId: 0, contentType: "", quantity: 1, orderId: orderId, receiptId: orderId)
    }
        
    func showThankyouAlert(strCardNumber: String, strCardType: String, strCardScheme: String, strCardToken: String, strTransactionId: String, strLatestWalletBalance: String) {
        
        var strPromo = ""
        if iPromocodeUseId == 0 {  // promo code not applied
            strPromo = ""
        }
        else { // promo code is applied
            strPromo = txtPromoCode.text ?? ""
        }
        
        // Mixpanel
        Mixpanel.mainInstance().track(event: ScreenNames.CreateOrder.rawValue, properties: [
            "user_id": "\(User.shared.iUserId ?? 0)",
            "order_number": orderNumber,
            "promocode": strPromo,
            "order_total": orderAmount
        ])
        
        // product vise mixpanel event
        for objProduct in self.arrCart {
            var product_price = ""
            if objProduct.dDiscountedTotalProductPrice ?? "" == "0" { // show price in green color
                product_price = "\(objProduct.dDiscountedTotalProductPrice ?? "")"
            }
            else {
                // discount logic
                if objProduct.dDiscountAmount == "" {   // no discount
                    product_price = "\(objProduct.totalProductPriceWithVAT ?? "")"
                }
                else {  // discount is there
                    product_price = "\(objProduct.dDiscountedTotalProductPrice ?? "")"
                }
            }

            Mixpanel.mainInstance().track(event: ScreenNames.ProductPurchase.rawValue, properties: [
                "user_id": "\(User.shared.iUserId ?? 0)",
                "order_number": orderNumber,
                "product_name": objProduct.vProductName,
                "product_quantity": objProduct.iProductQuantity,
                "product_price": product_price,
                "order_total": orderAmount
            ])
        }
        
        // save wallet balance
        let WalletBalance = Double(strLatestWalletBalance) ?? 0
        let strWalletBalance = "\(forTrailingZero(temp: WalletBalance))"
        UserDefaults.standard.set(strWalletBalance, forKey: USERDEFAULTS_INFO_KEY.WALLET_BALANCE)
        // save WALLET_BALANCE_EN_NUMBER for english numbers 
        let strWalletBalanceEnNumbers = "\(forTrailingZeroEnglishNumbersOnly(temp: Double(strLatestWalletBalance) ?? 0))"
        UserDefaults.standard.set(strWalletBalanceEnNumbers, forKey: USERDEFAULTS_INFO_KEY.WALLET_BALANCE_EN_NUMBER)
        UserDefaults.standard.synchronize()

        // call add card api in background
        if strCardToken != "" {
            self.addUserCard(strCardNumber: strCardNumber, strCardType: strCardType, strCardScheme: strCardScheme, strCardToken: strCardToken, strTransactionId: strTransactionId)
        }
        
        let vc = ProductPopupStoryboard.instantiate(ThankYouViewController.self)
        vc.modalPresentationStyle = .overFullScreen
        vc.modalTransitionStyle = .crossDissolve
        vc.completionBlock = { [weak self] (strAction) in
            guard let self = self else { return }
            self.dismiss(animated: false, completion: nil)
                let vc = homeStoryboard.instantiate(CongratsRewardsVC.self)
                vc.modalPresentationStyle = .overFullScreen
                vc.modalTransitionStyle = .crossDissolve
            vc.completionBlock = {
                [weak self] (strAction) in
                guard let self = self else { return }
                self.dismiss(animated: false, completion: nil)
                if strAction == "Complete" {
                    NotificationCenter.default.post(name: NSNotification.Name("RemoveCartBadge"), object: nil)
                    NotificationCenter.default.post(name: NSNotification.Name("RemoveCartData"), object: nil)
                    self.tabBarController?.selectedIndex = 3
                }
            }
                self.present(vc, animated: true)
        }
        self.presentVC(vc)
    }
    
    private func addUserCard(strCardNumber: String, strCardType: String, strCardScheme: String, strCardToken: String, strTransactionId: String) {
        var dictParam : [String:Any] = [:]
        dictParam["vToken"] = strCardToken
        dictParam["vTransactionId"] = strTransactionId
        dictParam["vPaymentDescription"] = strCardNumber
        dictParam["vCardType"] = strCardType
        dictParam["vCardScheme"] = strCardScheme

        if AppSingletonObj.isConnectedToNetwork(){
            TikTokBusiness.trackEvent("ADD_NEW_CAR", withProperties: dictParam)
            Analytics.logEvent("ADD_NEW_CAR", parameters: dictParam)
            self.presenterCheckout?.apiCallForAddCard(dictData: dictParam)
        }
    }

    func displayErrorAlert(strMsg:String) {
        AppSingleton.shared.showCustomPopUpWithOkButton(strButtonTitle: ObjKeymessages.kLABEL_OK, strMessage: strMsg, showOnTopVC: false) { (isOk) in
            if isOk == true {
                self.popVC()
            }
        }
    }

    func displayErrorToast(strMsg:String) {
        AppSingletonObj.showAlert(strMessage: strMsg)
    }

    func displayErrorAlertAndGoToHome(strMsg:String) {
        AppSingleton.shared.showCustomPopUpWithOkButton(strButtonTitle: ObjKeymessages.kLABEL_OK, strMessage: strMsg, showOnTopVC: false) { (isOk) in
            if isOk == true {
                NotificationCenter.default.post(name: NSNotification.Name("RemoveCartBadge"), object: nil)
                NotificationCenter.default.post(name: NSNotification.Name("RemoveCartData"), object: nil)
                self.tabBarController?.selectedIndex = 3
            }
        }
    }
    
    func startPaymentWithTabbyByURL(url: String) {
        let vc = PaymentsByUrlVC(url: url, paymentCallback: {[weak self] statusPayment, message in
            debugPrint("tabby payment status : \(statusPayment)")
            Task {
                if statusPayment == true {
                    self?.showThankyouAlert(strCardNumber: "", strCardType: "", strCardScheme: "", strCardToken: "", strTransactionId: "", strLatestWalletBalance: "")
                } else {
                    self?.displayErrorAlertAndGoToHome(strMsg: message)
                }
            }
        })
        vc.modalTransitionStyle = .crossDissolve
        vc.modalPresentationStyle = .fullScreen
        present(vc, animated: true)
    }

    func promoCodeSuccess(model: PromocodeCheckResponseFields, strMsg: String) {
        objPromoCodeData = model
        iPromocodeUseId = model.iPromocodeUseId ?? 0
        txtPromoCode.isUserInteractionEnabled = false
        btnApply.setTitle(ObjKeymessages.kLABEL_REMOVE, for: .normal)
        self.newdVatCost = model.dVatCost?.toDouble() ?? 0.0
        self.dDiscountAmount = model.dDiscountAmount?.toDouble() ?? 0.0
        AppSingletonObj.showAlert(strMessage: strMsg)
        self.updateCostUI()
    }
    
    func removePromoCodeSuccess(strMsg: String) {
        self.dDiscountAmount = 0.0
        self.newdVatCost = 0.0
        iPromocodeUseId = 0
        txtPromoCode.text = ""
        txtPromoCode.isUserInteractionEnabled = true
        btnApply.setTitle(ObjKeymessages.kLABEL_APPLY, for: .normal)
        AppSingletonObj.showAlert(strMessage: strMsg)
        stackViewDiscount.isHidden = true
        stackViewDeliveryDiscount.isHidden = true
        self.calculateCosting(model: objCartData ?? GetCartListResponseFields())
    }

    func refreshCountryData(arrCountryData : [CountryListResponseFields], flagNoNetwork: Bool) {
        
        arrCountry = arrCountryData
        
        var countryCodeJson = [] as [[String:Any]]
        var dictAnswer: [String: Any] = [:]

        for obj in arrCountryData {
            dictAnswer["en"] = obj.vCountryName
            dictAnswer["es"] = obj.vCountryName
            dictAnswer["zh"] = obj.vCountryName
            dictAnswer["locale"] = obj.vImage
            dictAnswer["code"] = obj.vDialingCode
           
            countryCodeJson.append(dictAnswer)
        }
        
        CountryCodeJson = countryCodeJson

        if flagNoNetwork == true {  // Open country picker
            if arrCountry.count > 0 {  // Do not have country list, call API
                let countryView = CountrySelectView()
                countryView.show()
                countryView.selectedCountryCallBack = {[weak self] (countryDic) -> Void in
                    self?.countryDictSetUp(countryDic: countryDic)
                }
            }
        }
    }

    func displayAlertForFailedCaseAndGoToCurrentOrders(strMsg: String) {
        AppSingleton.shared.showCustomPopUpWithOkButton(strButtonTitle: ObjKeymessages.kLABEL_OK, strMessage: strMsg, showOnTopVC: false) { (isOk) in
            if isOk == true {
                NotificationCenter.default.post(name: NSNotification.Name("RemoveCartBadge"), object: nil)
                NotificationCenter.default.post(name: NSNotification.Name("RemoveCartData"), object: nil)
                self.tabBarController?.selectedIndex = 3
            }
        }
    }

    func showAlertAndReload(strMessage :  String) {
        // add items into cart again, call add to cart API
        self.addItemsToCart()
    }

    func displayAlertAndDismiss(string: String, cartCount: Int) {
        // update my cart button count
        group?.leave()
    }

    func notifyToLeaveDispatchGroup() {
        group?.leave()
    }
    
    func addItemsToCart() {
        self.view.endEditing(true)
        group = nil
        group = DispatchGroup()

        DispatchQueue.main.async {
            ActivityIndicator.shared.showCentralSpinner()
        }

        for obj in arrCart {
            if obj.dDiscountedTotalProductPrice == "0" { // free product
            }
            else {
                var dictParam : [String:Any] = [:]
                dictParam["biProductId"] = obj.biProductId
                dictParam["tiIsCheck"] = AddToCartIsCheck.Add.rawValue
                dictParam["iProductQuantity"] = obj.iProductQuantity
                dictParam["dbPrice"] = obj.dbPrice
                dictParam["bunddelId"] = 0
                for bundle in self.arrBundles {
                    dictParam["biProductId"] = 0
                    dictParam["tiIsCheck"] = AddToCartIsCheck.Add.rawValue
                    dictParam["iProductQuantity"] = obj.iProductQuantity
                    dictParam["dbPrice"] = obj.dbPrice
                    dictParam["bunddelId"] = bundle.bunddelId ?? 0
                }
                dictParam["isMosques"] = "\(obj.isMosques ?? 0)"
                if AppSingletonObj.isConnectedToNetwork(){
                    group?.enter()
                    TikTokBusiness.trackEvent("ADD_ITEMS_TO_CART", withProperties: dictParam)
                    Analytics.logEvent("ADD_ITEMS_TO_CART", parameters: dictParam)
                    self.presenterCheckout?.apiCallForAddToCart(dictData: dictParam)
                }
            }
        }
        
        group?.notify(queue: .main) { [weak self] in
            DispatchQueue.main.async {
                ActivityIndicator.shared.hideCentralSpinner()
            }
            self?.popVC()
            self?.group = nil
        }
    }

    func getCalculateUserPointsToSAR(model: CalculateUserPointsToSARResponseFields) {
        Constant.shared.USER_POINTS_BY_SAR = model.userPointsBySAR ?? 0.0
    }
    
    func getCalculateUserAmountToPoints(model: convertSARToPointsResponseFields) {
        Constant.shared.CONGRATS_COINS = model.get ?? 0
        let congratsCoins = "You earn up to material coins.".localized.replacingOccurrences(of: "%2d", with: "\(forTrailingZero(temp: Constant.shared.CONGRATS_COINS))")
        lblCongratsValue.text = congratsCoins
        Constant.shared.USED_COINS = model.pay ?? 0
        let usePoints = "You will use points".localized.replacingOccurrences(of: "%2d", with: "\(Constant.shared.USED_COINS)")
        lblUsePoints.text = usePoints
    }
    
    func getShiftsBySelectDate(shifts: [GetCartShiftResponseFields]?) {
        debugPrint(shifts)
        self.arrShiftsBySelectDate = shifts ?? []
        if shifts?.count ?? 0 >= 1 {
            self.selectedShiftBySelectedDateIndex = 0
        } else {
            self.selectedShiftBySelectedDateIndex = -1
        }
        self.isShowShiftsSelectedDate(isCollectionViewHidden: false)
        self.view.updateConstraints()
        self.view.updateFocusIfNeeded()
        collectionViewShiftsByDate.reloadData()
    }
    
    private func isShowShiftsSelectedDate(isCollectionViewHidden: Bool) {
        self.collectionViewShiftsByDate.isHidden = isCollectionViewHidden
        self.constraintShecduleShift.constant = isCollectionViewHidden ? 2.0 : 60
    }
}

extension CheckoutViewController : AddressListProtocol {
    func removeSelectedAddress() {
        TikTokBusiness.trackEvent("remove_address")
        Analytics.logEvent("remove_address", parameters: nil)
        setCurrentAddress()
    }
    
    
    func addOREditAddress(address:AddressResponseFields?) {
        lblType.text = address?.vType
        lblAddress.text = address?.txAddress
        addressId = address?.iAddressId ?? 0
        CheckoutViewModel.shared.setAddress(addressVtype: address?.vType ?? "", addressTxt: address?.txAddress ?? "", iAddressId: address?.iAddressId ?? 0)
        self.isMosqueAddress = address?.isMosques ?? 0 == 0 ? false : true
        if self.isMosqueAddress {
            self.imgMosqueIcon.isHidden = false
        } else {
            self.imgMosqueIcon.isHidden = true
        }
        if let address = address,let dict = address.dictionary {
            TikTokBusiness.trackEvent("change_add_address_loaction", withProperties: dict)
            Analytics.logEvent("change_add_address_loaction", parameters: dict)
            UserDefaults.standard.set(dict, forKey: USERDEFAULTS_INFO_KEY.CURRENT_ADDRESS)
            UserDefaults.standard.synchronize()
        }
    }
}

// PayTabs
extension CheckoutViewController {
    
    func payWithCard(model: CreateOrderResponseFields, flagCardOrNot: Bool, strCardToken: String, strCardTransactionReference: String, flagSplitWallet: Bool, strRemainingAmt: String) {
        
        var strAddress = ""
        var strCity = ""
        var strZipcode = ""
        var strState = ""
        var strCountryCode = ""

        if let address = UserDefaults.standard.object(forKey: USERDEFAULTS_INFO_KEY.CURRENT_ADDRESS) as? [String:Any],let data = address.data {
            let decodeResult: (decodableObj: AddressResponseFields?, error: Error?) = CodableHelper.decode(AddressResponseFields.self, from: data)
            if let decodeObj = decodeResult.decodableObj {
                strAddress = decodeObj.txAddress ?? ""
                strCity = decodeObj.vCity ?? ""
                strZipcode = decodeObj.vZipCode ?? ""
                strState = decodeObj.vState ?? ""
                strCountryCode = decodeObj.vCountryCode ?? ""

            }
        }

        var billingDetails: PaymentSDKBillingDetails! {
            return PaymentSDKBillingDetails(name: User.shared.vName,
                                         email: User.shared.vEmailId,
                                            phone: "\(User.shared.vISDCode ?? "")\(User.shared.vMobileNumber ?? "")",
                                         addressLine: strAddress,
                                         city: strCity,
                                         state: strState,
                                         countryCode: strCountryCode,
                                         zip: strZipcode)
        }

        var configuration: PaymentSDKConfiguration! {
            let theme = PaymentSDKTheme.default
            theme.logoImage = UIImage(named: "logo_login_white")
            theme.secondaryColor = UIColor.AppTheme_BlueColor_012CDA
            theme.secondaryFontColor = UIColor.AppTheme_BlueColor_012CDA
            theme.primaryFontColor = .black
            theme.strokeColor = UIColor.AppTheme_BlueColor_012CDA
            theme.buttonColor = UIColor.AppTheme_BlueColor_012CDA
            theme.titleFontColor = .black
            theme.buttonFontColor = .white

            var strToken = ""
            var strTransactionReference = ""
            if flagCardOrNot == false {
                strToken = ""
                strTransactionReference = ""
            }
            else {
                strToken = strCardToken
                strTransactionReference = strCardTransactionReference
            }
            
            // check for wallet utilisation
            orderAmount = Double(model.dOrderTotal ?? "") ?? 0.0
            if flagSplitWallet == true {  // split payment
                orderAmount = Double(strRemainingAmt) ?? 0.0
            }
            
            return PaymentSDKConfiguration(profileID: profileID,
                                           serverKey: serverKey,
                                           clientKey: clientKey,
                                           currency: SACurrencyCode,
                                           amount: orderAmount,
                                           merchantCountryCode: MerchantCountryCode)
                .cartDescription("cart description")
                .cartID(model.vOrderNumber ?? "")
                .screenTitle(AppName)
                .theme(theme)
                .showBillingInfo(true)
                .hideCardScanner(true)
                .languageCode(UserDefaults.standard.getLanguage() ?? "")
                .tokeniseType(.userOptinoal)
                .tokenFormat(.hex32)
                .token(strToken)
                .transactionReference(strTransactionReference)
                .billingDetails(billingDetails)
        }


        PaymentManager.startCardPayment(on: self, configuration: configuration,
                                 delegate: self)

    }
    
    func payWithApplePay(model: CreateOrderResponseFields, flagSplitWallet: Bool, strRemainingAmt: String) {
        
        var strAddress = ""
        var strCity = ""
        var strZipcode = ""
        var strState = ""
        var strCountryCode = ""

        if let address = UserDefaults.standard.object(forKey: USERDEFAULTS_INFO_KEY.CURRENT_ADDRESS) as? [String:Any],let data = address.data {
            let decodeResult: (decodableObj: AddressResponseFields?, error: Error?) = CodableHelper.decode(AddressResponseFields.self, from: data)
            if let decodeObj = decodeResult.decodableObj {
                strAddress = decodeObj.txAddress ?? ""
                strCity = decodeObj.vCity ?? ""
                strZipcode = decodeObj.vZipCode ?? ""
                strState = decodeObj.vState ?? ""
                strCountryCode = decodeObj.vCountryCode ?? ""

            }
        }

        var strEmail = ""
        if User.shared.vEmailId == "" {  // use default email id
            strEmail = DefaultEmailForApplePay
        }
        else {
            strEmail = User.shared.vEmailId ?? ""
        }
        
        var billingDetails: PaymentSDKBillingDetails! {
            return PaymentSDKBillingDetails(name: User.shared.vName,
                                         email: strEmail,
                                            phone: "\(User.shared.vISDCode ?? "")\(User.shared.vMobileNumber ?? "")",
                                         addressLine: strAddress,
                                         city: strCity,
                                         state: strState,
                                         countryCode: strCountryCode,
                                         zip: strZipcode)
        }

        var shippingDetails: PaymentSDKShippingDetails! {
            return PaymentSDKShippingDetails(name: User.shared.vName,
                                             email: strEmail,
                                             phone: "\(User.shared.vISDCode ?? "")\(User.shared.vMobileNumber ?? "")",
                                             addressLine: strAddress,
                                             city: strCity,
                                             state: strState,
                                             countryCode: strCountryCode,
                                             zip: strZipcode)
        }
        
        // check for wallet utilisation
        orderAmount = Double(model.dOrderTotal ?? "") ?? 0.0
        if flagSplitWallet == true {  // split payment
            orderAmount = Double(strRemainingAmt) ?? 0.0
        }
        
        var applePayConfiguration: PaymentSDKConfiguration! {
            return PaymentSDKConfiguration(profileID: profileID,
                                           serverKey: serverKey,
                                           clientKey: clientKey,
                                           currency: SACurrencyCode,
                                           amount: orderAmount,
                                           merchantCountryCode: MerchantCountryCode)
                .cartDescription("cart description ApplePay")
                .cartID(model.vOrderNumber ?? "")
                .screenTitle(AppName)
                .languageCode(UserDefaults.standard.getLanguage() ?? "")
                .merchantName("Material")
                .merchantAppleBundleID(PAYTABS_MERCHANT_IDENTIFIER)
                .simplifyApplePayValidation(true)
                .billingDetails(billingDetails)
                .shippingDetails(shippingDetails)

        }

        PaymentManager.startApplePayPayment(on: self,
                                     configuration: applePayConfiguration,
                                     delegate: self)

    }

}

extension CheckoutViewController: PaymentManagerDelegate {
    
    func paymentManager(didFinishTransaction transactionDetails: PaymentSDKTransactionDetails?, error: Error?) {
        if let transactionDetails = transactionDetails {
            var trans_type = 0
            var strCardScheme = ""
            if transactionDetails.cartDescription == "cart description ApplePay" {  // apple pay
                trans_type = 2
                strCardScheme = transactionDetails.paymentInfo?.paymentDescription ?? ""
            }
            else {  // card
                trans_type = 1
                strCardScheme = transactionDetails.paymentInfo?.cardScheme ?? ""
            }
            
            if transactionDetails.isSuccess() {
                print("Successful transaction")
                
                // call update transaction api for successful transaction
                self.updateTransaction(strTransactionReference: (transactionDetails.transactionReference ?? ""), paymentStatus: 1, strCardNumber: (transactionDetails.paymentInfo?.paymentDescription ?? ""), strCardType: (transactionDetails.paymentInfo?.cardType ?? ""), strCardScheme: strCardScheme, strCardToken: (transactionDetails.token ?? ""), transactionType: trans_type, walletUsedOrNot: splitPaymentOrNot)

            }
            else {
                // call update transaction api for failed transaction
                self.updateTransaction(strTransactionReference: (transactionDetails.transactionReference ?? ""), paymentStatus: 2, strCardNumber: (transactionDetails.paymentInfo?.paymentDescription ?? ""), strCardType: (transactionDetails.paymentInfo?.cardType ?? ""), strCardScheme: strCardScheme, strCardToken: (transactionDetails.token ?? ""), transactionType: trans_type, walletUsedOrNot: 0)
            }
        } else if let error = error {
            // call update transaction api for failed transaction
            self.updateTransaction(strTransactionReference: (""), paymentStatus: 2, strCardNumber: (""), strCardType: (""), strCardScheme: "", strCardToken: "", transactionType: 0, walletUsedOrNot: 0)
        }
    }
    
    func paymentManager(didCancelPayment error: Error?) {
        NotificationCenter.default.post(name: NSNotification.Name("RemoveCartBadge"), object: nil)
        NotificationCenter.default.post(name: NSNotification.Name("RemoveCartData"), object: nil)
        
        // call delete order API
        self.deleteOrder()
    }
    
    func deleteOrder() {
        var dictParam : [String:Any] = [:]
        dictParam["iOrderId"] = orderId

        if AppSingletonObj.isConnectedToNetwork(){
            TikTokBusiness.trackEvent("CANCEL_ORDER", withProperties: dictParam)
            Analytics.logEvent("CANCEL_ORDER", parameters: dictParam)
            self.presenterCheckout?.apiCallForCancelOrder(dictData: dictParam)
        }
    }
    
    func showError(message: String) {
        DispatchQueue.main.async {
            AppSingleton.shared.showCustomPopUpWithOkButton(strButtonTitle: ObjKeymessages.kLABEL_OK, strMessage: message, showOnTopVC: false) { (isOk) in
            }
        }
    }

    func updateTransaction(strTransactionReference: String, paymentStatus: Int, strCardNumber: String, strCardType: String, strCardScheme: String, strCardToken: String, transactionType: Int, walletUsedOrNot: Int) {
        
        if AppSingletonObj.isConnectedToNetworkForCheckoutScreen() {
            var dictParam : [String:Any] = [:]
            dictParam["iOrderId"] = self.orderId
            dictParam["tiTransactionType"] = transactionType
            dictParam["vTransactionRef"] = strTransactionReference
            dictParam["iPaymentStatus"] = paymentStatus
            dictParam["vCardName"] = strCardScheme

            TikTokBusiness.trackEvent("UPDATE_TRANSACTION", withProperties: dictParam)
            Analytics.logEvent("UPDATE_TRANSACTION", parameters: dictParam)
            self.presenterCheckout?.apiCallForUpdateTransaction(dictData: dictParam, paymentStatus: paymentStatus, strCardNumber: strCardNumber, strCardType: strCardType, strCardScheme: strCardScheme, strCardToken: strCardToken, walletUsedOrNot: walletUsedOrNot)
        }
        else {
            self.displayAlertForFailedCaseAndGoToCurrentOrders(strMsg: ObjKeymessages.kMSG_NO_INTERNET)
        }
    }
}

extension Double {
    /// Rounds the double to decimal places value
    func rounded(toPlaces places:Int) -> Double {
        let divisor = pow(10.0, Double(places))
        return (self * divisor).rounded() / divisor
    }
}

extension CheckoutViewController : UITextFieldDelegate {
    func textField(_ textField: UITextField, shouldChangeCharactersIn range: NSRange, replacementString string: String) -> Bool {
        
        if textField == txtPhoneNumber {
            let newLength: Int = (textField.text?.length)! + string.length - range.length
            if newLength > MaxMoNoLength {
                return false
            }
            return true
        }
        else {
            return true
        }
    }

}

extension CheckoutViewController: UICollectionViewDelegate, UICollectionViewDataSource, UICollectionViewDelegateFlowLayout {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        if collectionView == self.collectionViewShifts {
            return arrShifts.count
        } else {
            if self.arrShiftsBySelectDate.count == 0 {
                collectionView.setEmptyMessage("please_choose_another_day".localized)
            } else {
                collectionView.setEmptyMessage("")
            }
         return self.arrShiftsBySelectDate.count // + 1 for select date
         
        }
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        if collectionView == self.collectionViewShifts && self.arrShifts.count > indexPath.row {
            let cell = collectionView.dequeue(with: CheckoutShiftsCell.self, for: indexPath)
            if UserDefaults.standard.getLanguage()! == UserAPI.VLanguage_userLanguage.en.rawValue {
                cell.lblShiftTime.text = arrShifts[indexPath.row].vShiftDisplayNameEn
            }
            else {
                cell.lblShiftTime.text = arrShifts[indexPath.row].vShiftDisplayNameAr
            }
            cell.cornerRadius = 10
            if selectedShiftIndex == indexPath.row {  // show category selected
                cell.borderWidth = 1
                cell.borderColor = .AppTheme_BlueColor_012CDA
            }
            else {
                cell.borderWidth = 1
                cell.borderColor = .AppTheme_DarkGrayColor_A09F9F
            }
            cell.btnCheckoutShift.tag = indexPath.row
            if self.arrShifts[indexPath.row].isShiftAvailable == false {
                cell.isUserInteractionEnabled = false
                cell.lblShiftTime.textColor = .AppTheme_DarkGrayColor_A09F9F
            } else {
                cell.isUserInteractionEnabled = true
                cell.lblShiftTime.textColor = .AppTheme_DiffBlackColor_101113
                cell.btnCheckoutShift.addTarget(self, action: #selector(self.checkoutShiftsBySelectDateCellAction(_:)), for: .touchUpInside)
            }
            return cell
        } else {
            let cell = collectionView.dequeue(with: ShiftsBySelectDateCell.self, for: indexPath)
            if UserDefaults.standard.getLanguage()! == UserAPI.VLanguage_userLanguage.en.rawValue {
                cell.lblShiftTitle.text = arrShiftsBySelectDate[indexPath.row].vShiftDisplayNameEn
            }
            else {
                cell.lblShiftTitle.text = arrShiftsBySelectDate[indexPath.row].vShiftDisplayNameAr
            }
            cell.lblShiftTime.text = "\(ObjKeymessages.kLABEL_FROM) \(self.arrShiftsBySelectDate[indexPath.row].vStartAt ?? "") \(ObjKeymessages.kLABEL_TO) \(self.arrShiftsBySelectDate[indexPath.row].vCloseAt ?? "")."
            cell.cornerRadius = 12
            if selectedShiftBySelectedDateIndex == indexPath.row {  // show category selected
                cell.borderWidth = 1
                cell.borderColor = .AppTheme_BlueColor_012CDA
            }
            else {
                cell.borderWidth = 1
                cell.borderColor = .AppTheme_DarkGrayColor_A09F9F
            }
            if self.arrShiftsBySelectDate[indexPath.row].isDisabled == 1 {
                cell.lblShiftTitle.textColor = .AppTheme_GrayTextColor_767676
                cell.lblShiftTime.textColor = .AppTheme_GrayTextColor_767676
                cell.lblShiftAvailable.textColor = .AppTheme_RedColor_D82828
                cell.lblShiftAvailable.text = "not_available".localized
                cell.borderWidth = 1
                cell.borderColor = .AppTheme_DarkGrayColor_A09F9F
            } else {
                cell.lblShiftTitle.textColor = .AppTheme_DiffBlackColor_101113
                cell.lblShiftTime.textColor = .AppTheme_DiffBlackColor_101113
                cell.lblShiftAvailable.textColor = .AppTheme_DiscountGreenColor_05B13E
                cell.lblShiftAvailable.text = "available".localized
            }
            cell.btnCheckoutShift.tag = indexPath.row
            cell.btnCheckoutShift.addTarget(self, action: #selector(self.selectShiftsBySelectDateCellAction(_:)), for: .touchUpInside)
            return cell
        }
    }
    
    private func isShiftAvailable(vStartAt: String, orderCutOffTime: Int) -> Bool {
//        let startShiftTime1 = "01:00 PM"
        let currentTime = Date() //"1:20 PM"

        let formatter = DateFormatter()
        formatter.dateFormat = "h:mm a"

        let dd = formatter.string(for: currentTime)

        guard let date1 = formatter.date(from: vStartAt) else {return false}
        
        let date2 = formatter.date(from: dd ?? "")

        guard let elapsedTime = date2?.timeIntervalSince(date1) else {return false}

        // convert from seconds to hours, rounding down to the nearest hour
        let hours = floor(elapsedTime / 60 / 60) * 60
        
        debugPrint("hour ", hours)
        debugPrint("orderCutOffTime ", orderCutOffTime)
        if hours > Double(orderCutOffTime) {
            return true
        } else {
            return false
        }
    }
        
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        if collectionView == self.collectionViewShifts {
            return CGSize(width: 100, height: 25)
        } else if collectionView == self.collectionViewShiftsByDate {
            return CGSize(width: 150, height: 70)
        }
        else {
            return CGSize(width: 130, height: collectionViewShifts.h)
        }
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, minimumLineSpacingForSectionAt section: Int) -> CGFloat {
            return 5
    }
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        self.view.endEditing(true)
        if collectionView == self.collectionViewShifts {
            selectedShiftIndex = indexPath.row
            collectionViewShifts.reloadData()
        } else {
            selectedShiftBySelectedDateIndex = indexPath.row
            collectionViewShiftsByDate.reloadData()
        }
    }
    
    @objc func checkoutShiftsCellAction(_ sender: UIButton) {
        self.view.endEditing(true)
        selectedShiftIndex = sender.tag
        collectionViewShifts.reloadData()
        self.setDeliveryShiftTitleSelected()
    }
    
    @objc func checkoutShiftsBySelectDateCellAction(_ sender: UIButton) {
        self.view.endEditing(true)
        selectedShiftIndex = sender.tag
        if self.arrShifts[sender.tag].iShiftId == 3 {
            self.openChooseDeliveryDateVC()
            self.collectionViewShifts.reloadData()
        } else {
            if self.arrShifts[sender.tag].iShiftId == 3 {
                self.setShiftDisplayName(title: "select_date".localized)
            }
            collectionViewShifts.reloadData()
            if self.arrShifts[sender.tag].iShiftId == 1 {
                self.getShiftsDetailsByDate(Date())
            } else if self.arrShifts[sender.tag].iShiftId == 2 {
                self.getShiftsDetailsByDate(Date().addingTimeInterval(.days(1)))
            }
        }
    }
    
    @objc func selectShiftsBySelectDateCellAction(_ sender: UIButton) {
        self.view.endEditing(true)
        if self.arrShiftsBySelectDate[sender.tag].isDisabled == 1 {
            self.selectedShiftBySelectedDateIndex = -1
            self.showAlert(message: "shift_is_not_available".localized)
        } else {
            self.selectedShiftBySelectedDateIndex = sender.tag
        }
        self.collectionViewShiftsByDate.reloadData()
    }
    
}

// MARK: - Extension - ChooseDeliveryDateVCDelegate
extension CheckoutViewController: ChooseDeliveryDateVCDelegate {
    func getSelectedChooseDeliveryDate(date: String, selectedIndex: Int?, selectedDate: Date?) {
        self.setShiftDisplayName(title: selectedDate?.convertDateTimeToString(dateFormat: "MM-dd") ?? "")
        self.getShiftsDetailsByDate(selectedDate)
        self.deliveryShiftSelectedDate = date
        self.deliverySelectedDate = selectedDate
    }
    
    @objc private func getShiftsDetailsByDate(_ date: Date? = Date()) {
        self.deliverySelectedDate = date
        self.deliveryShiftSelectedDate = "\(date?.convertDateToStringByLocale() ?? Date().convertDateToStringByLocale())"
        self.selectedShiftBySelectedDateIndex = -1
        var dictParam : [String:Any] = [:]
        dictParam["offset"] = offset
        dictParam["shiftDate"] = date?.convertDateToString()
        if AppSingletonObj.isConnectedToNetwork() {
            self.presenterCheckout?.apiCallForGetShiftsByDate(dictData: dictParam)
        }
    }
    
    private func setShiftDisplayName(title: String) {
        if selectedShiftIndex == -1 { return }
        self.arrShifts[selectedShiftIndex].vShiftDisplayNameAr = title
        self.arrShifts[selectedShiftIndex].vShiftDisplayNameEn = title
        self.collectionViewShifts.reloadData()
    }
    
}

extension CheckoutViewController: TabbyPaymentResultDelegate {
    func checkPaymentResult(isPaymentDone: Bool) {
        if isPaymentDone {
            self.updateTransaction(strTransactionReference: TransactionTypeTabby, paymentStatus: 1, strCardNumber: "", strCardType: "", strCardScheme: "", strCardToken: "", transactionType: TransactionType.Tabby.rawValue, walletUsedOrNot: 0)
        } else {
            self.displayAlertForFailedCaseAndGoToCurrentOrders(strMsg: "payment_failed_please_try_again_or_change_type_of_pay".localized)
        }
    }
    
}
