
import UIKit

protocol CheckoutPresentationProtocol {
    func apiCallForGetCart(dictData:[String:Any])
    func apiResponseGetCart(response:GetCartResponse?,error:Error?)

    func checkValidation(dictData:[String:Any]) -> Bool
    func apiCallCreateOrder(dictData:[String:Any], transactionType: Int, flagCardOrNot: Bool, strCardToken: String, strCardTransactionReference: String, flagSplitWallet: Bool, strRemainingAmt: String, isreward: Bool, rewardamount: Double, isCartIncludeBundle: Int, expectedTime: Date?)
    func apiResponseCreateOrder(response:CreateOrderResponse?,error:Error?, transactionType: Int, flagCardOrNot: Bool, strCardToken: String, strCardTransactionReference: String, flagSplitWallet: Bool, strRemainingAmt: String)

    func apiCallForUpdateTransaction(dictData:[String:Any], paymentStatus: Int, strCardNumber: String, strCardType: String, strCardScheme: String, strCardToken: String, walletUsedOrNot: Int)
    func apiResponseUpdateTransaction(response:TransactionResponse?,error:Error?, paymentStatus: Int, strCardNumber: String, strCardType: String, strCardScheme: String, strCardToken: String, strTransactionId: String)

    func apiCallForAddCard(dictData:[String:Any])
    func apiResponseAddCard(response:CommonFields?,error:Error?)

    func apiCallApplyPromoCode(dictData:[String:Any])
    func apiResponseApplyPromoCode(response:PromocodeCheckResponse?,error:Error?)

    func apiCallRemovePromoCode(dictData:[String:Any])
    func apiResponseRemovePromoCode(response:CommonFields?,error:Error?)

    func apiCallForGetCountryList(flagNoNetwork: Bool)
    func apiResponseGetCountryList(response:CountryListResponse?,error:Error?, flagNoNetwork: Bool)

    func apiCallForCancelOrder(dictData:[String:Any])
    func apiResponseCancelOrder(response:CommonFields?,error:Error?)

    func apiCallForAddToCart(dictData:[String:Any])
    func apiResponseAddToCart(response:AddToCartResponse?,error:Error?)

    func apiCalculateUserPointsToSAR(points: Int?)
    func apiResponseForCallForCalculateUserPointsToSAR(response: CalculateUserPointsToSARResponse?, error: Error?)
    
    func apicalculateUserAmountToPoints(amount: Double, type: ConvertSARToPointsType)
    func apiResponseForCallForCalculateUserAmountToPoints(response: convertSARToPointsResponse?, error: Error?)
    
    func apiCallForGetShiftsByDate(dictData: [String:Any])
    func apiResponseGetShiftsByDate(response: GetCartResponse?, error:Error?)
}

class CheckoutPresenter: CheckoutPresentationProtocol {
    
    // MARK: Objects & Variables
    weak var viewControllerCheckout: CheckoutProtocol?
    var interactorCheckout: CheckoutInteractorProtocol?
    
    func checkValidation(dictData:[String:Any]) -> Bool {
        
        if setDataInString(dictData["vAlternativeISDCode"] as AnyObject).isBlank {
            AppSingletonObj.showAlert(strMessage: ObjKeymessages.kALERT_ISD_CODE)
            return false
        }
        else if setDataInString(dictData["vAlternativeMobileNumber"] as AnyObject).isBlank {
            AppSingletonObj.showAlert(strMessage: ObjKeymessages.kALERT_MOBILE_NUMBER)
            return false
        }
        else if setDataInString(dictData["vAlternativeMobileNumber"] as AnyObject).count < MinMoNoLength || setDataInString(dictData["vAlternativeMobileNumber"] as AnyObject).count > MaxMoNoLength {
            AppSingletonObj.showAlert(strMessage: ObjKeymessages.kALERT_MOBILE_NUMBER_TEN_DIGITS)
            return false
        }
        return true
    }

    func apiCallForGetCart(dictData:[String:Any]) {
        interactorCheckout?.apiCallForGetCart(dictData: dictData)
    }

    func apiResponseGetCart(response: GetCartResponse?, error: Error?) {
        if let error = error  {
            viewControllerCheckout?.displayAlert(string: error.localizedDescription)
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            return
        }
        
        if code == APIINVALIDAUTHORIZATIONCODE401 {
            AppSingletonObj.displaySessionExpiredAlert(strMsg: response.responseMessage ?? "")
            return
        }

        if code == APICODE400 {
            viewControllerCheckout?.displayAlert(string: response.responseMessage ?? "")
            return
        }
        
        guard let model = response.responseData,code == APISUCCESSCODE200  else {
            return
        }
        self.viewControllerCheckout?.refreshCart(model: model)
    }

    func apiCallCreateOrder(dictData:[String:Any], transactionType: Int, flagCardOrNot: Bool, strCardToken: String, strCardTransactionReference: String, flagSplitWallet: Bool, strRemainingAmt: String, isreward: Bool, rewardamount: Double, isCartIncludeBundle: Int, expectedTime: Date? = nil) {
        self.interactorCheckout?.apiCallForCreateOrder(dictData: dictData, transactionType: transactionType, flagCardOrNot: flagCardOrNot, strCardToken: strCardToken, strCardTransactionReference: strCardTransactionReference, flagSplitWallet: flagSplitWallet, strRemainingAmt: strRemainingAmt,isreward: isreward, rewardamount: rewardamount, isCartIncludeBundle: isCartIncludeBundle, expectedTime: expectedTime)
    }

    func apiResponseCreateOrder(response:CreateOrderResponse?,error:Error?, transactionType: Int, flagCardOrNot: Bool, strCardToken: String, strCardTransactionReference: String, flagSplitWallet: Bool, strRemainingAmt: String) {
        if let error = error  {
            AppSingletonObj.showAlert(strMsg: error.localizedDescription)
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            return
        }
        
        if code == APIINVALIDAUTHORIZATIONCODE401 {
            AppSingletonObj.displaySessionExpiredAlert(strMsg: response.responseMessage ?? "")
            return
        }

        if code == APICODE400 {
            AppSingletonObj.showAlert(strMsg: response.responseMessage ?? "")
            return
        }
        
        if code == APICODE203 {
            self.viewControllerCheckout?.displayErrorAlert(strMsg: response.responseMessage ?? "")
            return
        }

        guard let model = response.responseData,code == APISUCCESSCODE200  else {
            return
        }
        self.viewControllerCheckout?.makePaymentToPayTabs(model: model, transactionType: transactionType, flagCardOrNot: flagCardOrNot, strCardToken: strCardToken, strCardTransactionReference: strCardTransactionReference, flagSplitWallet: flagSplitWallet, strRemainingAmt: strRemainingAmt)
    }

    func apiCallForUpdateTransaction(dictData:[String:Any], paymentStatus: Int, strCardNumber: String, strCardType: String, strCardScheme: String, strCardToken: String, walletUsedOrNot: Int) {
        self.interactorCheckout?.apiCallForUpdateTransaction(dictData: dictData, paymentStatus: paymentStatus, strCardNumber: strCardNumber, strCardType: strCardType, strCardScheme: strCardScheme, strCardToken: strCardToken, walletUsedOrNot: walletUsedOrNot)
    }

    func apiResponseUpdateTransaction(response:TransactionResponse?,error:Error?, paymentStatus: Int, strCardNumber: String, strCardType: String, strCardScheme: String, strCardToken: String, strTransactionId: String) {
        if let error = error  {
            viewControllerCheckout?.displayAlertForFailedCaseAndGoToCurrentOrders(strMsg: error.localizedDescription)
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            return
        }
        
        if code == APIINVALIDAUTHORIZATIONCODE401 {
            AppSingletonObj.displaySessionExpiredAlert(strMsg: response.responseMessage ?? "")
            return
        }

        if code == APICODE400 {
            AppSingletonObj.showAlert(strMsg: response.responseMessage ?? "")
            return
        }
        
        if code == APICODE203 {
            if paymentStatus == 1 {  // success
                self.viewControllerCheckout?.displayErrorAlert(strMsg: response.responseMessage ?? "")
            }
            else {
                self.viewControllerCheckout?.displayErrorAlertAndGoToHome(strMsg: response.responseMessage ?? "")
            }
            return
        }

        guard let model = response.responseData,code == APISUCCESSCODE200  else {
            return
        }
        
        if let url = response.responseData?.paymentUrl {
            self.viewControllerCheckout?.startPaymentWithTabbyByURL(url: url)
            return
        }
        
        if paymentStatus == 1 || paymentStatus == 0 {  // success
            self.viewControllerCheckout?.showThankyouAlert(strCardNumber: strCardNumber, strCardType: strCardType, strCardScheme: strCardScheme, strCardToken: strCardToken, strTransactionId: strTransactionId, strLatestWalletBalance: model.latestWalletBalance ?? "")
        }

    }

    func apiCallForAddCard(dictData:[String:Any]) {
        self.interactorCheckout?.apiCallForAddCard(dictData: dictData)
    }

    func apiResponseAddCard(response:CommonFields?,error:Error?) {
        if let error = error  {
            viewControllerCheckout?.displayAlert(string: error.localizedDescription)
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            return
        }
        
        if code == APIINVALIDAUTHORIZATIONCODE401 {
            AppSingletonObj.displaySessionExpiredAlert(strMsg: response.responseMessage ?? "")
            return
        }

        if code == APICODE400 {
            viewControllerCheckout?.displayAlert(string: response.responseMessage ?? "")
            return
        }
    }

    func apiCallApplyPromoCode(dictData:[String:Any]) {
        self.interactorCheckout?.apiCallForApplyPromoCode(dictData: dictData)
    }

    func apiResponseApplyPromoCode(response:PromocodeCheckResponse?,error:Error?) {
        if let error = error  {
            AppSingletonObj.showAlert(strMsg: error.localizedDescription)
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            return
        }
        
        if code == APIINVALIDAUTHORIZATIONCODE401 {
            AppSingletonObj.displaySessionExpiredAlert(strMsg: response.responseMessage ?? "")
            return
        }

        if code == APICODE400 {
            AppSingletonObj.showAlert(strMessage: response.responseMessage ?? "")
            return
        }
        
        guard let model = response.responseData,code == APISUCCESSCODE200  else {
            return
        }
        self.viewControllerCheckout?.promoCodeSuccess(model: model, strMsg: response.responseMessage ?? "")
    }

    func apiCallRemovePromoCode(dictData:[String:Any]) {
        self.interactorCheckout?.apiCallForRemovePromoCode(dictData: dictData)
    }

    func apiResponseRemovePromoCode(response:CommonFields?,error:Error?) {
        if let error = error  {
            AppSingletonObj.showAlert(strMsg: error.localizedDescription)
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            return
        }
        
        if code == APIINVALIDAUTHORIZATIONCODE401 {
            AppSingletonObj.displaySessionExpiredAlert(strMsg: response.responseMessage ?? "")
            return
        }

        if code == APICODE400 {
            AppSingletonObj.showAlert(strMessage: response.responseMessage ?? "")
            return
        }
        
        self.viewControllerCheckout?.removePromoCodeSuccess(strMsg: response.responseMessage ?? "")
    }

    func apiCallForGetCountryList(flagNoNetwork: Bool) {
        interactorCheckout?.apiCallForGetCountryList(flagNoNetwork: flagNoNetwork)
    }

    func apiResponseGetCountryList(response: CountryListResponse?, error: Error?, flagNoNetwork: Bool) {
        if let error = error  {
            AppSingletonObj.showAlert(strMsg: error.localizedDescription)
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            return
        }
        
        if code == APICODE400 {
            AppSingletonObj.showAlert(strMsg: response.responseMessage ?? "")
            return
        }
        
        guard let model = response.responseData,code == APISUCCESSCODE200  else {
            return
        }
        self.viewControllerCheckout?.refreshCountryData(arrCountryData: model, flagNoNetwork: flagNoNetwork)
    }

    func apiCallForCancelOrder(dictData:[String:Any]) {
        interactorCheckout?.apiCallForCancelOrder(dictData: dictData)
    }

    func apiResponseCancelOrder(response:CommonFields?,error:Error?) {
        if let error = error  {
            viewControllerCheckout?.displayAlert(string: error.localizedDescription)
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            return
        }
        
        if code == APIINVALIDAUTHORIZATIONCODE401 {
            AppSingletonObj.displaySessionExpiredAlert(strMsg: response.responseMessage ?? "")
            return
        }

        if code == APICODE400 {
            viewControllerCheckout?.displayAlert(string: response.responseMessage ?? "")
            return
        }
        
        self.viewControllerCheckout?.showAlertAndReload(strMessage: response.responseMessage ?? "")
    }

    func apiCallForAddToCart(dictData:[String:Any]) {
        interactorCheckout?.apiCallForAddToCart(dictData: dictData)
    }

    func apiResponseAddToCart(response: AddToCartResponse?, error: Error?) {
        if let error = error  {
            viewControllerCheckout?.notifyToLeaveDispatchGroup()
            self.viewControllerCheckout?.displayErrorToast(strMsg: error.localizedDescription)
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            return
        }
        
        if code == APIINVALIDAUTHORIZATIONCODE401 {
            viewControllerCheckout?.notifyToLeaveDispatchGroup()
            self.viewControllerCheckout?.displayErrorToast(strMsg: response.responseMessage ?? "")
            return
        }

        if code == APICODE400 {
            viewControllerCheckout?.notifyToLeaveDispatchGroup()
            self.viewControllerCheckout?.displayErrorToast(strMsg: response.responseMessage ?? "")
            return
        }
        
        if code == APICODE203 {
            viewControllerCheckout?.notifyToLeaveDispatchGroup()
            self.viewControllerCheckout?.displayErrorToast(strMsg: response.responseMessage ?? "")
            return
        }

        guard let model = response.responseData,code == APISUCCESSCODE200  else {
            return
        }
        self.viewControllerCheckout?.displayAlertAndDismiss(string: response.responseMessage ?? "", cartCount: model.getCartProducts ?? 0)
    }

    func apiCalculateUserPointsToSAR(points: Int? = 0) {
        interactorCheckout?.apiCallForCalculateUserPointsToSAR(points: points ?? 0)
    }
    
    func apiResponseForCallForCalculateUserPointsToSAR(response: CalculateUserPointsToSARResponse?, error: Error?) {
        
        if let error = error  {
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            return
        }
        
        
        if code != APISUCCESSCODE200 {
            return
        }
        
        self.viewControllerCheckout?.getCalculateUserPointsToSAR(model: CalculateUserPointsToSARResponseFields(pointsValueBySAR: response.pointsValueBySAR, userPoints: response.userPoints, userPointsBySAR: response.userPointsBySAR, totalUserPoints: response.totalUserPoints, totalUserPointsValueBySAR: response.totalUserPointsValueBySAR))
    }
    
    func apicalculateUserAmountToPoints(amount: Double, type: ConvertSARToPointsType) {
        interactorCheckout?.apiCallForCalculateUserAmountToPoints(amount: amount, type: type)
    }
    
    func apiResponseForCallForCalculateUserAmountToPoints(response: convertSARToPointsResponse?, error: Error?) {
        
        if let error = error  {
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            return
        }
        
        
        if code != APISUCCESSCODE200 {
            return
        }
        
        self.viewControllerCheckout?.getCalculateUserAmountToPoints(model: convertSARToPointsResponseFields(get: response.get ?? 0, pay: response.pay ?? 0))
    }
    
    func apiCallForGetShiftsByDate(dictData:[String:Any]) {
        interactorCheckout?.apiCallForGetShiftsByDate(dictData: dictData)
    }

    func apiResponseGetShiftsByDate(response: GetCartResponse?, error: Error?) {
        if let error = error  {
            viewControllerCheckout?.displayAlert(string: error.localizedDescription)
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            return
        }
        
        if code == APIINVALIDAUTHORIZATIONCODE401 {
            AppSingletonObj.displaySessionExpiredAlert(strMsg: response.responseMessage ?? "")
            return
        }

        if code == APICODE400 {
            viewControllerCheckout?.displayAlert(string: response.responseMessage ?? "")
            return
        }
        
        guard let model = response.responseData,code == APISUCCESSCODE200  else {
            return
        }
        self.viewControllerCheckout?.getShiftsBySelectDate(shifts: model.shift ?? [])
    }
    
}
