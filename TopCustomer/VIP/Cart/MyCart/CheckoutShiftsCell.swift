
import UIKit

class CheckoutShiftsCell: UICollectionViewCell {

    @IBOutlet weak var btnCheckoutShift: UIButton!
    @IBOutlet weak var btnShift: UIButton!
    @IBOutlet weak var lblShiftTime: UILabel!
    

    override func awakeFromNib() {
        super.awakeFromNib()
        self.btnShift.titleLabel?.font = .LoewNextArabic.bold(size: 12).font
        self.btnShift.titleLabel?.textAlignment = .center
    }

}
