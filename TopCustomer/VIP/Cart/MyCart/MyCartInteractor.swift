
import UIKit

protocol MyCartInteractorProtocol {
    func apiCallForGetCart(dictData:[String:Any])
    func apiCallForAddToCart(dictData:[String:Any])
    func apiCallForRemoveFromCart(dictData:[String:Any])
    func apiCallForCartYouMayAlsoLikeProducts(categoryIDS: String, productIDS: String)
    func apiCallForCheckCart(iCartId: Int)
}

protocol MyCartDataStore {
    //{ get set }
}

class MyCartInteractor: MyCartInteractorProtocol, MyCartDataStore {

    // MARK: Objects & Variables
    var presenterMyCart: MyCartPresentationProtocol?
    
    func apiCallForGetCart(dictData:[String:Any]) {
        
        ActivityIndicator.shared.showCentralSpinner()
        
        let authorization = getAuthorizationText()
        
        CartAPI.getCart(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, offset: Int(setDataInString(dictData["offset"] as AnyObject)) ?? 0) { data, error in
            ActivityIndicator.shared.hideCentralSpinner()
            self.presenterMyCart?.apiResponseGetCart(response: data, error: error)
        }
        
    }

    func apiCallForAddToCart(dictData:[String:Any]) {
        
        ActivityIndicator.shared.showCentralSpinner()
        
        let authorization = getAuthorizationText()
        
        if Int(setDataInString(dictData["manual"] as AnyObject)) ?? 0 == 1 {
            CartAPI.addToCart(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, biProductId: Int(setDataInString(dictData["biProductId"] as AnyObject)) ?? 0, tiIsCheck: nil, iProductQuantity: Int(setDataInString(dictData["iProductQuantity"] as AnyObject)) ?? 0, dbPrice: setDataInString(dictData["dbPrice"] as AnyObject),isMosques: setDataInString(dictData["isMosques"] as AnyObject), dbBundleID: Int(setDataInString(dictData["bunddelId"] as AnyObject)) ?? 0) { data, error in
                
                ActivityIndicator.shared.hideCentralSpinner()
                self.presenterMyCart?.apiResponseAddToCart(response: data, error: error)
            }
        }
        else {
            CartAPI.addToCart(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, biProductId: Int(setDataInString(dictData["biProductId"] as AnyObject)) ?? 0, tiIsCheck: Int(setDataInString(dictData["tiIsCheck"] as AnyObject)) ?? 0, iProductQuantity: Int(setDataInString(dictData["iProductQuantity"] as AnyObject)) ?? 0, dbPrice: setDataInString(dictData["dbPrice"] as AnyObject), isMosques: setDataInString(dictData["isMosques"] as AnyObject), dbBundleID: Int(setDataInString(dictData["bunddelId"] as AnyObject)) ?? 0) { data, error in
                
                ActivityIndicator.shared.hideCentralSpinner()
                self.presenterMyCart?.apiResponseAddToCart(response: data, error: error)
            }
        }
    }

    func apiCallForRemoveFromCart(dictData:[String:Any]) {
        
        ActivityIndicator.shared.showCentralSpinner()
        
        let authorization = getAuthorizationText()
        
        CartAPI.removeFromCart(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, biProductId: Int(setDataInString(dictData["biProductId"] as AnyObject)) ?? 0,
                               biBundleID: Int(setDataInString(dictData["biBundleId"] as AnyObject)) ?? 0) { data, error in
            ActivityIndicator.shared.hideCentralSpinner()
            self.presenterMyCart?.apiResponseRemoveFromCart(response: data, error: error)
        }
        
    }
    
    func apiCallForCartYouMayAlsoLikeProducts(categoryIDS: String, productIDS: String) {
        
        CartAPI.getCartYouMayAlsoLikeProducts(categoryIDS: categoryIDS, productIDS: productIDS) { data, error in
            ActivityIndicator.shared.hideCentralSpinner()
            self.presenterMyCart?.apiResponseCartYouMayAlsoLikeProducts(response: data, error: error)
        }
        
    }
    
    func apiCallForCheckCart(iCartId: Int) {
        CartAPI.checkCart(iCartId: iCartId) { data, error in
            ActivityIndicator.shared.hideCentralSpinner()
            self.presenterMyCart?.apiResponseForCheckCart(response: data, error: error)
        }
    }

}
