//
//  ChooseDeliveryDateVC.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 26/06/2024.
//  Copyright © 2024 SOTSYS203. All rights reserved.
//

import UIKit

protocol ChooseDeliveryDateVCDelegate: AnyObject {
    func getSelectedChooseDeliveryDate(date: String, selectedIndex: Int?, selectedDate: Date?)
}

class ChooseDeliveryDateVC: UIViewController {
    
    // MARK: - IBOutlets
    @IBOutlet weak var lblChooseDeliveryDate: MaterialLocalizeLable!
    @IBOutlet weak var collectionChooseDeliveryDate: UICollectionView!
    @IBOutlet weak var btnConfirm: UIButton!
    
    // MARK: - Variables
    var matchingDates = [Date]()
    var selectedChooseDeliveryDate = ""
    weak var delegate: ChooseDeliveryDateVCDelegate?
    var selectedIndex: Int?
    var shiftAt: String?
    
    // MARK: View lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        // Do any additional setup after loading the view.
        collectionChooseDeliveryDate.registerCell(cell: ChooseDeliveryDateCVC.self)
        collectionChooseDeliveryDate.delegate = self
        collectionChooseDeliveryDate.dataSource = self
        self.getDatesBetweenRange()
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        delay(0.3) {
            guard let index = self.selectedIndex else {return}
            self.setSelectedDateBy(index: index)
            self.collectionChooseDeliveryDate.selectItem(at: IndexPath(row: index, section: 0), animated: true, scrollPosition: .centeredVertically)
        }
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        lblChooseDeliveryDate.text = "choose_delivery_date".localized
        btnConfirm.setTitle("confirm".localized, for: .normal)
        self.collectionChooseDeliveryDate.reloadData()
    }
    
    @IBAction func closeBtnTapped(_ sender: Any) {
        self.dismiss(animated: true)
    }
    
    private func getDatesBetweenRange() {
        let duration = 12 // Example - 2 days
        let calendar = Calendar.current
        let today = Date().addingTimeInterval(.days(2))
        let dateEnding = calendar.date(byAdding: .day, value: duration, to: today)!
        
        // Finding matching dates at midnight - adjust as needed
        let components = DateComponents(hour: 0, minute: 0, second: 0) // midnight
        calendar.enumerateDates(startingAfter: today, matching: components, matchingPolicy: .nextTime) { (date, strict, stop) in
            if let date = date {
                if date <= dateEnding {
                    let weekDay = calendar.component(.day, from: date)
//                    if self.isDeliveryToday() {
                        matchingDates.append(date)
//                    } else {
//                        matchingDates.append(date.addingTimeInterval(.days(2)))
//                    }
                } else {
                    stop = true
                }
            }
            debugPrint("Matching dates = \(matchingDates)")
        }
    }
    
    @IBAction func actionConfirm(_ sender: Any) {
        if self.selectedIndex == nil {
            AppSingletonObj.showAlert(strMessage: "please_choose_delivery_date".localized)
        } else {
            self.dismissVC {
                self.delegate?.getSelectedChooseDeliveryDate(date: self.selectedChooseDeliveryDate, selectedIndex: self.selectedIndex, selectedDate: self.matchingDates[self.selectedIndex ?? 0].getDate())
            }
        }
    }
    
}


extension ChooseDeliveryDateVC: UICollectionViewDelegate, UICollectionViewDataSource, UICollectionViewDelegateFlowLayout { //
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return self.matchingDates.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "ChooseDeliveryDateCVC", for: indexPath) as! ChooseDeliveryDateCVC
        cell.setupCell(dayName: self.getDayName(index: indexPath.row), dateOfDay: self.matchingDates[indexPath.row].convertDateToStringByLocale(dateFormat: "dd/MM"))
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize
    {
        let cellSize = CGSize(width: (collectionView.bounds.width - (3 * 7))/3, height: 80)
        return cellSize
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, minimumLineSpacingForSectionAt section: Int) -> CGFloat
    {
        return 0
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, insetForSectionAt section: Int) -> UIEdgeInsets
    {
        let sectionInset = UIEdgeInsets(top: 10, left: 0, bottom: 10, right: 0)
        return sectionInset
    }
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        self.selectedIndex = indexPath.row
        self.setSelectedDateBy(index: indexPath.row)
    }
    
    private func setSelectedDateBy(index: Int) {
        self.selectedChooseDeliveryDate = " \(self.getDayName(index: index)) \(self.matchingDates[index].convertDateToStringByLocale(dateFormat: "dd/MM")) "
    }
    
    private func getDayName(index: Int) -> String {
//        var dayName = ""
//        if isDeliveryToday() {
//            dayName = index == 0 ? "Today".localized : self.matchingDates[index].convertDateToStringByLocale(dateFormat: "EEEE").lowercased().localized
//        } else {
//            dayName = index == 0 ? "tomorrow".localized : self.matchingDates[index].convertDateToStringByLocale(dateFormat: "EEEE").lowercased().localized
//        }
        return self.matchingDates[index].convertDateToStringByLocale(dateFormat: "EEEE").lowercased().localized
    }
    
    private func isDeliveryToday() -> Bool {
        let originalTime = utcToLocalTimeOnlyDate(strTime: self.shiftAt ?? "") ?? Date() // "05:00 PM"
        let date = Date()// Aug 25, 2017, 11:55 AM
        let calendar = Calendar.current
        let hour = calendar.component(.hour, from: date)
        var fromHour = calendar.component(.hour, from: originalTime)
        if fromHour == 0 {
            fromHour = 24
        }
        if hour < fromHour && (hour - fromHour) != -1 {
            print("can delrivery")
            return true
        } else {
            debugPrint("can't delrivery")
            return false
        }
    }
    
    func utcToLocalTimeOnlyDate(strTime: String) -> Date? {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "hh:mm a"
        return dateFormatter.date(from: strTime)
    }
    
}
