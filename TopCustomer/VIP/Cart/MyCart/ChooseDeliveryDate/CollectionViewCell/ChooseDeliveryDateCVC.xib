<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="22505" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22504"/>
        <capability name="Named colors" minToolsVersion="9.0"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="LoewNextArabic-Bold.ttf">
            <string>LoewNextArabic-Bold</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" reuseIdentifier="ChooseDeliveryDateCVC" id="gTV-IL-0wX" customClass="ChooseDeliveryDateCVC" customModule="TopCustomer" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="150" height="150"/>
            <autoresizingMask key="autoresizingMask"/>
            <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center">
                <rect key="frame" x="0.0" y="0.0" width="150" height="150"/>
                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="90i-55-TkT">
                        <rect key="frame" x="10" y="10" width="130" height="130"/>
                        <subviews>
                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="15" translatesAutoresizingMaskIntoConstraints="NO" id="Q8m-5z-dNa">
                                <rect key="frame" x="46.666666666666671" y="45.666666666666657" width="36.666666666666671" height="39"/>
                                <subviews>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Today" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="IhM-pF-hJT" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                        <rect key="frame" x="0.0" y="0.0" width="36.666666666666664" height="12"/>
                                        <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="12"/>
                                        <color key="textColor" name="AppTheme_BorderColor_#1F1F1F"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="10/10" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="9UW-Ga-Khm" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                        <rect key="frame" x="0.0" y="27.000000000000007" width="36.666666666666664" height="12"/>
                                        <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="12"/>
                                        <color key="textColor" name="AppTheme_BorderColor_#1F1F1F"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                            </stackView>
                        </subviews>
                        <color key="backgroundColor" name="AppTheme_VeryLightGrayColor_#FAFAFA"/>
                        <constraints>
                            <constraint firstItem="Q8m-5z-dNa" firstAttribute="centerY" secondItem="90i-55-TkT" secondAttribute="centerY" id="Dj2-zD-xl7"/>
                            <constraint firstItem="Q8m-5z-dNa" firstAttribute="centerX" secondItem="90i-55-TkT" secondAttribute="centerX" id="JVx-NZ-8UM"/>
                        </constraints>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                                <real key="value" value="1"/>
                            </userDefinedRuntimeAttribute>
                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                <real key="value" value="8"/>
                            </userDefinedRuntimeAttribute>
                            <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                                <color key="value" name="AppTheme_VeryLightGrayColor_#FAFAFA"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </view>
                </subviews>
            </view>
            <viewLayoutGuide key="safeArea" id="ZTg-uK-7eu"/>
            <constraints>
                <constraint firstAttribute="bottom" secondItem="90i-55-TkT" secondAttribute="bottom" constant="10" id="BeJ-5S-v5f"/>
                <constraint firstItem="90i-55-TkT" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" constant="10" id="Pdg-6D-zvq"/>
                <constraint firstItem="90i-55-TkT" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" constant="10" id="RoN-aW-dG2"/>
                <constraint firstAttribute="trailing" secondItem="90i-55-TkT" secondAttribute="trailing" constant="10" id="ysj-s0-sle"/>
            </constraints>
            <connections>
                <outlet property="lblDateOfDay" destination="9UW-Ga-Khm" id="Zw6-TZ-guS"/>
                <outlet property="lblDayName" destination="IhM-pF-hJT" id="Rnc-8x-anZ"/>
                <outlet property="viewMain" destination="90i-55-TkT" id="Few-pQ-122"/>
            </connections>
            <point key="canvasLocation" x="95" y="20"/>
        </collectionViewCell>
    </objects>
    <resources>
        <namedColor name="AppTheme_BorderColor_#1F1F1F">
            <color red="0.12156862745098039" green="0.12156862745098039" blue="0.12156862745098039" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="AppTheme_VeryLightGrayColor_#FAFAFA">
            <color red="0.98000001907348633" green="0.98000001907348633" blue="0.98000001907348633" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
    </resources>
</document>
