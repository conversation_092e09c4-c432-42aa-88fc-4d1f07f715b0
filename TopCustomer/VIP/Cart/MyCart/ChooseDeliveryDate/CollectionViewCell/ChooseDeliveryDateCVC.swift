//
//  ChooseDeliveryDateCVC.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 26/06/2024.
//  Copyright © 2024 SOTSYS203. All rights reserved.
//

import UIKit

class ChooseDeliveryDateCVC: UICollectionViewCell {

    // MARK: - IBOutlets
    @IBOutlet weak var viewMain: UIView!
    @IBOutlet weak var lblDayName: MaterialLocalizeLable!
    @IBOutlet weak var lblDateOfDay: MaterialLocalizeLable!
    
    // MARK: View lifecycle
    override func awakeFromNib() {
        super.awakeFromNib()
        // Initialization code
        self.addShadow()
    }
    
    override var isSelected: Bool {
       didSet{
           if self.isSelected {
               UIView.animate(withDuration: 0.3) { // for animation effect
                   self.viewMain.backgroundColor = .AppTheme_BlueColor_012CDA.withAlphaComponent(0.25)
                   self.viewMain.borderWidth = 1
                   self.viewMain.borderColor = .AppTheme_BlueColor_012CDA.withAlphaComponent(1)
                   self.lblDayName.textColor = .AppTheme_BlueColor_012CDA.withAlphaComponent(1)
                   self.lblDateOfDay.textColor = .AppTheme_BlueColor_012CDA.withAlphaComponent(1)
               }
           }
           else {
               UIView.animate(withDuration: 0.3) { // for animation effect
                   self.viewMain.backgroundColor = .AppTheme_VeryLightGrayColor_FAFAFA.withAlphaComponent(1)
                   self.viewMain.borderWidth = 0
                   self.lblDayName.textColor = .AppTheme_DiffBlackColor_101113.withAlphaComponent(1)
                   self.lblDateOfDay.textColor = .AppTheme_DiffBlackColor_101113.withAlphaComponent(1)
               }
           }
       }
   }

    func setupCell(dayName: String, dateOfDay: String) {
        self.lblDayName.text = dayName
        self.lblDateOfDay.text = dateOfDay
    }
    
}
