
import UIKit
import <PERSON><PERSON><PERSON>bar
import TikTokBusinessSDK
import FirebaseAnalytics

protocol MyCartProtocol: AnyObject {
    func displayAlert(string:String)
    func refreshCart(model : GetCartListResponseFields)
    func reloadData(obj : AddToCartResponseFields, strMsg: String)
    func displayAlertAndDismiss(string:String, count: Int)
    func displayErrorAlert(strMsg:String)
    func getCartYouMayAlsoLikeProducts(products: [CartYouMayAlsoLikeProductsResponseFields])
    func isCheckCartDone(value: Bool)
}

class MyCartViewController: BaseViewController {

    // MARK: Objects & Variables
    @IBOutlet weak var stackMyCart: UIStackView!
    @IBOutlet weak var tblMyCart: UITableView!
    @IBOutlet weak var stackEmptyCart: UIStackView!
    @IBOutlet weak var lblEmptyCart: UILabel!
    @IBOutlet weak var btnGoShopping: CustomRoundedButtton!
    @IBOutlet weak var btnCheckoutNow: CustomRoundedButtton!
    @IBOutlet weak var viewMyCartTotal: UIView!
    @IBOutlet weak var stackMyCartTotal: UIStackView!
    @IBOutlet weak var viewHeadrLine: UIView!
    @IBOutlet weak var lblTitle: UILabel!
    @IBOutlet weak var lblSubTotalTitle: UILabel!
    @IBOutlet weak var lblVatTitle: UILabel!
    @IBOutlet weak var lblTotalTitle: UILabel!
    @IBOutlet weak var lblSubTotal: UILabel!
    @IBOutlet weak var lblVat: UILabel!
    @IBOutlet weak var lblTotal: UILabel!
    @IBOutlet weak var lblDeliveryTitle: UILabel!
    @IBOutlet weak var lblDelivery: UILabel!
    @IBOutlet weak var viewPrices: UIView!
    @IBOutlet weak var viewOnlyLeftFreeShipping: UIView!
    @IBOutlet weak var progressFreeShipping: UIProgressView!
    @IBOutlet weak var lblDeliveryTitleFreeShipping: MaterialLocalizeLable!
    @IBOutlet weak var lblOnlyLeftFreeShipping: MaterialLocalizeLable!
    @IBOutlet weak var lblBottom: UILabel!
    @IBOutlet weak var lblDeliveryToMosques: UILabel!
    
    // MARK: - Variables
    var presenterMyCart: MyCartPresentationProtocol?
    var arrCart: [GetCartResponseFields] = []
    var arrMayAlsoLikeProductsList: [CartYouMayAlsoLikeProductsResponseFields] = []
    var arrBundles: [BundelsListResponseFields] = []

    private var offset: Int = 1
    var subTotal = 0.0
    var strVat = ""
    var strDelivery = ""
    var isCartIncludedItemsOfMosques = false

    // MARK: IBOutlets
    
    // MARK: Object lifecycle
    /*
    override init(nibName nibNameOrNil: String?, bundle nibBundleOrNil: Bundle?) {
        super.init(nibName: nibNameOrNil, bundle: nibBundleOrNil)
        setup()
    }
    */
    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
        setup()
    }
    
    // MARK: Setup
    
    private func setup() {
        let viewController = self
        let interactor = MyCartInteractor()
        let presenter = MyCartPresenter()
        
        //View Controller will communicate with only presenter
        viewController.presenterMyCart = presenter
        
        //Presenter will communicate with Interector and Viewcontroller
        presenter.viewControllerMyCart = viewController
        presenter.interactorMyCart = interactor
        
        //Interactor will communucate with only presenter.
        interactor.presenterMyCart = presenter
    }
    
    
    // MARK: View lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        self.setTexts()
        self.tblMyCart.registerCell(cell: MyCartTableViewCell.self)
        let mayAlsoLikeCell = UINib.init(nibName: "MayAlsoLikeTVCell", bundle: nil)
        self.tblMyCart.register(mayAlsoLikeCell, forCellReuseIdentifier: "MayAlsoLikeTVCell")
        self.setupUI()
    }
       
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        self.getNewProducts()
        addObservers()
        sendCartYouMayAlsoLikeProducts()
        self.checkFromCartItemsWithDeliveryMosque(isOnlyCheckValue: true)
    }
    
    private func sendCartYouMayAlsoLikeProducts() {
        if arrCart.count >= 1 {  // show data
            var categories = ""
            let _ = arrCart.map { cart in
                cart.category_ids?.forEach({ val in
                    categories.append("\(val),")
                })
            }
            
            let productIDS = arrCart.map({ "\($0.biProductId ?? 0)" }) as [String]
            var products = ""
            productIDS.forEach { value in
                products.append("\(value),")
            }
            self.presenterMyCart?.apiCallForCartYouMayAlsoLikeProducts(categoryIDS: categories, productIDS: products)
        }
    }
    
    private func setupUI() {
        self.lblDeliveryTitleFreeShipping.text = "Delivery".localized
        self.lblDeliveryToMosques.text = "You_can't_add_products_delivery_to_mosques_with_others_products".localized
        lblDeliveryTitleFreeShipping.isHidden = false
        getOnlyLeftFreeShipping()
    }
    
    private func getOnlyLeftFreeShipping() {
        if Constant.shared.FREE_DELIVERY == 0.0 || Constant.shared.FREE_DELIVERY == 0 {
            self.viewOnlyLeftFreeShipping.isHidden = true
            self.lblDeliveryTitleFreeShipping.isHidden = true
        } else if subTotal >= Constant.shared.FREE_DELIVERY {
            self.lblBottom.isHidden = true
            self.viewOnlyLeftFreeShipping.isHidden = false
            self.lblOnlyLeftFreeShipping.text = "You got free shipping!".localized
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                UIView.animate(withDuration: 1.2) {
                    self.progressFreeShipping.setProgress(1, animated: true)
                    self.progressFreeShipping.isHidden = true
                    self.lblDeliveryTitleFreeShipping.isHidden = true
                }
            }
        } else if subTotal >= (Constant.shared.FREE_DELIVERY / 2) {
            self.lblBottom.isHidden = true
            self.progressFreeShipping.isHidden = false
            self.lblDeliveryTitleFreeShipping.isHidden = false
            viewOnlyLeftFreeShipping.isHidden = false
            let onlyLeft =  abs(subTotal - Constant.shared.FREE_DELIVERY) //forTrailingZero(temp: subTotal))
            self.lblOnlyLeftFreeShipping.text = "\("Only".localized) \(forTrailingZero(temp: onlyLeft)) \(ObjKeymessages.kLABEL_SAR) \("left for free shipping!".localized)"
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                UIView.animate(withDuration: 1.2) {
                    self.progressFreeShipping.setProgress(Float(self.subTotal / Constant.shared.FREE_DELIVERY), animated: true)
                }
            }
        } else {
            DispatchQueue.main.async {
                self.lblBottom.isHidden = false
                self.viewOnlyLeftFreeShipping.isHidden = true
                self.lblDeliveryTitleFreeShipping.isHidden = true
            }
        }
    }
    
    func addObservers() {
        removeObservers()
        NotificationCenter.default.addObserver(self, selector: #selector(removeCartData), name: NSNotification.Name(rawValue: "RemoveCartData"), object: nil)
    }

    @objc func removeCartData() {
        self.offset = 1
        self.getNewProducts()
    }

    func removeObservers() {
        NotificationCenter.default.removeObserver(self)
    }

    private func getNewProducts() {
        var dictParam : [String:Any] = [:]
        dictParam["offset"] = offset

        if AppSingletonObj.isConnectedToNetwork(){
            self.presenterMyCart?.apiCallForGetCart(dictData: dictParam)
        }
    }
    
    private func setTexts() {
        lblEmptyCart.text = ObjKeymessages.kLABEL_EMPTY_CART
        btnGoShopping.setTitle(ObjKeymessages.kLABEL_GO_SHOPPING, for: .normal)
        btnCheckoutNow.setTitle(ObjKeymessages.kLABEL_CHECKOUT_NOW, for: .normal)
        lblTitle.text = ObjKeymessages.kLABEL_MY_CART
        
        lblSubTotalTitle.text = ObjKeymessages.kLABEL_SUBTOTAL
        lblVatTitle.text = ObjKeymessages.kLABEL_VAT
        lblTotalTitle.text = ObjKeymessages.kLABEL_TOTAL
    }
    
    @IBAction func btnBackAction(_ sender: Any) {
        self.popVC()
    }
    
    @IBAction func btnGoShoppingAction(_ sender: Any) {
        self.tabBarController?.selectedIndex = 0
    }
    
    @IBAction func btnCheckoutNowAction(_ sender: Any) {
        if self.isCartIncludeItemsOfMosque(isOnlyCheckValue: true) {
            self.showAlert(message: "the_cart_inclue_items_of_mosque".localized)
        } else {
            let vc = MyCartStoryboard.instantiate(CheckoutViewController.self)
            vc.hidesBottomBarWhenPushed = true
            vc.isCartIncludedItemsOfMosques = self.isCartIncludedItemsOfMosques
            self.pushVC(vc)
        }
    }
    
    private func isCartIncludeItemsOfMosque(isOnlyCheckValue: Bool = false) -> Bool {
        let itemsOfMosque = arrCart.filter { item in
            item.isMosques == 1
        }.count
        
        let itemsOfNoneMosque = arrCart.filter { item in
            item.isMosques == 0
        }.count
        
        if isOnlyCheckValue == false {
            if itemsOfMosque >= 1 {
                self.isCartIncludedItemsOfMosques = true
                self.lblBottom.isHidden = false
                self.viewOnlyLeftFreeShipping.isHidden = true
            } else {
                self.viewOnlyLeftFreeShipping.isHidden = false
                self.lblBottom.isHidden = true
            }
        }
        
        if itemsOfNoneMosque != 0 && itemsOfMosque == 0 {
            return false
        } else if itemsOfNoneMosque != 0 && itemsOfMosque != 0 {
            return true
        } else if itemsOfNoneMosque != 0 && itemsOfMosque >= 1 {
            return true
        }
        return false
    }
    
}
//MARK: - Extensions
extension MyCartViewController: UITableViewDelegate, UITableViewDataSource {
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return arrCart.count + self.arrBundles.count + 1 // + 1 for MayAlsoLikeTVCell
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        if (indexPath.row) < arrCart.count && arrCart.count != 0 {
            let cell = tableView.dequeue(with: MyCartTableViewCell.self, for: indexPath)
            cell.stackViewBundleProducts.isHidden = true
            let index = indexPath.row
            if self.arrCart[index].isMosques == 1 {
                cell.viewDeliveryToMosques.isHidden = false
            } else {
                cell.viewDeliveryToMosques.isHidden = true
            }
            cell.btnRemoveItem.tag = index
            cell.isBundleItem = false
            cell.imgProduct.kf.indicatorType = .activity
            let url = URL(string: arrCart[index].vProductImage ?? "")
            cell.imgProduct.kf.setImage(with: url , placeholder: UIImage(named: "placeholder_category"))
            cell.delegate = self
            cell.lblProductName.text = arrCart[index].vProductName
            cell.lblQuantityUnit.text = arrCart[index].vProductUnit
            cell.lblProductSkuValue.text = arrCart[index].vProductSKU
            cell.btnQty.text = arrCart[index].iProductQuantity ?? 0
            cell.btnQty.textfield.text = "\(arrCart[index].iProductQuantity ?? 0)"
            if arrCart[index].dDiscountedTotalProductPrice ?? "" == "0" { // show price in green color
                cell.lblPrice.textColor = UIColor.AppTheme_DiscountGreenColor_05B13E
                cell.lblPrice.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: arrCart[index].dDiscountedTotalProductPrice?.toDouble() ?? 0.0))")
                cell.btnQty.isHidden = true
                cell.lblFreeQuantity.isHidden = false
                cell.lblFreeQuantity.text = "\(forTrailingZero(temp: arrCart[index].iProductQuantity ?? 0))"
            }
            else {
                cell.lblPrice.textColor = .black
                cell.btnQty.isHidden = false
                cell.lblFreeQuantity.isHidden = true
                cell.lblFreeQuantity.text = ""
                // discount logic
                if arrCart[index].dDiscountAmount == "" {   // no discount
                    cell.lblPrice.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: arrCart[index].totalProductPriceWithVAT?.toDouble() ?? 0.0))")
                }
                else {  // discount is there
                    cell.lblPrice.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: arrCart[index].dDiscountedTotalProductPrice?.toDouble() ?? 0.0))")
                }
            }
            // discount logic ends here
            cell.btnQty.buttonMin.isUserInteractionEnabled = cell.btnQty.text != "0"
            cell.btnQty.onChange { (value, strAction) in
                print("Quantity Change: \(value)")
                cell.btnQty.buttonMin.isUserInteractionEnabled = value != 0
                
                if strAction == "ManualEmpty" {
                    cell.btnQty.text = self.arrCart[index].iProductQuantity ?? 0
                    cell.btnQty.textfield.text = "\(self.arrCart[index].iProductQuantity ?? 0)"
                    return
                }
                if value <= 0 {  // call remove from cart API
                    if self.arrCart.count > 0 {
                        var dictParam : [String:Any] = [:]
                        dictParam["biProductId"] = self.arrCart[index].biProductId
                        if AppSingletonObj.isConnectedToNetwork(){
                            self.presenterMyCart?.apiCallForRemoveFromCart(dictData: dictParam)
                        }
                    }
                }
                else {   // call add to cart API
                    // call API to update quantity
                    if self.arrCart.count > 0 {
                        var dictParam : [String:Any] = [:]
                        dictParam["biProductId"] = self.arrCart[index].biProductId
                        if strAction == "Add" {
                            dictParam["tiIsCheck"] = AddToCartIsCheck.Add.rawValue
                            dictParam["iProductQuantity"] = 1
                        }
                        else if strAction == "Manual" {
                            dictParam["manual"] = 1
                            dictParam["iProductQuantity"] = value
                        }
                        else {
                            dictParam["tiIsCheck"] = AddToCartIsCheck.Remove.rawValue
                            dictParam["iProductQuantity"] = 1
                        }
                        dictParam["dbPrice"] = self.arrCart[index].dbPrice
                        if AppSingletonObj.isConnectedToNetwork(){
                            self.presenterMyCart?.apiCallForAddToCart(dictData: dictParam)
                        }
                    }
                }
            }
            return cell
            // logic of bundles here..
        } else if (indexPath.row) < (self.arrBundles.count + self.arrCart.count) && self.arrBundles.count != 0 {
            let cell = tableView.dequeue(with: MyCartTableViewCell.self, for: indexPath)
            cell.viewDeliveryToMosques.isHidden = true
            
            var index = 0
            if self.arrCart.count == 0 {
                index = indexPath.row
            } else if arrBundles.count == 1 {
                index = 0
            } else {
                index = (self.arrCart.count + indexPath.row) - self.arrBundles.count
            }
            cell.getCartBunddelProducts = self.arrBundles[index].getCartBunddelProducts ?? []
            if self.arrBundles[index].getCartBunddelProducts?.count ?? 0 >= 1 {
                cell.reloadDataProductsOfBundle()
            }
            cell.btnRemoveItem.tag = index
            cell.isBundleItem = true
            cell.imgProduct.kf.indicatorType = .activity
            let url = URL(string: self.arrBundles[index].vBunddelImage ?? "")
            cell.imgProduct.kf.setImage(with: url , placeholder: UIImage(named: "placeholder_category"))
            cell.delegate = self
            cell.lblQuantityUnit.text = self.arrBundles[index].txBunddelDescription ?? ""
            cell.lblProductName.text = self.arrBundles[index].vBunddelName
            cell.lblProductSkuValue.text = self.arrBundles[index].vBunddelSKU
            cell.btnQty.text = self.arrBundles[index].iBunddelQuantity ?? 1
            cell.btnQty.textfield.text = "\(self.arrBundles[index].iBunddelQuantity ?? 1)"
            // discounts or not
            if arrBundles[index].discountPrice ?? 0 <= 0 { // show price in green color
                cell.lblPrice.textColor = UIColor.AppTheme_DiscountGreenColor_05B13E
                cell.lblPrice.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: arrBundles[index].price ?? 0.0))")
                cell.btnQty.isHidden = true
                cell.lblFreeQuantity.isHidden = false
                cell.lblFreeQuantity.text = "\(forTrailingZero(temp: arrBundles[index].iBunddelQuantity ?? 1))"
            }
            else {
                cell.lblPrice.textColor = .black
                cell.btnQty.isHidden = false
                cell.lblFreeQuantity.isHidden = true
                cell.lblFreeQuantity.text = ""
                cell.lblPrice.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: arrBundles[index].discountPrice ?? 0.0))")
            }
            // logic of add or remove bundle
            cell.btnQty.buttonMin.isUserInteractionEnabled = cell.btnQty.text != "0"
            cell.btnQty.onChange { (value, strAction) in
                print("Quantity Change: \(value)")
                cell.btnQty.buttonMin.isUserInteractionEnabled = value != 0
                if strAction == "ManualEmpty" {
                    cell.btnQty.text = self.arrBundles[index].iBunddelQuantity ?? 1
                    cell.btnQty.textfield.text = "\(self.arrBundles[index].iBunddelQuantity ?? 1)"
                    return
                }
                if value <= 0 {  // call remove from cart API
                    if self.arrBundles.count > 0 {
                        var dictParam : [String:Any] = [:]
                        dictParam["biBundleId"] = self.arrBundles[index].bunddelId
                        if AppSingletonObj.isConnectedToNetwork() {
                            self.presenterMyCart?.apiCallForRemoveFromCart(dictData: dictParam)
                        }
                    }
                }
                else {   // call add to cart API
                    // call API to update quantity
                    if self.arrBundles.count > 0 {
                        var dictParam : [String:Any] = [:]
                        dictParam["bunddelId"] = self.arrBundles[index].bunddelId
                        dictParam["biProductId"] = 0
                        if strAction == "Add" {
                            dictParam["tiIsCheck"] = AddToCartIsCheck.Add.rawValue
                            dictParam["iProductQuantity"] = 1
                        }
                        else if strAction == "Manual" {
                            dictParam["manual"] = 1
                            dictParam["iProductQuantity"] = value
                        }
                        else {
                            dictParam["tiIsCheck"] = AddToCartIsCheck.Remove.rawValue
                            dictParam["iProductQuantity"] = 1
                        }
                        dictParam["dbPrice"] = self.arrBundles[index].price
                        if AppSingletonObj.isConnectedToNetwork(){
                            self.presenterMyCart?.apiCallForAddToCart(dictData: dictParam)
                        }
                    }
                }
            }
            return cell
        } else {
            // MayAlsoLikeTVCell
            let cell = tableView.dequeueReusableCell(withIdentifier: "MayAlsoLikeTVCell") as! MayAlsoLikeTVCell
            cell.setCartYouMayAlsoLikeProducts(products: self.arrMayAlsoLikeProductsList)
            cell.mayAlsoLikeTVCellDelegate = self
            return cell
        }
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        if (indexPath.row) < arrCart.count && arrCart.count != 0 {
            return 170
        }
        else if (indexPath.row) < (self.arrBundles.count + self.arrCart.count) && self.arrBundles.count != 0 {
            var index = 0
            if self.arrCart.count == 0 {
                index = indexPath.row
            } else if arrBundles.count == 1 {
                index = 0
            } else {
                index = (self.arrCart.count + indexPath.row) - self.arrBundles.count
            }
            
            return CGFloat(170 + (40 * (self.arrBundles[index].getCartBunddelProducts?.count ?? 0)))
        } else {
            return 245 //CGFloat(width: (self.tblProductsOfBundle.frame.width), height: 193)
        }
    }

}
// MARK: - Extension
extension MyCartViewController: MyCartProtocol {
    func displayAlert(string: String) {
        AppSingleton.shared.showCustomPopUpWithOkButton(strButtonTitle: ObjKeymessages.kLABEL_OK, strMessage: string, showOnTopVC: false) { (isOk) in
        }
    }
    
    fileprivate func checkFromCartItemsWithDeliveryMosque(isOnlyCheckValue: Bool = false) {
        if self.isCartIncludeItemsOfMosque(isOnlyCheckValue: isOnlyCheckValue) {
            self.lblDeliveryToMosques.isHidden = false
            self.lblBottom.isHidden = true
            self.btnCheckoutNow.backgroundColor = .AppTheme_DarkGrayColor_5D5D5D
        } else {
            self.lblDeliveryToMosques.isHidden = true
            self.btnCheckoutNow.backgroundColor = .AppTheme_BlueColor_012CDA
        }
    }
    
    func refreshCart(model : GetCartListResponseFields) {
        subTotal = 0
        strVat = model.dVatCost ?? ""
        strDelivery = model.dDistanceCost ?? ""

        // emtpy the array if it is initial call
        if self.offset == 1 {
            self.arrCart.removeAll()
            self.arrBundles.removeAll()
        }
        self.arrCart.append(contentsOf: model.products ?? [])
        self.arrBundles.append(contentsOf: model.bunddels ?? [])
        tblMyCart.reloadData()

        if arrCart.count <= 0 && arrBundles.count <= 0 {  // show no data view
            stackEmptyCart.isHidden = false
            btnCheckoutNow.isHidden = true
            viewPrices.isHidden = true
            stackMyCart.isHidden = true
            viewMyCartTotal.isHidden = true
        }
        else {
            self.sendCartYouMayAlsoLikeProducts()
            stackEmptyCart.isHidden = true
            btnCheckoutNow.isHidden = false
            viewPrices.isHidden = false
            stackMyCart.isHidden = false
            viewMyCartTotal.isHidden = false
        }
        
        // calculate Subtotal
        for objProduct in arrCart {
            if objProduct.dDiscountAmount != "" {   //discount
                subTotal = subTotal + ((objProduct.dDiscountedTotalProductPrice)?.toDouble() ?? 0.0)
            }
            else if objProduct.dDiscountedTotalProductPrice == "0" { // free product
                subTotal = subTotal + ((objProduct.dDiscountedTotalProductPrice)?.toDouble() ?? 0.0)
            }
            else {  // no discount
                subTotal = subTotal + ((objProduct.totalProductPriceWithVAT)?.toDouble() ?? 0.0)
            }
        }
        
        // calculate Subtotal
        for objBundle in self.arrBundles {
            if objBundle.discountPrice ?? 0 <= 0 {   //discount
                subTotal = subTotal + (objBundle.price ?? 0.0) * Double((objBundle.iBunddelQuantity ?? 1))
            } else {
                subTotal = subTotal + (objBundle.discountPrice ?? 0.0) * Double((objBundle.iBunddelQuantity ?? 1))
            }
        }
        
        lblSubTotal.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: subTotal))")
        self.updateCart(cartCount: arrCart.count + self.arrBundles.count)
        self.getOnlyLeftFreeShipping()
        
        // check from cart items
        let cartID = model.products?.first?.iCartId ?? 0 == 0 ? self.arrBundles.first?.iCartId ?? 0 : model.products?.first?.iCartId ?? 0
        self.presenterMyCart?.apiCallForCheckCart(iCartId: cartID)
        checkFromCartItemsWithDeliveryMosque()
    }
    
    func reloadData(obj : AddToCartResponseFields, strMsg: String) {

        AppSingletonObj.showAlert(strMessage: strMsg)

        if let row = self.arrCart.firstIndex(where: {$0.biProductId == obj.biProductId}) {
            arrCart[row].totalProductPriceWithVAT = obj.dbFinalPrice
            arrCart[row].iProductQuantity = obj.iProductQuantity
            arrCart[row].dbPrice = obj.dbPrice
            arrCart[row].dDiscountedTotalProductPrice = obj.dbFinalPrice

        }
        tblMyCart.reloadData()
        
        subTotal = 0

        // calculate Subtotal
        for objProduct in arrCart {
            if objProduct.dDiscountAmount != "" {   //discount
                subTotal = subTotal + ((objProduct.dDiscountedTotalProductPrice)?.toDouble() ?? 0.0)
            }
            else if objProduct.dDiscountedTotalProductPrice == "0" { // free product
                subTotal = subTotal + ((objProduct.dDiscountedTotalProductPrice)?.toDouble() ?? 0.0)
            }
            else {  // no discount
                subTotal = subTotal + ((objProduct.totalProductPriceWithVAT)?.toDouble() ?? 0.0)
            }
        }

        
        lblSubTotal.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: subTotal))")

        self.offset = 1
        self.getNewProducts()
        self.vibrationDevice()
        self.getOnlyLeftFreeShipping()
        self.checkFromCartItemsWithDeliveryMosque()
    }

    func displayAlertAndDismiss(string: String, count: Int) {
        
        // update my cart button count
        self.updateCart(cartCount: count)
        
        AppSingletonObj.showAlert(strMessage: string)
        self.offset = 1
        self.getNewProducts()
    }

    private func updateCart(cartCount:Int?) {
        // update my cart button count
        if let myTabbar = self.tabBarController?.tabBar as? STTabbar {
            if cartCount ?? 0 <= 0 {
                myTabbar.label?.isHidden = true
            }
            else {
                myTabbar.label?.isHidden = false
                myTabbar.label?.text = "\(cartCount ?? 0)"
            }
        }

    }

    func displayErrorAlert(strMsg:String) {
        AppSingleton.shared.showCustomPopUpWithOkButton(strButtonTitle: ObjKeymessages.kLABEL_OK, strMessage: strMsg, showOnTopVC: false) { (isOk) in
            if isOk == true {
                self.tblMyCart.reloadData()
            }
        }
    }

    func getCartYouMayAlsoLikeProducts(products: [CartYouMayAlsoLikeProductsResponseFields]) {
        self.arrMayAlsoLikeProductsList = products
        self.tblMyCart.reloadData()
    }
    
    func isCheckCartDone(value: Bool) {
        if value == false {
            self.showAlertPopup(message: "some items at cart not available".localized) {}
        }
    }
}

func forTrailingZero(temp: Double) -> String {
    let numberFormatter = NumberFormatter()
    numberFormatter.locale = Locale(identifier: "EN")
    numberFormatter.numberStyle = .decimal
    numberFormatter.maximumFractionDigits = 2
    guard let number = numberFormatter.string(from: NSNumber(value: temp)) else { //fatalError("Can not get number")
        return ""
    }
    return number
}

func forTrailingZero(temp: Int) -> String {
    let numberFormatter = NumberFormatter()
    numberFormatter.locale = Locale(identifier: "EN")
    numberFormatter.numberStyle = .none
    numberFormatter.maximumFractionDigits = 0
    guard let number = numberFormatter.string(from: NSNumber(value: temp)) else { //fatalError("Can not get number")
        return ""
    }
    return number
}

// save numbers with english language
func forTrailingZeroEnglishNumbersOnly(temp: Double) -> String {
    let numberFormatter = NumberFormatter()
    numberFormatter.locale = Locale(identifier: "EN")
    numberFormatter.numberStyle = .decimal
    numberFormatter.maximumFractionDigits = 2
    guard let number =  numberFormatter.string(from: NSNumber(value: temp)) else { //fatalError("Can not get number")
        return ""
    }
    return number
}

extension MyCartViewController: FullScreenImageDelegate {
    func showInFullScreen(image: UIImage?) {
        let vc = ProductPopupStoryboard.instantiate(FullScreenImageViewController.self)
        vc.mediaImage = image
        vc.modalPresentationStyle = .overFullScreen
        self.present(vc, animated: true)
    }
 
    func removeItem(itemIndex: Int, isBundleItem: Bool) {
        var dictParam : [String:Any] = [:]
        var contentId = 0
        if isBundleItem {
            if itemIndex >= self.arrBundles.count  {
                return
            }
            dictParam["biBundleId"] = self.arrBundles[itemIndex].bunddelId
            contentId = self.arrBundles[itemIndex].bunddelId ?? 0
        } else {
            if itemIndex >= self.arrCart.count  {
                return
            }
            dictParam["biProductId"] = self.arrCart[itemIndex].biProductId
            contentId = self.arrCart[itemIndex].biProductId ?? 0
        }
        // Apps Flyer Event
        TikTokBusiness.trackEvent("REMOVE_ITEM_FROM_CART", withProperties: ["item_name": arrCart[itemIndex].vProductName ?? "", "item_id": contentId])
        Analytics.logEvent("REMOVE_ITEM_FROM_CART", parameters: ["item_name": arrCart[itemIndex].vProductName ?? "", "item_id": contentId])
        AppsFlyerEvents.shared.afRemoveFromCart(contentId: contentId, contentType: arrCart[itemIndex].vProductName ?? "")
        if AppSingletonObj.isConnectedToNetwork() {
            self.presenterMyCart?.apiCallForRemoveFromCart(dictData: dictParam)
        }
    }
    
}

extension MyCartViewController: MayAlsoLikeTVCellDelegate {
    func addItemToCart(productInfo: [String : Any]) {
        if !User.shared.checkUserLoginStatus() {
            self.showNoLoginUserAlert(complation: nil)
            return
        }
        self.presenterMyCart?.apiCallForAddToCart(dictData: productInfo)
    }
    
}
