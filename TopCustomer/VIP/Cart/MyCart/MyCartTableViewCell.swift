
import UIKit

protocol FullScreenImageDelegate {
    func showInFullScreen(image: UIImage?)
    func removeItem(itemIndex: Int, isBundleItem: Bool)
}

class MyCartTableViewCell: UITableViewCell {

    //MARK: - Outlets
    @IBOutlet weak var btnQty: QTYButtonOLD!
    @IBOutlet weak var lblPrice: UILabel!
    @IBOutlet weak var lblProductName: UILabel!
    @IBOutlet weak var imgProduct: UIImageView!
    @IBOutlet weak var lblQuantityUnit: UILabel!
    @IBOutlet weak var lblProductSkuValue: UILabel!
    @IBOutlet weak var lblFreeQuantity: UILabel!
    @IBOutlet weak var btnRemoveItem: UIButton!
    @IBOutlet weak var viewDeliveryToMosques: UIView!
    @IBOutlet weak var lblDeliveryToMosque: MaterialLocalizeLable!
    @IBOutlet weak var stackViewBundleProducts: UIStackView!
    @IBOutlet weak var tblProductsOfBundle: UITableView!
    @IBOutlet weak var consProductsOfBundleHeight: NSLayoutConstraint!
    @IBOutlet weak var lblCartBottomLine: UILabel!
    @IBOutlet weak var lblBundleBottomLine: UILabel!
    
    // MARK: - Variables
    var delegate: FullScreenImageDelegate?
    var isBundleItem: Bool = false
    var getCartBunddelProducts = [CartBunddelProductsFields]()
    
    // MARK: View lifecycle
    override func awakeFromNib() {
        super.awakeFromNib()
        self.lblDeliveryToMosque.text = "delivery_to_mosques".localized
        self.tblProductsOfBundle.delegate = self
        self.tblProductsOfBundle.dataSource = self
        self.tblProductsOfBundle.registerCell(cell: CartProductsOfBundleTVCell.self)
        self.stackViewBundleProducts.isHidden = true
    }

    override func setSelected(_ selected: Bool, animated: Bool) {
        super.setSelected(selected, animated: animated)

        // Configure the view for the selected state
    }
    
    @IBAction func btnFullScreenAction(_ sender: Any) {
        delegate?.showInFullScreen(image: imgProduct.image)
    }
    
    @IBAction func btnRemoveItemAction(_ sender: Any) {
        delegate?.removeItem(itemIndex: self.btnRemoveItem.tag, isBundleItem: self.isBundleItem)
    }
    
    func reloadDataProductsOfBundle() {
        self.lblCartBottomLine.isHidden = true
        self.lblBundleBottomLine.isHidden = false
        self.stackViewBundleProducts.isHidden = false
        self.consProductsOfBundleHeight.constant = CGFloat((70 * self.getCartBunddelProducts.count))
        self.updateConstraints()
        self.tblProductsOfBundle.reloadData()
    }
    
}

// MARK: - Extension TableView
extension MyCartTableViewCell: UITableViewDelegate, UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return self.getCartBunddelProducts.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "CartProductsOfBundleTVCell", for: indexPath) as! CartProductsOfBundleTVCell
        cell.setupCell(productCount: getCartBunddelProducts[indexPath.row].qty?.string ?? "", productName: getCartBunddelProducts[indexPath.row].product?.vProductName ?? "", productUnit: getCartBunddelProducts[indexPath.row].product?.vProductUnit ?? "", productImageUrl: getCartBunddelProducts[indexPath.row].product?.vProductImage ?? "")
        return cell
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 40
    }
    
}
