<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="22505" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22504"/>
        <capability name="Named colors" minToolsVersion="9.0"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="LoewNextArabic-Bold.ttf">
            <string>LoewNextArabic-Bold</string>
        </array>
        <array key="LoewNextArabic-Medium.ttf">
            <string>LoewNextArabic-Medium</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" rowHeight="170" id="KGk-i7-Jjw" customClass="MyCartTableViewCell" customModule="TopCustomer" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="320" height="250"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="320" height="250"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="y9p-w6-d7r">
                        <rect key="frame" x="0.0" y="0.0" width="320" height="150"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="53u-SC-97W">
                                <rect key="frame" x="20" y="20" width="106" height="110"/>
                                <subviews>
                                    <view hidden="YES" clipsSubviews="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="XpM-89-2j3">
                                        <rect key="frame" x="0.0" y="110" width="106" height="0.0"/>
                                        <subviews>
                                            <label hidden="YES" opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Arwa Water" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="GdU-bR-jMq">
                                                <rect key="frame" x="8" y="0.0" width="90" height="0.0"/>
                                                <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="11"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                        </subviews>
                                        <color key="backgroundColor" name="AppTheme_LightGrayOrderColor_#EFEFEF"/>
                                        <constraints>
                                            <constraint firstAttribute="bottom" secondItem="GdU-bR-jMq" secondAttribute="bottom" id="4Y1-Yr-Fm7"/>
                                            <constraint firstAttribute="height" id="4ad-v7-WtJ"/>
                                            <constraint firstAttribute="trailing" secondItem="GdU-bR-jMq" secondAttribute="trailing" constant="8" id="Ch2-Km-qYa"/>
                                            <constraint firstItem="GdU-bR-jMq" firstAttribute="top" secondItem="XpM-89-2j3" secondAttribute="top" id="RmS-KU-7Ch"/>
                                            <constraint firstItem="GdU-bR-jMq" firstAttribute="leading" secondItem="XpM-89-2j3" secondAttribute="leading" constant="8" id="Yht-Ss-FUS"/>
                                        </constraints>
                                    </view>
                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="Mpq-fQ-J6n">
                                        <rect key="frame" x="5" y="5" width="96" height="100"/>
                                    </imageView>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="WQ1-yB-W4q">
                                        <rect key="frame" x="5" y="5" width="96" height="100"/>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                        <connections>
                                            <action selector="btnFullScreenAction:" destination="KGk-i7-Jjw" eventType="touchUpInside" id="mFT-9R-ybV"/>
                                        </connections>
                                    </button>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstItem="WQ1-yB-W4q" firstAttribute="leading" secondItem="Mpq-fQ-J6n" secondAttribute="leading" id="1ae-7c-SZR"/>
                                    <constraint firstItem="Mpq-fQ-J6n" firstAttribute="leading" secondItem="53u-SC-97W" secondAttribute="leading" constant="5" id="8mU-b1-FdV"/>
                                    <constraint firstItem="WQ1-yB-W4q" firstAttribute="bottom" secondItem="Mpq-fQ-J6n" secondAttribute="bottom" id="C9Q-Qr-fdy"/>
                                    <constraint firstAttribute="bottom" secondItem="XpM-89-2j3" secondAttribute="bottom" id="ENl-08-d3y"/>
                                    <constraint firstItem="WQ1-yB-W4q" firstAttribute="top" secondItem="Mpq-fQ-J6n" secondAttribute="top" id="HDA-6s-ALi"/>
                                    <constraint firstItem="Mpq-fQ-J6n" firstAttribute="top" secondItem="53u-SC-97W" secondAttribute="top" constant="5" id="OJG-9f-pee"/>
                                    <constraint firstItem="XpM-89-2j3" firstAttribute="leading" secondItem="53u-SC-97W" secondAttribute="leading" id="RCA-VV-vbl"/>
                                    <constraint firstItem="XpM-89-2j3" firstAttribute="top" secondItem="Mpq-fQ-J6n" secondAttribute="bottom" constant="5" id="UHz-1u-gpN"/>
                                    <constraint firstAttribute="trailing" secondItem="Mpq-fQ-J6n" secondAttribute="trailing" constant="5" id="kdv-G3-6DM"/>
                                    <constraint firstAttribute="trailing" secondItem="XpM-89-2j3" secondAttribute="trailing" id="pP9-hS-rDS"/>
                                    <constraint firstAttribute="height" constant="110" id="qe0-X0-XRo"/>
                                    <constraint firstAttribute="width" constant="106" id="uNF-Lp-AK2"/>
                                    <constraint firstItem="WQ1-yB-W4q" firstAttribute="trailing" secondItem="Mpq-fQ-J6n" secondAttribute="trailing" id="wXa-jh-kFp"/>
                                </constraints>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="color" keyPath="shadowColor">
                                        <color key="value" name="AppTheme_ShadowColor_#0000003E"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="size" keyPath="shadowOffset">
                                        <size key="value" width="0.0" height="0.0"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="number" keyPath="shadowOpacity">
                                        <real key="value" value="1"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="k7c-kH-LgV">
                                <rect key="frame" x="5" y="5" width="30" height="30"/>
                                <subviews>
                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="x-mark" translatesAutoresizingMaskIntoConstraints="NO" id="d9b-zj-Gbs">
                                        <rect key="frame" x="5" y="5" width="20" height="20"/>
                                    </imageView>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Eme-x3-lWY">
                                        <rect key="frame" x="0.0" y="0.0" width="30" height="30"/>
                                        <state key="normal" title="Button"/>
                                        <buttonConfiguration key="configuration" style="plain" title=" "/>
                                        <connections>
                                            <action selector="btnRemoveItemAction:" destination="KGk-i7-Jjw" eventType="touchUpInside" id="e6A-6h-3j4"/>
                                        </connections>
                                    </button>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="30" id="1EZ-Of-wot"/>
                                    <constraint firstItem="Eme-x3-lWY" firstAttribute="leading" secondItem="k7c-kH-LgV" secondAttribute="leading" id="DF3-cg-gB1"/>
                                    <constraint firstAttribute="trailing" secondItem="Eme-x3-lWY" secondAttribute="trailing" id="FX3-A1-fYg"/>
                                    <constraint firstItem="d9b-zj-Gbs" firstAttribute="top" secondItem="k7c-kH-LgV" secondAttribute="top" constant="5" id="M7y-mH-l9E"/>
                                    <constraint firstAttribute="bottom" secondItem="d9b-zj-Gbs" secondAttribute="bottom" constant="5" id="YE6-lH-JoW"/>
                                    <constraint firstItem="Eme-x3-lWY" firstAttribute="top" secondItem="k7c-kH-LgV" secondAttribute="top" id="f1f-NZ-nT1"/>
                                    <constraint firstItem="d9b-zj-Gbs" firstAttribute="leading" secondItem="k7c-kH-LgV" secondAttribute="leading" constant="5" id="fiD-Es-Kvs"/>
                                    <constraint firstAttribute="width" constant="30" id="soG-lw-lWa"/>
                                    <constraint firstAttribute="trailing" secondItem="d9b-zj-Gbs" secondAttribute="trailing" constant="5" id="tDR-e6-Qcy"/>
                                    <constraint firstAttribute="bottom" secondItem="Eme-x3-lWY" secondAttribute="bottom" id="yxO-By-7l3"/>
                                </constraints>
                            </view>
                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="fillProportionally" spacing="7" translatesAutoresizingMaskIntoConstraints="NO" id="9UN-ye-1aG">
                                <rect key="frame" x="141" y="18.5" width="164" height="113"/>
                                <subviews>
                                    <label opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Arwa Water" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="epf-Ar-ttJ" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                        <rect key="frame" x="0.0" y="0.0" width="164" height="6.5"/>
                                        <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="12"/>
                                        <nil key="textColor"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="330 ml X48" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Vl2-bf-VSs" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                        <rect key="frame" x="0.0" y="13.5" width="164" height="7"/>
                                        <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="12"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="SKU19910145" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="c9I-T4-eLD" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                        <rect key="frame" x="0.0" y="27.5" width="164" height="6.5"/>
                                        <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="12"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="NMX-Bn-kwL" userLabel="delivery to mosques">
                                        <rect key="frame" x="0.0" y="41" width="164" height="18"/>
                                        <subviews>
                                            <stackView opaque="NO" contentMode="scaleToFill" distribution="fillProportionally" alignment="bottom" spacing="2" translatesAutoresizingMaskIntoConstraints="NO" id="9j6-mr-FcQ">
                                                <rect key="frame" x="0.0" y="0.0" width="164" height="18"/>
                                                <subviews>
                                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="mosque-icon" translatesAutoresizingMaskIntoConstraints="NO" id="toV-YH-snV">
                                                        <rect key="frame" x="0.0" y="0.0" width="18" height="18"/>
                                                        <constraints>
                                                            <constraint firstAttribute="width" constant="18" id="6RX-8h-heG"/>
                                                            <constraint firstAttribute="height" constant="18" id="7aE-jp-u1F"/>
                                                        </constraints>
                                                    </imageView>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="delivery to mosque" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="YOn-Gv-FIO" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                        <rect key="frame" x="20" y="6" width="144" height="12"/>
                                                        <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="12"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                </subviews>
                                            </stackView>
                                        </subviews>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstItem="9j6-mr-FcQ" firstAttribute="top" secondItem="NMX-Bn-kwL" secondAttribute="top" id="0ju-IV-HcT"/>
                                            <constraint firstAttribute="trailing" secondItem="9j6-mr-FcQ" secondAttribute="trailing" id="Mvl-9W-FdC"/>
                                            <constraint firstAttribute="height" constant="18" id="XJn-AD-zG2"/>
                                            <constraint firstAttribute="bottom" secondItem="9j6-mr-FcQ" secondAttribute="bottom" id="cT1-Ii-Jc3"/>
                                            <constraint firstItem="9j6-mr-FcQ" firstAttribute="leading" secondItem="NMX-Bn-kwL" secondAttribute="leading" id="eQj-a5-45B"/>
                                        </constraints>
                                    </view>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="F2z-bf-cxi">
                                        <rect key="frame" x="0.0" y="66" width="164" height="10"/>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="10" id="jup-J6-V3c"/>
                                        </constraints>
                                    </view>
                                    <stackView opaque="NO" contentMode="scaleToFill" distribution="fillProportionally" translatesAutoresizingMaskIntoConstraints="NO" id="hJ2-mx-tyF">
                                        <rect key="frame" x="0.0" y="83" width="164" height="30"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="18.00 SAR" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="0CS-P6-LsP" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                <rect key="frame" x="0.0" y="0.0" width="44" height="30"/>
                                                <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="12"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="pVp-hB-Npl">
                                                <rect key="frame" x="44" y="0.0" width="120" height="30"/>
                                                <subviews>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="rW4-sb-2oN" customClass="QTYButtonOLD" customModule="TopCustomer" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="0.0" width="120" height="30"/>
                                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="30" id="4w0-7D-Wc5"/>
                                                            <constraint firstAttribute="width" constant="120" id="Aap-q0-Jza"/>
                                                        </constraints>
                                                    </view>
                                                    <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Bfm-iG-Onm">
                                                        <rect key="frame" x="37" y="0.0" width="46" height="30"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="30" id="FEI-06-btA"/>
                                                            <constraint firstAttribute="width" constant="46" id="HY2-pV-FSY"/>
                                                        </constraints>
                                                        <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="12"/>
                                                        <nil key="textColor"/>
                                                        <nil key="highlightedColor"/>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                                                                <integer key="value" value="1"/>
                                                            </userDefinedRuntimeAttribute>
                                                            <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                                                                <color key="value" name="AppTheme_LightGrayColor_#CECECE"/>
                                                            </userDefinedRuntimeAttribute>
                                                        </userDefinedRuntimeAttributes>
                                                    </label>
                                                </subviews>
                                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="120" id="HQk-Ed-W3n"/>
                                                    <constraint firstAttribute="trailing" secondItem="rW4-sb-2oN" secondAttribute="trailing" id="L9M-Dg-l2f"/>
                                                    <constraint firstItem="Bfm-iG-Onm" firstAttribute="centerX" secondItem="rW4-sb-2oN" secondAttribute="centerX" id="SoL-Eh-nSb"/>
                                                    <constraint firstItem="rW4-sb-2oN" firstAttribute="top" secondItem="pVp-hB-Npl" secondAttribute="top" id="kOR-0d-AsY"/>
                                                    <constraint firstItem="Bfm-iG-Onm" firstAttribute="centerY" secondItem="pVp-hB-Npl" secondAttribute="centerY" id="meh-CJ-Bo6"/>
                                                    <constraint firstAttribute="bottom" secondItem="rW4-sb-2oN" secondAttribute="bottom" id="xzJ-y2-KGB"/>
                                                </constraints>
                                            </view>
                                        </subviews>
                                    </stackView>
                                </subviews>
                            </stackView>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="3qW-kH-VTy">
                                <rect key="frame" x="18" y="149" width="284" height="1"/>
                                <color key="backgroundColor" name="AppTheme_LightGrayColor_#CECECE"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="1" id="7gN-GZ-IkX"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                <color key="textColor" name="AppTheme_LightGrayColor_#CECECE"/>
                                <nil key="highlightedColor"/>
                            </label>
                        </subviews>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstAttribute="bottom" secondItem="3qW-kH-VTy" secondAttribute="bottom" id="643-gk-g1N"/>
                            <constraint firstAttribute="trailing" secondItem="3qW-kH-VTy" secondAttribute="trailing" constant="18" id="885-fj-MdS"/>
                            <constraint firstItem="k7c-kH-LgV" firstAttribute="leading" secondItem="y9p-w6-d7r" secondAttribute="leading" constant="5" id="Fqb-pB-Tfq"/>
                            <constraint firstAttribute="bottom" secondItem="53u-SC-97W" secondAttribute="bottom" constant="20" id="JyB-2S-XG1"/>
                            <constraint firstItem="53u-SC-97W" firstAttribute="top" secondItem="y9p-w6-d7r" secondAttribute="top" constant="20" id="OtO-TZ-oJI"/>
                            <constraint firstItem="3qW-kH-VTy" firstAttribute="leading" secondItem="y9p-w6-d7r" secondAttribute="leading" constant="18" id="Ovy-oF-koK"/>
                            <constraint firstItem="9UN-ye-1aG" firstAttribute="centerY" secondItem="53u-SC-97W" secondAttribute="centerY" id="TAb-M4-Hzh"/>
                            <constraint firstAttribute="height" constant="150" id="atn-mY-zpL"/>
                            <constraint firstItem="53u-SC-97W" firstAttribute="leading" secondItem="y9p-w6-d7r" secondAttribute="leading" constant="20" id="gwN-kd-gtj"/>
                            <constraint firstAttribute="trailing" secondItem="9UN-ye-1aG" secondAttribute="trailing" constant="15" id="ii8-JA-zyF"/>
                            <constraint firstItem="k7c-kH-LgV" firstAttribute="top" secondItem="y9p-w6-d7r" secondAttribute="top" constant="5" id="qbf-De-dcY"/>
                            <constraint firstItem="9UN-ye-1aG" firstAttribute="leading" secondItem="53u-SC-97W" secondAttribute="trailing" constant="15" id="ue0-a1-MvR"/>
                        </constraints>
                    </view>
                    <stackView hidden="YES" opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="p8d-ma-FyU">
                        <rect key="frame" x="20" y="150" width="280" height="100"/>
                        <subviews>
                            <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" style="plain" separatorStyle="none" allowsSelection="NO" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="-1" estimatedSectionHeaderHeight="-1" sectionFooterHeight="-1" estimatedSectionFooterHeight="-1" translatesAutoresizingMaskIntoConstraints="NO" id="BIQ-hr-zWT">
                                <rect key="frame" x="0.0" y="0.0" width="280" height="100"/>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                            </tableView>
                        </subviews>
                        <constraints>
                            <constraint firstAttribute="bottom" secondItem="BIQ-hr-zWT" secondAttribute="bottom" id="9v4-R2-N9t"/>
                            <constraint firstAttribute="trailing" secondItem="BIQ-hr-zWT" secondAttribute="trailing" id="Dxx-iN-qA2"/>
                            <constraint firstItem="BIQ-hr-zWT" firstAttribute="leading" secondItem="p8d-ma-FyU" secondAttribute="leading" id="X6S-es-jT8"/>
                            <constraint firstAttribute="height" relation="greaterThanOrEqual" constant="100" id="gr8-wv-lgh"/>
                            <constraint firstItem="BIQ-hr-zWT" firstAttribute="top" secondItem="p8d-ma-FyU" secondAttribute="top" id="p8R-Uh-lyo"/>
                        </constraints>
                    </stackView>
                    <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="5fE-5y-ncK">
                        <rect key="frame" x="18" y="249" width="284" height="1"/>
                        <color key="backgroundColor" name="AppTheme_LightGrayColor_#CECECE"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="1" id="319-0Z-jqD"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                        <color key="textColor" name="AppTheme_LightGrayColor_#CECECE"/>
                        <nil key="highlightedColor"/>
                    </label>
                </subviews>
                <constraints>
                    <constraint firstAttribute="trailing" secondItem="5fE-5y-ncK" secondAttribute="trailing" constant="18" id="2Q0-Vz-yUi"/>
                    <constraint firstAttribute="trailing" secondItem="p8d-ma-FyU" secondAttribute="trailing" constant="20" id="8bs-gV-dIZ"/>
                    <constraint firstAttribute="bottom" secondItem="p8d-ma-FyU" secondAttribute="bottom" id="CnD-vU-TK1"/>
                    <constraint firstItem="y9p-w6-d7r" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" id="D2Y-WX-sYJ"/>
                    <constraint firstItem="p8d-ma-FyU" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="20" id="HFv-Zy-WVr"/>
                    <constraint firstItem="p8d-ma-FyU" firstAttribute="top" secondItem="y9p-w6-d7r" secondAttribute="bottom" id="VBo-oj-yrd"/>
                    <constraint firstAttribute="trailing" secondItem="y9p-w6-d7r" secondAttribute="trailing" id="bcH-Vu-8tb"/>
                    <constraint firstItem="5fE-5y-ncK" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="18" id="kB2-do-Sss"/>
                    <constraint firstAttribute="bottom" secondItem="5fE-5y-ncK" secondAttribute="bottom" id="saR-K3-36n"/>
                    <constraint firstItem="y9p-w6-d7r" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" id="z8Y-2T-UYh"/>
                </constraints>
            </tableViewCellContentView>
            <viewLayoutGuide key="safeArea" id="njF-e1-oar"/>
            <connections>
                <outlet property="btnQty" destination="rW4-sb-2oN" id="elf-2L-f8V"/>
                <outlet property="btnRemoveItem" destination="Eme-x3-lWY" id="aWx-Dt-hot"/>
                <outlet property="consProductsOfBundleHeight" destination="gr8-wv-lgh" id="HXB-Ij-HJ3"/>
                <outlet property="imgProduct" destination="Mpq-fQ-J6n" id="gyj-Mu-9qp"/>
                <outlet property="lblBundleBottomLine" destination="5fE-5y-ncK" id="Z9d-Qa-wCP"/>
                <outlet property="lblCartBottomLine" destination="3qW-kH-VTy" id="GEc-is-riX"/>
                <outlet property="lblDeliveryToMosque" destination="YOn-Gv-FIO" id="Wtx-LW-bZ0"/>
                <outlet property="lblFreeQuantity" destination="Bfm-iG-Onm" id="rsU-lg-6Jm"/>
                <outlet property="lblPrice" destination="0CS-P6-LsP" id="ixy-x6-eru"/>
                <outlet property="lblProductName" destination="epf-Ar-ttJ" id="TQk-bs-pmn"/>
                <outlet property="lblProductSkuValue" destination="c9I-T4-eLD" id="37o-ss-Eeo"/>
                <outlet property="lblQuantityUnit" destination="Vl2-bf-VSs" id="MjR-lR-lmc"/>
                <outlet property="stackViewBundleProducts" destination="p8d-ma-FyU" id="ua4-O1-q8B"/>
                <outlet property="tblProductsOfBundle" destination="BIQ-hr-zWT" id="zcD-u4-KpQ"/>
                <outlet property="viewDeliveryToMosques" destination="NMX-Bn-kwL" id="5Mv-wg-Zq3"/>
            </connections>
            <point key="canvasLocation" x="118.84057971014494" y="124.55357142857142"/>
        </tableViewCell>
    </objects>
    <resources>
        <image name="mosque-icon" width="26" height="26"/>
        <image name="x-mark" width="512" height="512"/>
        <namedColor name="AppTheme_LightGrayColor_#CECECE">
            <color red="0.80784313725490198" green="0.80784313725490198" blue="0.80784313725490198" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="AppTheme_LightGrayOrderColor_#EFEFEF">
            <color red="0.93725490196078431" green="0.93725490196078431" blue="0.93725490196078431" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="AppTheme_ShadowColor_#0000003E">
            <color red="0.0" green="0.0" blue="0.0" alpha="0.25" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
