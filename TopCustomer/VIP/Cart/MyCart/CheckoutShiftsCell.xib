<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="32700.99.1234" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22685"/>
        <capability name="Named colors" minToolsVersion="9.0"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="LoewNextArabic-Bold.ttf">
            <string>LoewNextArabic-Bold</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" id="gTV-IL-0wX" customClass="CheckoutShiftsCell" customModule="TopCustomer" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="187" height="30"/>
            <autoresizingMask key="autoresizingMask"/>
            <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center">
                <rect key="frame" x="0.0" y="0.0" width="187" height="30"/>
                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="LB1-QP-ZTT">
                        <rect key="frame" x="0.0" y="0.0" width="187" height="30"/>
                        <subviews>
                            <button opaque="NO" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="0iI-k8-vyK">
                                <rect key="frame" x="0.0" y="0.0" width="187" height="30"/>
                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                <state key="normal" title="Button"/>
                                <buttonConfiguration key="configuration" style="plain" title=" "/>
                            </button>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="12p-5Z-J8o" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                <rect key="frame" x="0.0" y="0.0" width="187" height="30"/>
                                <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="12"/>
                                <color key="textColor" name="AppTheme_DiffBlackColor_#101113"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <button hidden="YES" opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="ajR-qL-D1P">
                                <rect key="frame" x="0.0" y="0.0" width="187" height="30"/>
                                <state key="normal" title="Button"/>
                                <buttonConfiguration key="configuration" style="plain" title=" ">
                                    <fontDescription key="titleFontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="12"/>
                                    <color key="baseForegroundColor" name="AppTheme_DiffBlackColor_#101113"/>
                                </buttonConfiguration>
                            </button>
                        </subviews>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="12p-5Z-J8o" firstAttribute="leading" secondItem="LB1-QP-ZTT" secondAttribute="leading" id="G0q-KN-msc"/>
                            <constraint firstAttribute="bottom" secondItem="ajR-qL-D1P" secondAttribute="bottom" id="H4M-4m-oFH"/>
                            <constraint firstItem="ajR-qL-D1P" firstAttribute="top" secondItem="LB1-QP-ZTT" secondAttribute="top" id="LvP-Vh-D9K"/>
                            <constraint firstAttribute="width" relation="greaterThanOrEqual" constant="187" id="Nwg-vE-U7A"/>
                            <constraint firstAttribute="trailing" secondItem="12p-5Z-J8o" secondAttribute="trailing" id="TD4-pr-VqE"/>
                            <constraint firstItem="12p-5Z-J8o" firstAttribute="top" secondItem="LB1-QP-ZTT" secondAttribute="top" id="cXr-pL-VyS"/>
                            <constraint firstItem="ajR-qL-D1P" firstAttribute="leading" secondItem="LB1-QP-ZTT" secondAttribute="leading" id="rlD-VQ-8J7"/>
                            <constraint firstAttribute="bottom" secondItem="12p-5Z-J8o" secondAttribute="bottom" id="tol-X5-vax"/>
                            <constraint firstAttribute="trailing" secondItem="ajR-qL-D1P" secondAttribute="trailing" id="xAB-mL-wTb"/>
                        </constraints>
                    </view>
                </subviews>
            </view>
            <viewLayoutGuide key="safeArea" id="ZTg-uK-7eu"/>
            <constraints>
                <constraint firstAttribute="trailing" secondItem="LB1-QP-ZTT" secondAttribute="trailing" id="4Lj-Oa-0dK"/>
                <constraint firstItem="LB1-QP-ZTT" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" id="EtY-b1-VyP"/>
                <constraint firstAttribute="bottom" secondItem="LB1-QP-ZTT" secondAttribute="bottom" id="FhG-a7-vuN"/>
                <constraint firstItem="LB1-QP-ZTT" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" id="v7b-lx-hiD"/>
            </constraints>
            <connections>
                <outlet property="btnCheckoutShift" destination="0iI-k8-vyK" id="IpE-ku-ryY"/>
                <outlet property="btnShift" destination="ajR-qL-D1P" id="cOG-Eg-vAI"/>
                <outlet property="lblShiftTime" destination="12p-5Z-J8o" id="yBW-bU-qOF"/>
            </connections>
            <point key="canvasLocation" x="131" y="-32"/>
        </collectionViewCell>
    </objects>
    <resources>
        <namedColor name="AppTheme_DiffBlackColor_#101113">
            <color red="0.062745098039215685" green="0.066666666666666666" blue="0.074509803921568626" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
