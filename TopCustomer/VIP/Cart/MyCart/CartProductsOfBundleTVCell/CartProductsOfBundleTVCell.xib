<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="22505" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22504"/>
        <capability name="Named colors" minToolsVersion="9.0"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="LoewNextArabic-Bold.ttf">
            <string>LoewNextArabic-Bold</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" rowHeight="50" id="KGk-i7-Jjw" customClass="CartProductsOfBundleTVCell" customModule="TopCustomer" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="375" height="50"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="375" height="50"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <view opaque="NO" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="YVG-08-sU7">
                        <rect key="frame" x="5" y="5" width="365" height="40"/>
                        <subviews>
                            <stackView opaque="NO" contentMode="scaleToFill" spacing="5" translatesAutoresizingMaskIntoConstraints="NO" id="Lxl-fY-rBz">
                                <rect key="frame" x="0.0" y="0.0" width="365" height="40"/>
                                <subviews>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="UeL-j9-Zfi">
                                        <rect key="frame" x="0.0" y="0.0" width="18" height="40"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="18" id="NHE-1Z-rmF"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="10"/>
                                        <color key="textColor" name="AppTheme_BorderColor_#1F1F1F"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="GbF-5Y-xbY">
                                        <rect key="frame" x="23" y="0.0" width="30" height="40"/>
                                        <subviews>
                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="logo_login" translatesAutoresizingMaskIntoConstraints="NO" id="zS5-e1-nmy">
                                                <rect key="frame" x="2.6666666666666679" y="7.6666666666666643" width="25" height="25"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="25" id="qaf-CU-hjN"/>
                                                    <constraint firstAttribute="width" constant="25" id="rYZ-x0-S7W"/>
                                                </constraints>
                                            </imageView>
                                        </subviews>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstItem="zS5-e1-nmy" firstAttribute="centerY" secondItem="GbF-5Y-xbY" secondAttribute="centerY" id="LYK-Z3-d7s"/>
                                            <constraint firstItem="zS5-e1-nmy" firstAttribute="centerX" secondItem="GbF-5Y-xbY" secondAttribute="centerX" id="VIu-TQ-j5N"/>
                                            <constraint firstAttribute="width" constant="30" id="urj-3Z-QZh"/>
                                        </constraints>
                                    </view>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" ambiguous="YES" text="" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="1CO-qe-N40">
                                        <rect key="frame" x="58" y="0.0" width="180" height="40"/>
                                        <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="10"/>
                                        <color key="textColor" name="AppTheme_BorderColor_#1F1F1F"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" ambiguous="YES" text="" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Imf-Ao-fqs">
                                        <rect key="frame" x="243" y="0.0" width="122" height="40"/>
                                        <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="10"/>
                                        <color key="textColor" name="AppTheme_BorderColor_#1F1F1F"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            </stackView>
                        </subviews>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstAttribute="bottom" secondItem="Lxl-fY-rBz" secondAttribute="bottom" id="96m-6L-SPP"/>
                            <constraint firstItem="Lxl-fY-rBz" firstAttribute="leading" secondItem="YVG-08-sU7" secondAttribute="leading" id="IZq-v0-od9"/>
                            <constraint firstItem="Lxl-fY-rBz" firstAttribute="top" secondItem="YVG-08-sU7" secondAttribute="top" id="WGN-EY-hXi"/>
                            <constraint firstAttribute="trailing" secondItem="Lxl-fY-rBz" secondAttribute="trailing" id="pfU-G0-gMP"/>
                        </constraints>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                                <color key="value" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            </userDefinedRuntimeAttribute>
                            <userDefinedRuntimeAttribute type="color" keyPath="shadowColor">
                                <color key="value" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </view>
                </subviews>
                <constraints>
                    <constraint firstAttribute="bottom" secondItem="YVG-08-sU7" secondAttribute="bottom" constant="5" id="ICO-tv-uku"/>
                    <constraint firstAttribute="trailing" secondItem="YVG-08-sU7" secondAttribute="trailing" constant="5" id="qrU-4F-hSj"/>
                    <constraint firstItem="YVG-08-sU7" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" constant="5" id="x01-Rb-Yij"/>
                    <constraint firstItem="YVG-08-sU7" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="5" id="xh6-fG-Nhr"/>
                </constraints>
            </tableViewCellContentView>
            <viewLayoutGuide key="safeArea" id="njF-e1-oar"/>
            <connections>
                <outlet property="imgProduct" destination="zS5-e1-nmy" id="Zkv-JA-VqD"/>
                <outlet property="lblProductCount" destination="UeL-j9-Zfi" id="YeW-ZV-DOV"/>
                <outlet property="lblProductName" destination="1CO-qe-N40" id="Dpl-Z4-own"/>
                <outlet property="lblProductUnit" destination="Imf-Ao-fqs" id="wQp-qV-vYA"/>
                <outlet property="viewMain" destination="YVG-08-sU7" id="YfE-CR-Gk4"/>
            </connections>
            <point key="canvasLocation" x="29" y="20"/>
        </tableViewCell>
    </objects>
    <resources>
        <image name="logo_login" width="100.66666412353516" height="33"/>
        <namedColor name="AppTheme_BorderColor_#1F1F1F">
            <color red="0.12156862745098039" green="0.12156862745098039" blue="0.12156862745098039" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
    </resources>
</document>
