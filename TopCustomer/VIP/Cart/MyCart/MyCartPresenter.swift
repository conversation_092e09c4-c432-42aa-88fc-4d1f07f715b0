
import UIKit

protocol MyCartPresentationProtocol {
    func apiCallForGetCart(dictData:[String:Any])
    func apiResponseGetCart(response:GetCartResponse?,error:Error?)

    func apiCallForAddToCart(dictData:[String:Any])
    func apiResponseAddToCart(response:AddToCartResponse?,error:Error?)

    func apiCallForRemoveFromCart(dictData:[String:Any])
    func apiResponseRemoveFromCart(response:AddToCartResponse?,error:Error?)
    
    func apiCallForCartYouMayAlsoLikeProducts(categoryIDS: String, productIDS: String)
    func apiResponseCartYouMayAlsoLikeProducts(response: CartYouMayAlsoLikeProductsResponse?, error: Error?)

    func apiCallForCheckCart(iCartId: Int)
    func apiResponseForCheckCart(response: CheckCartResponse?, error: Error?)
    
}

class MyCartPresenter: MyCartPresentationProtocol {
    
    // MARK: Objects & Variables
    weak var viewControllerMyCart: MyCartProtocol?
    var interactorMyCart: MyCartInteractorProtocol?
    
    func apiCallForGetCart(dictData:[String:Any]) {
        interactorMyCart?.apiCallForGetCart(dictData: dictData)
    }

    func apiResponseGetCart(response: GetCartResponse?, error: Error?) {
        if let error = error  {
            viewControllerMyCart?.displayAlert(string: error.localizedDescription)
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            return
        }
        
        if code == APIINVALIDAUTHORIZATIONCODE401 {
            AppSingletonObj.displaySessionExpiredAlert(strMsg: response.responseMessage ?? "")
            return
        }

        if code == APICODE400 {
            viewControllerMyCart?.displayAlert(string: response.responseMessage ?? "")
            return
        }
        
        guard let model = response.responseData,code == APISUCCESSCODE200  else {
            return
        }
        self.viewControllerMyCart?.refreshCart(model: model)
    }

    func apiCallForAddToCart(dictData:[String:Any]) {
        interactorMyCart?.apiCallForAddToCart(dictData: dictData)
    }

    func apiResponseAddToCart(response: AddToCartResponse?, error: Error?) {
        if let error = error  {
            viewControllerMyCart?.displayAlert(string: error.localizedDescription)
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            return
        }
        
        if code == APIINVALIDAUTHORIZATIONCODE401 {
            AppSingletonObj.displaySessionExpiredAlert(strMsg: response.responseMessage ?? "")
            return
        }

        if code == APICODE400 {
            viewControllerMyCart?.displayAlert(string: response.responseMessage ?? "")
            return
        }
        
        if code == APICODE203 {
            self.viewControllerMyCart?.displayErrorAlert(strMsg: response.responseMessage ?? "")
            return
        }

        guard let model = response.responseData,code == APISUCCESSCODE200  else {
            return
        }
        self.viewControllerMyCart?.reloadData(obj: model, strMsg: response.responseMessage ?? "")
    }

    func apiCallForRemoveFromCart(dictData:[String:Any]) {
        interactorMyCart?.apiCallForRemoveFromCart(dictData: dictData)
    }

    func apiResponseRemoveFromCart(response: AddToCartResponse?, error: Error?) {
        if let error = error  {
            viewControllerMyCart?.displayAlert(string: error.localizedDescription)
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            return
        }
        
        if code == APIINVALIDAUTHORIZATIONCODE401 {
            AppSingletonObj.displaySessionExpiredAlert(strMsg: response.responseMessage ?? "")
            return
        }

        if code == APICODE400 {
            viewControllerMyCart?.displayAlert(string: response.responseMessage ?? "")
            return
        }
        
        if code == APICODE203 {
            self.viewControllerMyCart?.displayErrorAlert(strMsg: response.responseMessage ?? "")
            return
        }

        guard let model = response.responseData,code == APISUCCESSCODE200  else {
            return
        }
        self.viewControllerMyCart?.displayAlertAndDismiss(string: response.responseMessage ?? "", count: response.responseData?.getCartProducts ?? 0)
    }

    func apiCallForCartYouMayAlsoLikeProducts(categoryIDS: String, productIDS: String) {
        interactorMyCart?.apiCallForCartYouMayAlsoLikeProducts(categoryIDS: categoryIDS, productIDS: productIDS)
    }
    
    func apiResponseCartYouMayAlsoLikeProducts(response: CartYouMayAlsoLikeProductsResponse?, error: Error?) {
        if let error = error  {
            viewControllerMyCart?.displayAlert(string: error.localizedDescription)
            return
        }
        
        guard let response = response,let code = response.statusCode else {
            return
        }
        
        if code == APIINVALIDAUTHORIZATIONCODE401 {
            AppSingletonObj.displaySessionExpiredAlert(strMsg: response.message ?? "")
            return
        }

        if code == APICODE400 {
            viewControllerMyCart?.displayAlert(string: response.message ?? "")
            return
        }
        
        if code == APICODE203 {
            self.viewControllerMyCart?.displayErrorAlert(strMsg: response.message ?? "")
            return
        }

        guard let data = response.data, code == APISUCCESSCODE200  else {
            return
        }
        
        self.viewControllerMyCart?.getCartYouMayAlsoLikeProducts(products: data)
    }
    
    func apiCallForCheckCart(iCartId: Int) {
        if iCartId != 0 {
            interactorMyCart?.apiCallForCheckCart(iCartId: iCartId)
        }
    }
    
    func apiResponseForCheckCart(response: CheckCartResponse?, error: Error?) {
        
        if response?.responseData == nil {
            return
        }
        
        if let error = error  {
            viewControllerMyCart?.displayAlert(string: error.localizedDescription)
            self.viewControllerMyCart?.isCheckCartDone(value: false)
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            self.viewControllerMyCart?.isCheckCartDone(value: false)
            return
        }
        
        if code == APIINVALIDAUTHORIZATIONCODE401 {
            AppSingletonObj.displaySessionExpiredAlert(strMsg: response.responseMessage ?? "")
            self.viewControllerMyCart?.isCheckCartDone(value: false)
            return
        }

        if code == APICODE400 {
            viewControllerMyCart?.displayAlert(string: response.responseMessage ?? "")
            self.viewControllerMyCart?.isCheckCartDone(value: false)
            return
        }
        
        if code == APICODE203 {
            self.viewControllerMyCart?.displayErrorAlert(strMsg: response.responseMessage ?? "")
            self.viewControllerMyCart?.isCheckCartDone(value: false)
            return
        }

        guard let data = response.responseData, code == APISUCCESSCODE200  else {
            self.viewControllerMyCart?.isCheckCartDone(value: false)
            return
        }
        
    }
    
}
