<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="21701" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21678"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="collection view cell content view" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="LoewNextArabic-Bold.ttf">
            <string>LoewNextArabic-Bold</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" reuseIdentifier="MayAlsoLikeTVCell" id="KGk-i7-Jjw" customClass="MayAlsoLikeTVCell" customModule="TopCustomer" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="414" height="245"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="414" height="245"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="A09-cs-etH">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="230"/>
                        <subviews>
                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="20" translatesAutoresizingMaskIntoConstraints="NO" id="QlD-4X-caE">
                                <rect key="frame" x="0.0" y="10" width="414" height="220"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="8Nj-TZ-G2W">
                                        <rect key="frame" x="0.0" y="0.0" width="414" height="25"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="You may also like?" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="ctu-dM-zm2">
                                                <rect key="frame" x="15" y="2" width="384" height="14"/>
                                                <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="14"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                        </subviews>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="25" id="1eJ-aO-zEd"/>
                                            <constraint firstAttribute="trailing" secondItem="ctu-dM-zm2" secondAttribute="trailing" constant="15" id="Hry-Oi-5i9"/>
                                            <constraint firstItem="ctu-dM-zm2" firstAttribute="leading" secondItem="8Nj-TZ-G2W" secondAttribute="leading" constant="15" id="dQi-g0-DDy"/>
                                            <constraint firstItem="ctu-dM-zm2" firstAttribute="top" secondItem="8Nj-TZ-G2W" secondAttribute="top" constant="2" id="ume-jU-G0K"/>
                                        </constraints>
                                    </view>
                                    <collectionView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" dataMode="prototypes" translatesAutoresizingMaskIntoConstraints="NO" id="0ms-A9-574">
                                        <rect key="frame" x="10" y="25" width="394" height="195"/>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="195" id="rUh-mj-GtA"/>
                                        </constraints>
                                        <collectionViewFlowLayout key="collectionViewLayout" scrollDirection="horizontal" automaticEstimatedItemSize="YES" minimumLineSpacing="0.0" minimumInteritemSpacing="0.0" id="ayr-Ek-IY4">
                                            <size key="itemSize" width="150" height="195"/>
                                            <size key="headerReferenceSize" width="0.0" height="0.0"/>
                                            <size key="footerReferenceSize" width="0.0" height="0.0"/>
                                            <inset key="sectionInset" minX="0.0" minY="0.0" maxX="0.0" maxY="0.0"/>
                                        </collectionViewFlowLayout>
                                        <cells>
                                            <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" reuseIdentifier="RecommendationProductCVC" id="sQG-pA-C0t">
                                                <rect key="frame" x="0.0" y="0.0" width="150" height="195"/>
                                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                                <collectionViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" insetsLayoutMarginsFromSafeArea="NO" id="HgE-ZJ-yiE">
                                                    <rect key="frame" x="0.0" y="0.0" width="150" height="195"/>
                                                    <autoresizingMask key="autoresizingMask"/>
                                                </collectionViewCellContentView>
                                            </collectionViewCell>
                                        </cells>
                                    </collectionView>
                                </subviews>
                                <constraints>
                                    <constraint firstItem="0ms-A9-574" firstAttribute="leading" secondItem="QlD-4X-caE" secondAttribute="leading" constant="10" id="8O5-hn-dbD"/>
                                    <constraint firstAttribute="bottom" secondItem="0ms-A9-574" secondAttribute="bottom" id="DZP-cS-NcH"/>
                                    <constraint firstAttribute="trailing" secondItem="0ms-A9-574" secondAttribute="trailing" constant="10" id="iD9-Oz-uQT"/>
                                    <constraint firstItem="0ms-A9-574" firstAttribute="top" secondItem="8Nj-TZ-G2W" secondAttribute="bottom" id="pBF-e3-6gE"/>
                                </constraints>
                            </stackView>
                        </subviews>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstAttribute="bottom" secondItem="QlD-4X-caE" secondAttribute="bottom" id="1zr-cW-eyq"/>
                            <constraint firstAttribute="trailing" secondItem="QlD-4X-caE" secondAttribute="trailing" id="T9t-AA-ejj"/>
                            <constraint firstItem="QlD-4X-caE" firstAttribute="leading" secondItem="A09-cs-etH" secondAttribute="leading" id="UX5-hF-Mza"/>
                            <constraint firstItem="QlD-4X-caE" firstAttribute="top" secondItem="A09-cs-etH" secondAttribute="top" constant="10" id="jIc-e7-cZ8"/>
                        </constraints>
                    </view>
                </subviews>
                <constraints>
                    <constraint firstItem="A09-cs-etH" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" id="RtW-mE-FXx"/>
                    <constraint firstItem="A09-cs-etH" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" id="cgI-hT-QDn"/>
                    <constraint firstAttribute="trailing" secondItem="A09-cs-etH" secondAttribute="trailing" id="eKf-Ui-zn3"/>
                    <constraint firstAttribute="bottom" secondItem="A09-cs-etH" secondAttribute="bottom" id="l5Q-fW-CQH"/>
                </constraints>
            </tableViewCellContentView>
            <viewLayoutGuide key="safeArea" id="njF-e1-oar"/>
            <connections>
                <outlet property="collMayAlsoLikeProducts" destination="0ms-A9-574" id="JQC-4C-g7t"/>
                <outlet property="lblYouMayAlsoLike" destination="ctu-dM-zm2" id="GBW-6v-NuW"/>
            </connections>
            <point key="canvasLocation" x="82" y="20"/>
        </tableViewCell>
    </objects>
    <resources>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
