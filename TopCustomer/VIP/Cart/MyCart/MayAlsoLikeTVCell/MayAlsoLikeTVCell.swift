//
//  MayAlsoLikeTVCell.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 28/08/2023.
//  Copyright © 2023 SOTSYS203. All rights reserved.
//

import UIKit
import TikTokBusinessSDK
import FirebaseAnalytics

protocol MayAlsoLikeTVCellDelegate: NSObject {
    func addItemToCart(productInfo: [String:Any])
}

class MayAlsoLikeTVCell: UITableViewCell {
    
    // MARK: - IBOutlets
    @IBOutlet weak var lblYouMayAlsoLike: UILabel!
    @IBOutlet weak var collMayAlsoLikeProducts: UICollectionView!
    
    // MARK: - Variables
    weak var mayAlsoLikeTVCellDelegate: MayAlsoLikeTVCellDelegate?
    var arrMayAlsoLikeProductsList: [CartYouMayAlsoLikeProductsResponseFields] = []
    
    // MARK: - Func
    override func awakeFromNib() {
        super.awakeFromNib()
        // Initialization code
        self.lblYouMayAlsoLike.text = "You may also like?".localized
        let cellNib = UINib.init(nibName: "RecommendationProductCVC", bundle: nil)
        self.collMayAlsoLikeProducts.register(cellNib, forCellWithReuseIdentifier: "RecommendationProductCVC")
        self.collMayAlsoLikeProducts.delegate = self
        self.collMayAlsoLikeProducts.dataSource = self
        self.lblYouMayAlsoLike.isHidden = true
        
    }
    
    override func setSelected(_ selected: Bool, animated: Bool) {
        super.setSelected(selected, animated: animated)
        // Configure the view for the selected state
    }
    
    func setCartYouMayAlsoLikeProducts(products: [CartYouMayAlsoLikeProductsResponseFields]) {
        if products.count == 0 {
            return
        }
        self.lblYouMayAlsoLike.isHidden = false
        self.arrMayAlsoLikeProductsList = products
        self.collMayAlsoLikeProducts.reloadData()
        if self.arrMayAlsoLikeProductsList.count == 0 { return }
        self.collMayAlsoLikeProducts.scrollToTop {
            if UserDefaults.standard.getLanguage() == UserAPI.VLanguage_userLanguage.ar.rawValue {
                self.collMayAlsoLikeProducts.scrollToItem(at: NSIndexPath(item: self.arrMayAlsoLikeProductsList.count - 1, section: 0) as IndexPath, at: .right, animated: false)
            } else {
                self.collMayAlsoLikeProducts.scrollToItem(at: NSIndexPath(item: 0, section: 0) as IndexPath, at: .left, animated: false)
            }
        }
        
    }
    
}

extension MayAlsoLikeTVCell: UICollectionViewDataSource, UICollectionViewDelegate, UICollectionViewDelegateFlowLayout {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return arrMayAlsoLikeProductsList.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "RecommendationProductCVC", for: indexPath) as! RecommendationProductCVC
        
        cell.imgProduct.kf.indicatorType = .activity
        let url = URL(string: arrMayAlsoLikeProductsList[indexPath.row].image ?? "")
        cell.imgProduct.kf.setImage(with: url , placeholder: UIImage(named: "placeholder_product"))
        
        cell.lblProductName.text = "\(arrMayAlsoLikeProductsList[indexPath.row].name ?? "")"
        cell.lblPrice.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: arrMayAlsoLikeProductsList[indexPath.row].price ?? 0.0))")
        cell.lblProductCategory.text = "\(arrMayAlsoLikeProductsList[indexPath.row].unit ?? "")"
        if self.arrMayAlsoLikeProductsList[indexPath.row].discountPrice != nil && self.arrMayAlsoLikeProductsList[indexPath.row].discountPrice != 0 {
            cell.lblProductName.numberOfLines = 1
            cell.stackDiscount.isHidden = false
            let discount = "\(arrMayAlsoLikeProductsList[indexPath.row].price ?? 0.0)"
            cell.lblDiscountPrice.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: discount, isDiscounted: true)
            
            let originalPrice = Double(arrMayAlsoLikeProductsList[indexPath.row].price ?? 0.0)
            let discountedPrice = Double(arrMayAlsoLikeProductsList[indexPath.row].discountPrice ?? 0.0)
            let a = originalPrice - discountedPrice
            let b = a / originalPrice
            let percent = b * 100
            cell.lblDiscountValue.text = "(\(forTrailingZero(temp: percent.rounded(.up)))% \(ObjKeymessages.kLABEL_OFF))"
            cell.lblPrice.attributedText = ObjKeymessages.getSaudiRiyalSymbol (value: "\(arrMayAlsoLikeProductsList[indexPath.row].discountPrice ?? 0.0)")
        } else {
            cell.stackDiscount.isHidden = true
            cell.lblProductName.numberOfLines = 2
        }
        cell.btnAddToCart.tag = indexPath.row
        cell.btnAddToCart.addTarget(self, action: #selector(addNewProductToCart(_:)), for: .touchUpInside)
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, willDisplay cell: UICollectionViewCell, forItemAt indexPath: IndexPath) {
        if let cell = cell as? RecommendationProductCVC {
            cell.imgProduct.kf.indicatorType = .activity
            let url = URL(string: arrMayAlsoLikeProductsList[indexPath.row].image ?? "")
            cell.imgProduct.kf.setImage(with: url , placeholder: UIImage(named: "placeholder_product"))
        }
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        return CGSize(width: 150, height: 195)
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, minimumLineSpacingForSectionAt section: Int) -> CGFloat {
        return 0
    }
    
    @objc func addNewProductToCart(_ sender: UIButton) {
        let product = arrMayAlsoLikeProductsList[sender.tag]
        var dictParam : [String:Any] = [:]
        dictParam["biProductId"] = product.id
        dictParam["tiIsCheck"] = AddToCartIsCheck.Add.rawValue
        dictParam["iProductQuantity"] = 1
        dictParam["dbPrice"] = product.price
        
        if AppSingletonObj.isConnectedToNetwork() {
            TikTokBusiness.trackEvent("ADD_TO_CART", withProperties: dictParam)
            Analytics.logEvent("ADD_TO_CART", parameters: dictParam)
            self.mayAlsoLikeTVCellDelegate?.addItemToCart(productInfo: dictParam)
        }
    }
    
}
