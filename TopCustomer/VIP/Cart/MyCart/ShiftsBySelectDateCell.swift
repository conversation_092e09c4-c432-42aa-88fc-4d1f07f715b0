//
//  ShiftsBySelectDateCell.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 27/07/2024.
//  Copyright © 2024 SOTSYS203. All rights reserved.
//

import UIKit

class ShiftsBySelectDateCell: UICollectionViewCell {
    @IBOutlet weak var btnCheckoutShift: UIButton!
    @IBOutlet weak var lblShiftTitle: MaterialLocalizeLable!
    @IBOutlet weak var lblShiftTime: MaterialLocalizeLable!
    @IBOutlet weak var lblShiftAvailable: MaterialLocalizeLable!
    
    override func awakeFromNib() {
        super.awakeFromNib()
        // Initialization code
        self.lblShiftTitle.font = .LoewNextArabic.bold(size: 12).font
        self.lblShiftTime.font = .LoewNextArabic.regular(size: 10).font
        self.lblShiftAvailable.font = .LoewNextArabic.bold(size: 12).font
    }

}
