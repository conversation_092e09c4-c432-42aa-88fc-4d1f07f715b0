<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="21701" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21678"/>
        <capability name="Named colors" minToolsVersion="9.0"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="Stack View standard spacing" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="LoewNextArabic-Bold.ttf">
            <string>LoewNextArabic-Bold</string>
        </array>
        <array key="LoewNextArabic-Medium.ttf">
            <string>LoewNextArabic-Medium</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" id="gTV-IL-0wX" customClass="ProductsCollectionViewCell" customModule="TopCustomer" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="174" height="236"/>
            <autoresizingMask key="autoresizingMask"/>
            <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center">
                <rect key="frame" x="0.0" y="0.0" width="174" height="236"/>
                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="5k8-sh-OZQ">
                        <rect key="frame" x="10" y="10" width="154" height="216"/>
                        <subviews>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="249" translatesAutoresizingMaskIntoConstraints="NO" id="IIv-8G-pBg">
                                <rect key="frame" x="0.0" y="0.0" width="154" height="150"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="150" id="0A5-9F-2LI"/>
                                    <constraint firstAttribute="width" constant="154" id="qnq-na-6mU"/>
                                </constraints>
                            </imageView>
                            <stackView opaque="NO" contentMode="scaleToFill" spacingType="standard" translatesAutoresizingMaskIntoConstraints="NO" id="6VL-em-sc1">
                                <rect key="frame" x="0.0" y="8" width="154" height="18"/>
                                <subviews>
                                    <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="2Yj-sz-Ark">
                                        <rect key="frame" x="0.0" y="0.0" width="6" height="18"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Low Quantity" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="o3b-HH-Nha">
                                                <rect key="frame" x="3" y="0.0" width="0.0" height="18"/>
                                                <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="6"/>
                                                <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                        </subviews>
                                        <color key="backgroundColor" name="AppTheme_RedColor_#D82828"/>
                                        <constraints>
                                            <constraint firstAttribute="trailing" secondItem="o3b-HH-Nha" secondAttribute="trailing" constant="3" id="6y5-XT-GOe"/>
                                            <constraint firstItem="o3b-HH-Nha" firstAttribute="top" secondItem="2Yj-sz-Ark" secondAttribute="top" id="KLg-D7-b3Q"/>
                                            <constraint firstAttribute="height" constant="18" id="fbb-tQ-wJk"/>
                                            <constraint firstAttribute="bottom" secondItem="o3b-HH-Nha" secondAttribute="bottom" id="iVR-Fa-iBF"/>
                                            <constraint firstItem="o3b-HH-Nha" firstAttribute="leading" secondItem="2Yj-sz-Ark" secondAttribute="leading" constant="3" id="kD6-QB-rsu"/>
                                        </constraints>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                <real key="value" value="8"/>
                                            </userDefinedRuntimeAttribute>
                                        </userDefinedRuntimeAttributes>
                                    </view>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="yqm-oo-svu">
                                        <rect key="frame" x="0.0" y="0.0" width="154" height="18"/>
                                        <subviews>
                                            <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="aYf-AN-KH4">
                                                <rect key="frame" x="124" y="0.0" width="30" height="18"/>
                                                <subviews>
                                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="o1H-ci-LUt" customClass="MaterialLocalizeImageView" customModule="TopCustomer" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="0.0" width="30" height="18"/>
                                                    </imageView>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Ad" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="SJJ-VH-Syp" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                        <rect key="frame" x="8.5" y="4.5" width="13.5" height="9"/>
                                                        <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="9"/>
                                                        <color key="textColor" name="AppTheme_DiffBlackColor_#101113"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                </subviews>
                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstItem="o1H-ci-LUt" firstAttribute="leading" secondItem="aYf-AN-KH4" secondAttribute="leading" id="NEk-33-JVh"/>
                                                    <constraint firstItem="o1H-ci-LUt" firstAttribute="top" secondItem="aYf-AN-KH4" secondAttribute="top" id="SgR-TG-n7f"/>
                                                    <constraint firstItem="SJJ-VH-Syp" firstAttribute="centerY" secondItem="aYf-AN-KH4" secondAttribute="centerY" id="Z8d-LP-Xn2"/>
                                                    <constraint firstAttribute="trailing" secondItem="o1H-ci-LUt" secondAttribute="trailing" id="gBi-Pp-m8s"/>
                                                    <constraint firstAttribute="height" constant="18" id="lxc-fq-3bM"/>
                                                    <constraint firstAttribute="bottom" secondItem="o1H-ci-LUt" secondAttribute="bottom" id="okT-Oz-y9b"/>
                                                    <constraint firstItem="SJJ-VH-Syp" firstAttribute="centerX" secondItem="aYf-AN-KH4" secondAttribute="centerX" id="u7c-yX-0H6"/>
                                                    <constraint firstAttribute="width" constant="30" id="vzU-C7-BiC"/>
                                                </constraints>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                        <real key="value" value="3"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                                                        <real key="value" value="0.5"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                                                        <color key="value" name="AppTheme_LightGrayColor_#A0A0A0"/>
                                                    </userDefinedRuntimeAttribute>
                                                </userDefinedRuntimeAttributes>
                                            </view>
                                        </subviews>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstItem="aYf-AN-KH4" firstAttribute="top" secondItem="yqm-oo-svu" secondAttribute="top" id="B3m-37-pel"/>
                                            <constraint firstAttribute="height" constant="18" id="Jw6-Bj-F3D"/>
                                            <constraint firstAttribute="trailing" secondItem="aYf-AN-KH4" secondAttribute="trailing" id="VSw-w4-StI"/>
                                        </constraints>
                                    </view>
                                </subviews>
                            </stackView>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="7rm-Mr-7Rv">
                                <rect key="frame" x="0.0" y="162" width="154" height="54"/>
                                <subviews>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="CHc-lY-AID">
                                        <rect key="frame" x="0.0" y="0.0" width="154" height="1"/>
                                        <color key="backgroundColor" name="AppTheme_LightGrayColor_#CECECE"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="0.75" id="hDX-AL-iVl"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                        <color key="textColor" name="AppTheme_StrikeThroughColor_#707070"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="252" verticalCompressionResistancePriority="749" text="Arwa Water 330ml" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="ffH-U1-cF2">
                                        <rect key="frame" x="7" y="8" width="140" height="4"/>
                                        <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="10"/>
                                        <nil key="textColor"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Price" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="rVm-Rv-UUX">
                                        <rect key="frame" x="7" y="20" width="140" height="10"/>
                                        <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="10"/>
                                        <color key="textColor" name="AppTheme_LightGrayColor_#A0A0A0"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <stackView opaque="NO" contentMode="scaleToFill" spacing="5" translatesAutoresizingMaskIntoConstraints="NO" id="pfy-Ge-cdw">
                                        <rect key="frame" x="7" y="38" width="127" height="8"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="SAR 19.00" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="EhI-ow-C9W">
                                                <rect key="frame" x="0.0" y="0.0" width="41.5" height="8"/>
                                                <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="8"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="19.00 SAR" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="2s9-DJ-sLo">
                                                <rect key="frame" x="46.5" y="0.0" width="41.5" height="8"/>
                                                <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="8"/>
                                                <color key="textColor" name="AppTheme_RedColor_#D82828"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="253" verticalHuggingPriority="251" text="(10% off)" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="LcZ-J4-ily">
                                                <rect key="frame" x="93" y="0.0" width="34" height="8"/>
                                                <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="8"/>
                                                <color key="textColor" name="AppTheme_RedColor_#D82828"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                        </subviews>
                                    </stackView>
                                </subviews>
                                <constraints>
                                    <constraint firstItem="pfy-Ge-cdw" firstAttribute="leading" secondItem="ffH-U1-cF2" secondAttribute="leading" id="Al6-3C-fij"/>
                                    <constraint firstItem="ffH-U1-cF2" firstAttribute="leading" secondItem="7rm-Mr-7Rv" secondAttribute="leading" constant="7" id="HZo-us-gta"/>
                                    <constraint firstItem="CHc-lY-AID" firstAttribute="top" secondItem="7rm-Mr-7Rv" secondAttribute="top" id="HvE-S4-so9"/>
                                    <constraint firstItem="pfy-Ge-cdw" firstAttribute="top" secondItem="rVm-Rv-UUX" secondAttribute="bottom" constant="8" id="IMJ-65-rx1"/>
                                    <constraint firstItem="ffH-U1-cF2" firstAttribute="top" secondItem="7rm-Mr-7Rv" secondAttribute="top" constant="8" id="Jmp-6F-Efc"/>
                                    <constraint firstAttribute="trailing" secondItem="CHc-lY-AID" secondAttribute="trailing" id="K8P-k5-h9a"/>
                                    <constraint firstItem="CHc-lY-AID" firstAttribute="leading" secondItem="7rm-Mr-7Rv" secondAttribute="leading" id="KDn-Gh-Ref"/>
                                    <constraint firstItem="rVm-Rv-UUX" firstAttribute="top" secondItem="ffH-U1-cF2" secondAttribute="bottom" constant="8" id="KF9-OB-a0a"/>
                                    <constraint firstAttribute="trailing" relation="greaterThanOrEqual" secondItem="pfy-Ge-cdw" secondAttribute="trailing" constant="7" id="LtK-wQ-dii"/>
                                    <constraint firstItem="rVm-Rv-UUX" firstAttribute="trailing" secondItem="ffH-U1-cF2" secondAttribute="trailing" id="WVl-yb-NJa"/>
                                    <constraint firstAttribute="trailing" secondItem="ffH-U1-cF2" secondAttribute="trailing" constant="7" id="drn-Mj-3iF"/>
                                    <constraint firstItem="rVm-Rv-UUX" firstAttribute="leading" secondItem="ffH-U1-cF2" secondAttribute="leading" id="rJu-Xo-A45"/>
                                    <constraint firstAttribute="bottom" secondItem="pfy-Ge-cdw" secondAttribute="bottom" constant="8" id="zfU-gh-4SP"/>
                                </constraints>
                            </view>
                        </subviews>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstAttribute="bottom" secondItem="7rm-Mr-7Rv" secondAttribute="bottom" id="0tJ-22-Ebg"/>
                            <constraint firstItem="6VL-em-sc1" firstAttribute="top" secondItem="5k8-sh-OZQ" secondAttribute="top" constant="8" id="1FM-4H-V6m"/>
                            <constraint firstItem="7rm-Mr-7Rv" firstAttribute="leading" secondItem="5k8-sh-OZQ" secondAttribute="leading" id="2Mg-zm-UKU"/>
                            <constraint firstItem="IIv-8G-pBg" firstAttribute="top" secondItem="5k8-sh-OZQ" secondAttribute="top" id="4Rj-42-pAr"/>
                            <constraint firstAttribute="trailing" secondItem="IIv-8G-pBg" secondAttribute="trailing" id="HC5-LI-tP9"/>
                            <constraint firstItem="IIv-8G-pBg" firstAttribute="leading" secondItem="5k8-sh-OZQ" secondAttribute="leading" id="NuF-nF-iNS"/>
                            <constraint firstAttribute="trailing" secondItem="6VL-em-sc1" secondAttribute="trailing" id="W9L-Sf-cKI"/>
                            <constraint firstAttribute="trailing" secondItem="7rm-Mr-7Rv" secondAttribute="trailing" id="YoV-MI-sG0"/>
                            <constraint firstItem="7rm-Mr-7Rv" firstAttribute="top" secondItem="IIv-8G-pBg" secondAttribute="bottom" constant="12" id="Z8D-3m-Pqx"/>
                            <constraint firstItem="6VL-em-sc1" firstAttribute="leading" secondItem="5k8-sh-OZQ" secondAttribute="leading" id="yTn-sC-AtI"/>
                        </constraints>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                <real key="value" value="7"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </view>
                </subviews>
            </view>
            <viewLayoutGuide key="safeArea" id="ZTg-uK-7eu"/>
            <constraints>
                <constraint firstItem="5k8-sh-OZQ" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" constant="10" id="3OH-Tb-QX3"/>
                <constraint firstItem="5k8-sh-OZQ" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" constant="10" id="8Mm-5R-xjf"/>
                <constraint firstAttribute="bottom" secondItem="5k8-sh-OZQ" secondAttribute="bottom" constant="10" id="fMK-yL-Psr"/>
                <constraint firstAttribute="trailing" secondItem="5k8-sh-OZQ" secondAttribute="trailing" constant="10" id="oQK-fe-xlB"/>
            </constraints>
            <size key="customSize" width="139" height="201"/>
            <connections>
                <outlet property="imgProduct" destination="IIv-8G-pBg" id="Ncn-Td-Zux"/>
                <outlet property="lblLowQuantity" destination="o3b-HH-Nha" id="zkO-at-fhR"/>
                <outlet property="lblOffer" destination="LcZ-J4-ily" id="oaE-gE-xg3"/>
                <outlet property="lblOldPrice" destination="2s9-DJ-sLo" id="1GT-D2-Wtp"/>
                <outlet property="lblPrice" destination="EhI-ow-C9W" id="oN8-rC-Hmw"/>
                <outlet property="lblPriceTitle" destination="rVm-Rv-UUX" id="wOk-Sp-Xps"/>
                <outlet property="lblProductName" destination="ffH-U1-cF2" id="2LN-rs-XVA"/>
                <outlet property="lblTitle" destination="SJJ-VH-Syp" id="7F6-gI-Xnw"/>
                <outlet property="viewBG" destination="5k8-sh-OZQ" id="iaT-BK-BuW"/>
                <outlet property="viewBookmark" destination="aYf-AN-KH4" id="iSA-ub-erH"/>
                <outlet property="viewLowQuantity" destination="2Yj-sz-Ark" id="jf6-t9-GB8"/>
            </connections>
            <point key="canvasLocation" x="195.6521739130435" y="152.67857142857142"/>
        </collectionViewCell>
    </objects>
    <resources>
        <namedColor name="AppTheme_DiffBlackColor_#101113">
            <color red="0.062745098039215685" green="0.066666666666666666" blue="0.074509803921568626" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="AppTheme_LightGrayColor_#A0A0A0">
            <color red="0.62745098039215685" green="0.62745098039215685" blue="0.62745098039215685" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="AppTheme_LightGrayColor_#CECECE">
            <color red="0.80784313725490198" green="0.80784313725490198" blue="0.80784313725490198" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="AppTheme_RedColor_#D82828">
            <color red="0.84705882352941175" green="0.15686274509803921" blue="0.15686274509803921" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="AppTheme_StrikeThroughColor_#707070">
            <color red="0.4392156862745098" green="0.4392156862745098" blue="0.4392156862745098" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
