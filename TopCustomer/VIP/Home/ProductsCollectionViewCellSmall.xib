<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="23504" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="23506"/>
        <capability name="Named colors" minToolsVersion="9.0"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="Stack View standard spacing" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="LoewNextArabic-Bold.ttf">
            <string>LoewNextArabic-Bold</string>
        </array>
        <array key="LoewNextArabic-Medium.ttf">
            <string>LoewNextArabic-Medium</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" id="gTV-IL-0wX" customClass="ProductsCollectionViewCellSmall" customModule="TopCustomer" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="230" height="230"/>
            <autoresizingMask key="autoresizingMask"/>
            <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center">
                <rect key="frame" x="0.0" y="0.0" width="230" height="230"/>
                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="5k8-sh-OZQ">
                        <rect key="frame" x="5" y="5" width="220" height="220"/>
                        <subviews>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="249" translatesAutoresizingMaskIntoConstraints="NO" id="IIv-8G-pBg">
                                <rect key="frame" x="0.0" y="0.0" width="220" height="110"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="110" id="0A5-9F-2LI"/>
                                    <constraint firstAttribute="width" constant="154" id="qnq-na-6mU"/>
                                </constraints>
                                <variation key="default">
                                    <mask key="constraints">
                                        <exclude reference="qnq-na-6mU"/>
                                    </mask>
                                </variation>
                            </imageView>
                            <stackView opaque="NO" contentMode="scaleToFill" spacingType="standard" translatesAutoresizingMaskIntoConstraints="NO" id="Ygm-y6-2J4">
                                <rect key="frame" x="0.0" y="8" width="220" height="18"/>
                                <subviews>
                                    <view contentMode="scaleToFill" ambiguous="YES" translatesAutoresizingMaskIntoConstraints="NO" id="6iJ-rQ-zaN">
                                        <rect key="frame" x="0.0" y="0.0" width="56" height="18"/>
                                        <subviews>
                                            <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="2Yj-sz-Ark">
                                                <rect key="frame" x="0.0" y="0.0" width="45" height="18"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" fixedFrame="YES" text="Low Quantity" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="o3b-HH-Nha">
                                                        <rect key="frame" x="0.0" y="0.0" width="41.5" height="18"/>
                                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                                        <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="6"/>
                                                        <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                </subviews>
                                                <color key="backgroundColor" name="AppTheme_RedColor_#D82828"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="18" id="AYk-P8-ctB"/>
                                                    <constraint firstAttribute="width" constant="45" id="LVf-vR-WIx"/>
                                                </constraints>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                        <real key="value" value="8"/>
                                                    </userDefinedRuntimeAttribute>
                                                </userDefinedRuntimeAttributes>
                                            </view>
                                        </subviews>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstItem="2Yj-sz-Ark" firstAttribute="leading" secondItem="6iJ-rQ-zaN" secondAttribute="leading" id="Vsr-5q-KYm"/>
                                            <constraint firstItem="2Yj-sz-Ark" firstAttribute="top" secondItem="6iJ-rQ-zaN" secondAttribute="top" id="deR-Vm-epB"/>
                                            <constraint firstAttribute="bottom" secondItem="2Yj-sz-Ark" secondAttribute="bottom" id="lZx-hQ-Ra1"/>
                                        </constraints>
                                    </view>
                                    <view contentMode="scaleToFill" ambiguous="YES" translatesAutoresizingMaskIntoConstraints="NO" id="oG7-BN-daP">
                                        <rect key="frame" x="64" y="0.0" width="156" height="18"/>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    </view>
                                    <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="QDp-01-Kzo">
                                        <rect key="frame" x="224" y="0.0" width="0.0" height="18"/>
                                        <subviews>
                                            <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="o6v-wC-d0U">
                                                <rect key="frame" x="-30" y="0.0" width="30" height="18"/>
                                                <subviews>
                                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="AUJ-a8-yae" customClass="MaterialLocalizeImageView" customModule="TopCustomer" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="0.0" width="30" height="18"/>
                                                    </imageView>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Ad" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="AJT-k6-Vco" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                        <rect key="frame" x="8.5" y="4.5" width="13.5" height="9"/>
                                                        <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="9"/>
                                                        <color key="textColor" name="AppTheme_DiffBlackColor_#101113"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                </subviews>
                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstItem="AJT-k6-Vco" firstAttribute="centerX" secondItem="o6v-wC-d0U" secondAttribute="centerX" id="1ZH-qa-1Ir"/>
                                                    <constraint firstAttribute="width" constant="30" id="3Gg-6Z-Ple"/>
                                                    <constraint firstAttribute="bottom" secondItem="AUJ-a8-yae" secondAttribute="bottom" id="5VS-ON-ESi"/>
                                                    <constraint firstAttribute="height" constant="18" id="SnN-7M-1eP"/>
                                                    <constraint firstItem="AJT-k6-Vco" firstAttribute="centerY" secondItem="o6v-wC-d0U" secondAttribute="centerY" id="VIh-vq-jyf"/>
                                                    <constraint firstItem="AUJ-a8-yae" firstAttribute="top" secondItem="o6v-wC-d0U" secondAttribute="top" id="gL5-yU-f8N"/>
                                                    <constraint firstAttribute="trailing" secondItem="AUJ-a8-yae" secondAttribute="trailing" id="kSB-Wc-VmX"/>
                                                    <constraint firstItem="AUJ-a8-yae" firstAttribute="leading" secondItem="o6v-wC-d0U" secondAttribute="leading" id="vRM-vi-oLU"/>
                                                </constraints>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                        <real key="value" value="3"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                                                        <real key="value" value="0.5"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                                                        <color key="value" name="AppTheme_LightGrayColor_#A0A0A0"/>
                                                    </userDefinedRuntimeAttribute>
                                                </userDefinedRuntimeAttributes>
                                            </view>
                                        </subviews>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstItem="o6v-wC-d0U" firstAttribute="top" secondItem="QDp-01-Kzo" secondAttribute="top" id="RAL-5b-Rmh"/>
                                            <constraint firstAttribute="trailing" secondItem="o6v-wC-d0U" secondAttribute="trailing" id="cc1-FH-P29"/>
                                            <constraint firstAttribute="height" constant="18" id="e8b-8W-AtY"/>
                                        </constraints>
                                    </view>
                                </subviews>
                            </stackView>
                            <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="pAH-Q6-BIm">
                                <rect key="frame" x="50" y="100" width="170" height="22"/>
                                <subviews>
                                    <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="1j1-KS-gXn">
                                        <rect key="frame" x="2" y="2" width="166" height="18"/>
                                        <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="9"/>
                                        <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="discount-icon" translatesAutoresizingMaskIntoConstraints="NO" id="dfb-ey-oLX">
                                        <rect key="frame" x="141" y="0.0" width="24" height="22"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="22" id="dsM-p4-vQy"/>
                                        </constraints>
                                    </imageView>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="1j1-KS-gXn" firstAttribute="leading" secondItem="pAH-Q6-BIm" secondAttribute="leading" constant="2" id="AtO-kU-uy9"/>
                                    <constraint firstAttribute="trailing" secondItem="1j1-KS-gXn" secondAttribute="trailing" constant="2" id="Jgc-qY-5Ly"/>
                                    <constraint firstItem="dfb-ey-oLX" firstAttribute="top" secondItem="pAH-Q6-BIm" secondAttribute="top" id="Kd8-bE-Ndm"/>
                                    <constraint firstAttribute="bottom" secondItem="1j1-KS-gXn" secondAttribute="bottom" constant="2" id="PAU-YQ-sAU"/>
                                    <constraint firstAttribute="trailing" secondItem="dfb-ey-oLX" secondAttribute="trailing" constant="5" id="c3Q-42-j8v"/>
                                    <constraint firstItem="1j1-KS-gXn" firstAttribute="top" secondItem="pAH-Q6-BIm" secondAttribute="top" constant="2" id="mDe-0l-LJY"/>
                                    <constraint firstAttribute="bottom" secondItem="dfb-ey-oLX" secondAttribute="bottom" id="pYf-ge-2Lm"/>
                                </constraints>
                            </view>
                            <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="0q9-Ow-ASp" userLabel="Quantities Discount">
                                <rect key="frame" x="15" y="100" width="203" height="20"/>
                                <subviews>
                                    <stackView opaque="NO" contentMode="scaleToFill" alignment="center" translatesAutoresizingMaskIntoConstraints="NO" id="GfP-Bz-fUa">
                                        <rect key="frame" x="0.0" y="0.0" width="203" height="20"/>
                                        <subviews>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="2Ym-oY-9a9">
                                                <rect key="frame" x="0.0" y="0.0" width="19" height="20"/>
                                                <subviews>
                                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="quantities_discount_icon" translatesAutoresizingMaskIntoConstraints="NO" id="Bt8-Cj-gv6">
                                                        <rect key="frame" x="2" y="2.5" width="15" height="15"/>
                                                        <constraints>
                                                            <constraint firstAttribute="width" constant="15" id="fl9-vN-d9v"/>
                                                            <constraint firstAttribute="height" constant="15" id="xQU-TK-nvo"/>
                                                        </constraints>
                                                    </imageView>
                                                </subviews>
                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstItem="Bt8-Cj-gv6" firstAttribute="centerX" secondItem="2Ym-oY-9a9" secondAttribute="centerX" id="3nj-Mz-CQv"/>
                                                    <constraint firstItem="Bt8-Cj-gv6" firstAttribute="centerY" secondItem="2Ym-oY-9a9" secondAttribute="centerY" id="AVv-b3-cNr"/>
                                                    <constraint firstAttribute="width" constant="19" id="JwS-tk-dtp"/>
                                                </constraints>
                                            </view>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Quantities Discount" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="eDp-uO-TW4" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                <rect key="frame" x="19" y="6.5" width="184" height="7"/>
                                                <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="7"/>
                                                <color key="textColor" name="AppTheme_FieldBGColor_#F7F7F7"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                        </subviews>
                                    </stackView>
                                </subviews>
                                <color key="backgroundColor" name="AppTheme_DiscountGreenColor_#05B13E"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="20" id="6py-hH-wae"/>
                                    <constraint firstAttribute="bottom" secondItem="GfP-Bz-fUa" secondAttribute="bottom" id="NTJ-wH-Mqu"/>
                                    <constraint firstItem="GfP-Bz-fUa" firstAttribute="top" secondItem="0q9-Ow-ASp" secondAttribute="top" id="QLs-bv-gKU"/>
                                    <constraint firstAttribute="trailing" secondItem="GfP-Bz-fUa" secondAttribute="trailing" id="cgX-Qa-EIG"/>
                                    <constraint firstItem="GfP-Bz-fUa" firstAttribute="leading" secondItem="0q9-Ow-ASp" secondAttribute="leading" id="f2y-EN-1cS"/>
                                </constraints>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                        <real key="value" value="6"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="7rm-Mr-7Rv">
                                <rect key="frame" x="0.0" y="122" width="220" height="98"/>
                                <subviews>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="CHc-lY-AID">
                                        <rect key="frame" x="0.0" y="0.0" width="220" height="1"/>
                                        <color key="backgroundColor" name="AppTheme_LightGrayColor_#CECECE"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="0.75" id="hDX-AL-iVl"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                        <color key="textColor" name="AppTheme_StrikeThroughColor_#707070"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="252" verticalCompressionResistancePriority="749" text="Arwa Water 330ml" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="ffH-U1-cF2" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                        <rect key="frame" x="7" y="8" width="206" height="0.0"/>
                                        <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="10"/>
                                        <nil key="textColor"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Price" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="rVm-Rv-UUX" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                        <rect key="frame" x="7" y="16" width="206" height="8"/>
                                        <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="8"/>
                                        <color key="textColor" name="AppTheme_LightGrayColor_#A0A0A0"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="5" translatesAutoresizingMaskIntoConstraints="NO" id="pfy-Ge-cdw">
                                        <rect key="frame" x="7" y="32" width="41.5" height="58"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="SAR 19.00" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="EhI-ow-C9W" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                <rect key="frame" x="0.0" y="0.0" width="41.5" height="24"/>
                                                <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="12"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="PMr-D7-MPc" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                <rect key="frame" x="0.0" y="26.5" width="41.5" height="0.0"/>
                                                <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="9"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <stackView opaque="NO" contentMode="scaleToFill" spacing="5" translatesAutoresizingMaskIntoConstraints="NO" id="QsE-et-80Y">
                                                <rect key="frame" x="0.0" y="29" width="41.5" height="8"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="1000" verticalHuggingPriority="251" horizontalCompressionResistancePriority="1000" text="19.00 SAR" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="2s9-DJ-sLo" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="0.0" width="41.5" height="8"/>
                                                        <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="8"/>
                                                        <color key="textColor" name="AppTheme_RedColor_#D82828"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="253" verticalHuggingPriority="251" text="(10% off)" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="LcZ-J4-ily" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="0.0" width="0.0" height="8"/>
                                                        <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="8"/>
                                                        <color key="textColor" name="AppTheme_RedColor_#D82828"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                </subviews>
                                            </stackView>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="eoo-ga-ZgE">
                                                <rect key="frame" x="0.0" y="42" width="41.5" height="16"/>
                                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                            </view>
                                        </subviews>
                                        <constraints>
                                            <constraint firstItem="eoo-ga-ZgE" firstAttribute="leading" secondItem="pfy-Ge-cdw" secondAttribute="leading" id="FY3-hI-CXU"/>
                                            <constraint firstAttribute="trailing" secondItem="EhI-ow-C9W" secondAttribute="trailing" id="HTA-m4-uvb"/>
                                            <constraint firstAttribute="bottom" secondItem="eoo-ga-ZgE" secondAttribute="bottom" id="ZvW-pc-nBX"/>
                                            <constraint firstItem="eoo-ga-ZgE" firstAttribute="top" secondItem="QsE-et-80Y" secondAttribute="bottom" id="fCc-Z3-sfL"/>
                                            <constraint firstAttribute="trailing" secondItem="eoo-ga-ZgE" secondAttribute="trailing" id="fml-VZ-uWZ"/>
                                            <constraint firstItem="EhI-ow-C9W" firstAttribute="leading" secondItem="pfy-Ge-cdw" secondAttribute="leading" id="stC-yL-lyG"/>
                                        </constraints>
                                    </stackView>
                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="kIT-Pg-pqf">
                                        <rect key="frame" x="48.5" y="93" width="164.5" height="0.0"/>
                                        <subviews>
                                            <button hidden="YES" opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="brJ-md-pla">
                                                <rect key="frame" x="0.0" y="0.0" width="28" height="28"/>
                                                <color key="backgroundColor" name="AppTheme_BlueColor_#012CDA"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="28" id="N8t-Uy-gxe"/>
                                                    <constraint firstAttribute="width" constant="28" id="ggK-Ia-5MB"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="20"/>
                                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                <state key="normal" title="+">
                                                    <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                </state>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                        <real key="value" value="14"/>
                                                    </userDefinedRuntimeAttribute>
                                                </userDefinedRuntimeAttributes>
                                            </button>
                                        </subviews>
                                    </stackView>
                                </subviews>
                                <constraints>
                                    <constraint firstItem="pfy-Ge-cdw" firstAttribute="leading" secondItem="ffH-U1-cF2" secondAttribute="leading" id="Al6-3C-fij"/>
                                    <constraint firstItem="kIT-Pg-pqf" firstAttribute="leading" secondItem="pfy-Ge-cdw" secondAttribute="trailing" id="Ecw-ni-3V1"/>
                                    <constraint firstAttribute="bottom" secondItem="kIT-Pg-pqf" secondAttribute="bottom" constant="5" id="Gus-LC-6nN"/>
                                    <constraint firstItem="ffH-U1-cF2" firstAttribute="leading" secondItem="7rm-Mr-7Rv" secondAttribute="leading" constant="7" id="HZo-us-gta"/>
                                    <constraint firstItem="CHc-lY-AID" firstAttribute="top" secondItem="7rm-Mr-7Rv" secondAttribute="top" id="HvE-S4-so9"/>
                                    <constraint firstItem="pfy-Ge-cdw" firstAttribute="top" secondItem="rVm-Rv-UUX" secondAttribute="bottom" constant="8" id="IMJ-65-rx1"/>
                                    <constraint firstItem="ffH-U1-cF2" firstAttribute="top" secondItem="7rm-Mr-7Rv" secondAttribute="top" constant="8" id="Jmp-6F-Efc"/>
                                    <constraint firstAttribute="trailing" secondItem="CHc-lY-AID" secondAttribute="trailing" id="K8P-k5-h9a"/>
                                    <constraint firstItem="CHc-lY-AID" firstAttribute="leading" secondItem="7rm-Mr-7Rv" secondAttribute="leading" id="KDn-Gh-Ref"/>
                                    <constraint firstItem="rVm-Rv-UUX" firstAttribute="top" secondItem="ffH-U1-cF2" secondAttribute="bottom" constant="8" id="KF9-OB-a0a"/>
                                    <constraint firstAttribute="trailing" secondItem="kIT-Pg-pqf" secondAttribute="trailing" constant="7" id="WHU-hM-Fza"/>
                                    <constraint firstItem="rVm-Rv-UUX" firstAttribute="trailing" secondItem="ffH-U1-cF2" secondAttribute="trailing" id="WVl-yb-NJa"/>
                                    <constraint firstAttribute="trailing" secondItem="ffH-U1-cF2" secondAttribute="trailing" constant="7" id="drn-Mj-3iF"/>
                                    <constraint firstAttribute="trailing" secondItem="pfy-Ge-cdw" secondAttribute="trailing" id="fOL-vW-JdW"/>
                                    <constraint firstItem="rVm-Rv-UUX" firstAttribute="leading" secondItem="ffH-U1-cF2" secondAttribute="leading" id="rJu-Xo-A45"/>
                                    <constraint firstAttribute="bottom" secondItem="pfy-Ge-cdw" secondAttribute="bottom" constant="8" id="zfU-gh-4SP"/>
                                </constraints>
                            </view>
                        </subviews>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstAttribute="bottom" secondItem="7rm-Mr-7Rv" secondAttribute="bottom" id="0tJ-22-Ebg"/>
                            <constraint firstItem="7rm-Mr-7Rv" firstAttribute="leading" secondItem="5k8-sh-OZQ" secondAttribute="leading" id="2Mg-zm-UKU"/>
                            <constraint firstItem="IIv-8G-pBg" firstAttribute="top" secondItem="5k8-sh-OZQ" secondAttribute="top" id="4Rj-42-pAr"/>
                            <constraint firstAttribute="trailing" secondItem="0q9-Ow-ASp" secondAttribute="trailing" constant="2" id="646-yZ-B8c"/>
                            <constraint firstAttribute="trailing" secondItem="pAH-Q6-BIm" secondAttribute="trailing" id="9uW-eH-0hk"/>
                            <constraint firstAttribute="trailing" secondItem="IIv-8G-pBg" secondAttribute="trailing" id="HC5-LI-tP9"/>
                            <constraint firstItem="IIv-8G-pBg" firstAttribute="leading" secondItem="5k8-sh-OZQ" secondAttribute="leading" id="NuF-nF-iNS"/>
                            <constraint firstAttribute="trailing" secondItem="Ygm-y6-2J4" secondAttribute="trailing" id="VzO-gZ-dbx"/>
                            <constraint firstItem="0q9-Ow-ASp" firstAttribute="leading" secondItem="5k8-sh-OZQ" secondAttribute="leading" constant="15" id="W5h-lI-x07"/>
                            <constraint firstAttribute="trailing" secondItem="7rm-Mr-7Rv" secondAttribute="trailing" id="YoV-MI-sG0"/>
                            <constraint firstItem="7rm-Mr-7Rv" firstAttribute="top" secondItem="IIv-8G-pBg" secondAttribute="bottom" constant="12" id="Z8D-3m-Pqx"/>
                            <constraint firstItem="7rm-Mr-7Rv" firstAttribute="top" secondItem="pAH-Q6-BIm" secondAttribute="bottom" id="crn-tx-vKS"/>
                            <constraint firstItem="pAH-Q6-BIm" firstAttribute="top" secondItem="IIv-8G-pBg" secondAttribute="bottom" constant="-10" id="eVn-Sh-gqU"/>
                            <constraint firstItem="7rm-Mr-7Rv" firstAttribute="top" secondItem="0q9-Ow-ASp" secondAttribute="bottom" constant="2" id="roq-3d-xih"/>
                            <constraint firstItem="pAH-Q6-BIm" firstAttribute="leading" secondItem="5k8-sh-OZQ" secondAttribute="leading" constant="50" id="sem-FR-BjR"/>
                            <constraint firstItem="Ygm-y6-2J4" firstAttribute="top" secondItem="5k8-sh-OZQ" secondAttribute="top" constant="8" id="uUx-jl-zjk"/>
                            <constraint firstItem="Ygm-y6-2J4" firstAttribute="leading" secondItem="5k8-sh-OZQ" secondAttribute="leading" id="vt5-9u-Sku"/>
                        </constraints>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                <real key="value" value="7"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </view>
                    <view hidden="YES" userInteractionEnabled="NO" alpha="0.65000000000000002" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="gZE-pn-WRz">
                        <rect key="frame" x="5" y="5" width="220" height="220"/>
                        <color key="backgroundColor" white="0.0" alpha="0.20000000000000001" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                <real key="value" value="7"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </view>
                </subviews>
            </view>
            <viewLayoutGuide key="safeArea" id="ZTg-uK-7eu"/>
            <constraints>
                <constraint firstItem="5k8-sh-OZQ" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" constant="5" id="3OH-Tb-QX3"/>
                <constraint firstItem="5k8-sh-OZQ" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" constant="5" id="8Mm-5R-xjf"/>
                <constraint firstItem="gZE-pn-WRz" firstAttribute="bottom" secondItem="5k8-sh-OZQ" secondAttribute="bottom" id="Qhl-di-A25"/>
                <constraint firstItem="gZE-pn-WRz" firstAttribute="leading" secondItem="5k8-sh-OZQ" secondAttribute="leading" id="TyC-0f-oxJ"/>
                <constraint firstAttribute="bottom" secondItem="5k8-sh-OZQ" secondAttribute="bottom" constant="5" id="fMK-yL-Psr"/>
                <constraint firstItem="gZE-pn-WRz" firstAttribute="top" secondItem="5k8-sh-OZQ" secondAttribute="top" id="mfH-bs-4Eg"/>
                <constraint firstAttribute="trailing" secondItem="5k8-sh-OZQ" secondAttribute="trailing" constant="5" id="oQK-fe-xlB"/>
                <constraint firstItem="gZE-pn-WRz" firstAttribute="trailing" secondItem="5k8-sh-OZQ" secondAttribute="trailing" id="rgp-6K-bOn"/>
            </constraints>
            <size key="customSize" width="139" height="201"/>
            <connections>
                <outlet property="btnAddToCart" destination="brJ-md-pla" id="U8S-po-qoY"/>
                <outlet property="imgProduct" destination="IIv-8G-pBg" id="Ncn-Td-Zux"/>
                <outlet property="lblLowQuantity" destination="o3b-HH-Nha" id="zkO-at-fhR"/>
                <outlet property="lblOffer" destination="LcZ-J4-ily" id="oaE-gE-xg3"/>
                <outlet property="lblOfferTitle" destination="1j1-KS-gXn" id="sKH-5U-aWX"/>
                <outlet property="lblOldPrice" destination="2s9-DJ-sLo" id="1GT-D2-Wtp"/>
                <outlet property="lblPrice" destination="EhI-ow-C9W" id="oN8-rC-Hmw"/>
                <outlet property="lblPriceTitle" destination="rVm-Rv-UUX" id="wOk-Sp-Xps"/>
                <outlet property="lblProductName" destination="ffH-U1-cF2" id="2LN-rs-XVA"/>
                <outlet property="lblQuantitiesDiscount" destination="eDp-uO-TW4" id="kX5-cy-dD6"/>
                <outlet property="lblQuantitiesDiscountPrice" destination="PMr-D7-MPc" id="s6C-xa-Isx"/>
                <outlet property="lblTitle" destination="AJT-k6-Vco" id="ezn-lS-E5h"/>
                <outlet property="stackViewDiscountPrice" destination="QsE-et-80Y" id="FH3-aJ-pJF"/>
                <outlet property="viewBG" destination="5k8-sh-OZQ" id="iaT-BK-BuW"/>
                <outlet property="viewBookmark" destination="o6v-wC-d0U" id="xav-0A-RGB"/>
                <outlet property="viewLowQuantity" destination="2Yj-sz-Ark" id="jf6-t9-GB8"/>
                <outlet property="viewOfferTitle" destination="pAH-Q6-BIm" id="s4l-aS-8gb"/>
                <outlet property="viewOutOfStock" destination="gZE-pn-WRz" id="lEx-fI-Bqw"/>
                <outlet property="viewQuantitiesDiscount" destination="0q9-Ow-ASp" id="2CB-lD-8QB"/>
            </connections>
            <point key="canvasLocation" x="195.6521739130435" y="152.34375"/>
        </collectionViewCell>
    </objects>
    <resources>
        <image name="discount-icon" width="24" height="24"/>
        <image name="quantities_discount_icon" width="900" height="900"/>
        <namedColor name="AppTheme_BlueColor_#012CDA">
            <color red="0.0039215686274509803" green="0.17254901960784313" blue="0.85490196078431369" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="AppTheme_DiffBlackColor_#101113">
            <color red="0.062745098039215685" green="0.066666666666666666" blue="0.074509803921568626" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="AppTheme_DiscountGreenColor_#05B13E">
            <color red="0.019607843137254902" green="0.69411764705882351" blue="0.24313725490196078" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="AppTheme_FieldBGColor_#F7F7F7">
            <color red="0.96862745098039216" green="0.96862745098039216" blue="0.96862745098039216" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="AppTheme_LightGrayColor_#A0A0A0">
            <color red="0.62745098039215685" green="0.62745098039215685" blue="0.62745098039215685" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="AppTheme_LightGrayColor_#CECECE">
            <color red="0.80784313725490198" green="0.80784313725490198" blue="0.80784313725490198" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="AppTheme_RedColor_#D82828">
            <color red="0.84705882352941175" green="0.15686274509803921" blue="0.15686274509803921" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="AppTheme_StrikeThroughColor_#707070">
            <color red="0.4392156862745098" green="0.4392156862745098" blue="0.4392156862745098" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
