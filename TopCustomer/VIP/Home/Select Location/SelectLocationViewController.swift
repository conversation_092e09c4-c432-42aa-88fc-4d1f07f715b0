
import UIKit

protocol SelectLocationProtocol: AnyObject {
    func displayAlert(string:String)
    func refreshAddressList(arrAddressData : [AddressResponseFields])
//    func displaySessionExpiredAlert(strMsg: String)
    func showAlertAndReload(strMessage :  String, addressId: Int)

}

protocol AddressListProtocol {
    func addOREditAddress(address:AddressResponseFields?)
    func removeSelectedAddress()
    
//    func addressListDataForUser(arrAddress : [UserAddressData])
}

class SelectLocationViewController: BottomPopupViewController {

    //MARK: - Properties -
    var presenterSelectLocation: SelectLocationPresentationProtocol?

    //MARK: - IBOutlets -
    @IBOutlet weak var btnNewAddress: UIButton!
    @IBOutlet weak var lblLocation: UILabel!
    @IBOutlet weak var tblAddress: UITableView!
    
    var arrAddress: [AddressResponseFields] = []
    var delegate : AddressListProtocol?
    var isMustSelectMosque = false

    //MARK: - Object lifecycle
    override var popupHeight: CGFloat { return 60 }
    var onSelectedLocationId: ((GetAddres) -> Void?)?
    
    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
        setup()
    }
     
    
    //MARK: - View Life Cycle -
    override func viewDidLoad() {
        super.viewDidLoad()
        
        // Mixpanel
        MixpanelEvents.sharedInstance.logTimeEvent(strScreenName: ScreenNames.AddressListingScreen.rawValue)

//        btnNewAddress.setTitle(ObjKeymessages.kLABEL_NEW_ADDRESS, for: .normal)
        let string = ObjKeymessages.kLABEL_NEW_ADDRESS
        let range = (string as NSString).range(of: ObjKeymessages.kLABEL_NEW_ADDRESS)
        let attributedString = NSMutableAttributedString(string: string)
        attributedString.addAttribute(NSAttributedString.Key.underlineStyle, value: NSNumber(value: 1), range: range)
        attributedString.addAttribute(NSAttributedString.Key.underlineColor, value: UIColor.black, range: range)
        attributedString.addAttribute(NSAttributedString.Key.font, value: UIFont(name: Fonts.LoewNextArabicMedium, size: 14)!, range: range)
        attributedString.addAttribute(NSAttributedString.Key.foregroundColor, value: UIColor.black, range: range)
        btnNewAddress.setAttributedTitle(attributedString, for: .normal)

        
        lblLocation.text = ObjKeymessages.kLABEL_LOCATION
        setupUI()
        self.getAddress()

    }
    
    override func viewDidDisappear(_ animated: Bool) {
        MixpanelEvents.sharedInstance.trackTimeEvent(strScreenName: ScreenNames.AddressListingScreen.rawValue)
    }

    private func getAddress() {
        if AppSingletonObj.isConnectedToNetwork(){
            self.presenterSelectLocation?.apiCallForGetAddress()
        }
    }

    //MARK: - Memory Management -
    override func didReceiveMemoryWarning() {
        super.didReceiveMemoryWarning()
        print("didReceiveMemoryWarning : \(self.classForCoder)")
    }
    
    //MARK: - DeInit -
    deinit {
        print("DeAllocated :  \(self.classForCoder)")
    }
    
    //MARK: Our Own Functions
    private func setupUI() {
        self.btnNewAddress.makeButtonRightToLeftIfRequiredWhileCenterAndOppsiteDirection()
    }
    
    //MARK: - Actions
    @IBAction func actionNewAddress(_ sender: UIButton) {
//        self.dismissVC {
//
//        }
        let vc = ProductPopupStoryboard.instantiate(AddAddressViewController.self)
//            vc.modalPresentationStyle = .fullScreen
        vc.modalPresentationStyle = .overFullScreen
        vc.modalTransitionStyle = .crossDissolve
        vc.isMustSelectMosque = self.isMustSelectMosque
        
        vc.refreshAddressList = { [weak self] () in
            self?.getAddress()
        }
        
        self.presentVC(vc)

    }
    
    //MARK: - Protocol Functions -
    
    
}

//MARK: - Extensions

extension SelectLocationViewController {
    //MARK: - VIP Setup -
    /// VIP Setup for SelectLocationViewController
    private func setup() {
        let viewController = self
        let interactor = SelectLocationInteractor()
        let presenter = SelectLocationPresenter()
        
        //View Controller will communicate with only presenter
        viewController.presenterSelectLocation = presenter
        
        //Presenter will communicate with Interector and Viewcontroller
        presenter.viewControllerSelectLocation = viewController
        presenter.interactorSelectLocation = interactor
        
        //Interactor will communucate with only presenter.
        interactor.presenterSelectLocation = presenter
    }
}

extension SelectLocationViewController : UITableViewDelegate, UITableViewDataSource{
    func numberOfSections(in tableView: UITableView) -> Int {
        return 1
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return arrAddress.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        guard let cell = tblAddress.dequeueReusableCell(withIdentifier: CellIdentifiers.kADDRESS_LIST_CELL) as? AddressListTableViewCell else{
            return UITableViewCell()
        }

        cell.lblType.text = arrAddress[indexPath.row].vType
        cell.lblAddress.text = arrAddress[indexPath.row].txAddress
        
        cell.btnDelete.tag = indexPath.row
        cell.btnDelete.addTarget(self, action: #selector(self.deleteAddress(sender:)), for: .touchUpInside)

        if let address = UserDefaults.standard.object(forKey: USERDEFAULTS_INFO_KEY.CURRENT_ADDRESS) as? [String:Any],let data = address.data {
            let decodeResult: (decodableObj: AddressResponseFields?, error: Error?) = CodableHelper.decode(AddressResponseFields.self, from: data)
            if let decodeObj = decodeResult.decodableObj {  // address selected
                if decodeObj.iAddressId == arrAddress[indexPath.row].iAddressId {
                    cell.imgType.image = UIImage(named: "location_pin_selected")
                }
                else {
                    cell.imgType.image = UIImage(named: "location_pin_deselected")
                }
            }
            else {  // address not selected
                cell.imgType.image = UIImage(named: "location_pin_deselected")
            }
        }
        else { // address not selected
            cell.imgType.image = UIImage(named: "location_pin_deselected")
        }
        return cell
    }
    
    @objc func deleteAddress(sender:UIButton){
        /*let alert = UIAlertController(title: AppName, message: ObjKeymessages.kMSG_DELETE_ADDRESS_CONFIRMATION, preferredStyle: .alert)
        let noButton = UIAlertAction(title: ObjKeymessages.kLABEL_NO, style: .default, handler: {(_ action: UIAlertAction) -> Void in
        })
        let yesButton = UIAlertAction(title: ObjKeymessages.kLABEL_YES, style: .default, handler: {(_ action: UIAlertAction) -> Void in
            
            var dictParam : [String:Any] = [:]
            dictParam["iAddressId"] = self.arrAddress[sender.tag].iAddressId ?? 0
            
            if AppSingletonObj.isConnectedToNetwork(){
                self.presenterSelectLocation?.apiCallForDeleteAddress(dictData: dictParam)
            }

        })
        alert.addAction(noButton)
        alert.addAction(yesButton)
        self.present(alert, animated: true) {() -> Void in }*/
        
        /*let vc = settingsStoryboard.instantiate(CustomPopupAlertViewController.self)
        vc.modalPresentationStyle = .overFullScreen
        vc.modalTransitionStyle = .crossDissolve
        vc.strDescription = ObjKeymessages.kMSG_DELETE_ADDRESS_CONFIRMATION
        vc.strLeftButtonTitle = ObjKeymessages.kLABEL_NO
        vc.isNeedToSwitchButton = true
        vc.strRightButtonTitle = ObjKeymessages.kLABEL_YES
        vc.isCenterButton = false
        vc.completion = { (index) in
            if index == 0 {
            }else{
                var dictParam : [String:Any] = [:]
                dictParam["iAddressId"] = self.arrAddress[sender.tag].iAddressId ?? 0
                
                if AppSingletonObj.isConnectedToNetwork(){
                    self.presenterSelectLocation?.apiCallForDeleteAddress(dictData: dictParam)
                }
            }
        }
        self.presentVC(vc)*/
        
        AppSingleton.shared.showCustomPopUpWithYesNoButton(strButton1Title: ObjKeymessages.kLABEL_YES, strButton2Title: ObjKeymessages.kLABEL_NO, strMessage: ObjKeymessages.kMSG_DELETE_ADDRESS_CONFIRMATION, showOnTopVC: true) { (isOk) in
            if isOk == true {
                var dictParam : [String:Any] = [:]
                dictParam["iAddressId"] = self.arrAddress[sender.tag].iAddressId ?? 0
                
                if AppSingletonObj.isConnectedToNetwork(){
                    self.presenterSelectLocation?.apiCallForDeleteAddress(dictData: dictParam)
                }
            }
        }
    }

    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        delegate?.addOREditAddress(address: arrAddress[indexPath.row])
        onSelectedLocationId?(GetAddres(id: arrAddress[indexPath.row].iAddressId ?? 1, name: arrAddress[indexPath.row].vType ?? ""))
        self.dismissVC(completion: nil)
    }
    
}

extension SelectLocationViewController: SelectLocationProtocol {
    func displayAlert(string: String) {
//        self.showAlert(title: AppName, message: string)
        AppSingleton.shared.showCustomPopUpWithOkButton(strButtonTitle: ObjKeymessages.kLABEL_OK, strMessage: string, showOnTopVC: true) { (isOk) in
        }
    }

    func refreshAddressList(arrAddressData : [AddressResponseFields]) {
        
        if self.isMustSelectMosque {
            let mosquesList = arrAddressData.filter { address in
                address.isMosques ?? 0 == 1
            }
            self.arrAddress = mosquesList
        } else {
            let mosquesList = arrAddressData.filter { address in
                address.isMosques ?? 0 == 0
            }
            self.arrAddress = mosquesList
        }
        tblAddress.reloadData()
        
        DispatchQueue.main.async {
            self.updatePopupHeight(to: min(CGFloat((self.tblAddress.contentSize.height) + 50 + 15 + (UIApplication.shared.windows.first?.safeAreaInsets.bottom ?? 0)), UIScreen.main.bounds.height * 0.95))
        }
    }

    /*func displaySessionExpiredAlert(strMsg: String) {
        let alert = UIAlertController(title: AppName, message: strMsg, preferredStyle: .alert)
        let okButton = UIAlertAction(title: ObjKeymessages.kLABEL_OK, style: .default, handler: {(_ action: UIAlertAction) -> Void in
            AppDel?.restartApp()
            self.dismiss(animated: true, completion: nil)
        })
        alert.addAction(okButton)
        self.present(alert, animated: true) {() -> Void in }
    }*/

    func showAlertAndReload(strMessage :  String, addressId: Int) {
        AppSingletonObj.showAlert(strMessage: strMessage)
        self.getAddress()
        
        if let address = UserDefaults.standard.object(forKey: USERDEFAULTS_INFO_KEY.CURRENT_ADDRESS) as? [String:Any],let data = address.data {
            let decodeResult: (decodableObj: AddressResponseFields?, error: Error?) = CodableHelper.decode(AddressResponseFields.self, from: data)
            if let decodeObj = decodeResult.decodableObj {
                if decodeObj.iAddressId == addressId {  // user deleted current selected address
                    UserDefaults.standard.removeObject(forKey: USERDEFAULTS_INFO_KEY.CURRENT_ADDRESS)
                    UserDefaults.standard.synchronize()
                    delegate?.removeSelectedAddress()

                }
            }
        }

    }

}
