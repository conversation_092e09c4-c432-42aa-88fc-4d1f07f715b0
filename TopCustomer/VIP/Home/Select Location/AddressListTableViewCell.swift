
import UIKit

class AddressListTableViewCell: UITableViewCell {

    @IBOutlet weak var lblType: UILabel!
    @IBOutlet weak var lblAddress: UILabel!    
    @IBOutlet weak var imgType: UIImageView!    
    @IBOutlet weak var btnDelete: UIButton!
    
    override func awakeFromNib() {
        super.awakeFromNib()
        // Initialization code
    }

    override func setSelected(_ selected: <PERSON><PERSON>, animated: <PERSON><PERSON>) {
        super.setSelected(selected, animated: animated)

        // Configure the view for the selected state
    }

}
