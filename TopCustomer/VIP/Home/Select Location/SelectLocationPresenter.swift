
import UIKit

protocol SelectLocationPresentationProtocol : AnyObject {
    func apiCallForGetAddress()
    func apiResponseGetAddress(response:AddressListResponse?,error:Error?)

    func apiCallForDeleteAddress(dictData:[String:Any])
    func apiResponseDeleteAddress(response:CommonFields?,error:Error?, addressId: Int)

}

class SelectLocationPresenter: SelectLocationPresentationProtocol {
    
    //MARK: - Objects & Variables -
    weak var viewControllerSelectLocation: SelectLocationProtocol?
    var interactorSelectLocation: SelectLocationInteractorProtocol?

    func apiCallForGetAddress() {
        interactorSelectLocation?.apiCallForGetAddress()
    }

    func apiResponseGetAddress(response:AddressListResponse?,error:Error?) {
//        if let vc = self.viewControllerSelectLocation as? BaseViewController {
//            DispatchQueue.main.async {
//                vc.endRefresing()
//            }
//        }
        if let error = error  {
            viewControllerSelectLocation?.displayAlert(string: error.localizedDescription)
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            //            viewController?.displayAlert(strTitle: "", strMessage: KeyMessages.kInvalidateResponse)
            return
        }
        
        if code == APIINVALIDAUTHORIZATIONCODE401 {
//            AppSingletonObj.goToLoginScreen(message: response.responseMessage ?? "")
            AppSingletonObj.displaySessionExpiredAlert(strMsg: response.responseMessage ?? "")
            return
        }

        if code == APICODE400 {
            viewControllerSelectLocation?.displayAlert(string: response.responseMessage ?? "")
            return
        }
        
        guard let model = response.responseData,code == APISUCCESSCODE200  else {
            return
        }
        self.viewControllerSelectLocation?.refreshAddressList(arrAddressData: model)
    }

    func apiCallForDeleteAddress(dictData:[String:Any]) {
        interactorSelectLocation?.apiCallForDeleteAddress(dictData: dictData)
    }

    func apiResponseDeleteAddress(response:CommonFields?,error:Error?, addressId: Int) {
//        if let vc = self.viewControllerSelectLocation as? BaseViewController {
//            DispatchQueue.main.async {
//                vc.endRefresing()
//            }
//        }
        if let error = error  {
            viewControllerSelectLocation?.displayAlert(string: error.localizedDescription)
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            //            viewController?.displayAlert(strTitle: "", strMessage: KeyMessages.kInvalidateResponse)
            return
        }
        
        if code == APIINVALIDAUTHORIZATIONCODE401 {
//            AppSingletonObj.goToLoginScreen(message: response.responseMessage ?? "")
            AppSingletonObj.displaySessionExpiredAlert(strMsg: response.responseMessage ?? "")
            return
        }

        if code == APICODE400 {
            viewControllerSelectLocation?.displayAlert(string: response.responseMessage ?? "")
            return
        }
        
//        guard let model = response.responseData,code == APISUCCESSCODE200  else {
//            return
//        }
        self.viewControllerSelectLocation?.showAlertAndReload(strMessage: response.responseMessage ?? "", addressId: addressId)
    }

}
