
import UIKit

protocol SelectLocationInteractorProtocol : AnyObject {
    func apiCallForGetAddress()
    func apiCallForDeleteAddress(dictData:[String:Any])

}

protocol SelectLocationDataStore {
    //{ get set }
}

class SelectLocationInteractor: SelectLocationInteractorProtocol, SelectLocationDataStore {

    //MARK: - Objects & Variables -
    weak var presenterSelectLocation: SelectLocationPresentationProtocol?
    
    //MARK: - API Calls
    func apiCallForGetAddress() {
        
        ActivityIndicator.shared.showCentralSpinner()
        
        let authorization = getAuthorizationText()
        
        UserAPI.userAddresses(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang) { data, error in
            ActivityIndicator.shared.hideCentralSpinner()
            self.presenterSelectLocation?.apiResponseGetAddress(response: data, error: error)

        }
        
    }

    func apiCallForDeleteAddress(dictData:[String:Any]) {
        
        ActivityIndicator.shared.showCentralSpinner()
        
        let authorization = getAuthorizationText()
        
        UserAPI.deleteUserAddress(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, iAddressId: Int(setDataInString(dictData["iAddressId"] as AnyObject)) ?? 0) { data, error in
            ActivityIndicator.shared.hideCentralSpinner()
            self.presenterSelectLocation?.apiResponseDeleteAddress(response: data, error: error, addressId: Int(setDataInString(dictData["iAddressId"] as AnyObject)) ?? 0)
        }
        
    }

}
