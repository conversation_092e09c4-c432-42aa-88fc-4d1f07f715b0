
import UIKit
import <PERSON><PERSON>abbar
import Mixpanel
import WebKit
import Lottie
import ImageSlideshow
import TikTokBusinessSDK
import FirebaseAnalytics

protocol ProductPopupProtocol: AnyObject {
    func displayAlert(string:String)
    func getProductDetailsInfo(model : ProductDetailsResponseFields)
    func displayAlertAndDismiss(string:String, cartCount: Int)
//    func displaySessionExpiredAlert(strMsg: String)
    func displayErrorAlert(strMsg:String)
    func getFavoriteValue(model : FavouriteResponseFields)
    func showToastMessage(strMsg: String)
    func getProductInfoResponse(model: ProductInfoResponseFields)

}

protocol UpdateCountProtocol {
    func updateCartCount(cartCount:Int?)
//    func addressListDataForUser(arrAddress : [UserAddressData])
}

class ProductPopupViewController: BottomPopupViewController {

    @IBOutlet weak var collVwProducts: UICollectionView!
    @IBOutlet weak var btnQty: QTYButton!
    @IBOutlet weak var collVwProductsHeightConstraint: NSLayoutConstraint!
    @IBOutlet weak var lblProductDesc: UILabel!
    @IBOutlet weak var lblQuantityTitle: UILabel!
    @IBOutlet weak var btnAddToCart: CustomRoundedButtton!
    @IBOutlet weak var lblYouMay: UILabel!
    @IBOutlet weak var lblProductName: UILabel!
    @IBOutlet weak var imgProduct: UIImageView!
    @IBOutlet weak var lblProductSkuTitle: UILabel!
    @IBOutlet weak var lblProductSkuValue: UILabel!
    @IBOutlet weak var lblCombinedProductName: UILabel!
    @IBOutlet weak var scrollView: UIScrollView!
    @IBOutlet weak var viewLowQuantity: UIView!
    @IBOutlet weak var lblLowQuantity: UILabel!
    @IBOutlet weak var lblLeftInStock: UILabel!
    @IBOutlet weak var cons_top_collectionView: NSLayoutConstraint!
    @IBOutlet weak var cons_bottom_collectionView: NSLayoutConstraint!
    @IBOutlet weak var cons_top_lblYouMay: NSLayoutConstraint!
    @IBOutlet weak var lblPrice: MaterialLocalizeLable!
    @IBOutlet weak var viewQuantity: UIView!
    @IBOutlet weak var lblOldPrice: UILabel!
    @IBOutlet weak var btnFavorite: UIButton!
    @IBOutlet weak var activityIndicatorView: UIActivityIndicatorView!
    @IBOutlet weak var stackViewActivityIndicatorView: UIStackView!
    @IBOutlet weak var btnNotifyMe: CustomRoundedButtton!
    @IBOutlet weak var lblOffer: UILabel!
    @IBOutlet weak var viewOuterMaxQuantity: UIView!
    @IBOutlet weak var cons_top_stackViewMaxQuantity: NSLayoutConstraint!
    @IBOutlet weak var lblMaxQuantityInfo: UILabel!
    @IBOutlet weak var stackViewOfferDiscount: UIStackView!
    @IBOutlet weak var lblOfferDiscount: MaterialLocalizeLable!
    @IBOutlet weak var lblGetOffer: MaterialLocalizeLable!
    @IBOutlet weak var imageOfferDiscount: UIImageView!
    @IBOutlet weak var tblProductsOfBundle: UITableView!
    @IBOutlet weak var constraintHeightTblProductsOfBundle: NSLayoutConstraint!
    @IBOutlet weak var lblContainProducts: MaterialLocalizeLable!
    @IBOutlet weak var viewAddToCartAnimation: AnimationView!
    @IBOutlet weak var collVQuantitiesDiscount: UICollectionView!
    @IBOutlet weak var viewQuantityDiscount: UIView!
    @IBOutlet weak var lblQuantityDiscount: MaterialLocalizeLable!
    @IBOutlet weak var stackViewQuantitiesDiscount: UIStackView!
    @IBOutlet weak var lblPriceQuantityDiscount: MaterialLocalizeLable!
    @IBOutlet weak var viewImagesArr: ImageSlideshow!
    @IBOutlet weak var btnFullZoomImage: UIButton!
    
    // MARK: Objects & Variables
    var presenterProductPopup: ProductPopupPresentationProtocol?

    var productId = 0
    var objProductDetails: ProductResponseFields?
    var objBundleDetails: BundelsListResponseFields?
    var arrMayLikeProducts: [ProductResponseFields] = []

    var delegate : UpdateCountProtocol?
    var strFromWhichScreen = ""
    
    var showYouMayLikePopup: ((ProductResponseFields)->())?
    var refreshList: (()->())?

    var ifFavOrNot = 0
    var offerQuantity = 0
    var bundleID = 0

    override var popupHeight: CGFloat { return 270 }
    // return height for offer details
    var heightOfferTitle: CGFloat = 50
    var quantitiesDiscountArr = [""]
    var pricesQuantitiesDiscountArr: [PricesResponseFields]? = []
    var quantitiesDiscountPrice = 0.0
    var isQuantitiesDiscountPriceUsed = false
    var imagesArr = [String]()
    // MARK: IBOutlets
    
    // MARK: Object lifecycle
    /*
    override init(nibName nibNameOrNil: String?, bundle nibBundleOrNil: Bundle?) {
        super.init(nibName: nibNameOrNil, bundle: nibBundleOrNil)
        setup()
    }
    */
    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
        setup()
    }
    
    // MARK: Setup
    
    private func setup() {
        let viewController = self
        let interactor = ProductPopupInteractor()
        let presenter = ProductPopupPresenter()
        
        //View Controller will communicate with only presenter
        viewController.presenterProductPopup = presenter
        
        //Presenter will communicate with Interector and Viewcontroller
        presenter.viewControllerProductPopup = viewController
        presenter.interactorProductPopup = interactor
        
        //Interactor will communucate with only presenter.
        interactor.presenterProductPopup = presenter
    }
    
    
    // MARK: View lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
//        webView.scrollView.showsHorizontalScrollIndicator = false
//        webView.scrollView.showsVerticalScrollIndicator = false

        // Mixpanel
        MixpanelEvents.sharedInstance.logTimeEvent(strScreenName: ScreenNames.ProductDetailScreen.rawValue)
        
        self.setTexts()
        setupUI()
        
        if User.shared.checkUserLoginStatus() {
            if self.strFromWhichScreen == "CallAPI" { // Product not found, call API
            }
            else {
                self.getFavorite()
            }
            
            self.getProductInfo()
        }
        else { // not logged in
            viewOuterMaxQuantity.isHidden = true
            cons_top_stackViewMaxQuantity.constant = 0
        }
        
        if self.strFromWhichScreen == "CallAPI" { // Product not found, call API
            self.getProductDetails()
        }
        else {  // Product found, do not call API
            self.setDetails()
        }
        // set config for bundle
        if bundleID != 0 {
            self.tblProductsOfBundle.estimatedRowHeight = 44
            self.tblProductsOfBundle.rowHeight = UITableView.automaticDimension
            self.btnAddToCart.setTitle(ObjKeymessages.kLABEL_ADD_TO_CART, for: .normal)
            self.btnAddToCart.setTitle(ObjKeymessages.kLABEL_ADD_TO_CART, for: .normal)
            self.btnAddToCart.backgroundColor = UIColor.AppTheme_BlueColor_012CDA
            self.btnAddToCart.isUserInteractionEnabled = true
            self.viewQuantity.isHidden = false
            self.btnNotifyMe.isHidden = true
            self.lblLeftInStock.isHidden = true
            self.tblProductsOfBundle.delegate = self
            self.tblProductsOfBundle.dataSource = self
            self.tblProductsOfBundle.registerCell(cell: ProductsOfBundleTVCell.self)
        } else {
            self.constraintHeightTblProductsOfBundle.constant = 0
        }
    }
    
    private func getProductInfo() {
        var dictParam : [String:Any] = [:]
        print(objProductDetails)
        if self.strFromWhichScreen == "CallAPI" {
            dictParam["biProductId"] = productId
        }
        else {
            dictParam["biProductId"] = objProductDetails?._id == nil ? objProductDetails?.biProductId : objProductDetails?._id
//            dictParam["biProductId"] = objProductDetails?.biProductId
        }
        // set bundle id
        dictParam["biBundleId"] = self.objProductDetails?.bunddelId == nil ? self.objBundleDetails?.bunddelId : self.objProductDetails?.bunddelId
        debugPrint(dictParam)
        
        if AppSingletonObj.isConnectedToNetwork(){
            self.presenterProductPopup?.apiCallForGetProductInfo(dictData: dictParam)
        }
    }

    private func getFavorite() {
        var dictParam : [String:Any] = [:]
        dictParam["biProductId"] = objProductDetails?._id == nil ? objProductDetails?.biProductId : objProductDetails?._id
        dictParam["tiIsFavourite"] = 2

        if AppSingletonObj.isConnectedToNetwork(){
            self.presenterProductPopup?.apiCallForGetFavorite(dictData: dictParam)
        }
    }
    
    override func viewDidDisappear(_ animated: Bool) {
        MixpanelEvents.sharedInstance.trackTimeEvent(strScreenName: ScreenNames.ProductDetailScreen.rawValue)
    }
    
    private func getProductDetails() {
        var dictParam : [String:Any] = [:]
        dictParam["biProductId"] = productId
        print(dictParam)
        if AppSingletonObj.isConnectedToNetwork(){
            self.presenterProductPopup?.apiCallForGetProductDetails(dictData: dictParam)
        }
    }
    
    private func setTexts() {
        lblContainProducts.text = "contain_these_products:".localized
        lblProductSkuTitle.text = ObjKeymessages.kLABEL_PRODUCT_SKU
//        lblProductDesc.text = ObjKeymessages.kMSG_PRODUCT_DESC
        lblQuantityTitle.text = ObjKeymessages.kLABEL_QUANTITY
        btnAddToCart.setTitle(ObjKeymessages.kLABEL_ADD_TO_CART, for: .normal)
        lblYouMay.text = "\(ObjKeymessages.kLABEL_YOU_MAY)..."

        lblLowQuantity.text = ObjKeymessages.kLABEL_LOW_QUANTITY
        lblLeftInStock.text = "" //"\(ObjKeymessages.kLABEL_ONLY) 2 \(ObjKeymessages.kLABEL_LEFT_IN_STOCK)"
        btnNotifyMe.setTitle(ObjKeymessages.kLABEL_NOTIFY_ME, for: .normal)
    }
    
    func setupUI() {
        if objProductDetails?.imagesApp?.count ?? 0 >= 1 {
            self.imgProduct.isHidden = true
            self.viewImagesArr.isHidden = false
            self.btnFullZoomImage.isEnabled = false
            viewImagesArr.slideshowInterval = 2.5
            viewImagesArr.pageIndicatorPosition = .init(horizontal: .center, vertical: .bottom)
            viewImagesArr.contentScaleMode = UIViewContentMode.scaleAspectFit

            let pageIndicator = UIPageControl()
            pageIndicator.currentPageIndicatorTintColor = UIColor.lightGray
            pageIndicator.pageIndicatorTintColor = UIColor.black
            viewImagesArr.pageIndicator = pageIndicator

            // optional way to show activity indicator during image load (skipping the line will show no activity indicator)
            viewImagesArr.activityIndicator = DefaultActivityIndicator()
            viewImagesArr.delegate = self

            // can be used with other sample sources as `afNetworkingSource`, `alamofireSource` or `sdWebImageSource` or `kingfisherSource`
            var alamofireSource = [AlamofireSource]()
            
            self.objProductDetails?.imagesApp?.forEach({ image in
                if let img = AlamofireSource(urlString: image) {
                    alamofireSource.append(img)
                }
            })
            viewImagesArr.setImageInputs(alamofireSource)
            let recognizer = UITapGestureRecognizer(target: self, action: #selector(self.didTap))
            viewImagesArr.addGestureRecognizer(recognizer)
        } else {
            self.imgProduct.isHidden = false
            self.viewImagesArr.isHidden = true
            self.btnFullZoomImage.isEnabled = true
        }
        
        viewLowQuantity.clipsToBounds = true
        viewLowQuantity.layer.cornerRadius = 8
        
        if UserDefaults.standard.getLanguage()! == UserAPI.VLanguage_userLanguage.en.rawValue {       // english
            viewLowQuantity.layer.maskedCorners = [.layerMaxXMinYCorner, .layerMaxXMaxYCorner]
        }
        else {   // arabic
            viewLowQuantity.layer.maskedCorners = [.layerMinXMinYCorner, .layerMinXMaxYCorner]
        }

        self.collVwProducts.registerCell(cell: ProductsCollectionViewCellSmall.self)
        self.collVwProducts.delegate = self
        self.collVwProducts.dataSource = self
        
        self.collVQuantitiesDiscount.registerCell(cell: QuantitiesDiscountCVCell.self)
        self.collVQuantitiesDiscount.delegate = self
        self.collVQuantitiesDiscount.dataSource = self
        
        btnQty.textfield.text = "1"
        btnQty.delegate = self
        self.lblContainProducts.isHidden = true
        if objProductDetails?.quantityDiscount ?? 1 == 1 && objProductDetails?.quantityDiscount != nil {
            self.isQuantitiesDiscountPriceUsed = true
            self.viewQuantityDiscount.isHidden = false
            self.stackViewQuantitiesDiscount.isHidden = false
            self.pricesQuantitiesDiscountArr = objProductDetails?.prices ?? []
             let arr = objProductDetails?.prices?.compactMap({ price in
                 var buyDiscount = "buy_piece_with_quantities_discount".localized.replacingOccurrences(of: "%1d", with: "\(price.quantity_from ?? 1)")
                     .replacingOccurrences(of: "%2d", with: "\(price.quantity_to ?? 0 == 0 ? "or_over".localized : "\(price.quantity_to ?? 1) \("piece".localized)")")
                     .replacingOccurrences(of: "%3d", with: "\(forTrailingZero(temp: price.price?.toDouble() ?? 0.0))")
                     .replacingOccurrences(of: "%4d", with: "")
                 return "\(buyDiscount)"
            })
            self.quantitiesDiscountArr = arr ?? []
            self.lblPriceQuantityDiscount.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: self.getQuantityDiscountPrice(quantity: 1)))")
        }
        /*btnQty.buttonMin.isUserInteractionEnabled = false

        btnQty.onChange { (value, strAction) in
            print("Quantity Change: \(value)")
            self.btnQty.buttonMin.isUserInteractionEnabled = value != 1
        }*/
    }
    
    // MARK: Call something
    
    func callSomething() {
        
    }
    
    @IBAction func actionFavorite(_ sender: Any) {
        
        if !User.shared.checkUserLoginStatus() {
            self.showNoLoginUserAlert(complation: nil)
            return
        }

        var fav = 0
        if ifFavOrNot == 1 { // remove from favorite
            fav = 0
        }
        else {  // add to favorite
            fav = 1
            // Apps Flyer Event
            TikTokBusiness.trackEvent("ADD_WISHLIST", withProperties: [
                "item_id": objProductDetails?.biProductId ?? 0
            ])
            Analytics.logEvent("ADD_WISHLIST", parameters: [
                "item_id": objProductDetails?.biProductId ?? 0
            ])
            AppsFlyerEvents.shared.afAddToWishlist(price: objProductDetails?.price ?? 0.0 == 0.0 ? objProductDetails?.dbOriginalProductPrice?.toDouble() ?? 0.0 : objProductDetails?.price ?? 0.0, content: objProductDetails?.name == nil ? objProductDetails?.vProductName ?? "" : objProductDetails?.name ?? "", contentId: objProductDetails?.biProductId ?? 0, contentType: objProductDetails?.vProductUnit ?? "")

        }
        
        var dictParam : [String:Any] = [:]
        dictParam["biProductId"] = objProductDetails?._id == nil ? objProductDetails?.biProductId : objProductDetails?._id
        dictParam["tiIsFavourite"] = fav

        if AppSingletonObj.isConnectedToNetworkForCheckoutScreen() {
            stackViewActivityIndicatorView.isHidden = false
            activityIndicatorView.isHidden = false
            activityIndicatorView.startAnimating()
            btnFavorite.isHidden = true
            self.presenterProductPopup?.apiCallForGetFavorite(dictData: dictParam)
        }
        else {
            AppSingletonObj.showAlert(strMessage: ObjKeymessages.kMSG_NO_INTERNET)
        }
    }
    
    @IBAction func btnFullScreenAction(_ sender: Any) {
        let vc = ProductPopupStoryboard.instantiate(FullScreenImageViewController.self)
        vc.mediaImage = imgProduct.image
        vc.images = self.objProductDetails?.imagesApp ?? []
        vc.modalPresentationStyle = .overFullScreen
        self.present(vc, animated: true)
    }
    
    @IBAction func btnDimViewAction(_ sender: Any) {
        self.view.endEditing(true)
        self.refreshList?()
        self.dismiss(animated: true, completion: nil)
    }
    
    @IBAction func actionNotifyMe(_ sender: Any) {
        self.view.endEditing(true)

        if !User.shared.checkUserLoginStatus() {
            self.showNoLoginUserAlert(complation: nil)
            return
        }

        var dictParam : [String:Any] = [:]
        dictParam["biProductId"] = objProductDetails?._id == nil ? objProductDetails?.biProductId : objProductDetails?._id
        dictParam["biBundleId"] = self.objProductDetails?.bunddelId ?? 0 == 0 ? self.objBundleDetails?.bunddelId : self.objProductDetails?.bunddelId

        if AppSingletonObj.isConnectedToNetwork(){
            TikTokBusiness.trackEvent("NOTIFY_PRODUCT", withProperties: dictParam)
            Analytics.logEvent("NOTIFY_PRODUCT", parameters: dictParam)
            self.presenterProductPopup?.apiCallForNotifyMe(dictData: dictParam)
        }

    }
    
    @IBAction func actionAddToCart(_ sender: Any) {
        self.view.endEditing(true)
        

        if !User.shared.checkUserLoginStatus() {
            self.showNoLoginUserAlert(complation: nil)
            return
        }

        let quantity = Int(self.btnQty.textfield.text ?? "") ?? 0
        if quantity <= 0 {
            AppSingletonObj.showAlert(strMessage: ObjKeymessages.kMSG_VALID_QUANTITY)
            return
        }

        var dictParam : [String:Any] = [:]
        dictParam["biProductId"] = objProductDetails?._id == nil ? objProductDetails?.biProductId : objProductDetails?._id
        dictParam["tiIsCheck"] = AddToCartIsCheck.Add.rawValue
        dictParam["iProductQuantity"] = self.btnQty.textfield.text
//        dictParam["dbPrice"] = objProductDetails?.dbOriginalProductPrice
        dictParam["dbPrice"] = objProductDetails?.discountPrice ?? 0  == 0 ? objProductDetails?.dbOriginalProductPrice : objProductDetails?.discountPrice ?? 0
        dictParam["bunddelId"] = self.objProductDetails?.bunddelId ?? 0 == 0 ? self.objBundleDetails?.bunddelId : self.objProductDetails?.bunddelId
        // price for bundle
        if self.objBundleDetails?.bunddelId != nil || self.objBundleDetails?.bunddelId != 0 {
            dictParam["dbPrice"] = self.objBundleDetails?.price ?? 0
        }
        // price for Quantities Discount Price
        if isQuantitiesDiscountPriceUsed {
            dictParam["dbPrice"] = self.quantitiesDiscountPrice
        }

        dictParam["isMosques"] = "\(objProductDetails?.isMosques ?? 0)"
        // Apps Flyer Event
        let price = dictParam["dbPrice"] as? Double
        AppsFlyerEvents.shared.afAddToCart(price: price ?? 0.0, content: objProductDetails?.name == nil ? objProductDetails?.vProductName ?? "" : objProductDetails?.name ?? "", contentId: objProductDetails?.biProductId ?? 0, contentType: objProductDetails?.vProductUnit ?? "", quantity: Int(self.btnQty.textfield.text ?? "1") ?? 1)
        if AppSingletonObj.isConnectedToNetwork(){
            TikTokBusiness.trackEvent("ADD_TO_CART", withProperties: dictParam)
            Analytics.logEvent("ADD_TO_CART", parameters: dictParam)
            self.presenterProductPopup?.apiCallForAddToCart(dictData: dictParam)
        }
    }
    
    @IBAction func shareAction(_ sender: Any) {
        let shareUrl = "https://share.material.sa/product/\(objProductDetails?.id == nil ? objProductDetails?.biProductId ?? 0: objProductDetails?.id ?? 0)"
        debugPrint(shareUrl)
        self.shareTextInDefaultShareKit(array: [shareUrl])
    }
    
    func shareTextInDefaultShareKit(array:[Any],complation: (()->())? = nil) {
        let activityVC : UIActivityViewController = UIActivityViewController(activityItems: array, applicationActivities: nil)
        activityVC.completionWithItemsHandler = { (activityType,isCompleted,returnItems,error) in
            print("==>>> Contrll is dismiss")
            complation?()
        }
        self.presentVC(activityVC)
    }

}

extension ProductPopupViewController: UICollectionViewDelegate, UICollectionViewDataSource, UICollectionViewDelegateFlowLayout {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        if collectionView == self.collVwProducts {
            return arrMayLikeProducts.count
        } else {
            return self.quantitiesDiscountArr.count
        }
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        if collectionView == self.collVQuantitiesDiscount {
            let cell = collectionView.dequeue(with: QuantitiesDiscountCVCell.self, for: indexPath)
            cell.setupCell(title: self.quantitiesDiscountArr[indexPath.row])
            return cell
        } else {
            let cell = collectionView.dequeue(with: ProductsCollectionViewCellSmall.self, for: indexPath)
            cell.isAppadvertisement(value: arrMayLikeProducts[indexPath.row].isAppAdvertisement == 1 ? false : true)
            cell.isQuantitiesDiscountHidden(value: arrMayLikeProducts[indexPath.row].quantityDiscount ?? 1 == 1 ? false : true)
            cell.btnAddToCart.isHidden = false
            cell.btnAddToCart.tag = indexPath.row
            cell.btnAddToCart.addTarget(self, action: #selector(self.addToCart(sender:)), for: .touchUpInside)
            
            cell.lblProductName.text = "\(arrMayLikeProducts[indexPath.row].vProductName ?? "" == "" ? arrMayLikeProducts[indexPath.row].name ?? "" : arrMayLikeProducts[indexPath.row].vProductName ?? "")"
            cell.lblPriceTitle.text = "\(arrMayLikeProducts[indexPath.row].vProductUnit ?? "")" //ObjKeymessages.kLABEL_PRICE
            if arrMayLikeProducts[indexPath.row].price == nil || arrMayLikeProducts[indexPath.row].price ?? 0 <= 0 {
                cell.lblPrice.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: arrMayLikeProducts[indexPath.row].dbOriginalProductPrice?.toDouble() ?? 0.0))")
            } else {
                cell.lblPrice.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: arrMayLikeProducts[indexPath.row].price ?? 0.0))")
            }
            
            if arrMayLikeProducts[indexPath.row].offertitle != nil && arrMayLikeProducts[indexPath.row].offertitle != "" {
                cell.viewOfferTitle.isHidden = false
                cell.lblOfferTitle.text = "\("Offer".localized)"
            } else {
                cell.viewOfferTitle.isHidden = true
            }
            
            if arrMayLikeProducts[indexPath.row].isLowQuantity == 0 {  // 0 - hide low quantity label
                cell.viewLowQuantity.isHidden = true
                
                cell.imgProduct.kf.indicatorType = .activity
                let url = URL(string: arrMayLikeProducts[indexPath.row].vProductImage ?? "")
                cell.imgProduct.kf.setImage(with: url , placeholder: UIImage(named: "placeholder_product"))
            }
            else {  // 1 - show low quantity label
                if arrMayLikeProducts[indexPath.row].iHowMuchLeftInStock ?? 0 <= 0 {  // out of stock
                    cell.viewLowQuantity.isHidden = true
                    
                    cell.imgProduct.kf.indicatorType = .activity
                    let url = URL(string: arrMayLikeProducts[indexPath.row].vProductImage ?? "")
                    
                    cell.imgProduct.kf.setImage(with: url , placeholder: UIImage(named: "placeholder_product")) { result in
                        switch result {
                        case .success(let value):
                            print("Image: \(value.image). Got from: \(value.cacheType)")
                            cell.imgProduct.image = value.image.grayed
                        case .failure(let error):
                            print("Error: \(error)")
                        }
                    }
                }
                else {
                    cell.viewLowQuantity.isHidden = false
                    
                    cell.imgProduct.kf.indicatorType = .activity
                    let url = URL(string: arrMayLikeProducts[indexPath.row].vProductImage ?? "")
                    cell.imgProduct.kf.setImage(with: url , placeholder: UIImage(named: "placeholder_product"))
                }
            }
            
            // discount logic
            //        if arrMayLikeProducts[indexPath.row].dDiscountAmount == "" {   // no discount
            if arrMayLikeProducts[indexPath.row].tiDiscountType == 1 {  // no discount
                cell.lblPrice.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: arrMayLikeProducts[indexPath.row].price ?? 0.0 == 0.0 ? arrMayLikeProducts[indexPath.row].dbOriginalProductPrice?.toDouble() ?? 0.0 : arrMayLikeProducts[indexPath.row].price ?? 0.0))")
                cell.lblOldPrice.isHidden = true
                cell.lblOffer.isHidden = true
            }
            else {  // discount is there
                cell.lblPrice.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: arrMayLikeProducts[indexPath.row].discountPrice ?? 0.0 == 0.0 ? arrMayLikeProducts[indexPath.row].dDiscountedProductPrice?.toDouble() ?? 0.0 : arrMayLikeProducts[indexPath.row].discountPrice ?? 0.0))")
                cell.lblOldPrice.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: arrMayLikeProducts[indexPath.row].price ?? 0.0 == 0.0 ? arrMayLikeProducts[indexPath.row].dbOriginalProductPrice?.toDouble() ?? 0.0 : arrMayLikeProducts[indexPath.row].price ?? 0.0))", isDiscounted: true)
                cell.lblOffer.text = "( \(forTrailingZero(temp: arrMayLikeProducts[indexPath.row].discountPrice ?? 0.0 == 0.0 ? arrMayLikeProducts[indexPath.row].dDiscountedProductPrice?.toDouble() ?? 0.0 : arrMayLikeProducts[indexPath.row].discountPrice ?? 0.0))% \(ObjKeymessages.kLABEL_OFF) )"
                let originalPrice = Double(arrMayLikeProducts[indexPath.row].price ?? 0.0 == 0.0 ? arrMayLikeProducts[indexPath.row].dbOriginalProductPrice?.toDouble() ?? 0.0 : arrMayLikeProducts[indexPath.row].price ?? 0.0)
                let discountedPrice = Double(arrMayLikeProducts[indexPath.row].discountPrice ?? 0.0 == 0.0 ? arrMayLikeProducts[indexPath.row].dDiscountedProductPrice?.toDouble() ?? 0.0 : arrMayLikeProducts[indexPath.row].discountPrice ?? 0.0)
                //            let percent =  originalPrice - ((discountedPrice * 100) / originalPrice)
                let a = originalPrice - discountedPrice
                let b = a / originalPrice
                let percent = b * 100
                cell.lblOffer.text = "(\(forTrailingZero(temp: percent.rounded(.up)))% \(ObjKeymessages.kLABEL_OFF))"
                
                cell.lblOldPrice.isHidden = false
                cell.lblOffer.isHidden = false
            }
            // discount logic ends here
            
            if arrMayLikeProducts[indexPath.row].iHowMuchLeftInStock ?? 0 <= 0 {  // out of stock
                cell.viewOutOfStock.isHidden = false
                cell.btnAddToCart.isHidden = true
            }
            else {
                cell.viewOutOfStock.isHidden = true
                cell.btnAddToCart.isHidden = false
            }
            
            return cell
        }
    }
    
    @objc func addToCart(sender:UIButton){
        self.view.endEditing(true)

        if !User.shared.checkUserLoginStatus() {
            self.showNoLoginUserAlert(complation: nil)
            return
        }

        var dictParam : [String:Any] = [:]
        dictParam["biProductId"] = arrMayLikeProducts[sender.tag].id
        dictParam["tiIsCheck"] = AddToCartIsCheck.Add.rawValue
        dictParam["iProductQuantity"] = 1
        dictParam["dbPrice"] = arrMayLikeProducts[sender.tag].discountPrice ?? 0 == 0 ? arrMayLikeProducts[sender.tag].dDiscountedProductPrice?.toDouble() ?? 0 : arrMayLikeProducts[sender.tag].discountPrice
        dictParam["bunddelId"] = self.objBundleDetails?.bunddelId ?? 0
        dictParam["isMosques"] = "\(arrMayLikeProducts[sender.tag].isMosques ?? 0)"
        if AppSingletonObj.isConnectedToNetwork(){
            self.presenterProductPopup?.apiCallForAddToCart(dictData: dictParam)
        }
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
//        return CGSize(width: (self.collVwProducts.frame.width - 20) / 2, height: 240)   // 186
//        return CGSize(width: 174, height: 246)
//        return CGSize(width: (self.collVwProducts.frame.width - 10) / 3, height: 193)
        if collectionView == self.collVwProducts {
            return CGSize(width: (self.collVwProducts.frame.width + 50) / 3, height: 193)  // - 10
        } else {
            return CGSize(width: (self.collVQuantitiesDiscount.frame.width + 50) / 3, height: 50)
        }
    }
    
    /*func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, minimumLineSpacingForSectionAt section: Int) -> CGFloat {
            return 5
    }*/

    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
//            let vc = ProductPopupStoryboard.instantiate(ProductPopupViewController.self)
//            vc.modalPresentationStyle = .overFullScreen
//            vc.modalTransitionStyle = .crossDissolve
//            self.presentVC(vc)
        if collectionView == self.collVwProducts {
            self.showYouMayLikePopup?(arrMayLikeProducts[indexPath.row])
            self.dismiss(animated: true, completion: nil)
        }
    }
    
    func collectionView(_ collectionView: UICollectionView, willDisplay cell: UICollectionViewCell, forItemAt indexPath: IndexPath) {
        if collectionView == self.collVwProducts {
            self.collVwProducts.layoutIfNeeded()
//            self.collVwProductsHeightConstraint.constant = self.collVwProducts.contentSize.height
        }
    }
}

extension ProductPopupViewController: ProductPopupProtocol {
    func displayAlert(string: String) {
//        self.showAlert(title: AppName, message: string)
        AppSingleton.shared.showCustomPopUpWithOkButton(strButtonTitle: ObjKeymessages.kLABEL_OK, strMessage: string, showOnTopVC: true) { (isOk) in
        }
    }

    func getProductDetailsInfo(model : ProductDetailsResponseFields) {
        objProductDetails = model.productDetails
        arrMayLikeProducts = model.products ?? []
        self.objBundleDetails = model.bundle
        if self.objBundleDetails != nil {
            arrMayLikeProducts = model.bundle?.getCartBunddelProducts?.first?.youMayAlsoLikeProducts ?? []
            self.setBundleDetails()
        } else {
            self.setDetails()
        }
        self.getFavorite()

        /*collVwProducts.reloadData()
        
        if arrMayLikeProducts.count <= 0 {  // no data
            self.collVwProductsHeightConstraint.constant = 0
            lblYouMay.isHidden = true
            cons_top_collectionView.constant = 0
            cons_bottom_collectionView.constant = 0
            cons_top_lblYouMay.constant = 0
        }
        
        DispatchQueue.main.async {
            self.updatePopupHeight(to: min(CGFloat((self.scrollView.contentSize.height) + 15 + (UIApplication.shared.windows.first?.safeAreaInsets.bottom ?? 0)), UIScreen.main.bounds.height * 0.95))
        }*/
    }

    fileprivate func setLogicOfDiscountWithNewSearch(_ priceOfProduct: Double, _ discountOfProduct: Double) {
        if objProductDetails?.tiDiscountType == 1 || priceOfProduct == discountOfProduct {  // no discount
            lblPrice.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: objProductDetails?.price ?? 0.0 == 0.0 ? objProductDetails?.dbOriginalProductPrice?.toDouble() ?? 0.0 : objProductDetails?.price ?? 0.0))")
            lblOldPrice.isHidden = true
            lblOffer.isHidden = true
        }
        else {  // discount is there
            lblPrice.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: objProductDetails?.discountPrice ?? 0.0 == 0.0 ? objProductDetails?.dDiscountedProductPrice?.toDouble() ?? 0.0 : objProductDetails?.discountPrice ?? 0.0))")
            lblOldPrice.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: objProductDetails?.price ?? 0.0 == 0.0 ? objProductDetails?.dbOriginalProductPrice?.toDouble() ?? 0.0 : objProductDetails?.price ?? 0.0))", isDiscounted: true)
            lblOldPrice.isHidden = false
            lblOffer.isHidden = false
            lblOffer.text = "( \(forTrailingZero(temp: objProductDetails?.discountPrice ?? 0.0 == 0.0 ? objProductDetails?.dDiscountedProductPrice?.toDouble() ?? 0.0 : objProductDetails?.discountPrice ?? 0.0))% \(ObjKeymessages.kLABEL_OFF) )"
            let originalPrice = Double(objProductDetails?.price ?? 0.0 == 0.0 ? objProductDetails?.dbOriginalProductPrice?.toDouble() ?? 0.0 : objProductDetails?.price ?? 0.0)
            let discountedPrice = Double(objProductDetails?.discountPrice ?? 0.0 == 0.0 ? objProductDetails?.dDiscountedProductPrice?.toDouble() ?? 0.0 : objProductDetails?.discountPrice ?? 0.0)
            let a = originalPrice - discountedPrice
            let b = a / originalPrice
            let percent = b * 100
            lblOffer.text = "(\(forTrailingZero(temp: percent.rounded(.up)))% \(ObjKeymessages.kLABEL_OFF))"
        }
    }
    
    fileprivate func setLogicOfDiscountWithOldSearch(_ priceOfProduct: Double, _ discountOfProduct: Double) {
        if objProductDetails?.tiDiscountType ?? 0 == 0  {  // no discount
            lblPrice.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: objProductDetails?.price ?? 0.0 == 0.0 ? objProductDetails?.dbOriginalProductPrice?.toDouble() ?? 0.0 : objProductDetails?.price ?? 0.0))")
            lblOldPrice.isHidden = true
            lblOffer.isHidden = true
        }
        else {  // discount is there
            lblPrice.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: objProductDetails?.discountPrice ?? 0.0 == 0.0 ? objProductDetails?.dDiscountedProductPrice?.toDouble() ?? 0.0 : objProductDetails?.discountPrice ?? 0.0))")
            lblOldPrice.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: objProductDetails?.price ?? 0.0 == 0.0 ? objProductDetails?.dbOriginalProductPrice?.toDouble() ?? 0.0 : objProductDetails?.price ?? 0.0))", isDiscounted: true)
            lblOldPrice.isHidden = false
            lblOffer.isHidden = false
            lblOffer.text = "( \(forTrailingZero(temp: objProductDetails?.discountPrice ?? 0.0 == 0.0 ? objProductDetails?.dDiscountedProductPrice?.toDouble() ?? 0.0 : objProductDetails?.discountPrice ?? 0.0))% \(ObjKeymessages.kLABEL_OFF) )"
            let originalPrice = Double(objProductDetails?.price ?? 0.0 == 0.0 ? objProductDetails?.dbOriginalProductPrice?.toDouble() ?? 0.0 : objProductDetails?.price ?? 0.0)
            let discountedPrice = Double(objProductDetails?.discountPrice ?? 0.0 == 0.0 ? objProductDetails?.dDiscountedProductPrice?.toDouble() ?? 0.0 : objProductDetails?.discountPrice ?? 0.0)
            let a = originalPrice - discountedPrice
            let b = a / originalPrice
            let percent = b * 100
            lblOffer.text = "(\(forTrailingZero(temp: percent.rounded(.up)))% \(ObjKeymessages.kLABEL_OFF))"
        }
    }
    
    private func setDetails() {
        // Apps Flyer Event
        AppsFlyerEvents.shared.afContentView(price: objProductDetails?.price ?? 0.0 == 0.0 ? objProductDetails?.dbOriginalProductPrice?.toDouble() ?? 0.0 : objProductDetails?.price ?? 0.0, content: objProductDetails?.name == nil ? objProductDetails?.vProductName ?? "" : objProductDetails?.name ?? "", contentId: objProductDetails?.biProductId ?? 0, contentType: objProductDetails?.vProductUnit ?? "")
        // set strike through in old price
        let attributeString: NSMutableAttributedString = NSMutableAttributedString(string: lblOldPrice.text ?? "")
        attributeString.addAttribute(NSAttributedString.Key.strikethroughStyle, value: NSUnderlineStyle.single.rawValue, range: NSRange(location: 0, length: lblOldPrice.text?.length ?? 0))
        attributeString.addAttribute(NSAttributedString.Key.strikethroughColor, value: UIColor.AppTheme_StrikeThroughColor_707070, range: NSRange(location: 0, length: lblOldPrice.text?.length ?? 0))
//        lblOldPrice.attributedText = attributeString

        lblProductName.text = objProductDetails?.name == nil ? objProductDetails?.vProductName ?? "" : objProductDetails?.name ?? ""
//        lblPrice.text = "\(objProductDetails?.dbOriginalProductPrice ?? "") \(ObjKeymessages.kLABEL_SAR)"
        let priceOfProduct = objProductDetails?.price ?? 0.0 == 0.0 ? objProductDetails?.dbOriginalProductPrice?.toDouble() ?? 0.0 : objProductDetails?.price ?? 0.0
        let discountOfProduct = objProductDetails?.discountPrice ?? 0.0 == 0.0 ? objProductDetails?.dDiscountedProductPrice?.toDouble() ?? 0.0 : objProductDetails?.discountPrice ?? 0.0
        
        if self.strFromWhichScreen == "HomeSearch" {
            self.setLogicOfDiscountWithOldSearch(priceOfProduct, discountOfProduct)
        } else {
            self.setLogicOfDiscountWithNewSearch(priceOfProduct, discountOfProduct)
        }
        

        lblProductSkuValue.text = objProductDetails?.vProductSKU
        lblCombinedProductName.text = "\(objProductDetails?.name == nil ? objProductDetails?.vProductName ?? "" : objProductDetails?.name ?? "") \(objProductDetails?.vProductUnit ?? "")"
        lblProductDesc.text = objProductDetails?.txProductDescription
        
        if objProductDetails?.isLowQuantity == 0 {  // 0 - hide low quantity label
            imgProduct.kf.indicatorType = .activity
            let url = URL(string: objProductDetails?.vProductImage ?? "")
            imgProduct.kf.setImage(with: url , placeholder: UIImage(named: "placeholder_product"))

            viewLowQuantity.isHidden = true
            lblLeftInStock.isHidden = true
            lblLeftInStock.text = ""
        }
        else {  // 1 - show low quantity label
            if objProductDetails?.iHowMuchLeftInStock ?? 0 <= 0 {  // out of stock
                viewLowQuantity.isHidden = true
                
                imgProduct.kf.indicatorType = .activity
                let url = URL(string: objProductDetails?.vProductImage ?? "")
                
                imgProduct.kf.setImage(with: url , placeholder: UIImage(named: "placeholder_product")) { result in
                   switch result {
                   case .success(let value):
                       print("Image: \(value.image). Got from: \(value.cacheType)")
                       self.imgProduct.image = value.image.grayed
                   case .failure(let error):
                       print("Error: \(error)")
                   }
                 }
            }
            else {
                viewLowQuantity.isHidden = false
                
                imgProduct.kf.indicatorType = .activity
                let url = URL(string: objProductDetails?.vProductImage ?? "")
                imgProduct.kf.setImage(with: url , placeholder: UIImage(named: "placeholder_product"))
            }
            lblLeftInStock.isHidden = false
            lblLeftInStock.text = "\(ObjKeymessages.kLABEL_ONLY) \(forTrailingZero(temp: objProductDetails?.iHowMuchLeftInStock ?? 0)) \(ObjKeymessages.kLABEL_LEFT_IN_STOCK)"
        }
        
        collVwProducts.reloadData()
        
        if arrMayLikeProducts.count <= 0 {  // no data
            self.collVwProductsHeightConstraint.constant = 0
            lblYouMay.isHidden = true
            cons_top_collectionView.constant = 0
            cons_bottom_collectionView.constant = 0
            cons_top_lblYouMay.constant = 0
        }
        
        delay(0.3) {
            self.updatePopupHeight(to: min(CGFloat((self.scrollView.contentSize.height) + self.heightOfferTitle + (UIApplication.shared.windows.first?.safeAreaInsets.bottom ?? 0)), UIScreen.main.bounds.height * 0.95))
        }

        if objProductDetails?.iHowMuchLeftInStock ?? 0 <= 0 {  // out of stock
            btnAddToCart.setTitle(ObjKeymessages.kLABEL_OUT_OF_STOCK, for: .normal)
            btnAddToCart.backgroundColor = UIColor.AppTheme_LightGrayColor_CECECE
            btnAddToCart.isUserInteractionEnabled = false
            viewQuantity.isHidden = true
            btnNotifyMe.isHidden = false
        }
        else {
            btnAddToCart.setTitle(ObjKeymessages.kLABEL_ADD_TO_CART, for: .normal)
            btnAddToCart.backgroundColor = UIColor.AppTheme_BlueColor_012CDA
            btnAddToCart.isUserInteractionEnabled = true
            viewQuantity.isHidden = false
            btnNotifyMe.isHidden = true
        }

        
    }
    
    private func setBundleDetails() {
        // set strike through in old price
        let attributeString: NSMutableAttributedString = NSMutableAttributedString(string: lblOldPrice.text ?? "")
        attributeString.addAttribute(NSAttributedString.Key.strikethroughStyle, value: NSUnderlineStyle.single.rawValue, range: NSRange(location: 0, length: lblOldPrice.text?.length ?? 0))
        attributeString.addAttribute(NSAttributedString.Key.strikethroughColor, value: UIColor.AppTheme_StrikeThroughColor_707070, range: NSRange(location: 0, length: lblOldPrice.text?.length ?? 0))
//        lblOldPrice.attributedText = attributeString
        lblProductName.text = objBundleDetails?.vBunddelName ?? ""
        let priceOfProduct = objBundleDetails?.price
        let discountOfProduct = objBundleDetails?.discountPrice ?? 0.0
        if objBundleDetails?.discountPrice ?? 0 <= 0  {  // no discount
            lblPrice.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: objBundleDetails?.price ?? 0.0))")
            lblOldPrice.isHidden = true
            lblOffer.isHidden = true
        }
        else {  // discount is there
            lblPrice.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: objBundleDetails?.discountPrice ?? 0.0))")
            lblOldPrice.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: objBundleDetails?.price ?? 0.0))", isDiscounted: true)
            lblOldPrice.isHidden = false
            lblOffer.isHidden = false
            lblOffer.text = "( \(forTrailingZero(temp: objBundleDetails?.discountPrice ?? 0.0))% \(ObjKeymessages.kLABEL_OFF) )"
            let originalPrice = Double(objBundleDetails?.price ?? 0.0)
            let discountedPrice = Double(objBundleDetails?.discountPrice ?? 0.0)
            let a = originalPrice - discountedPrice
            let b = a / originalPrice
            let percent = b * 100
            lblOffer.text = "(\(forTrailingZero(temp: percent.rounded(.up)))% \(ObjKeymessages.kLABEL_OFF))"
        }
        lblProductSkuValue.text = objBundleDetails?.vBunddelSKU
        lblCombinedProductName.text = "\(objBundleDetails?.vBunddelName ?? "")"
        lblProductDesc.text = objBundleDetails?.txBunddelDescription
        imgProduct.kf.indicatorType = .activity
        let url = URL(string: objBundleDetails?.vBunddelImage ?? "")
        imgProduct.kf.setImage(with: url , placeholder: UIImage(named: "placeholder_product"))
        
        if self.objBundleDetails?.getCartBunddelProducts?.count ?? 0 >= 1 {
            self.arrMayLikeProducts = self.objBundleDetails?.getCartBunddelProducts?.first?.youMayAlsoLikeProducts ?? []
            self.lblContainProducts.isHidden = false
            lblProductDesc.text?.append("\n\n")
            DispatchQueue.main.async {
                self.tblProductsOfBundle.separatorStyle = .none
                self.tblProductsOfBundle.isMultipleTouchEnabled = false
                self.constraintHeightTblProductsOfBundle.constant = CGFloat((70 * (self.objBundleDetails?.getCartBunddelProducts?.count ?? 1)))
                self.view.updateConstraints()
                self.tblProductsOfBundle.reloadData()
            }
        }
        
        if objBundleDetails?.iHowMuchLeftInStock == 0 { // out of stock 
            btnAddToCart.setTitle(ObjKeymessages.kLABEL_OUT_OF_STOCK, for: .normal)
            btnAddToCart.backgroundColor = UIColor.AppTheme_LightGrayColor_CECECE
            btnAddToCart.isUserInteractionEnabled = false
            viewQuantity.isHidden = true
        }
        
        collVwProducts.reloadData()
        if arrMayLikeProducts.count <= 0 {  // no data
            self.collVwProductsHeightConstraint.constant = 0
            lblYouMay.isHidden = true
            cons_top_collectionView.constant = 0
            cons_bottom_collectionView.constant = 0
            cons_top_lblYouMay.constant = 0
        } else {
            self.collVwProductsHeightConstraint.constant = 195
            lblYouMay.isHidden = false
            cons_top_collectionView.constant = 10
            cons_bottom_collectionView.constant = 20
            cons_top_lblYouMay.constant = 15
        }
        
        delay(0.3) {
            self.updatePopupHeight(to: min(CGFloat((self.scrollView.contentSize.height) + self.heightOfferTitle + (UIApplication.shared.windows.first?.safeAreaInsets.bottom ?? 0)), UIScreen.main.bounds.height * 0.95))
        }
    }
    
    func displayAlertAndDismiss(string: String, cartCount: Int) {
        /*let alert = UIAlertController(title: AppName, message: string, preferredStyle: .alert)
        let okButton = UIAlertAction(title: ObjKeymessages.kLABEL_OK, style: .default, handler: {(_ action: UIAlertAction) -> Void in
            self.dismissVC(completion: nil)
        })
        alert.addAction(okButton)
        self.present(alert, animated: true) {() -> Void in }*/
        
        // update my cart button count
        delegate?.updateCartCount(cartCount: cartCount)

        

        self.vibrationDevice()
        AppSingletonObj.showAlert(strMessage: string)
        self.addedToCartStartAnimation()
        DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
            self.dismissVC(completion: nil)
        }
    }
    
    private func addedToCartStartAnimation() {
        if !UserDefaults.standard.isCurrentLanguageArabic() {
            let path = Bundle.main.path(forResource: "add_to_cart_animation",
                                        ofType: "json") ?? ""
            self.viewAddToCartAnimation.animation = Animation.filepath(path)
        }
        self.viewAddToCartAnimation.contentMode = .scaleAspectFill
        self.viewAddToCartAnimation.loopMode = .playOnce
        self.btnAddToCart.isHidden = true
        self.viewAddToCartAnimation.isHidden = false
        self.viewAddToCartAnimation.play()
    }

    /*func displaySessionExpiredAlert(strMsg: String) {
        let alert = UIAlertController(title: AppName, message: strMsg, preferredStyle: .alert)
        let okButton = UIAlertAction(title: ObjKeymessages.kLABEL_OK, style: .default, handler: {(_ action: UIAlertAction) -> Void in
            AppDel?.restartApp()
            self.dismiss(animated: true, completion: nil)
        })
        alert.addAction(okButton)
        self.present(alert, animated: true) {() -> Void in }
    }*/

    func displayErrorAlert(strMsg:String) {
        /*let alert = UIAlertController(title: AppName, message: strMsg, preferredStyle: .alert)
        let okButton = UIAlertAction(title: ObjKeymessages.kLABEL_OK, style: .default, handler: {(_ action: UIAlertAction) -> Void in
            self.dismissVC(completion: nil)
        })
        alert.addAction(okButton)
        self.present(alert, animated: true) {() -> Void in }*/
        
        /*let vc = settingsStoryboard.instantiate(CustomPopupAlertViewController.self)
        vc.modalPresentationStyle = .overFullScreen
        vc.modalTransitionStyle = .crossDissolve
        vc.strDescription = strMsg
        vc.isCenterButton = true
        vc.strCenterButtonTitle = ObjKeymessages.kLABEL_OK
        vc.completion = { (index) in
            if index == 0 {
                self.dismissVC(completion: nil)
            }
        }
        self.presentVC(vc)*/
        
        AppSingleton.shared.showCustomPopUpWithOkButton(strButtonTitle: ObjKeymessages.kLABEL_OK, strMessage: strMsg, showOnTopVC: true) { (isOk) in
            debugPrint("\(strMsg)")
//            if isOk == true {
//                self.dismissVC(completion: nil)
//            }
        }
    }

    func getFavoriteValue(model : FavouriteResponseFields) {
        
        stackViewActivityIndicatorView.isHidden = true
        activityIndicatorView.isHidden = true
        activityIndicatorView.stopAnimating()
        btnFavorite.isHidden = false

        if model.tiIsFavourite == 1 {
            ifFavOrNot = 1
//            btnFavorite.setImage(UIImage(named: "Heart-Filled"), for: .normal)
            btnFavorite.isSelected = true
        }
        else {
            ifFavOrNot = 0
//            btnFavorite.setImage(UIImage(named: "Heart"), for: .normal)
            btnFavorite.isSelected = false
        }
    }

    func getProductInfoResponse(model: ProductInfoResponseFields) {
        // logic of bundles
        if model.bundle != nil {
            self.objBundleDetails = model.bundle
            self.setBundleDetails()
            return
        }
        if model.offerDescription == "" { // Do not show max quantity view
            viewOuterMaxQuantity.isHidden = true
            cons_top_stackViewMaxQuantity.constant = 0
        }
        else { // Show max quantity view
            viewOuterMaxQuantity.isHidden = false
            cons_top_stackViewMaxQuantity.constant = 25
            
            let string = model.offerDescription ?? ""
            let range = (string as NSString).range(of: model.maxQty ?? "")
            let range1 = (string as NSString).range(of: model.dateRange ?? "")
            let attributedString = NSMutableAttributedString(string: string)
            attributedString.addAttribute(NSAttributedString.Key.font, value: UIFont(name: Fonts.LoewNextArabicBold, size: 14)!, range: range)
            attributedString.addAttribute(NSAttributedString.Key.font, value: UIFont(name: Fonts.LoewNextArabicBold, size: 14)!, range: range1)
            attributedString.addAttribute(NSAttributedString.Key.foregroundColor, value: UIColor.black, range: range)
            attributedString.addAttribute(NSAttributedString.Key.foregroundColor, value: UIColor.black, range: range1)
            lblMaxQuantityInfo.attributedText = attributedString
        }
        // check from offer title
        if model.offerTitle != "" && model.offerTitle != nil {
            self.imageOfferDiscount.translatesAutoresizingMaskIntoConstraints = false
            self.animate(self.imageOfferDiscount)
            stackViewOfferDiscount.isHidden = false
            lblOfferDiscount.text = model.offerTitle
            self.offerQuantity = model.offerQuantity ?? 0
            self.calculateGetOfferQuantity(cartQuantity: 1)
        } else {
            stackViewOfferDiscount.isHidden = true
        }
    }
    
    private func animate(_ image: UIImageView) {
        UIView.animate(withDuration: 2, animations: {
            image.transform = CGAffineTransform(rotationAngle: CGFloat(Double.pi))
        }) { (isAnimationComplete) in
            UIView.animate(withDuration: 2) {
                image.transform = CGAffineTransform.identity
            }
        }
    }

    func calculateGetOfferQuantity(cartQuantity: Int) {
        if cartQuantity >= self.offerQuantity {
            lblGetOffer.text = "You got the offer".localized
        } else {
            lblGetOffer.text = "Add to get the offer".localized.replacingOccurrences(of: "%2d", with: "\(abs(offerQuantity - cartQuantity))")
        }
        if self.isQuantitiesDiscountPriceUsed {
            self.lblPriceQuantityDiscount.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: self.getQuantityDiscountPrice(quantity: cartQuantity)))")
        }
    }
    
    private func getQuantityDiscountPrice(quantity: Int) -> Double {
        let _ = pricesQuantitiesDiscountArr?.map({ prices in
            if quantity >= prices.quantity_from ?? 0 && prices.quantity_to == 0 {
                self.quantitiesDiscountPrice = prices.price?.toDouble() ?? 0.0
            }
            else if quantity == prices.quantity_from ?? 0 {
                self.quantitiesDiscountPrice = prices.price?.toDouble() ?? 0.0
            }
            else if quantity >= prices.quantity_from ?? 0 && quantity <= prices.quantity_to ?? 0 {
                self.quantitiesDiscountPrice = prices.price?.toDouble() ?? 0.0
            }
        })
        
        let discountPrice = objProductDetails?.discountPrice ?? 0.0 == 0.0 ? objProductDetails?.dDiscountedProductPrice?.toDouble() ?? 0.0 : objProductDetails?.discountPrice ?? 0.0
        
        if self.quantitiesDiscountPrice >= 0 && self.quantitiesDiscountPrice != discountPrice {
            self.lblPriceQuantityDiscount.isHidden = false
            // set strike through in old price
            let attributeString: NSMutableAttributedString = NSMutableAttributedString(string: lblPrice.text ?? "")
            attributeString.addAttribute(NSAttributedString.Key.strikethroughStyle, value: NSUnderlineStyle.single.rawValue, range: NSRange(location: 0, length: lblPrice.text?.length ?? 0))
            attributeString.addAttribute(NSAttributedString.Key.strikethroughColor, value: UIColor.AppTheme_StrikeThroughColor_707070, range: NSRange(location: 0, length: lblPrice.text?.length ?? 0))
            self.lblPrice.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: lblPrice.text ?? "", isDiscounted: true)
//            self.lblPrice.textColor = .AppTheme_RedColor_D82828
        } else {
            self.lblPriceQuantityDiscount.isHidden = true
            let text = lblPrice.text ?? ""
            self.lblPrice.attributedText = nil
            self.lblPrice.text = text
            self.lblPrice.textColor = .black
        }
        return self.quantitiesDiscountPrice
    }
    
    func showToastMessage(strMsg: String) {
        Mixpanel.mainInstance().track(event: ScreenNames.NotifyMeForOutOfStockProduct.rawValue, properties: [
            "user_id": "\(User.shared.iUserId ?? 0)",
            "product_id": objProductDetails?.id == nil ? objProductDetails?.biProductId : objProductDetails?.id,
            "product_sku": objProductDetails?.vProductSKU,
            "product_name": objProductDetails?.name == nil ? objProductDetails?.vProductName : objProductDetails?.name,
            "product_unit": objProductDetails?.vProductUnit
        ])

        AppSingletonObj.showAlert(strMessage: strMsg)
    }

}

/*class UIViewWithDashedLineBorder: UIView {

    override func draw(_ rect: CGRect) {

        let path = UIBezierPath(roundedRect: rect, cornerRadius: 8)

//        UIColor.purple.setFill()
//        path.fill()

        UIColor.lightGray.setStroke()
        path.lineWidth = 4

        let dashPattern : [CGFloat] = [10, 4]
        path.setLineDash(dashPattern, count: 2, phase: 0)
        path.stroke()
    }
}*/

class CustomDashedView: UIView {

//    @IBInspectable override var cornerRadius: CGFloat = 0 {
//        didSet {
//            layer.cornerRadius = cornerRadius
//            layer.masksToBounds = cornerRadius > 0
//        }
//    }
    @IBInspectable var dashWidth: CGFloat = 0
    @IBInspectable var dashColor: UIColor = .clear
    @IBInspectable var dashLength: CGFloat = 0
    @IBInspectable var betweenDashesSpace: CGFloat = 0

    var dashBorder: CAShapeLayer?
//    var cornerRadius: CGFloat = 0
    
    override func layoutSubviews() {
        super.layoutSubviews()
        dashBorder?.removeFromSuperlayer()
        let dashBorder = CAShapeLayer()
        dashBorder.lineWidth = dashWidth
        dashBorder.strokeColor = dashColor.cgColor
        dashBorder.lineDashPattern = [dashLength, betweenDashesSpace] as [NSNumber]
        dashBorder.frame = bounds
        dashBorder.fillColor = nil
        if cornerRadius > 0 {
            dashBorder.path = UIBezierPath(roundedRect: bounds, cornerRadius: cornerRadius).cgPath
        } else {
            dashBorder.path = UIBezierPath(rect: bounds).cgPath
        }
        layer.addSublayer(dashBorder)
        self.dashBorder = dashBorder
    }
}

extension String {
    var htmlToAttributedString: NSAttributedString? {
        guard let data = data(using: .utf8) else { return nil }
        do {
            return try NSAttributedString(data: data, options: [.documentType: NSAttributedString.DocumentType.html, .characterEncoding:String.Encoding.utf8.rawValue], documentAttributes: nil)
        } catch {
            return nil
        }
    }
    var htmlToString: String {
        return htmlToAttributedString?.string ?? ""
    }
}

extension ProductPopupViewController: QTYButtonDelegate {
    // MARK: - Func
    func didChangeValue(value: Int) {
        self.calculateGetOfferQuantity(cartQuantity: value)
    }
    
}

// MARK: - Extension - UITableView
extension ProductPopupViewController: UITableViewDelegate, UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        self.objBundleDetails?.getCartBunddelProducts?.count ?? 0
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "ProductsOfBundleTVCell", for: indexPath) as! ProductsOfBundleTVCell
        cell.setupCell(productCount: objBundleDetails?.getCartBunddelProducts?[indexPath.row].qty?.string ?? "", productName: objBundleDetails?.getCartBunddelProducts?[indexPath.row].product?.vProductName ?? "", productUnit: objBundleDetails?.getCartBunddelProducts?[indexPath.row].product?.vProductUnit ?? "", productImageUrl: objBundleDetails?.getCartBunddelProducts?[indexPath.row].product?.vProductImage ?? "")
        return cell
    }
    
}
// MARK: - Extension - ImageSlideshowDelegate
extension ProductPopupViewController: ImageSlideshowDelegate {
    func imageSlideshow(_ imageSlideshow: ImageSlideshow, didChangeCurrentPageTo page: Int) {
        print("current page:", page)
    }
    
    @objc func didTap() {
        let fullScreenController = viewImagesArr.presentFullScreenController(from: self)
        fullScreenController.modalPresentationStyle = .overCurrentContext
           // set the activity indicator for full screen controller (skipping the line will show no activity indicator)
       }
    
    func imageSlideshowDidEndDecelerating(_ imageSlideshow: ImageSlideshow) {
        debugPrint("imageSlideshowDidEndDecelerating")
    }
    
}
