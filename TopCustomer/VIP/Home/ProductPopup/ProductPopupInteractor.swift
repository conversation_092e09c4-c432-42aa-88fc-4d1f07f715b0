
import UIKit

protocol ProductPopupInteractorProtocol {
    func apiCallForGetProductDetails(dictData:[String:Any])
    func apiCallForAddToCart(dictData:[String:Any])
    func apiCallForGetFavorite(dictData:[String:Any])
//    func apiCallForMarkFavorite(dictData:[String:Any])
    func apiCallForNotifyMe(dictData:[String:Any])
    func apiCallForGetProductInfo(dictData:[String:Any])

}

protocol ProductPopupDataStore {
    //{ get set }
}

class ProductPopupInteractor: ProductPopupInteractorProtocol, ProductPopupDataStore {

    // MARK: Objects & Variables
    var presenterProductPopup: ProductPopupPresentationProtocol?
    
    func apiCallForGetProductDetails(dictData:[String:Any]) {
        
        ActivityIndicator.shared.showCentralSpinner()
        
        let authorization = User.shared.checkUserLoginStatus() ? getAuthorizationText() : GuestAccessToken

        ProductAPI.productDetails(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, biProductId: Int(setDataInString(dictData["biProductId"] as AnyObject)) ?? 0) { data, error in
            ActivityIndicator.shared.hideCentralSpinner()
            self.presenterProductPopup?.apiResponseGetProductDetails(response: data, error: error)
        }
        
    }

    func apiCallForAddToCart(dictData:[String:Any]) {
        
        ActivityIndicator.shared.showCentralSpinner()
        
        let authorization = getAuthorizationText()
        
        CartAPI.addToCart(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, biProductId: Int(setDataInString(dictData["biProductId"] as AnyObject)) ?? 0, tiIsCheck: Int(setDataInString(dictData["tiIsCheck"] as AnyObject)) ?? 0, iProductQuantity: Int(setDataInString(dictData["iProductQuantity"] as AnyObject)) ?? 0, dbPrice: setDataInString(dictData["dbPrice"] as AnyObject), isMosques: setDataInString(dictData["isMosques"] as AnyObject), dbBundleID: Int(setDataInString(dictData["bunddelId"] as AnyObject)) ?? 0) { data, error in
            
            ActivityIndicator.shared.hideCentralSpinner()
            self.presenterProductPopup?.apiResponseAddToCart(response: data, error: error)
        }
        
    }

    func apiCallForGetFavorite(dictData:[String:Any]) {
        
//        ActivityIndicator.shared.showCentralSpinner()
        
        let authorization = getAuthorizationText()
        
        FavouriteAPI.markFavourite(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, biProductId: Int(setDataInString(dictData["biProductId"] as AnyObject)) ?? 0, tiIsFavourite: Int(setDataInString(dictData["tiIsFavourite"] as AnyObject)) ?? 0) { data, error in
//                ActivityIndicator.shared.hideCentralSpinner()
                self.presenterProductPopup?.apiResponseGetFavorite(response: data, error: error)
        }
        
    }

    /*func apiCallForMarkFavorite(dictData:[String:Any]) {
        
        ActivityIndicator.shared.showCentralSpinner()
        
        let authorization = getAuthorizationText()
        
        FavouriteAPI.markFavourite(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, biProductId: Int(setDataInString(dictData["biProductId"] as AnyObject)) ?? 0, tiIsFavourite: Int(setDataInString(dictData["tiIsFavourite"] as AnyObject)) ?? 0) { data, error in
                ActivityIndicator.shared.hideCentralSpinner()
                self.presenterProductPopup?.apiResponseGetFavorite(response: data, error: error)
        }
        
    }*/

    func apiCallForNotifyMe(dictData:[String:Any]) {
        
        ActivityIndicator.shared.showCentralSpinner()
        
        let authorization = getAuthorizationText()
        
        NotifyMeAPI.notifyMe(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang,
                             biProductId: Int(setDataInString(dictData["biProductId"] as AnyObject)) ?? 0,
                             biBundleId: Int(setDataInString(dictData["biBundleId"] as AnyObject)) ?? 0) { data, error in
            ActivityIndicator.shared.hideCentralSpinner()
            self.presenterProductPopup?.apiResponseNotifyMe(response: data, error: error)
        }
        
    }

    func apiCallForGetProductInfo(dictData:[String:Any]) {
        let authorization = getAuthorizationText()
        
        ProductAPI.productInfo(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang,
                               biProductId: Int(setDataInString(dictData["biProductId"] as AnyObject)) ?? 0,
                               biBundleId: Int(setDataInString(dictData["biBundleId"] as AnyObject)) ?? 0) { data, error in
            self.presenterProductPopup?.apiResponseGetProductInfo(response: data, error: error)
        }
    }

}
