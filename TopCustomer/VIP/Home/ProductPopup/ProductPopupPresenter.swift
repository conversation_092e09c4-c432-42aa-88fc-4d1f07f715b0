
import UIKit

protocol ProductPopupPresentationProtocol {
    func apiCallForGetProductDetails(dictData:[String:Any])
    func apiResponseGetProductDetails(response:ProductDetailResponse?,error:Error?)

    func apiCallForAddToCart(dictData:[String:Any])
    func apiResponseAddToCart(response:AddToCartResponse?,error:Error?)

    func apiCallForGetFavorite(dictData:[String:Any])
    func apiResponseGetFavorite(response:FavouriteResponse?,error:Error?)

//    func apiCallForMarkFavorite(dictData:[String:Any])
//    func apiResponseMarkFavorite(response:FavouriteResponse?,error:Error?)

    func apiCallForNotifyMe(dictData:[String:Any])
    func apiResponseNotifyMe(response:CommonFields?,error:Error?)
    
    func apiCallForGetProductInfo(dictData:[String:Any])
    func apiResponseGetProductInfo(response:ProductInfoResponse?,error:Error?)

}

class ProductPopupPresenter: ProductPopupPresentationProtocol {
    
    // MARK: Objects & Variables
    weak var viewControllerProductPopup: ProductPopupProtocol?
    var interactorProductPopup: ProductPopupInteractorProtocol?
    
    func apiCallForGetProductDetails(dictData:[String:Any]) {
        interactorProductPopup?.apiCallForGetProductDetails(dictData: dictData)
    }

    func apiResponseGetProductDetails(response: ProductDetailResponse?, error: Error?) {
//        if let vc = self.viewControllerProductPopup as? BaseViewController {
//            DispatchQueue.main.async {
//                vc.endRefresing()
//            }
//        }
        if let error = error  {
            viewControllerProductPopup?.displayAlert(string: error.localizedDescription)
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            //            viewController?.displayAlert(strTitle: "", strMessage: KeyMessages.kInvalidateResponse)
            return
        }
        
        if code == APIINVALIDAUTHORIZATIONCODE401 {
//            AppSingletonObj.goToLoginScreen(message: response.responseMessage ?? "")
            AppSingletonObj.displaySessionExpiredAlert(strMsg: response.responseMessage ?? "")
            return
        }

        if code == APICODE400 {
//            viewControllerProductPopup?.displayAlert(string: response.responseMessage ?? "")
            viewControllerProductPopup?.displayErrorAlert(strMsg: response.responseMessage ?? "")
            return
        }
        
        guard let model = response.responseData,code == APISUCCESSCODE200  else {
            return
        }
        self.viewControllerProductPopup?.getProductDetailsInfo(model: model)
    }

    func apiCallForAddToCart(dictData:[String:Any]) {
        interactorProductPopup?.apiCallForAddToCart(dictData: dictData)
    }

    func apiResponseAddToCart(response: AddToCartResponse?, error: Error?) {
//        if let vc = self.viewControllerProductPopup as? BaseViewController {
//            DispatchQueue.main.async {
//                vc.endRefresing()
//            }
//        }
        if let error = error  {
            viewControllerProductPopup?.displayAlert(string: error.localizedDescription)
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            //            viewController?.displayAlert(strTitle: "", strMessage: KeyMessages.kInvalidateResponse)
            return
        }
        
        if code == APIINVALIDAUTHORIZATIONCODE401 {
//            AppSingletonObj.goToLoginScreen(message: response.responseMessage ?? "")
            AppSingletonObj.displaySessionExpiredAlert(strMsg: response.responseMessage ?? "")
            return
        }

        if code == APICODE400 {
            viewControllerProductPopup?.displayAlert(string: response.responseMessage ?? "")
            return
        }
        
        if code == APICODE203 {
            self.viewControllerProductPopup?.displayErrorAlert(strMsg: response.responseMessage ?? "")
            return
        }

        guard let model = response.responseData,code == APISUCCESSCODE200  else {
            return
        }
        self.viewControllerProductPopup?.displayAlertAndDismiss(string: response.responseMessage ?? "", cartCount: model.getCartProducts ?? 0)
    }

    func apiCallForGetFavorite(dictData:[String:Any]) {
        interactorProductPopup?.apiCallForGetFavorite(dictData: dictData)
    }

    func apiResponseGetFavorite(response: FavouriteResponse?, error: Error?) {
//        if let vc = self.viewControllerProductPopup as? BaseViewController {
//            DispatchQueue.main.async {
//                vc.endRefresing()
//            }
//        }
        if let error = error  {
            viewControllerProductPopup?.displayAlert(string: error.localizedDescription)
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            //            viewController?.displayAlert(strTitle: "", strMessage: KeyMessages.kInvalidateResponse)
            return
        }
        
        if code == APIINVALIDAUTHORIZATIONCODE401 {
//            AppSingletonObj.goToLoginScreen(message: response.responseMessage ?? "")
            AppSingletonObj.displaySessionExpiredAlert(strMsg: response.responseMessage ?? "")
            return
        }

        if code == APICODE400 {
            viewControllerProductPopup?.displayAlert(string: response.responseMessage ?? "")
            return
        }
        
        if code == APICODE203 {
            self.viewControllerProductPopup?.displayErrorAlert(strMsg: response.responseMessage ?? "")
            return
        }

        guard let model = response.responseData,code == APISUCCESSCODE200  else {
            return
        }
        self.viewControllerProductPopup?.getFavoriteValue(model: model)
    }

//    func apiCallForMarkFavorite(dictData:[String:Any]) {
//        interactorProductPopup?.apiCallForMarkFavorite(dictData: dictData)
//    }
//
//    func apiResponseMarkFavorite(response: FavouriteResponse?, error: Error?) {
//        if let vc = self.viewControllerProductPopup as? BaseViewController {
////            DispatchQueue.main.async {
////                vc.endRefresing()
////            }
//        }
//        if let error = error  {
//            viewControllerProductPopup?.displayAlert(string: error.localizedDescription)
//            return
//        }
//
//        guard let response = response,let code = response.responseCode else {
//            //            viewController?.displayAlert(strTitle: "", strMessage: KeyMessages.kInvalidateResponse)
//            return
//        }
//
//        if code == APIINVALIDAUTHORIZATIONCODE401 {
////            AppSingletonObj.goToLoginScreen(message: response.responseMessage ?? "")
//            self.viewControllerProductPopup?.displaySessionExpiredAlert(strMsg: response.responseMessage ?? "")
//            return
//        }
//
//        if code == APICODE400 {
//            viewControllerProductPopup?.displayAlert(string: response.responseMessage ?? "")
//            return
//        }
//
//        if code == APICODE203 {
//            self.viewControllerProductPopup?.displayErrorAlert(strMsg: response.responseMessage ?? "")
//            return
//        }
//
//        guard let model = response.responseData,code == APISUCCESSCODE200  else {
//            return
//        }
//        self.viewControllerProductPopup?.getFavoriteValue(model: model)
//    }

    func apiCallForNotifyMe(dictData:[String:Any]) {
        interactorProductPopup?.apiCallForNotifyMe(dictData: dictData)
    }

    func apiResponseNotifyMe(response:CommonFields?,error:Error?) {
//        if let vc = self.viewControllerProductPopup as? BaseViewController {
//            DispatchQueue.main.async {
//                vc.endRefresing()
//            }
//        }
        if let error = error  {
            viewControllerProductPopup?.displayAlert(string: error.localizedDescription)
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            //            viewController?.displayAlert(strTitle: "", strMessage: KeyMessages.kInvalidateResponse)
            return
        }
        
        if code == APIINVALIDAUTHORIZATIONCODE401 {
//            AppSingletonObj.goToLoginScreen(message: response.responseMessage ?? "")
            AppSingletonObj.displaySessionExpiredAlert(strMsg: response.responseMessage ?? "")
            return
        }

        if code == APICODE400 {
            viewControllerProductPopup?.displayAlert(string: response.responseMessage ?? "")
            return
        }
        
        if code == APICODE203 {
            self.viewControllerProductPopup?.displayErrorAlert(strMsg: response.responseMessage ?? "")
            return
        }

//        guard let model = response.responseData,code == APISUCCESSCODE200  else {
//            return
//        }
        
        self.viewControllerProductPopup?.showToastMessage(strMsg: response.responseMessage ?? "")

    }

    func apiCallForGetProductInfo(dictData:[String:Any]) {
        interactorProductPopup?.apiCallForGetProductInfo(dictData: dictData)
    }

    func apiResponseGetProductInfo(response:ProductInfoResponse?,error:Error?) {
        if let error = error  {
            viewControllerProductPopup?.displayAlert(string: error.localizedDescription)
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            //            viewController?.displayAlert(strTitle: "", strMessage: KeyMessages.kInvalidateResponse)
            return
        }
        
        if code == APIINVALIDAUTHORIZATIONCODE401 {
//            AppSingletonObj.goToLoginScreen(message: response.responseMessage ?? "")
            AppSingletonObj.displaySessionExpiredAlert(strMsg: response.responseMessage ?? "")
            return
        }

        if code == APICODE400 {
            viewControllerProductPopup?.displayAlert(string: response.responseMessage ?? "")
            return
        }
        
        if code == APICODE203 {
            self.viewControllerProductPopup?.displayErrorAlert(strMsg: response.responseMessage ?? "")
            return
        }

        guard let model = response.responseData,code == APISUCCESSCODE200  else {
            return
        }
        self.viewControllerProductPopup?.getProductInfoResponse(model: model)

    }

}
