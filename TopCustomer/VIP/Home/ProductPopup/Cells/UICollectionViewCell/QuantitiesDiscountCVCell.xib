<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="32700.99.1234" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22685"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="LoewNextArabic-Bold.ttf">
            <string>LoewNextArabic-Bold</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" reuseIdentifier="QuantitiesDiscountCVCell" id="gTV-IL-0wX" customClass="QuantitiesDiscountCVCell" customModule="TopCustomer" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="150" height="35"/>
            <autoresizingMask key="autoresizingMask"/>
            <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center">
                <rect key="frame" x="0.0" y="0.0" width="150" height="35"/>
                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                <subviews>
                    <stackView opaque="NO" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="YEg-9W-R5t">
                        <rect key="frame" x="5" y="5" width="140" height="25"/>
                        <subviews>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Buy 1-5 piece 18 SAR\piece" textAlignment="center" lineBreakMode="wordWrap" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="KLk-Sj-nAf" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                <rect key="frame" x="0.0" y="0.0" width="140" height="25"/>
                                <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="13"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                        </subviews>
                    </stackView>
                </subviews>
            </view>
            <viewLayoutGuide key="safeArea" id="ZTg-uK-7eu"/>
            <constraints>
                <constraint firstItem="YEg-9W-R5t" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" constant="5" id="43l-J7-JSs"/>
                <constraint firstItem="YEg-9W-R5t" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" constant="5" id="M1n-s2-NAs"/>
                <constraint firstAttribute="trailing" secondItem="YEg-9W-R5t" secondAttribute="trailing" constant="5" id="lba-yD-oZX"/>
                <constraint firstAttribute="bottom" secondItem="YEg-9W-R5t" secondAttribute="bottom" constant="5" id="ljp-Qe-cwe"/>
            </constraints>
            <connections>
                <outlet property="lblTitle" destination="KLk-Sj-nAf" id="yzt-46-dej"/>
            </connections>
            <point key="canvasLocation" x="139" y="20"/>
        </collectionViewCell>
    </objects>
</document>
