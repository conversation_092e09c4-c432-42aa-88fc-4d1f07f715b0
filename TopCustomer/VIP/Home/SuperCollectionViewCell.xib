<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="22505" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22504"/>
        <capability name="Named colors" minToolsVersion="9.0"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="LoewNextArabic-Bold.ttf">
            <string>LoewNextArabic-Bold</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" reuseIdentifier="SuperCollectionViewCell" id="gTV-IL-0wX" customClass="SuperCollectionViewCell" customModule="TopCustomer" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="150" height="120"/>
            <autoresizingMask key="autoresizingMask"/>
            <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center">
                <rect key="frame" x="0.0" y="0.0" width="150" height="120"/>
                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="MRR-um-ybk">
                        <rect key="frame" x="5" y="5" width="140" height="110"/>
                        <subviews>
                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="iQY-yH-YnL">
                                <rect key="frame" x="0.0" y="5" width="140" height="100"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="gW4-fK-Fgd">
                                        <rect key="frame" x="0.0" y="0.0" width="140" height="74"/>
                                        <subviews>
                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="redraw" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="logo_login_white" translatesAutoresizingMaskIntoConstraints="NO" id="fRt-19-MTK">
                                                <rect key="frame" x="7.6666666666666714" y="-18" width="125.00000000000001" height="110"/>
                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="125" id="UxB-ZR-5TU"/>
                                                    <constraint firstAttribute="height" constant="110" id="f1d-Dk-KtU"/>
                                                </constraints>
                                            </imageView>
                                        </subviews>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstItem="fRt-19-MTK" firstAttribute="centerY" secondItem="gW4-fK-Fgd" secondAttribute="centerY" id="29H-RF-SeE"/>
                                            <constraint firstItem="fRt-19-MTK" firstAttribute="centerX" secondItem="gW4-fK-Fgd" secondAttribute="centerX" id="Vej-gJ-f37"/>
                                        </constraints>
                                    </view>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="yNd-p1-JXE">
                                        <rect key="frame" x="0.0" y="74" width="140" height="26"/>
                                        <constraints>
                                            <constraint firstAttribute="height" relation="greaterThanOrEqual" constant="16" id="vjz-NP-HL9"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="12"/>
                                        <color key="textColor" name="AppTheme_BorderColor_#1F1F1F"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="P1I-an-CAd">
                                        <rect key="frame" x="0.0" y="100" width="140" height="0.0"/>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="height" relation="greaterThanOrEqual" id="zab-z8-6ha"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="yNd-p1-JXE" firstAttribute="leading" secondItem="iQY-yH-YnL" secondAttribute="leading" id="lE8-Gj-5Xa"/>
                                    <constraint firstAttribute="trailing" secondItem="yNd-p1-JXE" secondAttribute="trailing" id="p9S-R4-ivu"/>
                                </constraints>
                            </stackView>
                        </subviews>
                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstAttribute="bottom" secondItem="iQY-yH-YnL" secondAttribute="bottom" constant="5" id="9Ro-sR-Rbn"/>
                            <constraint firstAttribute="trailing" secondItem="iQY-yH-YnL" secondAttribute="trailing" id="Ate-Tr-u0X"/>
                            <constraint firstItem="iQY-yH-YnL" firstAttribute="top" secondItem="MRR-um-ybk" secondAttribute="top" constant="5" id="KKM-S7-kb9"/>
                            <constraint firstItem="iQY-yH-YnL" firstAttribute="leading" secondItem="MRR-um-ybk" secondAttribute="leading" id="SBJ-3F-uXA"/>
                        </constraints>
                    </view>
                </subviews>
            </view>
            <viewLayoutGuide key="safeArea" id="ZTg-uK-7eu"/>
            <constraints>
                <constraint firstAttribute="trailing" secondItem="MRR-um-ybk" secondAttribute="trailing" constant="5" id="2jK-KB-rxX"/>
                <constraint firstItem="MRR-um-ybk" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" constant="5" id="5Nc-ED-7Lp"/>
                <constraint firstAttribute="bottom" secondItem="MRR-um-ybk" secondAttribute="bottom" constant="5" id="CgH-XR-PpZ"/>
                <constraint firstItem="MRR-um-ybk" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" constant="5" id="aMa-P0-IYc"/>
            </constraints>
            <connections>
                <outlet property="imgViewCategory" destination="fRt-19-MTK" id="mBp-hO-VBP"/>
                <outlet property="lblCategoryName" destination="yNd-p1-JXE" id="NWj-8s-XeF"/>
            </connections>
            <point key="canvasLocation" x="128" y="20"/>
        </collectionViewCell>
    </objects>
    <resources>
        <image name="logo_login_white" width="100.66666412353516" height="33"/>
        <namedColor name="AppTheme_BorderColor_#1F1F1F">
            <color red="0.12156862745098039" green="0.12156862745098039" blue="0.12156862745098039" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
    </resources>
</document>
