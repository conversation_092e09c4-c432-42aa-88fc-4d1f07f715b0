
import UIKit

class CategoriesCollectionViewCellBig: UICollectionViewCell {

    //MARK: - Outlets
    @IBOutlet weak var imgCategory: UIImageView!
    @IBOutlet weak var lblCategoryName: UILabel!
    @IBOutlet weak var viewOuter: UIView!
    @IBOutlet weak var lblTitle: MaterialLocalizeLable!
    @IBOutlet weak var viewBookmark: UIView!
    
    override func draw(_ rect: CGRect) {
        super.draw(rect)
        
        viewOuter.layer.cornerRadius = viewOuter.frame.size.width / 2
        viewOuter.layer.masksToBounds = true

        //drop shadow
        self.viewOuter.layer.shadowColor = UIColor(red: 0.0, green: 0.0, blue: 0.0, alpha: 0.15).cgColor
        //UIColor.AppTheme_ShadowColor_0000003E.cgColor
        
        self.viewOuter.layer.shadowOpacity = 0.8
        self.viewOuter.layer.shadowRadius = 3.0
        self.viewOuter.layer.shadowOffset = CGSize(width: 1.0, height: 3.0)
        self.viewOuter.clipsToBounds = true
        self.viewOuter.layer.masksToBounds = false
    }

    override func awakeFromNib() {
        super.awakeFromNib()
        self.lblTitle.text = "Ad".localized
    }
    
    func isAppadvertisement(value: Bool) {
        self.viewBookmark.isHidden = value
    }

}
