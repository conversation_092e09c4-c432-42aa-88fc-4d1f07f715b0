//
//  AdvertisingCollectionViewCell.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 02/01/2024.
//  Copyright © 2024 SOTSYS203. All rights reserved.
//

import UIKit
import AACarousel

class AdvertisingCollectionViewCell: UICollectionViewCell, AACarouselDelegate {
    
    // MARK: - IBOutlets
    @IBOutlet weak var imageSlideshow: AACarousel!
    @IBOutlet weak var lblTitle: MaterialLocalizeLable!
    @IBOutlet weak var viewBookmark: UIView!
    
    // MARK: - Variables
    var titleArray = [String]()
    var adsImagesArr = [String]()
    
    // MARK: - Func
    override func awakeFromNib() {
        super.awakeFromNib()
        self.lblTitle.text = "Ad".localized
        self.setDefaultShadow()
    }
    
    //require method
    func downloadImages(_ url: String, _ index:Int) {
        var imageView = UIImageView()
        imageView.image = UIImage(named: "logo_login")
        imageView.kf.indicatorType = .activity
        imageView.kf.setImage(
            with: URL(string: url),
            placeholder: UII<PERSON>(named: "logo_login"),
            options: [
                .scaleFactor(UIScreen.main.scale),
                .transition(.fade(1)),
                .cacheOriginalImage
            ])
        {
            result in
            switch result {
            case .success(let value):
                print("Task done for: \(value.source.url?.absoluteString ?? "")")
                self.imageSlideshow.images[index] = value.image
            case .failure(let error):
                print("Job failed: \(error.localizedDescription)")
            }
        }
    }
    
    func setData(adsImages: [String]) {
        imageSlideshow.delegate = self
        imageSlideshow.setCarouselData(paths: adsImages,  describedTitle: [], isAutoScroll: true, timer: 2.5, defaultImage: "logo_login")
        //optional method
        imageSlideshow.setCarouselOpaque(layer: false, describedTitle: false, pageIndicator: false)
        imageSlideshow.setCarouselLayout(displayStyle: 0, pageIndicatorPositon: 2, pageIndicatorColor: nil, describedTitleColor: nil, layerColor: nil)
    }
    
    //optional method (interaction for touch image)
    func didSelectCarouselView(_ view: AACarousel ,_ index: Int) {
        //        let alert = UIAlertView.init(title:"Alert" , message: titleArray[index], delegate: self, cancelButtonTitle: "OK")
        //        alert.show()
    }
    
    //optional method (show first image faster during downloading of all images)
    func callBackFirstDisplayView(_ imageView: UIImageView, _ url: [String], _ index: Int) {
        imageView.kf.setImage(with: URL(string: url[index]), placeholder: UIImage.init(named: "logo_login"), options: [.transition(.fade(1))], progressBlock: nil, completionHandler: nil)
    }
    
    func startAutoScroll() {
        //optional method
        imageSlideshow.startScrollImageView()
    }
    
    func stopAutoScroll() {
        //optional method
        imageSlideshow.stopScrollImageView()
    }
    
}
