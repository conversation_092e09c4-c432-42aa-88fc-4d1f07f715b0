<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="21701" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21678"/>
        <capability name="Named colors" minToolsVersion="9.0"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="LoewNextArabic-Bold.ttf">
            <string>LoewNextArabic-Bold</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" id="gTV-IL-0wX" customClass="AdvertisingCollectionViewCell" customModule="TopCustomer" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="174" height="195"/>
            <autoresizingMask key="autoresizingMask"/>
            <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center">
                <rect key="frame" x="0.0" y="0.0" width="174" height="195"/>
                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="XSH-UM-YMS">
                        <rect key="frame" x="5" y="5" width="164" height="185"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="pib-Qj-dlh" customClass="AACarousel" customModule="AACarousel">
                                <rect key="frame" x="10" y="10" width="144" height="165"/>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="kD3-FW-48i">
                                <rect key="frame" x="0.0" y="20" width="30" height="18"/>
                                <subviews>
                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="anv-3j-OiG" customClass="MaterialLocalizeImageView" customModule="TopCustomer" customModuleProvider="target">
                                        <rect key="frame" x="0.0" y="0.0" width="30" height="18"/>
                                    </imageView>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Ad" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="mpk-6w-X20" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                        <rect key="frame" x="8.3333333333333321" y="4.6666666666666679" width="13.333333333333332" height="9"/>
                                        <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="9"/>
                                        <color key="textColor" name="AppTheme_StrikeThroughColor_#707070"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="anv-3j-OiG" firstAttribute="top" secondItem="kD3-FW-48i" secondAttribute="top" id="9MK-Tz-ssQ"/>
                                    <constraint firstItem="anv-3j-OiG" firstAttribute="leading" secondItem="kD3-FW-48i" secondAttribute="leading" id="Kcy-mu-jPf"/>
                                    <constraint firstItem="mpk-6w-X20" firstAttribute="centerY" secondItem="kD3-FW-48i" secondAttribute="centerY" id="La4-Xf-PMh"/>
                                    <constraint firstAttribute="height" constant="18" id="YuC-z8-lJ6"/>
                                    <constraint firstAttribute="bottom" secondItem="anv-3j-OiG" secondAttribute="bottom" id="lKy-A7-Df6"/>
                                    <constraint firstAttribute="width" constant="30" id="usG-jG-Idw"/>
                                    <constraint firstAttribute="trailing" secondItem="anv-3j-OiG" secondAttribute="trailing" id="vtQ-O0-2JK"/>
                                    <constraint firstItem="mpk-6w-X20" firstAttribute="centerX" secondItem="kD3-FW-48i" secondAttribute="centerX" id="vzk-5c-fnK"/>
                                </constraints>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                        <real key="value" value="3"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                                        <real key="value" value="0.5"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                                        <color key="value" name="AppTheme_LightGrayColor_#A0A0A0"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </view>
                        </subviews>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstAttribute="trailing" secondItem="pib-Qj-dlh" secondAttribute="trailing" constant="10" id="XDs-If-KNY"/>
                            <constraint firstItem="kD3-FW-48i" firstAttribute="top" secondItem="XSH-UM-YMS" secondAttribute="top" constant="20" id="Xip-19-y7Y"/>
                            <constraint firstItem="pib-Qj-dlh" firstAttribute="top" secondItem="XSH-UM-YMS" secondAttribute="top" constant="10" id="iFu-Yv-wSx"/>
                            <constraint firstItem="pib-Qj-dlh" firstAttribute="leading" secondItem="XSH-UM-YMS" secondAttribute="leading" constant="10" id="lVF-HF-TZa"/>
                            <constraint firstAttribute="bottom" secondItem="pib-Qj-dlh" secondAttribute="bottom" constant="10" id="mST-Wf-hzG"/>
                            <constraint firstItem="kD3-FW-48i" firstAttribute="leading" secondItem="XSH-UM-YMS" secondAttribute="leading" id="wao-LW-TEd"/>
                        </constraints>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                <real key="value" value="7"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </view>
                </subviews>
            </view>
            <viewLayoutGuide key="safeArea" id="ZTg-uK-7eu"/>
            <constraints>
                <constraint firstAttribute="trailing" secondItem="XSH-UM-YMS" secondAttribute="trailing" constant="5" id="BLy-hl-04M"/>
                <constraint firstAttribute="bottom" secondItem="XSH-UM-YMS" secondAttribute="bottom" constant="5" id="UjT-fx-O2I"/>
                <constraint firstItem="XSH-UM-YMS" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" constant="5" id="VNT-RW-5Km"/>
                <constraint firstItem="XSH-UM-YMS" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" constant="5" id="Xgg-03-GLV"/>
            </constraints>
            <connections>
                <outlet property="imageSlideshow" destination="pib-Qj-dlh" id="4ZO-e9-Um8"/>
                <outlet property="lblTitle" destination="mpk-6w-X20" id="W41-4D-Vv5"/>
                <outlet property="viewBookmark" destination="kD3-FW-48i" id="xuU-Th-c0y"/>
            </connections>
            <point key="canvasLocation" x="61.068702290076331" y="20.774647887323944"/>
        </collectionViewCell>
    </objects>
    <resources>
        <namedColor name="AppTheme_LightGrayColor_#A0A0A0">
            <color red="0.62745098039215685" green="0.62745098039215685" blue="0.62745098039215685" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="AppTheme_StrikeThroughColor_#707070">
            <color red="0.4392156862745098" green="0.4392156862745098" blue="0.4392156862745098" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
