
import UIKit

class CategoriesCollectionViewCell: UICollectionViewCell {
    
    //MARK: - Outlets
    @IBOutlet weak var imgCategory: UIImageView!
    @IBOutlet weak var lblCategoryName: UILabel!
    @IBOutlet weak var viewOuter: UIView!
    @IBOutlet weak var lblTitle: MaterialLocalizeLable!
    @IBOutlet weak var viewBookmark: UIView!
    
    // MARK: - Func
    override func draw(_ rect: CGRect) {
        super.draw(rect)
        viewOuter.layer.cornerRadius = viewOuter.frame.size.width / 2
        //drop shadow
        self.viewOuter.layer.shadowColor = UIColor.lightGray.cgColor //UIColor(red: 0.0, green: 0.0, blue: 0.0, alpha: 0.15).cgColor
        //UIColor.AppTheme_ShadowColor_0000003E.cgColor
        self.viewOuter.layer.shadowOpacity = 0.8
        self.viewOuter.layer.shadowRadius = 3.0
        self.viewOuter.layer.shadowOffset = CGSize(width: 1.0, height: 3.0)
        self.viewOuter.clipsToBounds = true
        self.viewOuter.layer.masksToBounds = false
    }
    
    override func awakeFromNib() {
        super.awakeFromNib()
        viewOuter.layer.masksToBounds = true
        self.lblTitle.text = "Ad".localized
        self.isSelected == true ? setSelected() : setUnselected()
    }
    
    override var isSelected: Bool {
        didSet {
            self.isSelected == true ? setSelected() : setUnselected()
        }
    }
    
    func isAppadvertisement(value: Bool) {
        self.viewBookmark.isHidden = value
    }
    
    // new changes
    func setSelected() {  
        // show category selected
        // apply blue border for selection
        self.viewOuter.layer.borderWidth = 1.0
        self.viewOuter.layer.borderColor = UIColor.AppTheme_BlueColor_012CDA.cgColor
    }
    
    func setUnselected() {
        self.viewOuter.layer.borderWidth = 0.0
        self.viewOuter.layer.borderColor = UIColor.clear.cgColor
    }
    
}
