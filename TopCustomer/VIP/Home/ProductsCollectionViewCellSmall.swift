
import UIKit

class ProductsCollectionViewCellSmall: UICollectionViewCell {

    //MARK: - Outlets
    
    @IBOutlet weak var imgProduct: UIImageView!
    @IBOutlet weak var lblProductName: UILabel!
    @IBOutlet weak var lblPrice: UILabel!
    @IBOutlet weak var lblPriceTitle: UILabel!
    @IBOutlet weak var viewLowQuantity: UIView!
    @IBOutlet weak var lblLowQuantity: UILabel!
    @IBOutlet weak var lblOldPrice: UILabel!
    @IBOutlet weak var lblOffer: UILabel!
    @IBOutlet weak var viewBG: UIView!
    @IBOutlet weak var viewOutOfStock: UIView!
    @IBOutlet weak var btnAddToCart: UIButton!
    @IBOutlet weak var viewOfferTitle: UIView!
    @IBOutlet weak var lblOfferTitle: UILabel!
    @IBOutlet weak var lblTitle: MaterialLocalizeLable!
    @IBOutlet weak var viewBookmark: UIView!
    @IBOutlet weak var viewQuantitiesDiscount: UIView!
    @IBOutlet weak var lblQuantitiesDiscount: MaterialLocalizeLable!
    @IBOutlet weak var stackViewDiscountPrice: UIStackView!
    @IBOutlet weak var lblQuantitiesDiscountPrice: MaterialLocalizeLable!
    
    override func awakeFromNib() {
        super.awakeFromNib()
        lblLowQuantity.text = ObjKeymessages.kLABEL_LOW_QUANTITY
        self.lblTitle.text = "Ad".localized
        self.lblQuantitiesDiscount.text = "quantities_discount".localized
        viewLowQuantity.clipsToBounds = true
        viewLowQuantity.layer.cornerRadius = 8
        if UserDefaults.standard.getLanguage()! == UserAPI.VLanguage_userLanguage.en.rawValue {       // english
            viewLowQuantity.layer.maskedCorners = [.layerMaxXMinYCorner, .layerMaxXMaxYCorner]
            self.lblQuantitiesDiscount?.font = UIFont(name: Fonts.LoewNextArabicBold, size: 7)
        }
        else {   // arabic
            viewLowQuantity.layer.maskedCorners = [.layerMinXMinYCorner, .layerMinXMaxYCorner]
            self.lblQuantitiesDiscount?.font = UIFont(name: Fonts.LoewNextArabicBold, size: 10)
        }

        let attributeString: NSMutableAttributedString = NSMutableAttributedString(string: lblOldPrice.text ?? "")
        attributeString.addAttribute(NSAttributedString.Key.strikethroughStyle, value: NSUnderlineStyle.single.rawValue, range: NSRange(location: 0, length: lblOldPrice.text?.length ?? 0))
        attributeString.addAttribute(NSAttributedString.Key.strikethroughColor, value: UIColor.AppTheme_StrikeThroughColor_707070, range: NSRange(location: 0, length: lblOldPrice.text?.length ?? 0))
//        lblOldPrice.attributedText = attributeString
        
        // border radius
        self.layer.cornerRadius = 7.0

        // border
        self.layer.borderColor = UIColor.clear.cgColor
        self.layer.borderWidth = 0.0

        //drop shadow
        self.layer.shadowColor = UIColor(red: 0.0, green: 0.0, blue: 0.0, alpha: 0.15).cgColor
        //UIColor.AppTheme_ShadowColor_0000003E.cgColor
        
        
        self.layer.shadowOpacity = 0.8
        self.layer.shadowRadius = 3.0
        self.layer.shadowOffset = CGSize(width: 1.0, height: 1.0)
        self.clipsToBounds = true
        self.layer.masksToBounds = false

    }

    func isAppadvertisement(value: Bool) {
        self.viewBookmark.isHidden = value
    }
    
    func isQuantitiesDiscountHidden(value: Bool) {
        self.viewQuantitiesDiscount.isHidden = value
    }
    
}
