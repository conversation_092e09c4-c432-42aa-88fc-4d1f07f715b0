<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="22505" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22504"/>
        <capability name="Named colors" minToolsVersion="9.0"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="LoewNextArabic-Bold.ttf">
            <string>LoewNextArabic-Bold</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" id="gTV-IL-0wX" customClass="CategoriesCollectionViewCellBig" customModule="TopCustomer" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="84" height="117"/>
            <autoresizingMask key="autoresizingMask"/>
            <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center">
                <rect key="frame" x="0.0" y="0.0" width="84" height="117"/>
                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="s2i-V0-yUD">
                        <rect key="frame" x="5" y="8" width="74" height="74"/>
                        <subviews>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="center" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="dMm-0h-JeS">
                                <rect key="frame" x="15" y="15" width="44" height="44"/>
                                <constraints>
                                    <constraint firstAttribute="width" secondItem="dMm-0h-JeS" secondAttribute="height" multiplier="1:1" id="C33-0N-9eU"/>
                                </constraints>
                            </imageView>
                        </subviews>
                        <color key="backgroundColor" name="AppTheme_LightGrayOrderColor_#EFEFEF"/>
                        <constraints>
                            <constraint firstAttribute="bottom" secondItem="dMm-0h-JeS" secondAttribute="bottom" constant="15" id="3ag-eu-0zE"/>
                            <constraint firstAttribute="width" constant="74" id="7NO-oq-BWS"/>
                            <constraint firstItem="dMm-0h-JeS" firstAttribute="top" secondItem="s2i-V0-yUD" secondAttribute="top" constant="15" id="a7p-ce-9kT"/>
                            <constraint firstAttribute="trailing" secondItem="dMm-0h-JeS" secondAttribute="trailing" constant="15" id="eBT-mD-nwD"/>
                            <constraint firstItem="dMm-0h-JeS" firstAttribute="leading" secondItem="s2i-V0-yUD" secondAttribute="leading" constant="15" id="f09-pY-MZY"/>
                            <constraint firstAttribute="height" constant="74" id="n41-wX-lah"/>
                            <constraint firstAttribute="width" secondItem="s2i-V0-yUD" secondAttribute="height" multiplier="1:1" id="wIf-We-Dyl"/>
                        </constraints>
                    </view>
                    <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Nl5-Pc-ix3">
                        <rect key="frame" x="0.0" y="16" width="30" height="18"/>
                        <subviews>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="c4V-KS-kZK" customClass="MaterialLocalizeImageView" customModule="TopCustomer" customModuleProvider="target">
                                <rect key="frame" x="0.0" y="0.0" width="30" height="18"/>
                            </imageView>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Ad" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="oMQ-pf-lGu" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                <rect key="frame" x="8.5" y="4.5" width="13.5" height="9"/>
                                <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="9"/>
                                <color key="textColor" name="AppTheme_DiffBlackColor_#101113"/>
                                <nil key="highlightedColor"/>
                            </label>
                        </subviews>
                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstAttribute="width" constant="30" id="EuF-P3-6rR"/>
                            <constraint firstAttribute="bottom" secondItem="c4V-KS-kZK" secondAttribute="bottom" id="Hf4-RE-8qr"/>
                            <constraint firstItem="c4V-KS-kZK" firstAttribute="top" secondItem="Nl5-Pc-ix3" secondAttribute="top" id="Qcl-Fo-9bG"/>
                            <constraint firstItem="oMQ-pf-lGu" firstAttribute="centerX" secondItem="Nl5-Pc-ix3" secondAttribute="centerX" id="oeX-Le-UOD"/>
                            <constraint firstItem="oMQ-pf-lGu" firstAttribute="centerY" secondItem="Nl5-Pc-ix3" secondAttribute="centerY" id="pFF-9P-5kH"/>
                            <constraint firstAttribute="height" constant="18" id="pKL-W4-NBL"/>
                            <constraint firstAttribute="trailing" secondItem="c4V-KS-kZK" secondAttribute="trailing" id="sAC-mr-mox"/>
                            <constraint firstItem="c4V-KS-kZK" firstAttribute="leading" secondItem="Nl5-Pc-ix3" secondAttribute="leading" id="xnu-fN-Qrz"/>
                        </constraints>
                    </view>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Still Water" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="xb7-YM-FTA">
                        <rect key="frame" x="0.0" y="92" width="84" height="20"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="20" id="f3Y-wh-Jv5"/>
                        </constraints>
                        <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="10"/>
                        <color key="textColor" name="AppTheme_DiffBlackColor_#101113"/>
                        <nil key="highlightedColor"/>
                    </label>
                </subviews>
            </view>
            <viewLayoutGuide key="safeArea" id="ZTg-uK-7eu"/>
            <constraints>
                <constraint firstItem="xb7-YM-FTA" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" id="NTB-bd-wzz"/>
                <constraint firstAttribute="bottom" secondItem="xb7-YM-FTA" secondAttribute="bottom" constant="5" id="Npq-Hg-PM3"/>
                <constraint firstAttribute="trailing" secondItem="xb7-YM-FTA" secondAttribute="trailing" id="TJ7-ku-Vgu"/>
                <constraint firstItem="s2i-V0-yUD" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" constant="8" id="YA6-Bv-wFj"/>
                <constraint firstItem="xb7-YM-FTA" firstAttribute="top" secondItem="s2i-V0-yUD" secondAttribute="bottom" constant="10" id="c2C-xR-9fD"/>
                <constraint firstItem="s2i-V0-yUD" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" constant="5" id="duH-aD-4Mg"/>
                <constraint firstItem="Nl5-Pc-ix3" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" constant="16" id="eqh-4l-m2n"/>
                <constraint firstItem="Nl5-Pc-ix3" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" id="rBt-aX-nNB"/>
                <constraint firstAttribute="trailing" secondItem="s2i-V0-yUD" secondAttribute="trailing" constant="5" id="ue1-Zr-78C"/>
            </constraints>
            <size key="customSize" width="105" height="183"/>
            <connections>
                <outlet property="imgCategory" destination="dMm-0h-JeS" id="9ps-Bl-XNL"/>
                <outlet property="lblCategoryName" destination="xb7-YM-FTA" id="oaZ-et-Hhb"/>
                <outlet property="lblTitle" destination="oMQ-pf-lGu" id="bxW-Tq-ytA"/>
                <outlet property="viewBookmark" destination="Nl5-Pc-ix3" id="TPZ-iT-osH"/>
                <outlet property="viewOuter" destination="s2i-V0-yUD" id="W4M-Rz-s50"/>
            </connections>
            <point key="canvasLocation" x="171.01449275362319" y="130.24553571428572"/>
        </collectionViewCell>
    </objects>
    <resources>
        <namedColor name="AppTheme_DiffBlackColor_#101113">
            <color red="0.062745098039215685" green="0.066666666666666666" blue="0.074509803921568626" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="AppTheme_LightGrayOrderColor_#EFEFEF">
            <color red="0.93725490196078431" green="0.93725490196078431" blue="0.93725490196078431" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
    </resources>
</document>
