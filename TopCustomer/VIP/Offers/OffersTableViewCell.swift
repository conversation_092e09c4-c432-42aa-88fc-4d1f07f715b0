
import UIKit

class OffersTableViewCell: UITableViewCell {

    // MARK: - IBOutlets
    @IBOutlet weak var imgOffer: UIImageView!
    @IBOutlet weak var lblOfferDescription: UILabel!
    @IBOutlet weak var lblProductOriginalAmount: UILabel!
    @IBOutlet weak var lblProductOfferedAmount: UILabel!
    
    // MARK: - Func
    override func awakeFromNib() {
        super.awakeFromNib()
        // Initialization code
        
        // border radius
        self.layer.cornerRadius = 7.0

        // border
        self.layer.borderColor = UIColor.clear.cgColor
        self.layer.borderWidth = 0.0

        //drop shadow
        self.layer.shadowColor = UIColor(red: 0.0, green: 0.0, blue: 0.0, alpha: 0.15).cgColor
        //UIColor.AppTheme_ShadowColor_0000003E.cgColor
        
        self.layer.shadowOpacity = 0.8
        self.layer.shadowRadius = 3.0
        self.layer.shadowOffset = CGSize(width: 1.0, height: 1.0)
        self.clipsToBounds = true
        self.layer.masksToBounds = false

    }

    override func setSelected(_ selected: <PERSON><PERSON>, animated: <PERSON><PERSON>) {
        super.setSelected(selected, animated: animated)

        // Configure the view for the selected state
    }

}
