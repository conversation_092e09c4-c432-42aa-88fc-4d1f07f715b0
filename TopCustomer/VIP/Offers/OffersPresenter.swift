
import UIKit

protocol OffersPresentationProtocol {
    func apiCallForGetOffers(offset : Int, flagLoader: Bool)
    func apiResponseGetOffers(response:OfferListResponse?,error:Error?,offset : Int)

}

class OffersPresenter: OffersPresentationProtocol {
    
    // MARK: Objects & Variables
    weak var viewControllerOffers: OffersProtocol?
    var interactorOffers: OffersInteractorProtocol?
    
    func apiCallForGetOffers(offset : Int, flagLoader: Bool) {
        interactorOffers?.apiCallForGetOffers(offset : offset, flagLoader: flagLoader)
    }

    func apiResponseGetOffers(response: OfferListResponse?, error: Error?, offset : Int) {
//        if let vc = self.viewControllerOffers as? BaseViewController {
//            DispatchQueue.main.async {
//                vc.endRefresing()
//            }
//        }
        if let error = error  {
            viewControllerOffers?.displayAlert(string: error.localizedDescription)
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            //            viewController?.displayAlert(strTitle: "", strMessage: KeyMessages.kInvalidateResponse)
            return
        }
        
        if code == APIINVALIDAUTHORIZATIONCODE401 {
//            AppSingletonObj.goToLoginScreen(message: response.responseMessage ?? "")
            AppSingletonObj.displaySessionExpiredAlert(strMsg: response.responseMessage ?? "")
            return
        }

        if code == APICODE400 {
            viewControllerOffers?.displayAlert(string: response.responseMessage ?? "")
            return
        }
        
        guard let model = response.responseData,code == APISUCCESSCODE200  else {
            return
        }
        self.viewControllerOffers?.refreshData(model: model, strMsg: response.responseMessage ?? "")
    }

}
