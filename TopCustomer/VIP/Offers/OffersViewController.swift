
import UIKit
import STTabbar

protocol OffersProtocol: AnyObject {
    func displayAlert(string:String)
    func refreshData(model : OfferListResponseFields, strMsg: String)
//    func displaySessionExpiredAlert(strMsg: String)

}

class OffersViewController: BaseViewController, OffersProtocol {

    // MARK: Objects & Variables
    var presenterOffers: OffersPresentationProtocol?

    // MARK: IBOutlets
    @IBOutlet weak var tblOffers: UITableView!
    @IBOutlet weak var lblTitle: UILabel!
    @IBOutlet weak var lblNoRecord: UILabel!

    private var totalCount: Int = 0
    private var offset: Int = 1

    var arrayOffers: [OfferResponseFields] = []

    // MARK: Object lifecycle
    /*
    override init(nibName nibNameOrNil: String?, bundle nibBundleOrNil: Bundle?) {
        super.init(nibName: nibNameOrNil, bundle: nibBundleOrNil)
        setup()
    }
    */
    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
        setup()
    }
    
    // MARK: Setup
    
    private func setup() {
        let viewController = self
        let interactor = OffersInteractor()
        let presenter = OffersPresenter()
        
        //View Controller will communicate with only presenter
        viewController.presenterOffers = presenter
        
        //Presenter will communicate with Interector and Viewcontroller
        presenter.viewControllerOffers = viewController
        presenter.interactorOffers = interactor
        
        //Interactor will communucate with only presenter.
        interactor.presenterOffers = presenter
    }
    
    
    // MARK: View lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        self.setTexts()
        lblNoRecord.isHidden = false
        addObservers()

    }
    
    func addObservers() {
        removeObservers()
        NotificationCenter.default.addObserver(self, selector: #selector(openOfferDetailScreen), name: NSNotification.Name(rawValue: "OpenOfferDetailFromDeepLink"), object: nil)

    }

    @objc func openOfferDetailScreen(_ notification: NSNotification) {
        if let strOfferId = (notification.object as? [String:Any]) {
            if let id = strOfferId["offerId"] as? String {
                print(id)
                
                DispatchQueue.main.async {
                    let vc = ProductPopupStoryboard.instantiate(OfferDetailsViewController.self)
                    vc.modalPresentationStyle = .overFullScreen
                    vc.modalTransitionStyle = .crossDissolve
                    vc.offerId = Int(id) ?? 0
                    vc.delegate = self
            //        vc.objOfferResponseFields = self.arrayOffers[indexPath.row]
//                    self.presentVC(vc)
                    self.topMostViewController?.presentVC(vc)
                }
            }
        }
    }

    func removeObservers() {
        NotificationCenter.default.removeObserver(self)
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        self.getOffers()
    }
    
    private func getOffers() {
        if AppSingletonObj.isConnectedToNetwork(){
            self.offset = 1
            self.presenterOffers?.apiCallForGetOffers(offset : offset, flagLoader: true)
        }
    }

    private func setTexts() {
        lblTitle.text = ObjKeymessages.kLABEL_OFFERS
        lblNoRecord.text = ObjKeymessages.kLABEL_NO_OFFERS
    }

    func displayAlert(string: String) {
//        self.showAlert(title: AppName, message: string)
        AppSingleton.shared.showCustomPopUpWithOkButton(strButtonTitle: ObjKeymessages.kLABEL_OK, strMessage: string, showOnTopVC: false) { (isOk) in
        }
    }

    func refreshData(model: OfferListResponseFields, strMsg: String) {
        
        self.totalCount = model.totalRecord ?? 0

        // emtpy the array if it is initial call
        if self.offset == 1 {
            self.arrayOffers.removeAll()
        }
        self.arrayOffers.append(contentsOf: model.offers ?? [])

        lblNoRecord.text = strMsg
        
        if arrayOffers.count == 0 {
            lblNoRecord.isHidden = false
        }
        else {
            lblNoRecord.isHidden = true
        }
        tblOffers.reloadData()
    }

    /*func displaySessionExpiredAlert(strMsg: String) {
        let alert = UIAlertController(title: AppName, message: strMsg, preferredStyle: .alert)
        let okButton = UIAlertAction(title: ObjKeymessages.kLABEL_OK, style: .default, handler: {(_ action: UIAlertAction) -> Void in
            AppDel?.restartApp()
            self.dismiss(animated: true, completion: nil)
        })
        alert.addAction(okButton)
        self.present(alert, animated: true) {() -> Void in }
    }*/

}

extension OffersViewController : UITableViewDelegate, UITableViewDataSource{
    func numberOfSections(in tableView: UITableView) -> Int {
        return 1
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return self.arrayOffers.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tblOffers.dequeueReusableCell(withIdentifier: "OffersTableViewCell") as! OffersTableViewCell
        cell.imgOffer.kf.indicatorType = .activity
        let url = URL(string: arrayOffers[indexPath.row].vOfferImage ?? "")
        cell.imgOffer.kf.setImage(with: url , placeholder: UIImage(named: "placeholder_banner"))
        cell.lblOfferDescription.text = arrayOffers[indexPath.row].txOfferDescription ?? ""
        cell.lblProductOriginalAmount.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(arrayOffers[indexPath.row].productOfferedAmount ?? "0.0")")
        let discount = "\(arrayOffers[indexPath.row].productOriginalAmount ?? "0.0")"
        cell.lblProductOfferedAmount.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: discount, isDiscounted: true)
        return cell
    }
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        let vc = ProductPopupStoryboard.instantiate(OfferDetailsViewController.self)
        vc.modalPresentationStyle = .overFullScreen
        vc.modalTransitionStyle = .crossDissolve
        vc.offerId = self.arrayOffers[indexPath.row].iOfferId ?? 0
        vc.delegate = self
//        vc.objOfferResponseFields = self.arrayOffers[indexPath.row]
        self.presentVC(vc)
    }
    
    func tableView(_ tableView: UITableView, willDisplay cell: UITableViewCell, forRowAt indexPath: IndexPath) {
        if indexPath.row == (self.arrayOffers.count ) - 1  {
            if (self.arrayOffers.count ) < totalCount {
                offset = offset + 1
                
                if AppSingletonObj.isConnectedToNetwork(){
                    self.presenterOffers?.apiCallForGetOffers(offset : offset, flagLoader: true)
                }
            }
        }
    }

}

extension OffersViewController : UpdateCountFromOfferProtocol {
    
    func updateCartCountFromOffer(cartCount:Int?) {
        // update my cart button count
        if let myTabbar = self.tabBarController?.tabBar as? STTabbar {
            if cartCount ?? 0 <= 0 {
                myTabbar.label?.isHidden = true
            }
            else {
                myTabbar.label?.isHidden = false
                myTabbar.label?.text = "\(cartCount ?? 0)"
                self.vibrationDevice()
            }
        }

    }
}

