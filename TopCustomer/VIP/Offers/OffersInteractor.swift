
import UIKit

protocol OffersInteractorProtocol {
    func apiCallForGetOffers(offset : Int, flagLoader: Bool)

}

protocol OffersDataStore {
    //{ get set }
}

class OffersInteractor: OffersInteractorProtocol, OffersDataStore {

    // MARK: Objects & Variables
    var presenterOffers: OffersPresentationProtocol?
    
    func apiCallForGetOffers(offset : Int, flagLoader: Bool) {
        
        if flagLoader == true {
            ActivityIndicator.shared.showCentralSpinner()
        }
        
        let authorization = getAuthorizationText()
        
        OfferAPI.offerListing(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, offset: offset) { data, error in
            
            ActivityIndicator.shared.hideCentralSpinner()
            self.presenterOffers?.apiResponseGetOffers(response: data, error: error, offset : offset)
        }
        
    }

}
