
import UIKit

protocol OfferDetailsPresentationProtocol {
    func apiCallAddToCart(dictData:[String:Any])
    func apiResponseAddToCart(response:AddToCartResponse?,error:Error?)

    func apiCallForGetOfferDetails(dictData:[String:Any])
    func apiResponseGetOfferDetails(response:OfferResponse?,error:Error?)

}

class OfferDetailsPresenter: OfferDetailsPresentationProtocol {
    
    // MARK: Objects & Variables
    weak var viewControllerOfferDetails: OfferDetailsProtocol?
    var interactorOfferDetails: OfferDetailsInteractorProtocol?
    
    func apiCallAddToCart(dictData:[String:Any]) {
        self.interactorOfferDetails?.apiCallAddToCart(dictData: dictData)
    }

    func apiResponseAddToCart(response:AddToCartResponse?,error:Error?) {
        if let error = error  {
            viewControllerOfferDetails?.displayAlert(string: error.localizedDescription)
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            return
        }
        
        if code == APIINVALIDAUTHORIZATIONCODE401 {
            AppSingletonObj.displaySessionExpiredAlert(strMsg: response.responseMessage ?? "")
            return
        }

        if code == APICODE400 {
            viewControllerOfferDetails?.displayAlert(string: response.responseMessage ?? "")
            return
        }
        
        if code == APICODE203 {
            self.viewControllerOfferDetails?.displayErrorAlert(strMsg: response.responseMessage ?? "")
            return
        }

        guard let model = response.responseData,code == APISUCCESSCODE200  else {
            return
        }
        self.viewControllerOfferDetails?.displayAlertAndCloseScreen(strMsg: response.responseMessage ?? "", cartCount: model.getCartProducts ?? 0)
    }

    func apiCallForGetOfferDetails(dictData:[String:Any]) {
        interactorOfferDetails?.apiCallForGetOfferDetails(dictData: dictData)
    }

    func apiResponseGetOfferDetails(response: OfferResponse?, error: Error?) {
        if let error = error  {
            viewControllerOfferDetails?.displayAlert(string: error.localizedDescription)
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            return
        }
        
        if code == APIINVALIDAUTHORIZATIONCODE401 {
            AppSingletonObj.displaySessionExpiredAlert(strMsg: response.responseMessage ?? "")
            return
        }

        if code == APICODE400 {
            viewControllerOfferDetails?.displayErrorAlert(strMsg: response.responseMessage ?? "")
            return
        }
        
        guard let model = response.responseData,code == APISUCCESSCODE200  else {
            return
        }
        self.viewControllerOfferDetails?.getOfferDetailsInfo(model: model)
    }

}
