
import UIKit
import STTabbar

protocol OfferDetailsProtocol: AnyObject {
    func displayAlertAndCloseScreen(strMsg: String, cartCount: Int)
    func getOfferDetailsInfo(model : OfferResponseFields)
    func displayAlert(string:String)
    func displayErrorAlert(strMsg:String)

}

protocol UpdateCountFromOfferProtocol {
    func updateCartCountFromOffer(cartCount:Int?)
}

class OfferDetailsViewController: BottomPopupViewController, OfferDetailsProtocol {

    // MARK: Objects & Variables
    var presenterOfferDetails: OfferDetailsPresentationProtocol?

    // MARK: IBOutlets
    @IBOutlet weak var scrollView: UIScrollView!
    @IBOutlet weak var btnQty: QTYButton!
    @IBOutlet weak var lblTitle: UILabel!
    @IBOutlet weak var lblProductSkuTitle: UILabel!
    @IBOutlet weak var lblProductDesc: UILabel!
    @IBOutlet weak var lblQuantityTitle: UILabel!
    @IBOutlet weak var btnAddToCart: CustomRoundedButtton!
    @IBOutlet weak var lblProductSku: UILabel!
    @IBOutlet weak var lblProductName: UILabel!
    @IBOutlet weak var imgOffer: UIImageView!
    @IBOutlet weak var lblPrice: MaterialLocalizeLable!

    
    var objOfferResponseFields: OfferResponseFields?
    var offerId = 0
    var delegate : UpdateCountFromOfferProtocol?

    // MARK: Object lifecycle
    /*
    override init(nibName nibNameOrNil: String?, bundle nibBundleOrNil: Bundle?) {
        super.init(nibName: nibNameOrNil, bundle: nibBundleOrNil)
        setup()
    }
    */
    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
        setup()
    }
    
    // MARK: Setup
    
    private func setup() {
        let viewController = self
        let interactor = OfferDetailsInteractor()
        let presenter = OfferDetailsPresenter()
        
        //View Controller will communicate with only presenter
        viewController.presenterOfferDetails = presenter
        
        //Presenter will communicate with Interector and Viewcontroller
        presenter.viewControllerOfferDetails = viewController
        presenter.interactorOfferDetails = interactor
        
        //Interactor will communucate with only presenter.
        interactor.presenterOfferDetails = presenter
    }
    
    
    // MARK: View lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        self.setTexts()
        btnQty.textfield.text = "1"
        self.getOfferDetails()
        delay(0.3) {
            self.updatePopupHeight(to: min(CGFloat((self.scrollView.contentSize.height) + 15 + (UIApplication.shared.windows.first?.safeAreaInsets.bottom ?? 0)), UIScreen.main.bounds.height * 0.95))
        }
    }
    
    private func getOfferDetails() {
        var dictParam : [String:Any] = [:]
        dictParam["iOfferId"] = offerId

        if AppSingletonObj.isConnectedToNetwork(){
            self.presenterOfferDetails?.apiCallForGetOfferDetails(dictData: dictParam)
        }
    }
    
    private func setTexts() {
        lblTitle.text = ObjKeymessages.kLABEL_OFFER
        lblProductSkuTitle.text = ObjKeymessages.kLABEL_PRODUCT_SKU
        lblQuantityTitle.text = ObjKeymessages.kLABEL_QUANTITY
        btnAddToCart.setTitle(ObjKeymessages.kLABEL_ADD_TO_CART, for: .normal)

    }
    
    @IBAction func btnDimViewAction(_ sender: Any) {
        self.view.endEditing(true)
        self.dismiss(animated: true, completion: nil)
    }

    @IBAction func addToCartAction(_ sender: Any) {
        self.view.endEditing(true)
        if AppSingletonObj.isConnectedToNetwork(){
            
            let quantity = Int(self.btnQty.textfield.text ?? "") ?? 0
            if quantity <= 0 {
                AppSingletonObj.showAlert(strMessage: ObjKeymessages.kMSG_VALID_QUANTITY)
                return
            }
            
            var dictParam : [String:Any] = [:]
            dictParam["biProductId"] = objOfferResponseFields?.biProductId
            dictParam["iProductQuantity"] = objOfferResponseFields?.iBuyProductQuantity //self.btnQty.textfield.text
            dictParam["dbPrice"] = objOfferResponseFields?.productOriginalAmount

            self.presenterOfferDetails?.apiCallAddToCart(dictData: dictParam)
        }
    }
    
    func displayErrorAlert(strMsg:String) {
        AppSingleton.shared.showCustomPopUpWithOkButton(strButtonTitle: ObjKeymessages.kLABEL_OK, strMessage: strMsg, showOnTopVC: true) { (isOk) in
            if isOk == true {
                self.dismissVC(completion: nil)
            }
        }
    }

    func displayAlertAndCloseScreen(strMsg: String, cartCount: Int) {
        // update my cart button count
        delegate?.updateCartCountFromOffer(cartCount: cartCount)

        self.vibrationDevice()
        AppSingletonObj.showAlert(strMessage: strMsg)
        self.dismissVC(completion: nil)
    }

    func displayAlert(string: String) {
        AppSingleton.shared.showCustomPopUpWithOkButton(strButtonTitle: ObjKeymessages.kLABEL_OK, strMessage: string, showOnTopVC: false) { (isOk) in
        }
    }

    func getOfferDetailsInfo(model : OfferResponseFields) {
        objOfferResponseFields = model
        self.setData()        
    }

    private func setData() {
        imgOffer.kf.indicatorType = .activity
        let url = URL(string: objOfferResponseFields?.vOfferImage ?? "")
        imgOffer.kf.setImage(with: url , placeholder: UIImage(named: "placeholder_banner"))
        lblProductSku.text = objOfferResponseFields?.vBuyProductSKU
        lblProductName.text = "\(objOfferResponseFields?.vBuyProductName ?? "") \(objOfferResponseFields?.productunit ?? "")"
        lblProductDesc.text = objOfferResponseFields?.txOfferDescription
        lblPrice.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\("Total offer price".localized): \(objOfferResponseFields?.productOfferedAmount ?? "")")
    }

    @IBAction func btnFullScreenAction(_ sender: Any) {
        let vc = ProductPopupStoryboard.instantiate(FullScreenImageViewController.self)
        vc.mediaImage = imgOffer.image
        vc.modalPresentationStyle = .overFullScreen
        vc.images = objOfferResponseFields?.imagesApp ?? []
        self.present(vc, animated: true)
    }

    @IBAction func shareAction(_ sender: Any) {
        FirebaseDeeplinkingHelper.shared.generateFeedPlanLinkForOffer(strOfferId: "\(offerId)", strTitle: objOfferResponseFields?.vOfferName ?? "", strDescription: objOfferResponseFields?.txOfferDescription ?? "", strImageUrl: objOfferResponseFields?.vOfferImage ?? "") { url in
            let strMsg = "\(self.objOfferResponseFields?.txOfferDescription ?? "") \(ObjKeymessages.kLABEL_SHARE_TEXT)\n\n\(url ?? "")"
            print(strMsg)
            self.shareTextInDefaultShareKit(array: [strMsg])
        }
    }
    
    func shareTextInDefaultShareKit(array:[Any],complation: (()->())? = nil) {
        let activityVC : UIActivityViewController = UIActivityViewController(activityItems: array, applicationActivities: nil)
        activityVC.completionWithItemsHandler = { (activityType,isCompleted,returnItems,error) in
            complation?()
        }
        self.presentVC(activityVC)
    }

}
