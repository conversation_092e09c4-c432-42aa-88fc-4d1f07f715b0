
import UIKit

protocol OfferDetailsInteractorProtocol {
    func apiCallAddToCart(dictData:[String:Any])
    func apiCallForGetOfferDetails(dictData:[String:Any])

}

protocol OfferDetailsDataStore {
    //{ get set }
}

class OfferDetailsInteractor: OfferDetailsInteractorProtocol, OfferDetailsDataStore {

    // MARK: Objects & Variables
    var presenterOfferDetails: OfferDetailsPresentationProtocol?
    
    func apiCallAddToCart(dictData:[String:Any]) {
        ActivityIndicator.shared.showCentralSpinner()
        
        let authorization = getAuthorizationText()

        CartAPI.addToCart(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, biProductId: Int(setDataInString(dictData["biProductId"] as AnyObject)) ?? 0,
                          tiIsCheck: AddToCartIsCheck.Add.rawValue, iProductQuantity: Int(setDataInString(dictData["iProductQuantity"] as AnyObject)) ?? 0,
                          dbPrice: setDataInString(dictData["dbPrice"] as AnyObject),
                          isMosques: setDataInString(dictData["isMosques"] as AnyObject)) { data, error in
            
            ActivityIndicator.shared.hideCentralSpinner()
            self.presenterOfferDetails?.apiResponseAddToCart(response: data, error: error)
        }
                          
    }

    func apiCallForGetOfferDetails(dictData:[String:Any]) {
        
        DispatchQueue.main.async {
            ActivityIndicator.shared.showCentralSpinner()
        }
        
        let authorization = getAuthorizationText()
        
        OfferAPI.offerDetails(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, iOfferId: Int(setDataInString(dictData["iOfferId"] as AnyObject)) ?? 0) { data, error in
            ActivityIndicator.shared.hideCentralSpinner()
            self.presenterOfferDetails?.apiResponseGetOfferDetails(response: data, error: error)
        }
        
    }

}
