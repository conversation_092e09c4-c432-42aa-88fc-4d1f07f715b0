
import Foundation
import UIKit

class FloatingButtonView: UIView {
    // MARK: - IBOutlets
    @IBOutlet weak var whatsAppButton: UIButton!
    
    // MARK: - Init
    override func awakeFromNib() {
        super.awakeFromNib()
    }
    
    @IBAction func openWhatsappBtnAction(_ sender: Any) {
        guard let whatsappURL = URL(string: "https://wa.me/+966554020703") else {return}
        
        if UIApplication.shared.canOpenURL(whatsappURL) {
            UIApplication.shared.open(whatsappURL, options: [:], completionHandler: nil)
        } else {
            AppSingleton.shared.showCustomPopUpWithOkButton(strButtonTitle: ObjKeymessages.kLABEL_OK, strMessage: "please_install_whatsapp".localized, showOnTopVC: false) { (isOk) in
            }
        }
    }
    
}
