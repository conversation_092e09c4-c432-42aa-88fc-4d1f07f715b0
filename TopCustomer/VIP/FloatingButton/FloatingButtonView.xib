<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="23504" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="23506"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" id="iN0-l3-epB" customClass="FloatingButtonView" customModule="TopCustomer" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="69" height="70"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <stackView opaque="NO" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="xFK-Yl-49Z">
                    <rect key="frame" x="0.0" y="0.0" width="69" height="70"/>
                    <subviews>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="RpP-7Z-lqx">
                            <rect key="frame" x="0.0" y="0.0" width="69" height="70"/>
                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                            <state key="normal" image="whatsapp_icon"/>
                            <connections>
                                <action selector="openWhatsappBtnAction:" destination="iN0-l3-epB" eventType="touchUpInside" id="yVT-EZ-HDr"/>
                            </connections>
                        </button>
                    </subviews>
                </stackView>
            </subviews>
            <viewLayoutGuide key="safeArea" id="vUN-kp-3ea"/>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstAttribute="trailing" secondItem="xFK-Yl-49Z" secondAttribute="trailing" id="cGQ-Kn-ROf"/>
                <constraint firstItem="xFK-Yl-49Z" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" id="cbN-lb-BmD"/>
                <constraint firstAttribute="bottom" secondItem="xFK-Yl-49Z" secondAttribute="bottom" id="sQm-nG-JAA"/>
                <constraint firstItem="xFK-Yl-49Z" firstAttribute="leading" secondItem="iN0-l3-epB" secondAttribute="leading" id="yeh-wZ-h4P"/>
            </constraints>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <connections>
                <outlet property="whatsAppButton" destination="RpP-7Z-lqx" id="qBN-ue-oe1"/>
            </connections>
            <point key="canvasLocation" x="-202.29007633587784" y="216.19718309859155"/>
        </view>
    </objects>
    <resources>
        <image name="whatsapp_icon" width="48" height="48"/>
    </resources>
</document>
