//
//  ViewInvoiceVC.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 25/02/2025.
//  Copyright © 2025 SOTSYS203. All rights reserved.
//

import UIKit
import WebKit

class ViewInvoiceVC: UIViewController, WKNavigationDelegate {
    
    var webView: WKWebView!
    var invoiceUrl: String = ""
    
    override func viewDidLoad() {
        super.viewDidLoad()
        // Do any additional setup after loading the view.
        self.view.backgroundColor = .white
        webView = WKWebView()
        webView.navigationDelegate = self
        view = webView
        webView.backgroundColor = .white
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        let url = URL(string: self.invoiceUrl)!
        webView.load(URLRequest(url: url))
        webView.allowsBackForwardNavigationGestures = true
        
    }
    
}
