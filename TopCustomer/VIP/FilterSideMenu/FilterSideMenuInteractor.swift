
import UIKit

protocol FilterSideMenuInteractorProtocol {
    func apiCallForGetCategories()

}

protocol FilterSideMenuDataStore {
    //{ get set }
}

class FilterSideMenuInteractor: FilterSideMenuInteractorProtocol, FilterSideMenuDataStore {

    // MARK: Objects & Variables
    var presenterFilterSideMenu: FilterSideMenuPresentationProtocol?
    
    func apiCallForGetCategories() {
        
        ActivityIndicator.shared.showCentralSpinner()
        
        let authorization = User.shared.checkUserLoginStatus() ? getAuthorizationText() : GuestAccessToken

        CategoryAPI.categoryListing(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang) { data, error in
            ActivityIndicator.shared.hideCentralSpinner()
            self.presenterFilterSideMenu?.apiResponseGetCategories(response: data, error: error)
        }
        
    }

}
