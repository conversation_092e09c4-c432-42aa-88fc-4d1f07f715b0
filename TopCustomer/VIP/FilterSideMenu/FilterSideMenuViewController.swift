
import UIKit

protocol FilterSideMenuProtocol: AnyObject {
    func displayAlert(string:String)
    func refreshData(arrCategory : [ListResponseFields])

}

class FilterSideMenuViewController: BaseViewController {

    // MARK: Objects & Variables
    var presenterFilterSideMenu: FilterSideMenuPresentationProtocol?

    // MARK: IBOutlets
    @IBOutlet weak var collVwProducts: UICollectionView!
    @IBOutlet weak var collVwProductsHeightConstraint: NSLayoutConstraint!
    @IBOutlet weak var outerView: UIView!
    @IBOutlet weak var lblCategories: UILabel!
    @IBOutlet weak var btnSelectCity: UIButton!
    
    var arrCategories: [ListResponseFields] = []
    var goToSearchProductScreen: ((Int, String)->(Void))?

    
    // MARK: Object lifecycle
    /*
    override init(nibName nibNameOrNil: String?, bundle nibBundleOrNil: Bundle?) {
        super.init(nibName: nibNameOrNil, bundle: nibBundleOrNil)
        setup()
    }
    */
    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
        setup()
    }
    
    // MARK: Setup
    
    private func setup() {
        let viewController = self
        let interactor = FilterSideMenuInteractor()
        let presenter = FilterSideMenuPresenter()
        
        //View Controller will communicate with only presenter
        viewController.presenterFilterSideMenu = presenter
        
        //Presenter will communicate with Interector and Viewcontroller
        presenter.viewControllerFilterSideMenu = viewController
        presenter.interactorFilterSideMenu = interactor
        
        //Interactor will communucate with only presenter.
        interactor.presenterFilterSideMenu = presenter
    }
    
    
    // MARK: View lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        lblCategories.text = ObjKeymessages.kLABEL_CATEGORIES
        // corner radius to view
        outerView.clipsToBounds = true
        outerView.layer.cornerRadius = 18
        
        let strLangCode = UserDefaults.standard.getLanguage()!
        if strLangCode == UserAPI.VLanguage_userLanguage.en.rawValue {
            outerView.layer.maskedCorners = [.layerMaxXMinYCorner, .layerMaxXMaxYCorner]
        }
        else {
            outerView.layer.maskedCorners = [.layerMinXMinYCorner, .layerMinXMaxYCorner]
        }
        
        self.getCategories()

    }
    
    private func getCategories() {
        if AppSingletonObj.isConnectedToNetwork(){
            self.presenterFilterSideMenu?.apiCallForGetCategories()
        }
    }

    func setupUI() {
        self.collVwProducts.registerCell(cell: CategoriesCollectionViewCellBig.self)
        self.collVwProducts.delegate = self
        self.collVwProducts.dataSource = self
        btnSelectCity.setTitle("select_city".localized, for: .normal)
    }

    @IBAction func btnDimViewAction(_ sender: Any) {
        self.dismiss(animated: true, completion: nil)
    }
    
    
    @IBAction func btnSelectCityAction(_ sender: UIButton) {
        
    }
    
}

extension FilterSideMenuViewController: UICollectionViewDelegate, UICollectionViewDataSource, UICollectionViewDelegateFlowLayout {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return arrCategories.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeue(with: CategoriesCollectionViewCellBig.self, for: indexPath)
        
        cell.imgCategory.contentMode = .scaleAspectFit
        cell.isAppadvertisement(value: arrCategories[indexPath.row].isAppAdvertisement == 1 ? false : true)
        cell.imgCategory.kf.indicatorType = .activity
        let url = URL(string: arrCategories[indexPath.row].vImage ?? "")
        cell.imgCategory.kf.setImage(with: url , placeholder: UIImage(named: "placeholder_category"))

        cell.lblCategoryName.text = arrCategories[indexPath.row].vName

        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        return CGSize(width: 74, height: 117)
    }
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        self.goToSearchProductScreen?(self.arrCategories[indexPath.row]._id ?? 0, self.arrCategories[indexPath.row].vName ?? "")
        self.dismiss(animated: true, completion: nil)
    }
    
    func collectionView(_ collectionView: UICollectionView, willDisplay cell: UICollectionViewCell, forItemAt indexPath: IndexPath) {
        if collectionView == self.collVwProducts {
            self.collVwProducts.layoutIfNeeded()
            self.collVwProductsHeightConstraint.constant = self.collVwProducts.contentSize.height
        }
    }
}

extension FilterSideMenuViewController: FilterSideMenuProtocol {
    func displayAlert(string: String) {
        AppSingleton.shared.showCustomPopUpWithOkButton(strButtonTitle: ObjKeymessages.kLABEL_OK, strMessage: string, showOnTopVC: false) { (isOk) in
        }
    }

    func refreshData(arrCategory: [ListResponseFields]) {
        arrCategories = arrCategory
        collVwProducts.reloadData()
    }

}
