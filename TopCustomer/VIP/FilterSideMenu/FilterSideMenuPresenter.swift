
import UIKit

protocol FilterSideMenuPresentationProtocol {
    func apiCallForGetCategories()
    func apiResponseGetCategories(response:ListResponse?,error:Error?)

}

class FilterSideMenuPresenter: FilterSideMenuPresentationProtocol {
    
    // MARK: Objects & Variables
    weak var viewControllerFilterSideMenu: FilterSideMenuProtocol?
    var interactorFilterSideMenu: FilterSideMenuInteractorProtocol?
 
    func apiCallForGetCategories() {
        interactorFilterSideMenu?.apiCallForGetCategories()
    }

    func apiResponseGetCategories(response: ListResponse?, error: Error?) {
        if let error = error  {
            viewControllerFilterSideMenu?.displayAlert(string: error.localizedDescription)
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            return
        }
        
        if code == APIINVALIDAUTHORIZATIONCODE401 {
            AppSingletonObj.goToLoginScreen(message: response.responseMessage ?? "")
            return
        }

        if code == APICODE400 {
            viewControllerFilterSideMenu?.displayAlert(string: response.responseMessage ?? "")
            return
        }
        
        guard let model = response.responseData,code == APISUCCESSCODE200  else {
            return
        }
        self.viewControllerFilterSideMenu?.refreshData(arrCategory: model)
    }

}
