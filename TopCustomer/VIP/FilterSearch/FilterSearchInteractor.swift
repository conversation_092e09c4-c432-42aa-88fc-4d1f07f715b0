
import UIKit

protocol FilterSearchInteractorProtocol {
    func apiCallForGetNewProducts(dictData:[String:Any], strWhichScreen: String, pagingOrnot: Bool, pageNo: Int, isOnlyBundle: Int)
    func apiCallForSearchSuggestions(dictData:[String:Any])
    func getUserSearch()
    func addUserSearch(searchtext: String)
    func deleteUserSearch(searchID: Int)
    func deleteAllUserSearch()
    func apiCallForAddToCart(dictData:[String:Any])
    func apiCallForGetSearchResult(dictData:[String:Any], strWhichScreen: String, pagingOrnot: Bool, pageNo: Int, isOnlyBundle: Int)
}

protocol FilterSearchDataStore {
    //{ get set }
}

class FilterSearchInteractor: FilterSearchInteractorProtocol, FilterSearchDataStore {

    // MARK: Objects & Variables
    var presenterFilterSearch: FilterSearchPresentationProtocol?
    
    func apiCallForGetNewProducts(dictData:[String:Any], strWhichScreen: String, pagingOrnot: Bool, pageNo: Int, isOnlyBundle: Int) {
        
        if pagingOrnot == true {  // from paging - do not show loader
        }
        else {
            DispatchQueue.main.async {
                ActivityIndicator.shared.showCentralSpinner()
            }
        }

        let authorization = User.shared.checkUserLoginStatus() ? getAuthorizationText() : GuestAccessToken

        var iProductType = ProductType.New.rawValue
        if strWhichScreen == "MoreBestSellers" {  // best seller
            iProductType = ProductType.BestSeller.rawValue
        }
        else {
            iProductType = ProductType.New.rawValue
        }
        
        ProductAPI.productListing(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, iProductType: iProductType, iCategoryId: Int(setDataInString(dictData["iCategoryId"] as AnyObject)) ?? 0, vSerchString: setDataInString(dictData["vSerchString"] as AnyObject), offset: Int(setDataInString(dictData["offset"] as AnyObject)) ?? 0, pageNo: pageNo, isOnlyBundle: isOnlyBundle) { data, error in
            ActivityIndicator.shared.hideCentralSpinner()
            self.presenterFilterSearch?.apiResponseGetNewProducts(response: data, error: error)
        }
        
        
    }
    
    func apiCallForGetSearchResult(dictData:[String:Any], strWhichScreen: String, pagingOrnot: Bool, pageNo: Int, isOnlyBundle: Int) {
        if pagingOrnot == true {  // from paging - do not show loader
        }
        else {
            DispatchQueue.main.async {
                ActivityIndicator.shared.showCentralSpinner()
            }
        }
        
        let authorization = User.shared.checkUserLoginStatus() ? getAuthorizationText() : GuestAccessToken
        
        var iProductType = ProductType.New.rawValue
        if strWhichScreen == "MoreBestSellers" {  // best seller
            iProductType = ProductType.BestSeller.rawValue
        }
        else {
            iProductType = ProductType.New.rawValue
        }
        
        ProductAPI.productSearchListing(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, iProductType: iProductType, iCategoryId: Int(setDataInString(dictData["iCategoryId"] as AnyObject)) ?? 0, vSerchString: setDataInString(dictData["vSerchString"] as AnyObject), offset: Int(setDataInString(dictData["offset"] as AnyObject)) ?? 0, pageNo: pageNo, isOnlyBundle: isOnlyBundle) { data, error in
            ActivityIndicator.shared.hideCentralSpinner()
            self.presenterFilterSearch?.apiResponseGetSearchResult(response: data, error: error)
        }
    }

    func apiCallForSearchSuggestions(dictData:[String:Any]) {
        
        let authorization = User.shared.checkUserLoginStatus() ? getAuthorizationText() : GuestAccessToken

        ProductAPI.productSuggestion(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, vProductName: setDataInString(dictData["vProductName"] as AnyObject), iCategoryId: Int(setDataInString(dictData["iCategoryId"] as AnyObject)) ?? 0) { data, error in
                self.presenterFilterSearch?.apiResponseForSearchSuggestions(response: data, error: error)
        }
        
    }
    
    func getUserSearch() {
        UserSearchAPI.getUserSearch { data, error in
            self.presenterFilterSearch?.apiResponseForGetUserSearch(response: data, error: error)
        }
    }

    func addUserSearch(searchtext: String) {
        UserSearchAPI.addUserSearch(searchtext: searchtext) { data, error in
            debugPrint("addUserSearch ===>", data)
        }
    }
    
    func deleteUserSearch(searchID: Int) {
        UserSearchAPI.deleteOneUserSearch(searchId: searchID) { data, error in
            self.presenterFilterSearch?.apiResponseForDeleteUserSearch(response: data, error: error)
        }
    }
    
    func deleteAllUserSearch() {
        UserSearchAPI.deleteAllUserSearch() { data, error in
            debugPrint("deleteAllUserSearch ===>", data)
            self.presenterFilterSearch?.apiResponseForDeleteUserSearch(response: data, error: error)
        }
    }
    
    func apiCallForAddToCart(dictData:[String:Any]) {
        ActivityIndicator.shared.showCentralSpinner()
        let authorization = getAuthorizationText()
        CartAPI.addToCart(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, biProductId: Int(setDataInString(dictData["biProductId"] as AnyObject)) ?? 0, tiIsCheck: Int(setDataInString(dictData["tiIsCheck"] as AnyObject)) ?? 0, iProductQuantity: Int(setDataInString(dictData["iProductQuantity"] as AnyObject)) ?? 0, dbPrice: setDataInString(dictData["dbPrice"] as AnyObject), dbBundleID: Int(setDataInString(dictData["bunddelId"] as AnyObject)) ?? 0) { data, error in
            
            ActivityIndicator.shared.hideCentralSpinner()
            self.presenterFilterSearch?.apiResponseAddToCart(response: data, error: error)
        }
    }
    
}
