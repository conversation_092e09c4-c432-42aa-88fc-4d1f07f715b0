
import UIKit
import <PERSON><PERSON>abbar
import Mixpanel
import Speech
import TikTokBusinessSDK
import FirebaseAnalytics

protocol FilterSearchProtocol: AnyObject {
    func displayAlert(string:String)
    func refreshNewProducts(model : ProductListResponseFields)
    func refreshSuggestions(arrSuggestionsTexts : [ProductSuggestionResponseFields])
    func getUserSearch(userSearch: [UserSearchResponseData])
    func addUserSearch(searchtext: String)
    func refreshUserSearch()
    func displayAlertAndDismiss(string:String, cartCount: Int)
}

protocol UpdateCountFromSearchProtocol {
    func updateCartCountFromSearch(cartCount:Int?)
}

class FilterSearchViewController: BaseViewController {

    // MARK: Objects & Variables
    var presenterFilterSearch: FilterSearchPresentationProtocol?

    // MARK: IBOutlets
    @IBOutlet weak var collectionView: UICollectionView!
    @IBOutlet weak var lblTitle: UILabel!
    @IBOutlet weak var searchBar: UISearchBar!
    @IBOutlet weak var lblNoData: UILabel!
    @IBOutlet weak var viewSearch: UIView!
    @IBOutlet weak var cons_height_searchView: NSLayoutConstraint!
    @IBOutlet weak var txtSearch: UITextField!
    @IBOutlet weak var cons_bottom_searchView: NSLayoutConstraint!
    @IBOutlet weak var activityIndicatorView: UIActivityIndicatorView!
    @IBOutlet weak var cons_activityIndicatorView_height: NSLayoutConstraint!
    @IBOutlet weak var stackViewActivityIndicatorView: UIStackView!
    @IBOutlet weak var tblSuggestions: UITableView!
    @IBOutlet weak var cons_height_tblSuggestions: NSLayoutConstraint!
    @IBOutlet weak var btnMic:UIButton!
    @IBOutlet weak var lblListening: MaterialLocalizeLable!
    @IBOutlet weak var stackViewRecentSearches: UIStackView!
    @IBOutlet weak var lblRecentSearches: MaterialLocalizeLable!
    @IBOutlet weak var btnUserSearchDeleteAll: UIButton!
    
    var iCategoryId = 0
    var strCategoryName = ""
    var isOnlyBundle = false
    var arrNewProducts: [ProductResponseFields] = []
    var arrBunddelsList: [ProductResponseFields] = []
    var appAdvertisementProduct: [AppadvertisementProduct] = []
    var strFromWhichScreen = ""
    var arrSuggestions: [ProductSuggestionResponseFields] = []

    private var totalCount: Int = 0
    private var offset: Int = 1
    private var strSearchWord = ""
    private var isAutoCompleteShow = false
    
    var delegate : UpdateCountFromSearchProtocol?
    private var workItemReference : DispatchWorkItem? = nil
    
    private var recognitionRequest: SFSpeechAudioBufferRecognitionRequest?
    private var recognitionTask: SFSpeechRecognitionTask?
    private let audioEngine = AVAudioEngine()
    var isStart : Bool = false
    var timerDidFinishTalk: Timer?
    private var speechRecognizer = SFSpeechRecognizer(locale: Locale.init(identifier: "en-US"))
    var arrUserSearch: [UserSearchResponseData] = []
    let buttonUnderline: [NSAttributedString.Key: Any] = [
        .font: UIFont.systemFont(ofSize: 12),
        .foregroundColor: UIColor.gray,
        .underlineStyle: NSUnderlineStyle.single.rawValue
    ]
    // page number
    var pageNo = 1
    // MARK: Object lifecycle
    /*
    override init(nibName nibNameOrNil: String?, bundle nibBundleOrNil: Bundle?) {
        super.init(nibName: nibNameOrNil, bundle: nibBundleOrNil)
        setup()
    }
    */
    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
        setup()
    }
    
    // MARK: Setup
    
    private func setup() {
        let viewController = self
        let interactor = FilterSearchInteractor()
        let presenter = FilterSearchPresenter()
        
        //View Controller will communicate with only presenter
        viewController.presenterFilterSearch = presenter
        
        //Presenter will communicate with Interector and Viewcontroller
        presenter.viewControllerFilterSearch = viewController
        presenter.interactorFilterSearch = interactor
        
        //Interactor will communucate with only presenter.
        interactor.presenterFilterSearch = presenter
    }
    
    
    // MARK: View lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        txtSearch.clearButtonMode = .always
        txtSearch.placeholder = ObjKeymessages.kLABEL_SEARCH_HERE
        lblNoData.text = ObjKeymessages.kLABEL_NO_DATA_FOUND
        
        var dictParam : [String:Any] = [:]

        var strScreenName = ""
        if self.strFromWhichScreen == "HomeSearch" {
            txtSearch.becomeFirstResponder()
            viewSearch.isHidden = false
            btnMic.isHidden = false
            cons_height_searchView.constant = 40
            cons_bottom_searchView.constant = 25
            lblTitle.text = ObjKeymessages.kLABEL_SEARCH
            dictParam["iCategoryId"] = 0
            strScreenName = ScreenNames.SearchScreen.rawValue
        }
        else if self.strFromWhichScreen == "SideMenu" {
            viewSearch.isHidden = false
            btnMic.isHidden = false
            cons_height_searchView.constant = 40
            cons_bottom_searchView.constant = 25
            lblTitle.text = strCategoryName
            dictParam["iCategoryId"] = self.iCategoryId
            strScreenName = ScreenNames.CategoryProductListScreen.rawValue
        }
        else if self.strFromWhichScreen == "MoreNewProducts" {
            viewSearch.isHidden = true
            btnMic.isHidden = true
            cons_height_searchView.constant = 0
            cons_bottom_searchView.constant = 0
            lblTitle.text = ObjKeymessages.kLABEL_NEW
            dictParam["iCategoryId"] = 0
            strScreenName = ScreenNames.NewProductsScreen.rawValue
        }
        else if self.strFromWhichScreen == "MoreBestSellers" {
            viewSearch.isHidden = true
            btnMic.isHidden = true
            cons_height_searchView.constant = 0
            cons_bottom_searchView.constant = 0
            lblTitle.text = ObjKeymessages.kLABEL_BEST_SELLERS
            dictParam["iCategoryId"] = 0
            strScreenName = ScreenNames.BestSellerScreen.rawValue
        }
        else if self.strFromWhichScreen == "Banner" {
            viewSearch.isHidden = false
            btnMic.isHidden = false
            cons_height_searchView.constant = 40
            cons_bottom_searchView.constant = 25
            lblTitle.text = strCategoryName
            dictParam["iCategoryId"] = self.iCategoryId
            strScreenName = ScreenNames.CategoryProductListScreen.rawValue
        }
        else if self.strFromWhichScreen == "DynamicHomeCategory" {
            viewSearch.isHidden = true
            btnMic.isHidden = true
            cons_height_searchView.constant = 0
            cons_bottom_searchView.constant = 0
            lblTitle.text = strCategoryName
            dictParam["iCategoryId"] = self.iCategoryId
            strScreenName = ScreenNames.CategoryProductListScreen.rawValue
        }

        // Mixpanel
        MixpanelEvents.sharedInstance.logTimeEvent(strScreenName: strScreenName)

        self.collectionView.registerCell(cell: ProductsCollectionViewCellSmall.self)
        self.collectionView.registerCell(cell: AdvertisingCollectionViewCell.self)
        self.collectionView.registerCell(cell: BundelsListCollectionViewCell.self)
        
        dictParam["vSerchString"] = ""
        strSearchWord = ""
        dictParam["offset"] = offset

        if self.strFromWhichScreen != "HomeSearch" {
            self.getNewProducts(dictData: dictParam, strWhichScreen: self.strFromWhichScreen, pagingOrnot: false)
        }
        

    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        lblRecentSearches.text = "Recent Searches".localized
        let attributeString = NSMutableAttributedString(
                        string: "REMOVE".localized,
                        attributes: buttonUnderline)
        btnUserSearchDeleteAll.setAttributedTitle(attributeString, for: .normal)
    }
    
    override func viewDidDisappear(_ animated: Bool) {
        var strScreenName = ""
        if self.strFromWhichScreen == "HomeSearch" {
            strScreenName = ScreenNames.SearchScreen.rawValue
        }
        else if self.strFromWhichScreen == "SideMenu" {
            strScreenName = ScreenNames.CategoryProductListScreen.rawValue
        }
        else if self.strFromWhichScreen == "MoreNewProducts" {
            strScreenName = ScreenNames.NewProductsScreen.rawValue
        }
        else if self.strFromWhichScreen == "MoreBestSellers" {
            strScreenName = ScreenNames.BestSellerScreen.rawValue
        }
        else if self.strFromWhichScreen == "Banner" {
            strScreenName = ScreenNames.CategoryProductListScreen.rawValue
        }
        MixpanelEvents.sharedInstance.trackTimeEvent(strScreenName: strScreenName)
    }

    private func getNewProducts(dictData:[String:Any], strWhichScreen: String, pagingOrnot: Bool) {
        if AppSingletonObj.isConnectedToNetwork(){
            self.presenterFilterSearch?.apiCallForGetNewProducts(dictData: dictData, strWhichScreen: strWhichScreen, pagingOrnot: pagingOrnot, pageNo: self.pageNo, isOnlyBundle: self.isOnlyBundle == true ? 1 : 0)
        }
    }
    
    private func getSearchResult(dictData:[String:Any], strWhichScreen: String, pagingOrnot: Bool) {
        if AppSingletonObj.isConnectedToNetwork() {
            self.presenterFilterSearch?.apiCallForGetSearchResult(dictData: dictData, strWhichScreen: strWhichScreen, pagingOrnot: pagingOrnot, pageNo: self.pageNo, isOnlyBundle: self.isOnlyBundle == true ? 1 : 0)
        }
    }

    @IBAction func actionBack(_ sender: UIButton) {
        self.popVC()
    }

    @IBAction func btnMicAction(_ sender: Any) {
        self.view.endEditing(true)
        isStart = !isStart
        if audioEngine.isRunning {
            btnMic.isEnabled = false
            cancelSpeechRecognization()
        }
        else {
            self.checkSpeechPermision()
        }
    }

    func cancelSpeechRecognization() {
        
        recognitionTask?.finish()
        recognitionTask?.cancel()
        recognitionTask = nil
        
        recognitionRequest?.endAudio()
        audioEngine.stop()
        
        //MARK: UPDATED
        if audioEngine.inputNode.numberOfInputs > 0 {
            audioEngine.inputNode.removeTap(onBus: 0)
        }
                
        self.recognitionRequest = nil
        self.recognitionTask = nil
       
        if !self.strSearchWord.isEmpty {
            self.txtSearch.text = self.strSearchWord
            self.textFieldDidChangeSelection(self.txtSearch)
        }
    }

    func checkSpeechPermision() {
        SFSpeechRecognizer.requestAuthorization { (authStatus) in
            var isButtonEnabled = false
            switch authStatus {
            case .authorized:
                self.checkMicPermission()
            case .denied,.restricted:
                DispatchQueue.main.async {
                    ez.runThisInMainThread {
                        AppSingleton.shared.showCustomPopUpWithYesNoButton(strButton1Title: ObjKeymessages.kLABEL_SETTINGS, strButton2Title: ObjKeymessages.kLABEL_CANCEL, strMessage: ObjKeymessages.kMSG_SPEECH_PERMISSION, showOnTopVC: true) { (isOk) in
                            if isOk == true {
                                self.gotoAppSettings()
                            }
                        }
                    }
                }
            case .notDetermined:
                self.startRecording()
            @unknown default:
                fatalError()
            }
        }
    }

    func checkMicPermission() {
        let msg = ObjKeymessages.kMSG_MICROPHONE_PERMISSION
        
        switch AVAudioSession.sharedInstance().recordPermission {
        case AVAudioSession.RecordPermission.granted:
            self.startRecording()
        case AVAudioSession.RecordPermission.denied:
            self.presentSettings(msg: msg)
        case AVAudioSession.RecordPermission.undetermined:
            AVAudioSession.sharedInstance().requestRecordPermission({ (granted) in
                if granted {
                    DispatchQueue.main.async {
                        self.startRecording()
                    }
                }else {
                    DispatchQueue.main.async {
                        self.presentSettings(msg: msg)
                    }
                }
            })
        default:
            self.presentSettings(msg: msg)
            break
        }
        
        
    }

    private func gotoAppSettings() {
        guard let settingsUrl = URL(string: UIApplication.openSettingsURLString) else {
            return
        }
        if UIApplication.shared.canOpenURL(settingsUrl) {
            UIApplication.shared.open(settingsUrl, completionHandler: { (success) in
                print("Settings opened: \(success)") // Prints true
            })
        }
    }

    func presentSettings(msg : String) {
        ez.runThisInMainThread {
            AppSingleton.shared.showCustomPopUpWithYesNoButton(strButton1Title: ObjKeymessages.kLABEL_SETTINGS, strButton2Title: ObjKeymessages.kLABEL_CANCEL, strMessage: ObjKeymessages.kMSG_MICROPHONE_PERMISSION, showOnTopVC: true) { (isOk) in
                if isOk == true {
                    self.gotoAppSettings()
                }
            }
        }
    }

    func startRecording() {
        
        if recognitionTask != nil {
            recognitionTask?.cancel()
            recognitionTask = nil
        }
        
        let audioSession = AVAudioSession.sharedInstance()
        do {
            try audioSession.setCategory(AVAudioSession.Category.record)
            try audioSession.setMode(AVAudioSession.Mode.measurement)
            try audioSession.setActive(true, options: .notifyOthersOnDeactivation)
        } catch {
            print("audioSession properties weren't set because of an error.")
        }
        
        recognitionRequest = SFSpeechAudioBufferRecognitionRequest()
        
        let inputNode = audioEngine.inputNode
        
        guard let recognitionRequest = recognitionRequest else {
            fatalError("Unable to create an SFSpeechAudioBufferRecognitionRequest object")
        }
        
        recognitionRequest.shouldReportPartialResults = true
        
        if UserDefaults.standard.getLanguage()! == UserAPI.VLanguage_userLanguage.en.rawValue {       // english
            speechRecognizer = SFSpeechRecognizer(locale: Locale.init(identifier: "en-US"))
        }
        else {
            speechRecognizer = SFSpeechRecognizer(locale: Locale.init(identifier: "ar-SA"))
        }
        
        recognitionTask = speechRecognizer?.recognitionTask(with: recognitionRequest, resultHandler: { (result, error) in
            
            self.btnMic.isEnabled = true

            if result != nil && self.audioEngine.isRunning {
                self.timerDidFinishTalk?.invalidate()
                self.timerDidFinishTalk = nil
                self.timerDidFinishTalk = Timer.scheduledTimer(timeInterval: TimeInterval(1), target: self, selector:#selector(self.didFinishTalk), userInfo: nil, repeats: false)

                self.strSearchWord = result?.bestTranscription.formattedString ?? ""
                TikTokBusiness.trackEvent("search_bar_voice_search", withProperties: ["search_word": self.strSearchWord])
                Analytics.logEvent("search_bar_voice_search", parameters: ["search_word": self.strSearchWord])
            }
        })
        
        let recordingFormat = inputNode.outputFormat(forBus: 0)
        inputNode.installTap(onBus: 0, bufferSize: 1024, format: recordingFormat) { (buffer, when) in
            self.recognitionRequest?.append(buffer)
        }
        
        audioEngine.prepare()
        
        do {
            try audioEngine.start()
        } catch {
            print("audioEngine couldn't start because of an error.")
        }
        
        DispatchQueue.main.async {
            self.btnMic.isEnabled = false
        }
        
    }
    

    @objc func didFinishTalk() {
        print("Timer ends....")
        self.cancelSpeechRecognization()
    }

    @IBAction func btnUserSearchDeleteAllTapped(_ sender: Any) {
        self.showAlertYesNoPopup(message: "Are you sure to clear all previous searches?".localized, yesTitle: "Yes".localized, noTitle: "No".localized) {
            self.presenterFilterSearch?.deleteAllUserSearch()
        } noCompletion: {
            debugPrint("No Action")
        }
    }
    
}

extension FilterSearchViewController: UICollectionViewDelegate, UICollectionViewDataSource, UICollectionViewDelegateFlowLayout {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        if self.appAdvertisementProduct.count >= 2 {
            return arrNewProducts.count + self.arrBunddelsList.count
        } else {
            return (arrNewProducts.count + self.arrBunddelsList.count) + self.appAdvertisementProduct.count
        }
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        if indexPath.row == 0 && self.appAdvertisementProduct.count >= indexPath.row && self.appAdvertisementProduct.count >= 1 {
            let cell = collectionView.dequeue(with: AdvertisingCollectionViewCell.self, for: indexPath)
            cell.setData(adsImages: self.appAdvertisementProduct[indexPath.row].vProductImage ?? [])
            return cell
        }
        
        var index = indexPath.row
        if self.appAdvertisementProduct.count >= 1 {
            index -= 1
        }
        // logic of bundle cell
        if arrBunddelsList.count >= 1 {
            if arrBunddelsList[index].bunddelId != nil {
                let cell = collectionView.dequeue(with: BundelsListCollectionViewCell.self, for: indexPath)
                cell.isAppadvertisement(value: true)
                let bundelsListResponseFields = BundelsListResponseFields(bunddelId: self.arrBunddelsList[index].bunddelId, vBunddelName: self.arrBunddelsList[index].vBunddelName, price: self.arrBunddelsList[index].price, discountPrice: self.arrBunddelsList[index].discountPrice, vBunddelImage: self.arrBunddelsList[index].vBunddelImage, tiDiscountType: self.arrBunddelsList[index].tiDiscountType, vBunddelSKU: self.arrBunddelsList[index].vBunddelSKU, txBunddelDescription: self.arrBunddelsList[index].txBunddelDescription, IsAppadvertisement: self.arrBunddelsList[index].isAppAdvertisement, iBunddelQuantity: 1, iCartId: 1)
                cell.setupCell(bundle: bundelsListResponseFields)
                cell.btnAddToCart.tag =  index //self.bundelsList[indexPath.row].bunddelId ?? 0
                cell.btnAddToCart.addTarget(self, action: #selector(self.addBundleToCart(_:)), for: .touchUpInside)
                return cell
            }
        }
        // end logic of bundle cell
        let cell = collectionView.dequeue(with: ProductsCollectionViewCellSmall.self, for: indexPath)
        cell.isAppadvertisement(value: arrNewProducts[index].isAppAdvertisement == 1 ? false : true)
        cell.isQuantitiesDiscountHidden(value: arrNewProducts[indexPath.row].quantityDiscount ?? 1 == 1 ? false : true)
        cell.lblProductName.text = "\(arrNewProducts[index].vProductName ?? "")"
        cell.lblPriceTitle.text = "\(arrNewProducts[index].vProductUnit ?? "")"
        let price = arrNewProducts[index].price ?? 0.0 == 0.0 ? arrNewProducts[index].dbOriginalProductPrice?.toDouble() ?? 0.0 : arrNewProducts[index].price ?? 0.0
        cell.lblPrice.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: price))")

        if arrNewProducts[index].offertitle != nil && arrNewProducts[index].offertitle != "" {
            cell.viewOfferTitle.isHidden = false
            cell.lblOfferTitle.text = "\("Offer".localized)"
        } else {
            cell.viewOfferTitle.isHidden = true
        }
        
        if arrNewProducts[index].isLowQuantity == 0 {  // 0 - hide low quantity label
            cell.viewLowQuantity.isHidden = true
            
            cell.imgProduct.kf.indicatorType = .activity
            let url = URL(string: arrNewProducts[index].vProductImage ?? "")
            cell.imgProduct.kf.setImage(with: url , placeholder: UIImage(named: "placeholder_product"))
        }
        else {  // 1 - show low quantity label
            if arrNewProducts[index].iHowMuchLeftInStock ?? 0 <= 0 {  // out of stock
                cell.viewLowQuantity.isHidden = true
                
                cell.imgProduct.kf.indicatorType = .activity
                let url = URL(string: arrNewProducts[index].vProductImage ?? "")
                
                cell.imgProduct.kf.setImage(with: url , placeholder: UIImage(named: "placeholder_product")) { result in
                   switch result {
                   case .success(let value):
                       cell.imgProduct.image = value.image.grayed
                   case .failure(let error):
                       print("Error: \(error)")
                   }
                 }
            }
            else {
                cell.viewLowQuantity.isHidden = false
                
                cell.imgProduct.kf.indicatorType = .activity
                let url = URL(string: arrNewProducts[index].vProductImage ?? "")
                cell.imgProduct.kf.setImage(with: url , placeholder: UIImage(named: "placeholder_product"))
            }
        }

        if self.strFromWhichScreen == "HomeSearch" {
            // discount logic
            if arrNewProducts[index].tiDiscountType == 0 {  // no discount
                let price = arrNewProducts[index].price ?? 0.0 == 0.0 ? arrNewProducts[index].dbOriginalProductPrice?.toDouble() ?? 0.0 : arrNewProducts[index].price ?? 0.0
                cell.lblPrice.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: price))")
                cell.lblOldPrice.isHidden = true
                cell.lblOffer.isHidden = true
            }
            else {  // discount is there
                let discountPrice = arrNewProducts[index].discountPrice ?? 0.0 == 0.0 ? arrNewProducts[index].dDiscountedProductPrice?.toDouble() ?? 0 : arrNewProducts[index].discountPrice ?? 0.0
                let price = arrNewProducts[index].price ?? 0.0 == 0.0 ? arrNewProducts[index].dbOriginalProductPrice?.toDouble() ?? 0.0 : arrNewProducts[index].price ?? 0.0
                cell.lblPrice.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: discountPrice))")
                cell.lblOldPrice.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: price))", isDiscounted: true)
                cell.lblOffer.text = "( \(forTrailingZero(temp: discountPrice))% \(ObjKeymessages.kLABEL_OFF) )"
                let originalPrice = Double(price)
                let discountedPrice = Double(discountPrice)
                let a = originalPrice - discountedPrice
                let b = a / originalPrice
                let percent = b * 100
                cell.lblOffer.text = "(\(forTrailingZero(temp: percent.rounded(.up)))% \(ObjKeymessages.kLABEL_OFF))"
                
                cell.lblOldPrice.isHidden = false
                cell.lblOffer.isHidden = false
            }
            // discount logic ends here
        } else {
            // discount logic
            if arrNewProducts[index].tiDiscountType == 1 {  // no discount
                let price = arrNewProducts[index].price ?? 0.0 == 0.0 ? arrNewProducts[index].dbOriginalProductPrice?.toDouble() ?? 0.0 : arrNewProducts[index].price ?? 0.0
                cell.lblPrice.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: price))")
                cell.lblOldPrice.isHidden = true
                cell.lblOffer.isHidden = true
            }
            else {  // discount is there
                let discountPrice = arrNewProducts[index].discountPrice ?? 0.0 == 0.0 ? arrNewProducts[index].dDiscountAmount?.toDouble() ?? 0 : arrNewProducts[index].discountPrice ?? 0.0
                let price = arrNewProducts[index].price ?? 0.0 == 0.0 ? arrNewProducts[index].dbOriginalProductPrice?.toDouble() ?? 0.0 : arrNewProducts[index].price ?? 0.0
                cell.lblPrice.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: discountPrice))")
                cell.lblOldPrice.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: price))", isDiscounted: true)
                cell.lblOffer.text = "( \(forTrailingZero(temp: discountPrice))% \(ObjKeymessages.kLABEL_OFF) )"
                let originalPrice = Double(price)
                let discountedPrice = Double(discountPrice)
                let a = originalPrice - discountedPrice
                let b = a / originalPrice
                let percent = b * 100
                cell.lblOffer.text = "(\(forTrailingZero(temp: percent.rounded(.up)))% \(ObjKeymessages.kLABEL_OFF))"
                cell.lblOldPrice.isHidden = false
                cell.lblOffer.isHidden = false
            }
            // discount logic ends here
        }
        if arrNewProducts[index].iHowMuchLeftInStock ?? 0 <= 0 {  // out of stock
            cell.viewOutOfStock.isHidden = false
        }
        else {
            cell.viewOutOfStock.isHidden = true
        }
        if arrNewProducts[index].quantityDiscount ?? 1 == 0 {
            cell.stackViewDiscountPrice.isHidden = false
        } else {
            let priceMin = self.arrNewProducts[index].prices?.map({$0.price?.toDouble() ?? 0.0}).min() ?? 0.0
            let priceMax = self.arrNewProducts[index].prices?.map({$0.price?.toDouble() ?? 0.0}).max() ?? 0.0
            cell.lblPrice.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: priceMin)) - \(forTrailingZero(temp: priceMax))")
            cell.stackViewDiscountPrice.isHidden = true
        }
        
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        // logic of bundle cell
        var index = indexPath.row
        if self.appAdvertisementProduct.count >= 1 {
            index -= 1
        }
        // bundle cell
        let width = (collectionView.frame.width - 50) / 2 // Adjust for padding and number of columns
        return CGSize(width: width, height: 230) // Fixed height for items
    }
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        if self.arrBunddelsList.count >= 1 {
            if indexPath.row >= arrBunddelsList.count {
                return
            }
            self.goToProductDetailScreen(obj: arrBunddelsList[indexPath.row])
        } else {
            if indexPath.row >= arrNewProducts.count {
                return
            }
            self.goToProductDetailScreen(obj: arrNewProducts[indexPath.row])
        }
    }
    
    private func goToProductDetailScreen(obj: ProductResponseFields) {
        let vc = ProductPopupStoryboard.instantiate(ProductPopupViewController.self)
        vc.objProductDetails = obj
        vc.arrMayLikeProducts = obj.youMayAlsoLikeProducts ?? []
        vc.productId = obj.biProductId ?? 0
        vc.bundleID = obj.bunddelId ?? 0
        vc.delegate = self
        if self.strFromWhichScreen == "HomeSearch" {
            vc.strFromWhichScreen = "HomeSearch"
        }
        vc.showYouMayLikePopup = { [weak self] (obj) in
            DispatchQueue.main.async {
                self?.goToProductDetailScreen(obj: obj)
            }
        }

        self.presentVC(vc)
    }
    
    func collectionView(_ collectionView: UICollectionView, willDisplay cell: UICollectionViewCell, forItemAt indexPath: IndexPath) {
        if indexPath.row == (self.arrNewProducts.count + self.arrBunddelsList.count) - 1  {
            if (self.arrNewProducts.count + self.arrBunddelsList.count) < totalCount {
                offset = offset + 1
                
                var dictParam : [String:Any] = [:]
                if self.strFromWhichScreen == "HomeSearch" {
                    dictParam["iCategoryId"] = 0
                }
                else {
                    dictParam["iCategoryId"] = self.iCategoryId
                }

                dictParam["vSerchString"] = strSearchWord
                dictParam["offset"] = offset

                cons_activityIndicatorView_height.constant = 40
                stackViewActivityIndicatorView.isHidden = false
                activityIndicatorView.isHidden = false
                activityIndicatorView.startAnimating()
                self.pageNo += 1
                if txtSearch.text?.length ?? 0 >= 1 {
                    var dictParam : [String:Any] = [:]
                    dictParam["vSerchString"] = txtSearch.text ?? ""
                    dictParam["offset"] = offset
                    self.getSearchResult(dictData: dictParam, strWhichScreen: self.strFromWhichScreen, pagingOrnot: true)
                } else {
                    self.getNewProducts(dictData: dictParam, strWhichScreen: self.strFromWhichScreen, pagingOrnot: true)
                }
            }
        }
    }
    
    @objc func addBundleToCart(_ sender: UIButton) {
        self.addBundleToCart(bundle: sender.tag)
    }
    
}

extension FilterSearchViewController: FilterSearchProtocol {
    
    func displayAlert(string: String) {
        AppSingleton.shared.showCustomPopUpWithOkButton(strButtonTitle: ObjKeymessages.kLABEL_OK, strMessage: string, showOnTopVC: false) { (isOk) in
        }
    }

    func refreshNewProducts(model : ProductListResponseFields) {
        
        cons_activityIndicatorView_height.constant = 0
        stackViewActivityIndicatorView.isHidden = true
        activityIndicatorView.isHidden = true
        activityIndicatorView.stopAnimating()

        self.totalCount = model.totalRecord ?? 0

        // emtpy the array if it is initial call
        if self.offset == 1 {
            self.arrNewProducts.removeAll()
            self.arrBunddelsList.removeAll()
        }
        self.arrNewProducts.append(contentsOf: model.productList ?? [])
        self.arrBunddelsList.append(contentsOf: model.bunddelsList ?? [])
        self.appAdvertisementProduct.append(contentsOf: model.AppadvertisementProduct ?? [])

        if arrNewProducts.count == 0 && arrBunddelsList.count == 0 {
            lblNoData.isHidden = false
        }
        else {
            lblNoData.isHidden = true
        }

        collectionView.reloadData()
        
    }

    func refreshSuggestions(arrSuggestionsTexts : [ProductSuggestionResponseFields]) {
        
        guard isAutoCompleteShow else {
            isAutoCompleteShow = true
            return
        }
        
        self.arrSuggestions = arrSuggestionsTexts
        
        tblSuggestions.reloadData()
        self.tblSuggestions.layoutIfNeeded()

        if self.arrSuggestions.count <= 0 {
            self.cons_height_tblSuggestions.constant = 0
            tblSuggestions.isHidden = true
            
            self.textFieldShouldReturn(txtSearch)
        }
        else {
            tblSuggestions.isHidden = false
            if self.tblSuggestions.contentSize.height <= 204 {
                DispatchQueue.main.async {
                    self.cons_height_tblSuggestions.constant = self.tblSuggestions.contentSize.height
                }
            }
            else {
                self.cons_height_tblSuggestions.constant = 204
            }
        }

    }

    func getUserSearch(userSearch: [UserSearchResponseData]) {
        if userSearch.count == 0 {
            self.stackViewRecentSearches.isHidden = true
            tblSuggestions.isHidden = true
            return
        }
        self.arrUserSearch = userSearch
        self.tblSuggestions.reloadData()
        self.tblSuggestions.layoutIfNeeded()
        tblSuggestions.isHidden = false
        if self.tblSuggestions.contentSize.height <= 204 {
            DispatchQueue.main.async {
                self.cons_height_tblSuggestions.constant = self.tblSuggestions.contentSize.height
            }
        } else {
            self.cons_height_tblSuggestions.constant = 204
        }
    }
    
    func addUserSearch(searchtext: String) {
        self.presenterFilterSearch?.addUserSearch(searchtext: searchtext)
    }
    
    func refreshUserSearch() {
        self.presenterFilterSearch?.getUserSearch()
    }
    
}

extension FilterSearchViewController: UITextFieldDelegate {
    
    func textFieldDidBeginEditing(_ textField: UITextField) {
        if audioEngine.isRunning {
            self.didFinishTalk()
        }
        
        if textField.text == "" || textField.text == " " {
            self.presenterFilterSearch?.getUserSearch()
        }
    }
    
    func textFieldShouldReturn(_ textField: UITextField) -> Bool {
        
        self.workItemReference?.cancel()
        DispatchQueue.main.async {
            TikTokBusiness.trackEvent("SEARCH", withProperties: ["search_tect": textField.text ?? ""])
            Analytics.logEvent("SEARCH", parameters: ["search_tect": textField.text ?? ""])
            AppsFlyerEvents.shared.afSearch(searchValue: textField.text ?? "")
            self.presenterFilterSearch?.addUserSearch(searchtext: textField.text ?? "")
        }
        textField.resignFirstResponder()
        tblSuggestions.isHidden = true
        isAutoCompleteShow = false
        performAction(strText: textField.text ?? "")
        return true
    }

    func textFieldShouldClear(_ textField: UITextField) -> Bool {
        if audioEngine.isRunning {
            self.didFinishTalk()
        }

        self.totalCount = 0
        self.arrNewProducts.removeAll()
        self.arrBunddelsList.removeAll()
        lblNoData.isHidden = false
        collectionView.reloadData()
        self.presenterFilterSearch?.getUserSearch()
        return true
    }
    
    func performAction(strText: String) {
        if strText == "" || strText == " " {
            self.presenterFilterSearch?.getUserSearch()
            return
        }
        self.stackViewRecentSearches.isHidden = true
        var dictParam : [String:Any] = [:]
        if self.strFromWhichScreen == "HomeSearch" {
            dictParam["iCategoryId"] = 0
        }
        else {
            dictParam["iCategoryId"] = self.iCategoryId
        }

        dictParam["vSerchString"] = txtSearch.text ?? ""
        strSearchWord = txtSearch.text ?? ""
        
        offset = 1
        self.pageNo = 1
        dictParam["offset"] = offset
        
        if strText.isEmpty && self.strFromWhichScreen == "HomeSearch" {
            self.totalCount = 0
            self.arrNewProducts.removeAll()
            self.arrBunddelsList.removeAll()
            lblNoData.isHidden = true
            collectionView.reloadData()
            return
        }
        
        // Mixpanel
        Mixpanel.mainInstance().track(event: ScreenNames.SearchedKeyword.rawValue, properties: [
            "search_keyword": strText
        ])

        self.getSearchResult(dictData: dictParam, strWhichScreen: self.strFromWhichScreen, pagingOrnot: false)
    }
 
    func textFieldDidChangeSelection(_ textField: UITextField) {
           
        if textField.text == "" || textField.text == " " {
            self.presenterFilterSearch?.getUserSearch()
            return
        }
        isAutoCompleteShow = true
        
        self.workItemReference?.cancel()
        
        let userSearchWorkItem = DispatchWorkItem
        {
            //Do api call
            self.getSearchSuggestions(strSearchedText: textField.text ?? "")
            self.strSearchWord = textField.text ?? ""
        }
        
        self.workItemReference = userSearchWorkItem
        
        DispatchQueue.main.asyncAfter(deadline: .now() + .seconds(0.7),
                                      execute: userSearchWorkItem)
        
    }
    
    func getSearchSuggestions(strSearchedText: String) {
        if strSearchedText.isEmpty {
            tblSuggestions.isHidden = true
        }
        else {
            if AppSingletonObj.isConnectedToNetwork(){
                var dictParam : [String:Any] = [:]
                dictParam["vProductName"] = strSearchedText
                
                if self.strFromWhichScreen == "HomeSearch" {
                    dictParam["iCategoryId"] = 0
                }
                else if self.strFromWhichScreen == "SideMenu" {
                    dictParam["iCategoryId"] = self.iCategoryId
                }
                else if self.strFromWhichScreen == "MoreNewProducts" {
                    dictParam["iCategoryId"] = 0
                }
                else if self.strFromWhichScreen == "MoreBestSellers" {
                    dictParam["iCategoryId"] = 0
                }
                else if self.strFromWhichScreen == "Banner" {
                    dictParam["iCategoryId"] = self.iCategoryId
                }

                self.presenterFilterSearch?.apiCallForSearchSuggestions(dictData: dictParam)
            }
        }
    }
    
}

extension FilterSearchViewController : UpdateCountProtocol {
    
    func updateCartCount(cartCount:Int?) {
        // update my cart button count
        delegate?.updateCartCountFromSearch(cartCount: cartCount)
    }
}

extension FilterSearchViewController : UITableViewDelegate, UITableViewDataSource{
    func numberOfSections(in tableView: UITableView) -> Int {
        return 1
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        if txtSearch.text == "" || txtSearch.text == " " {
            return self.arrUserSearch.count
        } else {
            return arrSuggestions.count
        }
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        if txtSearch.text == "" || txtSearch.text == " " {
            self.stackViewRecentSearches.isHidden = false
            let cell = tblSuggestions.dequeueReusableCell(withIdentifier: "SearchSuggestionCell") as! SearchSuggestionCell
            cell.lblSuggestionText.text = self.arrUserSearch[indexPath.row].searchtext
            cell.deleteBtn.isHidden = true
            cell.deleteBtn.tag = indexPath.row
            
            let attributeString = NSMutableAttributedString(
                string: "REMOVE".localized,
                attributes: buttonUnderline)
            cell.deleteBtn.setAttributedTitle(attributeString, for: .normal)
            cell.deleteBtn.addTarget(self, action: #selector(deleteUserSearch(_:)), for: .touchUpInside)
            return cell
        } else {
            self.stackViewRecentSearches.isHidden = true
            if arrSuggestions.count == 0 { return UITableViewCell() }
            let cell = tblSuggestions.dequeueReusableCell(withIdentifier: "SearchSuggestionCell") as! SearchSuggestionCell
            cell.lblSuggestionText.text = arrSuggestions[indexPath.row].vProductName
            cell.deleteBtn.isHidden = true
            return cell
        }
    }
    
    @objc func deleteUserSearch(_ sender: UIButton) {
        let index = sender.tag
        self.presenterFilterSearch?.deleteUserSearch(searchID: self.arrUserSearch[index].id ?? 0)
    }
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        TikTokBusiness.trackEvent(TTEventName.search.rawValue, withProperties: ["search_text": txtSearch.text ?? ""])
        Analytics.logEvent(TTEventName.search.rawValue, parameters: ["search_text": txtSearch.text ?? ""])
        if txtSearch.text == "" || txtSearch.text == " " {
            AppsFlyerEvents.shared.afSearch(searchValue: txtSearch.text ?? "")
            txtSearch.resignFirstResponder()
            txtSearch.text = arrUserSearch[indexPath.row].searchtext ?? ""
            tblSuggestions.isHidden = true
            performAction(strText: arrUserSearch[indexPath.row].searchtext ?? "")
        } else {
            DispatchQueue.main.async {
                TikTokBusiness.trackEvent("SEARCH", withProperties: ["search_text": self.arrSuggestions[indexPath.row].vProductName ?? ""])
                Analytics.logEvent("SEARCH", parameters: ["search_text": self.arrSuggestions[indexPath.row].vProductName ?? ""])
                AppsFlyerEvents.shared.afSearch(searchValue: self.arrSuggestions[indexPath.row].vProductName ?? "")
                self.addUserSearch(searchtext: self.arrSuggestions[indexPath.row].vProductName ?? "")
            }
            txtSearch.resignFirstResponder()
            txtSearch.text = arrSuggestions[indexPath.row].vProductName ?? ""
            tblSuggestions.isHidden = true
            performAction(strText: arrSuggestions[indexPath.row].vProductName ?? "")
        }
        
    }
}

// MARK: - BundelsListTableViewCellDelegate
extension FilterSearchViewController: BundelsListTableViewCellDelegate {
    // MARK: - Func
    func openBundelsListDetails(bundleID: Int, index: Int) {
        debugPrint(bundleID)
        self.goToProductDetailScreen(obj: self.arrBunddelsList[index])
    }
    
    func addBundleToCart(bundle: Int) {
        debugPrint(bundle)
        if !User.shared.checkUserLoginStatus() {
            self.showNoLoginUserAlert(complation: nil)
            return
        }
        var dictParam : [String:Any] = [:]
        dictParam["biProductId"] = 0
        dictParam["bunddelId"] = self.arrBunddelsList[bundle].bunddelId ?? 0
        dictParam["tiIsCheck"] = AddToCartIsCheck.Add.rawValue
        dictParam["iProductQuantity"] = "1"
        dictParam["dbPrice"] = self.arrNewProducts[bundle].price ?? 0 <= 0 ? self.arrBunddelsList[bundle].price ?? 0 : self.arrNewProducts[bundle].price ?? 0.0
        
        if AppSingletonObj.isConnectedToNetwork(){
            self.presenterFilterSearch?.apiCallForAddToCart(dictData: dictParam)
        }
    }
    
    func displayAlertAndDismiss(string:String, cartCount: Int) {
        self.stopLoading()
        // update my cart button count
        self.updateCartCount(cartCount: cartCount)
        self.vibrationDevice()
        AppSingletonObj.showAlert(strMessage: string)
    }

    func displayErrorAlert(strMsg: String) {
        AppSingleton.shared.showCustomPopUpWithOkButton(strButtonTitle: ObjKeymessages.kLABEL_OK, strMessage: strMsg, showOnTopVC: true) { (isOk) in
            if isOk == true {
                self.dismissVC(completion: nil)
            }
        }
    }
    
}

extension TimeInterval {

    public static func seconds(_ value: Double) -> TimeInterval {
        return value
    }

    public static func minutes(_ value: Double) -> TimeInterval {
        return value * 60
    }

    public static func hours(_ value: Double) -> TimeInterval {
        return value * 60 * 60
    }

    public static func days(_ value: Double) -> TimeInterval {
        return value * 60 * 60 * 24
    }

}
