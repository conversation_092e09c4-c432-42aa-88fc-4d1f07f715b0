
import UIKit

protocol FilterSearchPresentationProtocol {
    func apiCallForGetNewProducts(dictData:[String:Any], strWhichScreen: String, pagingOrnot: Bool, pageNo: Int, isOnlyBundle: Int)
    func apiResponseGetNewProducts(response:ProductListResponse?,error:Error?)

    func apiCallForSearchSuggestions(dictData:[String:Any])
    func apiResponseForSearchSuggestions(response:ProductSuggestionResponse?,error:Error?)

    func getUserSearch()
    func apiResponseForGetUserSearch(response: UserSearchResponse?, error: Error?)
    func addUserSearch(searchtext: String)
    func deleteUserSearch(searchID: Int)
    func apiResponseForDeleteUserSearch(response: BaseResponseModel?, error: Error?)
    func deleteAllUserSearch()
    func apiCallForAddToCart(dictData:[String:Any])
    func apiResponseAddToCart(response:AddToCartResponse?,error:Error?)
    
    func apiCallForGetSearchResult(dictData:[String:Any], strWhichScreen: String, pagingOrnot: Bool, pageNo: Int, isOnlyBundle: Int)
    func apiResponseGetSearchResult(response:ProductListResponse?,error:Error?)
}

class FilterSearchPresenter: FilterSearchPresentationProtocol {
    
    // MARK: Objects & Variables
    weak var viewControllerFilterSearch: FilterSearchProtocol?
    var interactorFilterSearch: FilterSearchInteractorProtocol?
    
    func apiCallForGetNewProducts(dictData:[String:Any], strWhichScreen: String, pagingOrnot: Bool, pageNo: Int, isOnlyBundle: Int) {
        interactorFilterSearch?.apiCallForGetNewProducts(dictData: dictData, strWhichScreen: strWhichScreen, pagingOrnot: pagingOrnot, pageNo: pageNo, isOnlyBundle: isOnlyBundle)
    }
    
    func apiResponseGetNewProducts(response: ProductListResponse?, error: Error?) {
        if let error = error  {
            viewControllerFilterSearch?.displayAlert(string: error.localizedDescription)
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            return
        }
        
        if code == APIINVALIDAUTHORIZATIONCODE401 {
            AppSingletonObj.goToLoginScreen(message: response.responseMessage ?? "")
            return
        }
        
        if code == APICODE400 {
            viewControllerFilterSearch?.displayAlert(string: response.responseMessage ?? "")
            return
        }
        
        guard let model = response.responseData,code == APISUCCESSCODE200  else {
            return
        }
        self.viewControllerFilterSearch?.refreshNewProducts(model: model)
    }
    
    func apiCallForGetSearchResult(dictData:[String:Any], strWhichScreen: String, pagingOrnot: Bool, pageNo: Int, isOnlyBundle: Int) {
        interactorFilterSearch?.apiCallForGetSearchResult(dictData: dictData, strWhichScreen: strWhichScreen, pagingOrnot: pagingOrnot, pageNo: pageNo, isOnlyBundle: isOnlyBundle)

    }
    
    func apiResponseGetSearchResult(response:ProductListResponse?,error:Error?) {
        if let error = error  {
            viewControllerFilterSearch?.displayAlert(string: error.localizedDescription)
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            return
        }
        
        if code == APIINVALIDAUTHORIZATIONCODE401 {
            AppSingletonObj.goToLoginScreen(message: response.responseMessage ?? "")
            return
        }
        
        if code == APICODE400 {
            viewControllerFilterSearch?.displayAlert(string: response.responseMessage ?? "")
            return
        }
        
        guard let model = response.responseData,code == APISUCCESSCODE200  else {
            return
        }
        self.viewControllerFilterSearch?.refreshNewProducts(model: model)
    }
    
    func apiCallForSearchSuggestions(dictData:[String:Any]) {
        interactorFilterSearch?.apiCallForSearchSuggestions(dictData: dictData)
    }
    
    func apiResponseForSearchSuggestions(response:ProductSuggestionResponse?,error:Error?) {
        if let error = error  {
            viewControllerFilterSearch?.displayAlert(string: error.localizedDescription)
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            return
        }
        
        if code == APIINVALIDAUTHORIZATIONCODE401 {
            AppSingletonObj.goToLoginScreen(message: response.responseMessage ?? "")
            return
        }
        
        if code == APICODE400 {
            viewControllerFilterSearch?.displayAlert(string: response.responseMessage ?? "")
            return
        }
        
        guard let model = response.responseData,code == APISUCCESSCODE200  else {
            return
        }
        self.viewControllerFilterSearch?.refreshSuggestions(arrSuggestionsTexts: model)
    }
    
    func getUserSearch() {
        interactorFilterSearch?.getUserSearch()
    }
    
    func apiResponseForGetUserSearch(response: UserSearchResponse?, error: Error?) {
        if let error = error  {
            viewControllerFilterSearch?.displayAlert(string: error.localizedDescription)
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            return
        }
        
        if code == APIINVALIDAUTHORIZATIONCODE401 {
            AppSingletonObj.goToLoginScreen(message: response.responseMessage ?? "")
            return
        }
        
        if code == APICODE400 {
            viewControllerFilterSearch?.displayAlert(string: response.responseMessage ?? "")
            return
        }
        
        guard let model = response.responseData,code == APISUCCESSCODE200  else {
            return
        }
        self.viewControllerFilterSearch?.getUserSearch(userSearch: model)
    }
    
    func addUserSearch(searchtext: String) {
        interactorFilterSearch?.addUserSearch(searchtext: searchtext)
    }
    
    func deleteUserSearch(searchID: Int) {
        interactorFilterSearch?.deleteUserSearch(searchID: searchID)
    }
    
    func apiResponseForDeleteUserSearch(response: BaseResponseModel?, error: Error?) {
        self.viewControllerFilterSearch?.refreshUserSearch()
    }
    
    func deleteAllUserSearch() {
        interactorFilterSearch?.deleteAllUserSearch()
    }
    
    func apiCallForAddToCart(dictData:[String:Any]) {
        interactorFilterSearch?.apiCallForAddToCart(dictData: dictData)
    }

    func apiResponseAddToCart(response: AddToCartResponse?, error: Error?) {
        if let error = error  {
            viewControllerFilterSearch?.displayAlert(string: error.localizedDescription)
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            //            viewController?.displayAlert(strTitle: "", strMessage: KeyMessages.kInvalidateResponse)
            return
        }
        
        if code == APIINVALIDAUTHORIZATIONCODE401 {
            AppSingletonObj.displaySessionExpiredAlert(strMsg: response.responseMessage ?? "")
            return
        }
        
        if code == APICODE400 {
            viewControllerFilterSearch?.displayAlert(string: response.responseMessage ?? "")
            return
        }
        
        if code == APICODE203 {
            self.viewControllerFilterSearch?.displayAlert(string: response.responseMessage ?? "")
            return
        }
        
        guard let model = response.responseData,code == APISUCCESSCODE200  else {
            return
        }
        self.viewControllerFilterSearch?.displayAlertAndDismiss(string: response.responseMessage ?? "", cartCount: model.getCartProducts ?? 0)
    }
    
}
