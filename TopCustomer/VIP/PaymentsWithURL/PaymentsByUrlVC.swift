//
//  PaymentsByUrlVC.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 28/11/2024.
//  Copyright © 2024 SOTSYS203. All rights reserved.
//

import UIKit
import WebKit

class PaymentsByUrlVC: BaseViewController, WKNavigationDelegate {

    @IBOutlet weak var webView: WKWebView!
    
    private let url: String
    private var paymentCallback: ((Bool, String) -> Void)?
    init(url: String,
         paymentCallback: ((Bool, String) -> Void)?
    ) {
        self.url = url
        self.paymentCallback = paymentCallback
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        // Do any additional setup after loading the view.
        setupWebView()
    }

    private func setupWebView() {
        webView.navigationDelegate = self
        if let url = URL(string: url) {
            let request = URLRequest(url: url)
            webView.load(request)
        }
    }
    
    func webView(_ webView: WKWebView, didFinish navigation: WKNavigation!) {
        guard let url = webView.url?.absoluteString else { return }
        if url.lowercased().contains("success") {
            self.dismiss(animated: true) {
                self.paymentCallback?(true, "Done")
            }
        } else if url.lowercased().contains("failed") {
            self.dismiss(animated: true) {
                self.paymentCallback?(false, "payment_failed".localized)
            }
        }
    }
    
    @IBAction func didCloseButtonTapped(_ sender: UIButton) {
        self.showAlertYesNoPopup(message: "are_you_sure_you_want_to_cancel_the_payment".localized, yesCompletion: {
            self.dismiss(animated: true) {
                self.paymentCallback?(false, "payment_cancelled".localized)
            }
        })
        
    }
    
    func webView(_ webView: WKWebView, didFailProvisionalNavigation navigation: WKNavigation!, withError error: Error) {
        view.makeToast(error.localizedDescription, position: .top)
    }

}
