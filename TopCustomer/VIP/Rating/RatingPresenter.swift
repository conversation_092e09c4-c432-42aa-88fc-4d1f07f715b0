
import UIKit

protocol RatingPresentationProtocol {
    func checkValidation(dictData:[String:Any]) -> Bool
    func apiCallRating(dictData:[String:Any])
    func apiResponseRating(response:AddRatingResponse?,error:Error?)

    func apiCallForSkipRating(dictData:[String:Any])
    func apiResponseSkipRating(response:SkipRatingResponse?,error:Error?)

}

class RatingPresenter: RatingPresentationProtocol {
    
    // MARK: Objects & Variables
    weak var viewControllerRating: RatingProtocol?
    var interactorRating: RatingInteractorProtocol?
    
    func checkValidation(dictData:[String:Any]) -> Bool {
        if setDataInString(dictData["dbRating"] as AnyObject) == 0 {
            AppSingletonObj.showAlert(strMessage: ObjKeymessages.kMSG_NO_RATING)
            return false
        }
        return true
    }

    func apiCallRating(dictData:[String:Any]) {
        self.interactorRating?.apiCallForRating(dictData: dictData)
    }

    func apiResponseRating(response:AddRatingResponse?,error:Error?) {
        if let error = error  {
            AppSingletonObj.showAlert(strMsg: error.localizedDescription)
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            return
        }
        
        if code == APIINVALIDAUTHORIZATIONCODE401 {
            AppSingletonObj.displaySessionExpiredAlert(strMsg: response.responseMessage ?? "")
            return
        }

        if code == APICODE400 {
            AppSingletonObj.showAlert(strMessage: response.responseMessage ?? "")
            return
        }
        
        self.viewControllerRating?.displayAlert(strMsg: response.responseMessage ?? "")
    }

    func apiCallForSkipRating(dictData:[String:Any]) {
        interactorRating?.apiCallForSkipRating(dictData: dictData)
    }

    func apiResponseSkipRating(response:SkipRatingResponse?,error:Error?) {
        if let error = error  {
            viewControllerRating?.displayNativeAlert(string: error.localizedDescription)
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            return
        }
        
        if code == APIINVALIDAUTHORIZATIONCODE401 {
            AppSingletonObj.displaySessionExpiredAlert(strMsg: response.responseMessage ?? "")
            return
        }

        if code == APICODE400 {
            viewControllerRating?.displayNativeAlert(string: response.responseMessage ?? "")
            return
        }
        
        self.viewControllerRating?.closeRatingPopup()
    }

}
