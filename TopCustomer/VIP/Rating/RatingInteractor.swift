
import UIKit

protocol RatingInteractorProtocol {
    func apiCallForRating(dictData:[String:Any])
    func apiCallForSkipRating(dictData:[String:Any])

}

protocol RatingDataStore {
    //{ get set }
}

class RatingInteractor: RatingInteractorProtocol, RatingDataStore {

    // MARK: Objects & Variables
    var presenterRating: RatingPresentationProtocol?
    
    func apiCallForRating(dictData:[String:Any]) {
        ActivityIndicator.shared.showCentralSpinner()
        
        let authorization = getAuthorizationText()

        RatingAPI.addRating(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, iOrderId: Int(setDataInString(dictData["iOrderId"] as AnyObject)) ?? 0, dbRating: setDataInString(dictData["dbRating"] as AnyObject), tComment: setDataInString(dictData["tComment"] as AnyObject)) { data, error in
            ActivityIndicator.shared.hideCentralSpinner()
            self.presenterRating?.apiResponseRating(response: data, error: error)
        }
        
    }

    func apiCallForSkipRating(dictData:[String:Any]) {
        
        ActivityIndicator.shared.showCentralSpinner()
        
        let authorization = getAuthorizationText()
        
        RatingAPI.skipRating(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, iOrderId: Int(setDataInString(dictData["iOrderId"] as AnyObject)) ?? 0) { data, error in
            ActivityIndicator.shared.hideCentralSpinner()
            self.presenterRating?.apiResponseSkipRating(response: data, error: error)
        }
        
    }

}
