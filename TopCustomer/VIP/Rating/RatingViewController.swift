
import UIKit
import IQKeyboardManagerSwift

protocol RatingProtocol: AnyObject {
    func displayAlert(strMsg: String)
    func displayNativeAlert(string: String)
    func closeRatingPopup()
    
}

protocol ReloadHistoryOrderListDelegate {
    func reloadScreen()
}

class RatingViewController: BaseViewController, RatingProtocol {
    
    // MARK: Objects & Variables
    var presenterRating: RatingPresentationProtocol?
    
    // MARK: IBOutlets
    @IBOutlet weak var btnSkip: UIButton!
    @IBOutlet weak var lblDidYouEnjoy: MaterialLocalizeLable!
    @IBOutlet weak var lblPleaseRate: MaterialLocalizeLable!
    @IBOutlet weak var textView: IQTextView!
    @IBOutlet weak var viewRating: FloatRatingView!
    @IBOutlet weak var btnSend: CustomRoundedButtton!
    
    var orderId = 0
    var rating = 0
    var delegate : ReloadHistoryOrderListDelegate?
    var strFromWhichScreen = ""
    
    // MARK: Object lifecycle
    /*
     override init(nibName nibNameOrNil: String?, bundle nibBundleOrNil: Bundle?) {
     super.init(nibName: nibNameOrNil, bundle: nibBundleOrNil)
     setup()
     }
     */
    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
        setup()
    }
    
    // MARK: Setup
    
    private func setup() {
        let viewController = self
        let interactor = RatingInteractor()
        let presenter = RatingPresenter()
        
        //View Controller will communicate with only presenter
        viewController.presenterRating = presenter
        
        //Presenter will communicate with Interector and Viewcontroller
        presenter.viewControllerRating = viewController
        presenter.interactorRating = interactor
        
        //Interactor will communucate with only presenter.
        interactor.presenterRating = presenter
    }
    
    // MARK: View lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        self.setTexts()
        
        viewRating.type = .wholeRatings
        viewRating.rating = 0.0
        viewRating.delegate = self
        
        if UserDefaults.standard.getLanguage()! == UserAPI.VLanguage_userLanguage.en.rawValue {       // english
            FloatRatingView.appearance().semanticContentAttribute = .forceLeftToRight
            viewRating.transform = CGAffineTransformMakeScale(1.0, 1.0)
        }
        else {
            FloatRatingView.appearance().semanticContentAttribute = .forceRightToLeft
            viewRating.transform = CGAffineTransformMakeScale(-1.0, 1.0)
        }
        
    }
    
    private func setTexts() {
        lblDidYouEnjoy.text = ObjKeymessages.kLABEL_ARE_YOU_SATISFIED
        lblPleaseRate.text = ObjKeymessages.kLABEL_PLEASE_RATE
        textView.placeholder = ObjKeymessages.kLABEL_LEAVE_A_REVIEW
        btnSend.setTitle(ObjKeymessages.kLABEL_SEND, for: .normal)
        
        let string = ObjKeymessages.kLABEL_SKIP
        let range = (string as NSString).range(of: ObjKeymessages.kLABEL_SKIP)
        let attributedString = NSMutableAttributedString(string: string)
        attributedString.addAttribute(NSAttributedString.Key.underlineStyle, value: NSNumber(value: 1), range: range)
        attributedString.addAttribute(NSAttributedString.Key.underlineColor, value: UIColor.black, range: range)
        attributedString.addAttribute(NSAttributedString.Key.font, value: UIFont(name: Fonts.LoewNextArabicMedium, size: 14)!, range: range)
        attributedString.addAttribute(NSAttributedString.Key.foregroundColor, value: UIColor.black, range: range)
        btnSkip.setAttributedTitle(attributedString, for: .normal)

        if UserDefaults.standard.getLanguage()! == UserAPI.VLanguage_userLanguage.en.rawValue {       // english
        }
        else {   // arabic
            if textView.textAlignment == NSTextAlignment.center {
            }
            else if textView.textAlignment == NSTextAlignment.right || textView.textAlignment == NSTextAlignment.left{
                textView.textAlignment = textView.textAlignment == NSTextAlignment.right ? NSTextAlignment.left : NSTextAlignment.right
            }
        }
        
    }
    
    @IBAction func btnDimViewAction(_ sender: Any) {
        self.view.endEditing(true)
        if self.strFromWhichScreen != "Home" {
            self.dismiss(animated: true, completion: nil)
        }
    }
    
    @IBAction func actionSend(_ sender: Any) {
        self.view.endEditing(true)
        if AppSingletonObj.isConnectedToNetwork(){
            var dictParam : [String:Any] = [:]
            dictParam["iOrderId"] = orderId
            dictParam["dbRating"] = self.rating
            dictParam["tComment"] = textView.text
            
            if self.presenterRating?.checkValidation(dictData: dictParam) ?? false {
                self.presenterRating?.apiCallRating(dictData: dictParam)
            }
        }
    }
    
    @IBAction func actionSkip(_ sender: Any) {
        self.view.endEditing(true)
        if self.strFromWhichScreen == "Home" { // call api for skip rating
            var dictParam : [String:Any] = [:]
            dictParam["iOrderId"] = orderId
            if AppSingletonObj.isConnectedToNetwork(){
                self.presenterRating?.apiCallForSkipRating(dictData: dictParam)
            }
        }
        else {
            self.dismiss(animated: true, completion: nil)
        }
    }
    
    func displayAlert(strMsg: String) {
        delegate?.reloadScreen()
        AppSingletonObj.showAlert(strMessage: strMsg)
        self.dismissVC(completion: nil)
    }
    
    func displayNativeAlert(string: String) {
        AppSingleton.shared.showCustomPopUpWithOkButton(strButtonTitle: ObjKeymessages.kLABEL_OK, strMessage: string, showOnTopVC: false) { (isOk) in
        }
    }
    
    func closeRatingPopup() {
        self.dismissVC(completion: nil)
    }
    
}

extension RatingViewController: FloatRatingViewDelegate {
    
    func floatRatingView(_ ratingView: FloatRatingView, didUpdate rating: Double) {
        print(rating)
        self.rating = Int(rating)
    }

}
