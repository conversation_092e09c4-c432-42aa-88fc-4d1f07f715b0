
import UIKit

protocol OrderDetailInteractorProtocol {
    func apiCallForGetOrderDetail(dictData:[String:Any])
    func apiCallForUpdateTransaction(dictData:[String:Any], paymentStatus: Int, strCardNumber: String, strCardType: String, strCardScheme: String, strCardToken: String, walletUsedOrNot: Int)
    func apiCallForAddCard(dictData:[String:Any])
    func apiCallForGetCountryList(flagNoNetwork: Bool)
    func apiCallEditAlternateNo(dictData:[String:Any])
    func changePaymentAfterPlaceorder(iOrderId: Int, tiTransactionType: Int, vTransactionRef: String, iPaymentStatus: Int, iUseWallet: Int?,
                                      vCardName: String?, strCardNumber: String, strCardType: String, strCardScheme: String, strCardToken: String)
}

protocol OrderDetailDataStore {
    //{ get set }
}

class OrderDetailInteractor: OrderDetailInteractorProtocol, OrderDetailDataStore {

    // MARK: Objects & Variables
    var presenterOrderDetail: OrderDetailPresentationProtocol?
    
    func apiCallForGetOrderDetail(dictData:[String:Any]) {
        
        ActivityIndicator.shared.showCentralSpinner()
        
        let authorization = getAuthorizationText()
        
        OrderAPI.getOrder(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, iOrderId: Int(setDataInString(dictData["iOrderId"] as AnyObject)) ?? 0) { data, error in
                ActivityIndicator.shared.hideCentralSpinner()
                self.presenterOrderDetail?.apiResponseGetOrderDetail(response: data, error: error)

        }
        
    }

    func apiCallForUpdateTransaction(dictData:[String:Any], paymentStatus: Int, strCardNumber: String, strCardType: String, strCardScheme: String, strCardToken: String, walletUsedOrNot: Int) {

        DispatchQueue.main.async {
            ActivityIndicator.shared.showCentralSpinner()
        }
        
        let authorization = getAuthorizationText()
        
        OrderAPI.orderTransactionUpdate(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, iOrderId: Int(setDataInString(dictData["iOrderId"] as AnyObject)) ?? 0, tiTransactionType: Int(setDataInString(dictData["tiTransactionType"] as AnyObject)) ?? 0, vTransactionRef: setDataInString(dictData["vTransactionRef"] as AnyObject), iPaymentStatus: Int(setDataInString(dictData["iPaymentStatus"] as AnyObject)) ?? 0, iUseWallet: walletUsedOrNot, vCardName: setDataInString(dictData["vCardName"] as AnyObject)) { data, error in
            ActivityIndicator.shared.hideCentralSpinner()
            self.presenterOrderDetail?.changePaymentAfterPlaceorderResponse(response: data, error: error, paymentStatus: paymentStatus, strCardNumber: strCardNumber, strCardType: strCardType, strCardScheme: strCardScheme, strCardToken: strCardToken, strTransactionId: setDataInString(dictData["vTransactionRef"] as AnyObject))
        }        
    }

    func apiCallForAddCard(dictData:[String:Any]) {
        let authorization = getAuthorizationText()

        UserCardsAPI.addUserCards(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, vToken: setDataInString(dictData["vToken"] as AnyObject), vTransactionId: setDataInString(dictData["vTransactionId"] as AnyObject), vPaymentDescription: setDataInString(dictData["vPaymentDescription"] as AnyObject), vCardType: setDataInString(dictData["vCardType"] as AnyObject), vCardScheme: setDataInString(dictData["vCardScheme"] as AnyObject)) { data, error in
            
            self.presenterOrderDetail?.apiResponseAddCard(response: data, error: error)

        }
    }

    func apiCallForGetCountryList(flagNoNetwork: Bool) {
        
        ActivityIndicator.shared.showCentralSpinner()
        
        CountryAPI.countryListing(accept: AcceptParamForHeader, lang: CurrentAppLang) { data, error in
            ActivityIndicator.shared.hideCentralSpinner()
            self.presenterOrderDetail?.apiResponseGetCountryList(response: data, error: error, flagNoNetwork: flagNoNetwork)

        }
        
    }

    func apiCallEditAlternateNo(dictData:[String:Any]) {
        ActivityIndicator.shared.showCentralSpinner()
        
        let authorization = getAuthorizationText()
            
        OrderAPI.updateAlternateNumber(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, iOrderId: Int(setDataInString(dictData["iOrderId"] as AnyObject)) ?? 0, vAlternativeISDCode: setDataInString(dictData["vAlternativeISDCode"] as AnyObject), vAlternativeMobileNumber: setDataInString(dictData["vAlternativeMobileNumber"] as AnyObject)) { data, error in
        
            ActivityIndicator.shared.hideCentralSpinner()
            self.presenterOrderDetail?.apiResponseEditAlternateNo(response: data, error: error)

        }
        
    }
    
    func changePaymentAfterPlaceorder(iOrderId: Int, tiTransactionType: Int, vTransactionRef: String, iPaymentStatus: Int, iUseWallet: Int?,
                                      vCardName: String?, strCardNumber: String, strCardType: String, strCardScheme: String, strCardToken: String) {
        
        DispatchQueue.main.async {
            ActivityIndicator.shared.showCentralSpinner()
        }
        
        let authorization = getAuthorizationText()
        
        OrderAPI.changePaymentAfterPlaceOrder(iOrderId: iOrderId, tiTransactionType: tiTransactionType, vTransactionRef: vTransactionRef, iPaymentStatus: iPaymentStatus, iUseWallet: iUseWallet, vCardName: vCardName) { data, error in
            ActivityIndicator.shared.hideCentralSpinner()
            
            self.presenterOrderDetail?.changePaymentAfterPlaceorderResponse(response: data, error: error, paymentStatus: iPaymentStatus, strCardNumber: strCardNumber, strCardType: strCardType, strCardScheme: strCardScheme, strCardToken: strCardToken, strTransactionId: vTransactionRef)
        }
    }

}
