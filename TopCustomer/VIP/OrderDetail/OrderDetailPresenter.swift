
import UIKit

protocol OrderDetailPresentationProtocol {
    func apiCallForGetOrderDetail(dictData:[String:Any])
    func apiResponseGetOrderDetail(response:OrderDetailResponse?,error:Error?)

    func apiCallForUpdateTransaction(dictData:[String:Any], paymentStatus: Int, strCardNumber: String, strCardType: String, strCardScheme: String, strCardToken: String, walletUsedOrNot: Int)

    func apiCallForAddCard(dictData:[String:Any])
    func apiResponseAddCard(response:CommonFields?,error:Error?)

    func apiCallForGetCountryList(flagNoNetwork: Bool)
    func apiResponseGetCountryList(response:CountryListResponse?,error:Error?, flagNoNetwork: Bool)

    func apiCallEditAlternateNo(dictData:[String:Any])
    func apiResponseEditAlternateNo(response:CommonFields?,error:Error?)

    func changePaymentAfterPlaceorder(iOrderId: Int, tiTransactionType: Int, vTransactionRef: String, iPaymentStatus: Int, iUseWallet: Int?,
                                      vCardName: String?, strCardNumber: String, strCardType: String, strCardScheme: String, strCardToken: String)
    func changePaymentAfterPlaceorderResponse(response:TransactionResponse?, error: Error?, paymentStatus: Int, strCardNumber: String, strCardType: String, strCardScheme: String, strCardToken: String, strTransactionId: String)
}

class OrderDetailPresenter: OrderDetailPresentationProtocol {
    
    // MARK: Objects & Variables
    weak var viewControllerOrderDetail: OrderDetailProtocol?
    var interactorOrderDetail: OrderDetailInteractorProtocol?
    
    func apiCallForGetOrderDetail(dictData:[String:Any]) {
        interactorOrderDetail?.apiCallForGetOrderDetail(dictData: dictData)
    }

    func apiResponseGetOrderDetail(response: OrderDetailResponse?, error: Error?) {
        if let error = error  {
            viewControllerOrderDetail?.displayAlert(string: error.localizedDescription)
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            return
        }
        
        if code == APIINVALIDAUTHORIZATIONCODE401 {
            AppSingletonObj.goToLoginScreen(message: response.responseMessage ?? "")
            return
        }

        if code == APICODE400 {
            viewControllerOrderDetail?.displayAlertAndDismissScreen(string: response.responseMessage ?? "")
            return
        }
        
        guard let model = response.responseData,code == APISUCCESSCODE200  else {
            return
        }
        self.viewControllerOrderDetail?.getOrderDetails(model: model)
    }

    func apiCallForUpdateTransaction(dictData:[String:Any], paymentStatus: Int, strCardNumber: String, strCardType: String, strCardScheme: String, strCardToken: String, walletUsedOrNot: Int) {
        self.interactorOrderDetail?.apiCallForUpdateTransaction(dictData: dictData, paymentStatus: paymentStatus, strCardNumber: strCardNumber, strCardType: strCardType, strCardScheme: strCardScheme, strCardToken: strCardToken, walletUsedOrNot: walletUsedOrNot)
    }

    func apiCallForAddCard(dictData:[String:Any]) {
        self.interactorOrderDetail?.apiCallForAddCard(dictData: dictData)
    }

    func apiResponseAddCard(response:CommonFields?,error:Error?) {
        if let error = error  {
            viewControllerOrderDetail?.displayAlert(string: error.localizedDescription)
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            return
        }
        
        if code == APIINVALIDAUTHORIZATIONCODE401 {
            AppSingletonObj.displaySessionExpiredAlert(strMsg: response.responseMessage ?? "")
            return
        }

        if code == APICODE400 {
            viewControllerOrderDetail?.displayAlert(string: response.responseMessage ?? "")
            return
        }
        
    }

    func apiCallForGetCountryList(flagNoNetwork: Bool) {
        interactorOrderDetail?.apiCallForGetCountryList(flagNoNetwork: flagNoNetwork)
    }

    func apiResponseGetCountryList(response: CountryListResponse?, error: Error?, flagNoNetwork: Bool) {
        if let error = error  {
            AppSingletonObj.showAlert(strMsg: error.localizedDescription)
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            return
        }
        
        if code == APICODE400 {
            AppSingletonObj.showAlert(strMsg: response.responseMessage ?? "")
            return
        }
        
        guard let model = response.responseData,code == APISUCCESSCODE200  else {
            return
        }
        self.viewControllerOrderDetail?.refreshCountryData(arrCountryData: model, flagNoNetwork: flagNoNetwork)
    }

    func apiCallEditAlternateNo(dictData:[String:Any]) {
        self.interactorOrderDetail?.apiCallEditAlternateNo(dictData: dictData)
    }

    func apiResponseEditAlternateNo(response:CommonFields?,error:Error?) {
        if let error = error  {
            AppSingletonObj.showAlert(strMsg: error.localizedDescription)
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            return
        }
        
        if code == APIINVALIDAUTHORIZATIONCODE401 {
            AppSingletonObj.displaySessionExpiredAlert(strMsg: response.responseMessage ?? "")
            return
        }

        if code == APICODE400 {
            AppSingletonObj.showAlert(strMessage: response.responseMessage ?? "")
            return
        }
        
        self.viewControllerOrderDetail?.reloadScreen(strMessage: response.responseMessage ?? "")
    }

    func changePaymentAfterPlaceorder(iOrderId: Int, tiTransactionType: Int, vTransactionRef: String, iPaymentStatus: Int, iUseWallet: Int?,
                                      vCardName: String?, strCardNumber: String, strCardType: String, strCardScheme: String, strCardToken: String) {
        debugPrint("changePaymentAfterPlaceorder")
        interactorOrderDetail?.changePaymentAfterPlaceorder(iOrderId: iOrderId, tiTransactionType: tiTransactionType, vTransactionRef: vTransactionRef, iPaymentStatus: iPaymentStatus, iUseWallet: iUseWallet, vCardName: vCardName, strCardNumber: strCardNumber,strCardType: strCardType, strCardScheme: strCardScheme, strCardToken: strCardToken)
    }
    
    
    func changePaymentAfterPlaceorderResponse(response:TransactionResponse?, error: Error?, paymentStatus: Int, strCardNumber: String, strCardType: String, strCardScheme: String, strCardToken: String, strTransactionId: String) {
        
        if let error = error  {
            viewControllerOrderDetail?.displayAlertForFailedCaseAndGoToCurrentOrders(strMsg: error.localizedDescription)
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            return
        }
        
        if code == APIINVALIDAUTHORIZATIONCODE401 {
            AppSingletonObj.displaySessionExpiredAlert(strMsg: response.responseMessage ?? "")
            return
        }

        if code == APICODE400 {
            AppSingletonObj.showAlert(strMsg: response.responseMessage ?? "")
            return
        }
        
        if code == APICODE203 {
            if paymentStatus == 1 {  // success
                self.viewControllerOrderDetail?.displayErrorAlert(strMsg: response.responseMessage ?? "")
            }
            else {
                self.viewControllerOrderDetail?.displayErrorAlertAndGoToHome(strMsg: response.responseMessage ?? "")
            }
            return
        }

        guard let model = response.responseData,code == APISUCCESSCODE200  else {
            return
        }
        // to fix freeze app after Apple Pay Done
        if isApplePayPayment == true {
            NotificationCenter.default.post(name: NSNotification.Name("OpenThanksAlert"), object: nil)
            return
        }

        if paymentStatus == 1 || paymentStatus == 0 {  // success
            self.viewControllerOrderDetail?.showThankyouAlertPayment(strCardNumber: strCardNumber, strCardType: strCardType, strCardScheme: strCardScheme, strCardToken: strCardToken, strTransactionId: strTransactionId, strLatestWalletBalance: model.latestWalletBalance ?? "")
        }
        
    }
    
}
