
import UIKit
import PaymentSDK
import SwiftyJSON
import Mixpanel

protocol OrderDetailProtocol: AnyObject {
    func displayAlert(string:String)
    func getOrderDetails(model : OrderDetailResponseFields)
    func displayErrorAlert(strMsg:String)
    func displayErrorAlertAndGoToHome(strMsg:String)
    func showThankyouAlertPayment(strCardNumber: String, strCardType: String, strCardScheme: String, strCardToken: String, strTransactionId: String, strLatestWalletBalance: String)
    func refreshCountryData(arrCountryData : [CountryListResponseFields], flagNoNetwork: Bool)
    func reloadScreen(strMessage :  String)
    func displayAlertForFailedCaseAndGoToCurrentOrders(strMsg: String)
    func displayAlertAndDismissScreen(string: String)
}

class OrderDetailViewController: BottomPopupViewController {

    // MARK: Objects & Variables
    var presenterOrderDetail: OrderDetailPresentationProtocol?

    // MARK: IBOutlets
    @IBOutlet weak var tblOrders: UITableView!
    @IBOutlet weak var tblOrdersHeightConstraints: NSLayoutConstraint!
    @IBOutlet weak var btnCancelOrder: UIButton!
    @IBOutlet weak var lblTitle: UILabel!
    @IBOutlet weak var lblOrderNoTitle: UILabel!
    @IBOutlet weak var lblOrderDate: UILabel!
    @IBOutlet weak var lblDetailsTitle: UILabel!
    @IBOutlet weak var lblDeliveryTitle: UILabel!
    @IBOutlet weak var lblTotalTitle: UILabel!
    @IBOutlet weak var lblOrderStatus: UILabel!
    @IBOutlet weak var lblStatusOrderReceived: UILabel!
    @IBOutlet weak var lblStatusOnTheWay: UILabel!
    @IBOutlet weak var btnTracking: UIButton!
    @IBOutlet weak var lblStatusDelivered: UILabel!
    @IBOutlet weak var lblOrderNoValue: UILabel!
    @IBOutlet weak var lblOrderDateValue: UILabel!
    @IBOutlet weak var lblDeliveryValue: UILabel!
    @IBOutlet weak var lblTotalValue: UILabel!
    @IBOutlet weak var imgOrderReceived: UIImageView!
    @IBOutlet weak var imgOrderOnTheWay: UIImageView!
    @IBOutlet weak var imgOrderDelivered: UIImageView!
    @IBOutlet weak var scrollView: UIScrollView!
    @IBOutlet weak var lblVatValue: UILabel!
    @IBOutlet weak var lblExpectedDeliveryTime: UILabel!
    @IBOutlet weak var lblVatTitle: UILabel!
    @IBOutlet weak var lblCancelNote: UILabel!
    @IBOutlet weak var lblPaymentMethodTitle: UILabel!
    @IBOutlet weak var lblPaymentMethodValue: UILabel!
    @IBOutlet weak var imgOrderReady: UIImageView!
    @IBOutlet weak var lblStatusOrderReady: UILabel!
    @IBOutlet weak var stackViewCall: UIStackView!
    @IBOutlet weak var cons_width_stackViewCall: NSLayoutConstraint!
    @IBOutlet weak var viewStatus: UIView!
    @IBOutlet weak var btnPayNow: CustomRoundedButtton!
    @IBOutlet weak var cons_height_viewPaymentMethod: NSLayoutConstraint!
    @IBOutlet weak var viewPaymentMethod: UIView!
    @IBOutlet weak var lblDiscountTitle: UILabel!
    @IBOutlet weak var lblDiscountValue: UILabel!
    @IBOutlet weak var lblAlternateNoTitle: UILabel!
    @IBOutlet weak var stackViewDiscount: UIStackView!
    @IBOutlet weak var lblDeliveryDiscountTitle: MaterialLocalizeLable!
    @IBOutlet weak var lblDeliveryDiscountValue: MaterialLocalizeLable!
    @IBOutlet weak var stackViewDeliveryDiscount: UIStackView!
    @IBOutlet weak var lblSubTotalTitle: UILabel!
    @IBOutlet weak var lblSubTotalValue: UILabel!
    @IBOutlet weak var btnCountryCode: UIButton!
    @IBOutlet weak var txtMobNo: UITextField!
    @IBOutlet weak var btnEditMoNo: UIButton!
    @IBOutlet weak var lblUponArrival: MaterialLocalizeLable!
    @IBOutlet weak var cons_top_lblExpectedDeliveryTime: NSLayoutConstraint!
    @IBOutlet weak var lblType: UILabel!
    @IBOutlet weak var lblAddress: UILabel!
    @IBOutlet weak var lblShift: UILabel!
    @IBOutlet weak var lblNote: UILabel!
    @IBOutlet weak var lblDeliveryAddressTitle: UILabel!
    @IBOutlet weak var lblDeliveryShiftTitle: UILabel!
    @IBOutlet weak var lblNotesTitle: UILabel!
    @IBOutlet weak var cons_bottom_viewNotes: NSLayoutConstraint!
    @IBOutlet weak var viewNote: UIView!
    @IBOutlet weak var lblOrderTypeTitle: MaterialLocalizeLable!
    @IBOutlet weak var lblOrderTypeValue: MaterialLocalizeLable!
    @IBOutlet weak var stackViewMain: UIStackView!
    @IBOutlet weak var changePaymentBtn: UIButton!
    @IBOutlet weak var stackCoinsDiscount: UIStackView!
    @IBOutlet weak var lblCoinsDiscount: MaterialLocalizeLable!
    @IBOutlet weak var lblCoinsDiscountHint: MaterialLocalizeLable!
    @IBOutlet weak var lblCoinsDiscountValue: MaterialLocalizeLable!
    
    var showCancelOrderPopup: (()->())?
    var refreshOrderData: (()->())?
    var isAddNewCard = false
    
    var orderId = 0
    var objOrderDetails: OrderDetailResponseFields?
    var splitPaymentOrNot = 0
    var orderAmount = 0.0
    var showUpdateTransactionAlert: ((String)->())?
    var arrCountry: [CountryListResponseFields] = []
    let duration = 1.0
    let buttonUnderline: [NSAttributedString.Key: Any] = [
        .font: UIFont.systemFont(ofSize: 12),
        .foregroundColor: UIColor.gray,
        .underlineStyle: NSUnderlineStyle.single.rawValue
    ] // .double.rawValue, .thick.rawValue
    // 1 - Card, 2 - Apple Pay, 3 - COD , 4- Wallet, 5 - Cash, 6 - POS
    var transactionType = TransactionType.Card
    var cardTransactionReference = ""
    // Transaction reference id (for COD = COD, For Wallet = Wallet, For Cash = COD-CASH, For POS = COD-POS)
    var vTransactionRef = ""
    // 0-False, 1-True
    var iUseWallet: Int?

    override var popupHeight: CGFloat { return 270 }
    // tabby Payment
    let tabbyPay = TabyPaymentManager()
    weak var delegate: ChoosePaymentDelegate?

    // MARK: Object lifecycle
    /*
    override init(nibName nibNameOrNil: String?, bundle nibBundleOrNil: Bundle?) {
        super.init(nibName: nibNameOrNil, bundle: nibBundleOrNil)
        setup()
    }
    */
    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
        setup()
    }
    
    // MARK: Setup
    
    private func setup() {
        let viewController = self
        let interactor = OrderDetailInteractor()
        let presenter = OrderDetailPresenter()
        
        //View Controller will communicate with only presenter
        viewController.presenterOrderDetail = presenter
        
        //Presenter will communicate with Interector and Viewcontroller
        presenter.viewControllerOrderDetail = viewController
        presenter.interactorOrderDetail = interactor
        
        //Interactor will communucate with only presenter.
        interactor.presenterOrderDetail = presenter
        
        // Tabby Payment
        self.tabbyPay.vc = self
        self.tabbyPay.delegate = self
    }
    
    
    // MARK: View lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        NotificationCenter.default.addObserver(self, selector: #selector(openThanksAlert), name: NSNotification.Name(rawValue: "OpenThanksAlert"), object: nil)
        initialSetUp()
        
        // Mixpanel
        MixpanelEvents.sharedInstance.logTimeEvent(strScreenName: ScreenNames.CurrentOrderDetailScreen.rawValue)

        if User.shared.checkUserLoginStatus() {
            SocketIOManager.shared.checkAndHandleReconnectSocket()
        }

        self.setTexts()
        self.getOrderDetails()
        self.setDriverStatus()
        self.getCountryList(flagNoNetwork: false)
    }
    
    @objc func openThanksAlert(_ notification: NSNotification) {
        let vc = ProductPopupStoryboard.instantiate(ThankYouViewController.self)
        vc.modalPresentationStyle = .overFullScreen
        vc.modalTransitionStyle = .crossDissolve
        vc.message = "payment_changed".localized
        
        vc.completionBlock = { [weak self] (strAction) in
            guard let self = self else { return }
            self.dismiss(animated: false, completion: nil)
            if strAction == "Complete" {
                self.dismiss(animated: true, completion: nil)
            }
        }
        self.present(vc, animated: true)
    }
    
    private func checkFromPayment() {
        if objOrderDetails?.tiTransactionType == TransactionType.COD.rawValue || objOrderDetails?.tiTransactionType == TransactionType.Wallet.rawValue {
            self.changePaymentBtn.isHidden = false
        } else {
            self.changePaymentBtn.isHidden = true
        }
    }

    private func getCountryList(flagNoNetwork: Bool) {
        if AppSingletonObj.isConnectedToNetworkForCheckoutScreen(){
            self.presenterOrderDetail?.apiCallForGetCountryList(flagNoNetwork: flagNoNetwork)
        }
    }

    override func viewDidDisappear(_ animated: Bool) {
        MixpanelEvents.sharedInstance.trackTimeEvent(strScreenName: ScreenNames.CurrentOrderDetailScreen.rawValue)
    }

    func setDriverStatus() {
        SocketIOManager.shared.socket.on("notify_order_status_to_customer") { (data, ack) in
            
            let dict = data.first as? [String:Any]
            let orderId = Int(setDataInString(dict?["iOrderId"] as AnyObject))
            let order_status = Int(setDataInString(dict?["tiOrderStatus"] as AnyObject))
            let driver_mobile = setDataInString(dict?["vDriverMobile"] as AnyObject)
            let startTime = setDataInString(dict?["tsExpectedStartTime"] as AnyObject)
            let endTime = setDataInString(dict?["tsExpectedEndTime"] as AnyObject)

            if orderId == self.objOrderDetails?.iOrderId {  // update only for correct order
                
                var flagDirectStep = false
                if ((order_status ?? 0) - (self.objOrderDetails?.tiOrderStatus ?? 0)) > 1 {
                    flagDirectStep = true
                }
                else {
                    flagDirectStep = false
                }
                
                if order_status == self.objOrderDetails?.tiOrderStatus {
                    return
                }
                
                // update two values in current object
                self.objOrderDetails?.tiOrderStatus = order_status
                self.objOrderDetails?.vDriverMobile = driver_mobile
                self.objOrderDetails?.tsExpectedStartTime = startTime
                self.objOrderDetails?.tsExpectedEndTime = endTime

                self.setExpectedDeliveryTimeLabel()
                self.updateUIAccordingToOrderStatus(isFromSocket: true, flagDirectStep: flagDirectStep)
            }

        }
    }

    func dataSerializationToJson(data: [Any],_ description : String = "") -> (status: Bool, json: JSON){
        let json = JSON(data)
        print (description, ": \(json)")
        return (true, json)
    }

    private func getOrderDetails() {
        var dictParam : [String:Any] = [:]
        dictParam["iOrderId"] = orderId

        if AppSingletonObj.isConnectedToNetworkForCheckoutScreen(){
            self.presenterOrderDetail?.apiCallForGetOrderDetail(dictData: dictParam)
        }
        else {
            self.displayAlertAndCloseScreen(strMsg: ObjKeymessages.kMSG_NO_INTERNET)
        }
    }

    func displayAlertAndCloseScreen(strMsg: String) {
        DispatchQueue.main.async {
            AppSingleton.shared.showCustomPopUpWithOkButton(strButtonTitle: ObjKeymessages.kLABEL_OK, strMessage: strMsg, showOnTopVC: false) { (isOk) in
                if isOk == true {
                    self.dismiss(animated: true, completion: nil)
                }
            }
        }
    }

    private func setTexts() {
        lblTitle.text = ObjKeymessages.kLABEL_MY_ORDER
        lblOrderNoTitle.text = "\(ObjKeymessages.kLABEL_ORDER_NO):"
        lblOrderDate.text = "\(ObjKeymessages.kLABEL_DATE):"
        lblDetailsTitle.text = ObjKeymessages.kLABEL_DETAILS
        lblDeliveryTitle.text = ObjKeymessages.kLABEL_DELIVERY
        lblTotalTitle.text = ObjKeymessages.kLABEL_TOTAL
        lblOrderStatus.text = ObjKeymessages.kLABEL_ORDER_STATUS
        lblStatusOrderReceived.text = ObjKeymessages.kLABEL_ORDER_RECEIVED
        lblStatusOnTheWay.text = ObjKeymessages.kLABEL_ON_THE_WAY
        btnTracking.setTitle(ObjKeymessages.kLABEL_TRACKING, for: .normal)
        lblStatusDelivered.text = ObjKeymessages.kLABEL_DELIVERED
        lblExpectedDeliveryTime.text = ObjKeymessages.kLABEL_EXPECTED_DELIVERY_TIME
        lblVatTitle.text = ObjKeymessages.kLABEL_VAT
        lblCancelNote.text = "* \(ObjKeymessages.kMSG_ORDER_CANCEL_NOTE)"
        lblPaymentMethodTitle.text = ObjKeymessages.kLABEL_PAYMENT_METHOD
        lblStatusOrderReady.text = ObjKeymessages.kLABEL_READY
        btnPayNow.setTitle(ObjKeymessages.kLABEL_PAY_NOW, for: .normal)
        lblDiscountTitle.text = "\(ObjKeymessages.kLABEL_DISCOUNT)"
        lblDeliveryDiscountTitle.text = ObjKeymessages.kLABEL_DELIVERY_DISCOUNT
        lblSubTotalTitle.text = "\(ObjKeymessages.kLABEL_SUBTOTAL) (\(ObjKeymessages.kLABEL_WITHOUT_VAT))"

        lblAlternateNoTitle.text = ObjKeymessages.kLABEL_DRIVER_WILL_CALL
        txtMobNo.placeholder = ObjKeymessages.kLABEL_PHONE_NO
        lblUponArrival.text = ObjKeymessages.kLABEL_UPON_ARRIVAL

        lblDeliveryAddressTitle.text = ObjKeymessages.kLABEL_DELIVERY_ADDRESS
        lblDeliveryShiftTitle.text = ObjKeymessages.kLABEL_DELIVERY_SHIFT
        lblNotesTitle.text = ObjKeymessages.kLABEL_NOTES

        lblOrderTypeTitle.text = ObjKeymessages.kLABEL_ORDER_TYPE

        btnCountryCode.setTitle(SACountryCode, for: .normal)
        self.lblCoinsDiscount.text = "Discount".localized
        self.lblCoinsDiscountHint.text = "Coins discount".localized
        
        let string = ObjKeymessages.kLABEL_CANCEL_ORDER
        let range = (string as NSString).range(of: ObjKeymessages.kLABEL_CANCEL_ORDER)
        let attributedString = NSMutableAttributedString(string: string)
        attributedString.addAttribute(NSAttributedString.Key.underlineStyle, value: NSNumber(value: 1), range: range)
        attributedString.addAttribute(NSAttributedString.Key.underlineColor, value: UIColor.AppTheme_BlueColor_012CDA, range: range)
        attributedString.addAttribute(NSAttributedString.Key.font, value: UIFont(name: Fonts.LoewNextArabicBold, size: 12)!, range: range)
        attributedString.addAttribute(NSAttributedString.Key.foregroundColor, value: UIColor.AppTheme_BlueColor_012CDA, range: range)
        btnCancelOrder.setAttributedTitle(attributedString, for: .normal)

        let stringTrackOrder = ObjKeymessages.kLABEL_TRACKING
        let rangeTrackOrder = (stringTrackOrder as NSString).range(of: ObjKeymessages.kLABEL_TRACKING)
        let attributedStringTrackOrder = NSMutableAttributedString(string: stringTrackOrder)
        attributedStringTrackOrder.addAttribute(NSAttributedString.Key.underlineStyle, value: NSNumber(value: 1), range: rangeTrackOrder)
        attributedStringTrackOrder.addAttribute(NSAttributedString.Key.underlineColor, value: UIColor.AppTheme_BlueColor_012CDA, range: rangeTrackOrder)
        attributedStringTrackOrder.addAttribute(NSAttributedString.Key.font, value: UIFont(name: Fonts.LoewNextArabicBold, size: 12)!, range: rangeTrackOrder)
        attributedStringTrackOrder.addAttribute(NSAttributedString.Key.foregroundColor, value: UIColor.AppTheme_BlueColor_012CDA, range: rangeTrackOrder)
        btnTracking.setAttributedTitle(attributedStringTrackOrder, for: .normal)

        if UserDefaults.standard.getLanguage()! == UserAPI.VLanguage_userLanguage.en.rawValue {       // english
        }
        else {   // arabic
            if txtMobNo.textAlignment == NSTextAlignment.center {
            }
            else if txtMobNo.textAlignment == NSTextAlignment.right || txtMobNo.textAlignment == NSTextAlignment.left{
                txtMobNo.textAlignment = txtMobNo.textAlignment == NSTextAlignment.right ? NSTextAlignment.left : NSTextAlignment.right
            }

        }

    }
    
    @IBAction func editMoNoAction(_ sender: Any) {
        if txtMobNo.isUserInteractionEnabled {
            self.view.endEditing(true)
            if AppSingletonObj.isConnectedToNetwork() {
                var dictParam : [String:Any] = [:]
                dictParam["iOrderId"] = orderId
                dictParam["vAlternativeISDCode"] = btnCountryCode.titleLabel?.text
                dictParam["vAlternativeMobileNumber"] = txtMobNo.text

                let result = checkValidation(dictData: dictParam)
                if result {
                    self.presenterOrderDetail?.apiCallEditAlternateNo(dictData: dictParam)
                }
            }
        }
        else {
            btnEditMoNo.isSelected = !btnEditMoNo.isSelected
            btnCountryCode.isUserInteractionEnabled = !btnCountryCode.isUserInteractionEnabled
            txtMobNo.isUserInteractionEnabled = !txtMobNo.isUserInteractionEnabled
            txtMobNo.becomeFirstResponder()
        }
    }

    func checkValidation(dictData:[String:Any]) -> Bool {
        
        if setDataInString(dictData["vAlternativeISDCode"] as AnyObject).isBlank {
            AppSingletonObj.showAlert(strMessage: ObjKeymessages.kALERT_ISD_CODE)
            return false
        }
        else if setDataInString(dictData["vAlternativeMobileNumber"] as AnyObject).isBlank {
            AppSingletonObj.showAlert(strMessage: ObjKeymessages.kALERT_MOBILE_NUMBER)
            return false
        }
        else if setDataInString(dictData["vAlternativeMobileNumber"] as AnyObject).count < MinMoNoLength || setDataInString(dictData["vAlternativeMobileNumber"] as AnyObject).count > MaxMoNoLength {
            AppSingletonObj.showAlert(strMessage: ObjKeymessages.kALERT_MOBILE_NUMBER_TEN_DIGITS)
            return false
        }
        return true
    }

    private func initialSetUp() {
        self.tblOrders.registerCell(cell: HistoryOrderDetailsTableViewCell.self)
        self.tblOrders.delegate = self
        self.tblOrders.dataSource = self
        let attributeString = NSMutableAttributedString(
            string: "Change".localized,
            attributes: buttonUnderline)
        changePaymentBtn.setAttributedTitle(attributeString, for: .normal)
    }

    @IBAction func actionCountryCode(_ sender: Any) {
        self.view.endEditing(true)
        
        if AppSingletonObj.isConnectedToNetworkForCheckoutScreen() {
            if arrCountry.count <= 0 {  // Do not have country list, call API
                self.getCountryList(flagNoNetwork: true)
            }
            else {  // Having country list, open country picker
                let countryView = CountrySelectView()
                countryView.show()
                countryView.selectedCountryCallBack = {[weak self] (countryDic) -> Void in
                    self?.countryDictSetUp(countryDic: countryDic)
                }
            }
        }
    }
    // change payment
    @IBAction func changePaymentBtnTapped(_ sender: Any) {
        
        if objOrderDetails?.tiTransactionType == TransactionType.COD.rawValue || objOrderDetails?.tiTransactionType == TransactionType.Wallet.rawValue {
        let vc = PaymentsStoryboard.instantiate(ChoosePaymentViewController.self)
        vc.delegate = self
        vc.modalPresentationStyle = .overFullScreen
        vc.modalTransitionStyle = .crossDissolve
        vc.grandTotal = objOrderDetails?.dTotalAmount ?? ""
        vc.codSelected = {
            DispatchQueue.main.async {
                AppSingletonObj.showAlert(strMessage: "you can't change the payment method to card on delivery".localized)
            }
        }
        
        vc.walletSelected = { [weak self] () in
            DispatchQueue.main.async {
                self?.transactionType = .Wallet
                self?.cardTransactionReference = ""
                self?.iUseWallet = 1
                self?.vTransactionRef = "Wallet"
                self?.makePaymentToPayTabs(transactionType: .Wallet, flagCardOrNot: false, strCardToken: "", strCardTransactionReference: "", flagSplitWallet: false, strRemainingAmt: self?.objOrderDetails?.dTotalAmount ?? "0")
            }
        }
        
        vc.applePaySelected = { [weak self] () in
            DispatchQueue.main.async {
                self?.splitPaymentOrNot = 0
                self?.transactionType = .ApplePay
                self?.cardTransactionReference = ""
                self?.iUseWallet = 0
                self?.vTransactionRef = "Apple Pay"
                self?.makePaymentToPayTabs(transactionType: .ApplePay, flagCardOrNot: false, strCardToken: "", strCardTransactionReference: "", flagSplitWallet: false, strRemainingAmt: self?.objOrderDetails?.dTotalAmount ?? "0")
            }
        }
        
        vc.passSelectedCardData = { [weak self] (cardToken, cardTransactionReference, flag) in
            DispatchQueue.main.async {
                self?.splitPaymentOrNot = 0
                self?.transactionType = .Card
                self?.cardTransactionReference = cardTransactionReference
                self?.iUseWallet = 0
                self?.vTransactionRef = "Card"
                self?.makePaymentToPayTabs(transactionType: .Card, flagCardOrNot: true, strCardToken: cardToken, strCardTransactionReference: cardTransactionReference, flagSplitWallet: false, strRemainingAmt: self?.objOrderDetails?.dTotalAmount ?? "0")
            }
        }
            
            vc.splitWalletSelected = { [weak self] (cardToken, cardTransactionReference, flag, strPaymentType, strRemainingAmountAfterWallet) in
                DispatchQueue.main.async {
                    self?.splitPaymentOrNot = 1
                    
                    if strPaymentType == "Card" {
                        self?.makePaymentToPayTabs(transactionType: .Card, flagCardOrNot: true, strCardToken: cardToken, strCardTransactionReference: cardTransactionReference, flagSplitWallet: flag, strRemainingAmt: strRemainingAmountAfterWallet)
                    } else if strPaymentType == "ApplePay" {
                        self?.makePaymentToPayTabs(transactionType: .ApplePay, flagCardOrNot: false, strCardToken: "", strCardTransactionReference: cardTransactionReference, flagSplitWallet: true, strRemainingAmt: strRemainingAmountAfterWallet)

                    }
                    
                }
            }
        self.presentVC(vc)
        } else {
            self.showAlert1(title: "", message: "You can't change the method after payment".localized)
        }
    }
    
    func makePaymentToPayTabs(transactionType: TransactionType, flagCardOrNot: Bool, strCardToken: String, strCardTransactionReference: String, flagSplitWallet: Bool, strRemainingAmt: String) {
        let model = CreateOrderResponseFields(iOrderId: self.objOrderDetails?.iOrderId,
                                  vOrderNumber: self.objOrderDetails?.vOrderNumber,
                                  tsOrderedAt: self.objOrderDetails?.tsOrderedAt,
                                  tiOrderStatus: self.objOrderDetails?.tiOrderStatus,
                                  dOrderTotal: self.objOrderDetails?.dTotalAmount)
        orderId = (model.iOrderId ?? 0)
        var walletUsedOrnot = UsedWalletOrNot.NotUsed.rawValue
        if flagSplitWallet == true {
            walletUsedOrnot = UsedWalletOrNot.Used.rawValue
        }
        
        if transactionType == TransactionType.Card {
            self.payWithCard(flagCardOrNot: flagCardOrNot, strCardToken: strCardToken, strCardTransactionReference:
                                strCardTransactionReference, flagSplitWallet: flagCardOrNot, strRemainingAmt: strRemainingAmt)
        }
        else if transactionType == TransactionType.ApplePay {
            self.payWithApplePay(flagSplitWallet: flagSplitWallet, strRemainingAmt: strRemainingAmt)
        }
        else if transactionType == TransactionType.Wallet {
            self.updateTransaction(strTransactionReference: "Wallet", paymentStatus: 1, strCardNumber: "", strCardType: "", strCardScheme: "", strCardToken: "", transactionType: TransactionType.Wallet.rawValue, walletUsedOrNot: 1)
        }
    }
    
    func countryDictSetUp(countryDic: [String: Any]?){
        if countryDic == nil {
            self.btnCountryCode.setTitle(SACountryCode, for: .normal)
            if let regCode = Locale.current.regionCode {
                if let dict = CountryCodeJson.filter({$0["locale"] as? String == regCode}).first {
                    self.btnCountryCode.setTitle("+\(dict["code"] as! NSNumber)", for: .normal)
                }
            }
        }
        else {
            self.btnCountryCode.setTitle("+\(countryDic!["code"] as! NSNumber)", for: .normal)
        }
    }

    @IBAction func btnDimViewAction(_ sender: Any) {
        self.view.endEditing(true)
        self.dismiss(animated: true, completion: nil)
    }

    @IBAction func cancelOrderAction(_ sender: Any) {
        self.view.endEditing(true)
        self.showCancelOrderPopup?()
        self.dismiss(animated: true, completion: nil)
    }
    
    @IBAction func actionTracking(_ sender: Any) {
        self.view.endEditing(true)

        let vc = ProductPopupStoryboard.instantiate(TrackOrderViewController.self)
        vc.modalPresentationStyle = .overFullScreen
        vc.modalTransitionStyle = .crossDissolve
        vc.customerLat = ((objOrderDetails?.dOrderLatitude)?.toDouble() ?? 0.0)
        vc.customerLong = ((objOrderDetails?.dOrderLongitude)?.toDouble() ?? 0.0)
        vc.driverLat = ((objOrderDetails?.dDriverLatitude)?.toDouble() ?? 0.0)
        vc.driverLong = ((objOrderDetails?.dDriverLongitude)?.toDouble() ?? 0.0)
        vc.orderID = objOrderDetails?.iOrderId ?? 0        
        vc.strCarAdditionalCar = objOrderDetails?.additionalSecondsEstForCar ?? ""
        
        DispatchQueue.main.async {
            self.presentVC(vc)
        }
    }
    
    @IBAction func phoneAction(_ sender: Any) {
        self.view.endEditing(true)
        if objOrderDetails?.vDriverMobile != "" {  // driver mobile number available
            let strPhoneNo = objOrderDetails?.vDriverMobile
            let strTrimmedPhoneNo = strPhoneNo?.replacingOccurrences(of: " ", with: "")
            if let url = URL(string: "tel://\(strTrimmedPhoneNo ?? "")"), UIApplication.shared.canOpenURL(url) {
                UIApplication.shared.open(url)
            }
        }
    }
    
    @IBAction func whatsAppAction(_ sender: Any) {
        self.view.endEditing(true)
        if objOrderDetails?.vDriverMobile != "" {  // driver mobile number available
            let strPhoneNo = objOrderDetails?.vDriverMobile
            let strSpaceTrimmedPhoneNo = strPhoneNo?.replacingOccurrences(of: " ", with: "")
            let strPlusTrimmedPhoneNo = strSpaceTrimmedPhoneNo?.replacingOccurrences(of: "+", with: "")

            var urlWhats = ""
            urlWhats = "https://api.whatsapp.com/send?phone=\(strPlusTrimmedPhoneNo ?? "")"

            if let urlString = urlWhats.addingPercentEncoding(withAllowedCharacters: NSCharacterSet.urlQueryAllowed) {
                if let whatsappURL = URL(string: urlString) {
                    if UIApplication.shared.canOpenURL(whatsappURL) {
                        UIApplication.shared.open(whatsappURL)
                    } else {
                        print("Install Whatsapp")
                    }
                }
            }
        }
    }

    @IBAction func actionPayNow(_ sender: Any) {
        self.view.endEditing(true)

        let vc = PaymentsStoryboard.instantiate(ChoosePaymentViewController.self)
        vc.modalPresentationStyle = .overFullScreen
        vc.modalTransitionStyle = .crossDissolve
        
        vc.grandTotal = objOrderDetails?.dTotalAmount ?? ""
        
        vc.passSelectedCardData = { [weak self] (cardToken, cardTransactionReference, isAddNewCard) in
            DispatchQueue.main.async {
                
                self?.splitPaymentOrNot = 0

                self?.isAddNewCard = isAddNewCard
                self?.payWithCard(flagCardOrNot: isAddNewCard, strCardToken: cardToken, strCardTransactionReference:
                                    cardTransactionReference, flagSplitWallet: false, strRemainingAmt: "")
            }
        }
        
        vc.applePaySelected = { [weak self] () in
            DispatchQueue.main.async {
                
                self?.splitPaymentOrNot = 0

                self?.payWithApplePay(flagSplitWallet: false, strRemainingAmt: "")
            }
        }

        vc.codSelected = { [weak self] () in
            DispatchQueue.main.async {
                self?.updateTransaction(strTransactionReference: TransactionTypeCOD, paymentStatus: 0, strCardNumber: "", strCardType: "", strCardScheme: "", strCardToken: "", transactionType: TransactionType.COD.rawValue, walletUsedOrNot: UsedWalletOrNot.NotUsed.rawValue)
            }
        }

        vc.walletSelected = { [weak self] () in
            DispatchQueue.main.async {
                self?.updateTransaction(strTransactionReference: TransactionTypeWallet, paymentStatus: 1, strCardNumber: "", strCardType: "", strCardScheme: "", strCardToken: "", transactionType: TransactionType.Wallet.rawValue, walletUsedOrNot: UsedWalletOrNot.NotUsed.rawValue)
            }
        }

        vc.splitWalletSelected = { [weak self] (cardToken, cardTransactionReference, flag, strPaymentType, strRemainingAmountAfterWallet) in
            DispatchQueue.main.async {
                
                self?.splitPaymentOrNot = 1
                
                if strPaymentType == "Card" {
                    self?.isAddNewCard = flag
                    self?.payWithCard(flagCardOrNot: flag, strCardToken: cardToken, strCardTransactionReference:
                                        cardTransactionReference, flagSplitWallet: true, strRemainingAmt: strRemainingAmountAfterWallet)
                }
                else if strPaymentType == "COD" {
                    self?.updateTransaction(strTransactionReference: TransactionTypeCOD, paymentStatus: 0, strCardNumber: "", strCardType: "", strCardScheme: "", strCardToken: "", transactionType: TransactionType.COD.rawValue, walletUsedOrNot: UsedWalletOrNot.Used.rawValue)
                }
                else if strPaymentType == "ApplePay" {
                    self?.payWithApplePay(flagSplitWallet: true, strRemainingAmt: strRemainingAmountAfterWallet)
                }

            }
        }

        self.presentVC(vc)

    }

}

extension OrderDetailViewController: UITableViewDelegate, UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return objOrderDetails?.productDetails?.count ?? 0
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeue(with: HistoryOrderDetailsTableViewCell.self, for: indexPath)
        
        cell.lblQuantity.text = "X\(forTrailingZero(temp: objOrderDetails?.productDetails?[indexPath.row].iProductQuantity ?? 0))"
        cell.lblName.text = "\(objOrderDetails?.productDetails?[indexPath.row].vProductName ?? "") \(objOrderDetails?.productDetails?[indexPath.row].vProductUnit ?? "")"
        cell.lblPrice.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: objOrderDetails?.productDetails?[indexPath.row].dbPrice?.toDouble() ?? 0.0))")

        return cell
    }
    
    func tableView(_ tableView: UITableView, willDisplay cell: UITableViewCell, forRowAt indexPath: IndexPath) {
        self.tblOrders.layoutIfNeeded()
        self.tblOrdersHeightConstraints.constant = self.tblOrders.contentSize.height
    }
}

extension OrderDetailViewController: OrderDetailProtocol {
    
    func displayAlert(string: String) {
        AppSingleton.shared.showCustomPopUpWithOkButton(strButtonTitle: ObjKeymessages.kLABEL_OK, strMessage: string, showOnTopVC: false) { (isOk) in
        }
    }

    func getOrderDetails(model : OrderDetailResponseFields) {
        objOrderDetails = model
        self.checkFromPayment()
        self.setData()
    }
    
    func showThankyouAlertPayment(strCardNumber: String, strCardType: String, strCardScheme: String, strCardToken: String, strTransactionId: String, strLatestWalletBalance: String) {
        
        // call add card api in background
        if strCardToken != "" {
            self.addUserCard(strCardNumber: strCardNumber, strCardType: strCardType, strCardScheme: strCardScheme, strCardToken: strCardToken, strTransactionId: strTransactionId)
        }
        
        let vc = ProductPopupStoryboard.instantiate(ThankYouViewController.self)
        vc.modalPresentationStyle = .overFullScreen
        vc.modalTransitionStyle = .crossDissolve
        vc.message = "payment_changed".localized
        vc.isAddNewCard = self.isAddNewCard
        
        vc.completionPaymentCardBlock = { [weak self] (strAction, isAddNewCard) in
            guard let self = self else { return }
            self.dismiss(animated: false, completion: nil)
            if strAction == "Complete" {
                if isAddNewCard {
                    self.refreshOrderData?()
                    self.dismiss(animated: false)
                }
            }
        }
        
        vc.completionBlock = { [weak self] (strAction) in
            guard let self = self else { return }
            self.dismiss(animated: false, completion: nil)
            if strAction == "Complete" {
                self.refreshOrderData?()
                self.dismiss(animated: true, completion: nil)
            }
        }
        
        delay(0.3) {
            UIApplication.topViewController()?.presentVC(vc)
        }
    }

    private func addUserCard(strCardNumber: String, strCardType: String, strCardScheme: String, strCardToken: String, strTransactionId: String) {
        var dictParam : [String:Any] = [:]
        dictParam["vToken"] = strCardToken
        dictParam["vTransactionId"] = strTransactionId
        dictParam["vPaymentDescription"] = strCardNumber
        dictParam["vCardType"] = strCardType
        dictParam["vCardScheme"] = strCardScheme

        if AppSingletonObj.isConnectedToNetwork(){
            self.presenterOrderDetail?.apiCallForAddCard(dictData: dictParam)
        }
    }

    func displayErrorAlert(strMsg:String) {
        AppSingleton.shared.showCustomPopUpWithOkButton(strButtonTitle: ObjKeymessages.kLABEL_OK, strMessage: strMsg, showOnTopVC: false) { (isOk) in
            if isOk == true {
                self.popVC()
            }
        }
    }

    func displayErrorAlertAndGoToHome(strMsg:String) {
        AppSingleton.shared.showCustomPopUpWithOkButton(strButtonTitle: ObjKeymessages.kLABEL_OK, strMessage: strMsg, showOnTopVC: false) { (isOk) in
        }

    }

    func refreshCountryData(arrCountryData : [CountryListResponseFields], flagNoNetwork: Bool) {
        
        arrCountry = arrCountryData
        
        var countryCodeJson = [] as [[String:Any]]
        var dictAnswer: [String: Any] = [:]

        for obj in arrCountryData {
            dictAnswer["en"] = obj.vCountryName
            dictAnswer["es"] = obj.vCountryName
            dictAnswer["zh"] = obj.vCountryName
            dictAnswer["locale"] = obj.vImage
            dictAnswer["code"] = obj.vDialingCode
           
            countryCodeJson.append(dictAnswer)
        }
        
        CountryCodeJson = countryCodeJson

        if flagNoNetwork == true {  // Open country picker
            if arrCountry.count > 0 {  // Do not have country list, call API
                let countryView = CountrySelectView()
                countryView.show()
                countryView.selectedCountryCallBack = {[weak self] (countryDic) -> Void in
                    self?.countryDictSetUp(countryDic: countryDic)
                }
            }
        }
    }

    func reloadScreen(strMessage: String) {
        AppSingletonObj.showAlert(strMessage: strMessage)
        btnEditMoNo.isSelected = !btnEditMoNo.isSelected
        btnCountryCode.isUserInteractionEnabled = !btnCountryCode.isUserInteractionEnabled
        txtMobNo.isUserInteractionEnabled = !txtMobNo.isUserInteractionEnabled
    }

    func displayAlertForFailedCaseAndGoToCurrentOrders(strMsg: String) {
        self.showUpdateTransactionAlert?(strMsg)
        self.dismiss(animated: true, completion: nil)
    }

    func displayAlertAndDismissScreen(string: String) {
        AppSingleton.shared.showCustomPopUpWithOkButton(strButtonTitle: ObjKeymessages.kLABEL_OK, strMessage: string, showOnTopVC: false) { (isOk) in
            if isOk == true {
                NotificationCenter.default.post(name: NSNotification.Name("RemoveCartBadge"), object: nil)
                NotificationCenter.default.post(name: NSNotification.Name("RemoveCartData"), object: nil)
                self.tabBarController?.selectedIndex = 3
            }
        }
    }

    private func setData() {
        if objOrderDetails?.RewardAmount ?? 0.0 > 0 {
            self.stackCoinsDiscount.isHidden = false
            self.lblCoinsDiscountValue.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "-\(forTrailingZero(temp: objOrderDetails?.RewardAmount ?? 0.0))")
        } else {
            self.stackCoinsDiscount.isHidden = true
        }
        lblOrderNoValue.text = objOrderDetails?.vOrderNumber
        
        // convert date to desired format
        let strDate = objOrderDetails?.tsOrderedAt
        let strFormattedDate = strDate?.utcToLocal(dateStr: strDate ?? "")
        lblOrderDateValue.text = strFormattedDate
        
        self.setExpectedDeliveryTimeLabel()
        
        tblOrders.reloadData()
        
        DispatchQueue.main.async {
            self.updatePopupHeight(to: min(CGFloat((self.scrollView.contentSize.height) + 46 + (UIApplication.shared.windows.first?.safeAreaInsets.bottom ?? 0)), UIScreen.main.bounds.height * 0.95))
        }
        
        lblDeliveryValue.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: objOrderDetails?.dDeliveryCharge?.toDouble() ?? 0.0))")
        
        lblDiscountValue.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "-\(forTrailingZero(temp: objOrderDetails?.dOrderDiscount?.toDouble() ?? 0.0))")
        
        if objOrderDetails?.iOrderDiscountType != 3 && objOrderDetails?.iOrderDiscountType != 0 {
            stackViewDiscount.isHidden = false
        }
        else {
            stackViewDiscount.isHidden = true
        }
        
        if objOrderDetails?.iOrderDiscountType == 3 || objOrderDetails?.dMinOrderFreeDelivery == 1 || objOrderDetails?.dHasFreeOrder == 1 || objOrderDetails?.isMosques == 1 {
            stackViewDeliveryDiscount.isHidden = false
            lblDeliveryDiscountValue.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "-\(forTrailingZero(temp: objOrderDetails?.dDistanceCostSetInAdmin?.toDouble() ?? 0.0))")
            lblDeliveryValue.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: objOrderDetails?.dDistanceCostSetInAdmin?.toDouble() ?? 0.0))")
        }
        else {
            stackViewDeliveryDiscount.isHidden = true
            lblDeliveryDiscountValue.text = ""
        }
        
        let vatCost = Double(objOrderDetails?.dVatCharge ?? "") ?? 0
        lblVatValue.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: vatCost))")
        let subTotalWithoutVat = Double(objOrderDetails?.dOrderSubTotal ?? "") ?? 0
        lblSubTotalValue.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: subTotalWithoutVat))")
        lblTotalValue.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(forTrailingZero(temp: objOrderDetails?.dTotalAmount?.toDouble() ?? 0.0))")
                
        btnCountryCode.setTitle(objOrderDetails?.vAlternativeISDCode ?? "", for: .normal)
        txtMobNo.text = objOrderDetails?.vAlternativeMobileNumber ?? ""
        
        if objOrderDetails?.tiPaymentStatus != 1 && objOrderDetails?.tiTransactionType != TransactionType.COD.rawValue {  // not success, hide view
            lblOrderStatus.isHidden = true
            viewStatus.isHidden = true
            btnPayNow.isHidden = false
            viewPaymentMethod.isHidden = true
            cons_height_viewPaymentMethod.constant = 0
            lblPaymentMethodValue.text = ""
        }
        else {
            lblOrderStatus.isHidden = false
            viewStatus.isHidden = false
            btnPayNow.isHidden = true
            viewPaymentMethod.isHidden = false
            cons_height_viewPaymentMethod.constant = 94
            
            if objOrderDetails?.tiIsWalletUsed == 1 {  // partial payment from wallet
                if objOrderDetails?.tiTransactionType == TransactionType.Card.rawValue {  // Card
                    lblPaymentMethodValue.text = "\(ObjKeymessages.kLABEL_WALLET) + \(ObjKeymessages.kLABEL_CARD)"
                }
                else if objOrderDetails?.tiTransactionType == TransactionType.ApplePay.rawValue {  // Apple Pay
                    lblPaymentMethodValue.text = "\(ObjKeymessages.kLABEL_WALLET) + \(ObjKeymessages.kLABEL_APPLE_PAY)"
                }
                else if objOrderDetails?.tiTransactionType == TransactionType.COD.rawValue {  // Card on delivery
                    lblPaymentMethodValue.text = "\(ObjKeymessages.kLABEL_WALLET) + \(ObjKeymessages.kLABEL_CARD_ON_DELIVERY)"
                }
                else if objOrderDetails?.tiTransactionType == TransactionType.Wallet.rawValue {  // Wallet
                    lblPaymentMethodValue.text = ObjKeymessages.kLABEL_WALLET
                }
                else if objOrderDetails?.tiTransactionType == TransactionType.Coins.rawValue {  // Wallet
                    lblPaymentMethodValue.text = ObjKeymessages.kLABEL_Coins
                }
                else if objOrderDetails?.tiTransactionType == TransactionType.Tabby.rawValue {  // Tabby
                    lblPaymentMethodValue.text = ObjKeymessages.kLABEL_Tabby
                }
            }
            else {
                if objOrderDetails?.tiTransactionType == TransactionType.Card.rawValue {  // Card
                    lblPaymentMethodValue.text = ObjKeymessages.kLABEL_CARD
                }
                else if objOrderDetails?.tiTransactionType == TransactionType.ApplePay.rawValue {  // Apple Pay
                    lblPaymentMethodValue.text = ObjKeymessages.kLABEL_APPLE_PAY
                }
                else if objOrderDetails?.tiTransactionType == TransactionType.COD.rawValue {  // Card on delivery
                    lblPaymentMethodValue.text = ObjKeymessages.kLABEL_CARD_ON_DELIVERY
                }
                else if objOrderDetails?.tiTransactionType == TransactionType.Wallet.rawValue {  // Wallet
                    lblPaymentMethodValue.text = ObjKeymessages.kLABEL_WALLET
                }
                else if objOrderDetails?.tiTransactionType == TransactionType.Coins.rawValue {  // Coins
                    lblPaymentMethodValue.text = ObjKeymessages.kLABEL_Coins
                }
                else if objOrderDetails?.tiTransactionType == TransactionType.Tabby.rawValue {  // Tabby
                    lblPaymentMethodValue.text = ObjKeymessages.kLABEL_Tabby
                }
            }
        }
        
        lblDiscountTitle.text = "\(ObjKeymessages.kLABEL_DISCOUNT) - (\(objOrderDetails?.vPromocode ?? ""))"
        
        if objOrderDetails?.dMinOrderFreeDelivery == 1 || objOrderDetails?.dHasFreeOrder == 1 || objOrderDetails?.isMosques == 1 {
            lblDeliveryDiscountTitle.text = "\(ObjKeymessages.kLABEL_DELIVERY_DISCOUNT)"
        }
        else {
            lblDeliveryDiscountTitle.text = "\(ObjKeymessages.kLABEL_DELIVERY_DISCOUNT) - (\(objOrderDetails?.vPromocode ?? ""))"
        }

        lblType.text = objOrderDetails?.vType
        lblAddress.text = objOrderDetails?.txAddress
                
        if objOrderDetails?.vShiftDisplayName == nil || objOrderDetails?.vShiftDisplayName == "" {  // shift name is empty. This is old user.
            if objOrderDetails?.tiShiftType == ShiftType.Morning.rawValue { // Morning
                lblShift.text = ObjKeymessages.kLABEL_MORNING
            }
            else { // Evening
                lblShift.text = ObjKeymessages.kLABEL_EVENING
            }
        }
        else {
            lblShift.text = objOrderDetails?.vShiftDisplayName
        }
        
        if objOrderDetails?.tAdditionalNote == "" {  // hide notes view
            lblNotesTitle.isHidden = true
            viewNote.isHidden = true
            cons_bottom_viewNotes.constant = 0
        }
        else { // show notes view
            lblNotesTitle.isHidden = false
            viewNote.isHidden = false
            cons_bottom_viewNotes.constant = 25
        }
        lblNote.text = objOrderDetails?.tAdditionalNote

        // Order Type
        if objOrderDetails?.iReccuringType == ReccuringType.OnlyOnce.rawValue {
            lblOrderTypeValue.text = ObjKeymessages.kLABEL_ONLY_ONCE
        }
        else if objOrderDetails?.iReccuringType == ReccuringType.EveryWeek.rawValue {
            lblOrderTypeValue.text = ObjKeymessages.kLABEL_EVERY_WEEK
        }
        else if objOrderDetails?.iReccuringType == ReccuringType.Every2Weeks.rawValue {
            lblOrderTypeValue.text = ObjKeymessages.kLABEL_EVERY_TWO_WEEKS
        }
        else if objOrderDetails?.iReccuringType == ReccuringType.EveryMonth.rawValue {
            lblOrderTypeValue.text = ObjKeymessages.kLABEL_EVERY_MONTH
        }

        self.updateUIAccordingToOrderStatus(isFromSocket: false, flagDirectStep: false)
    }

    func setExpectedDeliveryTimeLabel() {
        if objOrderDetails?.tsExpectedStartTime != "" && objOrderDetails?.tsExpectedEndTime != "" {  // show expected delivery time label
            lblExpectedDeliveryTime.isHidden = false
            // convert date to desired format
            let strStartTime = objOrderDetails?.tsExpectedStartTime
            let strEndTime = objOrderDetails?.tsExpectedEndTime
            let strFormattedStartTime = "\(strEndTime?.checkDateIsTodayOrTomorrow() ?? "") \(strEndTime?.getTimeOnlyWithDate(dateStr: strStartTime ?? "") ?? "")" //.utcToLocalTimeOnlyWithDate(dateStr: strStartTime ?? "")
//            strFormattedStartTime?.append("\(strEndTime?.utcToLocalTimeOnlyWithDate(dateStr: strStartTime ?? ""))")
            let strFormattedEndTime = strEndTime?.utcToLocalTimeOnly(dateStr: strEndTime ?? "")
            lblExpectedDeliveryTime.text = "\(ObjKeymessages.kLABEL_EXPECTED_DELIVERY_TIME): \(strFormattedStartTime ?? "") \(ObjKeymessages.kLABEL_TO) \(strFormattedEndTime ?? "")"
            cons_top_lblExpectedDeliveryTime.constant = 15
        }
        else { // hide expected delivery time label
            lblExpectedDeliveryTime.isHidden = true
            lblExpectedDeliveryTime.text = ""
            cons_top_lblExpectedDeliveryTime.constant = 0
        }
    }
    
    func updateUIAccordingToOrderStatus(isFromSocket: Bool, flagDirectStep: Bool) {
        if objOrderDetails?.tiOrderStatus == OrderStatus.OrderPlaced.rawValue || objOrderDetails?.tiOrderStatus == OrderStatus.OrderAssigned.rawValue || objOrderDetails?.tiOrderStatus == OrderStatus.OrderInProgress.rawValue {
            
            // if status no is 4, hide cancel order button for one time orders..
            btnCancelOrder.isHidden = false
            btnTracking.isHidden = true
            btnEditMoNo.isHidden = false
            
            animateView(start: 0, end: 1)

        }
        else if objOrderDetails?.tiOrderStatus == OrderStatus.OrderReady.rawValue {

            // if status no is 4, hide cancel order button for one time orders..
            btnCancelOrder.isHidden = true
            btnTracking.isHidden = true
            btnEditMoNo.isHidden = false
            
            animateView(start: 0, end: 2)

        }
        else if objOrderDetails?.tiOrderStatus == OrderStatus.OrderOnTheWay.rawValue {

            // if status no is 4, hide cancel order button for one time orders..
            btnCancelOrder.isHidden = true
            btnTracking.isHidden = false
            btnEditMoNo.isHidden = false
            
            if isFromSocket == true {
                animateView(start: 1, end: 3)
            }
            else {
                animateView(start: 0, end: 3)
            }

        }
        else if objOrderDetails?.tiOrderStatus == OrderStatus.OrderDelivered.rawValue {

            // if status no is 4, hide cancel order button for one time orders..
            btnCancelOrder.isHidden = true
            btnTracking.isHidden = true
            btnEditMoNo.isHidden = true
            
            if isFromSocket == true {
                if flagDirectStep == true {
                    animateView(start: 0, end: 4)
                }
                else {
                    animateView(start: 2, end: 4)
                }
            }
            else {
                animateView(start: 0, end: 4)
            }
        }
        else if objOrderDetails?.tiOrderStatus == OrderStatus.OrderCancelled.rawValue {
            AppSingleton.shared.showCustomPopUpWithOkButton(strButtonTitle: ObjKeymessages.kLABEL_OK, strMessage: ObjKeymessages.kMSG_ORDER_CANCELLED_BY_ADMIN, showOnTopVC: true) { (isOk) in
                if isOk == true {
                    self.refreshOrderData?()
                    self.dismiss(animated: true, completion: nil)
                }
            }
        }
        
        if objOrderDetails?.vDriverMobile != "" {  // driver mobile number available
            cons_width_stackViewCall.constant = 70
            stackViewCall.isHidden = false
        }
        else {
            cons_width_stackViewCall.constant = 0
            stackViewCall.isHidden = true
        }

    }
    
    func animateView(start: Int, end: Int) {
                        
        guard start < stackViewMain.subviews.count else { return }
        guard start < end else { return }
        
        // remove selected status if current status is less than it
        if end <= 3 {
            for i in (end)...3 {
                let segmentView = stackViewMain.subviews[i]
                segmentView.subviews.forEach { segment in
                    if let imageView = segment as? UIImageView {
                        imageView.image = UIImage(named: "status_deselected")
                        imageView.layer.removeAllAnimations()
                    }
                    else {
                        guard let frontView = segment.viewWithTag(1) else {
                            debugPrint("Not matched..!!")
                            return
                        }
                        
                        frontView.backgroundColor = UIColor.AppTheme_VeryLightStatusLineColor_EFEFEF
                        frontView.frame.size.height = 0
                    }
                }
            }
            
            if end == 1 {
                let segmentView = stackViewMain.subviews[end - 1]
                segmentView.subviews.forEach { segment in
                    guard let frontView = segment.viewWithTag(1) else {
                        debugPrint("Not matched..!!")
                        return
                    }
                    
                    frontView.backgroundColor = UIColor.AppTheme_VeryLightStatusLineColor_EFEFEF
                    frontView.frame.size.height = 0
                }
            }
            
        }
        // remove selected status logic complete
        
        
        let segmentView = stackViewMain.subviews[start]
        segmentView.subviews.forEach { segment in
            
            if let imageView = segment as? UIImageView {
                imageView.image = UIImage(named: "status_selected")
                
                if start < end - 1 {
                    debugPrint("No Animation")
                    imageView.layer.removeAllAnimations()
                }
                else {
                    imageView.alpha = 0
                    UIView.animate(withDuration: 0.6, delay: 0, options: [.curveEaseInOut, .repeat, .autoreverse]) {
                        imageView.alpha = 1
                    }
                }
            }
            else {
                guard start < end - 1 else {
                    return
                }
                guard let bgView = segment.viewWithTag(-1), let frontView = segment.viewWithTag(1) else {
                    debugPrint("Not matched..!!")
                    return
                }
                
                bgView.backgroundColor = UIColor.AppTheme_VeryLightStatusLineColor_EFEFEF
                frontView.backgroundColor = UIColor.AppTheme_VeryLightStatusLineColor_EFEFEF
                
                frontView.frame = bgView.frame
                frontView.frame.size.height = 0
                
                DispatchQueue.main.asyncAfter(deadline: .now() + (start < end - 1 ? 0 : (duration / 2))) { [weak self] in
                    
                    UIView.animate(withDuration: self?.duration ?? 0.0, delay: 0, options: .curveEaseInOut) {
                        frontView.backgroundColor = UIColor.AppTheme_BlueColor_012CDA
                        frontView.frame.size.height = bgView.frame.height
                        
                    } completion: { [weak self] _ in
                        self?.animateView(start: start + 1, end: end)
                    }
                }
            }
        }
    }
    
}

// PayTabs
extension OrderDetailViewController {
    func payWithCard(flagCardOrNot: Bool, strCardToken: String, strCardTransactionReference: String, flagSplitWallet: Bool, strRemainingAmt: String) {
          var strAddress = ""
          var strCity = ""
          var strZipcode = ""
          var strState = ""
          var strCountryCode = ""

          if let address = UserDefaults.standard.object(forKey: USERDEFAULTS_INFO_KEY.CURRENT_ADDRESS) as? [String:Any],let data = address.data {
              let decodeResult: (decodableObj: AddressResponseFields?, error: Error?) = CodableHelper.decode(AddressResponseFields.self, from: data)
              if let decodeObj = decodeResult.decodableObj {
                  strAddress = decodeObj.txAddress ?? ""
                  strCity = decodeObj.vCity ?? ""
                  strZipcode = decodeObj.vZipCode ?? ""
                  strState = decodeObj.vState ?? ""
                  strCountryCode = decodeObj.vCountryCode ?? ""

              }
          }

          var billingDetails: PaymentSDKBillingDetails! {
              return PaymentSDKBillingDetails(name: User.shared.vName,
                                           email: User.shared.vEmailId,
                                              phone: "\(User.shared.vISDCode ?? "")\(User.shared.vMobileNumber ?? "")",
                                           addressLine: strAddress,
                                           city: strCity,
                                           state: strState,
                                           countryCode: strCountryCode,
                                           zip: strZipcode)
          }

          var configuration: PaymentSDKConfiguration! {
              let theme = PaymentSDKTheme.default
              theme.logoImage = UIImage(named: "logo_login_white")
              theme.secondaryColor = UIColor.AppTheme_BlueColor_012CDA
              theme.secondaryFontColor = UIColor.AppTheme_BlueColor_012CDA
              theme.primaryFontColor = .black
              theme.strokeColor = UIColor.AppTheme_BlueColor_012CDA
              theme.buttonColor = UIColor.AppTheme_BlueColor_012CDA
              theme.titleFontColor = .black
              theme.buttonFontColor = .white
              
              var strToken = ""
              var strTransactionReference = ""
              if flagCardOrNot == false {
                  strToken = ""
                  strTransactionReference = ""
              }
              else {
                  strToken = strCardToken
                  strTransactionReference = strCardTransactionReference
              }

              // check for wallet utilisation
              orderAmount = Double(objOrderDetails?.dTotalAmount ?? "") ?? 0.0
              if flagSplitWallet == true {  // split payment
                  orderAmount = Double(strRemainingAmt) ?? 0.0
              }
              
              return PaymentSDKConfiguration(profileID: profileID,
                                             serverKey: serverKey,
                                             clientKey: clientKey,
                                             currency: SACurrencyCode,
                                             amount: orderAmount,
                                             merchantCountryCode: MerchantCountryCode)
                  .cartDescription("cart description")
                  .cartID(objOrderDetails?.vOrderNumber ?? "")
                  .screenTitle(AppName)
                  .theme(theme)
                  .showBillingInfo(true)
                  .hideCardScanner(true)
                  .languageCode(UserDefaults.standard.getLanguage() ?? "")
                  .tokeniseType(.userOptinoal)
                  .tokenFormat(.hex32)
                  .token(strToken)
                  .transactionReference(strTransactionReference)
                  .billingDetails(billingDetails)
          }
          
          PaymentManager.startCardPayment(on: self, configuration: configuration,
                                   delegate: self)

      }
      
      func payWithApplePay(flagSplitWallet: Bool, strRemainingAmt: String) {
          
          var strAddress = ""
          var strCity = ""
          var strZipcode = ""
          var strState = ""
          var strCountryCode = ""

          if let address = UserDefaults.standard.object(forKey: USERDEFAULTS_INFO_KEY.CURRENT_ADDRESS) as? [String:Any],let data = address.data {
              let decodeResult: (decodableObj: AddressResponseFields?, error: Error?) = CodableHelper.decode(AddressResponseFields.self, from: data)
              if let decodeObj = decodeResult.decodableObj {
                  strAddress = decodeObj.txAddress ?? ""
                  strCity = decodeObj.vCity ?? ""
                  strZipcode = decodeObj.vZipCode ?? ""
                  strState = decodeObj.vState ?? ""
                  strCountryCode = decodeObj.vCountryCode ?? ""

              }
          }

          var strEmail = ""
          if User.shared.vEmailId == "" {  // use default email id
              strEmail = DefaultEmailForApplePay
          }
          else {
              strEmail = User.shared.vEmailId ?? ""
          }

          var billingDetails: PaymentSDKBillingDetails! {
              return PaymentSDKBillingDetails(name: User.shared.vName,
                                           email: strEmail,
                                              phone: "\(User.shared.vISDCode ?? "")\(User.shared.vMobileNumber ?? "")",
                                           addressLine: strAddress,
                                           city: strCity,
                                           state: strState,
                                           countryCode: strCountryCode,
                                           zip: strZipcode)
          }

          var shippingDetails: PaymentSDKShippingDetails! {
              return PaymentSDKShippingDetails(name: User.shared.vName,
                                               email: strEmail,
                                               phone: "\(User.shared.vISDCode ?? "")\(User.shared.vMobileNumber ?? "")",
                                               addressLine: strAddress,
                                               city: strCity,
                                               state: strState,
                                               countryCode: strCountryCode,
                                               zip: strZipcode)
          }
          
          // check for wallet utilisation
          orderAmount = Double(objOrderDetails?.dTotalAmount ?? "") ?? 0.0
          if flagSplitWallet == true {  // split payment
              orderAmount = Double(strRemainingAmt) ?? 0.0
          }

          var applePayConfiguration: PaymentSDKConfiguration! {
              return PaymentSDKConfiguration(profileID: profileID,
                                             serverKey: serverKey,
                                             clientKey: clientKey,
                                             currency: SACurrencyCode,
                                             amount: orderAmount,
                                             merchantCountryCode: MerchantCountryCode)
                  .cartDescription("cart description ApplePay")
                  .cartID(objOrderDetails?.vOrderNumber ?? "")
                  .screenTitle(AppName)
                  .languageCode(UserDefaults.standard.getLanguage() ?? "")
                  .merchantName("Material")
                  .merchantAppleBundleID(PAYTABS_MERCHANT_IDENTIFIER)
                  .simplifyApplePayValidation(true)
                  .billingDetails(billingDetails)
                  .shippingDetails(shippingDetails)

          }

          PaymentManager.startApplePayPayment(on: self,
                                       configuration: applePayConfiguration,
                                       delegate: self)

      }


}

extension OrderDetailViewController: PaymentManagerDelegate {
    
    func paymentManager(didFinishTransaction transactionDetails: PaymentSDKTransactionDetails?, error: Error?) {
        if let transactionDetails = transactionDetails {
            
            var trans_type = 0
            var strCardScheme = ""

            if transactionDetails.cartDescription == "cart description ApplePay" {  // apple pay
                trans_type = 2
                strCardScheme = transactionDetails.paymentInfo?.paymentDescription ?? ""
            }
            else {  // card
                trans_type = 1
                strCardScheme = transactionDetails.paymentInfo?.cardScheme ?? ""
            }

            if transactionDetails.isSuccess() {
                print("Successful transaction")
                // call update transaction api for successful transaction
                self.updateTransaction(strTransactionReference: self.vTransactionRef, paymentStatus: 1, strCardNumber: (transactionDetails.paymentInfo?.paymentDescription ?? ""), strCardType: (transactionDetails.paymentInfo?.cardType ?? ""), strCardScheme: strCardScheme, strCardToken: (transactionDetails.token ?? ""), transactionType: self.transactionType.rawValue, walletUsedOrNot: splitPaymentOrNot)
            }
            else {
                print("Transaction failed")
                // call update transaction api for failed transaction
                self.showError(message: "payment_card_not_changed".localized)
            }
        } else if let error = error {
            self.showError(message: "payment_card_not_changed".localized)
        }
    }
    
    func showError(message: String) {
        DispatchQueue.main.async {
            AppSingleton.shared.showCustomPopUpWithOkButton(strButtonTitle: ObjKeymessages.kLABEL_OK, strMessage: message, showOnTopVC: true) { (isOk) in
            }
        }
    }

    func updateTransaction(strTransactionReference: String, paymentStatus: Int, strCardNumber: String, strCardType: String, strCardScheme: String, strCardToken: String, transactionType: Int, walletUsedOrNot: Int) {
        if AppSingletonObj.isConnectedToNetworkForCheckoutScreen() {
            

            self.presenterOrderDetail?.changePaymentAfterPlaceorder(iOrderId: orderId, tiTransactionType: transactionType, vTransactionRef: strTransactionReference, iPaymentStatus: paymentStatus, iUseWallet: walletUsedOrNot, vCardName: strCardScheme, strCardNumber: strCardNumber, strCardType: strCardType, strCardScheme: strCardScheme, strCardToken: strCardToken)
        }
        else {
            self.displayAlertForFailedCaseAndGoToCurrentOrders(strMsg: ObjKeymessages.kMSG_NO_INTERNET)
        }
    }
}

extension String {
    func convertToDictionary() -> [String: Any]? {
        if let data = data(using: .utf8) {
            return try? JSONSerialization.jsonObject(with: data, options: []) as? [String: Any]
        }
        return nil
    }
}

func secondsToHoursMinutesSeconds(_ seconds: Int) -> (Int, Int, Int) {
    return (seconds / 3600, (seconds % 3600) / 60, (seconds % 3600) % 60)
}


func secondToHourMinuteSecond(totalSeconds: Int) -> String {
    let hours = totalSeconds / 3600
    let minutes = (totalSeconds % 3600) / 60

    var strTime = ""
    if (hours > 0) {
        strTime.append("\(hours) \(ObjKeymessages.kLABEL_HOUR)")
    }

    if (minutes > 0) {
        if (hours > 0) {
            strTime.append(" \(minutes) \(ObjKeymessages.kLABEL_MINUTE)")
        } else {
            strTime.append("\(minutes) \(ObjKeymessages.kLABEL_MINUTE)")
        }
    }
    return strTime
}

extension OrderDetailViewController : UITextFieldDelegate {
    func textField(_ textField: UITextField, shouldChangeCharactersIn range: NSRange, replacementString string: String) -> Bool {
        
        if textField == txtMobNo {
            let newLength: Int = (textField.text?.length)! + string.length - range.length
            if newLength > MaxMoNoLength {
                return false
            }
            return true
        }
        else {
            return true
        }
    }

}
extension OrderDetailViewController: TabbyPaymentResultDelegate {
    func checkPaymentResult(isPaymentDone: Bool) {
        if isPaymentDone {
            self.updateTransaction(strTransactionReference: TransactionTypeTabby, paymentStatus: 1, strCardNumber: "", strCardType: "", strCardScheme: "", strCardToken: "", transactionType: TransactionType.Tabby.rawValue, walletUsedOrNot: 0)
        } else {
            self.displayAlertForFailedCaseAndGoToCurrentOrders(strMsg: "payment_failed_please_try_again_or_change_type_of_pay".localized)
        }
    }
    
}

extension OrderDetailViewController: ChoosePaymentDelegate {
    func startPaymentWithTabby() {
        self.tabbyPay.initPayment(refrencePaymentId: self.objOrderDetails?.vOrderNumber ?? "", total: self.objOrderDetails?.dTotalAmount ?? "0")
    }
    
}

extension UIView{
     func blink() {
         self.alpha = 0
         UIView.animate(withDuration: 0.5, delay: 0.0, options: [.curveEaseInOut, .repeat, .autoreverse], animations: {self.alpha = 1.0}, completion: nil)
     }
}
