
import UIKit
import CarouselSwipeView
import Kingfisher

protocol AdvertisementPopupProtocol: AnyObject {
    
}

class AdvertisementPopupViewController: BaseViewController, AdvertisementPopupProtocol, UIScrollViewDelegate {
    
    // MARK: Objects & Variables
    var presenterAdvertisementPopup: AdvertisementPopupPresentationProtocol?
    
    // MARK: IBOutlets
    @IBOutlet weak var viewOuter: UIView!
    @IBOutlet weak var imgAdvertisement: UIImageView!
    @IBOutlet weak var btnBuyNow: CustomRoundedButtton!
    @IBOutlet weak var viewAdvertising: UIView!
    @IBOutlet weak var lblAdvertisingTitle: MaterialLocalizeLable!
    @IBOutlet weak var collAdvertisement: UICollectionView!
    @IBOutlet weak var pageControl: UIPageControl!
    @IBOutlet weak var carouselView: CarouselSwipeViewManager!
    
    // MARK: - Variables
    var objAdvDetails: [AdvertisementResponseFields]?
    var navigateForAdvertisement: (()->())?
    private var timer:Timer?
    
    // MARK: Object lifecycle
    /*
     override init(nibName nibNameOrNil: String?, bundle nibBundleOrNil: Bundle?) {
     super.init(nibName: nibNameOrNil, bundle: nibBundleOrNil)
     setup()
     }
     */
    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
        setup()
    }
    
    // MARK: Setup
    
    private func setup() {
        let viewController = self
        let interactor = AdvertisementPopupInteractor()
        let presenter = AdvertisementPopupPresenter()
        
        //View Controller will communicate with only presenter
        viewController.presenterAdvertisementPopup = presenter
        
        //Presenter will communicate with Interector and Viewcontroller
        presenter.viewControllerAdvertisementPopup = viewController
        presenter.interactorAdvertisementPopup = interactor
        
        //Interactor will communucate with only presenter.
        interactor.presenterAdvertisementPopup = presenter
        
        
    }
    
    // MARK: View lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        var imagesArr = [String]()
        objAdvDetails?.forEach({ item in
            imagesArr.append(item.vImage ?? "")
        })
        carouselView.delegate = self
        carouselView.setCarouselData(paths: imagesArr,  describedTitle: [], isAutoScroll: true, timer: 2.5, defaultImage: "defaultImage", adsTextLabel: "ADS", orderNowTextButton: "order_now".localized)
        //optional method
        carouselView.setCarouselOpaque(layer: false, describedTitle: true, pageIndicator: false)
        carouselView.setCarouselLayout(displayStyle: 0, pageIndicatorPositon: 3, pageIndicatorColor: .black, describedTitleColor: nil, layerColor: nil)
    }
    
    @IBAction func btnDimViewAction(_ sender: Any) {
        self.dismiss(animated: true, completion: nil)
    }
    
    @IBAction func buyNowAction(_ sender: Any) {
        self.navigateForAdvertisement?()
        self.dismiss(animated: true, completion: nil)
    }
    
}
// MARK: - Extension - CarouselSwipeViewManagerDelegate
extension AdvertisementPopupViewController: CarouselSwipeViewManagerDelegate {
    
    //require method
    func downloadImages(_ url: String, _ index: Int) {
        let imageView = UIImageView()
        
        
        imageView.kf.setImage(with: URL(string: url) , placeholder: UIImage(named: "placeholder_product")) { result in
            switch result {
            case .success(let value):
                self.carouselView.images[index] = value.image
            case .failure(let error):
                print("Error: \(error)")
            }
        }
    }
    
    //optional method (interaction for touch image)
    func didSelectCarouselView(_ view: CarouselSwipeViewManager ,_ index:Int) {
        
    }
    
    //optional method (show first image faster during downloading of all images)
    func callBackFirstDisplayView(_ imageView: UIImageView, _ url: [String], _ index: Int) {
        imageView.kf.setImage(with: URL(string: url[index]), placeholder: UIImage.init(named: "defaultImage"), options: [.transition(.fade(1))], progressBlock: nil, completionHandler: nil)
    }
    
    func startAutoScroll() {
        //optional method
        carouselView.startScrollImageView()
    }
    
    func stopAutoScroll() {
        //optional method
        carouselView.stopScrollImageView()
    }
    
    override func didReceiveMemoryWarning() {
        super.didReceiveMemoryWarning()
        // Dispose of any resources that can be recreated.
    }
    
    func close() {
        debugPrint("close")
        self.dismiss(animated: true, completion: nil)
    }
    
    func navigateForAdvertisements(index: Int) {
        debugPrint("navigateForAdvertisements =>", index)
        self.setCurrentIndex(index: index)
        self.navigateForAdvertisement?()
        self.dismiss(animated: true, completion: nil)
    }
    
    private func setCurrentIndex(index: Int) {
        Constant.shared.ADVERTISEMENT_INDEX = index
    }
    
}
