<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="32700.99.1234" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22685"/>
        <capability name="Named colors" minToolsVersion="9.0"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="LoewNextArabic-Bold.ttf">
            <string>LoewNextArabic-Bold</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" id="gTV-IL-0wX" customClass="AdvertisementCVCell" customModule="TopCustomer" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="384" height="550"/>
            <autoresizingMask key="autoresizingMask"/>
            <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center">
                <rect key="frame" x="0.0" y="0.0" width="384" height="550"/>
                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="7vS-hw-46z">
                        <rect key="frame" x="20" y="10" width="344" height="530"/>
                        <subviews>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="placeholder_banner" translatesAutoresizingMaskIntoConstraints="NO" id="tUD-Zc-QPY">
                                <rect key="frame" x="0.0" y="0.0" width="344" height="440"/>
                            </imageView>
                            <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="gof-SW-Xk7">
                                <rect key="frame" x="15" y="16" width="50" height="35"/>
                                <subviews>
                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="O8R-Bz-nYa" customClass="MaterialLocalizeImageView" customModule="TopCustomer" customModuleProvider="target">
                                        <rect key="frame" x="0.0" y="0.0" width="50" height="35"/>
                                    </imageView>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Ad" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="hSc-zV-ehp" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                        <rect key="frame" x="15.333333333333334" y="11" width="19.333333333333329" height="13"/>
                                        <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="13"/>
                                        <color key="textColor" name="AppTheme_DiffBlackColor_#101113"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="O8R-Bz-nYa" firstAttribute="leading" secondItem="gof-SW-Xk7" secondAttribute="leading" id="GM1-hp-8DW"/>
                                    <constraint firstItem="hSc-zV-ehp" firstAttribute="centerX" secondItem="gof-SW-Xk7" secondAttribute="centerX" id="SJP-Nx-gcw"/>
                                    <constraint firstAttribute="trailing" secondItem="O8R-Bz-nYa" secondAttribute="trailing" id="WHK-nm-Jtn"/>
                                    <constraint firstAttribute="width" constant="50" id="YaF-ue-Nhr"/>
                                    <constraint firstItem="hSc-zV-ehp" firstAttribute="centerY" secondItem="gof-SW-Xk7" secondAttribute="centerY" id="dma-ZA-rl3"/>
                                    <constraint firstAttribute="height" constant="35" id="f9j-z7-f2a"/>
                                    <constraint firstItem="O8R-Bz-nYa" firstAttribute="top" secondItem="gof-SW-Xk7" secondAttribute="top" id="sdG-AC-uPo"/>
                                    <constraint firstAttribute="bottom" secondItem="O8R-Bz-nYa" secondAttribute="bottom" id="yGX-hN-PAp"/>
                                </constraints>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                        <real key="value" value="3"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                                        <real key="value" value="0.5"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                                        <color key="value" name="AppTheme_LightGrayColor_#A0A0A0"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </view>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="M7B-4C-jNX">
                                <rect key="frame" x="294" y="16" width="35" height="35"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="35" id="1Lc-WF-R1k"/>
                                    <constraint firstAttribute="width" constant="35" id="LHz-tB-n9k"/>
                                </constraints>
                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                <state key="normal" image="ic_close_circle"/>
                                <connections>
                                    <action selector="btnDimViewAction:" destination="gTV-IL-0wX" eventType="touchUpInside" id="5qv-Yv-hbX"/>
                                </connections>
                            </button>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="9aE-na-HaY" customClass="CustomRoundedButtton" customModule="TopCustomer" customModuleProvider="target">
                                <rect key="frame" x="72" y="470" width="200" height="40"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="40" id="3BA-Ko-s1a"/>
                                    <constraint firstAttribute="width" constant="200" id="TG7-St-K3K"/>
                                </constraints>
                                <inset key="contentEdgeInsets" minX="15" minY="0.0" maxX="15" maxY="0.0"/>
                                <connections>
                                    <action selector="buyNowAction:" destination="gTV-IL-0wX" eventType="touchUpInside" id="cWC-Mc-A89"/>
                                </connections>
                            </button>
                        </subviews>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="gof-SW-Xk7" firstAttribute="leading" secondItem="7vS-hw-46z" secondAttribute="leading" constant="15" id="2c9-uS-oDH"/>
                            <constraint firstItem="gof-SW-Xk7" firstAttribute="top" secondItem="7vS-hw-46z" secondAttribute="top" constant="16" id="8cY-8g-l0M"/>
                            <constraint firstItem="9aE-na-HaY" firstAttribute="top" secondItem="tUD-Zc-QPY" secondAttribute="bottom" constant="30" id="CJD-yY-ZBh"/>
                            <constraint firstItem="tUD-Zc-QPY" firstAttribute="top" secondItem="7vS-hw-46z" secondAttribute="top" id="JHU-Ha-aLD"/>
                            <constraint firstAttribute="trailing" secondItem="tUD-Zc-QPY" secondAttribute="trailing" id="SPd-Nb-7v4"/>
                            <constraint firstAttribute="height" constant="530" id="VuO-10-qMu"/>
                            <constraint firstItem="M7B-4C-jNX" firstAttribute="top" secondItem="7vS-hw-46z" secondAttribute="top" constant="16" id="ceB-Uc-KBL"/>
                            <constraint firstAttribute="trailing" secondItem="M7B-4C-jNX" secondAttribute="trailing" constant="15" id="hhu-c6-96X"/>
                            <constraint firstAttribute="bottom" secondItem="9aE-na-HaY" secondAttribute="bottom" constant="20" id="iaj-cE-om0"/>
                            <constraint firstItem="9aE-na-HaY" firstAttribute="centerX" secondItem="7vS-hw-46z" secondAttribute="centerX" id="xh5-ri-5Z8"/>
                            <constraint firstItem="tUD-Zc-QPY" firstAttribute="leading" secondItem="7vS-hw-46z" secondAttribute="leading" id="yYu-Z9-exf"/>
                        </constraints>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                <real key="value" value="20"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </view>
                </subviews>
            </view>
            <viewLayoutGuide key="safeArea" id="ZTg-uK-7eu"/>
            <constraints>
                <constraint firstAttribute="trailing" secondItem="7vS-hw-46z" secondAttribute="trailing" constant="20" id="83M-lN-b6V"/>
                <constraint firstItem="7vS-hw-46z" firstAttribute="width" secondItem="gTV-IL-0wX" secondAttribute="width" multiplier="0.895833" id="CHs-pm-PiN"/>
                <constraint firstItem="7vS-hw-46z" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" constant="10" id="Ji0-fF-rT3"/>
                <constraint firstAttribute="bottom" secondItem="7vS-hw-46z" secondAttribute="bottom" constant="10" id="md8-Aa-Bnt"/>
                <constraint firstItem="7vS-hw-46z" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" constant="20" id="ueI-Zz-LZC"/>
            </constraints>
            <userDefinedRuntimeAttributes>
                <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                    <real key="value" value="20"/>
                </userDefinedRuntimeAttribute>
            </userDefinedRuntimeAttributes>
            <connections>
                <outlet property="btnBuyNow" destination="9aE-na-HaY" id="waK-J5-Zjf"/>
                <outlet property="imgAdvertisement" destination="tUD-Zc-QPY" id="NEJ-IG-ADQ"/>
                <outlet property="lblAdvertisingTitle" destination="hSc-zV-ehp" id="DPc-mR-fS2"/>
                <outlet property="viewAdvertising" destination="gof-SW-Xk7" id="9eX-ks-mPZ"/>
                <outlet property="viewOuter" destination="7vS-hw-46z" id="jQ7-mm-lLa"/>
            </connections>
            <point key="canvasLocation" x="38" y="20"/>
        </collectionViewCell>
    </objects>
    <resources>
        <image name="ic_close_circle" width="90" height="90"/>
        <image name="placeholder_banner" width="290" height="105.33333587646484"/>
        <namedColor name="AppTheme_DiffBlackColor_#101113">
            <color red="0.062745098039215685" green="0.066666666666666666" blue="0.074509803921568626" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="AppTheme_LightGrayColor_#A0A0A0">
            <color red="0.62745098039215685" green="0.62745098039215685" blue="0.62745098039215685" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
