//
//  AdvertisementCVCell.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 28/10/2024.
//  Copyright © 2024 SOTSYS203. All rights reserved.
//

import UIKit

protocol AdvertisementCVCellDelegate {
    func navigateForAdvertisements()
    func close()
}

class AdvertisementCVCell: UICollectionViewCell, AdvertisementPopupProtocol {
    
    // MARK: IBOutlets
    @IBOutlet weak var viewOuter: UIView!
    @IBOutlet weak var imgAdvertisement: UIImageView!
    @IBOutlet weak var btnBuyNow: CustomRoundedButtton!
    @IBOutlet weak var viewAdvertising: UIView!
    @IBOutlet weak var lblAdvertisingTitle: MaterialLocalizeLable!
    
    // MARK: - Variables
    var objAdvDetails: AdvertisementResponseFields?
    var delegate: AdvertisementCVCellDelegate?
    
    // MARK: View lifecycle
    override func awakeFromNib() {
        super.awakeFromNib()
        // Initialization code
        self.lblAdvertisingTitle.text = "Ad".localized
    }
    
    func setData() {
        if objAdvDetails?.IsAppadvertisement == 1 {
            self.viewAdvertising.isHidden = false
        } else {
            self.viewAdvertising.isHidden = true
        }
        btnBuyNow.setTitle(objAdvDetails?.vButtonText, for: .normal)
        
        imgAdvertisement.contentMode = .scaleToFill
        
        imgAdvertisement.kf.indicatorType = .activity
        let url = URL(string: objAdvDetails?.vImage ?? "")
        imgAdvertisement.kf.setImage(with: url , placeholder: UIImage(named: "placeholder_banner"))
    }
    
    @IBAction func btnDimViewAction(_ sender: Any) {
        self.delegate?.close()
    }
    
    @IBAction func buyNowAction(_ sender: Any) {
        self.delegate?.navigateForAdvertisements()
    }
    
}
