//
//  InviteFriendsPresenter.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 09/07/2024.
//  Copyright © 2024 SOTSYS203. All rights reserved.
//

import UIKit

protocol InviteFriendsPresentationProtocol {
    func apiCallForGetQRCode()
    func apiResponseGetQRCode(response:UserQRCodeResponse?, error:Error?)
}

class InviteFriendsPresenter: InviteFriendsPresentationProtocol {
    
    // MARK: Objects & Variables
    weak var viewControllerInviteFriends: InviteFriendsProtocol?
    var interactorInviteFriends: InviteFriendsInteractorProtocol?
    
    func apiCallForGetQRCode() {
        interactorInviteFriends?.apiCallForGetQRCode()
    }
    
    func apiResponseGetQRCode(response: UserQRCodeResponse?, error: Error?) {
        if let error = error  {
            viewControllerInviteFriends?.displayAlert(string: error.localizedDescription)
            return
        }
        
        guard let response = response else {
            self.viewControllerInviteFriends?.displayAlert(string: response?.responseMessage ?? "an_error_occurred_please_try_again_later".localized)
            return
        }
        
        guard let qrCode = response.qrCode, let url = response.url, let referralCode = response.referralCode, let inviteCode = response.inviteCode else {
            self.viewControllerInviteFriends?.displayAlert(string: response.responseMessage ?? "an_error_occurred_please_try_again_later".localized)
            return
        }
        self.viewControllerInviteFriends?.getUserQRCode(data: UserQRCodeResponseFields(referralCode: referralCode, qrCode: qrCode, url: url, inviteCode: inviteCode))
    }
}
