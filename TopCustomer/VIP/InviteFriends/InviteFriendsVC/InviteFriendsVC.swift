//
//  InviteFriendsVC.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 07/07/2024.
//  Copyright © 2024 SOTSYS203. All rights reserved.
//

import UIKit

protocol InviteFriendsProtocol: AnyObject {
    func displayAlert(string:String)
    func getUserQRCode(data: UserQRCodeResponseFields)
}

class InviteFriendsVC: BottomPopupViewController {
    
    // MARK: - IBOutlets
    @IBOutlet weak var lblTitle: UILabel!
    @IBOutlet weak var lblReferralCode: UILabel!
    @IBOutlet weak var imageQrCode: UIImageView!
    @IBOutlet weak var lblUrl: UILabel!
    @IBOutlet weak var lblShare: UILabel!
    
    // MARK: - Variables
    var presenterInviteFriends: InviteFriendsPresentationProtocol?
    var referralCode = ""
    var qrCodeUrl = ""
    var shareLink = ""
    var inviteCode = ""
    
    //MARK: - Object lifecycle
    override var popupHeight: CGFloat { return 60 }
    
    
    // MARK: View lifecycle
    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
        setup()
    }
    
    // MARK: Setup
    private func setup() {
        let viewController = self
        let interactor = InviteFriendsInteractor()
        let presenter = InviteFriendsPresenter()
        //View Controller will communicate with only presenter
        viewController.presenterInviteFriends = presenter
        //Presenter will communicate with Interector and Viewcontroller
        presenter.viewControllerInviteFriends = viewController
        presenter.interactorInviteFriends = interactor
        //Interactor will communucate with only presenter.
        interactor.presenterInviteFriends = presenter
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        // Do any additional setup after loading the view.
        presenterInviteFriends?.apiCallForGetQRCode()
    }
    
    @IBAction func closeBtnTapped(_ sender: Any) {
        self.dismiss(animated: true)
    }
    
    @IBAction func copyUrlActionTapped(_ sender: Any) {
        debugPrint("copyUrlActionTapped")
        UIPasteboard.general.string = self.shareLink
        AppSingleton.shared.showAlert(strMessage: "link_copied".localized)
    }
    
    @IBAction func btnShareTapped(_ sender: Any) {
        
        self.shareInDefaultShareKit(array: [self.shareLink])
        
    }
    
    private func getShareLinke() {
        if UserDefaults.standard.inviteFriendsShareLink == "" {
            FirebaseDeeplinkingHelper.shared.generateInviteFriendsLink(inviteCode: self.inviteCode) { url, code in
                self.lblReferralCode.text = code ?? "" //url?.components(separatedBy: "/").last ?? ""
                self.shareLink = url ?? ""
                self.lblUrl.text = url ?? "an_error_occurred_please_try_again_later".localized
                let qrImage = self.printImageQRCode()
                self.imageQrCode.image = qrImage
                UserDefaults.standard.inviteFriendsCode = code ?? ""
                UserDefaults.standard.inviteFriendsShareLink = url ?? ""
                UserDefaults.standard.inviteFriendsShareQRImage = qrImage
                self.imageQrCode.kf.indicator?.stopAnimatingView()
            }
        }
        else {
            self.lblReferralCode.text = UserDefaults.standard.inviteFriendsCode
            self.shareLink = UserDefaults.standard.inviteFriendsShareLink
            self.lblUrl.text = UserDefaults.standard.inviteFriendsShareLink
            self.imageQrCode.image = UserDefaults.standard.inviteFriendsShareQRImage
            self.imageQrCode.kf.indicator?.stopAnimatingView()
        }
    }
}

// MARK: - Extension
extension InviteFriendsVC: InviteFriendsProtocol {
    
    func displayAlert(string: String) {
        AppSingleton.shared.showCustomPopUpWithOkButton(strButtonTitle: ObjKeymessages.kLABEL_OK, strMessage: string, showOnTopVC: false) { (isOk) in
        }
    }
    
    func getUserQRCode(data: UserQRCodeResponseFields) {
        self.imageQrCode.kf.indicatorType = .activity
        self.imageQrCode.kf.indicator?.startAnimatingView()
        self.inviteCode = data.inviteCode
        self.lblTitle.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "invite_your_friends_get_money".localized.replacingOccurrences(of: "%2d", with: "\(Constant.shared.VALUE_GIFT)"))
        self.qrCodeUrl = data.url
        self.lblUrl.text = ""
        self.lblShare.text = "share".localized
        self.referralCode = "\(data.referralCode)"
        self.getShareLinke()
    }
    
    func printImageQRCode() -> UIImage {
        let data = self.shareLink.data(using: String.Encoding.ascii)
        // Get a QR CIFilter
        guard let qrFilter = CIFilter(name: "CIQRCodeGenerator") else { return UIImage() }
        // Input the data
        qrFilter.setValue(data, forKey: "inputMessage")
        // Get the output image
        guard let qrImage = qrFilter.outputImage else { return UIImage() }
        // Scale the image
        let transform = CGAffineTransform(scaleX: 10, y: 10)
        let scaledQrImage = qrImage.transformed(by: transform)
        // Do some processing to get the UIImage
        let context = CIContext()
        guard let cgImage = context.createCGImage(scaledQrImage, from: scaledQrImage.extent) else { return UIImage() }
        let processedImage = UIImage(cgImage: cgImage)
        return processedImage
    }
    
}
