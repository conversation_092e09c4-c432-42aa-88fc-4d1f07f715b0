//
//  InviteFriendsInteractor.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 09/07/2024.
//  Copyright © 2024 SOTSYS203. All rights reserved.
//

import Foundation

protocol InviteFriendsInteractorProtocol {
    func apiCallForGetQRCode()
}

class InviteFriendsInteractor: InviteFriendsInteractorProtocol {
    
    // MARK: Objects & Variables
    var presenterInviteFriends: InviteFriendsPresentationProtocol?
    
    func apiCallForGetQRCode() {
        ActivityIndicator.shared.showCentralSpinner()
        
        UserSearchAPI.getQrCode(completion: { data, error in
            ActivityIndicator.shared.hideCentralSpinner()
            self.presenterInviteFriends?.apiResponseGetQRCode(response: data, error: error)
        })
    }
    
}
