//
//  InviteFriendsGiftVC.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 17/07/2024.
//  Copyright © 2024 SOTSYS203. All rights reserved.
//

import UIKit
import Lottie

class InviteFriendsGiftVC: BaseViewController {
    
    // MARK: - IBOutlets
    @IBOutlet weak var lblGiftValue: UILabel!
    @IBOutlet weak var viewAnimation: AnimationView!
    
    // MARK: View lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        self.lblGiftValue.alpha = 0
        // Do any additional setup after loading the view.
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        playAnimation()
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            self.setValueGift()
        }
    }
    
    private func setValueGift() {
        if Constant.shared.VALUE_GIFT != "" {
            self.lblGiftValue.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "+\(Constant.shared.VALUE_GIFT)")
            UIView.animate(withDuration: 1.5) {
                self.lblGiftValue.alpha = 1
            } completion: { value in
                UIView.animate(withDuration: 5.5) {
                    self.lblGiftValue.alpha = 0
                }
            }
        }
    }
    
    private func playAnimation() {
        if !UserDefaults.standard.isCurrentLanguageArabic() {
            let path = Bundle.main.path(forResource: "invite_friends_gift_en",
                                        ofType: "json") ?? ""
            self.viewAnimation.animation = Animation.filepath(path)
        }
        self.viewAnimation.contentMode = .scaleAspectFit
        self.viewAnimation.loopMode = .playOnce
        self.viewAnimation.play()
    }
    
    @IBAction func closeBtnTapped(_ sender: Any) {
        self.dismiss(animated: true)
    }
    
}
