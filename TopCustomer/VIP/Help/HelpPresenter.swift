
import UIKit

protocol HelpPresentationProtocol {
    func checkValidation(dictData:[String:Any]) -> Bool
    func apiCallContactUs(dictData:[String:Any])
    func apiResponseContactUs(response:CommonFields?,error:Error?)

}

class HelpPresenter: HelpPresentationProtocol {
    
    // MARK: Objects & Variables
    weak var viewControllerHelp: HelpProtocol?
    var interactorHelp: HelpInteractorProtocol?
    
    func checkValidation(dictData:[String:Any]) -> Bool {
        if setDataInString(dictData["vName"] as AnyObject).isBlank {
            AppSingletonObj.showAlert(strMessage: ObjKeymessages.kALERT_ENTER_NAME)
            return false
        }
        else if setDataInString(dictData["vEmailId"] as AnyObject).isBlank {
            AppSingletonObj.showAlert(strMessage: ObjKeymessages.kALERT_VALIDATE_EMAIL)
            return false
        }
        else if setDataInString(dictData["vEmailId"] as AnyObject).isEmail == false {
            AppSingletonObj.showAlert(strMessage: ObjKeymessages.kALERT_VALIDATE_EMAIL_VALID)
            return false
        }
        else if setDataInString(dictData["txDescription"] as AnyObject).isBlank {
            AppSingletonObj.showAlert(strMessage: ObjKeymessages.kALERT_DESC_BLANK)
            return false
        }
        return true
    }

    func apiCallContactUs(dictData:[String:Any]) {
        self.interactorHelp?.apiCallForContactUs(dictData: dictData)
    }

    func apiResponseContactUs(response:CommonFields?,error:Error?) {
        if let error = error  {
            AppSingletonObj.showAlert(strMsg: error.localizedDescription)
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            return
        }
        
        if code == APIINVALIDAUTHORIZATIONCODE401 {
            AppSingletonObj.displaySessionExpiredAlert(strMsg: response.responseMessage ?? "")
            return
        }

        if code == APICODE400 {
            AppSingletonObj.showAlert(strMessage: response.responseMessage ?? "")
            return
        }
        
        self.viewControllerHelp?.displayAlert(strMsg: response.responseMessage ?? "")
    }

}
