
import UIKit

protocol HelpInteractorProtocol {
    func apiCallForContactUs(dictData:[String:Any])

}

protocol HelpDataStore {
    //{ get set }
}

class HelpInteractor: HelpInteractorProtocol, HelpDataStore {

    // MARK: Objects & Variables
    var presenterHelp: HelpPresentationProtocol?
 
    func apiCallForContactUs(dictData:[String:Any]) {
        ActivityIndicator.shared.showCentralSpinner()
        
        let authorization = getAuthorizationText()

        ContactUsAPI.contactHelpCreate(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, vName: setDataInString(dictData["vName"] as AnyObject), vEmailId: setDataInString(dictData["vEmailId"] as AnyObject), txDescription: setDataInString(dictData["txDescription"] as AnyObject), iOrderId: Int(setDataInString(dictData["iOrderId"] as AnyObject)) ?? 0) { data, error in
                ActivityIndicator.shared.hideCentralSpinner()
                self.presenterHelp?.apiResponseContactUs(response: data, error: error)

        }
        
    }

}
