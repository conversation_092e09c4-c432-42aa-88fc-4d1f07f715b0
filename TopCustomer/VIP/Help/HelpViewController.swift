
import UIKit
import IQKeyboardManagerSwift

protocol HelpProtocol: AnyObject {
    func displayAlert(strMsg: String)

}

class HelpViewController: BaseViewController {

    // MARK: Objects & Variables
    var presenterHelp: HelpPresentationProtocol?

    // MARK: IBOutlets
    
    @IBOutlet weak var lblContactInfo: UILabel!
    @IBOutlet weak var txtName: CustomTextfieldWithFontStyle!
    @IBOutlet weak var txtEmail: CustomTextfieldWithFontStyle!
    @IBOutlet weak var lblHowCan: UILabel!
    @IBOutlet weak var textView: IQTextView!
    @IBOutlet weak var btnSend: CustomRoundedButtton!
    
    var orderId = 0
    
    // MARK: Object lifecycle
    /*
    override init(nibName nibNameOrNil: String?, bundle nibBundleOrNil: Bundle?) {
        super.init(nibName: nibNameOrNil, bundle: nibBundleOrNil)
        setup()
    }
    */
    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
        setup()
    }
    
    // MARK: Setup
    
    private func setup() {
        let viewController = self
        let interactor = HelpInteractor()
        let presenter = HelpPresenter()
        
        //View Controller will communicate with only presenter
        viewController.presenterHelp = presenter
        
        //Presenter will communicate with Interector and Viewcontroller
        presenter.viewControllerHelp = viewController
        presenter.interactorHelp = interactor
        
        //Interactor will communucate with only presenter.
        interactor.presenterHelp = presenter
    }
    
    
    // MARK: View lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        self.setTexts()
        txtName.text = User.shared.vName
        txtEmail.text = User.shared.vEmailId

    }
    
    private func setTexts() {
        lblContactInfo.text = ObjKeymessages.kLABEL_CONTACT_INFO
        txtName.placeholder = ObjKeymessages.kLABEL_NAME
        txtEmail.placeholder = ObjKeymessages.kLABEL_EMAIL_ADDRESS
        lblHowCan.text = ObjKeymessages.kLABEL_HOW_CAN
        textView.placeholder = ObjKeymessages.kLABEL_WRITE_YOUR_MSG
        btnSend.setTitle(ObjKeymessages.kLABEL_SEND, for: .normal)

        if UserDefaults.standard.getLanguage()! == UserAPI.VLanguage_userLanguage.en.rawValue {       // english
        }
        else {   // arabic
            if textView.textAlignment == NSTextAlignment.center {
            }
            else if textView.textAlignment == NSTextAlignment.right || textView.textAlignment == NSTextAlignment.left{
                textView.textAlignment = textView.textAlignment == NSTextAlignment.right ? NSTextAlignment.left : NSTextAlignment.right
            }
        }

    }
    
    @IBAction func btnDimViewAction(_ sender: Any) {
        self.dismiss(animated: true, completion: nil)
    }

    @IBAction func actionSend(_ sender: Any) {
        self.view.endEditing(true)
        if AppSingletonObj.isConnectedToNetwork(){
            var dictParam : [String:Any] = [:]
            dictParam["vName"] = txtName.text
            dictParam["vEmailId"] = txtEmail.text
            dictParam["txDescription"] = textView.text
            dictParam["iOrderId"] = self.orderId

            if self.presenterHelp?.checkValidation(dictData: dictParam) ?? false {
                self.presenterHelp?.apiCallContactUs(dictData: dictParam)
            }
        }
    }
    
}

extension HelpViewController : UITextFieldDelegate {
    
    func textField(_ textField: UITextField, shouldChangeCharactersIn range: NSRange, replacementString string: String) -> Bool {
        if textField == txtName {
            let newLength: Int = (textField.text?.length)! + string.length - range.length
            if newLength > MaxNameLength {
                return false
            }
            return true
        }
        else {
            return true
        }
    }
    
}

extension HelpViewController: HelpProtocol {
    func displayAlert(strMsg: String) {
        AppSingletonObj.showAlert(strMessage: strMsg)
        self.dismissVC(completion: nil)
    }

}
