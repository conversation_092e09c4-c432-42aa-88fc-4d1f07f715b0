
import UIKit

protocol PaymentsProtocol: AnyObject {
    func displayAlert(string:String)
    func loadCardList(objCard : CardResponseFields)
    func showAlertAndReload(strMessage :  String)

}

class PaymentsViewController: BaseViewController, PaymentsProtocol {

    // MARK: Objects & Variables
    var presenterPayments: PaymentsPresentationProtocol?
    var arrCards: [GetUserCardDetailResponseFields] = []

    // MARK: IBOutlets
    @IBOutlet weak var tblCards: UITableView!    
    @IBOutlet weak var lblTitle: UILabel!
    @IBOutlet weak var lblWallet: UILabel!
    @IBOutlet weak var lblNoRecord: UILabel!
    @IBOutlet weak var lblBalance: UILabel!
    
    
    // MARK: Object lifecycle
    /*
    override init(nibName nibNameOrNil: String?, bundle nibBundleOrNil: Bundle?) {
        super.init(nibName: nibNameOrNil, bundle: nibBundleOrNil)
        setup()
    }
    */
    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
        setup()
    }
    
    // MARK: Setup
    
    private func setup() {
        let viewController = self
        let interactor = PaymentsInteractor()
        let presenter = PaymentsPresenter()
        
        //View Controller will communicate with only presenter
        viewController.presenterPayments = presenter
        
        //Presenter will communicate with Interector and Viewcontroller
        presenter.viewControllerPayments = viewController
        presenter.interactorPayments = interactor
        
        //Interactor will communucate with only presenter.
        interactor.presenterPayments = presenter
    }
    
    
    // MARK: View lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        self.setTexts()
        self.getCardList()

    }
    
    private func setTexts() {
        lblTitle.text = ObjKeymessages.kLABEL_WALLET
        lblWallet.text = ObjKeymessages.kLABEL_WALLET
        lblNoRecord.text = ObjKeymessages.kLABEL_NO_CARDS_HERE
        
        lblBalance.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "0")
        self.setWalletBalance()
    }
    
    private func setWalletBalance() {
        if let balance = UserDefaults.standard.object(forKey: USERDEFAULTS_INFO_KEY.WALLET_BALANCE) as? String {
            lblBalance.attributedText = ObjKeymessages.getSaudiRiyalSymbol(value: "\(balance)")
        }
    }
    
    private func getCardList() {
        if AppSingletonObj.isConnectedToNetwork(){
            self.presenterPayments?.apiCallForGetCardList()
        }
    }

    @IBAction func actionBack(_ sender: UIButton) {
        self.popVC()
    }

    func displayAlert(string: String) {
        AppSingleton.shared.showCustomPopUpWithOkButton(strButtonTitle: ObjKeymessages.kLABEL_OK, strMessage: string, showOnTopVC: false) { (isOk) in
        }
    }

    func loadCardList(objCard : CardResponseFields) {
        
        self.arrCards = objCard.userCard ?? []

        // save wallet balance
        let WalletBalance = Double(objCard.latestWalletBalance ?? "") ?? 0
        let strWalletBalance = "\(forTrailingZero(temp: WalletBalance))"
        UserDefaults.standard.set(strWalletBalance, forKey: USERDEFAULTS_INFO_KEY.WALLET_BALANCE)
        
        // save wallet balance with english numbers
        let strWalletBalanceEnNumbers = "\(forTrailingZeroEnglishNumbersOnly(temp: Double(objCard.latestWalletBalance ?? "") ?? 0))"
        UserDefaults.standard.set(strWalletBalanceEnNumbers, forKey: USERDEFAULTS_INFO_KEY.WALLET_BALANCE_EN_NUMBER)
        
        UserDefaults.standard.synchronize()
        self.setWalletBalance()

        if self.arrCards.count <= 0 {  // show no data view
            lblNoRecord.isHidden = false
            tblCards.isHidden = true
        }
        else {
            lblNoRecord.isHidden = true
            tblCards.isHidden = false
        }
        
        tblCards.reloadData()

    }

    func showAlertAndReload(strMessage :  String) {
        AppSingletonObj.showAlert(strMessage: strMessage)
        self.getCardList()
    }

}

extension PaymentsViewController : UITableViewDelegate, UITableViewDataSource{
    func numberOfSections(in tableView: UITableView) -> Int {
        return 1
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return self.arrCards.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tblCards.dequeueReusableCell(withIdentifier: "PaymentCardListTableViewCell") as! PaymentCardListTableViewCell
                
        cell.lblCardNumber.text = arrCards[indexPath.row].vPaymentDescription
        cell.lblCardScheme.text = arrCards[indexPath.row].vCardScheme

        cell.btnDelete.tag = indexPath.row
        cell.btnDelete.addTarget(self, action: #selector(self.deleteCard(sender:)), for: .touchUpInside)

        return cell
    }
    
    @objc func deleteCard(sender:UIButton){
        AppSingleton.shared.showCustomPopUpWithYesNoButton(strButton1Title: ObjKeymessages.kLABEL_YES, strButton2Title: ObjKeymessages.kLABEL_NO, strMessage: ObjKeymessages.kMSG_DELETE_CARD_CONFIRMATION, showOnTopVC: false) { (isOk) in
            if isOk == true {
                var dictParam : [String:Any] = [:]
                dictParam["iCardId"] = self.arrCards[sender.tag].iCardId ?? 0
                
                if AppSingletonObj.isConnectedToNetwork(){
                    self.presenterPayments?.apiCallForDeleteCard(dictData: dictParam)
                }
            }
        }

    }

}
