
import UIKit

protocol PaymentsPresentationProtocol {
    func apiCallForGetCardList()
    func apiResponseGetCardList(response:CardResponse?,error:Error?)

    func apiCallForDeleteCard(dictData:[String:Any])
    func apiResponseDeleteCard(response:CommonFields?,error:Error?)

}

class PaymentsPresenter: PaymentsPresentationProtocol {
    
    // MARK: Objects & Variables
    weak var viewControllerPayments: PaymentsProtocol?
    var interactorPayments: PaymentsInteractorProtocol?
    
    func apiCallForGetCardList() {
        interactorPayments?.apiCallForGetCardList()
    }

    func apiResponseGetCardList(response: CardResponse?, error: Error?) {
        if let error = error  {
            viewControllerPayments?.displayAlert(string: error.localizedDescription)
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            return
        }
        
        if code == APIINVALIDAUTHORIZATIONCODE401 {
            AppSingletonObj.displaySessionExpiredAlert(strMsg: response.responseMessage ?? "")
            return
        }

        if code == APICODE400 {
            viewControllerPayments?.displayAlert(string: response.responseMessage ?? "")
            return
        }
        
        guard let objCard = response.responseData,code == APISUCCESSCODE200  else {
            return
        }
        self.viewControllerPayments?.loadCardList(objCard: objCard)
    }

    func apiCallForDeleteCard(dictData:[String:Any]) {
        interactorPayments?.apiCallForDeleteCard(dictData: dictData)
    }

    func apiResponseDeleteCard(response:CommonFields?,error:Error?) {
        if let error = error  {
            viewControllerPayments?.displayAlert(string: error.localizedDescription)
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            return
        }
        
        if code == APIINVALIDAUTHORIZATIONCODE401 {
            AppSingletonObj.displaySessionExpiredAlert(strMsg: response.responseMessage ?? "")
            return
        }

        if code == APICODE400 {
            viewControllerPayments?.displayAlert(string: response.responseMessage ?? "")
            return
        }
        
        self.viewControllerPayments?.showAlertAndReload(strMessage: response.responseMessage ?? "")
    }

}
