
import UIKit

protocol PaymentsInteractorProtocol {
    func apiCallForGetCardList()
    func apiCallForDeleteCard(dictData:[String:Any])

}

protocol PaymentsDataStore {
    //{ get set }
}

class PaymentsInteractor: PaymentsInteractorProtocol, PaymentsDataStore {

    // MARK: Objects & Variables
    var presenterPayments: PaymentsPresentationProtocol?
    
    func apiCallForGetCardList() {
        ActivityIndicator.shared.showCentralSpinner()
        
        let authorization = getAuthorizationText()
        
        UserCardsAPI.viewUserCards(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang) { data, error in
            ActivityIndicator.shared.hideCentralSpinner()
            self.presenterPayments?.apiResponseGetCardList(response: data, error: error)
        }
        
    }

    func apiCallForDeleteCard(dictData:[String:Any]) {
        
        ActivityIndicator.shared.showCentralSpinner()
        
        let authorization = getAuthorizationText()
        
        UserCardsAPI.deleteCard(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang, iCardId: Int(setDataInString(dictData["iCardId"] as AnyObject)) ?? 0) { data, error in
                ActivityIndicator.shared.hideCentralSpinner()
                self.presenterPayments?.apiResponseDeleteCard(response: data, error: error)

        }
    }

}
