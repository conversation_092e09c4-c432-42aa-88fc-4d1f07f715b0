

import UIKit

protocol OTPVerificationPresentationProtocol {
    func callResendOtp(dictData:[String:Any])
    func apiResponseResendOTP(response:CommonFields?,error:Error?)
    func checkValidation(dictData:[String:Any]) -> Bool
    func apiCallForOTP(dictData:[String:Any])
    func apiResponseOTP(response:UserResponse?,error:Error?)

}

class OTPVerificationPresenter: OTPVerificationPresentationProtocol {
    
    //MARK: - Objects & Variables -
    weak var viewControllerOTPVerification: OTPVerificationProtocol?
    var interactorOTPVerification: OTPVerificationInteractorProtocol?
    
    func callResendOtp(dictData: [String : Any]) {
        self.interactorOTPVerification?.apiCallResendOTP(dictData: dictData)
    }

    func checkValidation(dictData:[String:Any]) -> Bool {
        if setDataInString(dictData["iOTP"] as AnyObject).isBlank {
//            self.viewControllerOTPVerification?.displayAlert(string: ObjKeymessages.kALERT_OTP_VERIFY)
            AppSingletonObj.showAlert(strMessage: ObjKeymessages.kALERT_OTP_VERIFY)

            return false
        }
        else if setDataInString(dictData["iOTP"] as AnyObject).trimmed.count < MaxOTPLength {
//            self.viewControllerOTPVerification?.displayAlert(string: ObjKeymessages.kALERT_OTP_LENGTH)
            AppSingletonObj.showAlert(strMessage: ObjKeymessages.kALERT_OTP_LENGTH)

            return false
        }
        return true
    }

    func apiCallForOTP(dictData: [String : Any]) {
        self.interactorOTPVerification?.apiCallForOTP(dictData: dictData)
    }

    func apiResponseResendOTP(response:CommonFields?,error:Error?) {
//        self.isAPICalled = false
//        self.viewController?.hideActivityIndicator()
        if let error = error  {
//            viewControllerOTP?.displayAlert(string: error.localizedDescription)
            AppSingletonObj.showAlert(strMsg: error.localizedDescription)

            return
        }
        
        guard let response = response,let code = response.responseCode else {
            //            viewController?.displayAlert(strTitle: "", strMessage: KeyMessages.kInvalidateResponse)
            return
        }
        
        if code == APICODE400 {
//            viewControllerOTP?.displayAlert(string: response.responseMessage ?? "")
            AppSingletonObj.showAlert(strMsg: response.responseMessage ?? "")
            return
        }
        
        if code == APIINVALIDAUTHORIZATIONCODE401 {
            self.viewControllerOTPVerification?.showAlertAndGoToLogin(strMsg: response.responseMessage ?? "")
            return
        }

        if code == APISUCCESSCODE200 {
        }
        else {
            return
        }
        
        self.viewControllerOTPVerification?.removePin(strMsg: response.responseMessage ?? "")
    }

    func apiResponseOTP(response:UserResponse?,error:Error?) {
//        self.isAPICalled = false
//        self.viewController?.hideActivityIndicator()
        if let error = error  {
            viewControllerOTPVerification?.displayAlert(string: error.localizedDescription)
            return
        }
        
        guard let response = response,let code = response.responseCode else {
            //            viewController?.displayAlert(strTitle: "", strMessage: KeyMessages.kInvalidateResponse)
            return
        }
        
        if code == APICODE400 {
//            viewControllerOTP?.displayAlert(string: response.responseMessage ?? "")
            self.viewControllerOTPVerification?.removePinAfterVerify(strMsg: response.responseMessage ?? "")
            return
        }
        
        if code == APIINVALIDAUTHORIZATIONCODE401 {
            self.viewControllerOTPVerification?.showAlertAndGoToLogin(strMsg: response.responseMessage ?? "")
            return
        }

        guard let model = response.responseData,code == APISUCCESSCODE200  else {
            return
        }
        self.viewControllerOTPVerification?.showOkAlert(model: model, strMsg: response.responseMessage ?? "")
    }

}
