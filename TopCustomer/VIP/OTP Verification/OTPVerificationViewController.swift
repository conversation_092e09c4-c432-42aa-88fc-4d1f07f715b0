
import UIKit
import TikTokBusinessSDK
import FirebaseAnalytics

protocol OTPVerificationProtocol: AnyObject {
    func removePin(strMsg: String)
    func displayAlert(string:String)
    func removePinAfterVerify(strMsg: String)
    func showOkAlert(model : UserResponseFields, strMsg: String)
    func showAlertAndGoToLogin(strMsg: String)

}

class OTPVerificationViewController: BaseViewController, OTPVerificationProtocol {

    //MARK: - Properties -
    var presenterOTPVerification: OTPVerificationPresentationProtocol?

    //MARK: - IBOutlets -
    @IBOutlet weak var lblUserName: UILabel!
    @IBOutlet weak var viewOTP: OTPFieldView!
    @IBOutlet weak var btnSendCodeAgain: UIButton!
    @IBOutlet weak var lblTimer: UILabel!
    @IBOutlet weak var lblWelcomeTo: UILabel!
    @IBOutlet weak var lblPleaseEnter: UILabel!
    @IBOutlet weak var btnVerify: CustomRoundedButtton!
    
    
    var strCountryCode = ""
    var strMobNo = ""
    var strName = ""
    var strDeviceToken = ""
    var timer : Timer = Timer()
    private var strOTP : String = ""
    var isFromEditProfile = false

    // MARK: Object lifecycle
    /*
    override init(nibName nibNameOrNil: String?, bundle nibBundleOrNil: Bundle?) {
        super.init(nibName: nibNameOrNil, bundle: nibBundleOrNil)
        setup()
    }
    */
    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
        setup()
    }
    
    // MARK: Setup
    
    private func setup() {
        let viewController = self
        let interactor = OTPVerificationInteractor()
        let presenter = OTPVerificationPresenter()
        
        //View Controller will communicate with only presenter
        viewController.presenterOTPVerification = presenter
        
        //Presenter will communicate with Interector and Viewcontroller
        presenter.viewControllerOTPVerification = viewController
        presenter.interactorOTPVerification = interactor
        
        //Interactor will communucate with only presenter.
        interactor.presenterOTPVerification = presenter
    }

    //MARK: - View Life Cycle -
    override func viewDidLoad() {
        super.viewDidLoad()
        self.setTexts()
        self.setupUI()
        
    }
    
    private func setTexts() {
        lblUserName.text = "\(ObjKeymessages.kLABEL_HELLO) \(strName)!"
        
        lblWelcomeTo.text = ObjKeymessages.kLABEL_WELCOME_TO
        
        let strlastThreeDigitsOfMobNo = strMobNo.suffix(3)
        lblPleaseEnter.text = "\(ObjKeymessages.kALERT_ENTER_OTP) *******\(strlastThreeDigitsOfMobNo)"

        // set resend code attributed button
        let string = ObjKeymessages.kLABEL_SEND_CODE_AGAIN
        let range = (string as NSString).range(of: ObjKeymessages.kLABEL_SEND_CODE_AGAIN)
        let attributedString = NSMutableAttributedString(string: string)
        attributedString.addAttribute(NSAttributedString.Key.underlineStyle, value: NSNumber(value: 1), range: range)
        attributedString.addAttribute(NSAttributedString.Key.underlineColor, value: UIColor.AppTheme_LightGrayColor_A0A0A0, range: range)
        attributedString.addAttribute(NSAttributedString.Key.font, value: UIFont(name: Fonts.LoewNextArabicBold, size: 11)!, range: range)
        btnSendCodeAgain.setAttributedTitle(attributedString, for: .normal)
//        lblIAgree.attributedText = attributedString

        btnVerify.setTitle(ObjKeymessages.kLABEL_VERIFY, for: .normal)
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        lblTimer.text = ""

        self.startTimer()

    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        timer.invalidate()
    }

    func startTimer() {
        
        var count = 60
        
        timer.invalidate()
                
        self.btnSendCodeAgain.isUserInteractionEnabled = false
        self.btnSendCodeAgain.isEnabled = false
        btnSendCodeAgain.setTitleColor(UIColor(red: 172/255, green: 172/255, blue: 172/255, alpha: 1.0), for: .normal)

        
        timer = Timer.scheduledTimer(withTimeInterval: 1, repeats: true, block: { (timer) in
            let minutes = Int(count/60)
            let seconds = Int(count%60)
            let strTimerDuration = String(format: "%02d:%02d", minutes,seconds)
            
            //            self.btnResendOTP.titleLabel?.text = strTimerDuration
            UIView.performWithoutAnimation {
//                self.btnResendOTP.setTitle(strTimerDuration, for: .normal)
            }
            
            if count == 0 {
                self.lblTimer.text = strTimerDuration
//                self.lblTimer.text = ""
                self.btnSendCodeAgain.isUserInteractionEnabled = true
                self.btnSendCodeAgain.isEnabled = true
                self.btnSendCodeAgain.setTitleColor(UIColor(red: 118/255, green: 118/255, blue: 118/255, alpha: 1.0), for: .normal)

                timer.invalidate()
            }else {
                self.lblTimer.text = strTimerDuration
            }
            count = count - 1
        })
    }

    //MARK: Our Own Functions
    private func setupUI() {
        self.setupOtpView()
    }
    
    func setupOtpView(){
        self.viewOTP.fieldsCount = 4
        self.viewOTP.fieldBorderWidth = 0.0
        self.viewOTP.defaultBorderColor = .clear
        self.viewOTP.filledBorderColor = .clear
        self.viewOTP.defaultBackgroundColor = UIColor.AppTheme_FieldBGColor_F7F7F7
        self.viewOTP.filledBackgroundColor = UIColor.AppTheme_FieldBGColor_F7F7F7
        self.viewOTP.cursorColor = UIColor.AppTheme_BlueColor_012CDA
        self.viewOTP.displayType = .roundedCorner
        self.viewOTP.fieldSize = 55
        self.viewOTP.layoutIfNeeded()
        self.viewOTP.separatorSpace = 15
        self.viewOTP.shouldAllowIntermediateEditing = false
        self.viewOTP.delegate = self
        self.viewOTP.initializeUI()
    }
    
    //MARK: - Actions
    @IBAction func actionSendCodeAgain(_ sender: UIButton) {
        self.view.endEditing(true)
        if AppSingletonObj.isConnectedToNetwork() {
            var dictParam : [String:Any] = [:]
            dictParam["vISDCode"] =  strCountryCode
            dictParam["vMobileNumber"] =  strMobNo

            self.presenterOTPVerification?.callResendOtp(dictData: dictParam)
        }
    }
    
    @IBAction func actionBack(_ sender: Any) {
        self.popVC()
    }

    @IBAction func actionVerify(_ sender: UIButton) {
        /*let vc = homeStoryboard.instantiate(MainTabbarViewController.self)
        let navigationVc = UINavigationController(rootViewController: vc)
        AppDel?.window?.rootViewController = navigationVc*/
        
        self.view.endEditing(true)
        if AppSingletonObj.isConnectedToNetwork() {
            var dictParam : [String:Any] = [:]
            dictParam["vISDCode"] = strCountryCode
            dictParam["vMobileNumber"] = strMobNo
            dictParam["iOTP"] = strOTP
            dictParam["vDeviceToken"] = strDeviceToken
            
            print(dictParam)
            
            if self.presenterOTPVerification?.checkValidation(dictData: dictParam) ?? false {
                TikTokBusiness.trackEvent("VERIFY_USER", withProperties: dictParam)
                Analytics.logEvent("VERIFY_USER", parameters: dictParam)
                self.presenterOTPVerification?.apiCallForOTP(dictData: dictParam)
            }
        }
    }
    
    //MARK: - Protocol Functions -
    func removePin(strMsg: String) {
        /*let alert = UIAlertController(title: AppName, message: strMsg, preferredStyle: .alert)
        let yesButton = UIAlertAction(title: ObjKeymessages.kLABEL_OK, style: .default, handler: {(_ action: UIAlertAction) -> Void in
            self.viewOTP.initializeUI()
            self.startTimer()
        })
        alert.addAction(yesButton)
        self.present(alert, animated: true) {() -> Void in }*/
        
        AppSingletonObj.showAlert(strMessage: strMsg)
        self.viewOTP.initializeUI()
        self.startTimer()

    }

    func displayAlert(string: String) {
//        self.showAlert(title: AppName, message: string)
        AppSingleton.shared.showCustomPopUpWithOkButton(strButtonTitle: ObjKeymessages.kLABEL_OK, strMessage: string, showOnTopVC: false) { (isOk) in
        }
    }

    func removePinAfterVerify(strMsg: String) {
        /*let alert = UIAlertController(title: AppName, message: strMsg, preferredStyle: .alert)
        let yesButton = UIAlertAction(title: ObjKeymessages.kLABEL_OK, style: .default, handler: {(_ action: UIAlertAction) -> Void in
            self.viewOTP.initializeUI()
            self.strOTP = ""
        })
        alert.addAction(yesButton)
        self.present(alert, animated: true) {() -> Void in }*/
        
        AppSingletonObj.showAlert(strMessage: strMsg)
        self.viewOTP.initializeUI()
        self.strOTP = ""

    }

    func showOkAlert(model : UserResponseFields, strMsg: String) {
        
        // store user's data locally
        let obj = model
        guard let dictData = obj.dictionary else { return }
        User.shared.setDataFromDictionary(dict: dictData)
        User.shared.setUserLoginStatus(isLogin: true)
        
        // Mixpanel
        MixpanelEvents.sharedInstance.initializeMixpanel()

        DispatchQueue.main.async {
            AppSingletonObj.showAlert(strMessage: strMsg)
        }
        
        if self.isFromEditProfile == true { // go to settings screen
            for controller in self.navigationController!.viewControllers {
                if (controller is SettingsViewController) {
                    self.navigationController?.popToViewController(controller, animated: false)
                    break
                }
            }
        }
        else {  // go to home screen
            if model.new ?? false {
                TikTokBusiness.trackEvent("COMPLETE_REGISTERATION", withProperties: ["phone": strMobNo, "country_code": strCountryCode])
                Analytics.logEvent("COMPLETE_REGISTERATION", parameters: ["phone": strMobNo, "country_code": strCountryCode])
                AppsFlyerEvents.shared.afCompleteRegistration(phone: strMobNo, strCountryCode: strCountryCode)
            } else {
                TikTokBusiness.trackEvent(TTEventName.login.rawValue, withProperties: ["phone": strMobNo, "country_code": strCountryCode])
                Analytics.logEvent(TTEventName.login.rawValue, parameters: ["phone": strMobNo, "country_code": strCountryCode])
                AppsFlyerEvents.shared.afLogin(phone: strMobNo, strCountryCode: strCountryCode)
            }
            let vc = homeStoryboard.instantiate(MainTabbarViewController.self)
            let navigationVc = UINavigationController(rootViewController: vc)
            AppDel?.window?.rootViewController = navigationVc
        }
    }
    
    func showAlertAndGoToLogin(strMsg: String) {
        /*let alert = UIAlertController(title: AppName, message: strMsg, preferredStyle: .alert)
        let yesButton = UIAlertAction(title: ObjKeymessages.kLABEL_OK, style: .default, handler: {(_ action: UIAlertAction) -> Void in
            self.popVC()
        })
        alert.addAction(yesButton)
        self.present(alert, animated: true) {() -> Void in }*/
        
        AppSingleton.shared.showCustomPopUpWithOkButton(strButtonTitle: ObjKeymessages.kLABEL_OK, strMessage: strMsg, showOnTopVC: false) { (isOk) in
            if isOk == true {
                self.popVC()
            }
        }
    }

}

//MARK: - Extensions

extension OTPVerificationViewController {
    //MARK: - VIP Setup -
    /// VIP Setup for OTPVerificationViewController
}

extension OTPVerificationViewController: OTPFieldViewDelegate {
    func hasEnteredAllOTP(hasEnteredAll hasEntered: Bool) -> Bool {
        print("Has entered all OTP? \(hasEntered)")
        return false
    }
    
    func shouldBecomeFirstResponderForOTP(otpTextFieldIndex index: Int) -> Bool {
        return true
    }
    
    func enteredOTP(otp otpString: String) {
        strOTP = otpString
        print("OTPString: \(otpString)")
    }
}

extension Encodable {
  var dictionary: [String: Any]? {
    guard let data = try? JSONEncoder().encode(self) else { return nil }
    return (try? JSONSerialization.jsonObject(with: data, options: .allowFragments)).flatMap { $0 as? [String: Any] }
  }
}
