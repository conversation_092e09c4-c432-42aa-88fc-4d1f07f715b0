
import UIKit

protocol OTPVerificationInteractorProtocol {
    func apiCallResendOTP(dictData:[String:Any])
    func apiCallForOTP(dictData:[String:Any])

}

protocol OTPVerificationDataStore {
    //{ get set }
}

class OTPVerificationInteractor: OTPVerificationInteractorProtocol, OTPVerificationDataStore {

    //MARK: - Objects & Variables -
    var presenterOTPVerification: OTPVerificationPresentationProtocol?
    
    func apiCallResendOTP(dictData: [String : Any]) {
        ActivityIndicator.shared.showCentralSpinner()
        
        AuthenticationAPI.requestForOtp(accept: AcceptParamForHeader, lang: CurrentAppLang, iRoleId: RoleCustomer_2, vISDCode: setDataInString(dictData["vISDCode"] as AnyObject), vMobileNumber: setDataInString(dictData["vMobileNumber"] as AnyObject)) { data, error in
            ActivityIndicator.shared.hideCentralSpinner()
            self.presenterOTPVerification?.apiResponseResendOTP(response: data, error: error)
        }
        
    }

    func apiCallForOTP(dictData: [String : Any]) {
        ActivityIndicator.shared.showCentralSpinner()
        
        var strDeviceToken = ""
        if vDeviceToken == "" {
//            AppDelegate.refreshToken()
            strDeviceToken = AppDelegate.getDeviceToken()
        }
        else {
            strDeviceToken = vDeviceToken
        }

        AuthenticationAPI.verifyOtp(accept: AcceptParamForHeader, lang: CurrentAppLang, iRoleId: RoleCustomer_2, vISDCode: setDataInString(dictData["vISDCode"] as AnyObject), vMobileNumber: setDataInString(dictData["vMobileNumber"] as AnyObject), iOTP: Int(setDataInString(dictData["iOTP"] as AnyObject)) ?? 0, vDeviceToken: strDeviceToken, tiDeviceType: AuthenticationAPI.TiDeviceType_verifyOtp._2, vDeviceName: vDeviceName) { data, error in
            ActivityIndicator.shared.hideCentralSpinner()
            self.presenterOTPVerification?.apiResponseOTP(response: data, error: error)
        }
        
    }

}
