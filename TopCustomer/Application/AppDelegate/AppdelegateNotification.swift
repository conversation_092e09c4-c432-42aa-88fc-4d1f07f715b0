

import UIKit
import UserNotifications
import FirebaseMessaging
//import FirebaseInstanceID

extension AppDelegate : UNUserNotificationCenterDelegate, MessagingDelegate {
    func registerForPushNotifications() {
        if #available(iOS 10.0, *) {
            UNUserNotificationCenter.current().requestAuthorization(options: [.alert, .sound, .badge]) {
                (granted, error) in
                print("Permission granted: \(granted)")
                // set current date if date of allowPushNotifications = nil
                if UserDefaults.standard.allowPushNotificationsDate == nil {
                    UserDefaults.standard.allowPushNotificationsDate = Date()
                }
                guard granted else {
                    self.showPermissionAlert()
                    return
                }
                
                self.getNotificationSettings()
            }
        } else {
            let settings = UIUserNotificationSettings(types: [.alert, .sound, .badge], categories: nil)
            UIApplication.shared.registerUserNotificationSettings(settings)
            UIApplication.shared.registerForRemoteNotifications()
        }
        
        /*NotificationCenter.default.addObserver(self,
                                               selector: #selector(AppDelegate.tokenRefreshNotification),
                                               name: .InstanceIDTokenRefresh,
                                               object: nil)*/

    }
    
    /*class func refreshToken() {
        Messaging.messaging().deleteToken { err in
            if let err = err {
                print("Error while generating new FCM Token")
                print(err)
            }else{
                Messaging.messaging().token { token, err in
                     if let token = token {
                        print("NEW FCM TOKEN GENERATED")
                        print(token)
                        UserDefaults.standard.set(token, forKey: kDeviceToken)
                    }
                }
            }
        }
        
    }*/
    
    class func getDeviceToken() -> String {
        return UserDefaults.standard.string(forKey: kDeviceToken) ?? ""
    }
    
    /*@objc class func tokenRefreshNotification(_ notification: Notification) {
        InstanceID.instanceID().instanceID { (result, error) in
            if error != nil {
                
            } else if let refreshedToken = result {
//                DEVICE_TOKEN = refreshedToken.token
                print("==>> DEVICE_TOKEN ",refreshedToken.token)
                UserDefaults.standard.setValue(refreshedToken.token, forKey: kDeviceToken)
                UserDefaults.standard.synchronize()
            }
        }

        AppDelegate.connectToFcm()
    }

    class func connectToFcm() {
        InstanceID.instanceID().instanceID { (result, error) in
            if error != nil {
                
            } else if let refreshedToken = result {
//                DEVICE_TOKEN = refreshedToken.token
                print("==>> DEVICE_TOKEN ",refreshedToken.token)
                UserDefaults.standard.setValue(refreshedToken.token, forKey: kDeviceToken)
                UserDefaults.standard.synchronize()
                //AppDelegate.apiCallForDeviceToken()
            }
        }
        
        /*Messaging.messaging().shouldEstablishDirectChannel = false
        Messaging.messaging().shouldEstablishDirectChannel = true
        if Messaging.messaging().shouldEstablishDirectChannel {
            
        } else {
            
            
        }*/
        
    }*/

    @available(iOS 10.0, *)
    func getNotificationSettings() {
        
        UNUserNotificationCenter.current().getNotificationSettings { (settings) in
            
            guard settings.authorizationStatus == .authorized else { return }
            
            DispatchQueue.main.async {
                UIApplication.shared.registerForRemoteNotifications()
            }
        }
    }
    
    
    func showPermissionAlert() {
        /*
        let alert = UIAlertController(title: "Error", message: "Please enable access to Notifications in the Settings app.", preferredStyle: .alert)
        
        let settingsAction = UIAlertAction(title: "Settings", style: .default) { (alertAction) in
            self.gotoAppSettings()
            alert.dismiss(animated: false, completion: nil)
        }
        
        let cancelAction = UIAlertAction(title: "Cancel", style: .default, handler: nil)
        
        alert.addAction(settingsAction)
        alert.addAction(cancelAction)
        
        //self.window?.rootViewController?.show(alert, sender: nil)
        self.window?.rootViewController?.present(alert, animated: false, completion: nil)
        */
    }
    
    private func gotoAppSettings() {
        
        guard let settingsUrl = URL(string: UIApplication.openSettingsURLString) else {
            return
        }
        
        if UIApplication.shared.canOpenURL(settingsUrl) {
            if #available(iOS 10.0, *) {
                UIApplication.shared.open(settingsUrl, options: [:], completionHandler: nil)
            } else {
                UIApplication.shared.openURL(settingsUrl)
                // Fallback on earlier versions
            }
        }
    }
    
    
//    func application(application: UIApplication, didRegisterUserNotificationSettings notificationSettings: UIUserNotificationSettings) {
//        if notificationSettings.types != .badge {
//            application.registerForRemoteNotifications()
//        }
//    }
    
    func application(_ application: UIApplication, didRegisterForRemoteNotificationsWithDeviceToken deviceToken: Data) {}
    
    
    func application(_ application: UIApplication, didFailToRegisterForRemoteNotificationsWithError error: Error) {
        UserDefaults.standard.set(15.randomString, forKey: kDeviceToken)
        debugPrint("Failed to register:", error)
    }
    
    func application(_ application: UIApplication, didReceiveRemoteNotification userInfo: [AnyHashable : Any]) {
//        print("didReceiveRemoteNotification \(userInfo)")

        //let aps = userInfo["aps"]
        // If your app was running and in the foreground
        // Or
        // If your app was running or suspended in the background and the user brings it to the foreground by tapping the push notification
    }
    
    func messaging(_ messaging: Messaging, didReceiveRegistrationToken fcmToken: String?) {
        debugPrint("=================== FCM TOKEN ======================= \n", fcmToken ?? "")
        UserDefaults.standard.set(fcmToken ?? "", forKey: kDeviceToken)
        if User.shared.checkUserLoginStatus() == true {
            self.updateDeviceToken(oldToken: UserDefaults.standard.string(forKey: kDeviceToken) ?? "", newToken: fcmToken ?? "")
        }
    }
    
    func updateDeviceToken(oldToken: String, newToken: String) {
        let authorization = getAuthorizationText()
        UserAPI.updateDeviceToken(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang , oldToken: oldToken, newToken: newToken) { data, error in
            debugPrint("update Device Token Done")
        }
    }

    func userNotificationCenter(_ center: UNUserNotificationCenter, willPresent notification: UNNotification, withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void) {
        
        completionHandler([.alert,.badge,.sound])
        
        let userInfo = notification.request.content.userInfo
        

        if userInfo["type"] != nil {
           let type = userInfo["type"] as? String
            switch type {
            case PushNotificationType.REFRESH_CURRENT_ORDER_IPN.rawValue:
                if User.shared.checkUserLoginStatus() {
                    NotificationCenter.default.post(name: NSNotification.Name("PushRefreshCurrentOrdersForIPN"), object: nil, userInfo: nil)
                }
            default:
                break
            }
        }
    }
        
    func userNotificationCenter(_ center: UNUserNotificationCenter, didReceive response: UNNotificationResponse, withCompletionHandler completionHandler: @escaping () -> Void) {
        
        let userInfo = response.notification.request.content.userInfo
        
        debugPrint("PAYLOAD: \(userInfo)")
        
        if userInfo["type"] != nil {
           let type = userInfo["type"] as? String
            switch type {
            case PushNotificationType.ORDER_PLACED_SUCCESSFULLY.rawValue:
                if User.shared.checkUserLoginStatus() {
                    let orderId = userInfo["iOrderId"] as? String
                    self.navigateToCurrentOrderDetailsForOrderPlaced(strOrderId: orderId ?? "")
                }
            case PushNotificationType.ORDER_STATUS_CHANGE.rawValue, PushNotificationType.ORDER_DELIVERY_UN_SUCCESSFUL.rawValue:
                if User.shared.checkUserLoginStatus() {
                    let orderId = userInfo["iOrderId"] as? String
                    self.navigateToCurrentOrderDetails(strOrderId: orderId ?? "")
                }
            case PushNotificationType.ORDER_DELIVERED_SUCCESSFULLY.rawValue:  // Order delivered successfully, Show Rating popup
                if User.shared.checkUserLoginStatus() {
                    let orderId = userInfo["iOrderId"] as? String
                    self.openRatingPopup(strOrderId: orderId ?? "")
                }
            case PushNotificationType.NOTIFY_CUSTOMER_ORDER_CANCELLED_BY_ADMIN.rawValue:
                if User.shared.checkUserLoginStatus() {
                    let orderId = userInfo["iOrderId"] as? String
                    self.navigateToHistoryOrderDetails(strOrderId: orderId ?? "")
                }
            case PushNotificationType.CART_REMINDER.rawValue:
                if User.shared.checkUserLoginStatus() {
                    self.navigateToCartScreen()
                }
            case PushNotificationType.RECURRING_ORDER_PAYMENT.rawValue:
                if User.shared.checkUserLoginStatus() {
                    let orderId = userInfo["iOrderId"] as? String
                    self.navigateToScheduledOrderDetails(strOrderId: orderId ?? "")
                }
            case PushNotificationType.REFRESH_CURRENT_ORDER_IPN.rawValue:
                if User.shared.checkUserLoginStatus() {
                    self.refreshCurrentOrders()
                }
            case PushNotificationType.OFFER_ANNOUNCEMENT.rawValue: // Go to offer detail screen
                if User.shared.checkUserLoginStatus() {
                    let offerId = userInfo["iOfferId"] as? String
                    self.openOfferDetailPopup(strOfferId: offerId ?? "")
                }
            case PushNotificationType.CATEGORY_ANNOUNCEMENT.rawValue: // Go to product list screen
                if User.shared.checkUserLoginStatus() {
                    let categoryId = userInfo["iCategoryId"] as? String
                    let categoryName = userInfo["vCategoryName"] as? String
                    self.openProductListScreen(strCategoryId: categoryId ?? "", strCategoryName: categoryName ?? "")
                }
            case PushNotificationType.PRODUCT_ANNOUNCEMENT.rawValue: // Go to product detail screen
                if User.shared.checkUserLoginStatus() {
                    let productId = userInfo["biProductId"] as? String
                    self.openProductDetailPopup(strProductId: productId ?? "")
                }
            case PushNotificationType.NOTIFY_ME.rawValue: // Go to product detail screen
                if User.shared.checkUserLoginStatus() {
                    let productId = userInfo["biProductId"] as? String
                    self.openProductDetailPopup(strProductId: productId ?? "")
                }
            case PushNotificationType.FRIEND_INVITATION_GIFT.rawValue:
                if User.shared.checkUserLoginStatus() {
                    Constant.shared.IS_FRIEND_INVITATION_GIFT = true
                }
            default:
                break
            }
        }
    }

    private func openRatingPopup(strOrderId: String) {
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2, execute: {
            NotificationCenter.default.post(name: NSNotification.Name("OpenRatingPopup"), object: ["iOrderId": strOrderId], userInfo: nil)
        })
    }
    
    private func openOfferDetailPopup(strOfferId: String) {
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2, execute: {
            NotificationCenter.default.post(name: NSNotification.Name("OfferFromDeepLink"), object: ["offerId": strOfferId], userInfo: nil)
        })
    }
    
    private func openProductDetailPopup(strProductId: String) {
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2, execute: {
            NotificationCenter.default.post(name: NSNotification.Name("ProductFromDeepLink"), object: ["productId": strProductId], userInfo: nil)
        })
    }

    private func openProductListScreen(strCategoryId: String, strCategoryName: String) {
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2, execute: {
            NotificationCenter.default.post(name: NSNotification.Name("GoToProductList"), object: ["categoryId": strCategoryId, "categoryName": strCategoryName], userInfo: nil)
        })
    }

    private func navigateToCurrentOrderDetails(strOrderId: String) {
        UIApplication.shared.topMostViewController?.dismissVC(completion: nil)
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2, execute: {
            NotificationCenter.default.post(name: NSNotification.Name("OpenCurrentOrderDetailPopup"), object: ["iOrderId": strOrderId], userInfo: nil)
        })
    }
    
    private func navigateToHistoryOrderDetails(strOrderId: String) {
        UIApplication.shared.topMostViewController?.dismissVC(completion: nil)
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2, execute: {
            NotificationCenter.default.post(name: NSNotification.Name("OpenHistoryOrderDetailPopup"), object: ["iOrderId": strOrderId], userInfo: nil)
        })
    }

    private func navigateToCartScreen() {
        UIApplication.shared.topMostViewController?.dismissVC(completion: nil)
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2, execute: {
            NotificationCenter.default.post(name: NSNotification.Name("OpenCartScreen"), object: nil, userInfo: nil)
        })
    }

    private func navigateToCurrentOrderDetailsForOrderPlaced(strOrderId: String) {
        UIApplication.shared.topMostViewController?.dismissVC(completion: nil)
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2, execute: {
            NotificationCenter.default.post(name: NSNotification.Name("RemoveCartBadge"), object: nil)
            NotificationCenter.default.post(name: NSNotification.Name("OpenCurrentOrderDetailPopup"), object: ["iOrderId": strOrderId], userInfo: nil)
        })
    }

    private func navigateToScheduledOrderDetails(strOrderId: String) {
        UIApplication.shared.topMostViewController?.dismissVC(completion: nil)
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2, execute: {
            NotificationCenter.default.post(name: NSNotification.Name("OpenScheduledOrderDetailPopup"), object: ["iOrderId": strOrderId], userInfo: nil)
        })
    }

    private func refreshCurrentOrders() {
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2, execute: {
            NotificationCenter.default.post(name: NSNotification.Name("GoToCurrentOrdersForIPN"), object: nil, userInfo: nil)
        })

    }

}
