//
//  AppDelegate+AppsFlyerDeepLinkDelegate.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 26/09/2024.
//  Copyright © 2024 SOTSYS203. All rights reserved.
//

import UIKit
import AppsFlyerLib


extension AppDelegate: DeepLinkDelegate {
    
    // Open Deeplinks
    // Open URI-scheme for iOS 8 and below
    func application(_ application: UIApplication, open url: URL, sourceApplication: String?, annotation: Any) -> Bool {
        AppsFlyerLib.shared().handleOpen(url, sourceApplication: sourceApplication, withAnnotation: annotation)
        return true
    }
    
    // User logic
    fileprivate func walkToSceneWithParams(deepLinkObj: DeepLink) {
        let storyBoard: UIStoryboard = UIStoryboard(name: "Home", bundle: nil)
        UIApplication.shared.windows.first?.rootViewController?.dismiss(animated: true, completion: nil)
        
        guard let fruitNameStr = deepLinkObj.clickEvent["deep_link_value"] as? String else {
            print("Could not extract query params from link")
            return
        }
        
        let destVC = fruitNameStr + "_vc"
        if let newVC = storyBoard.instantiateVC(withIdentifier: destVC) {
            
            print("AppsFlyer routing to section: \(destVC)")
            newVC.deepLinkData = deepLinkObj
            
            UIApplication.shared.windows.first?.rootViewController?.present(newVC, animated: true, completion: nil)
        } else {
            print("AppsFlyer: could not find section: \(destVC)")
        }
    }
    
    func didResolveDeepLink(_ result: DeepLinkResult) {
        var fruitNameStr: String = ""
        switch result.status {
        case .notFound:
            print("Deep link not found")
        case .found:
            let deepLinkStr:String = result.deepLink!.toString()
            print("DeepLink data is: \(deepLinkStr)")
            
            if( result.deepLink?.isDeferred == true) {
                print("This is a deferred deep link")
            } else {
                print("This is a direct deep link")
            }
            walkToSceneWithParams(deepLinkObj: result.deepLink!)
        case .failure:
            print("Error %@", result.error!)
        }
        guard let deepLinkObj:DeepLink = result.deepLink else {
            NSLog("[AFSDK] Could not extract deep link object")
            return
        }
        
        if( deepLinkObj.isDeferred == true) {
            NSLog("[AFSDK] This is a deferred deep link")
        }
        else {
            NSLog("[AFSDK] This is a direct deep link")
        }
        fruitNameStr = deepLinkObj.deeplinkValue ?? ""
    }
    
}

extension UIStoryboard {
    func instantiateVC(withIdentifier identifier: String) -> MainTabbarViewController? {
        // "identifierToNibNameMap" – dont change it. It is a key for searching IDs
        //        let vc = homeStoryboard.instantiate(MainTabbarViewController.self)
        //        let navigationVc = UINavigationController(rootViewController: vc)
        //        AppDel?.window?.rootViewController = navigationVc
        if let identifiersList = self.value(forKey: "MainTabbarViewController") as? [String: Any] {
            if identifiersList[identifier] != nil {
                return self.instantiateViewController(withIdentifier: identifier) as? MainTabbarViewController
            }
        }
        return nil
    }
}
