

import UIKit
import Firebase
import <PERSON><PERSON><PERSON>eyboardManagerSwift
import GoogleMaps
import Branch
import Mixpanel
import FirebaseDynamicLinks
import GooglePlaces
import Tabby
import AppsFlyerLib
import AppTrackingTransparency
import AdSupport
import FBSDKCoreKit
import TikTokBusinessSDK
import SwiftUI

let kSecret = ""
let kPrivateKey = ""

@UIApplicationMain
class AppDelegate: UIResponder, UIApplicationDelegate {
    static var shared = AppDelegate()
    var window: UIWindow?
    
    func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?) -> Bool {
        
        let config = TikTokConfig.init(appId: "1626095299", tiktokAppId: "7267838517546385410")
        
        TikTokBusiness.initializeSdk(config) { success, error in
            if (!success) {
                debugPrint(error?.localizedDescription)
            } else {
                debugPrint("<PERSON>ik<PERSON> successfully intialized")
            }
        }
        // tiktok event Launch App
        let launchAPPEvent = TikTokEvents(name: "LaunchAPP", properties: ["app version": AppSingleton.appVersion ?? ""])
        launchAPPEvent.sendEvent()
        // Override point for customization after application launch.
        TabbySDK.shared.setup(withApiKey: "pk_test_01913613-8fba-8530-5afb-30b84f3315e2")
        AppsFlyerLib.shared().appsFlyerDevKey = "KGieheVjydZrVeYsieDymG"
        AppsFlyerLib.shared().appleAppID = "1626095299"
#if DEBUG
        AppsFlyerLib.shared().isDebug = true
#endif
        AppsFlyerLib.shared().delegate = self
        AppsFlyerLib.shared().waitForATTUserAuthorization(timeoutInterval: 60)
        UNUserNotificationCenter.current().delegate = self
        AppsFlyerLib.shared().deepLinkDelegate = self
        //Push Notification call
        self.registerForPushNotifications()
        
        // IQKeyboardManager
        self.keyBSettings()
        
        // google map
        GMSServices.provideAPIKey(Constant.GOOGLE_API_KEY)
        // google Places
        GMSPlacesClient.provideAPIKey(Constant.GOOGLE_API_KEY)
        
        // Firebase
        FirebaseApp.configure()
        MixpanelEvents.sharedInstance.initializeMixpanel()
        ApplicationDelegate.shared.application(
            application,
            didFinishLaunchingWithOptions: launchOptions
        )
        
        // facebook AppEvents
        AppEvents.shared.activateApp()
        Messaging.messaging().delegate = self
        // create random string to devie token
        self.setDeviceTokenRandom()
        
        if UserDefaults.standard.getLanguage()! == UserAPI.VLanguage_userLanguage.en.rawValue {       // english
            UIView.appearance().semanticContentAttribute = .forceLeftToRight
            UIWindow.appearance().semanticContentAttribute = .forceLeftToRight
            UITextField.appearance().semanticContentAttribute = .forceLeftToRight
            OTPFieldView.appearance().semanticContentAttribute = .forceLeftToRight
            OTPTextField.appearance().semanticContentAttribute = .forceLeftToRight
            CountrySelectView.appearance().semanticContentAttribute = .forceLeftToRight
            UICollectionView.appearance().semanticContentAttribute = .forceLeftToRight
            UICollectionViewCell.appearance().semanticContentAttribute = .forceLeftToRight
            
        }
        else {   // arabic
            UIView.appearance().semanticContentAttribute = .forceRightToLeft
            UIWindow.appearance().semanticContentAttribute = .forceRightToLeft
            UITextField.appearance().semanticContentAttribute = .forceRightToLeft
            OTPFieldView.appearance().semanticContentAttribute = .forceRightToLeft
            OTPTextField.appearance().semanticContentAttribute = .forceRightToLeft
            CountrySelectView.appearance().semanticContentAttribute = .forceRightToLeft
            UICollectionView.appearance().semanticContentAttribute = .forceRightToLeft
            UICollectionViewCell.appearance().semanticContentAttribute = .forceRightToLeft
            
        }
        
        self.branchDeepLink(launchOptions: launchOptions)
        
        // Firebase Deeplink
        self.setUpURLRedirectionFromDidFinishLaunching(didFinishLaunchingWithOptions: launchOptions)
        // facebook requestTrackingAuthorization
        if #available(iOS 14, *) {
            ATTrackingManager.requestTrackingAuthorization { status in
                switch status {
                case .authorized:
                    debugPrint("enable tracking")
                case .denied:
                    debugPrint("disable tracking")
                default:
                    debugPrint("disable tracking")
                }
            }
        }
        LocationManager.sharedInstance.startUpdatingLocation()
        return true
    }
    
    func setDeviceTokenRandom() {
        if !UserDefaults.standard.bool(forKey: kDeviceTokenRandomCreated) {
            let randomString = 30.randomString
            UserDefaults.standard.set(randomString, forKey: kDeviceToken)
            UserDefaults.standard.set(true, forKey: kDeviceTokenRandomCreated)
        }
    }
    
    func branchDeepLink(launchOptions: [UIApplication.LaunchOptionsKey: Any]?)
    {
        
        //        Branch.setUseTestBranchKey(true)
        Branch.getInstance().initSession(launchOptions: launchOptions) { (params, error) in
            // do stuff with deep link data (nav to page, display content, etc)
            debugPrint(params as? [String: AnyObject] ?? {})
            
            let dict = params as? [String: AnyObject] ?? [:]
            
            let jsonData = try? JSONSerialization.data(withJSONObject: dict, options: .prettyPrinted)
            let jsonString = String(data: jsonData!, encoding: .utf8)
            
            debugPrint(jsonString ?? "")
            //            BranchIoResponseJson = jsonString ?? ""
            
            // store json string in userdefaults
            if UserDefaults.standard.object(forKey: USERDEFAULTS_INFO_KEY.DEEPLINKING_JSON) != nil { //Key exists
            }
            else {
                UserDefaults.standard.set(jsonString ?? "", forKey: USERDEFAULTS_INFO_KEY.DEEPLINKING_JSON)
                UserDefaults.standard.synchronize()
            }
            
            /*let alert = UIAlertController(title: AppName, message: jsonString, preferredStyle: .alert)
             let okButton = UIAlertAction(title: ObjKeymessages.kLABEL_OK, style: .default, handler: {(_ action: UIAlertAction) -> Void in
             })
             alert.addAction(okButton)
             AppDel?.window?.rootViewController?.presentVC(alert)*/
            //                self.present(alert, animated: true) {() -> Void in }
        }
    }
    
    func application(_ app: UIApplication, open url: URL, options: [UIApplication.OpenURLOptionsKey : Any] = [:]) -> Bool {
        ApplicationDelegate.shared.application(
            app,
            open: url,
            sourceApplication: options[UIApplication.OpenURLOptionsKey.sourceApplication] as? String,
            annotation: options[UIApplication.OpenURLOptionsKey.annotation]
        )
        AppsFlyerLib.shared().handleOpen(url, options: options)
        Branch.getInstance().application(app, open: url, options: options)
        
        if let isFirebaseURL = self.setUpURLRedirectionFromOpenURL(app, open: url, options: options){
            return isFirebaseURL
        }
        return true
    }
    
    func application(_ application: UIApplication, continue userActivity: NSUserActivity, restorationHandler: @escaping ([UIUserActivityRestoring]?) -> Void) -> Bool {
        // handler for Universal Links
        AppsFlyerLib.shared().continue(userActivity, restorationHandler: nil)
        debugPrint(userActivity.webpageURL ?? "")
        
        if let url = userActivity.webpageURL {
            let view = url.lastPathComponent
            var parameters: [String: String] = [:]
            URLComponents(url: url, resolvingAgainstBaseURL: false)?.queryItems?.forEach {
                parameters[$0.name] = $0.value
            }
            if view.isNumber() {
                redirect(to: view, with: parameters)
            } else {
                guard let productId = url.valueOf("product_id")  else { return true}
                redirect(to: productId, with: parameters)
            }
        }
        
        self.setUpURLRedirectionFromContinueUserActivity(application, continue: userActivity, restorationHandler: restorationHandler,completion: { isFireBaseLink in
            if !isFireBaseLink {
                Branch.getInstance().continue(userActivity)
            }
        })
        
        return true
    }
    
    func redirect(to: String, with: [String: String]) {
        debugPrint(to)
        debugPrint(with)
        if User.shared.checkUserLoginStatus() {
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.2, execute: {
                NotificationCenter.default.post(name: NSNotification.Name("ProductFromDeepLink"), object: ["productId": to], userInfo: nil)
            })
        }
    }
    
    func application(_ application: UIApplication, didReceiveRemoteNotification userInfo: [AnyHashable : Any], fetchCompletionHandler completionHandler: @escaping (UIBackgroundFetchResult) -> Void) {
        // handler for Push Notifications
        AppsFlyerLib.shared().handlePushNotification(userInfo)
        Branch.getInstance().handlePushNotification(userInfo)
    }
    
    func navigateToHomeScreen() {
        //        let vc = homeStoryboard.instantiate(MainTabbarViewController.self)
        //        let navigationVc = UINavigationController(rootViewController: vc)
        //        AppDel?.window?.rootViewController = navigationVc
        //        AppDel?.window?.rootViewController = SelectDeliveryTypeView()
        AppDel?.window?.rootViewController = UIHostingController(rootView: SelectDeliveryTypeView())
        AppDel?.window?.makeKeyAndVisible()
        
    }
    
    func keyBSettings() {
        IQKeyboardManager.shared.enable = true
        
        IQKeyboardManager.shared.toolbarDoneBarButtonItemText = ObjKeymessages.kLABEL_DONE
        IQKeyboardManager.shared.toolbarPreviousNextAllowedClasses = [UIView.self,UIScrollView.self]
        
        IQKeyboardManager.shared.enableAutoToolbar = true
        IQKeyboardManager.shared.shouldResignOnTouchOutside = true
        UITextField.appearance().tintColor = .systemBlue
        UITextView.appearance().tintColor = .blue
    }
    
    func restartApp() {
        User.shared.setUserLoginStatus(isLogin: false)
        Mixpanel.mainInstance().reset()
        
        let objVC : LoginViewController = UIStoryboard.storyboard(.Main).instantiateViewController()
        let navigationVc = UINavigationController(rootViewController: objVC)
        navigationVc.setNavigationBarHidden(true, animated: false)
        self.window?.rootViewController = navigationVc
        //        AppDel?.window?.rootViewController = UIHostingController(rootView: SelectDeliveryTypeView())
        //        AppDel?.window?.makeKeyAndVisible()
        IQKeyboardManager.shared.toolbarDoneBarButtonItemText = ObjKeymessages.kLABEL_DONE
    }
    
    func restartAppAfterChangingLang() {
        //        let vc = homeStoryboard.instantiate(MainTabbarViewController.self)
        //        let navigationVc = UINavigationController(rootViewController: vc)
        //        navigationVc.setNavigationBarHidden(true, animated: false)
        //        self.window?.rootViewController = navigationVc
        IQKeyboardManager.shared.toolbarDoneBarButtonItemText = ObjKeymessages.kLABEL_DONE
        AppDel?.window?.rootViewController = UIHostingController(rootView: SelectDeliveryTypeView())
        AppDel?.window?.makeKeyAndVisible()
    }
    
    func applicationWillResignActive(_ application: UIApplication) {
        // Sent when the application is about to move from active to inactive state. This can occur for certain types of temporary interruptions (such as an incoming phone call or SMS message) or when the user quits the application and it begins the transition to the background state.
        // Use this method to pause ongoing tasks, disable timers, and invalidate graphics rendering callbacks. Games should use this method to pause the game.
    }
    
    func applicationDidEnterBackground(_ application: UIApplication) {
        // Use this method to release shared resources, save user data, invalidate timers, and store enough application state information to restore your application to its current state in case it is terminated later.
        // If your application supports background execution, this method is called instead of applicationWillTerminate: when the user quits.
    }
    
    func applicationWillEnterForeground(_ application: UIApplication) {
        // Called as part of the transition from the background to the active state; here you can undo many of the changes made on entering the background.
        if User.shared.checkUserLoginStatus() {
            SocketIOManager.shared.checkAndHandleReconnectSocket()
        }
    }
    
    func applicationDidBecomeActive(_ application: UIApplication) {
        UIApplication.shared.applicationIconBadgeNumber = 0
        if let customUserId = User.shared.iUserId {
            // Set CUID in AppsFlyer SDK for this session
            AppsFlyerLib.shared().customerUserID = "\(customUserId)"
            AppsFlyerLib.shared().start(completionHandler: { (dictionary, error) in
                if (error != nil) {
                    debugPrint(error ?? "")
                    return
                } else {
                    debugPrint(dictionary ?? "")
                    return
                }
            })
        }
        // Restart any tasks that were paused (or not yet started) while the application was inactive. If the application was previously in the background, optionally refresh the user interface.
        
        if #available(iOS 14, *) {
            ATTrackingManager.requestTrackingAuthorization { (status) in
                switch status {
                case .denied:
                    debugPrint("AuthorizationSatus is denied")
                case .notDetermined:
                    debugPrint("AuthorizationSatus is notDetermined")
                case .restricted:
                    debugPrint("AuthorizationSatus is restricted")
                case .authorized:
                    debugPrint("AuthorizationSatus is authorized")
                @unknown default:
                    debugPrint("Invalid authorization status")
                }
            }
        }
        if User.shared.checkUserLoginStatus() {
            SocketIOManager.shared.checkAndHandleReconnectSocket()
            self.resetBadgeCount()
        }
    }
    
    func resetBadgeCount() {
        if AppSingletonObj.isConnectedToNetworkForCheckoutScreen() {
            let authorization = getAuthorizationText()
            NotificationAPI.badgeReset(authorization: authorization, accept: AcceptParamForHeader, lang: CurrentAppLang) { data, error in
                
            }
        }
    }
    
    func applicationWillTerminate(_ application: UIApplication) {
        // Called when the application is about to terminate. Save data if appropriate. See also applicationDidEnterBackground:.
    }
}

extension AppDelegate{
    
    func handleURLRedirection(_ incomingURL: URL) -> Bool?{
        guard let component = URLComponents(url: incomingURL, resolvingAgainstBaseURL: false) else { return nil}
        
        if component.path == FirebaseConstants.DeepLinkType.openProduct {
            guard let queryItems = component.queryItems else {return nil}
            /*for queryItem in queryItems {
             // For refering the value of query param and value
             print("Parameters \(queryItem.name) has a value of \(queryItem.value ?? "")")
             }*/
            if let productId = queryItems.first(where: { $0.name == FirebaseConstants.QueryParameter.id })?.value{
                if User.shared.checkUserLoginStatus() {
                    NotificationCenter.default.post(name: NSNotification.Name("ProductFromDeepLink"), object: ["productId": productId], userInfo: nil)
                }
            }
            return true
        }
        else if component.path == FirebaseConstants.DeepLinkType.openOffer {
            guard let queryItems = component.queryItems else {return nil}
            /*for queryItem in queryItems {
             // For refering the value of query param and value
             print("Parameters \(queryItem.name) has a value of \(queryItem.value ?? "")")
             }*/
            if let offerId = queryItems.first(where: { $0.name == FirebaseConstants.QueryParameter.id })?.value{
                if User.shared.checkUserLoginStatus() {
                    NotificationCenter.default.post(name: NSNotification.Name("OfferFromDeepLink"), object: ["offerId": offerId], userInfo: nil)
                }
            }
            return true
        }
        else if component.path == FirebaseConstants.DeepLinkType.openBanner {
            guard let queryItems = component.queryItems else {return nil}
            /*for queryItem in queryItems {
             // For refering the value of query param and value
             print("Parameters \(queryItem.name) has a value of \(queryItem.value ?? "")")
             }*/
            if let bannerId = queryItems.first(where: { $0.name == FirebaseConstants.QueryParameter.id })?.value{
                if User.shared.checkUserLoginStatus() {
                    NotificationCenter.default.post(name: NSNotification.Name("BannerInfoFromDeepLink"), object: ["bannerId": bannerId], userInfo: nil)
                }
            }
            return true
        }
        else if component.path == FirebaseConstants.DeepLinkType.openCategory {
            guard let queryItems = component.queryItems else {return nil}
            /*for queryItem in queryItems {
             // For refering the value of query param and value
             print("Parameters \(queryItem.name) has a value of \(queryItem.value ?? "")")
             }*/
            if let categoryId = queryItems.first(where: { $0.name == FirebaseConstants.QueryParameter.id })?.value{
                if let categoryName = queryItems.first(where: { $0.name == FirebaseConstants.QueryParameter.category_name })?.value{
                    if User.shared.checkUserLoginStatus() {
                        NotificationCenter.default.post(name: NSNotification.Name("GoToProductList"), object: ["categoryId": categoryId, "categoryName": categoryName], userInfo: nil)
                    }
                }
            }
            return true
        }
        // invite friends
        else if incomingURL.absoluteString.contains(FirebaseConstants.DeepLinkType.inviteFriends) {
            guard let queryItems = component.queryItems else {return nil}
            if let link = queryItems.first(where: { $0.name == "link" })?.value {
                guard let inviteCode = link.components(separatedBy: "=").last else { return true}
                UserDefaults.standard.inviteCode = inviteCode
                debugPrint(UserDefaults.standard.inviteCode)
            }
            return true
        }
        return nil
    }
    
    func handleIncomingDynamicLink(_ dynamicLink: DynamicLink) {
        guard let url = dynamicLink.url else {
            debugPrint("That's weird. My dynamic link object has no url")
            return
        }
        debugPrint("Your incoming link parameter is : \(url.absoluteURL)")
    }
    
    
    func setUpURLRedirectionFromDidFinishLaunching(didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?){
        //Deeplinking Redirection - Application in kill state
        if let userActDic = launchOptions?[UIApplication.LaunchOptionsKey.userActivityDictionary] as? [String: Any],
           let  auserActivity  = userActDic["UIApplicationLaunchOptionsUserActivityKey"] as? NSUserActivity{
            
            NSLog("type \(userActDic.self),\(userActDic)") // using NSLog for logging as print did not log to my 'Device and Simulator' logs
            let launchURL = auserActivity.webpageURL
            DispatchQueue.main.asyncAfter(deadline: .now()+2.0, execute: {
                _ = self.handleURLRedirection(launchURL!)
            })
        }
    }
    
    func setUpURLRedirectionFromOpenURL(_ app: UIApplication, open url: URL, options: [UIApplication.OpenURLOptionsKey: Any] = [:]) -> Bool?{
        debugPrint("I have received a URL through a custome URL schema! \(url.absoluteURL)")
        if let dynamicLink = DynamicLinks.dynamicLinks().dynamicLink(fromCustomSchemeURL: url) {
            // Handle the deep link. For example, show the deep-linked content or
            // apply a promotional offer to the user's account.
            // ...
            debugPrint("My URL : \(dynamicLink)")
            self.handleIncomingDynamicLink(dynamicLink)
            return true
        }
        return nil
    }
    
    func setUpURLRedirectionFromContinueUserActivity(_ application: UIApplication, continue userActivity: NSUserActivity, restorationHandler: @escaping ([UIUserActivityRestoring]?) -> Void, completion: @escaping ((_ isFireBaseLink: Bool) -> Void)){
        if let incomingURL = userActivity.webpageURL {
            debugPrint("Incoming URL is \(incomingURL)")
            
            let linkHandle = DynamicLinks.dynamicLinks().handleUniversalLink(incomingURL) { (dynamiclink, error) in
                guard error == nil else {
                    debugPrint("Found An Error! \(error!.localizedDescription)")
                    completion(false)
                    return
                }
                if let dynamiclink = dynamiclink?.url {
                    debugPrint(dynamiclink)
                    completion(true)
                    _ = self.handleURLRedirection(dynamiclink)
                }else{
                    completion(false)
                }
            }
            if linkHandle {
                debugPrint("Your incoming link parameter is : \(incomingURL.absoluteURL)")
                _ = self.handleURLRedirection(incomingURL.absoluteURL)
            }
        }
    }
}

extension URL {
    func valueOf(_ queryParameterName: String) -> String? {
        guard let url = URLComponents(string: self.absoluteString) else { return nil }
        return url.queryItems?.first(where: { $0.name == queryParameterName })?.value
    }
}
