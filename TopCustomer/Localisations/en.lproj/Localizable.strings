"Name" = "Name";
"Done" = "Done";
"OK" = "OK";

"Yes" = "Yes";
"No" = "No";

"Are you sure you want to cancel your order?" = "Are you sure you want to cancel your order?";


"Privacy Policy" = "Privacy Policy";
"Terms and Conditions" = "Terms and Conditions";


"New Products" = "New Products";

"Home" = "Home";
"My Orders" = "My Orders";
"Offers" = "Offers";
"Settings" = "Settings";
"Cancel" = "Cancel";
"Log out" = "Log out";

"Current Orders" = "Current Orders";
"Scheduled Orders" = "Scheduled Orders";


"No Internet Connection." = "No Internet Connection.";
"Are you sure you want to logout?" = "Are you sure you want to logout?";

// Login screen
"Name cannot be blank." = "Name cannot be blank.";
"Country code cannot be blank." = "Country code cannot be blank.";
"Phone no. cannot be blank." = "Phone no. cannot be blank.";
"Phone no. must be between 9 to 12 characters." = "Phone no. must be between 9 to 12 characters.";
"Please accept privacy policy." = "Please accept privacy policy.";
"Phone no." = "Phone no.";
"I agree with Privacy Policy" = "I agree with Privacy Policy";
"Sign in" = "Sign in";
"Search Country" = "Search Country";



// OTP screen
"HELLO" = "HELLO";
"Welcome to MATERIAL" = "Welcome to MATERIAL";
"Please enter the veriﬁcation code that sent to your phone no." = "Please enter the veriﬁcation code that sent to your phone no.";
"Send the code again" = "Send the code again";
"Verify" = "Verify";
"OTP cannot be blank." = "OTP cannot be blank.";
"Please enter a valid OTP." = "Please enter a valid OTP.";


// Settings screen
"Balance" = "Your Balance";
"SAR" = "SAR";

"Account" = "Account";
"History Orders" = "History orders";
"Payments" = "Payments";
"Contact Us" = "Contact us";
"Select Language" = "Select Language";
"History Order" = "History Order";


// Account screen
"Type your name here" = "Type your name here";
"Mobile number" = "Mobile number";
"Email Address" = "Email Address";
"Type your Email here" = "Type your Email here";
"Date of Birth" = "Date of Birth";
"Gender" = "Gender";
"Male" = "Male";
"Female" = "Female";
"Update" = "Update";



// History Orders screen
"Order No." = "Order No.";
"Order date" = "Order date";
"Delivered" = "Delivered";
"Cancelled" = "Cancelled";
"Reorder" = "Reorder";
"Subtotal" = "Subtotal";
"Help" = "Help";
"Delivery" = "Delivery";
"Total" = "Total";
"All prices Includes VAT" = "All prices Includes VAT";
"Invoice" = "Invoice";
"Date" = "Date";


// Contact Us screen
"Contact Information" = "Contact Information";
"How Can we help you?" = "How can we help you?";
"Write your message here…" = "Write your message here…";
"Send" = "Send";
"Follow Us" = "Follow Us";
"Email address cannot be blank." = "Email address cannot be blank.";
"Please enter a valid Email address." = "Please enter a valid Email address.";
"Please write your message." = "Please write your message.";


// My cart screen
"Product sku" = "Product sku";
"Quantity" = "Quantity";
"Your Cart is Currently Empty !" = "Your Cart is Currently Empty !";
"Go Shopping" = "Go Shopping";
"Checkout" = "Checkout";
"My Cart" = "My Cart";
"Review Order" = "Review Order";


// Cart checkout screen
"Payment Method" = "Payment Method";
"Change" = "Change";
"Delivery Address" = "Delivery Address";
"Schedule Order" = "Schedule Order";
"Only Once" = "Only Once";
"Every week" = "Every week";
"Every 2 weeks" = "Every 2 weeks";
"Every Month" = "Every Month";
"Notes" = "Notes";
"Write your notes here…" = "Write your notes here…";
"Promo Code" = "Promo Code";
"APPLY" = "APPLY";
"Vat" = "Vat";
"VAT included" = "VAT included";
"VAT reg.no" = "VAT reg.no";
"Pay now" = "Pay now";
"Please select address" = "Please select address.";
"Delivery Shift" = "Delivery Shift";
"Morning" = "Morning";
"Evening" = "Evening";
"Please enter promo code." = "Please enter promo code.";
"REMOVE" = "REMOVE";
"Discount" = "Discount";
"Enter promo code here" = "Enter promo code here";
"From" = "From";


// Product detail popup
"Add to cart" = "Add to cart";
"You may also like" = "You may also like";


// Offer screen
"Offer" = "Offer";
"Please enter valid quantity." = "Please enter valid quantity.";


// Thankyou screen
"Thank you!" = "Thank you!";
"Your order has been sent successfully." = "Your order has been sent successfully.";


// Order detail screen
"Order Status" = "Order Status";
"My Order" = "My Order";
"Cancel Order" = "Cancel Order";
"Details" = "Details";
"Order Received" = "Order Received";
"Ready" = "Ready";
"On the way" = "On the way";
"Track order" = "Track order";
"Expected Delivery Time" = "Expected Delivery Time";
"Order can not be cancelled once its Ready" = "Order can not be cancelled once its Ready";
"Order is cancelled by admin." = "Order is cancelled by admin.";

"Card" = "Card";
"to" = "to";
"payment_changed" = "Payment method has changed successfully";
"payment_card_not_changed" = "Payment method has not changed, Please try again later, or call your bank";
"payment_not_changed" = "Not changed, Please try again later";
"You can't change the method after payment" = "You can't change the method after payment";
"payment failed, please try again." = "Payment failed, please try again.";
"payment done successfully" = "payment done successfully";
"you can't change the payment method to card on delivery" = "you can't change the payment method to card on delivery";
// Add address screen
"Delivery Location" = "Delivery Location";
"Location Name" = "Location Name";
"Ex: Home, Work" = "Ex: Home, Work";
"Save" = "Save";
"Location name cannot be blank" = "Location name cannot be blank.";
"Zipcode cannot be blank" = "Zipcode cannot be blank.";
"Address cannot be blank" = "Address cannot be blank.";



// Schedule order detail screen
"Delivery Date" = "Delivery Date";
"Cancel Schedule" = "Cancel Schedule";
"Will be delivered in" = "Will be delivered in";


// Select location screen
"Location" = "Location";
"New Address" = "New Address";


// Tax invoice screen
"Tax Invoice" = "Tax Invoice";


// Current orders screen
"No Order Found !" = "No Order Found !";
"Looks like you haven’t made your order yet." = "Looks like you haven’t made your order yet.";
"Order pending" = "Order pending";


// Payments screen
"Add a Card" = "Add a Card";
"Wallet" = "Wallet";
"Pay with a new card" = "Pay with a new card";
"No cards here." = "No cards here.";
"Are you sure you want to delete this card?" = "Are you sure you want to delete this card?";


// Choose payment screen
"Choose a payment method" = "Choose a payment method";
"Card on delivery" = "Card on delivery";
"Apple Pay" = "Apple Pay";
"Select" = "Select";
"Please select at least one payment option." = "Please select at least one payment option.";


// Add card screen
"Add new card" = "Add new card";
"Card Number" = "Card Number";
"Expiry date" = "Expiry date";
"CVV code" = "CVV code";
"Add" = "Add";


// Home screen
"Search" = "Search";
"Select address" = "Select address";
"Categories" = "Categories";
"Search here…" = "Search here…";
"Low Quantity" = "Low Quantity";
"Price" = "Price";
"More" = "More";
"Best Sellers" = "Best Sellers";
"off" = "off";

"Only" = "Only";
"left in stock" = "left in stock";



// new
"No data found" = "No result found.";
"No offers." = "No offers.";
"Please select location inside riyadh." = "Please select a location inside Riyadh.";

"Delete my Account" = "Delete my Account";
"Are you sure you want to delete your account? You will lose all your information, order details and account related data. This action is not reversible." = "Are you sure you want to delete your account? You will lose all your information, order details and account related data. This action is not reversible.";
"Skip" = "Skip";
"To access this feature you need to login into application. Do you want to login?" = "To access this feature you need to login into application. Do you want to log in?";


// added on 5 september
"Version" = "Version";
"Out of stock" = "Out of stock";
"Insufficient wallet balance." = "Insufficient wallet balance.";
"Delivery Discount" = "Delivery Discount";
"min" = "min";
"hr" = "hr";
"without VAT" = "without VAT";


// added on 3 october
"Are you sure you want to delete this address?" = "Are you sure you want to delete this address?";

// added on 20 october
"Please grant location permission in order to select address." = "The app can’t access to your current location. Please grant us Location Permission";
"Please grant notification permission in order to receive notifications." = "To have a better experience on your order please grant us a Notification permission.";


// added on 9 november
"Continue Shopping" = "Continue Shopping";

// added on 11 november
"Allow" = "Allow";
 

// added on 15 november
"Driver will call" = "Driver will call";
"Upon arrival, driver will call this number" = "Upon arrival, driver will call this number";

// added on 25 november
"Today" = "Today";
"Tomorrow" = "Tomorrow";


// added on 8 december
"Choose a reason for order cancellation" = "Choose a reason for order cancellation";
"Submit" = "Submit";
"Please select a reason." = "Please select a reason.";


// added on 21 january
"check it out on Material now." = "check it out on Material now.";


// added on 9 February
"Are you satisfied with our service?" = "Are you satisfied with our service?";
"Please rate" = "Please rate";
"Leave a review (Optional)" = "Leave a review (Optional)";


// added on 13 February
"Please select a rating by tapping on the stars." = "Please select a rating by tapping on the stars.";


"Order Type" = "Order Type";


"No Favorite Product!" = "No Favorite Product!";
"Are you sure you want to remove all products from your favorites?" = "Are you sure you want to remove all products from your favorites?";

"My favorites" = "My favorites";
"My account" = "My account";

"Favorites" = "Favorites";

"Please select delivery shift." = "Please select delivery shift.";

"Update available" = "Update available";
"Not now" = "Not now";

"Notify me" = "Notify me";

"Rate this app" = "Rate this app";

"Speech recognition will be used to determine which words you speak to search products.Please allow access to speech recognition from settings" = "Speech recognition will be used to determine which words you speak to search products.Please allow access to speech recognition from settings";

"Your microphone will be used to record your speech to search products. Please allow access to use your microphone from settings." = "Your microphone will be used to record your speech to search products. Please allow access to use your microphone from settings.";
"Free Delivery" = "Free Delivery";
"Coming Soon" = "Coming Soon";
"You may also like?" = "Suggested For You";
"You got free shipping!" = "You got free shipping!";
"Only" = "Only";
"left for free shipping!" = "left for free shipping!";
"Recent Searches" = "Recent Searches";
"Are you sure to clear all previous searches?" = "Are you sure to clear all previous searches?";
"Total offer price" = "Total offer price";
"You got the offer" = "You got the offer";
"Add to get the offer" =  "Add %2d to get the offer";
"Order No:" = "Order No:";
"Amount:" = "Amount:";
"Coins:" = "Coins:";
"Bottles" = "Coins";
"Your rewards" = "My Coins";
"To get coin as a gift" = "To get +%2d coins as a gift";
"collect_material_desc" = "Start collecting coins without any minimum purchase requirement.";
"buy_products_desc" = "Redeem your coins to receive discounts on your orders or even cover the entire purchase amount.";
"continue shopping" = "continue shopping";
"Congrats !" = "Congrats !";
"You earn up to material coins." = "You will get %2d coins by completing your order.";
"Points History" = "Points History";
"Reward Details" = "Reward Details";
"reward" = "Rewards";
"Get discount using your points" = "Get %2d discount using your coins";
"Pay with coins" = "Pay with coins";
"You will use points" = "*You will use %2d coins.";
"Points" = "Points";
"Coins" = "Coins";
"You paid with coins." = "You used %2d coins";
"Last Offers" = "Last Offers";
"Discount is active" = "Discount is active";
"Coins discount" = "(Coins discount)";
"You have achieved the highest level" = "You have achieved the highest level";
"Collect" = "Collect";
"You have earned coin by completing your order" = "You have earned %2d coin by completing your order";
"Gift" = "Gift";
"New" = "New";
"Ad" = "Ad";
"some items at cart not available" = "Some items at cart not available";
"Loading" = "Loading";
"bundles" = "Bundles";
"Add bundle to cart" = "Add bundle to cart";
"products:" = "products:";
"select_mosque" = "select the mosque";
"the_cart_inclue_items_of_mosque" = "The cart contains products for delivery to mosques. Please choose products for delivery to mosques only";
"delivery_to_mosques" = "Delivery to mosques";
"please_select_address_of_mosque_to_continue_your_order" = "please select address of mosque to continue your order";
"select_address_of_mosque" = "Select address of mosque";
"You_can't_add_products_delivery_to_mosques_with_others_products" = "You can't add products delivery to mosques with others products";
"with VAT" = "with VAT";
"contain_these_products:" = "Contain these products:";
"today" = "Today";
"friday" = "Friday";
"saturday" = "Saturday";
"sunday" = "Sunday";
"monday" = "Monday";
"tuesday" = "Tuesday";
"wednesday" = "Wednesday";
"thursday" = "Thursday";
"choose_delivery_date" = "Choose a Delivery Date";
"confirm" = "Confirm";
"please_choose_delivery_date" = "Please Choose a Delivery Date";
"tomorrow" = "Tomorrow";
"an_error_occurred_please_try_again_later" = "An error occurred, please try again later.";
"share" = "Share";
"your_referral_code" = "YOUR REFERRAL CODE";
"invite_get_money" = "Invite & Get %2d SAR";
"your_referral_code_is_copied" = "your referral code is copied";
"link_copied" = "Link copied";
"invite_your_friends_and_get_cash_prizes" = "Invite your friends and get cash prizes";
"invite_your_friends" = "Invite your friends";
"download_the_material_app_and_get_free_credit" = "Download the Material app and get free credit upon registration 👌 💙";
"select_date" = "Select date";
"shift_is_not_available" = "shift is not available";
"please_choose_another_day" = "please choose another day";
"invite_your_friends_get_money" = "Invite your friends & get %2d";
"tabby" = "tabby";
"divide_your_invoice_on_4_months" = "divide your invoice on 4 months";
"payment_failed_please_try_again_or_change_type_of_pay" = "payment failed, please try again or change type of pay";
"Tabby" = "Tabby";
"error" = "error";
"fast_deivery" = "Fast Deivery";
"price_updated" = "Price Updated";
"quantities_discount" = "Quantities Discount";
"buy_piece_with_quantities_discount" =  "Buy %1d-%2d \n %3d %4d\\piece";
"piece" = "piece";
"or_over" = "or over";
"order_now" = "Order Now";
"select_city" = "Select City";
"riyadh" = "Riyadh";
"alkhabar" = "al khobar";
"please_select_city" = "please select city";
"payment_failed" = "Payment Failed.";
"payment_cancelled" = "Payment Cancelled";
"are_you_sure_you_want_to_cancel_the_payment" = "Are you sure you want to cancel the payment?";
"available" = "Available";
"not_available" = "Not Available";
"please_install_whatsapp" = "Please Install Whatsapp";
"Where would you like to order today?" = "Where would you like to order today?";
"More stores in your area" = "More stores in your area";
