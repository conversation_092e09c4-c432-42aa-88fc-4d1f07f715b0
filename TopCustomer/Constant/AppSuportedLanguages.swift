
import Foundation

enum En_LANGUAGES {
    
    case Arabic, English
    
    func getCode() -> String {
        switch self {
        case .Arabic: return UserAPI.VLanguage_userLanguage.ar.rawValue
        case .English:  return UserAPI.VLanguage_userLanguage.en.rawValue
        }
    }
    func getCode_Int() -> Int {
        switch self {
        case .Arabic: return 1
        case .English:  return 0
        }
    }
    func getTitle() -> String {
        switch self {
        case .Arabic: return "ArabicTitle".localized
        case .English:  return "EnglishTitle".localized
        }
    }
    
}
