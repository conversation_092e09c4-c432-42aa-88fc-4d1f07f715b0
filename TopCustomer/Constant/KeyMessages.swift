
import UIKit

//USERDEFAULT
let kDeviceToken = "DEVICE_TOKEN"
let kDeviceTokenRandomCreated = "DEVICE_TOKEN_RANDOM_CREATED"
let kAuthKey = "vAuthKey"

class KeyMessages: NSObject {

    let kLABEL_NAME = "Name".localized
    let kLABEL_DONE = "Done".localized
    let kLABEL_OK = "OK".localized

    let kLABEL_YES = "Yes".localized
    let kLABEL_NO = "No".localized

    let kMSG_CANCEL_ORDER_CONFIRMATION = "Are you sure you want to cancel your order?".localized

    
    let kLABEL_PRIVACY_POLICY = "Privacy Policy".localized
    let kLABEL_TERMS_OF_SERVICE = "Terms and Conditions".localized

    
    let kLABEL_NEW = "New Products".localized
    
    let kLABEL_HOME = "Home".localized
    let kLABEL_MY_ORDERS = "My Orders".localized
    let kLABEL_OFFERS = "Offers".localized
    let kLABEL_SETTINGS = "Settings".localized
    let kLABEL_CANCEL = "Cancel".localized
    let kLABEL_LOG_OUT = "Log out".localized

    let kLABEL_CURRENT_ORDERS = "Current Orders".localized
    let kLABEL_SCHEDULED_ORDERS = "Scheduled Orders".localized

    
    let kMSG_NO_INTERNET = "No Internet Connection.".localized
    let kMSG_LOG_OUT = "Are you sure you want to logout?".localized

    // Login screen
    let kALERT_ENTER_NAME = "Name cannot be blank.".localized
    let kALERT_ISD_CODE = "Country code cannot be blank.".localized
    let kALERT_MOBILE_NUMBER = "Phone no. cannot be blank.".localized
    let kALERT_MOBILE_NUMBER_TEN_DIGITS = "Phone no. must be between 9 to 12 characters.".localized
    let kALERT_SELECT_PRIVACY = "Please accept privacy policy.".localized
    let kLABEL_PHONE_NO = "Phone no.".localized
    let kLABEL_I_AGREE_WITH = "I agree with Privacy Policy".localized
    let kLABEL_SIGN_IN = "Sign in".localized
    let kLABEL_SEARCH_COUNRTY = "Search Country".localized

    
    
    // OTP screen
    let kLABEL_HELLO = "HELLO".localized
    let kLABEL_WELCOME_TO = "Welcome to MATERIAL".localized
    let kALERT_ENTER_OTP = "Please enter the veriﬁcation code that sent to your phone no.".localized
    let kLABEL_SEND_CODE_AGAIN = "Send the code again".localized
    let kLABEL_VERIFY = "Verify".localized
    let kALERT_OTP_VERIFY = "OTP cannot be blank.".localized
    let kALERT_OTP_LENGTH = "Please enter a valid OTP.".localized

    func getSaudiRiyalSymbol(value: String, isDiscounted: Bool = false) -> NSAttributedString {
        let imageAttachment = NSTextAttachment()
        imageAttachment.image = UIImage(named: "Saudi_Riyal_Symbol") // SF Symbol (or use a custom image)
        if isDiscounted {
            imageAttachment.bounds = CGRect(x: 0, y: 0, width: 12, height: 12) // Adjust position
        } else {
            imageAttachment.bounds = CGRect(x: 0, y: -3, width: 16, height: 16) // Adjust position
        }
        let label = UILabel()
        let fullString = NSMutableAttributedString(string: "")
        fullString.append(NSAttributedString(attachment: imageAttachment))
        fullString.append(NSAttributedString(string: " \(value) "))
        if isDiscounted {
            fullString.addAttribute(NSAttributedString.Key.strikethroughStyle, value: NSUnderlineStyle.single.rawValue, range: NSRange(location: 0, length: value.length + 2))
            fullString.addAttribute(NSAttributedString.Key.strikethroughColor, value: UIColor.AppTheme_StrikeThroughColor_707070, range: NSRange(location: 0, length: value.length + 2))
        }
        label.attributedText = fullString
        return label.attributedText ?? NSAttributedString(string: "")
    }
    
    // Settings screen
//    let kLABEL_ALL_YOUR_WISHES = "ALL YOUR WISHES".localized
    let kLABEL_BALANCE = "Balance".localized
    let kLABEL_SAR = "SAR".localized

    let kLABEL_ACCOUNT = "Account".localized
    let kLABEL_HISTORY_ORDERS = "History Orders".localized
    let kLABEL_PAYMENTS = "Payments".localized
    let kLABEL_CONTACT_US = "Contact Us".localized
    let kLABEL_SELECT_LANGUAGE = "Select Language".localized
    let kLABEL_HISTORY_ORDER = "History Order".localized

    
    // Account screen
    let kLABEL_ACCOUNT_NAME_PLACEHOLDER = "Type your name here".localized
    let kLABEL_MOBILE_NO = "Mobile number".localized
    let kLABEL_EMAIL_ADDRESS = "Email Address".localized
    let kLABEL_EMAIL_PLACEHOLDER = "Type your Email here".localized
    let kLABEL_DATE_OF_BIRTH = "Date of Birth".localized
    let kLABEL_GENDER = "Gender".localized
    let kLABEL_MALE = "Male".localized
    let kLABEL_FEMALE = "Female".localized
    let kLABEL_UPDATE = "Update".localized
//    let kMSG_EMPTY_DOB = "Date of Birth can not be blank".localized

    
    
    // History Orders screen
    let kLABEL_ORDER_NO = "Order No.".localized
    let kLABEL_DATE = "Order date".localized
    let kLABEL_DELIVERED = "Delivered".localized
    let kLABEL_CANCELLED = "Cancelled".localized
    let kLABEL_REORDER = "Reorder".localized
    let kLABEL_SUBTOTAL = "Subtotal".localized
    let kLABEL_HELP = "Help".localized
    let kLABEL_DELIVERY = "Delivery".localized
    let kLABEL_TOTAL = "Total".localized
    let kLABEL_VAT_INCLUDED = "VAT included".localized
    let kLABEL_ALL_PRICES = "All prices Includes VAT".localized
    let kLABEL_INVOICE = "Invoice".localized
    let kLABEL_DATE_ONLY = "Date".localized


    // Contact Us screen
    let kLABEL_CONTACT_INFO = "Contact Information".localized
    let kLABEL_HOW_CAN = "How Can we help you?".localized
    let kLABEL_WRITE_YOUR_MSG = "Write your message here…".localized
    let kLABEL_SEND = "Send".localized
    let kLABEL_FOLLOW_US = "Follow Us".localized
    let kALERT_VALIDATE_EMAIL = "Email address cannot be blank.".localized
    let kALERT_VALIDATE_EMAIL_VALID = "Please enter a valid Email address.".localized
    let kALERT_DESC_BLANK = "Please write your message.".localized

    
    // My cart screen
    let kLABEL_PRODUCT_SKU = "Product sku".localized
    let kLABEL_QUANTITY = "Quantity".localized
    let kLABEL_EMPTY_CART = "Your Cart is Currently Empty !".localized
    let kLABEL_GO_SHOPPING = "Go Shopping".localized
    let kLABEL_CHECKOUT_NOW = "Checkout".localized
    let kLABEL_MY_CART = "My Cart".localized
    let kLABEL_REVIEW_ORDER = "Review Order".localized

    
    // Cart checkout screen
    let kLABEL_PAYMENT_METHOD = "Payment Method".localized
    let kLABEL_CHANGE = "Change".localized
    let kLABEL_DELIVERY_ADDRESS = "Delivery Address".localized
    let kLABEL_SCHEDULE_ORDER = "Schedule Order".localized
    let kLABEL_ONLY_ONCE = "Only Once".localized
    let kLABEL_EVERY_WEEK = "Every week".localized
    let kLABEL_EVERY_TWO_WEEKS = "Every 2 weeks".localized
    let kLABEL_EVERY_MONTH = "Every Month".localized
    let kLABEL_NOTES = "Notes".localized
    let kLABEL_WRITE_YOUR_NOTES = "Write your notes here…".localized
    let kLABEL_PROMO_CODE = "Promo Code".localized
    let kLABEL_APPLY = "APPLY".localized
    let kLABEL_VAT = "Vat".localized
    let kLABEL_VAT_INCLUDES = "VAT included".localized
    let kLABEL_VAT_REG_NO = "VAT reg.no".localized
    let kLABEL_PAY_NOW = "Pay now".localized
    let kMSG_EMPTY_ADDRESS = "Please select address".localized
    let kLABEL_DELIVERY_SHIFT = "Delivery Shift".localized
    let kLABEL_MORNING = "Morning".localized
    let kLABEL_EVENING = "Evening".localized
    let kMSG_EMPTY_PROMO_CODE = "Please enter promo code.".localized
    let kMSG_EMPTY_PAYMENT_METHOD = "Please choose the payment method".localized
    let kLABEL_REMOVE = "REMOVE".localized
    let kLABEL_DISCOUNT = "Discount".localized
    let kLABEL_PROMO_CODE_PLACEHOLDER = "Enter promo code here".localized
    let KLABEL_FREE_DELIVERY = "Free Delivery".localized

    let kLABEL_FROM = "From".localized
//    let kLABEL_AM = "AM".localized
//    let kLABEL_PM = "PM".localized

    
    // Product detail popup
    let kLABEL_ADD_TO_CART = "Add to cart".localized
    let kLABEL_YOU_MAY = "You may also like".localized

    
    // Offer screen
    let kLABEL_OFFER = "Offer".localized
    let kMSG_VALID_QUANTITY = "Please enter valid quantity.".localized

    
    // Thankyou screen
    let kLABEL_THANK_YOU = "Thank you!".localized
    let kMSG_ORDER_SENT = "Your order has been sent successfully.".localized


    // Order detail screen
    let kLABEL_ORDER_STATUS = "Order Status".localized
    let kLABEL_MY_ORDER = "My Order".localized
    let kLABEL_CANCEL_ORDER = "Cancel Order".localized
    let kLABEL_DETAILS = "Details".localized
    let kLABEL_ORDER_RECEIVED = "Order Received".localized
    let kLABEL_READY = "Ready".localized
    let kLABEL_ON_THE_WAY = "On the way".localized
    let kLABEL_EXPECTED_DELIVERY_TIME = "Expected Delivery Time".localized
    let kMSG_ORDER_CANCEL_NOTE = "Order can not be cancelled once its Ready".localized
    let kMSG_ORDER_CANCELLED_BY_ADMIN = "Order is cancelled by admin.".localized

    let kLABEL_CARD = "Card".localized
    let kLABEL_TO = "to".localized

    
    
    // Add address screen
    let kLABEL_DELIVERY_LOCATION = "Delivery Location".localized
    let kLABEL_LOCATION_NAME = "Location Name".localized
    let kLABEL_LOCATION_PLACEHOLDER = "Ex: Home, Work".localized
    let kLABEL_SAVE = "Save".localized
    let kMSG_EMPTY_LOCATION_NAME = "Location name cannot be blank".localized
    let kMSG_EMPTY_ZIPCODE = "Zipcode cannot be blank".localized
    let kMSG_EMPTY_DETECTED_ADDRESS = "Address cannot be blank".localized

    
    
    // Schedule order detail screen
    let kLABEL_DELIVERY_DATE = "Delivery Date".localized
    let kLABEL_CANCEL_SCHEDULE = "Cancel Schedule".localized
    let kLABEL_WILL_BE_DELIVERED = "Will be delivered in".localized

    
    // Select location screen
    let kLABEL_LOCATION = "Location".localized
    let kLABEL_NEW_ADDRESS = "New Address".localized

    
    // Tax invoice screen
    let kLABEL_TAX_INVOICE = "Tax Invoice".localized

    
    // Current orders screen
    let kLABEL_NO_ORDER_FOUND = "No Order Found !".localized
    let kLABEL_NO_ORDER_MADE = "Looks like you haven’t made your order yet.".localized
    let kLABEL_ORDER_PENDING = "Order pending".localized

    
    // Payments screen
    let kLABEL_ADD_A_CARD = "Add a Card".localized
    let kLABEL_WALLET = "Wallet".localized
    let kLABEL_PAY_WITH_OTHER_CARD = "Pay with a new card".localized
    let kLABEL_NO_CARDS_HERE = "No cards here.".localized
    let kMSG_DELETE_CARD_CONFIRMATION = "Are you sure you want to delete this card?".localized
    let kLABEL_Coins = "Coins".localized
    let kLABEL_Tabby = "Tabby".localized
    
    // Choose payment screen
    let kLABEL_CHOOSE_PAYMENT_METHOD = "Choose a payment method".localized
    let kLABEL_CARD_ON_DELIVERY = "Card on delivery".localized
    let kLABEL_APPLE_PAY = "Apple Pay".localized
    let kLABEL_SELECT = "Select".localized
    let kLABEL_NO_CARD_SELECTED = "Please select at least one payment option.".localized

    
    // Add card screen
    let kLABEL_ADD_NEW_CARD = "Add new card".localized
    let kLABEL_CARD_NUMBER = "Card Number".localized
    let kLABEL_EXPIRY_DATE = "Expiry date".localized
    let kLABEL_CVV_CODE = "CVV code".localized
    let kLABEL_ADD = "Add".localized


    // Home screen
    let kLABEL_SEARCH = "Search".localized
    let kLABEL_SELECT_ADDRESS = "Select address".localized
    let kLABEL_CATEGORIES = "Categories".localized
    let kLABEL_SEARCH_HERE = "Search here…".localized
    let kLABEL_LOW_QUANTITY = "Low Quantity".localized
    let kLABEL_PRICE = "Price".localized
    let kLABEL_MORE = "More".localized
    let kLABEL_BEST_SELLERS = "Best Sellers".localized
    let kLABEL_OFF = "off".localized
    
    let kLABEL_ONLY = "Only".localized
    let kLABEL_LEFT_IN_STOCK = "left in stock".localized

    
    // New
    let kLABEL_NO_DATA_FOUND = "No data found".localized
    let kLABEL_NO_OFFERS = "No offers.".localized

    
    // We added arabic manually for below texts
    let kLABEL_TRACKING = "Track order".localized
    let kLABEL_INVALID_LOCATION = "Please select location inside riyadh.".localized

    // added on 2 september - not in localisable file
    let kLABEL_DELETE_MY_ACCOUNT = "Delete my Account".localized
    let kMSG_DELETE_MY_ACCOUNT = "Are you sure you want to delete your account? You will lose all your information, order details and account related data. This action is not reversible.".localized
    let kLABEL_SKIP = "Skip".localized
    let kMSG_USER_IS_NOT_LOGIN_INTO_APPLICATION = "To access this feature you need to login into application. Do you want to login?".localized
    
    
    
    
    
    // added on 5 september
    let kLABEL_VERSION = "Version".localized

    // added on 5 september
    let kLABEL_OUT_OF_STOCK = "Out of stock".localized

    // added on 12 september
    let kMSG_LOW_WALLET_BALANCE = "Insufficient wallet balance.".localized

    // added on 13 september
    let kLABEL_DELIVERY_DISCOUNT = "Delivery Discount".localized

    // added on 14 september
    let kLABEL_MINUTE = "min".localized
    let kLABEL_HOUR = "hr".localized
    let kLABEL_WITHOUT_VAT = "without VAT".localized
    let kLABEL_WITH_VAT = "with VAT".localized

    // added on 3 october
    let kMSG_DELETE_ADDRESS_CONFIRMATION = "Are you sure you want to delete this address?".localized

    // added on 20 october
    let kMSG_LOCATION_PERMISSION = "Please grant location permission in order to select address.".localized
    let kMSG_NOTIFICATION_PERMISSION = "Please grant notification permission in order to receive notifications.".localized

    // added on 9 november
    let kLABEL_CONTINUE_SHOPPING = "Continue Shopping".localized

    // added on 11 november
    let kLABEL_ALLOW = "Allow".localized

    
    // added on 11 november
    let kLABEL_DRIVER_WILL_CALL = "Driver will call".localized
    let kLABEL_UPON_ARRIVAL = "Upon arrival, driver will call this number".localized
//    let kLABEL_ALTERNATE_NUMBER = "Alternate Number".localized

    
    // added on 25 november
    let kLABEL_TODAY = "Today".localized
    let kLABEL_TOMORROW = "Tomorrow".localized

    
    // added on 8 december
    let kLABEL_CANCEL_REASON_TITLE = "Choose a reason for order cancellation".localized
    let kLABEL_SUBMIT = "Submit".localized
    let kMSG_SELECT_ORDER_CANCEL_REASON = "Please select a reason.".localized
    
    // added on 21 january
    let kLABEL_SHARE_TEXT = "check it out on Material now.".localized

    // added on 9 February
    let kLABEL_ARE_YOU_SATISFIED = "Are you satisfied with our service?".localized
    let kLABEL_PLEASE_RATE = "Please rate".localized
    let kLABEL_LEAVE_A_REVIEW = "Leave a review (Optional)".localized
    
    // added on 13 February
//    let kLABEL_RATE_NOW = "Rate now".localized
    let kMSG_NO_RATING = "Please select a rating by tapping on the stars.".localized

    
    let kLABEL_ORDER_TYPE = "Order Type".localized

    
    let kMSG_EMPTY_FAV_PRODUCT = "No Favorite Product!".localized
    let kMSG_DELETE_ALL_FAVORITE = "Are you sure you want to remove all products from your favorites?".localized

    let kLABEL_MY_FAVORITE = "My favorites".localized
    let kLABEL_MY_ACCOUNT = "My account".localized
    
    let kLABEL_FAVORITE = "Favorites".localized
    let kMSG_EMPTY_DELIVERY_SHIFT = "Please select delivery shift.".localized

    let kLABEL_UPDATE_AVAILABLE = "Update available".localized
    let kLABEL_NOT_NOW = "Not now".localized

    let kLABEL_NOTIFY_ME = "Notify me".localized

    let kLABEL_RATE_THIS_APP = "Rate this app".localized

    let kMSG_SPEECH_PERMISSION = "Speech recognition will be used to determine which words you speak to search products.Please allow access to speech recognition from settings".localized
    let kMSG_MICROPHONE_PERMISSION = "Your microphone will be used to record your speech to search products. Please allow access to use your microphone from settings.".localized

    
}



extension NSTextAttachment {
    func setImageHeight(height: CGFloat) {
        guard let image = image else { return }
        let ratio = image.size.width / image.size.height

        bounds = CGRect(x: bounds.origin.x, y: bounds.origin.y, width: ratio * height, height: height)
    }
}
