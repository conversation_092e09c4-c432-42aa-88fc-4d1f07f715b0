
import UIKit
import Reachability
import SystemConfiguration
import Alamofire
//import NVActivityIndicatorView
import IQKeyboardManagerSwift
//import SwiftMessages
//import FirebaseAuth
import Toast_Swift

typealias SOSingltonCompletionHandler = (_ obj:AnyObject?, _ success:Bool?) -> Void


class AppSingleton: NSObject {
    static let shared = AppSingleton()
    static var instance: AppSingleton!

    /// EZSE: Returns app's version number
   public static var appVersion: String? {
       return Bundle.main.object(forInfoDictionaryKey: "CFBundleShortVersionString") as? String
   }

   /// EZSE: Return app's build number
   public static var appBuild: String? {
       return Bundle.main.object(forInfoDictionaryKey: kCFBundleVersionKey as String) as? String
   }
    
    // SHARED INSTANCE
    class func sharedInstance() -> AppSingleton {
        self.instance = (self.instance ?? AppSingleton())
        return self.instance
    }
    
    func showAlert(strMsg: String) {
        /*let alert = UIAlertController(title: AppName , message: strMsg, preferredStyle: UIAlertController.Style.alert)
        alert.addAction(UIAlertAction(title: ObjKeymessages.kLABEL_OK, style: UIAlertAction.Style.default, handler: nil))
        AppDel?.window?.rootViewController?.present(alert, animated: true, completion: nil)*/
        
        /*let vc = settingsStoryboard.instantiate(CustomPopupAlertViewController.self)
        vc.modalPresentationStyle = .overFullScreen
        vc.modalTransitionStyle = .crossDissolve
        vc.strDescription = strMsg
        vc.isCenterButton = true
        vc.strCenterButtonTitle = ObjKeymessages.kLABEL_OK
        vc.completion = { (index) in
        }
        AppDel?.window?.rootViewController?.present(vc, animated: true)*/
        
        self.showCustomPopUpWithOkButton(strButtonTitle: ObjKeymessages.kLABEL_OK, strMessage: strMsg, showOnTopVC: false) { (isOk) in
        }
    }
        
    func isConnectedToNetwork11() -> Bool{
        do {
           let reachability =  try Reachability()
           let valueInternet = reachability.connection != .unavailable
           if !valueInternet{
//               AppSingletonObj.showToast(title: ObjKeymessages.kMSG_NO_INTERNET)
               self.showAlert(strMsg: ObjKeymessages.kMSG_NO_INTERNET)
           }
           return valueInternet
        }catch {}
       
        return true
    }
    
    func displaySessionExpiredAlert(strMsg: String) {
        /*let alert = UIAlertController(title: AppName, message: strMsg, preferredStyle: .alert)
        let okButton = UIAlertAction(title: ObjKeymessages.kLABEL_OK, style: .default, handler: {(_ action: UIAlertAction) -> Void in
            AppDel?.restartApp()
//            self.dismiss(animated: true, completion: nil)
        })
        alert.addAction(okButton)
//        self.present(alert, animated: true) {() -> Void in }
        AppDel?.window?.rootViewController?.present(alert, animated: true, completion: nil)*/
        
        /*let vc = settingsStoryboard.instantiate(CustomPopupAlertViewController.self)
        vc.modalPresentationStyle = .overFullScreen
        vc.modalTransitionStyle = .crossDissolve
        vc.strDescription = strMsg
        vc.isCenterButton = true
        vc.strCenterButtonTitle = ObjKeymessages.kLABEL_OK
        vc.completion = { (index) in
            if index == 0 {
                AppDel?.restartApp()
            }
        }
//        self.presentVC(vc)
//        AppDel?.window?.rootViewController?.present(vc, animated: true, completion: nil)
        UIApplication.shared.topMostViewController?.present(vc, animated: true)*/
        
        self.showCustomPopUpWithOkButton(strButtonTitle: ObjKeymessages.kLABEL_OK, strMessage: strMsg, showOnTopVC: true) { (isOk) in
            if isOk == true {
                AppDel?.restartApp()
            }
        }
    }
    
    //MARK:- Network Rechability
    
    /*func isCheckNetwork() -> Bool {
        var Status:Bool = false
        let url = NSURL(string: "http://google.com/")
        let request = NSMutableURLRequest(url: url! as URL)
        request.httpMethod = "HEAD"
        request.cachePolicy = NSURLRequest.CachePolicy.reloadIgnoringLocalAndRemoteCacheData
        request.timeoutInterval = 10.0
        var response: URLResponse?
        _ = try? NSURLConnection.sendSynchronousRequest(request as URLRequest, returning: &response) as NSData?
        if let httpResponse = response as? HTTPURLResponse {
            if httpResponse.statusCode == 200 {
                Status = true
            }
        }
        print("STATUS ",Status)
        return Status
    }*/
    
    
    func isConnectedToNetwork() -> Bool {
            do {
               let reachability =  try Reachability()
               let valueInternet = reachability.connection != .unavailable
               if !valueInternet{
                   self.showAlert(strMsg: ObjKeymessages.kMSG_NO_INTERNET)
               }
               return valueInternet
            }catch {}
           
            return true
       /* var zeroAddress = sockaddr_in6()
        zeroAddress.sin6_len = UInt8(MemoryLayout.size(ofValue: zeroAddress))
        zeroAddress.sin6_family = sa_family_t(AF_INET6)
        
        let defaultRouteReachability = withUnsafePointer(to: &zeroAddress) {
            $0.withMemoryRebound(to: sockaddr.self, capacity: 1) {zeroSockAddress in
                SCNetworkReachabilityCreateWithAddress(nil, zeroSockAddress)
            }
        }
        
        var flags = SCNetworkReachabilityFlags()
        if !SCNetworkReachabilityGetFlags(defaultRouteReachability!, &flags) {
            return false
        }
        let isReachable = flags.contains(.reachable)
        let needsConnection = flags.contains(.connectionRequired)
        return (isReachable && !needsConnection)*/
        
    }
    
    func isConnectedToNetworkForCheckoutScreen() -> Bool {
            do {
               let reachability =  try Reachability()
               let valueInternet = reachability.connection != .unavailable
               if !valueInternet{
                   return false
               }
               return valueInternet
            }catch {}
            return true
    }

    func isConnectedToNetwork2() -> Bool {
        var zeroAddress = sockaddr_in()
        zeroAddress.sin_len = UInt8(MemoryLayout.size(ofValue: zeroAddress))
        zeroAddress.sin_family = sa_family_t(AF_INET)
        let defaultRouteReachability = withUnsafePointer(to: &zeroAddress) {
            $0.withMemoryRebound(to: sockaddr.self, capacity: 1) {zeroSockAddress in
                SCNetworkReachabilityCreateWithAddress(nil, zeroSockAddress)
            }
        }
        var flags = SCNetworkReachabilityFlags()
        if !SCNetworkReachabilityGetFlags(defaultRouteReachability! , &flags) {
            return false
        }
        //        let isReachable = (flags.rawValue & UInt32(kSCNetworkFlagsReachable)) != 0
        //        let needsConnection = (flags.rawValue & UInt32(kSCNetworkFlagsConnectionRequired)) != 0
        
        
        let isTransientConnection = (flags.rawValue & UInt32(kSCNetworkFlagsTransientConnection)) != 0
        let isReachable = (flags.rawValue & UInt32(kSCNetworkFlagsReachable)) != 0
        let isConnectionRequired = (flags.rawValue & UInt32(kSCNetworkFlagsConnectionRequired)) != 0
        let isConnectionAutomatic = (flags.rawValue & UInt32(kSCNetworkFlagsConnectionAutomatic)) != 0
        let isInterventionRequired = (flags.rawValue & UInt32(kSCNetworkFlagsInterventionRequired)) != 0
        let isLocalAddress = (flags.rawValue & UInt32(kSCNetworkFlagsIsLocalAddress)) != 0
        let isDirect = (flags.rawValue & UInt32(kSCNetworkFlagsIsDirect)) != 0
        print(isTransientConnection)
        print(isReachable)
        print(isConnectionRequired)
        print(isConnectionAutomatic)
        print(isInterventionRequired)
        print(isLocalAddress)
        print(isDirect)
        
        
        return (isReachable && !isConnectionRequired)
    }
    
    //MARK:- Activity Indicatior
    
    
    //MARK:- string extension
    func stringIsEmpty(_ text : String) -> Bool {
        if text.trimmingCharacters(in: .whitespaces).isEmpty {
            return true
        }
        else {
            return false
        }
    }
    
    func generateBoundaryString() -> String {
        return "Boundary-\(NSUUID().uuidString)"
    }

    /*func unArchiveUserModel() -> fir?
    {
        let userData = UserDefaults.retriveUserDataModel()
        
        if userData != nil {
            let unArchive = NSKeyedUnarchiver(forReadingWith: userData! as Data)
            let userDataObj = unArchive.decodeObject(forKey: "root") as! UserData
            return userDataObj
        }
        return nil
    }*/
    
    func convertToDictionary(text: String) -> [String: Any]? {
        if let data = text.data(using: .utf8) {
            do {
                return try JSONSerialization.jsonObject(with: data, options: []) as? [String: Any]
            } catch {
                print(error.localizedDescription)
            }
        }
        return nil
    }

    func goToLoginScreen(message: String) {
        /*let alert : UIAlertController = UIAlertController(title: AppName, message: message, preferredStyle: .alert)
        let alertOkAction = UIAlertAction(title: ObjKeymessages.kLABEL_OK, style: .default) { (alert) in
            AppDel?.restartApp()
        }
        alert.addAction(alertOkAction)
//        AppDel?.window?.rootViewController?.present(alert, animated: true, completion: nil)
        UIApplication.shared.keyWindow?.rootViewController?.presentedViewController?.present(alert, animated: true, completion: nil)*/
        
        AppSingleton.shared.showCustomPopUpWithOkButton(strButtonTitle: ObjKeymessages.kLABEL_OK, strMessage: message, showOnTopVC: false) { (isOk) in
            if isOk == true {
                AppDel?.restartApp()
            }
        }
    }
    
    func showAlert(strMessage: String) {
        let windowsKey = UIApplication.key
        var style = ToastStyle()
        style.messageColor = .black
        style.backgroundColor = UIColor(red: 240/255, green: 240/255, blue: 240/255, alpha: 0.9)
        //UIColor.AppTheme_LightGrayDescColor_A8A8A8
        
        style.cornerRadius = 15
        
        ToastManager.shared.style = style
        windowsKey?.makeToast(strMessage, duration: 3.0, position: .bottom)
//        completion()
    }
    
    func showCustomPopUpWithYesNoButton(strButton1Title: String, strButton2Title: String, strMessage: String, showOnTopVC: Bool, handler: @escaping (_ isOkBtnPressed: Bool) -> Void) {
        let vc = settingsStoryboard.instantiate(CustomPopupAlertViewController.self)
        vc.modalPresentationStyle = .overFullScreen
        vc.modalTransitionStyle = .crossDissolve
        vc.strDescription = strMessage
        vc.strLeftButtonTitle = strButton2Title
        vc.isNeedToSwitchButton = true
        vc.strRightButtonTitle = strButton1Title
        vc.isCenterButton = false
        vc.completion = { (index) in
            if index == 0 {  // if NO is pressed
                handler(false)
            }
            else { // if YES is pressed
                handler(true)
            }
        }
//        self.presentVC(vc)
        if showOnTopVC == true {
            UIApplication.shared.topMostViewController?.present(vc, animated: true)
        }
        else {
            AppDel?.window?.rootViewController?.present(vc, animated: true)
        }
    }
    
    func showCustomPopUpWithOkButton(strButtonTitle: String, strMessage: String, showOnTopVC: Bool, handler: @escaping (_ isOkBtnPressed: Bool) -> Void) {
         let vc = settingsStoryboard.instantiate(CustomPopupAlertViewController.self)
         vc.modalPresentationStyle = .overFullScreen
         vc.modalTransitionStyle = .crossDissolve
         vc.strDescription = strMessage
         vc.isCenterButton = true
         vc.strCenterButtonTitle = strButtonTitle
         vc.completion = { (index) in
             if index == 0 {
                 handler(true)
             }
         }
//         self.presentVC(vc)
        if showOnTopVC == true {
            UIApplication.shared.topMostViewController?.present(vc, animated: true)
        }
        else {
            AppDel?.window?.rootViewController?.present(vc, animated: true)
        }
    }

}

extension UIApplication {
    
    static var key: UIWindow? {
        if #available(iOS 13, *) {
            return UIApplication.shared.windows.first { $0.isKeyWindow }
        } else {
            return UIApplication.shared.keyWindow
        }
    }
}
