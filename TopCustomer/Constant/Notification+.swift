//
//  Notification+.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 14/05/2024.
//  Copyright © 2024 SOTSYS203. All rights reserved.
//

import Foundation

extension Notification.Name {
    static let GetUserSpecificData = Notification.Name("GetUserSpecificData")
    static let InviteFriends = Notification.Name("InviteFriends")
    static let RefreshHomeListing = Notification.Name("RefreshHomeListing")
}
