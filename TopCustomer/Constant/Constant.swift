

import UIKit
var isApplePayPayment = false
let AppDel = (UIApplication.shared.delegate as? AppDelegate)
let SACountryCode = "+966"
let AppSingletonObj = AppSingleton.sharedInstance()
let AppName = "Material"
let MinMoNoLength = 9
let MaxMoNoLength = 12
let MaxQuantityLength = 6

let TransactionTypeCOD = "COD"
let TransactionTypeWallet = "Wallet"
let TransactionTypeCoins = "Coins"
let TransactionTypeTabby = "Tabby"
let InitiateTabby = "initiateTabby"

var VatRegNumber = "311060632800003"

let DefaultEmailForApplePay = "<EMAIL>"

var ContactUSNumber = "+966 920033856"

let APPLICATION_APPSTORE_URL : String = "https://apps.apple.com/sa/app/%D9%85%D8%AA%D9%8A%D8%B1%D9%8A%D8%A7%D9%84/id1626095299"

let APISUCCESSCODE200 = 200
let APICODE400 = 400
let APIINVALIDAUTHORIZATIONCODE401 = 401
let APICODE203 = 203

let MaxOTPLength = 4
let RoleCustomer_2 = 2

var ObjKeymessages: KeyMessages = KeyMessages()

//let MorningShiftStartTime = 9
//let MorningShiftEndTime = 5
//let EveningShiftStartTime = 5
//let EveningShiftEndTime = 12

let MaxNameLength = 30
let MaxLocationNameLength = 55 
let SACurrencyCode = "SAR"
let MerchantCountryCode = "SA"
let vDeviceToken = AppDelegate.getDeviceToken()
let vDeviceName : String = UIDevice.current.name
let vDeviceUniqueId : String = UIDevice.current.identifierForVendor?.uuidString ?? ""
let localTimeZoneIdentifier: String = TimeZone.current.identifier

//var BranchIoResponseJson = ""

let AcceptParamForHeader = "application/json"
var CurrentAppLang = UserDefaults.standard.getLanguage()!
//UserDefaults.standard.setLanguage(language: strLangCode)
//"en"
let SlugTermsAndConditions = "terms-and-conditions"
let SlugPrivacyPolicy = "privacy-policy"

let InstagramURL = "https://www.instagram.com/materialsa_/"
let TwitterURL = "https://twitter.com/materialsa_/"
let AppStoreURL = "https://apps.apple.com/app/id1626095299?action=write-review"

let RiyadhCenterLatitude = 24.730311
let RiyadhCenterLongitude = 46.725364


#if DEBUG
let GuestAccessToken = "Bearer 171267|nMQoWQiTfv3e5FjFLNMauulhtmARWEMdwFXWEZ0K"

// PayTabs credentials - client's account - TEST KEYS
let profileID = "90354"
let serverKey = "SLJN2LDNBZ-JDN2LNJGBH-WBJBJGRTHR"
let clientKey = "CKKMQM-NBM96D-QMBQ6T-NVN2BB"

let MixpanelToken = "4299293e57a7c038a9dc8cd263c137c7"

#elseif STAGING
let GuestAccessToken = "Bearer 77|iotCtW7GAh9alXzD5nGG0dojWMWuiN14493hPqTp"

// PayTabs credentials - client's account - TEST KEYS
let profileID = "90354"
let serverKey = "SLJN2LDNBZ-JDN2LNJGBH-WBJBJGRTHR"
let clientKey = "CKKMQM-NBM96D-QMBQ6T-NVN2BB"

let MixpanelToken = "4299293e57a7c038a9dc8cd263c137c7"

#elseif DEVRELEASE
let GuestAccessToken = "Bearer 55|0eGdLG7dyNNIWBHbX1D8elsX5OYbYqdRHnj9Hcg7"

// PayTabs credentials - client's account - LIVE KEYS
//let profileID = "90540"
//let serverKey = "SBJN2LDN6R-JDNTG9N66K-6RLKWR2R6L"
//let clientKey = "CHKMQM-NBKQ6D-Q7TPQG-2QMDK7"

// Mixpanel API Secret: 8bdb0586ad01cb060806077683843509
//let MixpanelToken = "6f7ba8e5fcc899ec60ad63f53340c409"
// Keys for Testing
let profileID = "90354"
let serverKey = "SLJN2LDNBZ-JDN2LNJGBH-WBJBJGRTHR"
let clientKey = "CKKMQM-NBM96D-QMBQ6T-NVN2BB"

let MixpanelToken = "4299293e57a7c038a9dc8cd263c137c7"

#elseif RELEASESTAGING
let GuestAccessToken = "Bearer 77|iotCtW7GAh9alXzD5nGG0dojWMWuiN14493hPqTp"

// PayTabs credentials - client's account - TEST KEYS
let profileID = "90354"
let serverKey = "SLJN2LDNBZ-JDN2LNJGBH-WBJBJGRTHR"
let clientKey = "CKKMQM-NBM96D-QMBQ6T-NVN2BB"

let MixpanelToken = "4299293e57a7c038a9dc8cd263c137c7"

#else
let GuestAccessToken = "Bearer 55|0eGdLG7dyNNIWBHbX1D8elsX5OYbYqdRHnj9Hcg7"

// PayTabs credentials - client's account - LIVE KEYS
let profileID = "90540"
let serverKey = "SBJN2LDN6R-JDNTG9N66K-6RLKWR2R6L"
let clientKey = "CHKMQM-NBKQ6D-Q7TPQG-2QMDK7"

// Mixpanel API Secret: 8bdb0586ad01cb060806077683843509
let MixpanelToken = "6f7ba8e5fcc899ec60ad63f53340c409"

#endif


// my account
//let profileID = "103073"
//let serverKey = "SJJNGZBRRB-JGBRKTHJHN-GJW9KDW6Z9"
//let clientKey = "CHKMN6-Q9N66T-2RK7H6-PGNBQV"


let PAYTABS_MERCHANT_IDENTIFIER : String = "merchant.com.material.customer"

struct Fonts {
    static var LoewNextArabicBold = "LoewNextArabic-Bold"
    static var LoewNextArabicMedium = "LoewNextArabic-Medium"
    static var LoewNextArabicExtraBold = "LoewNextArabic-ExtraBold"
    static var LoewNextArabicHeavy = "LoewNextArabic-Heavy"

    
}

func setDataInString(_ text : AnyObject) -> String {
    var str = ""
    if text is String {
        str = text as! String
    }else if text is Int {
        str = String(text as! Int)
    }else if text is Float {
        str = String(text as! Float)
    }else if text is Double {
        str = String(text as! Double)
    }else if text is NSNumber {
        str = String(describing: text as! NSNumber)
    }else if text is Bool {
        str = String(describing: text as! Bool)
    }
    return str
}

class Constant: NSObject {
    static var shared = Constant()
    
    public static let GOOGLE_API_KEY : String = "AIzaSyAJ41JHX1Z7nqQVPNa9PKMoq-34sfiJBX0"
    var FREE_DELIVERY = 0.0
    var MY_POINTS_VALUE = 0
    var CURRENT_POINTS_VALUE = ""
    var USER_POINTS_BY_SAR = 0.0
    var POINTS_LEVEL = 0
    var POINTS_MAX_LEVELS = 1
    var WATER_BOTTLE_VALUE = ""
    var IS_WATER_BOTTLE_SHOW = true
    var SHAPE_SECONDS_TIMER_LOADING = 10
    let SHAPE_MAX_VALUE = 180
    var CONGRATS_COINS = 0
    var USED_COINS = 0
    var REMAINING_POINT_LEVEL = 0
    var REWARD_POINT_LEVEL = 0
    var IS_PAYMENT_WITH_COINS = false
    var BACKGROUND_COLOR_SHAPE: UIColor = .white
    var POINTS_DESCRIPTION = [String]()
    var LEVEL_POINT = 0
    var FILE_NAME = "main1"
    var IS_FRIEND_INVITATION_GIFT = false
    var VALUE_GIFT = ""
    var ADVERTISEMENT_INDEX = 0
    var SELECTED_WAREHOUSE_ID = 1
    var SELECTED_ADDRESS_NAME = ""
    var SELECTED_ADDRESS_ID = 0
    var SELECTED_LATITUDE = 0.0
    var SELECTED_LONGITUDE = 0.0
}

enum ScreenNames : String {
    
    case LoginViewController = "LOGIN_SCREEN"
    case PrivacyPolicyScreen = "PRIVACY_POLICY_SCREEN"
    case OTPScreen = "OTP_SCREEN"
    case SideMenuScreen = "SIDE_MENU_SCREEN"
    case HomeViewController = "HOME_SCREEN"
    case ProductDetailScreen = "PRODUCT_DETAIL_SCREEN"
    case AddressListingScreen = "ADDRESS_LISTING_SCREEN"
    case AddAddressScreen = "ADD_ADDRESS_SCREEN"
    case OffersScreen = "OFFERS_SCREEN"
    case OffersDetailsScreen = "OFFERS_DETAILS_SCREEN"
    case MyOrdersCurrent = "CURRENT_ORDERS_SCREEN"
    case MyOrdersScheduled = "SCHEDULED_ORDERS_SCREEN"
    case CurrentOrderDetailScreen = "CURRENT_ORDER_DETAIL_SCREEN"
    case TrackOrderScreen = "TRACK_ORDER_SCREEN"
    case ScheduledOrderDetailScreen = "SCHEDULED_ORDER_DETAIL_SCREEN"
    case SettingsScreen = "SETTINGS_SCREEN"
    case NewProductsScreen = "NEW_PRODUCTS_SCREEN"
    case BestSellerScreen = "BEST_SELLER_SCREEN"
    case SearchScreen = "SEARCH_SCREEN"
    case CategoryProductListScreen = "CATEGORY_PRODUCT_LIST_SCREEN"
    case MyCartScreen = "MY_CART_SCREEN"
    case ReviewOrderScreen = "REVIEW_ORDER_SCREEN"
    case ChoosePaymentScreen = "CHOOSE_PAYMENT_SCREEN"
    case AccountScreen = "ACCOUNT_SCREEN"
    case HistoryOrdersViewController = "HISTORY_ORDER_SCREEN"
    case HistoryOrderDetailScreen = "HISTORY_ORDER_DETAIL_SCREEN"
    case InvoiceScreen = "INVOICE_SCREEN"
    case HelpScreen = "HELP_SCREEN"
    case PaymentsScreen = "PAYMENTS_SCREEN"
    case WalletScreen = "WALLET_SCREEN"
    case ContactUsScreen = "CONTACT_US_SCREEN"
    case TermsAndConditionScreen = "TERMS_AND_CONDITIONS_SCREEN"
    case LanguageSelectionScreen = "LANGUAGE_SELECTION_SCREEN"
    case Logout = "LOGOUT"
    case DeleteMyAccount = "DELETE_MY_ACCOUNT"
    case AddMoneyScreen = "ADD_MONEY_SCREEN"
    case SearchedKeyword = "SEARCHED_KEYWORD"
    case CreateOrder = "CREATE_ORDER"
    case ProductPurchase = "PRODUCT_PURCHASE"
    case BannerInfoScreen = "BANNER_INFO"
    case AdvertisementPopupScreen = "ADVERTISEMENT_SCREEN"
    case RatingPopup  = "RATING"
    case FavoriteScreen  = "FAVOURITE_PRODUCTS"
    case NotifyMeForOutOfStockProduct  = "NOTIFY_ME_FOR_OUT_OF_STOCK_PRODUCT"

    
    /*func getEventName(strVCName: String) -> String {
        switch strVCName  {
        case ScreenNames.LoginViewController.rawValue:
            return ScreenNames.LoginViewController.rawValue
        default:
            return ScreenNames.LoginViewController.rawValue
        }
    }*/
}

enum PushNotificationType : String {
    case ADMIN_ANNOUNCEMENTS = "1"
    case ORDER_PLACED_SUCCESSFULLY = "2"
    case ORDER_STATUS_CHANGE = "3"
    case ORDER_DELIVERED_SUCCESSFULLY = "4"
    case ORDER_DELIVERY_UN_SUCCESSFUL = "5"
    case NOTIFY_CUSTOMER_ORDER_CANCELLED_BY_ADMIN = "8"
    case CART_REMINDER = "6"
    case RECURRING_ORDER_PAYMENT = "7"
    case REFRESH_CURRENT_ORDER_IPN = "13"
    case OFFER_ANNOUNCEMENT = "14"
    case CATEGORY_ANNOUNCEMENT = "15"
    case PRODUCT_ANNOUNCEMENT = "16"
    case NOTIFY_ME = "17"
    case FRIEND_INVITATION_GIFT = "18"
}


/*
 PAYLOAD: [AnyHashable("gcm.message_id"): 1677749701595743, AnyHashable("type"): 7, AnyHashable("vCategoryName"): , AnyHashable("aps"): {
     alert =     {
         body = "Hello Sanket Local, Kindly make the payment for your scheduled order 49191598.";
         title = "Payment pending for scheduled order";
     };
     badge = 0;
     sound = default;
 }, AnyHashable("iOfferId"): 0, AnyHashable("google.c.a.e"): 1, AnyHashable("iOrderId"): 1778, AnyHashable("google.c.fid"): duxEhT0Yv0srtwVSJxs5yH, AnyHashable("google.c.sender.id"): 325738153959, AnyHashable("biProductId"): 0, AnyHashable("iCategoryId"): 0, AnyHashable("iUserId"): 124]

 */

enum OrderStatus : Int {
    case OrderPlaced = 1
    case OrderAssigned = 2
    case OrderInProgress = 3
    case OrderReady = 4
    case OrderOnTheWay = 5
    case OrderDelivered = 6
    case OrderCancelled = 7
}

enum ReccuringType : Int {
    case OnlyOnce = 1
    case EveryWeek = 2
    case Every2Weeks = 3
    case EveryMonth = 4
}

enum TransactionType : Int {
    case Card = 1
    case ApplePay = 2
    case COD = 3
    case Wallet = 4
    case Coins = 8
    case Tabby = 9
    case NoType = 10
}

enum UsedWalletOrNot : Int {
    case NotUsed = 0
    case Used = 1
}

enum ProductType : Int {
    case New = 1
    case BestSeller = 2
}

enum AddToCartIsCheck : Int {
    case Remove = 0
    case Add = 1
}

enum ShiftType : Int {
    case Morning = 1
    case Evening = 2
}

enum HomeBannerType : Int {
    case Product = 1
    case Category = 2
    case Offer = 3
    case ExternalLink = 4
}

enum SettingsEng : String, CaseIterable {
//    case account = "Account"
//    case historyOrders = "History Orders"
//    case payments = "Payments"
    case contactUs = "Contact us"
    case privacyPolicy = "Privacy Policy"
//    case termsAndConditions = "Terms and Conditions"
    case selectLanguage = "English"
    case rateThisApp = "Rate this app"
    case logout = "Log out"
//    case deleteMyAccount = "Delete my Account"

    func getTitle() -> String {
        switch self {
//        case .account:  return ObjKeymessages.kLABEL_ACCOUNT
//        case .historyOrders:  return ObjKeymessages.kLABEL_HISTORY_ORDERS
//        case .payments:  return ObjKeymessages.kLABEL_PAYMENTS
        case .contactUs:  return ObjKeymessages.kLABEL_CONTACT_US
        case .privacyPolicy:  return ObjKeymessages.kLABEL_PRIVACY_POLICY
//        case .termsAndConditions:  return ObjKeymessages.kLABEL_TERMS_OF_SERVICE
        case .selectLanguage:  return "English" //ObjKeymessages.kLABEL_SELECT_LANGUAGE
        case .rateThisApp:  return ObjKeymessages.kLABEL_RATE_THIS_APP
        case .logout:  return ObjKeymessages.kLABEL_LOG_OUT
//        case .deleteMyAccount:  return ObjKeymessages.kLABEL_DELETE_MY_ACCOUNT
        }
    }
}

enum SettingsArb : String, CaseIterable {
//    case account = "Account"
//    case historyOrders = "History Orders"
//    case payments = "Payments"
    case contactUs = "Contact us"
    case privacyPolicy = "Privacy Policy"
//    case termsAndConditions = "Terms and Conditions"
    case selectLanguage = "عربي"
    case rateThisApp = "Rate this app"
    case logout = "Log out"
//    case deleteMyAccount = "Delete my Account"

    func getTitle() -> String {
        switch self {
//        case .account:  return ObjKeymessages.kLABEL_ACCOUNT
//        case .historyOrders:  return ObjKeymessages.kLABEL_HISTORY_ORDERS
//        case .payments:  return ObjKeymessages.kLABEL_PAYMENTS
        case .contactUs:  return ObjKeymessages.kLABEL_CONTACT_US
        case .privacyPolicy:  return ObjKeymessages.kLABEL_PRIVACY_POLICY
//        case .termsAndConditions:  return ObjKeymessages.kLABEL_TERMS_OF_SERVICE
        case .selectLanguage:  return "عربي" //ObjKeymessages.kLABEL_SELECT_LANGUAGE
        case .rateThisApp:  return ObjKeymessages.kLABEL_RATE_THIS_APP
        case .logout:  return ObjKeymessages.kLABEL_LOG_OUT
//        case .deleteMyAccount:  return ObjKeymessages.kLABEL_DELETE_MY_ACCOUNT
        }
    }

}

enum SettingsImages : String,CaseIterable{
//    case account = "icn_account"
//    case historyOrders = "icn_HistoryOrders"
//    case payments = "icn_Payments"
    case contactUs = "whatsapp"
    case privacyPolicy = "icn_PrivacyPolicy"
//    case termsAndConditions = "icn_terms"
    case selectLanguage = "icn_selectLang"
    case rateThisApp = "star"
    case logout = "icn_logout"
//    case deleteMyAccount = "deleteAccount"
}

extension UIView {
    @IBInspectable var isNavigationShadow: Bool {
        get {
            return isApply
        }
        set {
            self.isApply = newValue
            self.applyNavigationShadow()
        }
    }
    
    var isApply: Bool {
        get { return false }
        set { newValue }
    }
    
    func applyNavigationShadow() {
        self.shadowColor = UIColor.AppTheme_ShadowColor_0000003E
        self.shadowOffset = CGSize(width: 0, height: 4)
        self.shadowOpacity = 0.5
    }
    
    @IBInspectable var isViewShadow: Bool {
        get {
            return isViewApply
        }
        set {
            self.isViewApply = newValue
            self.applyViewShadow()
        }
    }
    
    var isViewApply: Bool {
        get { return false }
        set { newValue }
    }
    
    func applyViewShadow() {
        self.shadowColor = UIColor.AppTheme_ShadowColor_0000003E
        self.shadowOffset = CGSize(width: 4, height: 0)
        self.shadowOpacity = 0.5
    }
}

extension UIButton {
    func makeButtonRightToLeftIfRequired() {
        let attribute =  UIView.appearance().semanticContentAttribute
        let layoutDirection = UIView.userInterfaceLayoutDirection(for: attribute)
        if layoutDirection == .rightToLeft {
            self.contentHorizontalAlignment = .right
            self.titleEdgeInsets = UIEdgeInsets(top: 0, left: 0, bottom: 0, right: 10)
            
        }
        else{
            self.contentHorizontalAlignment = .left
            self.titleEdgeInsets = UIEdgeInsets(top: 0, left: 10, bottom: 0, right: 0)
        }
        self.imageEdgeInsets = UIEdgeInsets(top: 0, left: 0, bottom: 0, right: 0)
    }
    
    func makeButtonRightToLeftIfRequiredWhileCenter() {
        let attribute =  UIView.appearance().semanticContentAttribute
        let layoutDirection = UIView.userInterfaceLayoutDirection(for: attribute)
        if layoutDirection == .rightToLeft {
            self.contentHorizontalAlignment = .center
            self.titleEdgeInsets = UIEdgeInsets(top: 0, left: 0, bottom: 0, right: 10)
            
        }
        else{
            self.contentHorizontalAlignment = .center
            self.titleEdgeInsets = UIEdgeInsets(top: 0, left: 10, bottom: 0, right: 0)
        }
        self.imageEdgeInsets = UIEdgeInsets(top: 0, left: 0, bottom: 0, right: 0)
    }
    
    func makeButtonRightToLeftIfRequiredWhileCenterAndOppsiteDirection() {
        let attribute =  UIView.appearance().semanticContentAttribute
        let layoutDirection = UIView.userInterfaceLayoutDirection(for: attribute)
        if layoutDirection == .rightToLeft {
            self.semanticContentAttribute = .forceLeftToRight
            self.contentHorizontalAlignment = .center
            self.titleEdgeInsets = UIEdgeInsets(top: 0, left: 10, bottom: 0, right: 0)
        }
        else{
            self.semanticContentAttribute = .forceRightToLeft
            self.contentHorizontalAlignment = .center
            self.titleEdgeInsets = UIEdgeInsets(top: 0, left: 0, bottom: 0, right: 10)
        }
        self.imageEdgeInsets = UIEdgeInsets(top: 0, left: 0, bottom: 0, right: 0)
    }
}

class CellIdentifiers: NSObject {
    public static let kCURRENT_ORDERS_CELL = "CurrentOrdersTableViewCell"
    public static let kSCHEDULED_ORDERS_CELL = "ScheduledOrdersTableViewCell"
    public static let kADDRESS_LIST_CELL = "AddressListTableViewCell"
    public static let kHISTORY_ORDERS_CELL = "HistoryOrdersTableViewCell"

    
    
}

class USERDEFAULTS_INFO_KEY {
    public static let IS_LOGIN : String = "IS_LOGIN"
    public static let CURRENT_ADDRESS : String = "CURRENT_DELISHRY_ADDRESS"
    public static let WALLET_BALANCE : String = "Wallet_Balance"
    public static let DEEPLINKING_JSON : String = "Deeplinking_Json"
    public static let WALLET_BALANCE_EN_NUMBER : String = "Wallet_Balance_en_number"

}

struct GuestLocation: Codable {
    let lat: String
    let lng: String
    let addressName: String
}

func saveGuestLocation(_ location: GuestLocation) {
    let encoder = JSONEncoder()
    if let encoded = try? encoder.encode(location) {
        UserDefaults.standard.set(encoded, forKey: "guestLocation")
    }
}

func getGuestLocation() -> GuestLocation? {
    if let data = UserDefaults.standard.data(forKey: "guestLocation") {
        let decoder = JSONDecoder()
        if let location = try? decoder.decode(GuestLocation.self, from: data) {
            return location
        }
    }
    return nil
}

func isGuestLocationSaved() -> Bool {
    return UserDefaults.standard.data(forKey: "guestLocation") != nil
}

func getAuthorizationText() -> String {
    var accessToken : String = ""
    accessToken = User.shared.vAccessToken ?? ""
    return "Bearer \(accessToken)"
}

extension Bundle {
    var releaseVersionNumber: String? {
        return infoDictionary?["CFBundleShortVersionString"] as? String
    }
    var buildVersionNumber: String? {
        return infoDictionary?["CFBundleVersion"] as? String
    }
}

struct FirebaseConstants{
    struct UrlConstant{
        static let schema = "https"
        static let hostURL = "material.page.link"
        static let deepLinkURL = "https://material.page.link"
    }
    
    struct DeepLinkType {
        static let openProduct = "/product"
        static let openOffer = "/offer"
        static let openBanner = "/banner"
        static let openCategory = "/category"
        static let inviteFriends = "/invite"
    }
    
    struct QueryParameter {
        static let id = "id"
        static let category_name = "category_name"
        static let inviteCode = "invite_code"
    }
    
    struct IOSParameters{
        static let bundleID = Bundle.main.bundleIdentifier ?? ""
        static let appStoreID = "1626095299"
    }
}

public enum ConvertSARToPointsType: String, Codable {
    case pay = "pay"
    case getCoins = "get"
}
