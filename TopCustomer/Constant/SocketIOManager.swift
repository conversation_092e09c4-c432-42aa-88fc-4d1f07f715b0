
import UIKit
import SocketIO

class SocketIOManager: NSObject {

    #if DEBUG
        public static let manager = SocketManager(socketURL: URL(string: "http://dev2.spaceo.in:9002/")!, config: [.log(true), .compress, .reconnects(true),.path("/socket.io/")])
    #elseif STAGING
    // "http://dev2.spaceo.in:9003/"
        public static let manager = SocketManager(socketURL: URL(string: "http://15.184.58.176:5212/")!, config: [.log(true), .compress, .reconnects(true),.path("/socket.io/")])
    #elseif DEVRELEASE
        public static let manager = SocketManager(socketURL: URL(string: "http://157.175.27.160:5012/")!, config: [.log(false), .compress, .reconnects(true),.path("/socket.io/")])
    #elseif RELEASESTAGING
        public static let manager = SocketManager(socketURL: URL(string: "http://15.184.58.176:5212/")!, config: [.log(true), .compress, .reconnects(true),.path("/socket.io/")])
    #else
        public static let manager = SocketManager(socketURL: URL(string: "http://157.175.27.160:5012/")!, config: [.log(false), .compress, .reconnects(true),.path("/socket.io/")])

    #endif
    
    let socket = manager.defaultSocket
    
    public static var shared : SocketIOManager = SocketIOManager()
    
    override init() {
        super.init()
        self.handleSocketEvents()
    }
    
    func handleSocketEvents() {
        
        socket.on(clientEvent: .connect) {data, ack in
            print("socket connected")
            self.setUserConnectEvent()
        }
        
        socket.on(clientEvent: .reconnect) {data, ack in
            print("socket reconnect")
        }
        
        socket.on(clientEvent: .reconnectAttempt) {data, ack in
            print("socket reconnectAttempt")
        }
        
        socket.on(clientEvent: .disconnect) {data, ack in
            print("socket disconnect")
        }
        
        socket.on(clientEvent: .error) {data, ack in
            print("socket error ",data)
        }
        
        
    }
    
    func disConnectSocket() {
        socket.disconnect()
    }
    
    func checkAndHandleReconnectSocket() {
        if self.socket.status != .connected {
            self.socket.connect()
        }
    }
    
    func setUserConnectEvent() {
        if User.shared.checkUserLoginStatus() {
            var dictParam : [String:Any] = [:]
            dictParam["iUserId"] = User.shared.iUserId ?? 0
            self.socket.emitWithAck("connect_customer", dictParam).timingOut(after: 10) {data in
                print("\n\n connect_customer \(data) \n\n")
                if data.first as? String == "NO ACK" {
                    self.setUserConnectEvent()
                }
            }
        }
    }
}
