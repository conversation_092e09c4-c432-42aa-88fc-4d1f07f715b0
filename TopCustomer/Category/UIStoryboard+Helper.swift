
import UIKit

protocol StoryboardIdentifiable {
    static var storyboardIdentifier: String { get }
}

extension StoryboardIdentifiable where Self: UIViewController {
    static var storyboardIdentifier: String {
        return String(describing: self)
    }
}

let mainStoryboard = UIStoryboard.storyboard(.Main)
let settingsStoryboard = UIStoryboard.storyboard(.Settings)
let homeStoryboard = UIStoryboard.storyboard(.Home)
let ProductPopupStoryboard = UIStoryboard.storyboard(.ProductPopup)
let MyCartStoryboard = UIStoryboard.storyboard(.MyCart)
let PaymentsStoryboard = UIStoryboard.storyboard(.Payments)

extension UIViewController : StoryboardIdentifiable { }

extension UIStoryboard {
    
    /// The uniform place where we state all the storyboard we have in our application
    
    enum Storyboard: String {
        case Main
        case Settings
        case Home
        case ProductPopup
        case MyCart
        case Payments

        var filename: String {
            return rawValue//.capitalized
        }
    }
    
    
    // MARK: - Convenience Initializers
    
    convenience init(storyboard: Storyboard, bundle: Bundle? = nil) {
        self.init(name: storyboard.filename, bundle: bundle)
    }
    
    
    // MARK: - Class Functions
    
    class func storyboard(_ storyboard: Storyboard, bundle: Bundle? = nil) -> UIStoryboard {
        return UIStoryboard(name: storyboard.filename, bundle: bundle)
    }
    
    
    // MARK: - View Controller Instantiation from Generics
    
    func instantiateViewController<T>() -> T where T: StoryboardIdentifiable {
        guard let viewController = self.instantiateViewController(withIdentifier: T.storyboardIdentifier) as? T else {
            fatalError("Couldn't instantiate view controller with identifier \(T.storyboardIdentifier) ")
        }
        
        return viewController
    }
    
    public func instantiate<A: UIViewController>(_ type: A.Type, withIdentifier identifier: String? = nil) -> A {
        let id = identifier ?? String(describing: type.self)
        guard let vc = self.instantiateViewController(withIdentifier: id) as? A else {
            fatalError("Could not instantiate view controller \(A.self)") }
        return vc
    }

}
