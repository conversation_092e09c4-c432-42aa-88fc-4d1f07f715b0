
import Foundation
import UIKit

public protocol ClassNameProtocol {
    static var className: String { get }
    var className: String { get }
}

public extension ClassNameProtocol {
    static var className: String {
        return String(describing: self)
    }

    var className: String {
        return type(of: self).className
    }
}

extension NSObject: ClassNameProtocol {}

extension UITableView {
    public func registerCell<T: UITableViewCell>(cell: T.Type, bundle: Bundle? = nil) {
        let className = cell.className
        let nib = UINib(nibName: className, bundle: bundle)
        register(nib, forCellReuseIdentifier: className)
    }
    
    public func registerCell<T: UITableViewCell>(cell: [T.Type], bundle: Bundle? = nil) {
        cell.forEach { registerCell(cell: $0, bundle: bundle) }
    }
    
    public func dequeue<T: UITableViewCell>(with type: T.Type, for indexPath: IndexPath) -> T {
        return self.dequeueReusableCell(withIdentifier: type.className, for: indexPath) as! T
    }
    
    /*
    func setEmptyView() {
        ez.runThisAfterDelay(seconds: SOValidation.skelatonDelay) {
            let emptyView: EmptyView = EmptyView.fromNib()
            
            //        let objEmptyData = EmptyData.init(imgEmpty: messageImage, strTitle: title, strMessage: message)
            //        emptyView.objEmptyData = objEmptyData
            //        emptyView.frame = self.bounds
            self.backgroundView = emptyView
        }
    }
    
    func restore() {
        ez.runThisAfterDelay(seconds: SOValidation.skelatonDelay) {
            self.backgroundView = nil
        }
    }*/
    
    func hasRowAtIndexPath(indexPath: IndexPath) -> Bool {
        return indexPath.section < self.numberOfSections && indexPath.row < self.numberOfRows(inSection: indexPath.section)
    }
    
    func scrollToTop(animated: Bool = true) {
        let indexPath = IndexPath(row: 0, section: 0)
        if self.hasRowAtIndexPath(indexPath: indexPath) {
            self.scrollToRow(at: indexPath, at: .top, animated: animated)
        }
    }
}
