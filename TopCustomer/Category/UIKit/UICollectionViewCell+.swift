//
//  UICollectionViewCell+.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 20/12/2023.
//  Copyright © 2023 SOTSYS203. All rights reserved.
//

import UIKit

extension UICollectionViewCell {
    // MARK: - Func
    func setDefaultShadow() {
        // border radius
        self.layer.cornerRadius = 7.0

        // border
        self.layer.borderColor = UIColor.clear.cgColor
        self.layer.borderWidth = 0.0

        //drop shadow
        self.layer.shadowColor = UIColor(red: 0.0, green: 0.0, blue: 0.0, alpha: 0.15).cgColor
        self.layer.shadowOpacity = 0.8
        self.layer.shadowRadius = 3.0
        self.layer.shadowOffset = CGSize(width: 1.0, height: 1.0)
        self.clipsToBounds = true
        self.layer.masksToBounds = false
    }
    
}
