
import Foundation
import UIKit

extension UICollectionView {
    public func registerCell<T: UICollectionViewCell>(cell: T.Type, bundle: Bundle? = nil) {
        let className = cell.className
        let nib = UINib(nibName: className, bundle: bundle)
        register(nib, forCellWithReuseIdentifier: className)
    }
    
    public func registerCell<T: UICollectionViewCell>(cell: [T.Type], bundle: Bundle? = nil) {
        cell.forEach { registerCell(cell: $0, bundle: bundle) }
    }
    
    public func dequeue<T: UICollectionViewCell>(with type: T.Type, for indexPath: IndexPath) -> T {
        return self.dequeueReusableCell(withReuseIdentifier: type.className, for: indexPath) as! T
    }
    
    func setEmptyView() {
//        let emptyView: EmptyView = EmptyView.fromNib()
        
//        let objEmptyData = EmptyData.init(imgEmpty: messageImage, strTitle: title, strMessage: message)
//        emptyView.objEmptyData = objEmptyData
//        emptyView.frame = self.bounds
//        self.backgroundView = emptyView
        
    }
    
    func restore() {
        self.backgroundView = nil
    }
}
