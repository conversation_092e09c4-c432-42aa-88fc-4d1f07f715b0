//
//  UICollectionView+.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 28/08/2023.
//  Copyright © 2023 TopCustomer. All rights reserved.
//

import UIKit

extension UICollectionView {
    // MARK: - Func
    func scrollToBottom() {
        DispatchQueue.main.async {
            let indexPath = IndexPath(
                row: self.numberOfSections - 1,
                section: 0)
            if indexPath.row <= 0 {
                return
            }
            self.scrollToItem(at: indexPath, at: .bottom, animated: true)
        }
    }
    
    func scrollToTop(completionHandler: @escaping () ->() ) {
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            self.setContentOffset(.zero, animated: true)
            completionHandler()
        }
    }
    
    func setEmptyMessage(_ message: String) {
        let messageLabel = UILabel(frame: CGRect(x: 0, y: 0, width: self.bounds.size.width, height: self.bounds.size.height))
        messageLabel.text = message
        messageLabel.textColor = .black
        messageLabel.numberOfLines = 0;
        messageLabel.textAlignment = .center;
        messageLabel.font = .LoewNextArabic.bold(size: 14).font
        messageLabel.sizeToFit()
        self.backgroundView = messageLabel;
    }
    
}
