
import Foundation
import UIKit

extension UIColor {
    
    public static let AppTheme_BlueColor_012CDA : UIColor = UIColor(named: "AppTheme_BlueColor_#012CDA")!
    public static let AppTheme_BorderColor_414040 : UIColor = UIColor(named: "AppTheme_BorderColor_#414040")!
    public static let AppTheme_LightGrayColor_A0A0A0 : UIColor = UIColor(named: "AppTheme_LightGrayColor_#A0A0A0")!
    public static let AppTheme_VeryLightGrayColor_FEFEFE : UIColor = UIColor(named: "AppTheme_VeryLightGrayColor_#FEFEFE")!
    public static let AppTheme_SelectedTabColor_F4BA45 : UIColor = UIColor(named: "AppTheme_SelectedTabColor_#F4BA45")!
    public static let AppTheme_BorderColor_1F1F1F : UIColor = UIColor(named: "AppTheme_BorderColor_#1F1F1F")!
    public static let AppTheme_GrayTextColor_767676 : UIColor = UIColor(named: "AppTheme_GrayTextColor_#767676")!
    public static let AppTheme_GrayTextColor_919191 : UIColor = UIColor(named: "AppTheme_GrayTextColor_#919191")!
    public static let AppTheme_ShadowColor_0000003E : UIColor = UIColor(named: "AppTheme_ShadowColor_#0000003E")!
    public static let AppTheme_LightGrayOrderColor_AAAAAA : UIColor = UIColor(named: "AppTheme_LightGrayOrderColor_#AAAAAA")!
    public static let AppTheme_LightGrayProductBGColor_DFDFDF : UIColor = UIColor(named: "AppTheme_LightGrayProductBGColor_#DFDFDF")!
    public static let AppTheme_LightGrayColor_B4B2B2 : UIColor = UIColor(named: "AppTheme_LightGrayColor_#B4B2B2")!
    public static let AppTheme_DarkGrayColor_A09F9F : UIColor = UIColor(named: "AppTheme_DarkGrayColor_#A09F9F")!
    public static let AppTheme_VeryLightGrayColor_BDBCBC : UIColor = UIColor(named: "AppTheme_VeryLightGrayColor_#BDBCBC")!
    public static let AppTheme_VeryLightTextFieldBorderColor_A4A4A4 : UIColor = UIColor(named: "AppTheme_VeryLightTextFieldBorderColor_#A4A4A4")!
    public static let AppTheme_DarkGrayAddressColor_454545 : UIColor = UIColor(named: "AppTheme_DarkGrayAddressColor_#454545")!
    public static let AppTheme_LightGrayDescColor_A8A8A8 : UIColor = UIColor(named: "AppTheme_LightGrayDescColor_#A8A8A8")!
    public static let AppTheme_SubLabelLightGrayColor_B2B2B2 : UIColor = UIColor(named: "AppTheme_SubLabelLightGrayColor_#B2B2B2")!
    public static let AppTheme_VeryLightSeparatorColor_CBCBCB : UIColor = UIColor(named: "AppTheme_VeryLightSeparatorColor_#CBCBCB")!
    public static let AppTheme_DropShadowColor_00000046 : UIColor = UIColor(named: "AppTheme_DropShadowColor_#00000046")!
    public static let AppTheme_FieldBGColor_F7F7F7 : UIColor = UIColor(named: "AppTheme_FieldBGColor_#F7F7F7")!
    public static let AppTheme_RedColor_D82828 : UIColor = UIColor(named: "AppTheme_RedColor_#D82828")!
    public static let AppTheme_StrikeThroughColor_707070 : UIColor = UIColor(named: "AppTheme_StrikeThroughColor_#707070")!
    public static let AppTheme_DarkGrayColor_5D5D5D : UIColor = UIColor(named: "AppTheme_DarkGrayColor_#5D5D5D")!
    public static let AppTheme_DiffBlackColor_101113 : UIColor = UIColor(named: "AppTheme_DiffBlackColor_#101113")!
    public static let AppTheme_DiscountGreenColor_05B13E : UIColor = UIColor(named: "AppTheme_DiscountGreenColor_#05B13E")!
    public static let AppTheme_LightGrayColor_CECECE : UIColor = UIColor(named: "AppTheme_LightGrayColor_#CECECE")!
    public static let AppTheme_VeryLightStatusLineColor_EFEFEF : UIColor = UIColor(named: "AppTheme_VeryLightStatusLineColor_#EFEFEF")!
    public static let AppTheme_VeryLightGrayColor_FAFAFA : UIColor = UIColor(named: "AppTheme_VeryLightGrayColor_#FAFAFA")!


    
    
    
    
    
    
    convenience init?(hex: String) {
        var hexSanitized = hex.trimmingCharacters(in: .whitespacesAndNewlines)
        hexSanitized = hexSanitized.replacingOccurrences(of: "#", with: "")
        
        var rgb: UInt32 = 0
        
        var r: CGFloat = 0.0
        var g: CGFloat = 0.0
        var b: CGFloat = 0.0
        var a: CGFloat = 1.0
        
        let length = hexSanitized.count
        
        guard Scanner(string: hexSanitized).scanHexInt32(&rgb) else { return nil }
        
        if length == 6 {
            r = CGFloat((rgb & 0xFF0000) >> 16) / 255.0
            g = CGFloat((rgb & 0x00FF00) >> 8) / 255.0
            b = CGFloat(rgb & 0x0000FF) / 255.0
            
        } else if length == 8 {
            r = CGFloat((rgb & 0xFF000000) >> 24) / 255.0
            g = CGFloat((rgb & 0x00FF0000) >> 16) / 255.0
            b = CGFloat((rgb & 0x0000FF00) >> 8) / 255.0
            a = CGFloat(rgb & 0x000000FF) / 255.0
            
        } else {
            return nil
        }
        
        self.init(red: r, green: g, blue: b, alpha: a)
    }
    
    // MARK: - Computed Properties
    
    var toHex: String? {
        return toHex()
    }
    
    // MARK: - From UIColor to String
    
    func toHex(alpha: Bool = false) -> String? {
        guard let components = cgColor.components, components.count >= 3 else {
            return nil
        }
        
        let r = Float(components[0])
        let g = Float(components[1])
        let b = Float(components[2])
        var a = Float(1.0)
        
        if components.count >= 4 {
            a = Float(components[3])
        }
        
        if alpha {
            return String(format: "%02lX%02lX%02lX%02lX", lroundf(r * 255), lroundf(g * 255), lroundf(b * 255), lroundf(a * 255))
        } else {
            return String(format: "%02lX%02lX%02lX", lroundf(r * 255), lroundf(g * 255), lroundf(b * 255))
        }
    }
    
    func getColor(red: Int?, green: Int?, blue: Int?, alpha: String?) -> UIColor? {
        if (red ?? 0) >= 0 || (green ?? 0) >= 0 || (blue ?? 0) >= 0 {
            return UIColor(red: CGFloat(red ?? 0)/255, green: CGFloat(green ?? 0)/255, blue: CGFloat(blue ?? 0)/255, alpha: CGFloat(alpha?.double() ?? 1.0))
        }else{
            return nil
        }
    }
    
}
