//
//  UITableViewCell+.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 05/06/2024.
//  Copyright © 2024 SOTSYS203. All rights reserved.
//

import UIKit

extension UITableViewCell {
    // MARK: - Func
    func setDefaultShadow() {
        self.layer.cornerRadius = 8
        self.layer.shadowOffset = CGSize(width: 0, height: 3)
        self.layer.shadowRadius = 3
        self.layer.shadowOpacity = 0.3
        self.layer.shadowPath = UIBezierPath(roundedRect: self.bounds, byRoundingCorners: .allCorners, cornerRadii: CGSize(width: 8, height: 8)).cgPath
        self.layer.shouldRasterize = true
        self.layer.rasterizationScale = UIScreen.main.scale
        
    }
    
    func addShadowNew() {
        //viewContainer is the parent of viewContents
        //viewContents contains all the UI which you want to show actually.
        self.layer.cornerRadius = 12.69
        self.layer.masksToBounds = true
        self.layer.masksToBounds = false
        self.layer.shadowColor = UIColor.black.cgColor
        self.layer.shadowRadius = 3.0
        self.layer.shadowOffset = CGSize.init(width: 0, height: 2)
        self.layer.shadowOpacity = 0.2
        // sending viewContainer color to the viewContents.
        let backgroundCGColor = self.backgroundColor?.cgColor
        //You can set your color directly if you want by using below two lines. In my case I'm copying the color.
        self.backgroundColor = nil
        self.layer.backgroundColor =  backgroundCGColor
    }
    
}
