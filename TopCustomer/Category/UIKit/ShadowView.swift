//
//  ShadowView.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 16/08/2023.
//  Copyright © 2023 SOTSYS203. All rights reserved.
//

import UIKit

@IBDesignable class ShadowView: UIView {

    override func awakeFromNib() {
        super.awakeFromNib()
        setupView()
    }
    
    override func prepareForInterfaceBuilder() {
        setupView()
    }
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupView()
    }
    
    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
        setupView()
    }
    
   
    
    @IBInspectable var shadowColorView: UIColor?  {
        get {
            if let color = layer.shadowColor {
                return UIColor(cgColor: color)
            }
            return nil
        }
        set {
            if let color = newValue {
                layer.shadowColor = color.cgColor
            } else {
                layer.shadowColor = nil
            }
        }
    }

    @IBInspectable var shadowOpacityView: Float {
        get {
            return layer.shadowOpacity
        }
        set {
            layer.shadowOpacity = newValue
        }
    }

    @IBInspectable var shadowOffsetView: CGPoint {
        get {
            return CGPoint(x: layer.shadowOffset.width, y:layer.shadowOffset.height)
        }
        set {
            layer.shadowOffset = CGSize(width: newValue.x, height: newValue.y)
        }

     }

    @IBInspectable var shadowBlurView: CGFloat {
        get {
            return layer.shadowRadius
        }
        set {
            layer.shadowRadius = newValue / 2.0
        }
    }
    
    @IBInspectable var cornerRadiusView: CGFloat {
        get {
            return layer.cornerRadius
        }
        set {
            layer.cornerRadius = newValue
        }
    }
    
    @IBInspectable var isCornerRadiusTopOnly: Bool {
        get {
            return self.isTopOnly
        }
        set {
            self.isTopOnly = newValue
            setupView()
        }
    }
    
    var isTopOnly: Bool = false

    @IBInspectable var shadowSpreadView: CGFloat = 0 {
        didSet {
            if shadowSpreadView == 0 {
                layer.shadowPath = nil
            } else {
                let dx = -shadowSpreadView
                let rect = bounds.insetBy(dx: dx, dy: dx)
                layer.shadowPath = UIBezierPath(rect: rect).cgPath
            }
        }
    }
    
    func setupView() {
        self.layer.shadowColor = shadowColorView?.cgColor
        self.layer.shadowRadius = shadowBlurView
        self.layer.shadowOffset = CGSize(width: shadowOffsetView.x, height: shadowOffsetView.y)
        self.layer.shadowOpacity = shadowOpacityView
        self.layer.shadowColor = shadowColorView?.cgColor
        self.clipsToBounds = true
        self.layer.cornerRadius = cornerRadiusView
        self.layer.maskedCorners = [.layerMaxXMinYCorner, .layerMinXMinYCorner] // Top right corner, Top left corner respectively
        self.layer.applySketchShadow()
    }
}

extension CALayer {
  func applySketchShadow(
    color: UIColor = .black,
    alpha: Float = 1,
    x: CGFloat = 0,
    y: CGFloat = 3,
    blur: CGFloat = 6,
    spread: CGFloat = 0)
  {
    masksToBounds = false
    shadowColor = color.cgColor
    shadowOpacity = alpha
    shadowOffset = CGSize(width: x, height: y)
    shadowRadius = blur / 2.0
    if spread == 0 {
      shadowPath = nil
    } else {
      let dx = -spread
      let rect = bounds.insetBy(dx: dx, dy: dx)
      shadowPath = UIBezierPath(rect: rect).cgPath
    }
  }
}

