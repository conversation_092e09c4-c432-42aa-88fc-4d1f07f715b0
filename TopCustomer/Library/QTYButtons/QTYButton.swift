
import UIKit

protocol QTYButtonDelegate: AnyObject {
    func didChangeValue(value: Int)
}

public class QTYButton: UIView {
    class var bundle:Bundle{
        get{
            let podBundle = Bundle(for: QTYButton.self)
            if let bundleURL = podBundle.url(forResource: "QTYButtons", withExtension: "bundle") {
                return Bundle(url: bundleURL)!
            }else{
                return podBundle
            }
        }
    }
    
    @IBOutlet weak var contentView : UIView!
    @IBOutlet weak public var buttonAdd: UIButton!
    @IBOutlet weak public var buttonPlus: UIButton!
//    @IBOutlet weak public var label: UILabel!
    @IBOutlet weak public var buttonMin: UIButton!
    @IBOutlet weak public var textfield: UITextField!

    private var onChange : (Int, String) -> Void = { _,_  in }
    
    public var text: Int = 1 {
        didSet {
            if textfield != nil {
//                self.setupUI(strAddOrRemove: "")
            }
            self.delegate?.didChangeValue(value: text)
        }
    }
    weak var delegate: QTYButtonDelegate?
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        commonInit()
    }
    
    required public init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
        commonInit()
    }
    
    private func commonInit() {
        QTYButton.bundle.loadNibNamed("QTYButton", owner: self, options: [:])
        addSubview(contentView)
        contentView.translatesAutoresizingMaskIntoConstraints = false
        contentView.topAnchor.constraint(equalTo: topAnchor).isActive = true
        contentView.bottomAnchor.constraint(equalTo: bottomAnchor).isActive = true
        contentView.leftAnchor.constraint(equalTo: leftAnchor).isActive = true
        contentView.rightAnchor.constraint(equalTo: rightAnchor).isActive = true
    }
    
    func setupUI(strAddOrRemove: String) {
//        if text == 0 {
//            self.editable(value: false)
//        }else {
            self.editable(value: true)
//        }
//        self.textfield.text = String(describing: text)
        if strAddOrRemove != "" {
            self.onChange(Int(text), strAddOrRemove)
        }
    }
    
    func editable(value: Bool) {
        if value == true {
            self.buttonAdd.isHidden = true
            self.buttonMin.isHidden = false
        }else {
            self.buttonAdd.isHidden = true
            self.buttonMin.isHidden = true
        }
    }
    
    @IBAction func clickBuy(_ sender: Any) {
        self.endEditing(true)
        if self.text < 999999 {
            self.text = self.text + 1
            self.textfield.text = String(describing: text)
        }
    }
    
    @IBAction func clickAdd(_ sender: Any) {
        self.endEditing(true)
        if self.text < 999999 {
            self.text = self.text + 1
            self.textfield.text = String(describing: text)
            self.setupUI(strAddOrRemove: "Add")
        }
    }
    
    @IBAction func clickMin(_ sender: Any) {
        self.endEditing(true)
        if self.text > 1 {
            self.text = self.text - 1
            self.textfield.text = String(describing: text)
            self.setupUI(strAddOrRemove: "Remove")
        }
        
    }
}

extension QTYButton {
    public func onChange(value: @escaping (Int, String) -> Void) {
        self.onChange = { qty, strAction in
            value(qty, strAction)
        }
    }
}

// MARK: - UITextFieldDelegate
extension QTYButton : UITextFieldDelegate {
    
    public func textField(_ textField: UITextField, shouldChangeCharactersIn range: NSRange, replacementString string: String) -> Bool {
        
        let newLength: Int = (textField.text?.length)! + string.length - range.length
        if newLength > MaxQuantityLength {
            return false
        }
        return true
    }
    
    public func textFieldDidEndEditing(_ textField: UITextField) {
        let value = Int(textField.text ?? "") ?? 0
//        guard let text = textField.text, let value = Int(text) else {return}
        self.text = value
        self.delegate?.didChangeValue(value: Int(textField.text ?? "") ?? 0)
    }

}
