<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="22505" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22504"/>
        <capability name="Named colors" minToolsVersion="9.0"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="LoewNextArabic-Bold.ttf">
            <string>LoewNextArabic-Bold</string>
        </array>
        <array key="LoewNextArabic-Medium.ttf">
            <string>LoewNextArabic-Medium</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="QTYButton" customModule="TopCustomer" customModuleProvider="target">
            <connections>
                <outlet property="buttonAdd" destination="mh9-cm-uAg" id="XuL-v7-rwh"/>
                <outlet property="buttonMin" destination="6DB-B4-bj9" id="pcX-Ec-uYT"/>
                <outlet property="buttonPlus" destination="S5T-LZ-F4e" id="4Es-Zd-I6B"/>
                <outlet property="contentView" destination="6Pk-IW-zvw" id="Yoe-Ki-3gP"/>
                <outlet property="textfield" destination="3HA-jh-WvF" id="meJ-U4-eyX"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" id="6Pk-IW-zvw">
            <rect key="frame" x="0.0" y="0.0" width="504" height="203"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="S5T-LZ-F4e">
                    <rect key="frame" x="0.0" y="20" width="183" height="183"/>
                    <constraints>
                        <constraint firstAttribute="width" secondItem="S5T-LZ-F4e" secondAttribute="height" id="haZ-j4-eGh"/>
                    </constraints>
                    <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="14"/>
                    <state key="normal" title="+">
                        <color key="titleColor" name="AppTheme_LightGrayColor_#A0A0A0"/>
                    </state>
                    <connections>
                        <action selector="clickAdd:" destination="-1" eventType="touchUpInside" id="1RC-mi-wKR"/>
                    </connections>
                </button>
                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="6DB-B4-bj9" userLabel="Button Plus">
                    <rect key="frame" x="321" y="20" width="183" height="183"/>
                    <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="14"/>
                    <state key="normal" title="-">
                        <color key="titleColor" name="AppTheme_LightGrayColor_#A0A0A0"/>
                    </state>
                    <connections>
                        <action selector="clickMin:" destination="-1" eventType="touchUpInside" id="kgN-RK-R5m"/>
                    </connections>
                </button>
                <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" text="1" borderStyle="roundedRect" textAlignment="center" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="3HA-jh-WvF">
                    <rect key="frame" x="183" y="20" width="138" height="183"/>
                    <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="12"/>
                    <textInputTraits key="textInputTraits" keyboardType="ASCIICapableNumberPad"/>
                    <connections>
                        <outlet property="delegate" destination="-1" id="0T8-ZS-qSd"/>
                    </connections>
                </textField>
                <button hidden="YES" opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="mh9-cm-uAg" userLabel="button Buy">
                    <rect key="frame" x="168" y="20" width="336" height="183"/>
                    <fontDescription key="fontDescription" type="boldSystem" pointSize="15"/>
                    <state key="normal" title="Add">
                        <color key="titleColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    </state>
                    <connections>
                        <action selector="clickBuy:" destination="-1" eventType="touchUpInside" id="YfU-aQ-oQt"/>
                    </connections>
                </button>
            </subviews>
            <viewLayoutGuide key="safeArea" id="ywA-2g-WQw"/>
            <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
            <constraints>
                <constraint firstItem="mh9-cm-uAg" firstAttribute="top" secondItem="ywA-2g-WQw" secondAttribute="top" id="3o4-ts-hmR"/>
                <constraint firstItem="mh9-cm-uAg" firstAttribute="bottom" secondItem="ywA-2g-WQw" secondAttribute="bottom" id="Fpy-jY-7Hn"/>
                <constraint firstItem="mh9-cm-uAg" firstAttribute="width" secondItem="6Pk-IW-zvw" secondAttribute="width" multiplier="1:1.5" id="Nmx-MF-ibT"/>
                <constraint firstItem="3HA-jh-WvF" firstAttribute="top" secondItem="ywA-2g-WQw" secondAttribute="top" id="Ryz-yy-5po"/>
                <constraint firstItem="6DB-B4-bj9" firstAttribute="leading" secondItem="3HA-jh-WvF" secondAttribute="trailing" id="UYy-A7-u0G"/>
                <constraint firstItem="mh9-cm-uAg" firstAttribute="trailing" secondItem="ywA-2g-WQw" secondAttribute="trailing" id="Udg-DF-sTX"/>
                <constraint firstItem="3HA-jh-WvF" firstAttribute="leading" secondItem="S5T-LZ-F4e" secondAttribute="trailing" id="WSx-86-f0N"/>
                <constraint firstItem="ywA-2g-WQw" firstAttribute="trailing" secondItem="6DB-B4-bj9" secondAttribute="trailing" id="j3D-wi-L8z"/>
                <constraint firstItem="6DB-B4-bj9" firstAttribute="top" secondItem="ywA-2g-WQw" secondAttribute="top" id="jso-0Y-UPv"/>
                <constraint firstItem="6DB-B4-bj9" firstAttribute="bottom" secondItem="ywA-2g-WQw" secondAttribute="bottom" id="lgh-0m-eSc"/>
                <constraint firstItem="S5T-LZ-F4e" firstAttribute="top" secondItem="ywA-2g-WQw" secondAttribute="top" id="oOB-aP-qUs"/>
                <constraint firstItem="6DB-B4-bj9" firstAttribute="width" secondItem="S5T-LZ-F4e" secondAttribute="width" id="qxL-cy-ugM"/>
                <constraint firstItem="3HA-jh-WvF" firstAttribute="bottom" secondItem="ywA-2g-WQw" secondAttribute="bottom" id="sXc-sH-t9S"/>
                <constraint firstItem="S5T-LZ-F4e" firstAttribute="leading" secondItem="ywA-2g-WQw" secondAttribute="leading" id="sgv-8v-hsN"/>
                <constraint firstItem="S5T-LZ-F4e" firstAttribute="bottom" secondItem="ywA-2g-WQw" secondAttribute="bottom" id="ykb-s1-lyS"/>
            </constraints>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <userDefinedRuntimeAttributes>
                <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                    <integer key="value" value="1"/>
                </userDefinedRuntimeAttribute>
                <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                    <integer key="value" value="6"/>
                </userDefinedRuntimeAttribute>
                <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                    <color key="value" name="AppTheme_LightGrayColor_#CECECE"/>
                </userDefinedRuntimeAttribute>
            </userDefinedRuntimeAttributes>
            <point key="canvasLocation" x="121" y="-37.5"/>
        </view>
    </objects>
    <resources>
        <namedColor name="AppTheme_LightGrayColor_#A0A0A0">
            <color red="0.62745098039215685" green="0.62745098039215685" blue="0.62745098039215685" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="AppTheme_LightGrayColor_#CECECE">
            <color red="0.80784313725490198" green="0.80784313725490198" blue="0.80784313725490198" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
    </resources>
</document>
