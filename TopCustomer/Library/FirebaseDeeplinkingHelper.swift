
import Foundation
import FirebaseDynamicLinks

class FirebaseDeeplinkingHelper{
    
    
    static let shared = FirebaseDeeplinkingHelper()
    
    private init(){
        
    }
    
    
    func generateFeedPlanLink(strProductId: String, strTitle: String, strDescription: String, strImageUrl: String, completion : @escaping ((_ url: String?) -> Void)){
//        AppSingletonObj.showLoader(isTransparent: true)
        
        var components = URLComponents()
        components.scheme = "\(FirebaseConstants.UrlConstant.schema)"
        components.host = "\(FirebaseConstants.UrlConstant.hostURL)"
        components.path = "\(FirebaseConstants.DeepLinkType.openProduct)"
        components.queryItems = [
            URLQueryItem(name: FirebaseConstants.QueryParameter.id, value: strProductId)
        ]
        guard let link = components.url else { return }

        let dynamicLinksDomainURIPrefix = FirebaseConstants.UrlConstant.deepLinkURL
        let linkBuilder : DynamicLinkComponents = DynamicLinkComponents(link: link, domainURIPrefix: dynamicLinksDomainURIPrefix)!
        
        
        linkBuilder.iOSParameters = DynamicLinkIOSParameters(bundleID: FirebaseConstants.IOSParameters.bundleID)
        linkBuilder.iOSParameters?.appStoreID = FirebaseConstants.IOSParameters.appStoreID

        linkBuilder.androidParameters = DynamicLinkAndroidParameters(packageName: FirebaseConstants.IOSParameters.bundleID)
//        linkBuilder.androidParameters?.minimumVersion = 35

        let options = DynamicLinkComponentsOptions()
        options.pathLength = .short
        linkBuilder.options = options

        linkBuilder.socialMetaTagParameters = DynamicLinkSocialMetaTagParameters()
        linkBuilder.socialMetaTagParameters?.title = strTitle
        linkBuilder.socialMetaTagParameters?.descriptionText = strDescription
        linkBuilder.socialMetaTagParameters?.imageURL = URL(string: strImageUrl)

        guard let longDynamicLink = linkBuilder.url else { return }
        print("The long URL is: \(longDynamicLink)")
        
        var linkUrl = longDynamicLink
        linkBuilder.shorten() { url, warnings, error in
            if error == nil, let url = url{
                print("The short URL is: \(url)")
                linkUrl = url
                completion("\(linkUrl)")
            }else{
                completion(nil)
            }
        }
    }
    
    func generateFeedPlanLinkForOffer(strOfferId: String, strTitle: String, strDescription: String, strImageUrl: String, completion : @escaping ((_ url: String?) -> Void)){
//        AppSingletonObj.showLoader(isTransparent: true)
        
        var components = URLComponents()
        components.scheme = "\(FirebaseConstants.UrlConstant.schema)"
        components.host = "\(FirebaseConstants.UrlConstant.hostURL)"
        components.path = "\(FirebaseConstants.DeepLinkType.openOffer)"
        components.queryItems = [
            URLQueryItem(name: FirebaseConstants.QueryParameter.id, value: strOfferId)
        ]
        guard let link = components.url else { return }

        let dynamicLinksDomainURIPrefix = FirebaseConstants.UrlConstant.deepLinkURL
        let linkBuilder : DynamicLinkComponents = DynamicLinkComponents(link: link, domainURIPrefix: dynamicLinksDomainURIPrefix)!
        
        
        linkBuilder.iOSParameters = DynamicLinkIOSParameters(bundleID: FirebaseConstants.IOSParameters.bundleID)
        linkBuilder.iOSParameters?.appStoreID = FirebaseConstants.IOSParameters.appStoreID

        linkBuilder.androidParameters = DynamicLinkAndroidParameters(packageName: FirebaseConstants.IOSParameters.bundleID)
//        linkBuilder.androidParameters?.minimumVersion = 35

        let options = DynamicLinkComponentsOptions()
        options.pathLength = .short
        linkBuilder.options = options

        linkBuilder.socialMetaTagParameters = DynamicLinkSocialMetaTagParameters()
        linkBuilder.socialMetaTagParameters?.title = strTitle
        linkBuilder.socialMetaTagParameters?.descriptionText = strDescription
        linkBuilder.socialMetaTagParameters?.imageURL = URL(string: strImageUrl)

        guard let longDynamicLink = linkBuilder.url else { return }
        print("The long URL is: \(longDynamicLink)")
        
        var linkUrl = longDynamicLink
        linkBuilder.shorten() { url, warnings, error in
            if error == nil, let url = url{
                print("The short URL is: \(url)")
                linkUrl = url
                completion("\(linkUrl)")
            }else{
                completion(nil)
            }
        }
    }

    func generateFeedPlanLinkForBanner(strBannerId: String, strTitle: String, strDescription: String, strImageUrl: String, completion : @escaping ((_ url: String?) -> Void)){
//        AppSingletonObj.showLoader(isTransparent: true)
        
        var components = URLComponents()
        components.scheme = "\(FirebaseConstants.UrlConstant.schema)"
        components.host = "\(FirebaseConstants.UrlConstant.hostURL)"
        components.path = "\(FirebaseConstants.DeepLinkType.openBanner)"
        components.queryItems = [
            URLQueryItem(name: FirebaseConstants.QueryParameter.id, value: strBannerId)
        ]
        guard let link = components.url else { return }

        let dynamicLinksDomainURIPrefix = FirebaseConstants.UrlConstant.deepLinkURL
        let linkBuilder : DynamicLinkComponents = DynamicLinkComponents(link: link, domainURIPrefix: dynamicLinksDomainURIPrefix)!
        
        
        linkBuilder.iOSParameters = DynamicLinkIOSParameters(bundleID: FirebaseConstants.IOSParameters.bundleID)
        linkBuilder.iOSParameters?.appStoreID = FirebaseConstants.IOSParameters.appStoreID

        linkBuilder.androidParameters = DynamicLinkAndroidParameters(packageName: FirebaseConstants.IOSParameters.bundleID)
//        linkBuilder.androidParameters?.minimumVersion = 35

        let options = DynamicLinkComponentsOptions()
        options.pathLength = .short
        linkBuilder.options = options

        linkBuilder.socialMetaTagParameters = DynamicLinkSocialMetaTagParameters()
        linkBuilder.socialMetaTagParameters?.title = strTitle
        linkBuilder.socialMetaTagParameters?.descriptionText = strDescription
        linkBuilder.socialMetaTagParameters?.imageURL = URL(string: strImageUrl)

        guard let longDynamicLink = linkBuilder.url else { return }
        print("The long URL is: \(longDynamicLink)")
        
        var linkUrl = longDynamicLink
        linkBuilder.shorten() { url, warnings, error in
            if error == nil, let url = url{
                print("The short URL is: \(url)")
                linkUrl = url
                completion("\(linkUrl)")
            }else{
                completion(nil)
            }
        }
    }

    func generateFeedPlanLinkForCategory(strCategoryId: String, strTitle: String, strCategoryName: String, strDescription: String, strImageUrl: String, completion : @escaping ((_ url: String?) -> Void)){
//        AppSingletonObj.showLoader(isTransparent: true)
        
        var components = URLComponents()
        components.scheme = "\(FirebaseConstants.UrlConstant.schema)"
        components.host = "\(FirebaseConstants.UrlConstant.hostURL)"
        components.path = "\(FirebaseConstants.DeepLinkType.openCategory)"
        components.queryItems = [
            URLQueryItem(name: FirebaseConstants.QueryParameter.id, value: strCategoryId),
            URLQueryItem(name: FirebaseConstants.QueryParameter.category_name, value: strCategoryName)
        ]
        guard let link = components.url else { return }

        let dynamicLinksDomainURIPrefix = FirebaseConstants.UrlConstant.deepLinkURL
        let linkBuilder : DynamicLinkComponents = DynamicLinkComponents(link: link, domainURIPrefix: dynamicLinksDomainURIPrefix)!
        
        
        linkBuilder.iOSParameters = DynamicLinkIOSParameters(bundleID: FirebaseConstants.IOSParameters.bundleID)
        linkBuilder.iOSParameters?.appStoreID = FirebaseConstants.IOSParameters.appStoreID

        linkBuilder.androidParameters = DynamicLinkAndroidParameters(packageName: FirebaseConstants.IOSParameters.bundleID)
//        linkBuilder.androidParameters?.minimumVersion = 35

        let options = DynamicLinkComponentsOptions()
        options.pathLength = .short
        linkBuilder.options = options

        linkBuilder.socialMetaTagParameters = DynamicLinkSocialMetaTagParameters()
        linkBuilder.socialMetaTagParameters?.title = strTitle
        linkBuilder.socialMetaTagParameters?.descriptionText = strDescription
        linkBuilder.socialMetaTagParameters?.imageURL = URL(string: strImageUrl)

        guard let longDynamicLink = linkBuilder.url else { return }
        print("The long URL is: \(longDynamicLink)")
        
        var linkUrl = longDynamicLink
        linkBuilder.shorten() { url, warnings, error in
            if error == nil, let url = url{
                print("The short URL is: \(url)")
                linkUrl = url
                completion("\(linkUrl)")
            }else{
                completion(nil)
            }
        }
    }
    
    func generateInviteFriendsLink(inviteCode: String, completion : @escaping ((_ url: String?, _ code: String?) -> Void)) {
        var components = URLComponents()
        components.scheme = "\(FirebaseConstants.UrlConstant.schema)"
        components.host = "\(FirebaseConstants.UrlConstant.hostURL)"
        components.path = "\(FirebaseConstants.DeepLinkType.inviteFriends)"
        components.queryItems = [
            URLQueryItem(name: FirebaseConstants.QueryParameter.inviteCode, value: inviteCode)
        ]
        guard let link = components.url else { return }
        let dynamicLinksDomainURIPrefix = FirebaseConstants.UrlConstant.deepLinkURL
        let linkBuilder : DynamicLinkComponents = DynamicLinkComponents(link: link, domainURIPrefix: dynamicLinksDomainURIPrefix)!
        linkBuilder.iOSParameters = DynamicLinkIOSParameters(bundleID: FirebaseConstants.IOSParameters.bundleID)
        linkBuilder.iOSParameters?.appStoreID = FirebaseConstants.IOSParameters.appStoreID
        linkBuilder.androidParameters = DynamicLinkAndroidParameters(packageName: FirebaseConstants.IOSParameters.bundleID)
        
        let options = DynamicLinkComponentsOptions()
        options.pathLength = .short
        linkBuilder.options = options
        
        linkBuilder.socialMetaTagParameters = DynamicLinkSocialMetaTagParameters()
        linkBuilder.socialMetaTagParameters?.title = "invite_your_friends_and_get_cash_prizes".localized
        linkBuilder.socialMetaTagParameters?.descriptionText = "download_the_material_app_and_get_free_credit".localized
        linkBuilder.socialMetaTagParameters?.imageURL = URL(string: "https://material.sa/_nuxt/img/logo.7924274.png")
        
        guard let longDynamicLink = linkBuilder.url else { return }
        debugPrint("The long URL is: \(longDynamicLink)")
        linkBuilder.shorten() { url, warnings, error in
            if error == nil, let url = url{
                debugPrint("The short URL is: \(url)")
                completion("\(url)", url.absoluteString.components(separatedBy: "/").last ?? "")
            } else {
                completion(nil, nil)
            }
        }
    }

}
