<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="32700.99.1234" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22685"/>
        <capability name="Named colors" minToolsVersion="9.0"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="Lato-Medium.ttf">
            <string>Lato-Medium</string>
        </array>
        <array key="Lato-Semibold.ttf">
            <string>Lato-Semibold</string>
        </array>
        <array key="LoewNextArabic-Bold.ttf">
            <string>LoewNextArabic-Bold</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" reuseIdentifier="FSPagerViewCell" id="gTV-IL-0wX" customClass="FSPagerViewCell" customModule="TopCustomer" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="400" height="250"/>
            <autoresizingMask key="autoresizingMask"/>
            <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center">
                <rect key="frame" x="0.0" y="0.0" width="400" height="250"/>
                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Sim-2a-6Uc">
                        <rect key="frame" x="5" y="5" width="390" height="240"/>
                        <subviews>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="hzg-Y3-a8J">
                                <rect key="frame" x="0.0" y="10" width="390" height="220"/>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="imageDirection">
                                        <integer key="value" value="1"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="boolean" keyPath="isSkeletonable" value="YES"/>
                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                        <real key="value" value="30"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </imageView>
                            <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="tZJ-fr-7uo">
                                <rect key="frame" x="20" y="30" width="40" height="25"/>
                                <subviews>
                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="mjR-oT-FL8" customClass="MaterialLocalizeImageView" customModule="TopCustomer" customModuleProvider="target">
                                        <rect key="frame" x="0.0" y="0.0" width="40" height="25"/>
                                    </imageView>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Ad" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Q3L-QG-hEf" customClass="MaterialLocalizeLable" customModule="TopCustomer" customModuleProvider="target">
                                        <rect key="frame" x="13.5" y="8" width="13.5" height="9"/>
                                        <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="9"/>
                                        <color key="textColor" name="AppTheme_DiffBlackColor_#101113"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="Q3L-QG-hEf" firstAttribute="centerX" secondItem="tZJ-fr-7uo" secondAttribute="centerX" id="8E1-Dz-Ujl"/>
                                    <constraint firstItem="Q3L-QG-hEf" firstAttribute="centerY" secondItem="tZJ-fr-7uo" secondAttribute="centerY" id="LZm-Ql-rR8"/>
                                    <constraint firstItem="mjR-oT-FL8" firstAttribute="leading" secondItem="tZJ-fr-7uo" secondAttribute="leading" id="MWA-hP-jMw"/>
                                    <constraint firstItem="mjR-oT-FL8" firstAttribute="top" secondItem="tZJ-fr-7uo" secondAttribute="top" id="Yvf-ql-hjh"/>
                                    <constraint firstAttribute="height" constant="25" id="ZEz-ln-u5f"/>
                                    <constraint firstAttribute="width" constant="40" id="cgK-Mm-QgO"/>
                                    <constraint firstAttribute="bottom" secondItem="mjR-oT-FL8" secondAttribute="bottom" id="lPS-BW-6kH"/>
                                    <constraint firstAttribute="trailing" secondItem="mjR-oT-FL8" secondAttribute="trailing" id="zmX-SC-R1q"/>
                                </constraints>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                        <real key="value" value="3"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                                        <real key="value" value="0.5"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                                        <color key="value" name="AppTheme_LightGrayColor_#A0A0A0"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </view>
                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="5" translatesAutoresizingMaskIntoConstraints="NO" id="ce8-2J-NdG">
                                <rect key="frame" x="10" y="10" width="370" height="0.0"/>
                                <subviews>
                                    <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Title" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="3T2-58-QIa">
                                        <rect key="frame" x="0.0" y="0.0" width="102" height="0.0"/>
                                        <fontDescription key="fontDescription" name="Lato-Semibold" family="Lato" pointSize="28"/>
                                        <color key="textColor" name="AppColor1A"/>
                                        <nil key="highlightedColor"/>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="boolean" keyPath="isSkeletonable" value="YES"/>
                                        </userDefinedRuntimeAttributes>
                                    </label>
                                    <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Description" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="cb7-m6-ctC">
                                        <rect key="frame" x="0.0" y="0.0" width="102" height="0.0"/>
                                        <fontDescription key="fontDescription" name="Lato-Medium" family="Lato" pointSize="20"/>
                                        <color key="textColor" name="AppColor1A"/>
                                        <nil key="highlightedColor"/>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="boolean" keyPath="isSkeletonable" value="YES"/>
                                        </userDefinedRuntimeAttributes>
                                    </label>
                                </subviews>
                            </stackView>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="cKW-fn-FBh">
                                <rect key="frame" x="360" y="15" width="20" height="20"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="20" id="CLl-DF-Thd"/>
                                    <constraint firstAttribute="width" constant="20" id="xj4-Ho-0pO"/>
                                </constraints>
                                <color key="tintColor" name="AppTheme_BlueColor_#012CDA"/>
                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                <state key="normal" image="share_round"/>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                                        <real key="value" value="0.0"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </button>
                            <imageView hidden="YES" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_play" translatesAutoresizingMaskIntoConstraints="NO" id="H29-ff-yYm">
                                <rect key="frame" x="163" y="88" width="64" height="64"/>
                            </imageView>
                        </subviews>
                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="hzg-Y3-a8J" firstAttribute="leading" secondItem="Sim-2a-6Uc" secondAttribute="leading" id="4Ow-td-Wnw"/>
                            <constraint firstAttribute="bottom" secondItem="hzg-Y3-a8J" secondAttribute="bottom" constant="10" id="4cU-6R-dTP"/>
                            <constraint firstItem="tZJ-fr-7uo" firstAttribute="top" secondItem="ce8-2J-NdG" secondAttribute="bottom" constant="20" id="6PT-nc-mFS"/>
                            <constraint firstItem="hzg-Y3-a8J" firstAttribute="top" secondItem="Sim-2a-6Uc" secondAttribute="top" constant="10" id="8Fw-iq-IHl"/>
                            <constraint firstAttribute="trailing" secondItem="cKW-fn-FBh" secondAttribute="trailing" constant="10" id="8Nu-Nq-tTw"/>
                            <constraint firstItem="cKW-fn-FBh" firstAttribute="top" secondItem="Sim-2a-6Uc" secondAttribute="top" constant="15" id="NOX-LS-2Av"/>
                            <constraint firstItem="ce8-2J-NdG" firstAttribute="top" secondItem="Sim-2a-6Uc" secondAttribute="top" constant="10" id="RAh-z5-l41"/>
                            <constraint firstItem="H29-ff-yYm" firstAttribute="centerY" secondItem="Sim-2a-6Uc" secondAttribute="centerY" id="RRt-wg-VUe"/>
                            <constraint firstAttribute="trailing" secondItem="ce8-2J-NdG" secondAttribute="trailing" constant="10" id="S2d-61-bdY"/>
                            <constraint firstAttribute="trailing" secondItem="hzg-Y3-a8J" secondAttribute="trailing" id="kLd-Ev-GG0"/>
                            <constraint firstItem="H29-ff-yYm" firstAttribute="centerX" secondItem="Sim-2a-6Uc" secondAttribute="centerX" id="nOR-WZ-jFm"/>
                            <constraint firstItem="tZJ-fr-7uo" firstAttribute="leading" secondItem="Sim-2a-6Uc" secondAttribute="leading" constant="20" id="rEE-sj-bwq"/>
                            <constraint firstItem="ce8-2J-NdG" firstAttribute="leading" secondItem="Sim-2a-6Uc" secondAttribute="leading" constant="10" id="xiA-1N-ZBb"/>
                        </constraints>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                <real key="value" value="15"/>
                            </userDefinedRuntimeAttribute>
                            <userDefinedRuntimeAttribute type="boolean" keyPath="isSkeletonable" value="YES"/>
                            <userDefinedRuntimeAttribute type="color" keyPath="shadowColor">
                                <color key="value" name="AppTheme_BorderColor_#1F1F1F"/>
                            </userDefinedRuntimeAttribute>
                            <userDefinedRuntimeAttribute type="size" keyPath="shadowOffset">
                                <size key="value" width="0.0" height="0.0"/>
                            </userDefinedRuntimeAttribute>
                            <userDefinedRuntimeAttribute type="number" keyPath="shadowBlur">
                                <real key="value" value="12"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </view>
                </subviews>
            </view>
            <viewLayoutGuide key="safeArea" id="ZTg-uK-7eu"/>
            <constraints>
                <constraint firstAttribute="trailing" secondItem="Sim-2a-6Uc" secondAttribute="trailing" constant="5" id="Atv-U8-P6t"/>
                <constraint firstItem="Sim-2a-6Uc" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" constant="5" id="H6Z-01-ZBA"/>
                <constraint firstAttribute="bottom" secondItem="Sim-2a-6Uc" secondAttribute="bottom" constant="5" id="f8A-iO-gcb"/>
                <constraint firstItem="Sim-2a-6Uc" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" constant="5" id="qhH-N6-ZIe"/>
            </constraints>
            <userDefinedRuntimeAttributes>
                <userDefinedRuntimeAttribute type="boolean" keyPath="isSkeletonable" value="YES"/>
                <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                    <real key="value" value="15"/>
                </userDefinedRuntimeAttribute>
                <userDefinedRuntimeAttribute type="size" keyPath="shadowOffset">
                    <size key="value" width="0.0" height="0.0"/>
                </userDefinedRuntimeAttribute>
                <userDefinedRuntimeAttribute type="color" keyPath="shadowColor">
                    <color key="value" name="AppTheme_DiffBlackColor_#101113"/>
                </userDefinedRuntimeAttribute>
                <userDefinedRuntimeAttribute type="number" keyPath="shadowBlur">
                    <real key="value" value="12"/>
                </userDefinedRuntimeAttribute>
            </userDefinedRuntimeAttributes>
            <connections>
                <outlet property="btnShare" destination="cKW-fn-FBh" id="mmh-O4-1qV"/>
                <outlet property="imageView" destination="hzg-Y3-a8J" id="P2m-fC-L1P"/>
                <outlet property="imgPlay" destination="H29-ff-yYm" id="ePL-vw-xjD"/>
                <outlet property="lblAppadvertisementTitle" destination="Q3L-QG-hEf" id="reB-Ax-yEq"/>
                <outlet property="lblDescription" destination="cb7-m6-ctC" id="rec-Dp-kyr"/>
                <outlet property="lblTitle" destination="3T2-58-QIa" id="avQ-y5-eC7"/>
                <outlet property="viewBookmark" destination="tZJ-fr-7uo" id="Zci-XI-OMQ"/>
            </connections>
            <point key="canvasLocation" x="132" y="112"/>
        </collectionViewCell>
    </objects>
    <resources>
        <image name="ic_play" width="64" height="64"/>
        <image name="share_round" width="38" height="38"/>
        <namedColor name="AppColor1A">
            <color red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="AppTheme_BlueColor_#012CDA">
            <color red="0.0039215686274509803" green="0.17254901960784313" blue="0.85490196078431369" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="AppTheme_BorderColor_#1F1F1F">
            <color red="0.12156862745098039" green="0.12156862745098039" blue="0.12156862745098039" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="AppTheme_DiffBlackColor_#101113">
            <color red="0.062745098039215685" green="0.066666666666666666" blue="0.074509803921568626" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="AppTheme_LightGrayColor_#A0A0A0">
            <color red="0.62745098039215685" green="0.62745098039215685" blue="0.62745098039215685" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
    </resources>
</document>
