
import UIKit

public class QTYButtonOLD: UIView {
    class var bundle:Bundle{
        get{
            let podBundle = Bundle(for: QTYButtonOLD.self)
            if let bundleURL = podBundle.url(forResource: "QTYButtonOLD", withExtension: "bundle") {
                return Bundle(url: bundleURL)!
            }else{
                return podBundle
            }
        }
    }
    
    @IBOutlet weak var contentView : UIView!
    @IBOutlet weak public var buttonAdd: UIButton!
    @IBOutlet weak public var buttonPlus: UIButton!
//    @IBOutlet weak public var label: UILabel!
    @IBOutlet weak public var buttonMin: UIButton!
    @IBOutlet weak public var textfield: UITextField!

    private var onChange : (Int, String) -> Void = { _,_  in }
    
    public var text: Int = 1 {
        didSet {
            if textfield != nil {
//                self.setupUI(strAddOrRemove: "")
                if text > 1 {
                    self.setDefaultMinBtn()
                } else if text == 1 {
                    self.buttonMin.setTitle("", for: .normal)
                    self.buttonMin.setImage(UIImage(named: "trash"), for: .normal)
                }
            }
        }
    }
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        commonInit()
    }
    
    required public init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
        commonInit()
    }
    
    private func commonInit() {
        QTYButtonOLD.bundle.loadNibNamed("QTYButtonOLD", owner: self, options: [:])
        addSubview(contentView)
        contentView.translatesAutoresizingMaskIntoConstraints = false
        contentView.topAnchor.constraint(equalTo: topAnchor).isActive = true
        contentView.bottomAnchor.constraint(equalTo: bottomAnchor).isActive = true
        contentView.leftAnchor.constraint(equalTo: leftAnchor).isActive = true
        contentView.rightAnchor.constraint(equalTo: rightAnchor).isActive = true
    }
    
    func setupUI(strAddOrRemove: String) {
//        if text == 0 {
//            self.editable(value: false)
//        }else {
            self.editable(value: true)
//        }
//        self.label.text = String(describing: text)
        if strAddOrRemove != "" {
            self.onChange(Int(text), strAddOrRemove)
        }
    }
    
    func editable(value: Bool) {
        if value == true {
            self.buttonAdd.isHidden = true
            self.buttonMin.isHidden = false
        }else {
            self.buttonAdd.isHidden = true
            self.buttonMin.isHidden = true
        }
    }
    
    @IBAction func clickBuy(_ sender: Any) {
        self.endEditing(true)
        if self.text < 999999 {
            self.text = self.text + 1
            self.textfield.text = String(describing: text)
        } else if self.text == 1 {
            self.buttonMin.setTitle("", for: .normal)
            self.buttonMin.setImage(UIImage(named: "trash"), for: .normal)
        }
    }
    
    @IBAction func clickAdd(_ sender: Any) {
        self.endEditing(true)
        if self.text < 999999 {
            self.text = self.text + 1
            self.textfield.text = String(describing: text)
            self.setupUI(strAddOrRemove: "Add")
            self.setDefaultMinBtn()
        } else if text == 1 {
            self.buttonMin.setTitle("", for: .normal)
            self.buttonMin.setImage(UIImage(named: "trash"), for: .normal)
        }
    }
    
    @IBAction func clickMin(_ sender: Any) {
        self.endEditing(true)
        self.text = self.text - 1
        self.textfield.text = String(describing: text)
        self.setupUI(strAddOrRemove: "Remove")
        if self.text == 1 {
            self.buttonMin.setTitle("", for: .normal)
            self.buttonMin.setImage(UIImage(named: "trash"), for: .normal)
        }
    }
    
    private func setDefaultMinBtn() {
        self.buttonMin.setImage(nil, for: .normal)
        self.buttonMin.setTitle("-", for: .normal)
        if text == 1 {
            self.buttonMin.setTitle("", for: .normal)
            self.buttonMin.setImage(UIImage(named: "trash"), for: .normal)
        }
    }
}

extension QTYButtonOLD {
    public func onChange(value: @escaping (Int, String) -> Void) {
        self.onChange = { qty, strAction in
            value(qty, strAction)
        }
    }
}

// MARK: - UITextFieldDelegate
extension QTYButtonOLD : UITextFieldDelegate {
    
    public func textField(_ textField: UITextField, shouldChangeCharactersIn range: NSRange, replacementString string: String) -> Bool {
        
        let newLength: Int = (textField.text?.length)! + string.length - range.length
        if newLength > MaxQuantityLength {
            return false
        }
        return true
    }
    
    public func textFieldDidEndEditing(_ textField: UITextField) {
        let value = Int(textField.text ?? "") ?? 0
        self.text = value
        
        if textField.text == "" {
            self.onChange(Int(text), "ManualEmpty")
        }
        else {
            self.onChange(Int(text), "Manual")
        }

    }

}
