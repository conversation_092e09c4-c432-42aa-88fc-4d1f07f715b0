<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="22505" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22504"/>
        <capability name="Named colors" minToolsVersion="9.0"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="LoewNextArabic-Bold.ttf">
            <string>LoewNextArabic-Bold</string>
        </array>
        <array key="LoewNextArabic-Medium.ttf">
            <string>LoewNextArabic-Medium</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="QTYButtonOLD" customModule="TopCustomer" customModuleProvider="target">
            <connections>
                <outlet property="buttonAdd" destination="geu-vX-JmI" id="lJO-Ai-HBd"/>
                <outlet property="buttonMin" destination="A8J-XQ-fgr" id="eun-dp-zXJ"/>
                <outlet property="buttonPlus" destination="xiG-LJ-S99" id="vAc-al-AgL"/>
                <outlet property="contentView" destination="yPe-c9-hID" id="RAe-o4-n7g"/>
                <outlet property="textfield" destination="aHW-m5-Jqv" id="k0M-z6-ae0"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" id="yPe-c9-hID">
            <rect key="frame" x="0.0" y="0.0" width="504" height="203"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="xiG-LJ-S99">
                    <rect key="frame" x="0.0" y="20" width="183" height="183"/>
                    <constraints>
                        <constraint firstAttribute="width" secondItem="xiG-LJ-S99" secondAttribute="height" id="L6K-Ni-1Sc"/>
                    </constraints>
                    <fontDescription key="fontDescription" name="LoewNextArabic-Bold" family="Loew Next Arabic" pointSize="14"/>
                    <state key="normal" title="+">
                        <color key="titleColor" name="AppTheme_LightGrayColor_#A0A0A0"/>
                    </state>
                    <connections>
                        <action selector="clickAdd:" destination="-1" eventType="touchUpInside" id="rTl-cC-Sal"/>
                    </connections>
                </button>
                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="A8J-XQ-fgr" userLabel="Button Plus">
                    <rect key="frame" x="321" y="20" width="183" height="183"/>
                    <state key="normal" image="trash">
                        <color key="titleColor" name="AppTheme_LightGrayColor_#A0A0A0"/>
                    </state>
                    <connections>
                        <action selector="clickMin:" destination="-1" eventType="touchUpInside" id="AQu-4Z-UwG"/>
                    </connections>
                </button>
                <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" text="1" borderStyle="roundedRect" textAlignment="center" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="aHW-m5-Jqv">
                    <rect key="frame" x="183" y="20" width="138" height="183"/>
                    <fontDescription key="fontDescription" name="LoewNextArabic-Medium" family="Loew Next Arabic" pointSize="12"/>
                    <textInputTraits key="textInputTraits" keyboardType="ASCIICapableNumberPad"/>
                    <connections>
                        <outlet property="delegate" destination="-1" id="5ld-bc-Xe8"/>
                    </connections>
                </textField>
                <button hidden="YES" opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="geu-vX-JmI" userLabel="button Buy">
                    <rect key="frame" x="168" y="20" width="336" height="183"/>
                    <fontDescription key="fontDescription" type="boldSystem" pointSize="15"/>
                    <state key="normal" title="Add">
                        <color key="titleColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    </state>
                    <connections>
                        <action selector="clickBuy:" destination="-1" eventType="touchUpInside" id="8ZG-Lo-qaj"/>
                    </connections>
                </button>
            </subviews>
            <viewLayoutGuide key="safeArea" id="aJP-Ad-xm6"/>
            <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
            <constraints>
                <constraint firstItem="A8J-XQ-fgr" firstAttribute="bottom" secondItem="aJP-Ad-xm6" secondAttribute="bottom" id="45N-zs-3mB"/>
                <constraint firstItem="geu-vX-JmI" firstAttribute="top" secondItem="aJP-Ad-xm6" secondAttribute="top" id="9Wo-mI-PHI"/>
                <constraint firstItem="xiG-LJ-S99" firstAttribute="top" secondItem="aJP-Ad-xm6" secondAttribute="top" id="O27-H4-Ohc"/>
                <constraint firstItem="A8J-XQ-fgr" firstAttribute="top" secondItem="aJP-Ad-xm6" secondAttribute="top" id="SAc-X1-Uhz"/>
                <constraint firstItem="aJP-Ad-xm6" firstAttribute="trailing" secondItem="A8J-XQ-fgr" secondAttribute="trailing" id="UVp-ie-Tf4"/>
                <constraint firstItem="aHW-m5-Jqv" firstAttribute="top" secondItem="aJP-Ad-xm6" secondAttribute="top" id="Z7s-um-GCy"/>
                <constraint firstItem="A8J-XQ-fgr" firstAttribute="width" secondItem="xiG-LJ-S99" secondAttribute="width" id="bhq-BL-hRK"/>
                <constraint firstItem="geu-vX-JmI" firstAttribute="width" secondItem="yPe-c9-hID" secondAttribute="width" multiplier="1:1.5" id="cua-ZK-zr4"/>
                <constraint firstItem="geu-vX-JmI" firstAttribute="trailing" secondItem="aJP-Ad-xm6" secondAttribute="trailing" id="dOc-H0-c1r"/>
                <constraint firstItem="A8J-XQ-fgr" firstAttribute="leading" secondItem="aHW-m5-Jqv" secondAttribute="trailing" id="dfw-ob-Cbc"/>
                <constraint firstItem="aHW-m5-Jqv" firstAttribute="leading" secondItem="xiG-LJ-S99" secondAttribute="trailing" id="dgf-3g-BC5"/>
                <constraint firstItem="aHW-m5-Jqv" firstAttribute="bottom" secondItem="aJP-Ad-xm6" secondAttribute="bottom" id="fC4-4z-NrL"/>
                <constraint firstItem="geu-vX-JmI" firstAttribute="bottom" secondItem="aJP-Ad-xm6" secondAttribute="bottom" id="wcD-Lv-o37"/>
                <constraint firstItem="xiG-LJ-S99" firstAttribute="bottom" secondItem="aJP-Ad-xm6" secondAttribute="bottom" id="ybS-rZ-TgY"/>
                <constraint firstItem="xiG-LJ-S99" firstAttribute="leading" secondItem="aJP-Ad-xm6" secondAttribute="leading" id="zBU-ZI-HOC"/>
            </constraints>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <userDefinedRuntimeAttributes>
                <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                    <integer key="value" value="1"/>
                </userDefinedRuntimeAttribute>
                <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                    <integer key="value" value="6"/>
                </userDefinedRuntimeAttribute>
                <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                    <color key="value" name="AppTheme_LightGrayColor_#CECECE"/>
                </userDefinedRuntimeAttribute>
            </userDefinedRuntimeAttributes>
            <point key="canvasLocation" x="121" y="-37.5"/>
        </view>
    </objects>
    <resources>
        <image name="trash" width="15" height="15"/>
        <namedColor name="AppTheme_LightGrayColor_#A0A0A0">
            <color red="0.62745098039215685" green="0.62745098039215685" blue="0.62745098039215685" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="AppTheme_LightGrayColor_#CECECE">
            <color red="0.80784313725490198" green="0.80784313725490198" blue="0.80784313725490198" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
    </resources>
</document>
