//
//  GoogleDirectionDetailsViaWaypoint.swift
//  Model Generated using http://www.jsoncafe.com/ 
//  Created on May 19, 2020

import Foundation


class GoogleDirectionDetailsViaWaypoint : NSObject, NSCoding{

    var location : GoogleDirectionDetailsLocation?
    var stepIndex : Int?
    var stepInterpolation : Float?


    /**
     * Instantiate the instance using the passed dictionary values to set the properties values
     */
    init(fromDictionary dictionary: [String:Any]){
        stepIndex = dictionary["step_index"] as? Int
        stepInterpolation = dictionary["step_interpolation"] as? Float
        if let locationData = dictionary["location"] as? [String:Any]{
            location = GoogleDirectionDetailsLocation(fromDictionary: locationData)
        }
    }

    /**
     * Returns all the available property values in the form of [String:Any] object where the key is the approperiate json key and the value is the value of the corresponding property
     */
    func toDictionary() -> [String:Any]
    {
        var dictionary = [String:Any]()
        if stepIndex != nil{
            dictionary["step_index"] = stepIndex ?? 0
        }
        if stepInterpolation != nil{
            dictionary["step_interpolation"] = stepInterpolation ?? 0
        }
        if location != nil{
            dictionary["location"] = location?.toDictionary() ?? [:]
        }
        return dictionary
    }

    /**
     * NSCoding required initializer.
     * Fills the data from the passed decoder
     */
    @objc required init(coder aDecoder: NSCoder)
    {
        location = aDecoder.decodeObject(forKey: "location") as? GoogleDirectionDetailsLocation
        stepIndex = aDecoder.decodeObject(forKey: "step_index") as? Int
        stepInterpolation = aDecoder.decodeObject(forKey: "step_interpolation") as? Float
    }

    /**
     * NSCoding required method.
     * Encodes mode properties into the decoder
     */
    @objc func encode(with aCoder: NSCoder)
    {
        if location != nil{
            aCoder.encode(location, forKey: "location")
        }
        if stepIndex != nil{
            aCoder.encode(stepIndex, forKey: "step_index")
        }
        if stepInterpolation != nil{
            aCoder.encode(stepInterpolation, forKey: "step_interpolation")
        }
    }
}
