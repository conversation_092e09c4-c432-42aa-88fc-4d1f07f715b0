//
//  GoogleDirectionDetailsLeg.swift
//  Model Generated using http://www.jsoncafe.com/ 
//  Created on May 19, 2020

import Foundation


class GoogleDirectionDetailsLeg : NSObject, NSCoding{

    var distance : GoogleDirectionDetailsDistance?
    var duration : GoogleDirectionDetailsDuration?
    var endAddress : String?
    var endLocation : GoogleDirectionDetailsEndLocation?
    var startAddress : String?
    var startLocation : GoogleDirectionDetailsStartLocation?
    var steps : [GoogleDirectionDetailsStep] = []
    var trafficSpeedEntry : [AnyObject] = []
    var viaWaypoint : [GoogleDirectionDetailsViaWaypoint] = []


    /**
     * Instantiate the instance using the passed dictionary values to set the properties values
     */
    init(fromDictionary dictionary: [String:Any]){
        endAddress = dictionary["end_address"] as? String
        startAddress = dictionary["start_address"] as? String
        if let distanceData = dictionary["distance"] as? [String:Any]{
            distance = GoogleDirectionDetailsDistance(fromDictionary: distanceData)
        }
        if let durationData = dictionary["duration"] as? [String:Any]{
            duration = GoogleDirectionDetailsDuration(fromDictionary: durationData)
        }
        if let endLocationData = dictionary["end_location"] as? [String:Any]{
            endLocation = GoogleDirectionDetailsEndLocation(fromDictionary: endLocationData)
        }
        if let startLocationData = dictionary["start_location"] as? [String:Any]{
            startLocation = GoogleDirectionDetailsStartLocation(fromDictionary: startLocationData)
        }
        steps = [GoogleDirectionDetailsStep]()
        if let stepsArray = dictionary["steps"] as? [[String:Any]]{
            for dic in stepsArray{
                let value = GoogleDirectionDetailsStep(fromDictionary: dic)
                steps.append(value)
            }
        }
        viaWaypoint = [GoogleDirectionDetailsViaWaypoint]()
        if let viaWaypointArray = dictionary["via_waypoint"] as? [[String:Any]]{
            for dic in viaWaypointArray{
                let value = GoogleDirectionDetailsViaWaypoint(fromDictionary: dic)
                viaWaypoint.append(value)
            }
        }
    }

    /**
     * Returns all the available property values in the form of [String:Any] object where the key is the approperiate json key and the value is the value of the corresponding property
     */
    func toDictionary() -> [String:Any]
    {
        var dictionary = [String:Any]()
        if endAddress != nil{
            dictionary["end_address"] = endAddress
        }
        if startAddress != nil{
            dictionary["start_address"] = startAddress
        }
        if distance != nil{
            dictionary["distance"] = distance?.toDictionary()  ?? [:]
        }
        if duration != nil{
            dictionary["duration"] = duration?.toDictionary()  ?? [:]
        }
        if endLocation != nil{
            dictionary["endLocation"] = endLocation?.toDictionary()  ?? [:]
        }
        if startLocation != nil{
            dictionary["startLocation"] = startLocation?.toDictionary()  ?? [:]
        }
        
        var dictionarySteps = [[String:Any]]()
        for stepsElement in steps {
            dictionarySteps.append(stepsElement.toDictionary())
        }
        dictionary["steps"] = dictionarySteps
        
        
        var dictionaryViaWayPoint = [[String:Any]]()
        for viaWaypointElement in viaWaypoint {
            dictionaryViaWayPoint.append(viaWaypointElement.toDictionary())
        }
        dictionary["viaWaypoint"] = dictionaryViaWayPoint
        
        return dictionary
    }

    /**
     * NSCoding required initializer.
     * Fills the data from the passed decoder
     */
    @objc required init(coder aDecoder: NSCoder)
    {
        distance = aDecoder.decodeObject(forKey: "distance") as? GoogleDirectionDetailsDistance
        duration = aDecoder.decodeObject(forKey: "duration") as? GoogleDirectionDetailsDuration
        endAddress = aDecoder.decodeObject(forKey: "end_address") as? String
        endLocation = aDecoder.decodeObject(forKey: "end_location") as? GoogleDirectionDetailsEndLocation
        startAddress = aDecoder.decodeObject(forKey: "start_address") as? String
        startLocation = aDecoder.decodeObject(forKey: "start_location") as? GoogleDirectionDetailsStartLocation
        steps = aDecoder.decodeObject(forKey: "steps") as? [GoogleDirectionDetailsStep] ?? []
        trafficSpeedEntry = aDecoder.decodeObject(forKey: "traffic_speed_entry") as? [AnyObject] ?? []
        viaWaypoint = aDecoder.decodeObject(forKey: "via_waypoint") as? [GoogleDirectionDetailsViaWaypoint] ?? []
    }

    /**
     * NSCoding required method.
     * Encodes mode properties into the decoder
     */
    @objc func encode(with aCoder: NSCoder)
    {
        if distance != nil{
            aCoder.encode(distance, forKey: "distance")
        }
        if duration != nil{
            aCoder.encode(duration, forKey: "duration")
        }
        if endAddress != nil{
            aCoder.encode(endAddress, forKey: "end_address")
        }
        if endLocation != nil{
            aCoder.encode(endLocation, forKey: "end_location")
        }
        if startAddress != nil{
            aCoder.encode(startAddress, forKey: "start_address")
        }
        if startLocation != nil{
            aCoder.encode(startLocation, forKey: "start_location")
        }
        
        aCoder.encode(steps, forKey: "steps")
        
        
        aCoder.encode(trafficSpeedEntry, forKey: "traffic_speed_entry")
        
        
        aCoder.encode(viaWaypoint, forKey: "via_waypoint")
        
    }
}
