//
//  GoogleDirectionDetailsRootClass.swift
//  Model Generated using http://www.jsoncafe.com/ 
//  Created on May 19, 2020

import Foundation


class GoogleDirectionDetailsRootClass : NSObject, NSCoding{

    var geocodedWaypoints : [GoogleDirectionDetailsGeocodedWaypoint] = []
    var routes : [GoogleDirectionDetailsRoute] = []
    var status : String?


    /**
     * Instantiate the instance using the passed dictionary values to set the properties values
     */
    init(fromDictionary dictionary: [String:Any]){
        status = dictionary["status"] as? String
        geocodedWaypoints = [GoogleDirectionDetailsGeocodedWaypoint]()
        if let geocodedWaypointsArray = dictionary["geocoded_waypoints"] as? [[String:Any]]{
            for dic in geocodedWaypointsArray{
                let value = GoogleDirectionDetailsGeocodedWaypoint(fromDictionary: dic)
                geocodedWaypoints.append(value)
            }
        }
        routes = [GoogleDirectionDetailsRoute]()
        if let routesArray = dictionary["routes"] as? [[String:Any]]{
            for dic in routesArray{
                let value = GoogleDirectionDetailsRoute(fromDictionary: dic)
                routes.append(value)
            }
        }
    }

    /**
     * Returns all the available property values in the form of [String:Any] object where the key is the approperiate json key and the value is the value of the corresponding property
     */
    func toDictionary() -> [String:Any]
    {
        var dictionary = [String:Any]()
        if status != nil{
            dictionary["status"] = status  ?? ""
        }
        
        var dictionaryGeoCodeedWayPoint = [[String:Any]]()
        for geocodedWaypointsElement in geocodedWaypoints {
            dictionaryGeoCodeedWayPoint.append(geocodedWaypointsElement.toDictionary())
        }
        dictionary["geocodedWaypoints"] = dictionaryGeoCodeedWayPoint
        
        var dictionaryRoutes = [[String:Any]]()
        for routesElement in routes {
            dictionaryRoutes.append(routesElement.toDictionary())
        }
        dictionary["routes"] = dictionaryRoutes
        
        return dictionary
    }

    /**
     * NSCoding required initializer.
     * Fills the data from the passed decoder
     */
    @objc required init(coder aDecoder: NSCoder)
    {
        geocodedWaypoints = aDecoder.decodeObject(forKey: "geocoded_waypoints") as? [GoogleDirectionDetailsGeocodedWaypoint] ?? []
        routes = aDecoder.decodeObject(forKey: "routes") as? [GoogleDirectionDetailsRoute] ?? []
        status = aDecoder.decodeObject(forKey: "status") as? String
    }

    /**
     * NSCoding required method.
     * Encodes mode properties into the decoder
     */
    @objc func encode(with aCoder: NSCoder)
    {
        aCoder.encode(geocodedWaypoints, forKey: "geocoded_waypoints")
        aCoder.encode(routes, forKey: "routes")
        if status != nil{
            aCoder.encode(status, forKey: "status")
        }
    }
}
