//
//  GoogleDirectionDetailsPolyline.swift
//  Model Generated using http://www.jsoncafe.com/ 
//  Created on May 19, 2020

import Foundation


class GoogleDirectionDetailsPolyline : NSObject, NSCoding{

    var points : String?


    /**
     * Instantiate the instance using the passed dictionary values to set the properties values
     */
    init(fromDictionary dictionary: [String:Any]){
        points = dictionary["points"] as? String
    }

    /**
     * Returns all the available property values in the form of [String:Any] object where the key is the approperiate json key and the value is the value of the corresponding property
     */
    func toDictionary() -> [String:Any]
    {
        var dictionary = [String:Any]()
        if points != nil{
            dictionary["points"] = points ?? ""
        }
        return dictionary
    }

    /**
     * NSCoding required initializer.
     * Fills the data from the passed decoder
     */
    @objc required init(coder aDecoder: NSCoder)
    {
        points = aDecoder.decodeObject(forKey: "points") as? String
    }

    /**
     * NSCoding required method.
     * Encodes mode properties into the decoder
     */
    @objc func encode(with aCoder: NSCoder)
    {
        if points != nil{
            aCoder.encode(points, forKey: "points")
        }
    }
}
