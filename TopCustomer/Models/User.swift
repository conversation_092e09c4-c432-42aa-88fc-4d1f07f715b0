
import Foundation

enum UserInfoKeys : String {
    
    case iUserId
    case vName
    case vEmailId
    case vISDCode
    case vLanguage
    case vMobileNumber
    case vNewMobileNumber
    case tiIsMobileVerified
    case tiGender
    case iDob
    case tiIsSocialLogin
    case vAccessToken
    case vNewISDCode
    case vDeviceToken
    case vReferalPlatform

}

class User : NSObject {

    var iUserId: Int?
    var vName: String?
    var vEmailId: String?
    var vISDCode: String?
    var vLanguage: String?
    var vMobileNumber: String?
    var vNewMobileNumber: String?
    var tiIsMobileVerified: Int?
    var tiGender: Int?
    var iDob: String?
    var tiIsSocialLogin: Int?
    var vAccessToken: String?
    var vNewISDCode: String?
    var vDeviceToken: String?
    var vReferalPlatform: String?

    public static let shared = User()

    var arrKeys : [UserInfoKeys] = [.iUserId, .vName, .vEmailId, .vISDCode, .vLanguage, .vMobileNumber, .vNewMobileNumber, .tiIsMobileVerified, .tiGender, .iDob, .tiIsSocialLogin, .vAccessToken, .vNewISDCode, .vDeviceToken, .vReferalPlatform]

    
    override init() {
        super.init()
        let defaults = UserDefaults.standard
        
        iUserId = Int(setDataInString(defaults.object(forKey: UserInfoKeys.iUserId.rawValue) as AnyObject))
        vName = setDataInString(defaults.object(forKey: UserInfoKeys.vName.rawValue) as AnyObject)
        vEmailId = setDataInString(defaults.object(forKey: UserInfoKeys.vEmailId.rawValue) as AnyObject)
        vISDCode = setDataInString(defaults.object(forKey: UserInfoKeys.vISDCode.rawValue) as AnyObject)
        vLanguage = setDataInString(defaults.object(forKey: UserInfoKeys.vLanguage.rawValue) as AnyObject)
        vMobileNumber = setDataInString(defaults.object(forKey: UserInfoKeys.vMobileNumber.rawValue) as AnyObject)
        vNewMobileNumber = setDataInString(defaults.object(forKey: UserInfoKeys.vNewMobileNumber.rawValue) as AnyObject)
        tiIsMobileVerified = Int(setDataInString(defaults.object(forKey: UserInfoKeys.tiIsMobileVerified.rawValue) as AnyObject))
        tiGender = Int(setDataInString(defaults.object(forKey: UserInfoKeys.tiGender.rawValue) as AnyObject))
        iDob = setDataInString(defaults.object(forKey: UserInfoKeys.iDob.rawValue) as AnyObject)
        tiIsSocialLogin = Int(setDataInString(defaults.object(forKey: UserInfoKeys.tiIsSocialLogin.rawValue) as AnyObject))
        vAccessToken = setDataInString(defaults.object(forKey: UserInfoKeys.vAccessToken.rawValue) as AnyObject)
        vNewISDCode = setDataInString(defaults.object(forKey: UserInfoKeys.vNewISDCode.rawValue) as AnyObject)
        vDeviceToken = setDataInString(defaults.object(forKey: UserInfoKeys.vDeviceToken.rawValue) as AnyObject)
        vReferalPlatform = setDataInString(defaults.object(forKey: UserInfoKeys.vReferalPlatform.rawValue) as AnyObject)

    }

    func setDataFromDictionary(dict:[String:Any]) {
        let userDefault = UserDefaults.standard
        for key in arrKeys {
            if dict.keys.contains(key.rawValue){
                let value = dict[key.rawValue]
                userDefault.set(setDataInString(value as AnyObject), forKey: key.rawValue)
                switch (key) {
                                    
                case .vAccessToken:
                    self.vAccessToken = setDataInString(value as AnyObject)
                case .vName:
                    self.vName = setDataInString(value as AnyObject)
                case .vEmailId:
                    self.vEmailId = setDataInString(value as AnyObject)
                case .vISDCode:
                    self.vISDCode = setDataInString(value as AnyObject)
                case .vMobileNumber:
                    self.vMobileNumber = setDataInString(value as AnyObject)
                case .tiIsMobileVerified:
                    self.tiIsMobileVerified = Int(setDataInString(value as AnyObject))
                case .tiIsSocialLogin:
                    self.tiIsSocialLogin = Int(setDataInString(value as AnyObject))
                case .iUserId:
                    self.iUserId = Int(setDataInString(value as AnyObject))
                case .vNewMobileNumber:
                    self.vNewMobileNumber = setDataInString(value as AnyObject)
                case .vLanguage:
                    self.vLanguage = setDataInString(value as AnyObject)
                case .tiGender:
                    self.tiGender = Int(setDataInString(value as AnyObject))
                case .iDob:
                    self.iDob = setDataInString(value as AnyObject)
                case .vNewISDCode:
                    self.vNewISDCode = setDataInString(value as AnyObject)
                case .vDeviceToken:
                    self.vDeviceToken = setDataInString(value as AnyObject)
                case .vReferalPlatform:
                    self.vReferalPlatform = setDataInString(value as AnyObject)

                }
            }
        }
        userDefault.synchronize()
    }

    func setDataFromValue(value:Any,key:UserInfoKeys) {
        var dictData : [String:Any] = [:]
        dictData[key.rawValue] = value
        self.setDataFromDictionary(dict: dictData)
    }

    func toDictionary() -> [String:Any]
    {
        var dictionary = [String:Any]()
        if vAccessToken != nil{
            dictionary["vAccessToken"] = vAccessToken
        }
        if vName != nil{
            dictionary["vName"] = vName
        }
        if vEmailId != nil{
            dictionary["vEmailId"] = vEmailId
        }
        if vISDCode != nil{
            dictionary["vISDCode"] = vISDCode
        }
        if vMobileNumber != nil{
            dictionary["vMobileNumber"] = vMobileNumber
        }
        if tiIsMobileVerified != nil{
            dictionary["tiIsMobileVerified"] = tiIsMobileVerified
        }
        if tiIsSocialLogin != nil{
            dictionary["tiIsSocialLogin"] = tiIsSocialLogin
        }
        if iUserId != nil{
            dictionary["iUserId"] = iUserId
        }

        if vNewMobileNumber != nil {
            dictionary["vNewMobileNumber"] = vNewMobileNumber
        }
        
        if vLanguage != nil {
            dictionary["vLanguage"] = vLanguage
        }
        if tiGender != nil {
            dictionary["tiGender"] = tiGender
        }
        if iDob != nil {
            dictionary["iDob"] = iDob
        }
        if vNewISDCode != nil{
            dictionary["vNewISDCode"] = vNewISDCode
        }
        if vDeviceToken != nil{
            dictionary["vDeviceToken"] = vDeviceToken
        }
        if vReferalPlatform != nil{
            dictionary["vReferalPlatform"] = vReferalPlatform
        }

        return dictionary
    }

    func setUserLoginStatus(isLogin:Bool) {
        
        if !isLogin {
            self.logOut()
        }
        
        UserDefaults.standard.set(isLogin, forKey: USERDEFAULTS_INFO_KEY.IS_LOGIN)
        UserDefaults.standard.synchronize()
        
    }
    
    func checkUserLoginStatus() -> Bool {
        let isValidLogin = UserDefaults.standard.bool(forKey: USERDEFAULTS_INFO_KEY.IS_LOGIN)
        return isValidLogin
    }
    
    private func logOut() {
        let userDefault = UserDefaults.standard
        for keys in arrKeys {
            userDefault.removeObject(forKey: keys.rawValue)
        }
        
        userDefault.removeObject(forKey: USERDEFAULTS_INFO_KEY.CURRENT_ADDRESS)
        userDefault.removeObject(forKey: USERDEFAULTS_INFO_KEY.WALLET_BALANCE)
        userDefault.removeObject(forKey: USERDEFAULTS_INFO_KEY.WALLET_BALANCE_EN_NUMBER)

        userDefault.synchronize()
        
        removeAllUserData()
    }

    func removeAllUserData() {
        vAccessToken = nil
        vName = nil
        vEmailId = nil
        vISDCode = nil
        vMobileNumber = nil
        tiIsMobileVerified = nil
        tiIsSocialLogin = nil
        iUserId = nil
        vNewMobileNumber = nil
        vLanguage = nil
        tiGender = nil
        iDob = nil
        vNewISDCode = nil
        vDeviceToken = nil
        vReferalPlatform = nil

    }


}
