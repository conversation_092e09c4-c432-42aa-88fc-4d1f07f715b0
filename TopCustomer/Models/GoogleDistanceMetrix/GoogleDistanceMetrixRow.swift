//
//  GoogleDistanceMetrixRow.swift
//  Model Generated using http://www.jsoncafe.com/ 
//  Created on May 22, 2020

import Foundation


class GoogleDistanceMetrixRow : NSObject, NSCoding{

    var elements : [GoogleDistanceMetrixElement] = []


    /**
     * Instantiate the instance using the passed dictionary values to set the properties values
     */
    init(fromDictionary dictionary: [String:Any]){
        elements = [GoogleDistanceMetrixElement]()
        if let elementsArray = dictionary["elements"] as? [[String:Any]]{
            for dic in elementsArray{
                let value = GoogleDistanceMetrixElement(fromDictionary: dic)
                elements.append(value)
            }
        }
    }

    /**
     * Returns all the available property values in the form of [String:Any] object where the key is the approperiate json key and the value is the value of the corresponding property
     */
    func toDictionary() -> [String:Any]
    {
        var dictionary = [String:Any]()
        
        var dictionaryElements = [[String:Any]]()
        for elementsElement in elements {
            dictionaryElements.append(elementsElement.toDictionary())
        }
        dictionary["elements"] = dictionaryElements
        
        return dictionary
    }

    /**
     * NSCoding required initializer.
     * Fills the data from the passed decoder
     */
    @objc required init(coder aDecoder: NSCoder)
    {
        elements = aDecoder.decodeObject(forKey: "elements") as? [GoogleDistanceMetrixElement] ?? []
    }

    /**
     * NSCoding required method.
     * Encodes mode properties into the decoder
     */
    @objc func encode(with aCoder: NSCoder)
    {
        if elements != nil{
            aCoder.encode(elements, forKey: "elements")
        }
    }
}
