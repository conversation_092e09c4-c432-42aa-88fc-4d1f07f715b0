//
//  GoogleDistanceMetrixRootClass.swift
//  Model Generated using http://www.jsoncafe.com/ 
//  Created on May 22, 2020

import Foundation


class GoogleDistanceMetrixRootClass : NSObject, NSCoding{

    var destinationAddresses : [String] = []
    var originAddresses : [String] = []
    var rows : [GoogleDistanceMetrixRow] = []
    var status : String?


    /**
     * Instantiate the instance using the passed dictionary values to set the properties values
     */
    init(fromDictionary dictionary: [String:Any]){
        status = dictionary["status"] as? String
        rows = [GoogleDistanceMetrixRow]()
        if let rowsArray = dictionary["rows"] as? [[String:Any]]{
            for dic in rowsArray{
                let value = GoogleDistanceMetrixRow(fromDictionary: dic)
                rows.append(value)
            }
        }
    }

    /**
     * Returns all the available property values in the form of [String:Any] object where the key is the approperiate json key and the value is the value of the corresponding property
     */
    func toDictionary() -> [String:Any]
    {
        var dictionary = [String:Any]()
        if status != nil{
            dictionary["status"] = status ?? ""
        }
        var dictionaryRow = [[String:Any]]()
        for rowsElement in rows {
            dictionaryRow.append(rowsElement.toDictionary())
        }
        dictionary["rows"] = dictionaryRow
        
        return dictionary
    }

    /**
     * NSCoding required initializer.
     * Fills the data from the passed decoder
     */
    @objc required init(coder aDecoder: NSCoder)
    {
        destinationAddresses = aDecoder.decodeObject(forKey: "destination_addresses") as? [String] ?? []
        originAddresses = aDecoder.decodeObject(forKey: "origin_addresses") as? [String] ?? []
        rows = aDecoder.decodeObject(forKey: "rows") as? [GoogleDistanceMetrixRow] ?? []
        status = aDecoder.decodeObject(forKey: "status") as? String
    }

    /**
     * NSCoding required method.
     * Encodes mode properties into the decoder
     */
    @objc func encode(with aCoder: NSCoder)
    {
        if destinationAddresses != nil{
            aCoder.encode(destinationAddresses, forKey: "destination_addresses")
        }
        if originAddresses != nil{
            aCoder.encode(originAddresses, forKey: "origin_addresses")
        }
        if rows != nil{
            aCoder.encode(rows, forKey: "rows")
        }
        if status != nil{
            aCoder.encode(status, forKey: "status")
        }
    }
}
