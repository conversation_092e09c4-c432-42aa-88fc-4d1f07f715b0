//
//  GoogleDistanceMetrixElement.swift
//  Model Generated using http://www.jsoncafe.com/ 
//  Created on May 22, 2020

import Foundation


class GoogleDistanceMetrixElement : NSObject, NSCoding{

    var distance : GoogleDistanceMetrixDistance?
    var duration : GoogleDistanceMetrixDuration?
    var status : String?


    /**
     * Instantiate the instance using the passed dictionary values to set the properties values
     */
    init(fromDictionary dictionary: [String:Any]){
        status = dictionary["status"] as? String
        if let distanceData = dictionary["distance"] as? [String:Any]{
            distance = GoogleDistanceMetrixDistance(fromDictionary: distanceData)
        }
        if let durationData = dictionary["duration"] as? [String:Any]{
            duration = GoogleDistanceMetrixDuration(fromDictionary: durationData)
        }
    }

    /**
     * Returns all the available property values in the form of [String:Any] object where the key is the approperiate json key and the value is the value of the corresponding property
     */
    func toDictionary() -> [String:Any]
    {
        var dictionary = [String:Any]()
        if status != nil{
            dictionary["status"] = status ?? ""
        }
        if distance != nil{
            dictionary["distance"] = distance?.toDictionary() ?? [:]
        }
        if duration != nil{
            dictionary["duration"] = duration?.toDictionary() ?? [:]
        }
        return dictionary
    }

    /**
     * NSCoding required initializer.
     * Fills the data from the passed decoder
     */
    @objc required init(coder aDecoder: NSCoder)
    {
        distance = aDecoder.decodeObject(forKey: "distance") as? GoogleDistanceMetrixDistance
        duration = aDecoder.decodeObject(forKey: "duration") as? GoogleDistanceMetrixDuration
        status = aDecoder.decodeObject(forKey: "status") as? String
    }

    /**
     * NSCoding required method.
     * Encodes mode properties into the decoder
     */
    @objc func encode(with aCoder: NSCoder)
    {
        if distance != nil{
            aCoder.encode(distance, forKey: "distance")
        }
        if duration != nil{
            aCoder.encode(duration, forKey: "duration")
        }
        if status != nil{
            aCoder.encode(status, forKey: "status")
        }
    }
}
