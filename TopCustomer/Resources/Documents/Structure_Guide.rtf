{\rtf1\ansi\ansicpg1252\cocoartf1561
{\fonttbl\f0\fswiss\fcharset0 Helvetica;}
{\colortbl;\red255\green255\blue255;}
{\*\expandedcolortbl;;}
\margl1440\margr1440\vieww9000\viewh8400\viewkind0
\pard\tx566\tx1133\tx1700\tx2267\tx2834\tx3401\tx3968\tx4535\tx5102\tx5669\tx6236\tx6803\pardirnatural\partightenfactor0

\f0\b\fs24 \cf0 IMP URLS\
\
//Config Example\
https://www.appcoda.com/xcconfig-guide/\
https://hackernoon.com/a-cleaner-way-to-organize-your-ios-debug-development-and-release-distributions-6b5eb6a48356\
\
\
Folder Structure\
\
\pard\tx560\tx1120\tx1680\tx2240\tx2800\tx3360\tx3920\tx4480\tx5040\tx5600\tx6160\tx6720\pardirnatural\partightenfactor0

\b0 \cf0 	I.	 Application >> Specific app related stuff like AppDelegate, Bridging header etc\
	II.	 Controller >> All the controller with VIP in separate folder \
	III.	 Library >> Specific application classes like Helper, Base Classes, Service classes etc\
	IV.	 Model >> Application Domain model, Entity, Core data model etc.\
	V.	 Resources >> Images, videos, audios document.\
	VI.	 Vendors >> Third party libraries and Framework.}