
import UIKit

class CustomRoundedButtton: UIButton {

    // Only override draw() if you perform custom drawing.
    // An empty implementation adversely affects performance during animation.
    override func draw(_ rect: CGRect) {
        self.layer.cornerRadius = 7
        self.layer.masksToBounds = true
    }
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        sharedInit()
    }
    
    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
        sharedInit()
    }
    
    override func prepareForInterfaceBuilder() {
        sharedInit()
    }

    func sharedInit() {
        self.setTitleColor(.white, for: .normal)
        self.titleLabel?.font = UIFont(name: Fonts.LoewNextArabicBold, size: 13)
        self.backgroundColor = UIColor.AppTheme_BlueColor_012CDA
    }
}


class CustomRoundedSecondaryButtton: UIButton {

    // Only override draw() if you perform custom drawing.
    // An empty implementation adversely affects performance during animation.
    override func draw(_ rect: CGRect) {
        self.layer.cornerRadius = 7
        self.layer.borderWidth = 1.0
        self.layer.borderColor = UIColor.AppTheme_BlueColor_012CDA.cgColor
        self.layer.masksToBounds = true
    }
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        sharedInit()
    }
    
    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
        sharedInit()
    }
    
    override func prepareForInterfaceBuilder() {
        sharedInit()
    }

    func sharedInit() {
        self.setTitleColor(UIColor.AppTheme_BlueColor_012CDA, for: .normal)
        self.titleLabel?.font = UIFont(name: Fonts.LoewNextArabicBold, size: 13)
        self.backgroundColor = .white
    }
}
