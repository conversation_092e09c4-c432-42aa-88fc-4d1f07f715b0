//
//  FirebaseEvents.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 15/02/2025.
//  Copyright © 2025 SOTSYS203. All rights reserved.
//

import Foundation
import FirebaseAnalytics

class FirebaseEvents {
    // MARK: - Properties
    let name: String
    let properties: [String: Any]
    
    // MARK: - Init
    init(name: String, properties: [String: Any]) {
        self.name = name
        self.properties = properties
    }
    
    // MARK: - Func
    func sendEvent() {
        Analytics.logEvent(name, parameters: properties)
    }
    
}
