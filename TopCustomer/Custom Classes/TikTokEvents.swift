//
//  TikTokEvents.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 14/02/2025.
//  Copyright © 2025 SOTSYS203. All rights reserved.
//
import Foundation
import TikTokBusinessSDK

class TikTokEvents {
    // MARK: - Properties
    let name: String
    let properties: [String: Any]
    
    // MARK: - Init
    init(name: String, properties: [String: Any]) {
        self.name = name
        self.properties = properties
    }
    
    // MARK: - Func
    func sendEvent() {
        let event = TikTokBaseEvent()
        event.eventName = self.name
        event.properties = self.properties
        TikTokBusiness.trackTTEvent(event)
    }
    
}
