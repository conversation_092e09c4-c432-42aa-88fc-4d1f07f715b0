//
//  Font+.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 28/11/2023.
//  Copyright © 2023 SOTSYS203. All rights reserved.
//

import UIKit

extension UIFont {
    // MARK: - Enum
    enum LoewNextArabic {
        case regular(size: CGFloat)
        case bold(size: CGFloat)
        case medium(size: CGFloat)
        
        var font: UIFont! {
            switch self {
            case .regular(let size):
                return UIFont(name: "LoewNextArabic-Heavy", size: size)
            case .bold(let size):
                return UIFont(name: "LoewNextArabic-Bold", size: size)
            case .medium(let size):
                return UIFont(name: "LoewNextArabic-Medium", size: size)
            }
        }
    }
    
}
