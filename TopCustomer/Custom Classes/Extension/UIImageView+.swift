//
//  UIImageView+.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 11/07/2024.
//  Copyright © 2024 SOTSYS203. All rights reserved.
//

import UIKit
import SVGKit

// downloaded images SVG
extension UIImageView {
    func downloadedSVG(from url: URL, contentMode mode: UIView.ContentMode = .scaleAspectFit) {
        contentMode = mode
        URLSession.shared.dataTask(with: url) { data, response, error in
            guard
                let httpURLResponse = response as? HTTPURLResponse, httpURLResponse.statusCode == 200,
                let mimeType = response?.mimeType, mimeType.hasPrefix("image"),
                let data = data, error == nil,
                let receivedicon: SVGKImage = SVGKImage(data: data),
                let image = receivedicon.uiImage
            else { return }
            DispatchQueue.main.async() {
                self.image = image
                self.kf.indicator?.stopAnimatingView()
            }
        }.resume()
    }
}
