//
//  UILabel+.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 04/10/2023.
//  Copyright © 2023 SOTSYS203. All rights reserved.
//

import UIKit

extension UILabel {
    
    func alignmentText() {
        // english
        if UserDefaults.standard.getLanguage()! == UserAPI.VLanguage_userLanguage.en.rawValue {
            self.textAlignment = .left
        } else {
            self.textAlignment = .right
        }
    }
    
    func setLineSpacing(lineSpacing: CGFloat = 0.0, lineHeightMultiple: CGFloat = 0.0) {
         guard let labelText = self.text else { return }
         let paragraphStyle = NSMutableParagraphStyle()
         paragraphStyle.lineSpacing = lineSpacing
         paragraphStyle.lineHeightMultiple = lineHeightMultiple

         let attributedString:NSMutableAttributedString
         if let labelattributedText = self.attributedText {
             attributedString = NSMutableAttributedString(attributedString: labelattributedText)
         } else {
             attributedString = NSMutableAttributedString(string: labelText)
         }
         attributedString.addAttribute(NSAttributedString.Key.paragraphStyle, value:paragraphStyle, range:NSMakeRange(0, attributedString.length))
         self.attributedText = attributedString
     }
    
    func underLine() {
        // Create a shadow
        let myShadow = NSShadow()
        myShadow.shadowBlurRadius = 1
        myShadow.shadowOffset = CGSize(width: 1, height: 1)
        myShadow.shadowColor = UIColor.gray
        if let textUnwrapped = self.text {
            let underlineAttribute = [NSAttributedString.Key.shadow: myShadow] as [NSAttributedString.Key : Any]
            let underlineAttributedString = NSAttributedString(string: textUnwrapped, attributes: underlineAttribute)
            self.attributedText = underlineAttributedString
        }
    }
    
    func removeUnderLine() {
        if let textUnwrapped = self.text {
            self.attributedText = nil
            self.text = textUnwrapped
        }
    }
    
}

extension NSAttributedString {
    func withUnderlineSpacing(spacing: CGFloat) -> NSAttributedString {
        let attributedString = NSMutableAttributedString(attributedString: self)
        attributedString.addAttribute(.underlineStyle, value: NSUnderlineStyle.single.rawValue, range: NSRange(location: 0, length: self.length))
        attributedString.addAttribute(.baselineOffset, value: spacing, range: NSRange(location: 0, length: self.length))
        return attributedString
    }
}
