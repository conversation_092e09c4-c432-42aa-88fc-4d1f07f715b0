
import UIKit
import MediaPlayer
import TikTokBusinessSDK
import FirebaseAnalytics
import SwiftUI

class SplashViewController: UIViewController {
    
    //MARK:-  Variables & IBOutlet
    var player: AVPlayer?
    var playerLayer:AVPlayerLayer?
    
    var pID: String?
    var oID: String?
    var orderID: String?
    var categoryID: String?
    var categoryName: String?
    var orderIDForOrderFlow: String?
    var orderIDForCancelledOrderByAdmin: String?
    var flagCartListScreen = false
    var orderIDForScheduledOrder: String?
    var flagCurrentOrdersListScreen = false
    var bannerId: String?
    
    //MARK:-  ViewControllerLifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        
        
    }
    
    @objc func openDeliveryType() {
        AppDel?.window?.rootViewController = UIHostingController(rootView: SelectDeliveryTypeView())
        AppDel?.window?.makeKeyAndVisible()
    }
    
    @objc func goToHomeScreen(_ notification: NSNotification) {
        if let strProductId = (notification.object as? [String:Any]) {
            if let id = strProductId["productId"] as? String {
                print(id)
                pID = id
            }
        }
    }
    
    @objc func goToCurrentOrderDetail(_ notification: NSNotification) {
        if let strOrderId = (notification.object as? [String:Any]) {
            if let id = strOrderId["iOrderId"] as? String {
                print(id)
                orderIDForOrderFlow = id
            }
        }
    }
    
    @objc func goToHistoryOrderDetail(_ notification: NSNotification) {
        if let strOrderId = (notification.object as? [String:Any]) {
            if let id = strOrderId["iOrderId"] as? String {
                print(id)
                orderIDForCancelledOrderByAdmin = id
            }
        }
    }
    
    @objc func goToCartScreen() {
        flagCartListScreen = true
    }
    
    @objc func goToOfferScreen(_ notification: NSNotification) {
        if let strOfferId = (notification.object as? [String:Any]) {
            if let id = strOfferId["offerId"] as? String {
                print(id)
                oID = id
            }
        }
    }
    
    @objc func openBannerInfoPopup(_ notification: NSNotification) {
        if let strBannerId = (notification.object as? [String:Any]) {
            if let id = strBannerId["bannerId"] as? String {
                print(id)
                bannerId = id
            }
        }
    }
    
    @objc func openRating(_ notification: NSNotification) {
        if let strOrderId = (notification.object as? [String:Any]) {
            if let id = strOrderId["iOrderId"] as? String {
                print(id)
                orderID = id
            }
        }
    }
    
    @objc func goToProductListScreen(_ notification: NSNotification) {
        if let strCategoryId = (notification.object as? [String:Any]) {
            if let id = strCategoryId["categoryId"] as? String {
                print(id)
                categoryID = id
            }
            if let name = strCategoryId["categoryName"] as? String {
                print(name)
                categoryName = name
            }
        }
    }
    
    @objc func goToScheduledOrderDetail(_ notification: NSNotification) {
        if let strOrderId = (notification.object as? [String:Any]) {
            if let id = strOrderId["iOrderId"] as? String {
                print(id)
                orderIDForScheduledOrder = id
            }
        }
    }
    
    @objc func goToCurrentOrdersListScreen() {
        flagCurrentOrdersListScreen = true
    }
    
    @objc func applicationDidEnterBackground(){
        self.player?.pause()
    }
    
    @objc func applicationDidBecomeActive(){
        self.player?.play()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        NotificationCenter.default.addObserver(self, selector: #selector(openDeliveryType), name: NSNotification.Name("OpenDeliveryType"), object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(goToHomeScreen), name: NSNotification.Name(rawValue: "ProductFromDeepLink"), object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(goToOfferScreen), name: NSNotification.Name(rawValue: "OfferFromDeepLink"), object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(openRating), name: NSNotification.Name(rawValue: "OpenRatingPopup"), object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(goToProductListScreen), name: NSNotification.Name(rawValue: "GoToProductList"), object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(goToCurrentOrderDetail), name: NSNotification.Name(rawValue: "OpenCurrentOrderDetailPopup"), object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(goToHistoryOrderDetail), name: NSNotification.Name(rawValue: "OpenHistoryOrderDetailPopup"), object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(goToCartScreen), name: NSNotification.Name(rawValue: "OpenCartScreen"), object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(goToScheduledOrderDetail), name: NSNotification.Name(rawValue: "OpenScheduledOrderDetailPopup"), object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(goToCurrentOrdersListScreen), name: NSNotification.Name(rawValue: "GoToCurrentOrdersForIPN"), object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(openBannerInfoPopup), name: NSNotification.Name(rawValue: "BannerInfoFromDeepLink"), object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(applicationDidEnterBackground), name: Notification.Name.NSExtensionHostDidEnterBackground, object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(applicationDidBecomeActive), name: Notification.Name.NSExtensionHostDidBecomeActive, object: nil)
        self.loadVideo()
    }
    
    override func viewDidLayoutSubviews() {
        playerLayer?.frame = self.view.frame
    }
    override var shouldAutorotate: Bool{
        return true
    }
    override var supportedInterfaceOrientations: UIInterfaceOrientationMask{
        return .portrait
    }
    override func viewDidDisappear(_ animated: Bool) {
        super.viewDidDisappear(animated)
        
        NotificationCenter.default.removeObserver(self, name: Notification.Name.NSExtensionHostDidEnterBackground, object: nil)
        NotificationCenter.default.removeObserver(self, name: Notification.Name.NSExtensionHostDidBecomeActive, object: nil)
    }
    
    override func didReceiveMemoryWarning() {
        super.didReceiveMemoryWarning()
    }
    
    //MARK:-  Custom function
    private func loadVideo() {
        do {
            try AVAudioSession.sharedInstance().setCategory(AVAudioSession.Category.ambient)
        } catch { }
        
        NotificationCenter.default.addObserver(self, selector: #selector(self.playerDidFinishPlaying(sender:)), name: NSNotification.Name.AVPlayerItemDidPlayToEndTime, object: nil)
        
        let path = Bundle.main.path(forResource: "Material_video", ofType:"mp4")
        let filePathURL = NSURL.fileURL(withPath: path ?? "")
        player = AVPlayer(url: filePathURL)
        playerLayer = AVPlayerLayer(player: player)
        
        playerLayer?.frame = self.view.frame
        playerLayer?.videoGravity = AVLayerVideoGravity.resizeAspectFill
        playerLayer?.zPosition = -1
        player?.seek(to: CMTime.zero)
        player?.play()
        let videoPlayerView = VideoPlayerView(frame: self.view.frame)
        videoPlayerView.playerLayer.videoGravity = AVLayerVideoGravity.resizeAspectFill
        videoPlayerView.player = player
        videoPlayerView.layer.addSublayer(playerLayer!)
        self.view.addSubview(videoPlayerView)
        self.view.bringSubviewToFront(videoPlayerView)
    }
    
    @objc func playerDidFinishPlaying(sender: Notification) {
        print("playerDidFinishPlaying")
        
        let launchEvent = TikTokBaseEvent(name: TTEventName.launchAPP.rawValue)
        TikTokBusiness.trackTTEvent(launchEvent)
        Analytics.logEvent(TTEventName.launchAPP.rawValue, parameters: nil)
        
        if User.shared.checkUserLoginStatus() {
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5 , execute: {
                if let x = self.pID {
                    NotificationCenter.default.post(name: NSNotification.Name("OpenProductDetailFromDeepLink"), object: ["productId": x], userInfo: nil)
                    self.pID = nil
                }
                
                if let y = self.oID {
                    NotificationCenter.default.post(name: NSNotification.Name("OfferFromDeepLink"), object: ["offerId": y], userInfo: nil)
                    self.oID = nil
                }
                
                if let z = self.orderID {
                    NotificationCenter.default.post(name: NSNotification.Name("OpenRatingPopupOnHome"), object: ["iOrderId": z], userInfo: nil)
                    self.orderID = nil
                }
                
                if let cId = self.categoryID {
                    NotificationCenter.default.post(name: NSNotification.Name("OpenProductSearchScreen"), object: ["categoryId": cId, "categoryName": self.categoryName], userInfo: nil)
                    self.categoryID = nil
                    self.categoryName = nil
                }
                
                if let orderId = self.orderIDForOrderFlow { // OpenCurrentOrderDetail
                    NotificationCenter.default.post(name: NSNotification.Name("OpenCurrentOrderDetailPopup"), object: ["iOrderId": orderId], userInfo: nil)
                    self.orderIDForOrderFlow = nil
                }
                
                if let orderId = self.orderIDForCancelledOrderByAdmin { // OpenHistoryOrderDetail
                    NotificationCenter.default.post(name: NSNotification.Name("OpenHistoryOrderDetailPopup"), object: ["iOrderId": orderId], userInfo: nil)
                    self.orderIDForCancelledOrderByAdmin = nil
                }
                
                if self.flagCartListScreen == true {
                    NotificationCenter.default.post(name: NSNotification.Name("OpenCartScreen"), object: nil, userInfo: nil)
                    self.flagCartListScreen = false
                }
                
                if let orderId = self.orderIDForScheduledOrder { // OpenScheduledOrderDetail
                    NotificationCenter.default.post(name: NSNotification.Name("OpenScheduledOrderDetailPopup"), object: ["iOrderId": orderId], userInfo: nil)
                    self.orderIDForScheduledOrder = nil
                }
                
                if self.flagCurrentOrdersListScreen == true {
                    NotificationCenter.default.post(name: NSNotification.Name("GoToCurrentOrdersForIPN"), object: nil, userInfo: nil)
                    self.flagCurrentOrdersListScreen = false
                }
                
                if let bId = self.bannerId {
                    NotificationCenter.default.post(name: NSNotification.Name("BannerInfoFromDeepLink"), object: ["bannerId": bId], userInfo: nil)
                    self.bannerId = nil
                }
                
            })
//            self.navigateToHomeScreen()
            selectDeliveryType()
        }
        else {
            self.goToLoginScreen()
        }
    }
    
    func goToLoginScreen() {
        if isGuestLocationSaved() {
            selectDeliveryType()
        } else {
            let vc = ProductPopupStoryboard.instantiate(AddAddressViewController.self)
            vc.modalPresentationStyle = .overFullScreen
            vc.isMustSelectMosque = false
            vc.isLaunch = true
            vc.refreshAddressList = { [weak self] () in
                debugPrint("refreshAddressList")
            }
            self.presentVC(vc)
        }
    }
    
    func navigateToHomeScreen() {
        let vc = homeStoryboard.instantiate(MainTabbarViewController.self)
        let navigationVc = UINavigationController(rootViewController: vc)
        AppDel?.window?.rootViewController = navigationVc
    }
    
    func selectDeliveryType() {
        AppDel?.window?.rootViewController = UIHostingController(rootView: SelectDeliveryTypeView())
        AppDel?.window?.makeKeyAndVisible()
    }
    
}

class VideoPlayerView: UIView {
    var player: AVPlayer? {
        get {
            return playerLayer.player
        }
        
        set {
            playerLayer.player = newValue
        }
    }
    
    var playerLayer: AVPlayerLayer {
        
        return layer as! AVPlayerLayer
    }
    
    override class var layerClass: AnyClass {
        return AVPlayerLayer.self
    }
}
