//
//  CustomSwiftUIAlertView.swift
//  TopCustomer
//
//  Created by macintosh on 17/06/2025.
//  Copyright © 2025 SOTSYS203. All rights reserved.
//

import Foundation
import SwiftUI

import SwiftUI

struct CustomAlertView: View {
    var message: String
    var onDismiss: () -> Void

    var body: some View {
        VStack(spacing: 16) {
            Text("Error")
                .font(.headline)

            Text(message)
                .font(.body)
                .multilineTextAlignment(.center)

            <PERSON><PERSON>("OK") {
                onDismiss()
            }
            .padding()
            .frame(maxWidth: .infinity)
            .background(Color.blue)
            .foregroundColor(.white)
            .cornerRadius(8)
        }
        .padding()
        .background(Color.white)
        .cornerRadius(12)
        .shadow(radius: 10)
        .padding(40)
    }
}
