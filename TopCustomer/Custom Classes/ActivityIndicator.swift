
import Foundation
import UIKit
import Lottie

class ActivityIndicator {
    
    lazy internal var centralActivityIndicator: UIActivityIndicatorView = {
        let act = UIActivityIndicatorView(style: UIActivityIndicatorView.Style.large)
        act.color = UIColor.AppTheme_BlueColor_012CDA
        return act
    }()
    
    let view = AppDel?.window   //.rootViewController?.view
    
    static let shared = ActivityIndicator()
    var loadingView = AnimationView(frame: CGRect(x: 0, y: 0, width: 250 , height: 250))
    
    func showCentralSpinner() {
        view?.addSubview(centralActivityIndicator)
        let xConstraint = NSLayoutConstraint(item: centralActivityIndicator, attribute: NSLayoutConstraint.Attribute.centerX, relatedBy: NSLayoutConstraint.Relation.equal, toItem: view, attribute: NSLayoutConstraint.Attribute.centerX, multiplier: 1, constant: 0)
        let yConstraint = NSLayoutConstraint(item: centralActivityIndicator, attribute: NSLayoutConstraint.Attribute.centerY, relatedBy: NSLayoutConstraint.Relation.equal, toItem: view, attribute: NSLayoutConstraint.Attribute.centerY, multiplier: 1, constant: 0)
        let hei = NSLayoutConstraint(item: centralActivityIndicator, attribute: NSLayoutConstraint.Attribute.height, relatedBy: NSLayoutConstraint.Relation.equal, toItem: nil, attribute: NSLayoutConstraint.Attribute.height, multiplier: 1, constant: 40)
        let wid = NSLayoutConstraint(item: centralActivityIndicator, attribute: NSLayoutConstraint.Attribute.width, relatedBy: NSLayoutConstraint.Relation.equal, toItem: nil, attribute: NSLayoutConstraint.Attribute.width, multiplier: 1, constant: 40)
        centralActivityIndicator.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([xConstraint, yConstraint, hei, wid])
        centralActivityIndicator.alpha = 0.0
        AppDel?.window?.rootViewController?.view.layoutIfNeeded()
        AppDel?.window?.rootViewController?.view.isUserInteractionEnabled = false
        AppDel?.window?.isUserInteractionEnabled = false
        centralActivityIndicator.startAnimating()
        UIView.animate(withDuration: 0.2) { () -> Void in
            self.centralActivityIndicator.alpha = 1.0
        }
//        self.loadingAnimation()
    }
    
    func hideCentralSpinner() {
        ez.runThisInMainThread {
            self.centralActivityIndicator.removeFromSuperview()
            self.view?.isUserInteractionEnabled = true
            AppDel?.window?.isUserInteractionEnabled = true
            AppDel?.window?.rootViewController?.view.isUserInteractionEnabled = true
            self.centralActivityIndicator.stopAnimating()
            UIView.animate(withDuration: 0.2) { () -> Void in
                self.centralActivityIndicator.alpha = 0.0
            }
//            self.removeLoadingAnimation()
        }
       
    }
    
    private func loadingAnimation() {
        self.loadingView.center = self.view?.center ?? CGPoint(x: 0, y: 0)
        self.loadingView.contentMode = .scaleAspectFill
        self.loadingView.loopMode = .loop
        let path = Bundle.main.path(forResource: "loading_shape",
                                    ofType: "json") ?? ""
        self.loadingView.animation = Animation.filepath(path)
        self.loadingView.play()
        self.view?.addSubview(loadingView)
    }
    
    private func removeLoadingAnimation() {
        self.loadingView.removeFromSuperview()
    }
    
}
