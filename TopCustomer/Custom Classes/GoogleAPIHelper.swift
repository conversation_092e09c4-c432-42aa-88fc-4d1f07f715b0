
import Foundation
import CoreLocation
import Alamofire

class GoogleAPIHelper
{
    static let shared:GoogleAPIHelper! = GoogleAPIHelper()
    
    func getPolylineRoute(
        from source: CLLocationCoordinate2D,
        to destination: CLLocationCoordinate2D,
        waypointsString: CLLocationCoordinate2D?,
        completionHandler: @escaping (_ success: Bool, _ points:String?, _ duration:String?, _ distance:String?,_ googleModel: GoogleDirectionDetailsRootClass?, _ strSeconds:Int?) -> Void)
    {
        let origionString = "origin=\(source.latitude),\(source.longitude)"
        let destinationString = "destination=\(destination.latitude),\(destination.longitude)"
        
        var urlString = ""
        if let pointsWayString = waypointsString {
            urlString = "https://maps.googleapis.com/maps/api/directions/json?\(origionString)&\(destinationString)&waypoints=via:\(pointsWayString.latitude),\(pointsWayString.longitude)&sensor=true&mode=driving&key=\(Constant.GOOGLE_API_KEY)&units=imperial"
        }else{
            urlString = "https://maps.googleapis.com/maps/api/directions/json?\(origionString)&\(destinationString)&sensor=true&mode=driving&key=\(Constant.GOOGLE_API_KEY)&units=imperial"
        }
        
        let url = URL(string: urlString)!
        print(url)
        self.getPolylineRoute(url: url, completionHandler: completionHandler)
        
    }
    
    func getPolylineRoute(url: URL,
                          completionHandler: @escaping (_ success: Bool, _ points:String?, _ duration:String?, _ distance:String?,_ googleModel: GoogleDirectionDetailsRootClass?, _ strSeconds:Int?) -> Void)
    {
        let config = URLSessionConfiguration.default
        let session = URLSession(configuration: config)
        
        print("url := \(url)")
        
        let task = session.dataTask(with: url, completionHandler: {
            (data, response, error) in
            if error != nil {
                print(error!.localizedDescription)
                completionHandler(false,"", "", "",nil, 0)
            }
            else {
                do {
                    if let json : [String:Any] = try JSONSerialization.jsonObject(with: data!, options: []) as? [String: Any]{
                        
                        guard let routes = json["routes"] as? [[String:Any]] else {
                            DispatchQueue.main.async {
                                completionHandler(false,"", "", "",nil, 0)
                            }
                            return
                        }
                        if (routes.count > 0) {
                            DispatchQueue.main.async {
                                let overview_polyline = routes[0]
                                if let dictPolyline = overview_polyline["overview_polyline"] as? [String:Any] {
                                    let points = dictPolyline["points"] as? String
                                    // find time & distance
                                    var strDuration = ""
                                    var strDistance = ""
                                    var strSeconds = 0
                                    if let dictLegs = overview_polyline["legs"] as? [[String:Any]] {
                                        if let distance = dictLegs[0]["distance"] as? [String:Any] {
                                            strDistance = (distance["text"] as? String)!
                                        }
                                        if let duration = dictLegs[0]["duration"] as? [String:Any] {
                                            strDuration = (duration["text"] as? String)!
                                            strSeconds = (duration["value"] as? Int)!
                                        }
                                    }
                                    let model = GoogleDirectionDetailsRootClass(fromDictionary: json)
                                    completionHandler(true,points, strDuration, strDistance,model, strSeconds)
                                }
                                completionHandler(false,"", "", "",nil, 0)
                            }
                        }
                        else {
                            DispatchQueue.main.async {
                                completionHandler(false,"", "", "",nil, 0)
                            }
                        }
                    }
                }
                catch {
                    print("error in JSONSerialization")
                    DispatchQueue.main.async {
                        completionHandler(false,"", "", "",nil, 0)
                    }
                }
            }
        })
        task.resume()
    }
    
    func getDistanceMatrix(
        from source: CLLocationCoordinate2D,
        to destination: CLLocationCoordinate2D,
        waypointsString: CLLocationCoordinate2D?,
        completionHandler: @escaping (_ success: Bool, _ distanceMeter:Int?, _ distance:String?,_ googleModel: GoogleDistanceMetrixRootClass?) -> Void)
    {
        let origionString = "origins=\(source.latitude),\(source.longitude)"
        let destinationString = "destinations=\(destination.latitude),\(destination.longitude)"
        
        var urlString = ""
        if let pointsWayString = waypointsString {
            urlString = "https://maps.googleapis.com/maps/api/distancematrix/json?\(origionString)&\(destinationString)&waypoints=via:\(pointsWayString.latitude),\(pointsWayString.longitude)&sensor=true&mode=driving&key=\(Constant.GOOGLE_API_KEY)&units=imperial"
        }else{
            urlString = "https://maps.googleapis.com/maps/api/distancematrix/json?\(origionString)&\(destinationString)&sensor=true&mode=driving&key=\(Constant.GOOGLE_API_KEY)&units=imperial"
        }
        
        let url = URL(string: urlString)!
        print(url)
        self.getDistanceMatrixDetails(url: url, completionHandler: completionHandler)
        
    }
    
    func getDistanceMatrixDetails(url: URL,
                          completionHandler: @escaping (_ success: Bool, _ distanceMeter:Int?, _ distance:String?,_ googleModel: GoogleDistanceMetrixRootClass?) -> Void)
    {
      let config = URLSessionConfiguration.default
      let session = URLSession(configuration: config)
     
      print("url := \(url)")
      
      let task = session.dataTask(with: url, completionHandler: {
        (data, response, error) in
        if error != nil {
          print(error!.localizedDescription)
          completionHandler(false,nil, "",nil)
        }
        else {
          do {
            if let json : [String:Any] = try JSONSerialization.jsonObject(with: data!, options: []) as? [String: Any]{
              
              guard let routes = json["rows"] as? [[String:Any]] else {
                DispatchQueue.main.async {
                  completionHandler(false,nil, "",nil)
                }
                return
              }
              if (routes.count > 0) {
                DispatchQueue.main.async {
                  let model = GoogleDistanceMetrixRootClass(fromDictionary: json)
                  let strDistance = model.rows.first?.elements.first?.distance?.text ?? ""
                  let strDistanceValueinMeters = model.rows.first?.elements.first?.distance?.value
                  completionHandler(true, strDistanceValueinMeters,strDistance,model)
                }
              }
              else {
                DispatchQueue.main.async {
                  completionHandler(false,nil, "",nil)
                }
              }
            }
          }
          catch {
            print("error in JSONSerialization")
            DispatchQueue.main.async {
              completionHandler(false,nil, "",nil)
            }
          }
        }
      })
      task.resume()
    }
    
}
