
import UIKit
import Reachability
import Alamofire
import AudioToolbox

typealias ShowAlertOkHandler = () -> ()

class BaseViewController: UIViewController {
    var isDone = true
    var networkManager: NetworkReachabilityManager = NetworkReachabilityManager()!
    private let refreshControl = UIRefreshControl()

    override func viewDidLoad() {
        super.viewDidLoad()
        self.setLeftButton()
        self.setFontStyle()
        refreshControl.tintColor = .AppTheme_BlueColor_012CDA

        // Mixpanel
        let strVc = String(describing: type(of: self))
        self.setMixpanelEvent(strVc: strVc, isStart: true)
        
        self.navigationController?.interactivePopGestureRecognizer?.delegate = self as? UIGestureRecognizerDelegate
        self.navigationController?.interactivePopGestureRecognizer?.isEnabled = true

    }
    
    func setMixpanelEvent(strVc: String, isStart: Bool) {
        var strScreenName = ""
        
        switch strVc  {
        case "LoginViewController":
            strScreenName = ScreenNames.LoginViewController.rawValue
        case "PrivacyPolicy":
            strScreenName = ScreenNames.PrivacyPolicyScreen.rawValue
        case "Terms":
            strScreenName = ScreenNames.TermsAndConditionScreen.rawValue
        case "OTPVerificationViewController":
            strScreenName = ScreenNames.OTPScreen.rawValue
        case "FilterSideMenuViewController":
            strScreenName = ScreenNames.SideMenuScreen.rawValue
        case "NewHomeViewController":
            strScreenName = ScreenNames.HomeViewController.rawValue
        case "ProductPopupViewController":
            strScreenName = ScreenNames.ProductDetailScreen.rawValue
        case "SelectLocationViewController":
            strScreenName = ScreenNames.AddressListingScreen.rawValue
        case "AddAddressViewController":
            strScreenName = ScreenNames.AddAddressScreen.rawValue
        case "OffersViewController":
            strScreenName = ScreenNames.OffersScreen.rawValue
        case "OfferDetailsViewController":
            strScreenName = ScreenNames.OffersDetailsScreen.rawValue
        case "CurrentOrdersViewController":
            strScreenName = ScreenNames.MyOrdersCurrent.rawValue
        case "ScheduledOrdersViewController":
            strScreenName = ScreenNames.MyOrdersScheduled.rawValue
        case "OrderDetailViewController":
            strScreenName = ScreenNames.CurrentOrderDetailScreen.rawValue
        case "TrackOrderViewController":
            strScreenName = ScreenNames.TrackOrderScreen.rawValue
        case "ScheduledOrderDetailViewController":
            strScreenName = ScreenNames.ScheduledOrderDetailScreen.rawValue
        case "SettingsViewController":
            strScreenName = ScreenNames.SettingsScreen.rawValue
        case "MyCartViewController":
            strScreenName = ScreenNames.MyCartScreen.rawValue
        case "CheckoutViewController":
            strScreenName = ScreenNames.ReviewOrderScreen.rawValue
        case "ChoosePaymentViewController":
            strScreenName = ScreenNames.ChoosePaymentScreen.rawValue
        case "AccountViewController":
            strScreenName = ScreenNames.AccountScreen.rawValue
        case "HistoryOrdersViewController":
            strScreenName = ScreenNames.HistoryOrdersViewController.rawValue
        case "HistoryOrderDetailsViewController":
            strScreenName = ScreenNames.HistoryOrderDetailScreen.rawValue
        case "HelpViewController":
            strScreenName = ScreenNames.HelpScreen.rawValue
        case "PaymentsViewController":
            strScreenName = ScreenNames.PaymentsScreen.rawValue
        case "ContactUsViewController":
            strScreenName = ScreenNames.ContactUsScreen.rawValue
        case "SelectLanguageViewController":
            strScreenName = ScreenNames.LanguageSelectionScreen.rawValue
        case "BannerInfoViewController":
            strScreenName = ScreenNames.BannerInfoScreen.rawValue
        case "AdvertisementPopupViewController":
            strScreenName = ScreenNames.AdvertisementPopupScreen.rawValue
        case "RatingViewController":
            strScreenName = ScreenNames.RatingPopup.rawValue
        case "FavoriteViewController":
            strScreenName = ScreenNames.FavoriteScreen.rawValue
        default:
            break
        }
        
        print(strScreenName)
        
        if !strScreenName.isEmpty {
            if isStart == true {  // start event
                MixpanelEvents.sharedInstance.logTimeEvent(strScreenName: strScreenName)
            }
            else {  // end event
                MixpanelEvents.sharedInstance.trackTimeEvent(strScreenName: strScreenName)
            }
        }
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
    }
    
    override func viewDidDisappear(_ animated: Bool) {
        super.viewDidDisappear(animated)
        let strVc = String(describing: type(of: self))
        self.setMixpanelEvent(strVc: strVc, isStart: false)
    }

    func setFontStyle() {
        self.navigationController?.navigationBar.titleTextAttributes =
            [NSAttributedString.Key.foregroundColor: UIColor.AppTheme_BlueColor_012CDA,
             NSAttributedString.Key.font: UIFont(name: Fonts.LoewNextArabicBold, size: 23)!]
    }
    
    func setLeftButton() {
        let backImage = UIImage(named: "icn_back")?.withRenderingMode(.alwaysOriginal)
        let newBtn = UIBarButtonItem(image: backImage, style: .plain, target: self, action: #selector(actionLeftButton))
        self.navigationItem.leftBarButtonItem = newBtn
    }
    
    @objc func actionLeftButton() {
        self.view.endEditing(true)
        self.navigationController?.popViewController(animated: true)
    }
        
    func isInternetAvailable() -> Bool {
        if networkManager.isReachable{
            return true
        }else{
            return false
        }
    }
            
    func showFullScreenPopup(vc : UIViewController, animated : Bool = true) {
        vc.modalPresentationStyle = .fullScreen
        vc.modalPresentationStyle = .overCurrentContext
        vc.modalTransitionStyle = .crossDissolve
        AppDel!.window?.rootViewController?.present(vc, animated: true, completion: nil)
    }
       
    func setupRefreshControlInTableView(tableView : UITableView){
        refreshControl.addTarget(self, action: #selector(didPullToRefresh(_:)), for: .valueChanged)
        tableView.refreshControl = refreshControl
    }

    func setupRefreshControlInScrollView(scrollView : UIScrollView){
        refreshControl.addTarget(self, action: #selector(didPullToRefresh(_:)), for: .valueChanged)
        scrollView.refreshControl = refreshControl
    }

    @objc func didPullToRefresh(_ sender: UIRefreshControl) {
        ez.runThisAfterDelay(seconds: 1.0) {
            sender.endRefreshing()
        }
    }
    
    func startAnimating(){
        refreshControl.beginRefreshing()
    }
    
    func endRefresing(){
        refreshControl.endRefreshing()
    }
    
    func startLoading(){
        refreshControl.beginRefreshing()
    }
    
    func stopLoading(){
        refreshControl.endRefreshing()
    }

    func vibrationDevice() {
        let generator = UIImpactFeedbackGenerator(style: .heavy)
        generator.impactOccurred()
    }
    
    func showAlertPopup(message: String,
                        completion: @escaping ShowAlertOkHandler) {
        let alert = UIAlertController(title: nil, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "Ok".localized(), style: .default, handler: { action in
            completion()
        }))
        self.present(alert, animated: true)
    }
    
    func showAlertYesNoPopup(message: String,
                             yesCompletion: @escaping ShowAlertOkHandler) {
        let alert = UIAlertController(title: nil, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "Yes".localized(), style: .default, handler: { action in
            yesCompletion()
        }))
        alert.addAction(UIAlertAction(title: "No".localized(), style: .cancel, handler: { action in

        }))
        self.present(alert, animated: true)
    }
    
    func showAlertYesNoPopup(message: String,
                             yesTitle: String,
                             noTitle: String,
                             yesCompletion: @escaping ShowAlertOkHandler,
                             noCompletion: @escaping ShowAlertOkHandler) {
        let alert = UIAlertController(title: nil, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: yesTitle, style: .default, handler: { action in
            yesCompletion()
        }))
        alert.addAction(UIAlertAction(title: noTitle, style: .cancel, handler: { action in
            noCompletion()
        }))
        self.present(alert, animated: true)
    }
    
    func showAlert(message: String) {
        let alert = UIAlertController(title: nil, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "Ok".localized, style: .default, handler: nil))
        self.present(alert, animated: true)
    }
    
}
