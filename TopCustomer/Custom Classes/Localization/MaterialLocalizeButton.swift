
import Foundation
import UIKit

open class MaterialLocalizeButton : UIButton {
    
    override open func awakeFromNib() {
        super.awakeFromNib()
        self.updateLayout()
    }
    
    override open func layoutSubviews() {
        super.layoutSubviews()
    }
    
    func updateLayout(){
        let strLangCode = UserDefaults.standard.getLanguage()!
        if strLangCode == UserAPI.VLanguage_userLanguage.en.rawValue {
        }
        else {
            self.imageView?.transform = CGAffineTransform(scaleX: -1.0, y: 1.0)
            
            if self.contentHorizontalAlignment == .center{
            }
            else if self.contentHorizontalAlignment == .right || self.contentHorizontalAlignment == .left{
                self.contentHorizontalAlignment = self.contentHorizontalAlignment == .right ? .left : .right
            }
        }
    }
}

