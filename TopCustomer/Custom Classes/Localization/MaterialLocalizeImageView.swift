
import Foundation
import UIKit

class MaterialLocalizeImageView : UIImageView {
    override func awakeFromNib() {
        super.awakeFromNib()
    }
    
    override func layoutSubviews() {
        super.layoutSubviews()
        
        let strLangCode = UserDefaults.standard.getLanguage()!
        if strLangCode == UserAPI.VLanguage_userLanguage.en.rawValue {
        }
        else {
            self.image = self.image?.withHorizontallyFlippedOrientation()
        }
    }
    
}


