
import Foundation
import UIKit

class MaterialLocalizeLable : UILabel {
    
    override func awakeFromNib() {
        super.awakeFromNib()
        if text != nil {
            let strLangCode = UserDefaults.standard.getLanguage()!
            if strLangCode == UserAPI.VLanguage_userLanguage.en.rawValue {
            }
            else {
                if self.textAlignment == .center{
                }
                else if self.textAlignment == .right || self.textAlignment == .left{
                    self.textAlignment = self.textAlignment == .right ? .left : .right
                }
            }
        }
    }
}



