
import Foundation
import UIKit

class MaterialLocalizeTextfield : UITextField {
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        self.setup()
    }
    
    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
        self.setup()
    }

    private func setup() {
        // localisation
        let strLangCode = UserDefaults.standard.getLanguage()!
        if strLangCode == UserAPI.VLanguage_userLanguage.en.rawValue {
        }
        else {
            if self.textAlignment == NSTextAlignment.center {
            }
            else if self.textAlignment == NSTextAlignment.right || self.textAlignment == NSTextAlignment.left{
                self.textAlignment = self.textAlignment == NSTextAlignment.right ? NSTextAlignment.left : NSTextAlignment.right
            }
        }
    }

}


