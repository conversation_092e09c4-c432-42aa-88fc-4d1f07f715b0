//
//  AppsFlyerEvents.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 09/10/2024.
//  Copyright © 2024 SOTSYS203. All rights reserved.
//
import Foundation
import AppsFlyerLib
import FBSDKCoreKit
import TikTokBusinessSDK

class AppsFlyerEvents {
    static var shared = AppsFlyerEvents()
    
    func afLogin(phone: String, strCountryCode: String) {
        AppsFlyerLib.shared().logEvent(name: "af_login", values: ["phone": phone, "strCountryCode": strCountryCode])
        // tiktok event login
        let loginEvent = TikTokEvents(name: "Login", properties: ["phone": phone, "strCountryCode": strCountryCode])
        loginEvent.sendEvent()
    }
    
    func afCompleteRegistration(phone: String, strCountryCode: String) {
        AppsFlyerLib.shared().logEvent(name: "af_complete_registration", values: ["phone": phone, "strCountryCode": strCountryCode])
        // tiktok event Registration
        let registrationEvent = TikTokEvents(name: "Registration", properties: ["phone": phone, "strCountryCode": strCountryCode])
        registrationEvent.sendEvent()
    }
    
    func afSearch(searchValue: String) {
        AppsFlyerLib.shared().logEvent(name: "af_search", values: ["af_search_string": searchValue])
        AppEvents.shared.logEvent(.searched, parameters: [.searchString : searchValue])
        // tiktok event Search
        let searchEvent = TikTokEvents(name: "Search", properties: ["name" : searchValue])
        searchEvent.sendEvent()
    }
    
    
    func afContentView(price: Double,
                       content: String,
                       contentId: Int,
                       contentType: String,
                       currency: String = "SAR") {
        AppsFlyerLib.shared().logEvent(name: "af_content_view", values: ["af_price": price,
                                                                         "af_content": content,
                                                                         "af_content_id": contentId,
                                                                         "af_content_type": contentType,
                                                                         "af_currency": currency])
        AppEvents.shared.logEvent(.viewedContent, valueToSum: price, parameters: [.contentID : contentId,
                                                                                  .contentType: contentType,
                                                                                  .currency: currency,
        ])
        // tiktok event ViewContent
        let viewContentEvent = TikTokEvents(name: "ViewContent", properties: ["price": price,
                                                                              "content": content,
                                                                              "name": content,
                                                                              "content_id": contentId,
                                                                              "content_type": contentType,
                                                                              "currency": currency])
        viewContentEvent.sendEvent()
    }
    
    func afAddToWishlist(price: Double,
                         content: String,
                         contentId: Int,
                         contentType: String,
                         currency: String = "SAR") {
        
        TikTokBusiness.trackEvent("add_to_wishlist", withProperties: ["af_price": price,
                                                                      "af_content": content,
                                                                      "af_content_id": contentId,
                                                                      "af_content_type": contentType,
                                                                      "af_currency": currency])
        AppsFlyerLib.shared().logEvent(name: "af_add_to_wishlist", values: ["af_price": price,
                                                                            "af_content": content,
                                                                            "af_content_id": contentId,
                                                                            "af_content_type": contentType,
                                                                            "af_currency": currency])
        AppEvents.shared.logEvent(.addedToWishlist, valueToSum: price, parameters: [.contentID : contentId,
                                                                                    .contentType: contentType,
                                                                                    .currency: currency,
        ])
        // tiktok event AddToWishlist
        let addToWishlistEvent = TikTokEvents(name: "AddToWishlist", properties: ["price": price,
                                                                                  "content": content,
                                                                                  "name": content,
                                                                                  "content_id": contentId,
                                                                                  "content_type": contentType,
                                                                                  "currency": currency])
        addToWishlistEvent.sendEvent()
    }
    
    func afAddToCart(price: Double,
                     content: String,
                     contentId: Int,
                     contentType: String,
                     quantity: Int,
                     currency: String = "SAR") {
        TikTokBusiness.trackEvent("add_to_cart", withProperties: ["af_price": price,
                                                                  "af_content": content,
                                                                  "af_content_id": contentId,
                                                                  "af_content_type": contentType,
                                                                  "af_currency": currency,
                                                                  "af_quantity": quantity])
        AppsFlyerLib.shared().logEvent(name: "af_add_to_cart", values: ["af_price": price,
                                                                        "af_content": content,
                                                                        "af_content_id": contentId,
                                                                        "af_content_type": contentType,
                                                                        "af_currency": currency,
                                                                        "af_quantity": quantity])
        AppEvents.shared.logEvent(.addedToCart, valueToSum: price, parameters: [.contentID : contentId,
                                                                                .contentType: contentType,
                                                                                .currency: currency,
        ])
        // tiktok event AddToCart
        let addToCartEvent = TikTokEvents(name: "AddToCart", properties: ["price": price,
                                                                          "content": content,
                                                                          "name": content,
                                                                          "content_id": contentId,
                                                                          "content_type": contentType,
                                                                          "currency": currency])
        addToCartEvent.sendEvent()
        // firebase event add to cart
        let addToCartEventFirebase = FirebaseEvents(name: "add_to_cart", properties: ["value": price,
                                                                                      "currency": currency])
        addToCartEventFirebase.sendEvent()
    }
    
    func afPurchase(price: Double,
                    content: String,
                    contentId: Int,
                    contentType: String,
                    quantity: Int,
                    orderId: Int,
                    receiptId: Int,
                    currency: String = "SAR") {
        AppsFlyerLib.shared().logEvent(name: "af_purchase", values: ["af_price": price,
                                                                        "af_content": content,
                                                                        "af_content_id": contentId,
                                                                        "af_content_type": contentType,
                                                                        "af_currency": currency,
                                                                        "af_order_id": orderId,
                                                                        "af_receipt_id": receiptId])
        AppEvents.shared.logEvent(.purchased, valueToSum: price, parameters: [.contentID : contentId,
                                                                              .currency: currency,
        ])
        // tiktok event Checkout
        let purchaseEvent = TikTokEvents(name: "Purchase", properties: ["value": "\(price)",
                                                                        "currency": currency])
        purchaseEvent.sendEvent()
        // firebase event purchase
        let purchaseEventFirebase = FirebaseEvents(name: "purchase", properties: ["value": price,
                                                                                  "currency": currency])
        purchaseEventFirebase.sendEvent()
        
    }
    
    func afInitiatedCheckout(price: Double,
                             content: String,
                             contentId: Int,
                             contentType: String,
                             quantity: Int,
                             orderId: Int,
                             receiptId: Int,
                             currency: String = "SAR") {
        AppEvents.shared.logEvent(.initiatedCheckout, valueToSum: price, parameters: [.contentID : contentId,
                                                                                      .currency: currency,
        ])
        // tiktok event Checkout
        let checkoutEvent = TikTokEvents(name: "Checkout", properties: ["value": "\(price)",
                                                                        "currency": currency])
        checkoutEvent.sendEvent()
    }
    
    func afRemoveFromCart(contentId: Int, contentType: String) {
        AppsFlyerLib.shared().logEvent(name: "remove_from_cart", values: ["af_content_id": contentId, "af_content_type": contentType])
    }
    
}
