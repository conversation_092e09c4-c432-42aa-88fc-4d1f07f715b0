//
//  MainButtonDesignable.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 21/02/2024.
//  Copyright © 2024 SOTSYS203. All rights reserved.

import UIKit

@IBDesignable
class MainButtonDesignable: UIButton {
    @IBInspectable var fontSize: CGFloat = 10 {
        didSet {
            setupButton()
        }
    }
    @IBInspectable var isMedium: Bool = true {
        didSet {
            setupButton()
        }
    }
    
    override func awakeFromNib() {
        super.awakeFromNib()
        setupButton()
    }
    override func prepareForInterfaceBuilder() {
        setupButton()
    }
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupButton()
    }
    
    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
        setupButton()
    }
    
    func setupButton() {
        self.layer.cornerRadius = 7
        self.layer.borderWidth = 1.0
        self.layer.borderColor = UIColor.AppTheme_BlueColor_012CDA.cgColor
        self.layer.masksToBounds = true
        self.backgroundColor = UIColor.AppTheme_BlueColor_012CDA
        self.setTitleColor(.white, for: .normal)
        if isMedium {
            self.setTitleColor(.white, for: .normal)
            self.titleLabel?.font = UIFont(name: Fonts.LoewNextArabicBold, size: fontSize)
        }else{
            self.setTitleColor(.white, for: .normal)
            self.titleLabel?.font = UIFont(name: Fonts.LoewNextArabicBold, size: fontSize)
        }
    }
}
