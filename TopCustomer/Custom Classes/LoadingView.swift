
import UIKit
import Lottie

enum LoaderType : String {
    case loading = "LoaderAnimation"
}

typealias commonDialogueClosure = (_ commonDialog:LoadingView,_ btnType: UIButton) -> ()

class LoadingView: UIView {
    
    
    var overlayView = UIView()
    var activityIndicator = UIActivityIndicatorView()
    var bgView = UIView()
    
    var viewClosure:commonDialogueClosure?
    static let sharedInstance : LoadingView = LoadingView()
    var lottieAnimationView = AnimationView(name: LoaderType.loading.rawValue)
    var lottieAnimationView1 = AnimationView(name: LoaderType.loading.rawValue)
    var lottieAnimationView2 = AnimationView(name: LoaderType.loading.rawValue)

    
    public func showOverlay(frame: CGRect = UIScreen.main.bounds) {
        self.hideOverlayView()
        bgView.frame = frame
        bgView.backgroundColor = UIColor.black.withAlphaComponent(0.4)
        bgView.addSubview(overlayView)
        bgView.autoresizingMask = [.flexibleLeftMargin,.flexibleTopMargin,.flexibleRightMargin,.flexibleBottomMargin,.flexibleHeight, .flexibleWidth]
        overlayView.frame = CGRect(x: 0, y: 0, width: 80, height: 80)
        overlayView.center = bgView.center
        overlayView.autoresizingMask = [.flexibleLeftMargin,.flexibleTopMargin,.flexibleRightMargin,.flexibleBottomMargin]
        overlayView.backgroundColor = UIColor.white
        overlayView.clipsToBounds = true
        overlayView.layer.cornerRadius = 10
        
        activityIndicator.frame = CGRect(x: 0, y: 0, width: 40, height: 40)
        activityIndicator.style = UIActivityIndicatorView.Style.large
        activityIndicator.color = UIColor.red
        activityIndicator.center = CGPoint(x: overlayView.bounds.width / 2, y: overlayView.bounds.height / 2)
        
        overlayView.addSubview(activityIndicator)
        if let _lastWindow = UIApplication.shared.keyWindow {
            _lastWindow.addSubview(bgView)
        }
        self.activityIndicator.startAnimating()
        
    }
    
    public func hideOverlayView() {
        activityIndicator.stopAnimating()
        bgView.removeFromSuperview()
    }
        
}

enum FadeType {
    case fadeIn
    case fadeOut
}

extension UIView {
    
    func addFadeAnimationWithFadeType(_ fadeType: FadeType) {
        
        switch fadeType {
        case .fadeIn:
            
            DispatchQueue.main.async {
                self.alpha = 0.0
                UIView.animate(withDuration: 0.1, animations: { () -> Void in
                    self.alpha = 1.0
                })
            }
            
        case .fadeOut:
            
            UIView.animate(withDuration: 0.1, animations: { () -> Void in
                DispatchQueue.main.async {
                    self.alpha = 0.0
                }
            }, completion: { (finished) -> Void in
                if finished {
                    self.removeFromSuperview()
                }
            })
        }
    }
    
    static func loadFromNib() -> Self {
        func instantiateFromNib<T: UIView>() -> T {
            Bundle.main.loadNibNamed(String(describing: T.self), owner: nil, options: nil)![0] as! T
        }
        return instantiateFromNib()
    }
}
