
import UIKit
import Mixpanel

class MixpanelEvents: NSObject {

    class var sharedInstance : MixpanelEvents {
        struct Static {
            static let instance : MixpanelEvents = MixpanelEvents()
        }
        return Static.instance
    }

    func initializeMixpanel() {
        let tmp = Mixpanel.initialize(token: MixpanelToken, trackAutomaticEvents: true)
        tmp.identify(distinctId: "\(User.shared.iUserId ?? 0)")
        
        tmp.people.set(properties: [
            "$name": User.shared.vName,
            "$email": User.shared.vEmailId,
            "mobile_number": User.shared.vMobileNumber,
            "installed_from": User.shared.vReferalPlatform
        ])
        
        tmp.registerSuperProperties([
            "user_id": "\(User.shared.iUserId ?? 0)",
            "mobile_number": User.shared.vMobileNumber,
            "user_name": User.shared.vName,
            "installed_from": User.shared.vReferalPlatform
        ])
    }
    
    func logTimeEvent(strScreenName: String) {
        Mixpanel.mainInstance().time(event: strScreenName)
    }
    
    func trackTimeEvent(strScreenName: String) {
        Mixpanel.mainInstance().track(event: strScreenName)
    }

}
