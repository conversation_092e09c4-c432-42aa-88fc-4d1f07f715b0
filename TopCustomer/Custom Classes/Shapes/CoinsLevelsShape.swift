//
//  CoinsLevelsShape.swift
//  TopCustomer
//
//  Created by <PERSON><PERSON> on 23/11/2023.
//  Copyright © 2023 SOTSYS203. All rights reserved.
//

import Foundation
import Lottie

typealias levelCompletionHandler = (String, Bool) -> ()
typealias totalCoinsCompletionHandler = (Int) -> ()

class CoinsLevelsShape {
    // MARK: - Variables
    static public var shared = CoinsLevelsShape()
    let userDefaults = UserDefaults.standard
    var fileName = ""
    var isGetNextLevel = false
    
    // MARK: - Func
    func getPointsLevels(isCongratsRewardsVC: Bool = false, withCompletionHandler completionHandler: @escaping levelCompletionHandler) {
        self.getTotalUserPoints() { totalCoins in
            UserAPI.getPointsLevels() { data, error in
                guard let data = data?.responseData?.levels else { return }
                
                if self.userDefaults.currentLevelCoins != data.currentLevel ?? 1 && isCongratsRewardsVC == true {
                    self.isGetNextLevel = true
                    self.userDefaults.currentLevelCoins = data.currentLevel ?? 1
                } else {
                    self.isGetNextLevel = false
                    self.userDefaults.currentLevelCoins = data.currentLevel ?? 1
                    Constant.shared.REWARD_POINT_LEVEL = data.rewardPoint ?? 0
                }
                let userPoint = totalCoins
                let maxLevel = data.maxLevel ?? 0
                let remainingPoint = data.remainingPoint ?? 0
                let levelPoint = data.levelPoint ?? 0
                let maxPoint = data.maxPoint ?? 0
                
                Constant.shared.POINTS_MAX_LEVELS = maxLevel
                Constant.shared.REMAINING_POINT_LEVEL = remainingPoint
                Constant.shared.LEVEL_POINT = levelPoint
                Constant.shared.CURRENT_POINTS_VALUE = "\(userPoint)"
                
                if isCongratsRewardsVC {
                    if userPoint >= maxLevel {
                        completionHandler("\("main")\(levelPoint + 2)", true)
                        return
                    }
                }
                
                switch levelPoint {
                case 1:
                    if userPoint == 0 {
                        self.fileName = "main1"
                    } else if userPoint < maxPoint {
                        self.fileName = "middle1"
                    } else {
                        self.fileName = "main2"
                    }
                case 2:
                    if userPoint < maxPoint {
                        self.fileName = "middle2"
                    } else {
                        self.fileName = "main3"
                    }
                case 3:
                    if userPoint < maxPoint {
                        self.fileName = "middle3"
                    } else {
                        self.fileName = "main4"
                    }
                case 4:
                    if userPoint < maxPoint {
                        self.fileName = "middle4"
                    } else {
                        self.fileName = "main5"
                    }
                default:
                    self.fileName = "main1"
                }
                completionHandler(self.fileName, self.isGetNextLevel)
            }
        }
    }
    
    func getTotalUserPoints(withCompletionHandler completionHandler: @escaping totalCoinsCompletionHandler) {
        UserAPI.calculateUserPointsToSAR(points: 0) { data, error in
            guard let totalUserPoints = data?.totalUserPoints else {
                completionHandler(0)
                return
            }
            completionHandler(totalUserPoints)
        }
    }
    
    func setlottieFileAnaimation(viewCoinsAnimation: AnimationView? = nil) {
        Constant.shared.FILE_NAME = self.fileName
        viewCoinsAnimation?.stop()
        let path = Bundle.main.path(forResource: fileName,
                                    ofType: "json") ?? ""
        viewCoinsAnimation?.animation = Animation.filepath(path)
        viewCoinsAnimation?.contentMode = .scaleAspectFit
        viewCoinsAnimation?.loopMode = .playOnce
        viewCoinsAnimation?.play()
    }
    
}
