PODS:
  - AACarousel (1.1.3)
  - ActionSheetPicker-3.0 (2.7.4)
  - Alamofire (4.9.1)
  - AlamofireImage (3.6.0):
    - Alamofire (~> 4.9)
  - AppsFlyerFramework (6.17.0):
    - AppsFlyerFramework/Main (= 6.17.0)
  - AppsFlyerFramework/Main (6.17.0)
  - Branch (1.45.2)
  - CarouselSwipeView (1.0.4):
    - Kingfisher
  - CocoaLumberjack (3.8.5):
    - CocoaLumberjack/Core (= 3.8.5)
  - CocoaLumberjack/Core (3.8.5)
  - Firebase/Analytics (11.13.0):
    - Firebase/Core
  - Firebase/Core (11.13.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 11.13.0)
  - Firebase/CoreOnly (11.13.0):
    - FirebaseCore (~> 11.13.0)
  - Firebase/Crashlytics (11.13.0):
    - Firebase/CoreOnly
    - FirebaseCrashlytics (~> 11.13.0)
  - Firebase/DynamicLinks (11.13.0):
    - Firebase/CoreOnly
    - FirebaseDynamicLinks (~> 11.13.0)
  - Firebase/Messaging (11.13.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 11.13.0)
  - FirebaseAnalytics (11.13.0):
    - FirebaseAnalytics/AdIdSupport (= 11.13.0)
    - FirebaseCore (~> 11.13.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/MethodSwizzler (~> 8.1)
    - GoogleUtilities/Network (~> 8.1)
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
    - nanopb (~> 3.30910.0)
  - FirebaseAnalytics/AdIdSupport (11.13.0):
    - FirebaseCore (~> 11.13.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleAppMeasurement (= 11.13.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/MethodSwizzler (~> 8.1)
    - GoogleUtilities/Network (~> 8.1)
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
    - nanopb (~> 3.30910.0)
  - FirebaseCore (11.13.0):
    - FirebaseCoreInternal (~> 11.13.0)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/Logger (~> 8.1)
  - FirebaseCoreExtension (11.13.0):
    - FirebaseCore (~> 11.13.0)
  - FirebaseCoreInternal (11.13.0):
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
  - FirebaseCrashlytics (11.13.0):
    - FirebaseCore (~> 11.13.0)
    - FirebaseInstallations (~> 11.0)
    - FirebaseRemoteConfigInterop (~> 11.0)
    - FirebaseSessions (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/Environment (~> 8.1)
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - FirebaseDynamicLinks (11.13.0):
    - FirebaseCore (~> 11.13.0)
  - FirebaseInstallations (11.13.0):
    - FirebaseCore (~> 11.13.0)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/UserDefaults (~> 8.1)
    - PromisesObjC (~> 2.4)
  - FirebaseMessaging (11.13.0):
    - FirebaseCore (~> 11.13.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/Reachability (~> 8.1)
    - GoogleUtilities/UserDefaults (~> 8.1)
    - nanopb (~> 3.30910.0)
  - FirebaseRemoteConfigInterop (11.13.0)
  - FirebaseSessions (11.13.0):
    - FirebaseCore (~> 11.13.0)
    - FirebaseCoreExtension (~> 11.13.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/UserDefaults (~> 8.1)
    - nanopb (~> 3.30910.0)
    - PromisesSwift (~> 2.1)
  - GoogleAppMeasurement (11.13.0):
    - GoogleAppMeasurement/AdIdSupport (= 11.13.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/MethodSwizzler (~> 8.1)
    - GoogleUtilities/Network (~> 8.1)
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/AdIdSupport (11.13.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 11.13.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/MethodSwizzler (~> 8.1)
    - GoogleUtilities/Network (~> 8.1)
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (11.13.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/MethodSwizzler (~> 8.1)
    - GoogleUtilities/Network (~> 8.1)
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
    - nanopb (~> 3.30910.0)
  - GoogleDataTransport (10.1.0):
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - GoogleMaps (8.4.0):
    - GoogleMaps/Maps (= 8.4.0)
  - GoogleMaps/Base (8.4.0)
  - GoogleMaps/Maps (8.4.0):
    - GoogleMaps/Base
  - GooglePlaces (8.5.0)
  - GoogleUtilities/AppDelegateSwizzler (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.1.0):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.1.0):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.1.0)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.1.0)
  - GoogleUtilities/Reachability (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - ImageSlideshow/Alamofire (1.9.5):
    - AlamofireImage
    - ImageSlideshow/Core
  - ImageSlideshow/Core (1.9.5)
  - IQKeyboardManagerSwift (6.5.16)
  - Kingfisher (8.3.2)
  - lottie-ios (3.4.4)
  - Mixpanel-swift (5.1.0):
    - Mixpanel-swift/Complete (= 5.1.0)
  - Mixpanel-swift/Complete (5.1.0)
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - PayTabsSDK (6.6.32)
  - PromisesObjC (2.4.0)
  - PromisesSwift (2.4.0):
    - PromisesObjC (= 2.4.0)
  - ReachabilitySwift (5.2.4)
  - Socket.IO-Client-Swift (15.2.0):
    - Starscream (~> 3.1)
  - Starscream (3.1.1)
  - STTabbar (0.1.0)
  - SVGKit (3.0.0):
    - CocoaLumberjack (~> 3.0)
  - SwiftyJSON (5.0.2)
  - TikTokBusinessSDK (1.4.0)
  - Toast-Swift (5.1.1)

DEPENDENCIES:
  - AACarousel (from `https://github.com/Mohamed-AbdulRaouf/AACarousel.git`, commit `01a14180700dc4c739ec173f5fa1f5d90fcba761`)
  - ActionSheetPicker-3.0
  - Alamofire (~> 4.9.1)
  - AppsFlyerFramework
  - Branch
  - CarouselSwipeView (from `https://github.com/Mohamed-AbdulRaouf/CarouselSwipeView.git`)
  - Firebase/Analytics
  - Firebase/Core
  - Firebase/Crashlytics
  - Firebase/DynamicLinks
  - Firebase/Messaging
  - FirebaseAnalytics
  - GoogleMaps (= 8.4.0)
  - GooglePlaces
  - ImageSlideshow/Alamofire (from `https://github.com/Mohamed-AbdulRaouf/ImageSlideshow.git`)
  - IQKeyboardManagerSwift (~> 6.5.12)
  - Kingfisher
  - lottie-ios (~> 3.4.3)
  - Mixpanel-swift
  - PayTabsSDK
  - ReachabilitySwift
  - Socket.IO-Client-Swift (~> 15.2.0)
  - STTabbar (from `https://github.com/Mohamed-AbdulRaouf/STTabbar.git`, commit `fc4999f8aa5ce095448cb7eb91c59389d70c2b0b`)
  - SVGKit
  - SwiftyJSON
  - TikTokBusinessSDK
  - Toast-Swift

SPEC REPOS:
  trunk:
    - ActionSheetPicker-3.0
    - Alamofire
    - AlamofireImage
    - AppsFlyerFramework
    - Branch
    - CocoaLumberjack
    - Firebase
    - FirebaseAnalytics
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseCrashlytics
    - FirebaseDynamicLinks
    - FirebaseInstallations
    - FirebaseMessaging
    - FirebaseRemoteConfigInterop
    - FirebaseSessions
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleMaps
    - GooglePlaces
    - GoogleUtilities
    - IQKeyboardManagerSwift
    - Kingfisher
    - lottie-ios
    - Mixpanel-swift
    - nanopb
    - PayTabsSDK
    - PromisesObjC
    - PromisesSwift
    - ReachabilitySwift
    - Socket.IO-Client-Swift
    - Starscream
    - SVGKit
    - SwiftyJSON
    - TikTokBusinessSDK
    - Toast-Swift

EXTERNAL SOURCES:
  AACarousel:
    :commit: 01a14180700dc4c739ec173f5fa1f5d90fcba761
    :git: https://github.com/Mohamed-AbdulRaouf/AACarousel.git
  CarouselSwipeView:
    :git: https://github.com/Mohamed-AbdulRaouf/CarouselSwipeView.git
  ImageSlideshow:
    :git: https://github.com/Mohamed-AbdulRaouf/ImageSlideshow.git
  STTabbar:
    :commit: fc4999f8aa5ce095448cb7eb91c59389d70c2b0b
    :git: https://github.com/Mohamed-AbdulRaouf/STTabbar.git

CHECKOUT OPTIONS:
  AACarousel:
    :commit: 01a14180700dc4c739ec173f5fa1f5d90fcba761
    :git: https://github.com/Mohamed-AbdulRaouf/AACarousel.git
  CarouselSwipeView:
    :commit: 759527432d533636d70998058a91e08ec747a514
    :git: https://github.com/Mohamed-AbdulRaouf/CarouselSwipeView.git
  ImageSlideshow:
    :commit: 789ff47811ac776024b8e5bed52a2936173425cb
    :git: https://github.com/Mohamed-AbdulRaouf/ImageSlideshow.git
  STTabbar:
    :commit: fc4999f8aa5ce095448cb7eb91c59389d70c2b0b
    :git: https://github.com/Mohamed-AbdulRaouf/STTabbar.git

SPEC CHECKSUMS:
  AACarousel: 50e9e544078b9f3202395b23bc34b91baa13e3ce
  ActionSheetPicker-3.0: a380d57270b6b2d321c4add024ba5c466a7f5cfd
  Alamofire: 85e8a02c69d6020a0d734f6054870d7ecb75cf18
  AlamofireImage: be9963c6582d68b39e89191f64c82a7d7bf40fdd
  AppsFlyerFramework: 34cef9f9653f4fd01c27d4d87784425b9271f82f
  Branch: c1a7fa1811ef9203c01bc6c2588a229eb3ee1a84
  CarouselSwipeView: 3c852f9038c9753f2f8d678f83a9dfd046dafad1
  CocoaLumberjack: 6a459bc897d6d80bd1b8c78482ec7ad05dffc3f0
  Firebase: 3435bc66b4d494c2f22c79fd3aae4c1db6662327
  FirebaseAnalytics: 630349facf4a114a0977e5d7570e104261973287
  FirebaseCore: c692c7f1c75305ab6aff2b367f25e11d73aa8bd0
  FirebaseCoreExtension: c048485c347616dba6165358dbef765c5197597b
  FirebaseCoreInternal: 29d7b3af4aaf0b8f3ed20b568c13df399b06f68c
  FirebaseCrashlytics: 8281e577b6f85a08ea7aeb8b66f95e1ae430c943
  FirebaseDynamicLinks: a784b5beaf9850a7ee4270b0fb189dba6263edcc
  FirebaseInstallations: 0ee9074f2c1e86561ace168ee1470dc67aabaf02
  FirebaseMessaging: 195bbdb73e6ca1dbc76cd46e73f3552c084ef6e4
  FirebaseRemoteConfigInterop: 7915cec47731a806cda541f90898ad0fab8f9f86
  FirebaseSessions: eaa8ec037e7793769defe4201c20bd4d976f9677
  GoogleAppMeasurement: 0dfca1a4b534d123de3945e28f77869d10d0d600
  GoogleDataTransport: aae35b7ea0c09004c3797d53c8c41f66f219d6a7
  GoogleMaps: 8939898920281c649150e0af74aa291c60f2e77d
  GooglePlaces: 426efb69051e7b460e16300ba63598687d10fa1a
  GoogleUtilities: 00c88b9a86066ef77f0da2fab05f65d7768ed8e1
  ImageSlideshow: 5dd4ff151b51ea0a8106ee9c0bbce27281bac368
  IQKeyboardManagerSwift: 12d89768845bb77b55cc092ecc2b1f9370f06b76
  Kingfisher: 0621d0ac0c78fecb19f6dc5303bde2b52abaf2f5
  lottie-ios: 8f97d3271e155c2d688875c29cd3c74908aef5f8
  Mixpanel-swift: 7b26468fc0e2e521104e51d65c4bbf7cab8162f8
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  PayTabsSDK: c254fd8870003eb913f45a0ce1e20db974e9af83
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  PromisesSwift: 9d77319bbe72ebf6d872900551f7eeba9bce2851
  ReachabilitySwift: 32793e867593cfc1177f5d16491e3a197d2fccda
  Socket.IO-Client-Swift: 1e3e3a1f09f3312a167f0d781eb2f383d477357c
  Starscream: 4bb2f9942274833f7b4d296a55504dcfc7edb7b0
  STTabbar: fe93da755010233b348b2d0147c43bb1bef290fa
  SVGKit: 1ad7513f8c74d9652f94ed64ddecda1a23864dea
  SwiftyJSON: f5b1bf1cd8dd53cd25887ac0eabcfd92301c6a5a
  TikTokBusinessSDK: 43dd7f114a2aab32b876596d1f4fa262927834be
  Toast-Swift: 7a03a532afe3a560d4044bc7c237e2864d295173

PODFILE CHECKSUM: cea83f22ec272d4481ede748da55f3c90d2dd026

COCOAPODS: 1.16.2
