//
//  NotificationService.swift
//  ContentImageExtentionPush
//
//  Created by <PERSON><PERSON> on 04/12/2024.
//  Copyright © 2024 SOTSYS203. All rights reserved.
//

import UserNotifications

class NotificationService: UNNotificationServiceExtension {
    
    var contentHandler: ((UNNotificationContent) -> Void)?
    var bestAttemptContent: UNMutableNotificationContent?
    
    override func didReceive(_ request: UNNotificationRequest, withContentHandler contentHandler: @escaping (UNNotificationContent) -> Void) {
        self.contentHandler = contentHandler
        bestAttemptContent = (request.content.mutableCopy() as? UNMutableNotificationContent)
        
        guard let bestAttemptContent = bestAttemptContent else {
            return
        }
        
        // Modify the notification content here...
        bestAttemptContent.title = "\(bestAttemptContent.title)"
        
        if let urlString = request.content.userInfo["fcm_options"] as? [String: Any],
           let image = urlString["image"] as? String,
           let fileUrl = URL(string: image) {
            debugPrint("fileUrl: \(fileUrl)")
            
            guard let imageData = try? Data(contentsOf: fileUrl) else {
                contentHandler(bestAttemptContent)
                return
            }
            guard let attachment = saveImageToDisk(fileIdentifier: "image.jpg", data: imageData, options: nil) else {
                debugPrint("Error in saving image to disk.")
                contentHandler(bestAttemptContent)
                return
            }
            
            bestAttemptContent.attachments = [attachment]
        }
        
        contentHandler(bestAttemptContent)        
    }
    
    override func serviceExtensionTimeWillExpire() {
        // Called just before the extension will be terminated by the system.
        // Use this as an opportunity to deliver your "best attempt" at modified content, otherwise the original push payload will be used.
        if let contentHandler = contentHandler, let bestAttemptContent =  bestAttemptContent {
            contentHandler(bestAttemptContent)
        }
    }
    
    private func saveImageToDisk(fileIdentifier: String, data: Data, options: [AnyHashable : Any]?) -> UNNotificationAttachment? {
        let fileManager = FileManager.default
        let folderName = ProcessInfo.processInfo.globallyUniqueString
        let folderURL = FileManager.default.temporaryDirectory.appendingPathComponent(folderName, isDirectory: true)
        
        do {
            try fileManager.createDirectory(at: folderURL, withIntermediateDirectories: true, attributes: nil)
            let fileURL = folderURL.appendingPathComponent(fileIdentifier)
            try data.write(to: fileURL, options: [])
            let attachment = try UNNotificationAttachment(identifier: fileIdentifier, url: fileURL, options: options)
            return attachment
        } catch {
            print("Error: \(error)")
        }
        
        return nil
    }
    
}
