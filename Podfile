# Uncomment the next line to define a global platform for your project
platform :ios, '14.0'

target 'TopCustomer' do
  use_frameworks!

  pod 'FirebaseAnalytics'
  pod 'TikTokBusinessSDK'
  pod 'Alamofire', '~> 4.9.1'
  pod 'SwiftyJSON'
  pod 'ReachabilitySwift'
  pod 'IQKeyboardManagerSwift', '~> 6.5.12'
  pod 'Firebase/Crashlytics'
  pod 'Firebase/Analytics'
  pod 'Firebase/DynamicLinks'
  # Firebase push notification
  pod 'Firebase/Core'
  pod 'Firebase/Messaging'
  pod 'STTabbar', :git => 'https://github.com/Mohamed-<PERSON>aouf/STTabbar.git', :commit => 'fc4999f8aa5ce095448cb7eb91c59389d70c2b0b'
  pod 'Kingfisher'
  pod 'GoogleMaps', '8.4.0'
  pod 'GooglePlaces'
  pod 'Toast-Swift'
  # socket for real time updates
  pod 'Socket.IO-Client-Swift', '~> 15.2.0'
  pod 'PayTabsSDK'
  pod 'lottie-ios', '~> 3.4.3'
  pod 'Branch'
  pod 'Mixpanel-swift'
  pod 'AACarousel', :git => 'https://github.com/<PERSON>-<PERSON>/AACarousel.git', :commit => '01a14180700dc4c739ec173f5fa1f5d90fcba761'
  pod 'ActionSheetPicker-3.0'
  pod 'SVGKit'
  # For statically linked library
  pod 'AppsFlyerFramework'
  pod 'CarouselSwipeView', :git => 'https://github.com/Mohamed-AbdulRaouf/CarouselSwipeView.git'
  pod 'ImageSlideshow/Alamofire', :git => 'https://github.com/Mohamed-AbdulRaouf/ImageSlideshow.git'
end

post_install do |installer|
  installer.pods_project.targets.each do |target|
    target.build_configurations.each do |config|
      # تأكد إنك ما بتستبعدش arm64 لو شغال على Apple Silicon
      if `uname -m`.strip == 'x86_64'
        config.build_settings['EXCLUDED_ARCHS[sdk=iphonesimulator*]'] = 'arm64'
      else
        config.build_settings['EXCLUDED_ARCHS[sdk=iphonesimulator*]'] = ''
      end
      config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '14.0'
    end
  end
end
