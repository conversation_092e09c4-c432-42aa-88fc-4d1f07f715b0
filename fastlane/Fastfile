# This file contains the fastlane.tools configuration
# You can find the documentation at https://docs.fastlane.tools
#
# For a list of all available actions, check out
#
#     https://docs.fastlane.tools/actions
#
# For a list of all available plugins, check out
#
#     https://docs.fastlane.tools/plugins/available-plugins
#

# Uncomment the line if you want fastlane to automatically update itself
# update_fastlane

ENV["FASTLANE_XCODEBUILD_SETTINGS_TIMEOUT"] = "180"
ENV["FASTLANE_XCODE_LIST_TIMEOUT"] = "180"

default_platform(:ios)

platform :ios do
  desc "Description of what the lane does"
  lane :development_Customer_archive do
    gym(
        output_directory: '/Volumes/LaCie/Jenkins/workspace/SM-AJ-TopMaterials-iOS',
        output_name: 'TopCustomer.ipa',
        scheme: 'TopCustomer',
        include_bitcode: false,
        configuration: 'Debug',
        export_method: "development",
    )
upload_symbols_to_crashlytics(dsym_path:'/Volumes/LaCie/Jenkins/workspace/SM-AJ-TopMaterials-iOS/TopCustomer.app.DSYM.zip', gsp_path: 'TopCustomer/GoogleService-Info.plist')
  end
lane :staging_Customer_archive do
    gym(
        output_directory: '/Volumes/LaCie/Jenkins/workspace/SM-AJ-TopMaterials-iOS',
        output_name: 'TopCustomer.ipa',
        scheme: 'TopCustomer',
        include_bitcode: false,
        configuration: 'Staging',
        export_method: "development",
    )
upload_symbols_to_crashlytics(dsym_path:'/Volumes/LaCie/Jenkins/workspace/SM-AJ-TopMaterials-iOS/TopCustomer.app.DSYM.zip', gsp_path: 'TopCustomer/GoogleService-Info.plist')
  end
  lane :release_Customer_archive do
    gym(
        output_directory: '/Volumes/LaCie/Jenkins/workspace/SM-AJ-TopMaterials-iOS',
        output_name: 'TopCustomer.ipa',
        scheme: 'TopCustomer',
	clean: true,
        include_bitcode: false,
        configuration: 'Release',
        export_method: "app-store",
    )
upload_symbols_to_crashlytics(dsym_path:'/Volumes/LaCie/Jenkins/workspace/SM-AJ-TopMaterials-iOS/TopCustomer.app.DSYM.zip', gsp_path: 'TopCustomer/GoogleService-Info.plist')
  end
lane :release_Customer_staging_archive do
    gym(
        output_directory: '/Volumes/LaCie/Jenkins/workspace/SM-AJ-TopMaterials-iOS',
        output_name: 'TopCustomer.ipa',
        scheme: 'TopCustomer',
	clean: true,
        include_bitcode: false,
        configuration: 'ReleaseStaging',
        export_method: "app-store",
    )
upload_symbols_to_crashlytics(dsym_path:'/Volumes/LaCie/Jenkins/workspace/SM-AJ-TopMaterials-iOS/TopCustomer.app.DSYM.zip', gsp_path: 'TopCustomer/GoogleService-Info.plist')
  end
lane :development_Customer_release_archive do
    gym(
        output_directory: '/Volumes/LaCie/Jenkins/workspace/SM-AJ-TopMaterials-iOS',
        output_name: 'TopCustomer.ipa',
        scheme: 'TopCustomer',
        include_bitcode: false,
        configuration: 'DevRelease',
        export_method: "development",
    )
upload_symbols_to_crashlytics(dsym_path:'/Volumes/LaCie/Jenkins/workspace/SM-AJ-TopMaterials-iOS/TopCustomer.app.DSYM.zip', gsp_path: 'TopCustomer/GoogleService-Info.plist')
  end

end
